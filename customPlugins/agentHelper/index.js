const request             = require('request');
const router              = require('express').Router();
const fs                  = require('fs');
const jsep                = require("jsep").addBinaryOp("AND", 10).addBinaryOp("OR", 10);
const async               = require('async');
const spawn               = require('child_process').spawn;
// const findCookieByCaseuid = require('../../routes/analytics/common').findCookieByCaseuid;
const sendEmail           = require('../../Lib/email').sendEmail;
const moment              = require("moment");
const dumpCSV             = require("./prepareDataCSVForPython");
const kafkaLib = require("../../utils/kafka/kafka-lib");
const { tenantSqlConnection  } = require('../../auth/sqlConnection');
var path = require('path');
const { getEsClient } = require('../../utils/elastic');
const  pick  = require('../../utils/pick');
const { 
  trainAgentHelper, 
  deleteAgentHelperConfigurations, 
  saveAgentHelperConfigurations, 
  updateTrainingStatus, 
  getAgentHelperDetails,
  getAgentHelperList,
  updateAgentHelper,
  jiraToggle,
  getJiraCreds,
  saveJiraCreds,
  getSlackCreds,
  slackToggle,
  saveSlackCreds,
  deactivateAgentHelper,
  deactivateAllAgentHelper
} = require('./agentHelper')

let syncStatus = false;

//new agent helper apis
// Added to convert camel case values to unserscore value to directly used in sql queries
const camelcaseToUnderscoreProperty = (object) => {
  const newObject = {};
  if (object instanceof Array) {
    return object.map((v) => {
      let a = v;
      if (typeof v === 'object' && v !== null) a = processModelKeys(v);

      return a;
    });
  }

  const allKeys = Object.keys(object);

  allKeys.forEach((origKey) => {
    if (Object.prototype.hasOwnProperty.call(object, origKey)) {
      const newKey = origKey.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
      let value = object[`${origKey}`];
      if (typeof value === 'object' && value !== null) value = processModelKeys(value);
      newObject[`${newKey}`] = value;
    }
  });

  return newObject;
};


router.post('/', async (req, res) => {
  try{
    const body = pick(req.body, ['uid', 'configurations', 'type', 'scName', 'newConfigurations', 'search_uid_AH', 'search_uid_RA']);
    const data = {
      tenantId: req.headers['tenant-id'],
      ...body
    }
    const response = await saveAgentHelperConfigurations(data);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
})

router.put('/:uid', async (req, res) => {
  try{
    const { uid } = req.params;
    const tenantId = req.headers['tenant-id'];
    const databaseName = req.headers.databaseName
    await tenantSqlConnection(tenantId, databaseName);
    const data = pick(req.body, ['type', 'isEnabled', 'configuration', 'type', 'lastTrained', , 'trainingStatus', 'trainingMessage']);
    if(!uid){
      return res.status(400).send({message: "Please enter valid UID"});
    }
    const modifiedData = camelcaseToUnderscoreProperty(data); 
    const response = await updateAgentHelper(modifiedData, uid, tenantId);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
});


router.get('/', async (req, res) => {
  try{
    const data = {
      tenantId: req.headers['tenant-id']
    }
    const response = await getAgentHelperList(data);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
})

router.get('/:uid', async (req, res) => {
  try{
    const data = {
      uid: req.params.uid,
      tenantId: req.headers['tenant-id'],
    };
    const response = await getAgentHelperDetails(data);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
})

router.delete('/', async (req, res) => { 
  try{
    const query = pick(req.query, ['uid']);
    const data = {
      ...query,
      tenantId: req.headers['tenant-id'],
      tpk: req.headers.session.tpk
    };
    const response = await deleteAgentHelperConfigurations(data);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
});

router.post('/deactivate', async(req, res) => {
  try{
    const query = pick(req.query, ['uid', 'enable']);
    const data = {
      ...query,
      tenantId: req.headers['tenant-id'],
    };
    const response = await deactivateAgentHelper(data);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
})
 
router.post('/deactivate-all', async(req, res) => {
  try{
    const data = {
      tenantId: req.headers['tenant-id'],
    };
    const response = await deactivateAllAgentHelper(data);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
})


router.post('/training-status', async (req, res) => {
  try{
    const body = pick(req.body, ['uid', 'message', 'status', 'tenantId']);
    const response = await updateTrainingStatus(body);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
});

router.post('/train-agenthelper', async (req, res) => {
  try{
    const body = pick(req.body, ['uid']);
    const data = {
      tenantId: req.headers['tenant-id'],
      tpk: req.headers.session.tpk,
      ...body
    };
    const response = await trainAgentHelper(data);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
});


router.post('/slack/creds', async (req, res) => {
  try{
    const body = pick(req.body, ['creds','uid']);
    body.tenantId = req.headers['tenant-id'];
    const response = await saveSlackCreds(body);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
});

router.get('/slack/creds', async (req, res) => {
  try{
    const body = pick(req.query, ['uid','platform']);
    body.tenantId = req.headers['tenant-id'];
    const response = await getSlackCreds(body);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
});

router.put('/slack/enable', async (req, res) => {
  try{
    const body = pick(req.query, ['enable', 'uid']);
    body.tenantId = req.headers['tenant-id'];
    const response = await slackToggle(body);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
});

router.post('/jira/creds', async (req, res) => {
  try{
    const body = pick(req.body, ['creds', 'uid']);
    body.tenantId = req.headers['tenant-id'];
    const response = await saveJiraCreds(body);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
});

router.get('/jira/creds', async (req, res) => {
  try{
    const body = pick(req.query, ['uid']);
    body.tenantId = req.headers['tenant-id'];
    const response = await getJiraCreds(body);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
});

router.put('/jira/enable', async (req, res) => {
  try{
    const body = pick(req.query, ['uid', 'enable']);
    body.tenantId = req.headers['tenant-id'];
    const response = await jiraToggle(body);
    res.status(200).send(response);
  }catch(error){
    res.status(400).send(error);
  }
});

router.post("/query", async (req, res, next) => {
  /**
   * 1. read config file
   * 2. Get ClusterNumber
   * 3. perform matching
   * 4. Fetch analytics near case creation
   */
  try {
    const { tpk } = req.headers.session;
    const { uid } = req.body;
    const tenantId  = req.headers['tenant-id'];
    const databaseName = req.headers.databaseName
    const esClient = await getEsClient(tenantId);
    await tenantSqlConnection(tenantId, databaseName);
    async.auto({
      conf: cb => {
        const query = "SELECT * from agenthelper WHERE uid = ? AND tenant_id = ?";
        connection[tenantId].execute.query(query, [uid, tenantId], (error, doc) => {
          if(!error && doc.length){
            cb(null, doc[0])
          }else{
            cb(null, {});
          }
        })
      },
      getClusterOfIds: ["conf", (results, cb) => {
        let conf = JSON.parse(results.conf.configuration);
        let searchObject = {"text":""};
        if (conf.Input_Fields && conf.Input_Fields.length) {
          for (let i = 0; i < conf.Input_Fields.length; i++) {
            if(req.body.doc[conf.Input_Fields[i].name.split('___')[2]] != '' && req.body.doc[conf.Input_Fields[i].name.split('___')[2]] != null){
            searchObject[conf.Input_Fields[i].name] = req.body.doc[conf.Input_Fields[i].name.split('___')[2]];
            searchObject.text += req.body.doc[conf.Input_Fields[i].name.split('___')[2]] + " ";
            }
          } 
        }
        searchObject.tpk = tpk;
        searchObject.tenantId =  tenantId;
        searchObject.uid =  req.body.uid;
        searchObject.caseId = req.body && req.body.doc ? req.body.doc.Id : '';
        var options = {
          url     : `${config.get('agentHelperURL')}/predict`,
          method  : 'POST',
          headers : {'Content-Type': 'application/json'},
          body    : searchObject,
          json    : true
        };
        request(options, (error, response, body) => {
          let clusterIds =  [];
          let filterFunctions = []
          let llmActive = body && body.llmActive;
          if (!error && response.statusCode == 200) {
            body.ids.forEach((id,index) => {
              clusterIds.push(id);
              filterFunctions.push({
                "filter":{
                  "term":{
                    "_id":id
                  }
                },
                "weight": 10 - index
              })
            });
            cb(null,{clusterIds,filterFunctions, llmActive});
          } else {
            console.error(new Error(error));
            cb(null, []);
          }
        });
      }],
      match: ["conf", "getClusterOfIds", (results, cb) => {
        let conf = JSON.parse(results.conf.configuration);
        // let clusterNumber = results.getClusterNumber;
        let clusterIds = results.getClusterOfIds.clusterIds
        let obj = {
          index: conf.Source_object.parent,
          type: conf.Source_object.child,
          clusterIds,
          fields: [
            "caseComments.body",
            "Id",
            conf.Source_object.parent + "___" + conf.Source_object.child + "___CaseNumber",
            ...conf.Input_Fields.map(n => n.name)
          ]
        };
        let options = {
            method: "GET",
            url: config.get("indexService.url") + "/index-service/open-search/getAgentHelperDocs",
            headers: {
                "Content-Type": "application/json",
                "tenant-id": req.headers['tenant-id'],
                "index-service-secret": config.get("indexService.sharedSecret"),
            },
            body: obj,
            json: true
        };
        request(options, function (error, response, body) {
            if (error || body.code !== 200) {
              console.error(error || body.data);
              cb(error || body.data, null);
            } else {
              cb(null, body.data);
            }
        });
      }]
    }, (error, result) => {
      if (error) {
        console.error(error);
        res.send("");
      } else{
        if(result && result.getClusterOfIds && result.getClusterOfIds.llmActive){
          llmActive =  result.getClusterOfIds.llmActive
        }else{
          llmActive = false;
        }
        res.send({
          hits: { hits : result.match },
          llmActive
        });
      }
    });
  } catch (error) {
      console.error(error);
      res.send("");
  }
});


router.post("/query-stage", (req, res, next) => {
  /**
   * 1. read config file
   * 2. get Related Comments
   * 2. get First response
   */
  async.auto({
    conf: cb => {
      fs.readFile(`${__dirname}/../../resources/search_clients_custom/${tpk}_agentHelperConfig.json.ignore`, 'utf8', cb);
    },
    getRelatedComments: ['conf', function (dataFromAbove, cb) {
      let conf = JSON.parse(dataFromAbove.conf)[req.body.uid];

      let dsl = {
        "query": {
          "bool": {
            "should": [],
            "minimum_should_match": 1,
            "must": [{
              "nested": {
                "path": "caseComments",
                "query": {
                  "bool": {
                    "filter": [{
                      "exists": {
                        "field": "caseComments"
                      }
                    }]
                  }
                }
              }
            }]
          }
        },
        "aggs": {
          "bags": {
            "nested": {
              "path": "caseComments"
            },
            "aggs": {
              "of": {
                "filter": {},
                "aggs": {
                  "words": {
                    "terms": {
                      "field": "caseComments.body"
                    }
                  }
                }
              }
            }
          }
        },
        "size": 100
      };


      // Add input fields in dsl to get the related results
      if (conf.Input_Fields && conf.Input_Fields.length) {
        let searchObject = [];
        for (let i = 0; i < conf.Input_Fields.length; i++) {
          searchObject[i] = {
            "match": {
              [conf.Input_Fields[i].name]: req.body.doc[conf.Input_Fields[i].name.split('___')[2]]
            }
          };
        }
        dsl.query.bool.should = searchObject;

      }
      dsl.aggs.createdDate = {
        "avg": {
          "field": conf.Source_object.parent + "___" + conf.Source_object.child + "___CreatedDate"
        }
      };
      dsl.aggs.closedDate = {
        "avg": {
          "field": conf.Source_object.parent + "___" + conf.Source_object.child + "___ClosedDate"
        }
      };

      // Add Email domain filter to get the results filter by domain
      if (conf.Email_Domain) {
        dsl.query.bool.must[0].nested.query.bool.filter.push({
          "wildcard": {
            "caseComments.createdBy": conf.Email_Domain
          }
        });
        dsl.aggs.bags.aggs.of.filter = {
          "wildcard": {
            "caseComments.createdBy": conf.Email_Domain
          }
        };
      }
      console.log("dsl--");
      console.log(JSON.stringify(dsl));
      console.log("index Is", conf.Source_object.parent)
      esclientCS.search({
        index: conf.Source_object.parent,
        type: conf.Source_object.child,
        size: 1000,
        body: dsl
      }, (error, resp) => {
        if (error || resp.error) {
          console.error(error || resp.error);
          cb(error, null);
        } else {
          let result = {};
          if (resp.aggregations && resp.aggregations.closedDate && resp.aggregations.closedDate.value) {
            result.resolutionTime = resp.aggregations.closedDate.value - resp.aggregations.createdDate.value;
            result.resolutionTime = moment.duration(result.resolutionTime, "milliseconds").humanize();
            console.log("***********resolution*************", result.resolutionTime);
          }
          if (resp.aggregations && resp.aggregations.bags && resp.aggregations.bags.of && resp.aggregations.bags.of.words && resp.aggregations.bags.of.words.buckets) {
            result.comments = resp.aggregations.bags.of.words.buckets;
            cb(null, result);
          } else {
            cb(null, result);
          }

        }
      });

    }],
    getFirstResult: ['getRelatedComments', function (dataFromAbove, cb) {
      let conf = JSON.parse(dataFromAbove.conf)[req.body.uid];
      let relatedComments = (dataFromAbove.getRelatedComments);
      let dsl = {
        "query": {
          "nested": {
            "path": "caseComments",
            "query": {
              "bool": {
                "must": [{
                  "query_string": {
                    "fields": [
                      "caseComments.body"
                    ],
                    "query": ""
                  }
                }]
              }
            }
          }
        },
        "fields": ["caseComments.body", "Id"]
      };
      /*let dsl = {
          "query": {
              "query_string": {
                  "fields": ["caseComments.body"],
                  "query":""
              }
          },
          "inner_hits": { },
          "fields": ["caseComments.body","Id"]
      };*/
      if (relatedComments && relatedComments.comments) {
        let query = "";
        for (let i = 0; i < relatedComments.comments.length; i++) {
          if (i == 0) {
            query = relatedComments.comments[i].key + "^" + relatedComments.comments[i].doc_count;
          } else {
            query = query + " " + relatedComments.comments[i].key + "^" + relatedComments.comments[i].doc_count;
          }
        }
        let subject = conf.Source_object.parent + "___" + conf.Source_object.child + "___Subject";
        dsl.fields.push(subject);
        let caseNumber = conf.Source_object.parent + "___" + conf.Source_object.child + "___CaseNumber";
        dsl.fields.push(caseNumber);
        dsl.query.nested.query.bool.must[0].query_string.query = query
        // dsl.query.query_string.query = query;
        console.log("secondQuery is--");
        console.log(JSON.stringify(dsl));
        esclientCS.search({
          index: conf.Source_object.parent,
          type: conf.Source_object.child,
          body: dsl
        }, (error, resp) => {
          if (error || resp.error) {
            console.error(error || resp.error);
            cb(error, null);
          } else {
            if (relatedComments && relatedComments.resolutionTime) {
              resp.hits.resolutionTime = relatedComments.resolutionTime;
            }
            cb(null, resp);
          }
        });
      } else {
        cb(null, "");
      }
    }]
  }, (err, result) => {
    if (err)
      console.error(err);
    else
      res.send(result.getFirstResult);
  });
});

router.post("/sync-now", (req, res, net) => {
  if (syncStatus)
    res.send("Already in progress");
  else {
    syncStatus = true;
    res.send("Process Started");
    let child = spawn(`node`, [`agentHelperRefactor.js`, req.body.uid], {
      cwd: __dirname
    });
    child.stdout.on('data', (data) => {
      console.log(`stdout: ${data}`);
    });

    child.stderr.on('data', (data) => {
      console.error(`stderr: ${data}`);
    });
    child.on('close', (code) => {
      console.log(`Agent Helper exited with code ${code}`);
      syncStatus = false;
    });
  }
});

router.post("/case-sentiments", async (req, res, next) => {
  var options = {
    url     : `${config.get('agentHelperURL')}/sentiment/get-sentiment`,
    method  : 'POST',
    headers : {'Content-Type': 'application/json'},
    body    : req.body.case,
    json    : true
  };
  request(options, (error, response, body) => {
    if (error)
      res.send("");
    else
      res.send(body);
  });
});

function agentHelperAddon(cb, req) {
  connection[req.headers['tenant-id']].execute.query(`SELECT is_installed FROM addons, addons_status WHERE addons.id = addons_status.addon_id AND name='Agent Helper'`, (error, result) => {
    commonFunctions.errorlogger.info(result);
    cb(error, result);
  });
}

module.exports.index = router;
