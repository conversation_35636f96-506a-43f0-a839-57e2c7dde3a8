{"name": "admin-search-cli", "version": "0.0.0", "license": "MIT", "scripts": {"start": "node app.js", "start:dev": "NODE_ENV=development node app.js", "lint-backend": "(find ./auth ./constants ./config ./routes -type f -name '*.js' -exec node --check {} \\; && node --check app.js) || exit 1", "mocha": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls", "swagger": "node ./routes/content-swagger/index.js", "debug": "node --inspect-brk=0.0.0.0:2300 app.js", "nodemon": "nodemon app.js", "start:prod": "NODE_ENV=production node app.js"}, "private": true, "dependencies": {"@elastic/elasticsearch": "7.10.0", "@opensearch-project/opensearch": "^2.3.1", "aes256": "^1.0.4", "archiver": "^3.1.1", "async": "^3.1.1", "auth-middleware": "git+https://<EMAIL>/searchunify/auth-middleware#25.07.00", "axios": "^0.21.1", "base64url": "^3.0.1", "body-parser": "^1.19.0", "botkit": "^4.6.2", "bull": "4.8.1", "cheerio": "^0.22.0", "chokidar": "^3.6.0", "compromise": "^12.3.0", "config": "^3.2.5", "connect-multiparty": "^2.2.0", "cookie-parser": "^1.4.3", "copy-dir": "^1.2.0", "core-js": "^2.5.7", "cors": "^2.8.5", "crontab": "^1.3.0", "crypto-js": "^3.3.0", "csurf": "^1.11.0", "csv": "^5.3.1", "csv-parser": "^2.3.2", "csv2json": "^2.0.2", "csvjson": "^5.1.0", "csvtojson": "2.0.10", "debug": "^4.1.1", "detect-file-type": "^0.2.8", "doc-similarity": "0.0.1", "dompurify": "^2.3.4", "ejs": "^3.0.1", "elasticsearch": "^14.2.2", "emotional": "0.0.3", "excel4node": "^1.7.2", "express": "^4.17.1", "express-hbs": "^2.3.3", "express-mysql-session": "^2.1.3", "express-session": "^1.17.0", "express-throttle-redis": "0.0.4", "fast-csv": "^2.4.1", "file-type": "^12.4.0", "fs": "0.0.1-security", "geoip-lite": "^1.3.8", "google-auth-library": "^5.9.2", "he": "^1.2.0", "html-entities": "^2.1.1", "html-to-json": "^0.6.0", "html-to-text": "^5.1.1", "htmlparser": "^1.7.7", "http-proxy-middleware": "^2.0.6", "ip-range-check": "0.2.0", "js-yaml": "^3.11.0", "jsdom": "^16.7.0", "jsep": "^0.3.4", "jsforce": "^1.9.3", "json2csv": "^4.5.4", "jsonwebtoken": "^8.5.1", "kafkajs": "^1.12.0", "lodash": "^4.17.21", "logger": "0.0.1", "lru-cache": "^5.1.1", "md5": "^2.2.1", "moment": "^2.29.4", "moment-timezone": "^0.5.33", "mysql": "^2.18.1", "mysql2": "^2.0.2", "natural": "^0.6.3", "nltk-stopwords": "^1.0.3", "node": "^10.17.0", "node-cron": "^3.0.0", "node-fetch": "^2.6.1", "node-html-markdown": "^1.3.0", "node-sp-auth": "^2.5.7", "node-zendesk": "^1.2.1", "nodejs-aes256": "^1.0.1", "nodemailer": "^6.4.1", "nodemon": "^2.0.2", "npm": "^6.14.5", "oauth": "^0.9.15", "oauth2-server": "^3.0.1", "pako": "^1.0.11", "passport": "^0.4.1", "passport-saml": "^1.3.2", "promise": "^8.0.1", "qs": "^6.11.2", "redis": "^2.8.0", "request": "^2.88.2", "request-promise": "^4.2.5", "sanitize-html": "^1.21.1", "sentiment": "^5.0.1", "sequelize": "^5.21.4", "set-cookie-parser": "^2.4.8", "sharepoint-auth": "^1.0.3", "socket.io": "^2.3.0", "split": "^1.0.1", "stopword": "^0.3.4", "stream-json": "^1.3.3", "stream-replace": "^1.0.0", "striptags": "^3.1.1", "swagger-tools": "^0.10.3", "swagger-ui-express": "^4.1.2", "through": "^2.3.8", "tracer": "^1.0.1", "unique-words": "^2.0.1", "url": "^0.11.1", "uuidv4": "^6.2.13", "wordfilter": "^0.2.6", "xml2json": "^0.11.2", "xmlserializer": "^0.6.1", "yargs": "^15.3.1"}, "devDependencies": {"compression": "^1.7.5", "nyc": "14.1.1", "protractor": "^5.4.3", "wink-pos-tagger": "^2.2.2"}}