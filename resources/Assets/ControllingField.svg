<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="30" height="10" viewBox="0 0 30 10">
  <defs>
    <clipPath id="clip-Dependent_on_Controlling_field_Name_">
      <rect width="30" height="10"/>
    </clipPath>
  </defs>
  <g id="Dependent_on_Controlling_field_Name_" data-name="Dependent on Controlling field (Name ) " clip-path="url(#clip-Dependent_on_Controlling_field_Name_)">
    <g id="Group_4682" data-name="Group 4682" transform="translate(-105 -169)">
      <g id="Rectangle_4632" data-name="Rectangle 4632" transform="translate(115 170)" fill="#a4a4a4" stroke="#a4a4a4" stroke-width="0.4">
        <rect width="8" height="8" rx="1" stroke="none"/>
        <rect x="0.2" y="0.2" width="7.6" height="7.6" rx="0.8" fill="none"/>
      </g>
      <g id="Rectangle_4635" data-name="Rectangle 4635" transform="translate(126 170)" fill="#a4a4a4" stroke="#a4a4a4" stroke-width="0.4">
        <rect width="8" height="8" rx="1" stroke="none"/>
        <rect x="0.2" y="0.2" width="7.6" height="7.6" rx="0.8" fill="none"/>
      </g>
      <g id="arrow-forward" transform="translate(110 174)">
        <g id="arrow-forward-2" data-name="arrow-forward" transform="translate(-5 -5)">
          <g id="arrow-forward-3" data-name="arrow-forward">
            <rect id="Rectangle_4637" data-name="Rectangle 4637" width="10" height="10" transform="translate(0 10) rotate(-90)" fill="#a4a4a4" opacity="0"/>
            <path id="Path_6125" data-name="Path 6125" d="M4.417,8.333H9.358L7.846,10.15a.417.417,0,1,0,.642.533l2.083-2.5a.5.5,0,0,0,.037-.062c0-.021.021-.033.029-.054a.4.4,0,0,0,0-.3c0-.021-.021-.033-.029-.054a.5.5,0,0,0-.037-.062L8.488,5.15a.417.417,0,1,0-.642.533L9.358,7.5H4.417a.417.417,0,0,0,0,.833Z" transform="translate(-2.333 -2.917)" fill="#a4a4a4"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
