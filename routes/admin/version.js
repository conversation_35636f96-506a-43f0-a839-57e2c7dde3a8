/**
 * Created by man<PERSON><PERSON> on 6/4/17.
 */

var express = require('express');
var router = express.Router();

router.get('/getVersions',function (req,res) {
  getAllVersions(req,function(data) {
    res.send(data);
  });
})

function getAllVersions(req,cb) {
  var sql="select * from  version_control";
  connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
      if (!err) {

        var today=new Date()
        var pastRelease=[]
        var futureRelease=[]
        var currentVersion=[]

        for(var i=0;i<rows.length;i++)
        {
          if(rows[i].is_current)
            currentVersion.push(rows[i])
          else if(rows[i].releaseDate<today)
          pastRelease.push(rows[i])
          else
            futureRelease.push(rows[i])
        }

        cb({versions:{currentVersion:currentVersion,
        pastRelease:pastRelease,
          futureRelease:futureRelease
        }})


      }
    })
}

module.exports = {
  getAllVersions:getAllVersions,
  router: router
};
