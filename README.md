# To install admin on local using docker 

<PERSON>lone this project and run the following CLI Commands
*note:- Make sure that you have a sudo permission while accessing the docker commands

1. Change directory to admin 
2. Inside admin run the following commands for the first time:-
    1. `docker-compose -f docker-compose-mac-backend.yml build --no-cache` ( if you have changes in node-modules)
    2. `docker-compose -f docker-compose-mac-backend.yml --compatibility up -d`