module.exports = {
  objects: [{
    name: "courses",
    label: "Courses",
    status: 1,
    boosting_factor: 1,
    base_url: "",
    fields: [{
      name: "id",
      label: "Id",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "domainName",
      label: "domainName",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "courseSeriesName",
      label: "courseSeriesName ",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "courseSeriesId",
      label: " courseSeriesId",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "courseSeriesUrl",
      label: "courseSeriesUrl",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1,
      isMasked: 0
    },
    {
      name: "slug",
      label: "Slug",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "courseName",
      label: "courseName",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "courseUrl",
      label: "courseUrl",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1,
      isMasked: 0
    },
    {
      name: "registration_required",
      label: "registration_required",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "courseId",
      label: "courseId",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "shortDescription",
      label: " Short Description",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "longDescription",
      label: "Long Description",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "lessonCount",
      label: "Lesson Count",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "timezone",
      label: "Timezone",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "hidden",
      label: "Course Hidden",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "required_lesson_count",
      label: "Required lesson count",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "tags",
      label: "Tags",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "lesson_list",
      label: "Lesson list",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    }
    ]
  },
  {
    name: "course_series",
    label: "Course Series",
    status: 1,
    boosting_factor: 1,
    base_url: "",
    fields: [{
      name: "series_id",
      label: "Series Id",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "series_title",
      label: "Series Title",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "series_url",
      label: "Series Url",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1,
      isMasked: 0
    },
    {
      name: "series_registration_open",
      label: "Series Registration open",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "visible_on_catalog",
      label: "Visible on Catalog",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "published_course_count",
      label: "Published Course Count",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: "courses",
      label: "Courses",
      type: "string",
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    }
    ]
  }
  ],
  fields: []
}
