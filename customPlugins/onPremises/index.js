/**
 * Created by man<PERSON><PERSON> on 28/8/18.
 */



var request = require("request");
var async = require('async');
var fs = require("fs");
var path = require("path");
var onPremises = {};
var archiver = require('archiver');
var url = require('url');
var router = require('express').Router();


router.get("/getUrl", function (req, res, next) {
  config = require('config');
  fs.readFile(path.resolve(__dirname,'download/onPremisesNodeService/config.js'),'utf8',(err,data)=>{
    if(!err)

    data=JSON.parse(data.replace('module.exports=',''))
    res.send({flag:200,serviceUrl:config.onPremisesUrl || ''})
  })

});


router.post("/saveUrl", function (req, res, next) {
  console.log("here in Post")

  var service=url.parse(req.body.data.serviceUrl,true)
  var newConfig= Object.assign({},config)
  newConfig.onPremisesUrl=req.body.data.serviceUrl || ''
  newConfig.onPremisesUrl=req.body.data.serviceUrl || ''
  newConfig.onPremises=true


  fs.writeFile(path.resolve(DIRNAME,'config/',process.env.NODE_ENV+'.json'),JSON.stringify(newConfig,null,4), (err, data) => {
    if (err) throw err;
    config = require('config');
    delete require.cache[require.resolve('config')];    
    res.send({flag:200,status:"Done"})
  });
  // function to change  con fig under onPremises Folder


  // var onPremisesConfig={
  //   "PORT":service.port,
  //   "elasticUrl":req.body.data.elasticUrl,
  //   "encryptionKey":"Grazitti$$$271###"
  // }

  // fs.writeFile(path.resolve(__dirname,'download/onPremisesNodeService/config.js'),'module.exports='+JSON.stringify(onPremisesConfig,null,4), (err, data) => {
  //   if (err) throw err;
  //   console.log(data);

  // });

});

router.get("/downloadPackage", function (req, res, next) {
  let zipArchive = archiver('zip', { zlib: { level: 9 } });
  res.setHeader('Content-disposition', 'attachment; filename="onPremisesPAckage.zip"');
  zipArchive.pipe(res);
  createFolderZipRec(__dirname + "/download/", zipArchive, "", (error, result) => {
    if (error) console.log("Error: ", error);
    else {
      zipArchive.finalize();
    }
  });
});


var createFolderZipRec = (folder, zipArchive, base, cb) => {
  // let folders = [];
  fs.readdir(folder, (err, files) => {
    if (err) cb(err);
    else {
      async.parallel(files.map(f => {
        return cb => {
          fs.stat(path.join(folder, f), (err, stat) => {
            if (err) cb(err);
            else {
              if (stat && stat.isDirectory()) {
                createFolderZipRec(path.join(folder, f), zipArchive, path.join(base, f), cb);
              }
              else {
                let stream = fs.createReadStream(path.join(folder, f));
                let ext = f.split(".")[f.split(".").length - 1];
                ext = ext.toLowerCase();
                zipArchive.append(stream, {
                  name: f,
                  prefix: base + "/"
                });
                cb(null, path.join(base, f));
              }
            }
          });
        };
      }), cb);
    }
  });
};
module.exports.index = router;