/**
 * Created by man<PERSON><PERSON> on 18/8/16.
 */
var mysql = require('mysql');
var session = require('express-session');
const config = require('config');

const authUrl = config.get('authUrl');
//var MySQLStore = require('express-mysql-session')(session);
var sessionSet={value:null}
var db_config = {
  host: config.get('databaseSettings.host'),
  user: config.get('databaseSettings.user'),
  password: config.get('databaseSettings.password'),
  database: config.get('databaseSettings.database'),
  port: config.get('databaseSettings.mysqlPORT'),
  multipleStatements: true,
  connectionLimit: 2,
  // socketPath : '/var/run/mysqld/mysqld.sock'
  //config.get('databaseSettings.connectionLimit')
};
var commonFunctions = require('../utils/commonFunctions');
const { passportInitializationForAllTenants, passportInitializationForAllTenantsAndSCUids } = require('./passport/passport-initialization')
const createRequest = require('../auth/src/client');
const { tenantSqlConnection } = require('../auth/sqlConnection');
// const kafka = require("./kafka/startup");
// kafka.kafkaStartup();

function restart(callback) {

  commonFunctions.errorlogger.warn("RESTART THE SERVER");
  callback();
  // exec(" whoami; pm2 restart dashboard ; ", callback);

}

var handleDisconnect=function() {
  connection = {};
  connection.auth = {}
  connection.auth.execute = mysql.createPool(db_config); // Recreate the connection, since
  // the old one cannot be reused.
  return new Promise(function(resolve, reject) {
    connection.auth.execute.getConnection(function (err, connection) { // The server is either down
      if (err) { // or restarting (takes a while sometimes).
        commonFunctions.errorlogger.error('error when connecting to db:', err);
        setTimeout(handleDisconnect, 2000);
      } else {
        // if(sessionSet.value)
        // {
        //   sessionSet.value.store.connection=connection
        // }
        commonFunctions.errorlogger.warn("connection variable created ");
        initializeTenantConnections();
        resolve({flag:200,status:"Conected"})

      }
    }); // process asynchronous requests in the meantime.
    //  If you're also serving http, display a 503 error.
    connection.auth.execute.on('error', function (err) {
      commonFunctions.errorlogger.error('db error', err);
      if (err.code === 'PROTOCOL_CONNECTION_LOST') { // Connection to the MySQL server is usually
        handleDisconnect(); // lost due to either server restart, or a
      } else if (err.code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
        restart();
      } else { // connnection idle timeout (the wait_timeout
        reject(err); // server variable configures this)
      }
    });
  })
}


const initializeTenantConnections = async () => {
  try {
    const options = {
      method: 'GET',
      url: `${authUrl}/tenant/getAllTenants`
    };
    const response = await createRequest(options);
    Promise.all(response.data.map(async (tenant) => {
      await tenantSqlConnection(tenant.tenant_id, tenant.database_name);
      await passportInitializationForAllTenants(tenant);
      await passportInitializationForAllTenantsAndSCUids(tenant);
    }));
  } catch (error) {
    console.log(error);
  }
};

//handleDisconnect();
module.exports=
  {
    handleDisconnect:handleDisconnect,
    session:session,
    //MySQLStore:MySQLStore,
    sessionSet:sessionSet,
  }
