var fs = require("fs");
var path = require("path");
var sy_routes = {};
var commonFunctions = require('./../../utils/commonFunctions');
const kafkaLib = require('../../utils/kafka/kafka-lib');
const PassThrough = require('stream').PassThrough;
const config = require("config");

let isolateSynonymStopwords = config?config.get('isolateSynonymStopwords'):false;
let pocName = config?config.get('poc_name'):'';
let elStopwordsPath = "stopwords.txt";

const { streamUtils } = require('../../utils/nlp');
const { publishManageIndexesKafka } = require('../admin/service/nlpKafkaService');

// create synonym file if not exists
if((isolateSynonymStopwords && pocName) && !fs.existsSync(path.join(__dirname,'../../synonyms'))){
  try{
    fs.mkdirSync('../../synoyms');
  }catch(e){

  }
}

let dictFile = fs.existsSync(path.join(__dirname + '/dictionaryWords.dic.ignore'));
if (!dictFile) {
  fs.open(path.join(__dirname + '/dictionaryWords.dic.ignore'), 'w', function (err, file) {
    if (err) throw err;
  });
}

exports.index = function(req, res, next) {
    try {
        req.method=req.method=="OPTIONS"?req.headers["access-control-request-method"]:req.method
        sy_routes[req.params.path][req.method](req, res, next);
    } catch (error) {
        console.log("error", error);
        res.send(404);
    }
};

sy_routes['read'] = {};
sy_routes['read']['GET'] = (req, res, next) => {
  commonFunctions.errorlogger.debug('>>>>>>> read stopwords route');
  commonFunctions.errorlogger.debug('req headers : ', req.headers);
  res.setHeader("Content-Type", "text/plain");
  if (config.get("onPremises")) {
    var body = {

    }
    commonFunctions.httpRequest('GET', config.get('onPremisesUrl') + '/getSynonymsStopwords?', '', body, '', function (err, result) {
      if (!err)
        res.send(result);
      else
        res.end();
    })
  } else {
    if (!req.headers.session) {
      req.headers.session = {
        tpk: req.headers.tpk
      };
    }
    if((isolateSynonymStopwords && pocName) && !fs.existsSync(path.join(__dirname,'../../synonyms', `${req.headers.session.tpk}_${elStopwordsPath}`))){
      fs.writeFileSync(path.join(__dirname,'../../synonyms', `${req.headers.session.tpk}_${elStopwordsPath}`),'');
    }
    fs.createReadStream(path.join(__dirname,'../../synonyms', `${req.headers.session.tpk}_${elStopwordsPath}`)).pipe(res);
  }

};
sy_routes.dictWordsRead= {};
sy_routes.dictWordsRead.GET = (req,res,next)=>{
  if(!fs.existsSync(path.join(__dirname + `/${req.headers.session.tpk}_dictionaryWords.dic.ignore`))){
    fs.writeFileSync(path.join(__dirname + `/${req.headers.session.tpk}_dictionaryWords.dic.ignore`),'');
  }
    res.setHeader("Content-Type","text/plain");
    console.log("reading dictionary words");
    fs.readFile(path.join(__dirname + `/${req.headers.session.tpk}_dictionaryWords.dic.ignore`), 'utf8', (error, txt)=>{
      if(error)
          console.log("error",error);
      else{
          let conf, err;
          try{
              conf = (txt);
          } catch(ex){
              err = ex;
          }
          res.send({conf});
      }
    });
};
sy_routes['dictWordsWrite'] = {};
sy_routes['dictWordsWrite']['POST'] = (req, res, next) => {
  res.setHeader("Content-Type", "text/plain");
  console.log("Writing dictionary words");
  fs.writeFile(path.join(__dirname + `/${req.headers.session.tpk}_dictionaryWords.dic.ignore`), req.body.data, 'utf8', async (err, result) => {
    if (err) {
        cb(err);
    } else {
        try {
            await kafkaLib.publishMessage({
                topic: config.get("kafkaTopic.dictionaryWords"),
                messages: [{
                    value: JSON.stringify({
                      data: req.body.data,
                      tpk: req.headers.session.tpk
                    }),
                    key: req.headers['tenant-id']
                }]
            })
             commonFunctions.errorlogger.info("Dict send to search");
            res.send('"Saved"');
        } catch (e) {
            commonFunctions.errorlogger.error(e);
        }
    }
}
);
};

sy_routes['write'] = {};
sy_routes['write']['POST'] = async (req, res, next) => {
  try {
    const tenantId  = req.headers['tenant-id'];
    const sessionTpk = req.headers.session.tpk;

    const stopwordsFilePath = path.join(
      __dirname,
      '../../synonyms',
      `${sessionTpk}_${elStopwordsPath}`
    );
    if (!fs.existsSync(stopwordsFilePath)) {
      fs.writeFileSync(stopwordsFilePath, '');
    }

    const stopwords = new PassThrough();
    req.pipe(stopwords);

    await streamUtils.processStream(
      stopwords,
      stopwordsFilePath,
      "Stopwords processing complete"
    );

    await publishManageIndexesKafka({ OPERATION: "REFRESH", OPERATOR: "STOPWORDS", tpk: sessionTpk, tenantId });
    res.status(200).json({ message: 'success' });
  } catch (error) {
    commonFunctions.errorlogger.error("Error in route write stopwords : ", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};
  