var request = require("request");
var commonFunctions = require('./../../utils/commonFunctions');
const aes256 = require('nodejs-aes256');


const boxAuthorisationIntermediate = function (contentSource, authorization, callback) {

    var client_id = authorization.client_id;
    authUrl = 'https://account.box.com/api/oauth2/authorize?response_type=code&client_id=' + client_id;
    callback(null, { "oauth": authUrl });
}

const getUser  = function(accessToken, contentSourceId, req, callback){
    var options = { 
        method: 'GET',
        url: 'https://api.box.com/2.0/users/me',
        qs: { 
            access_token: accessToken
        },
        headers: {
            'cache-control': 'no-cache' 
        } 
    };
    request(options, function (error, response, body) {
        if(error){
            callback(error, null);
        }
        else if( response.statusCode != 200){
            callback("bad status Code", null);
        }
        else{
            body = JSON.parse(body);
            userID = body.id;
            commonFunctions.getContentSourceDataById(contentSourceId, req, function(err,data){
                if(data.authorization.htaccessUsername)
                {
                    if(data.authorization.htaccessUsername !== userID){
                        commonFunctions.deleteDriveSpace(contentSourceId,req, function (err, result) {
                            if (!err) {
                                callback(null, userID);
                            }
                            else{
                                callback(err, null);
                            }
                        });                        
                    }
                    else{
                        callback(null, userID);                        
                    }
                }
                else{
                    callback(null, userID);
                }
            });
        }
    });
}

const requestTofetchObjectFields = function (authorization, objectName,req, callback) {
    var customFieldArray = [];
    var flag = 7;
    commonFunctions.getContentSourceObjectsAndFieldsById(authorization.content_source_id,req, function (err, res) {
        if (err) {
            callback(err, null)
        }
        else {
          var standardObj = commonFunctions.metadata.loadMappingForDbAndElastic("13").objects;
            var fieldsArr = [];

            for (var i = 0; i < standardObj.length; i++) {
                if (objectName == standardObj[i].name) {
                    flag = 0;
                    fieldsArr = commonFunctions.metadata.loadMappingForDbAndElastic("13").fields.concat(standardObj[i].fields);
                    break;
                }
            }
            var listToAdd = [];
            var listInt = -1;
            for (var type = 0; type < res.length; type++) {
                if (objectName == res[type].name) {
                    listInt = type;
                }
            }
            if (listInt != -1) {
                type = listInt;
                listToAdd = res[type].fields;
                for (var mn = 0; mn < fieldsArr.length; mn++) {
                    var found = 0;
                    for (var field = 0; field < res[type].fields.length; field++) {
                        if (fieldsArr[mn].name == res[type].fields[field].name) {
                            found = 1;
                            break;
                        }
                    }
                    if (found == 0) {
                        listToAdd.push(fieldsArr[mn]);
                    }
                }
                for (var field = 0; field < listToAdd.length; field++) {

                    customFieldArray.push({
                        "name": listToAdd[field].name,
                        "label": listToAdd[field].label,
                        "type": listToAdd[field].type
                    })
                }

            } else {
                for (var field = 0; field < fieldsArr.length; field++) {

                    customFieldArray.push({
                        "name": fieldsArr[field].name,
                        "label": fieldsArr[field].label,
                        "type": fieldsArr[field].type
                    })
                }
            }
            callback(null, { data: customFieldArray, flag: flag });
        }
    })
}


module.exports = {
    boxAuthorisationIntermediate: boxAuthorisationIntermediate,
    requestTofetchObjectFields:requestTofetchObjectFields,
    getUser:getUser
}
