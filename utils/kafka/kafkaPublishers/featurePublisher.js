const kafka = require("../../kafka-status-page/kafka-lib");
var request = require('request');
var config = require("config");
const { errorlogger } = require("../../commonFunctions");

/**
 * featurePublisher - publishes featurePublisher kafka topic with all features available information
**/

const featurePublisher = async (tenantId = null) => {
    try {
      let url = `${config.get('authUrl')}/feature/fetchFeatureDetails`;
      if(tenantId) {
        url = `${url}?tenantId=${tenantId}`;
      };
      const options = {
        method: 'GET',
        url: url,
        headers: {
          'content-type': 'application/json',
          'mt-auth-secret': config.get("multiTenantAuthSecret")
        }
      };
      const featureDetails = await new Promise((resolve, reject) => {
        request(options, function (error, response, body) {
          if (error) {
            errorlogger.error('error',error)
            reject(error);
          } else if (body && body.length) {
            resolve(JSON.parse(body));
          } else {
            reject(`No feature info received`);
          }
        });
      });

      errorlogger.info(`Sending feature details>>`,featureDetails);

      /* publish featurePublisher kafka topic */
      kafka.publishMessage({
          topic: config.get("kafkaTopic.featureListTopic"),
          messages: [
              {
                  value: JSON.stringify(featureDetails),
              },
          ],
      });
      errorlogger.info('featurePublisher<<<');
    } catch (e) {
        errorlogger.error('Encountered error while publishing feature kafka ',e);
    }
};

module.exports = {
    featurePublisher,
};
