

module.exports = {
  "objects": [{
      "name": "doc",
      "label": "Help Docs",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": []

  },{
    "name": "supportdoc",
    "label": "Knowledge Base",
    "status": 1,
    "boosting_factor": 1,
    "base_url": "",
    "fields": []

  },{
    "name": "learningdoc",
    "label": "Learning Hub",
    "status": 1,
    "boosting_factor": 1,
    "base_url": "",
    "fields": []

  }],
  "fields": [{
    "name": "content",
    "label": "Content",
    "type": "string",
    "isFilterable": 1,
    "isSortable": 1,
    "isSearchable": 1,
    "isActive": 1
    },{
      "name": "host",
      "label": "Host",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "id",
      "label": "id",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "language",
      "label": "Language",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "post_time",
      "label": "Created Time",
      "type": "datetime",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 0,
      "isActive": 1
    },{
      "name": "product",
      "label": "Product",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "tag_1",
      "label": "Filter tag 1",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "tag_2",
      "label": "Filter tag 2",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "tag_3",
      "label": "Filter tag 3",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "tag_4",
      "label": "Filter tag 4",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "tag_5",
      "label": "Filter tag 5",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "tag_6",
      "label": "Filter tag 6",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "tag_7",
      "label": "Filter tag 7",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "tag_8",
      "label": "Filter tag 8",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "title",
      "label": "Title",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "view_href",
      "label": "Url",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },{
      "name": "version",
      "label": "Version",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    }
  ]
}
