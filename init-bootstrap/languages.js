var insertLanguages = function(){
    var create_content_languages_table ='CREATE TABLE IF NOT EXISTS content_source_languages (id int(11) NOT NULL AUTO_INCREMENT,content_source_id int(11),code varchar(255),name varchar(255) CHARACTER SET utf8 NOT NULL,analyzer varchar(255),PRIMARY KEY (id))';
    var createLanguageTable = 'CREATE TABLE IF NOT EXISTS languages (id int(11) NOT NULL AUTO_INCREMENT,name varchar(255) CHARACTER SET utf8 NOT NULL,code varchar(255),analyzer varchar(255),label varchar(255), PRIMARY KEY (id))';
    connection.query(createLanguageTable,(error,res,c)=>{
        var sql =`INSERT INTO languages(id,name,code,analyzer,label) VALUES (1,'عربى','ar','custom_ar_analyzer','Arabic'),(2,'հայերեն','hy','custom_hy_analyzer','Armenian'),(3,'Basque','eu','custom_eu_analyzer','Basque'),(4,'Brazilian','pt-br','custom_pt-br_analyzer','Brazilian'),
        (5,'български','bg','custom_bg_analyzer','Bulgarian'),(6,'Català','ca','custom_ca_analyzer','Catalan'),(7,'中文','zh','smartcn','Chinese'),(8,'čeština','cs','custom_cs_analyzer','Czech'),(9,'dansk','da','custom_da_analyzer','Danish'),(10,'Nederlands','nl','custom_nl_analyzer','Dutch'),(11,'English','en','custom_lowercase_stemmed','English'),(12,'Suomalainen','fi','custom_fi_analyzer','Finnish'),
        (13,'français','fr','custom_fr_analyzer','French'),(14,'Galego','gl','custom_gl_analyzer','Galician'),(15,'Deutsche','de','custom_de_analyzer','German'),(16,'Ελληνικά','el','custom_el_analyzer','Greek'),(17,'हिंदी','hi','custom_hi_analyzer','Hindi'),(18,'Magyar','hu','custom_hu_analyzer','Hungarian'),(19,'Indonesian','id','custom_id_analyzer','Indonesian'),(20,'Gaeilge','ga','custom_ga_analyzer','Irish'),
        (21,'italiano','it','custom_it_analyzer','Italian'),(22,'日本人','ja','custom_ja_analyzer','Japanese'),(23,'한국어','ko','openkoreantext-analyzer','Korean'),(24,'Kurdî','ku','custom_ku_analyzer','Kurdish'),(25,'norsk','nn','custom_nn_analyzer','Norwegian'),(26,'فارسی','fa','persian','Persian'),(27,'Polskie','pl','polish','Polish'),(28,'Português','pt','custom_pt_analyzer','Portuguese'),(29,'Română','ro','custom_ro_analyzer','Romanian'),
        (30,'русский','ru','custom_ru_analyzer','Russian'),(31,'Español','es','custom_es_analyzer','Spanish'),(32,'svenska','sv','custom_sv_analyzer','Swedish'),(33,'Türk','tr','custom_tr_analyzer','Turkish'),(34,'ไทย','th','custom_th_analyzer','Thai') ON DUPLICATE KEY UPDATE name=VALUES(name),
        code=VALUES(code),analyzer=VALUES(analyzer)`;
        
        connection.query(sql,(err,res)=>{
            connection.query(create_content_languages_table,(err,result)=>{
            })
        })
        connection.query("UPDATE languages SET analyzer = 'openkoreantext-analyzer' WHERE label = 'Korean'",(err,res)=>{})
        connection.query("ALTER TABLE keyword_boost ADD (href VARCHAR(255),title VARCHAR(255),deindex boolean default 0)",(err,res)=>{})
        connection.query("ALTER TABLE keyword_boost ADD (object_label VARCHAR(255),source_label VARCHAR(255))",(err,res)=>{})
        connection.query("ALTER TABLE search_clients ADD (post_tag VARCHAR(255) NULL,pre_tag VARCHAR(255) NULL)",(err,res)=>{});
        connection.query("ALTER TABLE content_source_authorization ADD (permission VARCHAR(255))",(err,res)=>{})
        connection.query("ALTER TABLE search_clients ADD re_rank_value INT DEFAULT 20",(err,res)=>{});
        connection.query("ALTER TABLE search_clients ADD re_ranking INT NULL",(err,res)=>{});
        connection.query("ALTER TABLE search_clients ADD ner INT NULL",(err,res)=>{});
        connection.query("ALTER TABLE intent_boosting ADD last_state INT NULL",(err,res)=>{});
        connection.query("ALTER TABLE keyword_boost ADD last_state INT NULL",(err,res)=>{});
    })

    let accountTableQuery = `CREATE TABLE IF NOT EXISTS accountDetails(
        id INT AUTO_INCREMENT PRIMARY KEY,
        licenseConsumed INT,
        licenseLimit INT,
        indexSizeConsumed float,
        indexSizeLimit INT,
        contentSourceConsumed INT,
        contentSourceLimit INT,
        documentsConsumed INT,
        documentsLimit INT,
        searchesConsumed INT,
        searchesLimit INT,
        searchApiConsumed INT, 
        searchApiLimit INT,
        contentApiConsumed INT,
        contentApiLimit INT,
        analyticsApiConsumed INT,
        analyticsApiLimit INT,
        searchClientsConsumed INT,
        searchClientsLimit INT,
        month VARCHAR(255) NOT NULL,
        year VARCHAR(255) NOT NULL
    )`
        connection.query(accountTableQuery,(err,response)=>{})

    // Release C21
    let intent_boosting_table = `CREATE TABLE IF NOT EXISTS intent_boosting (
        id int NOT NULL AUTO_INCREMENT,
        search_client_id int NOT NULL,
        intent varchar(255),
        index_name varchar(255),
        index_label varchar(255),
        object_name varchar(255),
        object_label varchar(255),
        document_id varchar(255),
        isBoostingEnabled int NOT NULL,
        rank int  NOT NULL,
        href varchar(255),
        title varchar(255),
        PRIMARY KEY (id),
        FOREIGN KEY (search_client_id) REFERENCES search_clients(id) ON DELETE CASCADE ON UPDATE CASCADE
        )
      `
      connection.query(intent_boosting_table,(err,response)=>{})

   
}

module.exports = {
    insertLanguages:insertLanguages
}
