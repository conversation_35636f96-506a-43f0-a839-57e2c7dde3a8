var path = require('path');
environment = require('./../environment');
var express = require('express');
var router = express.Router();
var request = require('request');
var async = require('async');
var connection_sql = require('./../../utils/connection');
var commonFunctions = require('./../../utils/commonFunctions');
var fs = require('fs');
var knowledgeGraphKafkaConfig = require('./knowledgeGraphKafkaConfig');

router.get('/getSearchClients', function (req, res, next) {
    getSearchClients(req,function (err, data) {
        res.send(data);
    })
});

router.get('/getContentSources', function (req, res, next) {
    getcontentsourceIdsbyUid(req.query.uid, req,function (err, data1) {

        var sql = 'SELECT * FROM content_sources';
        connection[req.headers['tenant-id']].execute.query(sql, function (err, data2) {
            if (err) {
                commonFunctions.errorlogger.error(err);
                res.send({ 'cs': null });
            }
            else {
                var csId = data1.map(function (x){
                    return x.id;
                });
                var csIdUnique = csId.filter(function(item, index){
                    return csId.indexOf(item) >= index;
                });
                var temp = [];
                for (var i = 0; i < csIdUnique.length; i++) {
                    for (var j = 0; j < data2.length; j++) {
                        if (csIdUnique[i] && csIdUnique[i] == data2[j].id) {
                            csTypeId = data1.filter(x=>x.id == csIdUnique[i]);
                            var obj = { 'id': csIdUnique[i], 'type_id': csTypeId[0].type_id, 'label': data2[j].label, 'elasticIndexName': data2[j].elasticIndexName };
                            temp.push(obj);
                        }
                    }
                }
                if (temp.length > 0)
                    res.send({ 'cs': temp });
                else
                    res.send({ 'cs': null });
            }
        });
    })
});

router.get('/getKnowledgeGraphRelation', function (req, res, next) {
    getKnowledgeGraphRelation(req,function (err, data) {
        res.send(data);
    })
});

router.post('/insertKnowledgeGraphRelation', function (req, res, next) {
    commonFunctions.CleanRedisCache();
    insertKnowledgeGraphRelation(req.body.relation,req, function (err, data) {
        res.send({ "status": data });
    })
})

router.get('/deleteKnowledgeGraphRelation', function (req, res, next) {
    deleteKnowledgeGraphRelation(req.query,req, function (err, data) {
        res.send({ "status": data });
    })
})

router.get('/getMapObjectAndFields', function (req, res, next) {
    commonFunctions.getContentSourceObjectsAndFieldsById(req.query.id,req, function (err, data) {
        res.send(data);
    })
})

router.post('/insertObjRelation', function (req, res, next) {
    insertAndUpdateObjectRelation(req.body.objRel,req, function (err, data) {
        if (err) {
            commonFunctions.errorlogger.error("Error while inserting obj Relation" , err);
            res.send({ 'status': 0 })
        }
        else {
            res.send({ 'status': 1 })
        }
    });
})

router.get('/getObjectRelation', function (req, res, next) {
    getObjectRelation(req.query.relId,req, function (err, data) {
        res.send(data);
    })
})

router.get('/deleteObjectRelation', function (req, res, data) {
    deleteObjectRelation(req.query,req, function (err, data) {
        res.send({ "status": data });
    })
})

router.get('/getObjectFields', function (req, res, next) {
    commonFunctions.getObjectFieldsById(req.query.id,req, function (err, data) {
        res.send(data);
    });
})

const getSearchClients = function (req,callback) {
    var sc = [];
    getAllSearchClients(req,function (err, data) {
        for (var i = 0; i < data.length; i++) {
            obj = { 'id': data[i].id, 'name': data[i].name, 'uid': data[i].uid }
            sc.push(obj);
        }
        callback(null, sc);
    })
}

const insertKnowledgeGraphRelation = function (relation,req, callback) {
    var kgArray = [relation.name, relation.scName.id, relation.node1.id, relation.node2.id];
    var sql = 'INSERT INTO knowledge_graph_relation (name, search_client_id, cs_source_id, cs_destination_id) VALUES (?,?,?,?)';
    connection[req.headers['tenant-id']].execute.query(sql, kgArray, function (err, data) {
        if (err) {
            commonFunctions.errorlogger.error(err);
            callback(null, 0);
        }
        else {
            let configObj = { platformId: relation.scName.id};
            sendDataToKafka(configObj,req,);
            callback(null, 1);
        }
    })
}

const deleteKnowledgeGraphRelation = function (query,req, callback) {
    var sql = "DELETE FROM knowledge_graph_relation WHERE id=" + query.id;
    connection[req.headers['tenant-id']].execute.query(sql, function (err, data) {
        if (err) {
            commonFunctions.errorlogger.error(err);
            callback(null, 0);
        }
        else {
            let configObj = { platformId: query.searchClientId};
            sendDataToKafka(configObj,req);
            callback(null, 1);
        }
    })
}

const deleteObjectRelation = function (query,req, callback) {
    var sql = "DELETE FROM k_graph_fields_relation WHERE id=" + query.id;
    connection[req.headers['tenant-id']].execute.query(sql, function (err, data) {
        if (err) {
            commonFunctions.errorlogger.error(err);
            callback(null, 0);
        }
        else {
            let configObj = { platformId: query.searchClientId};
            sendDataToKafka(configObj,req);
            callback(null, 1);
        }
    })
}

const getObjectRelation = function (relId,req, callback) {
    const sql = 'SELECT kgfr.id, kgfr.relation_id, kgfr.mapper_obj_id, kgfr.mapping_obj_id, kgfr.mapper_field_id, kgfr.mapping_field_id, kgfr.tag_line, kgfr.heading_1_id, kgfr.heading_2_id, kgfr.link_id, kgfr.img_id, cso1.name as mapper_obj_name, cso1.label as mapper_obj_label, cso2.name as mapping_obj_name, cso2.label as mapping_obj_label, csof1.name as mapper_field_name, csof1.label as mapper_field_label, csof2.name as mapping_field_name, csof2.label as mapping_field_label '+
                'FROM `k_graph_fields_relation` kgfr '+
                'LEFT JOIN content_source_objects cso1 ON cso1.id = kgfr.mapper_obj_id '+
                'LEFT JOIN content_source_objects as cso2 ON kgfr.mapping_obj_id = cso2.id '+
                'LEFT JOIN content_source_object_fields csof1 ON csof1.id = kgfr.mapper_field_id '+
                'LEFT JOIN content_source_object_fields csof2 ON csof2.id = kgfr.mapping_field_id '+
                'WHERE relation_id = ?';
                connection[req.headers['tenant-id']].execute.query(sql,[relId], function (err, data) {
        if (err) {
            callback(null, []);
        }
        else {
            callback(null, data);
        }
    })
}

const insertAndUpdateObjectRelation = function (objRel,req, callback) {
    let colums = [];
    let parameters = [];

    for (var key in objRel) {
        colums.push(key)
        parameters.push(objRel[key])
    }
    let sqlCS = "INSERT INTO `k_graph_fields_relation`(" + colums +
        ") VALUES (" + colums.map(x => {
            return '?'
        }).join(',') + ") " +
        "ON DUPLICATE KEY UPDATE " + colums.map(x => {
            return x + "=values(" + x + ")"
        }).join(',');
    let q = connection[req.headers['tenant-id']].execute.query(sqlCS, parameters, function (errAsync, rows) {
        if (errAsync) {
            callback(errAsync, null)
        }
        else {
            let configObj = {id: objRel.relation_id, tableName:'knowledge_graph_relation'}
            sendDataToKafka(configObj,req);
            callback(null, 'insertUpdated');
        }
    })
}

const getAllSearchClients = function (req,callback) {
    const sql = 'SELECT * FROM search_clients';
    connection[req.headers['tenant-id']].execute.query(sql, function (err, data) {
        if (err) {
            callback(null, []);
        }
        else {
            callback(null, data);
        }
    });
}

const getKnowledgeGraphRelation = function (req,callback) {
    const sql = `SELECT kgr.id, kgr.name, kgr.search_client_id, kgr.cs_source_id, kgr.cs_destination_id, sc.name as search_client, cs1.name cs_source, cs1.elasticIndexName cs_source_elastic, cs2.name AS cs_destination, cs2.elasticIndexName as cs_destination_elastic FROM knowledge_graph_relation kgr LEFT JOIN search_clients sc ON kgr.search_client_id = sc.id LEFT JOIN content_sources cs1 ON kgr.cs_source_id = cs1.id LEFT JOIN content_sources cs2 ON cs2.id = kgr.cs_destination_id`
    connection[req.headers['tenant-id']].execute.query(sql, function (err, data) {
        if (err) {
            callback(null, []);
        }
        else {
            callback(null, data);
        }
    })
}


const getcontentsourceIdsbyUid = function (uid,req, cb) {
    let sql = "SELECT cs.id,cs.content_source_type_id FROM `search_clients` sc " +
        "left join search_clients_to_content_objects scco on sc.id=scco.search_client_id " +
        "left join content_source_objects cso on scco.content_source_object_id=cso.id " +
        "left join content_sources cs on cso.content_source_id=cs.id " +
        "where sc.uid=? group by cso.name,cs.elasticIndexName"
        connection[req.headers['tenant-id']].execute.query(sql, [uid], function (err, rows) {
        var data = []
        if (!err) {
            if (rows.length) {
                rows.forEach(x => { data.push({ id: x.id, type_id: x.content_source_type_id }) })
                // data = data.filter((x, i, a) => a.indexOf(x) == i) // for unique
            }
            cb(null, data)
        }
        else
            cb(null, data)
    })
}

const sendDataToKafka = (configObj,req) => {
    if (config.get("kafkaTopic.enable") && ( configObj.platformId || (configObj.id && configObj.tableName) ) ) {
        knowledgeGraphKafkaConfig.knowledgeGraphSettings(configObj,req, function (err, result) {
            commonFunctions.errorlogger.info("Knowledge Graph settings updated through kafka ", err);
        })
    }
}

module.exports = {
    router: router,
    getSearchClients: getSearchClients,
    insertKnowledgeGraphRelation: insertKnowledgeGraphRelation
}
