const request = require('request');
const config = require('config');

const urlAuth  = config.get('authUrl');


const authMiddlewareRoute = (req, url, cb) => {
    const options = {
        method: req.method,
        url: urlAuth + url,
        body:  req.body,
        headers: { 'content-type': 'application/json', 'Cookie': req.headers.cookie, 'CSRF-Token' : req.headers['csrf-token']},
        qs:req.query,
        json: true
    }

    if (req.url.includes('/oauth/token')) {
        options.headers['authorization'] = req.headers['authorization'];
        if (req.headers['content-type'] == 'application/x-www-form-urlencoded') {
            options.headers['content-type'] = req.headers['content-type'];
            options.form = options.body;
        }
    }

    request(options, (error, response, body) => {
        if(error){
            cb(error, null)
        }
        cb(null, body);
    })
}

module.exports = {
    authMiddlewareRoute
};