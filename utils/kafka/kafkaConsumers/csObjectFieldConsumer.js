const kafkaLib = require("../kafka-lib");
const commonFunctions = require("../../commonFunctions");
const { getTenantInfoFromTenantId } = require('auth-middleware');
const { tenantSqlConnection  } = require('../../../auth/sqlConnection');
const { findUidAndSendSCSettings } = require('./../../../routes/admin/contentSources');

const mapping = {
    name: "name",
    label: "label",
    type: "type",
    isFilterable: "filterable",
    isSortable: "sortable",
    isSearchable: "searchable",
    isActive: "active",
    selector: "selector",
    single_multiple: "single_multiple",
    fragment_size: "fragment_size",
    isMerged: "merged",
    regex: "regex",
    regex_value: "regex",
    did_you_mean: "did_you_mean",
    annotated: "annotated"
}

const subscribeConsumer = async () => {
    try{
        const analyticsConsumer = await kafkaLib.getConsumer({
            groupId: `admin ${config.get("kafkaTopic.contentSourceObjectFields")}`,
            topic: config.get("kafkaTopic.contentSourceObjectFields"),
            fromBeginning: false
        })
        await analyticsConsumer.run({
            eachMessage: async ({
              message
            }) => {
              try {
                let data    = message.value ? JSON.parse(message.value) : {};

                const tenantInfo = await getTenantInfoFromTenantId(data.tenantId);
                await tenantSqlConnection(tenantInfo[0].tenantId, tenantInfo[0].database_name);
                const req = {
                    headers: {
                        'tenant-id' : tenantInfo[0].tenantId
                    }
                };
                if(data.source && data.source == "sucrawler"){
                    switch(data.type){
                        case 'ADD':{
                            const fields = data.fields;
                            const mysqlFields = [];
            
                            if(!fields.length) return;
                            
                            for(let i=0; i<fields.length; i+=1){
                                const field = {};
            
                                for(let key in mapping){
                                    field[key] = fields[i][mapping[key]];
                                }
            
                                mysqlFields.push(field);
                            }
            
                            const objectArr = {
                                id: data.objectId,
                                name: data.objectName,
                                fields: mysqlFields
                            };
                            const allData = {
                                insert_content_source_id: {
                                    content_source_type_id: data.cs_type_id,
                                    elasticIndexName: data.index_name
                                }
                            };
            
                            
                            commonFunctions.insertFields(allData, objectArr, req, (err,res) => {
                                if(err) commonFunctions.errorlogger.info("Error while inserting field from su-crawler", err);
                                else commonFunctions.errorlogger.info("Successfully inserted fields from su-crawlerr");
                            });
                        }   
                            break;
                        case 'DELETE':{
                            commonFunctions.errorlogger.info(`Received kafka request to delete fields ${JSON.stringify(data)}`);
                            const {objectId,fields,csMysqlId} = data;
                            if(!fields.length) return;

                            let query = `delete from content_source_object_fields where content_source_object_id=${objectId} `;
                            query += `and name in ('${fields.map(e => e.name).join("','")}')`;
                            connection[tenantInfo[0].tenantId].execute.query(query, (err) => {
                                if(err)
                                    commonFunctions.errorlogger.error(`Error occurred while deleting field | ${err}`);
                                else{
                                    commonFunctions.errorlogger.info(`Successfully removed field from database`);

                                    findUidAndSendSCSettings( csMysqlId, req, function (errorKafka) {
                                        if (errorKafka)
                                        commonFunctions.errorlogger.error(`Error while sending kafka event for search client | ${errorKafka}`);
                                        else{ 
                                            commonFunctions.errorlogger.info(`Kafka event published successfully for search client`);
                                        }
                                    });
                                }
                            });
                        }
                            break;
                        default:
                            commonFunctions.errorlogger.info('Unknown type value received :', type);
                    }
                }else{
                    commonFunctions.errorlogger.info(`Kafka message not processed | Action: ${data.type} | Source: ${data.source}`);
                }
              } catch (e) {
                  console.log(e)
              }
            }
          });
    }catch(error){
        if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
            kafkaLib.createTopic(config.get("kafkaTopic.contentSourceObjectFields"));
          }
    }
}

module.exports = {
    subscribeConsumer
}
