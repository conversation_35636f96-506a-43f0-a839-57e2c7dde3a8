#!/bin/bash

months=("jan" "feb" "mar" "apr" "may" "jun" "jul" "aug" "sep" "oct" "nov" "dec")
start_year='25'
selected=0
current_year=$(date +'%y')
current_month=$(date +'%h' | awk '{print tolower(substr($0, 1, 3))}')
options=()

for ((year=start_year; year<=current_year; year++)); do
    if [ "$year" -eq "$start_year" ]; then
        start_month=2
    else
        start_month=0
    fi
    for ((idx=start_month; idx<12; idx++)); do
        month="${months[$idx]}"
        options+=("$year-$month")
        if [ "$year-$month" == "24-feb" ]; then
            options+=("24-feb-opensearch")
        fi
        if [ "$year-$month" == "$current_year-$current_month" ]; then
            break
        fi
    done
done


print_options() {
    clear
    local idx=0
    echo -e "WARNING !! This script will reset your branch to remote, please make sure you've saved local changes !!"
    echo -e "Current year is: $current_year\n"
    echo -e "Current month is: $current_month\n"
    echo -e "listing all branch options...\n";
    echo -e "Please select the branch to start to move forward process for commits\n"
    for option in "${options[@]}"; do
        if [[ $idx -eq $selected ]]; then
            printf "\e[7m%s\e[0m\n" "$option"
        else
            printf "%s\n" "$option"
        fi
        ((idx++))
    done
}

main() {
    local key
    while true; do
        print_options

        IFS=

        read -rsn1 key
        case "$key" in
            $'\e') # Escape sequence for arrow keys
                read -rsn2 key2
                case "$key2" in
                    $'[A') ((selected = (selected - 1 + ${#options[@]}) % ${#options[@]}));;
                    $'[B') ((selected = (selected + 1) % ${#options[@]}));;
                esac
            ;;
            $'')
                echo "enter press";
                break;;
        esac
    done
}

main
echo -e "\nYou selected ${options[$selected]}"
echo -e "\nStarting auto merge [ this process would exit on first failure /conflicts ]"


array_length="${#options[@]}";
current_branch=$(git rev-parse --abbrev-ref HEAD)
echo -e "\nResetting origin to origin/$current_branch for $current_branch"
git reset --hard origin/"$current_branch"
git pull

for ((id=selected; id<array_length-1; id++)); do
    next=$((id+1))
    echo "${options[$next]}"
    git checkout "${options[$next]}"
    git pull
    echo -e "\n merging ${options[$id]} into ${options[$next]}\n"
    git merge --no-ff --no-edit "origin/${options[$id]}"
    if [ $? -ne 0 ]; then
        echo -e "Merge conflict detected. Exiting...\n"
        echo -e "Please manually fix the conflicts and resume the script from the fixed branch"
        exit 1
    else
        git push
    fi
done
