

module.exports = {
    "objects": [{
        "name": "list",
        "label": "List",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    },
    {
        "name": "page",
        "label": "Page",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    },
    {
        "name": "document",
        "label": "Document",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [{
        "name": "id",
        "label": "id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "ParentSiteName",
        "label": "Parent Site Name",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "ParentSiteUrl",
        "label": "Parent Site Url",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "Author",
        "label": "Author Name",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "Title",
        "label": "Title",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "view_href",
        "label": "View Href",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "post_time",
        "label": "Created Date",
        "type": "datetime",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 0,
        "isActive": 1,
        "isMerged":1
    },
    {
        "name": "updatedDate",
        "label": "Modified Date",
        "type": "datetime",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 0,
        "isActive": 1
    },
    {
        "name": "content",
        "label": "Description",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "AttachmentName",
        "label": "Attachment Name",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "Attachment",
        "label": "Attachment Data",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "commentCount",
        "label": "Comment Count",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "likeCount",
        "label": "Like Count",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "fileRef",
        "label": "File Ref",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        name: 'attachment_multiple',
        label: 'Attachment Multiple',
        type: 'string',
        isFilterable: 1,
        isSortable: 1,
        isSearchable: 1,
        isActive: 1
    }]
}
