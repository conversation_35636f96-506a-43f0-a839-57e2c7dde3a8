/*
    Created By: <PERSON><PERSON> Date: 1/11/2017
    Description: <PERSON><PERSON><PERSON> for SAP help docs using sap's internal apis
*/
var path = require('path');
environment = require('../../../../routes/environment');
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
var request = require("request");
var fs = require("fs");
var elastic = require('elasticsearch');
var async = require('async');
var commonFunctions = require('../../../../utils/commonFunctions');
var htmlToText = require('html-to-text');
var itr=1; 
var crawlstatus = {};
var initialOffset = 0;   // this is starting point to start crawling from in future (incremental crawl)
var config2 = fs.readFileSync(path.join(__dirname,'config.json'),"utf8");
console.log('config ',__dirname, config2);
config2 = JSON.parse(config2);
var crawlstatusfilepath = '../../../../logs/helpdocs/crawlstatus.json';
var sf_index = config2.elasticIndex;
var sf_type = config2.elasticType;

client = new elastic.Client({
    host: config.get('elasticIndexCS.host')+':'+config.get('elasticIndexCS.port')
});

// read headers information to send with api call
var h = JSON.parse(fs.readFileSync('helpdocs/sf_headers.json'));

// read latest cookie generated using selenium crawler
var latestCookiesData = fs.readFileSync('../../../../cookies/helpdoccookie.txt').toString();

h.Cookie = latestCookiesData.split('\n').join('; ');
counter = 1;
var bulkqueries = [];
var skipcount=0;
var deliverablecount = 1;

// function to fetch deliverables(products) listed by sap 
function fetchDeliverables(page, size){
    var headers = h;
    
    var options = {
        url: 'https://help.sap.com/http.svc/deliverables?$expand=project&draw=4&columns%5B0%5D%5Bdata%5D=title&columns%5B0%5D%5Bname%5D=&columns%5B0%5D%5Bsearchable%5D=true&columns%5B0%5D%5Borderable%5D=true&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=version&columns%5B1%5D%5Bname%5D=&columns%5B1%5D%5Bsearchable%5D=true&columns%5B1%5D%5Borderable%5D=true&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=versionName&columns%5B2%5D%5Bname%5D=&columns%5B2%5D%5Bsearchable%5D=true&columns%5B2%5D%5Borderable%5D=true&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=state&columns%5B3%5D%5Bname%5D=&columns%5B3%5D%5Bsearchable%5D=true&columns%5B3%5D%5Borderable%5D=true&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=production&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=locale&columns%5B4%5D%5Bname%5D=&columns%5B4%5D%5Bsearchable%5D=true&columns%5B4%5D%5Borderable%5D=true&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=en-US&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B5%5D%5Bdata%5D=build&columns%5B5%5D%5Bname%5D=&columns%5B5%5D%5Bsearchable%5D=true&columns%5B5%5D%5Borderable%5D=true&columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B6%5D%5Bdata%5D=transtype&columns%5B6%5D%5Bname%5D=&columns%5B6%5D%5Bsearchable%5D=true&columns%5B6%5D%5Borderable%5D=true&columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=html5.uacp&columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B7%5D%5Bdata%5D=uploadedDate&columns%5B7%5D%5Bname%5D=&columns%5B7%5D%5Bsearchable%5D=true&columns%5B7%5D%5Borderable%5D=true&columns%5B7%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B7%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B8%5D%5Bdata%5D=product&columns%5B8%5D%5Bname%5D=&columns%5B8%5D%5Bsearchable%5D=true&columns%5B8%5D%5Borderable%5D=true&columns%5B8%5D%5Bsearch%5D%5Bvalue%5D=successfactor&columns%5B8%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B9%5D%5Bdata%5D=productName&columns%5B9%5D%5Bname%5D=&columns%5B9%5D%5Bsearchable%5D=true&columns%5B9%5D%5Borderable%5D=true&columns%5B9%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B9%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B10%5D%5Bdata%5D=projectName&columns%5B10%5D%5Bname%5D=&columns%5B10%5D%5Bsearchable%5D=true&columns%5B10%5D%5Borderable%5D=true&columns%5B10%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B10%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B11%5D%5Bdata%5D=loio&columns%5B11%5D%5Bname%5D=&columns%5B11%5D%5Bsearchable%5D=true&columns%5B11%5D%5Borderable%5D=true&columns%5B11%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B11%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B12%5D%5Bdata%5D=buildableMapLOIO&columns%5B12%5D%5Bname%5D=&columns%5B12%5D%5Bsearchable%5D=true&columns%5B12%5D%5Borderable%5D=true&columns%5B12%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B12%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B13%5D%5Bdata%5D=groups&columns%5B13%5D%5Bname%5D=&columns%5B13%5D%5Bsearchable%5D=true&columns%5B13%5D%5Borderable%5D=false&columns%5B13%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B13%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B14%5D%5Bdata%5D=appUrl&columns%5B14%5D%5Bname%5D=&columns%5B14%5D%5Bsearchable%5D=true&columns%5B14%5D%5Borderable%5D=true&columns%5B14%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B14%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B15%5D%5Bdata%5D=contextType&columns%5B15%5D%5Bname%5D=&columns%5B15%5D%5Bsearchable%5D=true&columns%5B15%5D%5Borderable%5D=true&columns%5B15%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B15%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B16%5D%5Bdata%5D=system&columns%5B16%5D%5Bname%5D=&columns%5B16%5D%5Bsearchable%5D=true&columns%5B16%5D%5Borderable%5D=true&columns%5B16%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B16%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B17%5D%5Bdata%5D=notSearchableFromOutside&columns%5B17%5D%5Bname%5D=&columns%5B17%5D%5Bsearchable%5D=true&columns%5B17%5D%5Borderable%5D=true&columns%5B17%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B17%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B18%5D%5Bdata%5D=public&columns%5B18%5D%5Bname%5D=&columns%5B18%5D%5Bsearchable%5D=true&columns%5B18%5D%5Borderable%5D=true&columns%5B18%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B18%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B19%5D%5Bdata%5D=indexerId&columns%5B19%5D%5Bname%5D=&columns%5B19%5D%5Bsearchable%5D=true&columns%5B19%5D%5Borderable%5D=true&columns%5B19%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B19%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B20%5D%5Bdata%5D=author&columns%5B20%5D%5Bname%5D=&columns%5B20%5D%5Bsearchable%5D=true&columns%5B20%5D%5Borderable%5D=true&columns%5B20%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B20%5D%5Bsearch%5D%5Bregex%5D=false&order%5B0%5D%5Bcolumn%5D=3&order%5B0%5D%5Bdir%5D=desc&start='+(page*size)+'&length='+size+'&search%5Bvalue%5D=&search%5Bregex%5D=false&%24top='+size+'&%24page='+(page+1)+'&%24pageCount='+size+'&%24orderby=uploadedDate%3ADESCENDING&title=&version=&versionName=&state=production&locale=en-US&build=&transtype=html5.uacp&uploadedDate=&product=successfactor&productName=&projectName=&loio=&buildableMapLOIO=&groups=&appUrl=&contextType=&system=&notSearchableFromOutside=&public=&indexerId=&author=&_=1508825659847',
        headers: headers,
        json: true
    };

    function callback(error, response, body) {

        // callback returns array of deliverables with each deliverable with some basic information
        if (!error && response.statusCode == 200) {
            //console.log(new Date()+':'+body);
            //body = JSON.parse(body);
            if(body.status && body.status == "OK"){
                var requrls = body.data.d.results;
                console.log(new Date()+':'+'Starting parsing of '+requrls.length+' products.');

                // for each deliverable recieved we create an object to request corresponding data
                requrls = requrls.map(function(item){

                    var newobj = {
                        "_id" : "https://help.sap.com/viewer/"+item.loio+"/"+item.version+"/"+item.locale,
                        "_source":{
                            "product" : item.product,
                            "transtype" : item.transtype,
                            "title" : item.title,
                            "versionName" : item.versionName,
                            "locale" : item.locale,
                            "uploadedDate" : item.uploadedDate,
                            "version" : item.version,
                            "buildableMapLOIO" : item.buildableMapLOIO,
                            "productName" : item.productName,
                            "build" : item.build,
                            "id" : "https://help.sap.com/viewer/"+item.loio+"/"+item.version+"/"+item.locale,
                            "state" : item.state,
                            "loio" : "loio"+item.loio,
                            "projectName" : item.productName,
			                "deliverable_id":item.id
                        }
                    };
                    //console.log(new Date()+':'+newobj);
                    return newobj;
                });
                // For each deliverable fetch data using another api call
                fetchAllContent(requrls, function(){
                    if(body.data.d.meta.currentPageTotal == size){
                        fetchDeliverables(page+1, size);
                    }else{
                        crawlstatus.offset = 0;
                        fs.writeFileSync(crawlstatusfilepath,JSON.stringify(crawlstatus));
                        console.log(new Date()+':'+'All deliverables are parsed.');
                    }
                });
            }
        }else{
	        console.log('Error while fetching deliverables ',error);
            fetchDeliverables(page,size);
            console.log(error, response);
        }
    }

    console.log(new Date()+':'+'requesting for page: '+deliverablecount);
    request(options, callback);
}

function fetchAllContent(requrls, callback){
    requrls = requrls.slice((initialOffset-5)<0?0:(initialOffset-5), requrls.length);
    //console.log(new Date()+':'+requrls);
    requrls = requrls.map(function(item){
        item = item._source;
        
        if(item.state && item.loio && item.loio.length > 5 && item.version && item.locale && item.transtype == 'html5.uacp'){
            // filter for successfactor docs
            if(item.product && item.product.toLowerCase().indexOf('successfactor')>=0 && item.state.toLowerCase().trim() == 'production'){
                return {"deliverable_id": item.deliverable_id, "toc": 1, "state": item.state, "deliverableInfo": 1, "deliverable_loio": item.loio.substring(4), "version": item.version, "language": item.locale, "url": item.id, "versionName":item.versionName};
            }else
                return {};
        }else
            return {};
    });
    //console.log(new Date()+':'+JSON.stringify(requrls));
    var timeout = (initialOffset-5)<1?1:(initialOffset-5);
    crawlstatus.offset = timeout;
    initialOffset=0;
    var funcarray = requrls.map(function(item){
        return function(cb){
            //console.log(new Date()+':'+JSON.stringify(item));
            if(item.toc){
                
                /* setTimeout(function(){ */
                    var options = { 
                        method: 'GET',
                        url: 'https://help.sap.com/http.svc/pagecontent',
                        qs: item,
                        headers: h,
                        json: true
                    };
                    console.log('query parameters to deliverable: ', item);
                    console.log(new Date()+':'+timeout+': requesting for: '+item.deliverable_loio);
                    // updating status in file for help docs crawled
                    crawlstatus.offset++;
                    fs.writeFileSync(crawlstatusfilepath,JSON.stringify(crawlstatus));
                    (function tryRequest(cb2, tries1){
                        if(tries1>=10){
                            console.log('Tries finished.');
                            cb2(null, {'success': true});
                        }else{
                            request(options, function (error, response, body) {
                                if (error){
                                    timeout++;
                                    console.log('some error: ', error || response.headers);
                                    console.log('Retrying level 1...');
                                    tryRequest(cb2, ++tries1);
                                    return;
                                } 
                                try{
                                    console.log(new Date()+':'+JSON.stringify(body));
                                    //body = JSON.parse(body);  
                                    if(body.data && body.data.deliverable!=''){
                                        
                                        var source = body.data;
                                        var baseUrl = item.url;
                                        
                                        var tree = source.deliverable.fullToc;
                                        (function parseTOC(subtree, cbparse){
                                            var timeout2 = timeout;
                                            if(subtree){
                                                var funcarrayin = subtree.map(function(itemin){
                                                    return function(cbin){
                                                        var ctitle = itemin.t;
                                                        var curl = itemin.u;
                                                        var childtree = itemin.c;
                                                        var id = baseUrl+'/'+curl;
                                                        if(curl){
                                                            (function tryMore(cbin2, tries){
                                                                if(tries >= 10){
                                                                    console.log('tries exausted.');
                                                                    cbin2(null, {'success':true});
                                                                }else{
                                                                    var params = {
                                                                        //"toc":"0",
                                                                        //"state":item.state,
                                                                        "deliverableInfo":"1",
                                                                        "deliverable_loio":item.deliverable_loio,
                                                                        //"version":item.version,
                                                                        //"language":item.language,
                                                                        "file_path":curl,
									"deliverable_id":item.deliverable_id
                                                                    };
                                                                    var optionsin = { 
                                                                        method: 'GET',
                                                                        url: 'https://help.sap.com/http.svc/pagecontent',
                                                                        qs: params,
                                                                        headers:h,
                                                                        json: true 
                                                                    }; 
                                                                    request(optionsin, function (errorin, responsein, bodyin) {
                                                                        try{
                                                                            if (errorin){ 
                                                                                console.log(new Date()+':'+counter+':'+id+':'+errorin);
                                                                            }
                        
                                                                            if(!errorin){
                                                                                console.log(new Date()+':'+counter+': '+id,item);
                                                                                //bodyin = JSON.parse(bodyin);
										//console.log('===>',responsein);
                                                                                if(!bodyin.data || !bodyin.data.currentPage){
											console.log('returning from unintended result');
                                                                                    cbin(null, {"success":true});
                                                                                    return;
                                                                                }
                                                                                var newsource = {};
                                                                                newsource.post_time = new Date(bodyin.data.currentPage.lastModifiedDate.time);
                                                                                newsource.host='help.sap.com';
                                                                                newsource.view_href=id;
                                                                                newsource.id=id;
                                                                                newsource.content=htmlToText.fromString(bodyin.data.body, {wordwrap: false, ignoreHref: true, ignoreImage: true});
                                                                                newsource.content = newsource.content.replace('* Skip to content\n * Skip to navigation\n * Skip to footer\n\n','');
                                                                                newsource.title=bodyin.data.currentPage.t+' (Version: '+item.versionName+')\n'+body.data.deliverable.title;
                                                                                newsource.product=body.data.deliverable.productName;
                                                                                if(newsource.product && newsource.product.indexOf('SAP SuccessFactors ') == 0){
                                                                                    newsource.product = newsource.product.replace('SAP SuccessFactors ', '');
                                                                                }
                                                                                newsource.version=item.versionName;
                                                                                newsource.language='English';
                                                                                newsource.Category=bodyin.data.currentPage.t;
                                                                                newsource.uploadDate = body.data.deliverable.uploadDate;
                                                                                newsource.lastModifiedDate = body.data.deliverable.lastModifiedDate;
                                                                                //newsource.applicationVersion = body.data.deliverable.applicationVersion;
                                                                                var tags = bodyin.data.breadcrumb;
                                                                                for(k=0;k<3;k++){
                                                                                    if(k<tags.length-1)
                                                                                        newsource["tag_"+(k+1)]=tags[k].t;
                                                                                    else
                                                                                        newsource["tag_"+(k+1)]="Other";
                                                                                }
                                                                                newsource.tstamp = newsource.post_time;
                                                                                newsource.boost = 0;
                                                                                newsource.boardId = "";
                                                                                newsource.privacy = 'open';
                                                                                counter++;
                                                                                bulkqueries.push({"index":{ "_index": sf_index, "_type": sf_type, "_id": newsource.id }});
                                                                                bulkqueries.push(newsource);
                                                                                if(bulkqueries.length>=50){
                                                                                    bulkindex(bulkqueries);
                                                                                    bulkqueries = [];
                                                                                }
                                                                                //setTimeout(()=>{
                                                                                    cbin2(null, {"success":true, "c": childtree});
                                                                                //},1000);
                                                                                
                                                                            }else{
                                                                                //setTimeout(function(){
                                                                                    console.log('Retrying..');
                                                                                    tryMore(cbin2, ++tries);
                                                                                //},1000);
                                                                            }
                                                                        }catch(e){
										
                                                                            console.log(new Date()+':',e);
                                                                            cbin2(null, {"success":true});
                                                                        }
                                                                    
                                                                    });
                                                                }
                                                                
                                                            })(cbin, 1);
                                                        }else{
                                                            cbin(null, {"success":true, "c": childtree});
                                                        }
                                                    };
                                                });
            
                                                async.parallelLimit(funcarrayin, 5, function(errarr, resarr){
                                                    var parsefunarr = resarr.filter(function(ctree){return ctree.success;}).map(function(ctree){
                                                        return function(cbinparse){parseTOC(ctree.c, cbinparse);};
                                                    });
            
                                                    async.parallelLimit(parsefunarr,5, function(epin, rpin){
                                                        cbparse(null, {"success":true});
                                                    });
                                                });
                                            }else{
                                                cbparse(null, {"success":true});
                                            }
                                        })(tree, function(parseerr, parseres){
                                            source.body = commonFunctions.stripHtml(source.body);
                                            bulkqueries.push({"index":{ "_index": sf_index, "_type": sf_type, "_id": baseUrl }});
                                            var newsource = {};
                                            newsource.post_time = new Date();
                                            newsource.host='help.sap.com';
                                            newsource.view_href=baseUrl;
                                            newsource.id=baseUrl;
                                            newsource.content=htmlToText.fromString(body.data.body, {wordwrap: false, ignoreHref: true, ignoreImage: true});
                                            newsource.title=body.data.deliverable.productName;
                                            newsource.product=body.data.deliverable.productName;
                                            newsource.version=body.data.deliverable.versionName;
                                            newsource.language=body.data.deliverable.versionName;
                                            counter++;
                                            bulkqueries.push(newsource);
                                            if(bulkqueries.length>=50){
                                                bulkindex(bulkqueries);
                                                bulkqueries = [];
                                            }
                                            timeout++;

                                            //setTimeout(() => {
                                                cb2(null, {"success":true});
                                            //},1000);
                                            
                                            
                                        });
                                    }else{
					console.log('something wrong with the url - ',JSON.stringify(body));
                                        timeout++;
                                        cb2(null, {"success":false});
                                    }  
                                }catch(e){
                                    timeout++;
                                    fs.writeFileSync('../../../../logs/helpdocs/errorout_'+new Date().getTime()+'.txt',JSON.stringify(body));
                                    cb2(null, {"success":false});
                                } 
                            });
                        }
                    })(cb, 1);
                    
                /* }, timeout*1000);
                timeout++; */
            }else{
                timeout++;
                cb(null, {"success":true});
            }
            
        }
    });

    async.parallelLimit(funcarray,1, function(e,r){
        bulkindex(bulkqueries);
        callback();
    });

    function bulkindex(b){
        if(b.length>0){
            console.log(new Date()+':'+'length: '+b.length);
            client.bulk({
                body: b
            }, function(err, res){
                if(err)
                    console.log(new Date()+':'+'Error while executing bulk queries: '+JSON.stringify(err));
                else
                    console.log(new Date()+':'+'after executing bulk queries: success');

            });
        }
    }
}

if(fs.existsSync(crawlstatusfilepath)){
    console.log(new Date()+':'+'fetching previous crawling status');
    try{
        crawlstatus = JSON.parse(fs.readFileSync(crawlstatusfilepath),"utf8");
        if(typeof crawlstatus.offset == 'undefined'){
            crawlstatus.offset = 0;
        }
        console.log(new Date()+':'+JSON.stringify(crawlstatus));
    }catch(e){

    }
}else{
    console.log(new Date()+':'+'No previous crawling info found. Starting fresh');
}
if(typeof crawlstatus.offset == 'undefined'){
    crawlstatus = {};
    crawlstatus.offset = 0;
    crawlstatus.startDate = new Date().toISOString();
    fs.writeFileSync(crawlstatusfilepath,JSON.stringify(crawlstatus));
}
initialOffset = crawlstatus.offset;
fetchDeliverables(Math.floor(crawlstatus.offset/500),500);
   