const subscribeBotConsumer = require("./botAnalytics");
const similarSearchConsumer = require("../../../routes/admin/similarSearchRecommend");
const spellCheckConsumer = require("../../../customPlugins/semanticSearch/cron/spellCheckGraph");
const autoBoosting = require('../../../customPlugins/semanticSearch/cron/updateSessionPreferences');
const csCrawlUpdateConsumer = require('../../../routes/admin/updateCsCrawlData');
const csObjectFieldConsumer = require('./csObjectFieldConsumer');
const tenantPublished = require('./tenantPublished');
const csAuthTokens = require('./csAuthTokens');
const llmKafka = require('./llmKafka');
const didYouMeanTrainStatusConsumer = require('./didYouMeanTrainStatusConsumer');
const rateLimit = require('./updateRateLimits.js');
const sendArticleFeedbackToAdmin = require("./sendArticleFeedbackToAdmin");
const featureEngagementTopic = require('./featureEngagementConsumer.js');

const subscribeConsumers = async () => {
    subscribeBotConsumer.subscribeConsumer();
    spellCheckConsumer.subscribeConsumer();
    similarSearchConsumer.subscribeConsumer();
    autoBoosting.subscribeConsumer();
    csCrawlUpdateConsumer.subscribeCsCrawlEvents();
    csObjectFieldConsumer.subscribeConsumer();
    sendArticleFeedbackToAdmin.subscribeConsumer();
    tenantPublished.subscribeConsumer();
    csAuthTokens.subscribeConsumer();
    llmKafka.subscribeConsumer();
    rateLimit.subscribeConsumer();
    didYouMeanTrainStatusConsumer.subscribeConsumer();
    featureEngagementTopic.subscribeConsumer();
};

module.exports = {
  subscribeConsumers
};
