const request = require('request');
const config = require('config');
const setCookie = require('set-cookie-parser');
const createRequest = require('./client');
const { setTenantToHeaders } = require('./commonFunctions');

const urlAuth  = config.get('authUrl');

exports.sendAdminResponse = (req, res) => {
    const splitCookie = setCookie.splitCookiesString(req.headers['set-cookie']);
    const cookies = setCookie.parse(splitCookie);
    cookies.forEach((item) => {
      res.cookie(item.name, item.value, { maxAge: 1800000 , httpOnly: true });
    });
    if(req.body && req.body.userData && req.body.userData.userInfo){
        delete req.body.userData.userInfo;
    }
    res.send(req.body.userData);
}

exports.adminLogin = (req, res, url, cb) => {
    const cookie = req.headers.cookie;
    var options = {
        "method": "POST",
        "url": urlAuth + url,
        "headers": { 'content-type': 'application/json', 'Cookie': cookie, 'referer': req.headers.referer },
        "body": JSON.stringify(req.body)

    };
    request(options, function (err, response, body) {
        if (err) {
            console.log("ERROR :: " + err);
            cb(err, null);
        } else {
            try{
                req.headers['set-cookie'] = response.headers['set-cookie'];
                const data = JSON.parse(body);
                if (data.flag !== 150 && data.code !== 200) {
                    cb(body, null);
                } else {
                    if(data && data.path === 'getLogin'){
                        res.send(data);
                    }else{
                        setTenantToHeaders(req, data.userInfo,()=>{
                            req.body.userData = data;
                            cb(null ,data);
                        });
                    }
                }
            }catch(err){
                console.log("Error while connecting to auth server")
                cb(null,{});
            }
        }
    })
}


exports.fetchTenant = (req,cb) => {
    // if(!req.headers['csrf-token']){
    //     const splitCookie = setCookie.splitCookiesString(req.headers.cookie);
    //     const cookies = setCookie.parse(splitCookie);
    //     cookies.forEach((item) => {
    //         if(item._csrf){
    //             req.headers['csrf-token'] = item._csrf;
    //         }
    //     });
    // }

    var options = {
        "method": "GET",
        "url": `${urlAuth}/verification/getTenant`,
        "headers": { 'content-type': 'application/json', 'Cookie': req.headers.cookie, 'CSRF-Token' : req.headers['csrf-token']},
        "timeout": 10000
    };
    request(options, function (err, response, body) {
        if (err) {
            console.log("ERROR :: " + err);
        } else {
            try{
                const data = JSON.parse(body);
                if (data.code && data.code !== 200) {
                    cb(body, null);
                } else {
                    setTenantToHeaders(req, data, cb);
                }   
            }catch(error){
                console.log("Error while connecting to auth server")
            }
        }
    })
}

exports.fetchTenantFromSubdomain = (req, cb) => {
    const subdomain = req.query.subdomain || req.hostname;
    var options = {
        "method": "POST",
        "url": `${urlAuth}/verification/getTenantFromSubdomain`,
        "headers": { 'content-type': 'application/json' },
        "body":{
            subdomain: req.query.subdomain
        }
    };
    request(options, function (err, response, body) {
        if (err) {
            console.log("ERROR :: " + err);
        } else {
            try{
                const data = JSON.parse(body);
                if (data.code && data.code !== 200) {
                    cb(body, null);
                } else {
                    setTenantToHeaders(req, data[0], cb);
                }   
            }catch(error){
                console.log("Error while connecting to auth server")
            }
        }
    })
}

exports.authMiddlewareRoute = (req, url, cb) => {
    const options = {
        method: req.method,
        url: urlAuth + url,
        body:  req.body,
        headers: { 'content-type': 'application/json', 'Cookie': req.headers.cookie, 'CSRF-Token' : req.headers['csrf-token']},
        qs:req.query,
        json: true
    }

    if (req.url.includes('/oauth/token')) {
        options.headers['authorization'] = req.headers['authorization'];
        if (req.headers['content-type'] == 'application/x-www-form-urlencoded') {
            options.headers['content-type'] = req.headers['content-type'];
            options.form = options.body;
        }
    }

    request(options, (error, response, body) => {
        if(error){
            cb(error, null)
        }else{
            cb(null, body);
        }
    })
}

exports.generateTableauToken = (req, res, cb) => {
    var options = {
        method: req.method,
        url: `${urlAuth}/oauth/verifyTableau`,
        headers: req.headers,
        body:  req.body,
        qs:req.query,
        json: true
    };
    options.headers['original-url'] = req.url
    request(options, function (err, response, body) {
        if (err) {
            console.log("ERROR :: " + err);
        } else {
            try{
                if (!body) cb(null);
                else {
                    res.locals = {...body};
                    setTenantToHeaders(req, body, cb);
                }   
            }catch(error){
                console.log("Error while connecting to auth server")
            }
        }
    });
}

exports.authTokenMiddlewareRoute = (req, res, cb) => {
    const url = req.url;
    var options = {
        method: req.method,
        url: `${urlAuth}/oauth/verifyApiAccess`,
        headers: req.headers,
        json: true,
        referer: req.headers.referer
    };
    options.headers['original-url'] = req.url
    if (req.headers['content-type'] == 'application/x-www-form-urlencoded')
        options.form = options.body;
    if (/^\/authorise/.test(url)) // || /^\/authorise_success/.test(url))
        options.url = `${urlAuth}${req.url}`;

    try{
        request(options, (error, response, body) => {
            if(error){
                cb(error, null)
            } else {
                if (response.statusCode != 200) cb(response.statusMessage || body);
                //const data = JSON.parse(body);
                else {
                    req.body = { ...req.body, ...body};
                    res.locals = {...body};
                    setTenantToHeaders(req, body, cb);
                }
            }
        })
    }catch(error){
        console.log("Error while connecting to auth server")
    }
}

exports.getTenantInfoFromUid = async (uid) => {
    const options = {
        method: 'GET',
        url: `${urlAuth}/verification/getTenantInfoFromUid`,
        qs:{
            uid
        }
    };

    const response = await createRequest(options);

    return response.data;
}
exports.getTenantInfoFromTenantId = async (tenantId) => {
    const options = {
        method: 'GET',
        url: `${urlAuth}/verification/getTenantInfoFromTenantId`,
        qs:{
            tenantId
        }
    };
    const response = await createRequest(options);
    return response.data;
}
exports.getTenantsRateLimitInfo = async() => {
    const options = {
        method: 'GET',
        url: `${urlAuth}/tenant/getTenantsRateLimitInfo`
    };
    const response = await createRequest(options);
    return response.data;
}

exports.getAccessTokenFromTenantId = async (tenantId) => {
    const options = {
        method: 'GET',
        url: `${urlAuth}/tenant/getAccessTokenFromTenantId`,
        qs:{
            tenantId
        }
    };
    const response = await createRequest(options);
    return response.data;
};


exports.getLoginDetailsFromAuth = async (email, accessToken) => {
    const options = {
        method: 'GET',
        url: `${urlAuth}/user/getLoginDetails`,
        qs:{
            email,
            accessToken
        }
    };
    const response = await createRequest(options);
    return response.data;
}

exports.getTenantInfoFromTenantHash = async (tenantHash) => {
    const options = {
        method: 'GET',
        url: `${urlAuth}/tenant/getTenantInfoFromTenantHash`,
        qs:{
            tenantHash
        }
    };
    const response = await createRequest(options);
    return response.data;
}

exports.getSandboxToProdMappingData = async (tenantId) => {
    const options = {
        method: 'GET',
        url: `${urlAuth}/verification/getSandboxToProdMappingData`,
        qs:{
            tenantId
        }
    };
    const response = await createRequest(options);
    return response.data;
}
