var async = require('async');
var commonFunctions = require('./../../utils/commonFunctions');
const kafkaLib = require('../../utils/kafka/kafka-lib');

var content_Sourse_Config = function (tableName, contentSourceId, req,callback) {
    var taskArr = [];
    for (var i = 0; i < tableName.length; i++) {
        taskArr.push(getConfigData.bind(null, tableName[i], contentSourceId,req));
    }
    async.series(taskArr, async (err, data) => {
        if (err) {
            commonFunctions.crawlerlogger.error("Err found: =>", err);
            callback(err, []);
        }
        else {  
            var finalobj = {};
            for (var k = 0; k < data.length; k++) {
                finalobj[Object.keys(data[k])[0]] = Object.values(data[k])[0]
            }
            let options = {};
            try{
                await kafkaLib.publishMessage({
                    topic:config.get("kafkaTopic.contentSource"),
                    messages:[{
                        value:JSON.stringify(finalobj),
                        key: req.headers['tenant-id'] 
                    }]
                })
            }catch(e){
                console.log(e);
            }
            callback(null, finalobj);
        }
    });
}

var getConfigData = function (tableName, contentSourceId,req, cb) {
    if (tableName == "content_sources") {
        var sql = "select * from " + tableName + ' where id=' + contentSourceId;
    } else if (tableName == "content_source_object_fields") {
        var sql = "SELECT csof.* FROM content_source_object_fields AS csof INNER JOIN content_source_objects AS cso ON csof.content_source_object_id = cso.id AND cso.content_source_id =" + contentSourceId
    }
    else {
        var sql = "select * from " + tableName + ' where content_source_id=' + contentSourceId;
    }
    connection[req.headers['tenant-id']].execute.query(sql, function (err, result) {
        if (err) {
            cb(err, [])
        } else {
            var data = {};
            data[tableName] = result;
            cb(null, data);
        }
    });
}

module.exports = {
    content_Sourse_Config: content_Sourse_Config,
}
