
const express = require('express');
const request = require('request');
var router = express.Router();
const verify_credentials = function(data,cb){
  var jsonBody = {
    "clientId": data.authorization.client_id,
    "clientSecret":data.authorization.client_secret,
    "grantType": "client_credentials",
    "scope": "all"
    }

var options = {
    method: 'POST',
    url: data.contentSource.url + '/services/api/oauth2/token',
    headers: { 'content-type': 'application/json' },
    body: jsonBody,
    json: true
};
request(options, (error, response, body) => {
    if (!error && response && response.statusCode==200 && body) {
        cb(null, data);
    }else{
        cb("Invalid credentials or url", null);
    }
}); 
}

module.exports = {
  router:router,
  verify_credentials:verify_credentials
}
