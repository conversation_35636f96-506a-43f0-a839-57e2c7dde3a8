
<head>

  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.2/jquery.min.js"></script>

  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
  <style>
    .textError {
      color: green;
    }

    .central {
      text-align: center;
    }
  </style>
</head>

<body>
<div class="container central">
  <h3 class="textError">Connection Succeeded</h3>
  <% if (typeof error !== 'undefined' && error) { %>
    <p>
      <h5 >Action Required</h5>
       Access to <span style="font-weight: 600;">'View your YouTube Account'</span> was not granted. Please reconnect and enable this permission. </p>
    </p>
  <% } %>
  <button href="#" style="
    background: #2179B8;
    color: #fff;" result="allow" onclick="return CloseMySelf(this);">Next</button>

</div>
</body>
<script>
  debugger;

  // setInterval(function() {
  //   CloseMySelf();
  // });
  if ('<%=id%>'.indexOf('http') > -1) {
    const parts = '<%=id%>'.split('token=');
    const targetOrigin = parts[0];
    const token = parts[1];

    window.opener.postMessage({
      type: 'AUTH_COMPLETE',
      payload: { token }
    }, targetOrigin);
    window.close();
  }

function CloseMySelf(sender) {
    try {
      this.window.opener.document.getElementById('<%=id%>').click();
    }
    catch (err) {}
    window.close();
    return false;
}

</script>
</html>

