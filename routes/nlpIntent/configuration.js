var express = require('express');
var router = express.Router();
var commonFunctions = require('../../utils/commonFunctions');
const config = require('config');
const request = require('request');
const moment = require('moment');
var multipart = require('connect-multiparty');
var multipartMiddleware = multipart();
var fs = require('fs');
const kafkaLib = require('../../utils/kafka/kafka-lib');
var json = require('csvjson');
const appVariables = require('../../constants/appVariables');

const mlCoreURL = appVariables.ml.coreURL


/* to get all intents from nlp_intents */
router.get('/getAllIntents', function (req, res, next) {
    getAllIntents(req,function (err, data) {
      if (err) {
        next(err);
      }

      else res.send(data);
    })
});

/* to add and update single intent in nlp_intents */
router.post('/addIntent', function (req, res, next) {
  checkIfIntentNameExists(req, function (err, rows) {
    if (err) {
      res.send({status: 400, message: 'Error while adding intent'});
    }
    if (!rows.length)
      addIntent(req, function (err, data) {
        if (err) res.send(err);
        else res.send(data);
      })
    else
      res.send({status: 400, message: 'Intent with the same name exists'});
  })
});

/* to delete single intent from nlp_intents */
router.post('/deleteIntent', function (req, res, next) {
  deleteIntent(req, function (err, data) {
      if (err) next(err);
      else res.send(data);
  })
});

/* to move multiple utterances from one intent to another in nlp_intents */
router.post('/moveUtterances', function (req, res, next) {
  moveUtterances(req, function (err, data) {
      if (err) next(err);
      else res.send(data);
    })
});

/* to import multiple intents to nlp_intents from a file */
router.post('/importIntents', multipartMiddleware,function (req, res, next) {
  readFile(req, req.body.intent, function (err, data) {
    if (err) next(err);
    req.body.intent = data;
    importIntents(req, function (err, data) {
      if (err) res.send(err);
      else res.send(data);
    })
  })
});

/* to import multiple utterances to nlp_intents from file */
router.post('/importUtterances', multipartMiddleware, function (req, res, next) {
  readFile(req, req.body.utterances, function (err, data) {
    if (err) next(err);
    else {
      req.body.utterances = data;
      importUtterances(req, function (err, data) {
        if (err) res.send(err);
        else res.send(data);
      })
    }
  })
});

/* to search for matching utterances in utterances stored in nlp_suggestions */
router.post('/searchUtterances', function (req, res, next) {
  searchUtterances(req, function (err, data) {
    if (err) next(err);
    else res.send(data);
  })
});

/* to save intents in nlp_suggestions from suggestion api */
router.post('/saveIntentSuggestions', function (req, res, next) {
  if (!req.body.startDate || !req.body.endDate || !req.body.uid || !req.body.uid.length)
    res.send({status: 400, message: 'Required parameters missing'});
  else 
    saveIntentSuggestions(req, function (err, data) {
      if (err) res.send({status: 400, message: err});
      else res.send({status:200, message: 'New Intent Suggetsions added'});
    })
});

/* to update single intent stored in nlp_suggestions e.g. rename intent, remove utterances */
router.post('/updateIntentSuggestions', function (req, res, next) {
  updateIntentSuggestions(req, function (err, data) {
    if (err) next(err);
    else res.send(data);
  })
});

/* to delete single intent suggestion from nlp_suggestions */
router.post('/deleteIntentSuggestion', function (req, res, next) {
  deleteIntentSuggestion(req, function (err, data) {
    if (err) next(err);
    else res.send(data);
  })
});

router.post('/getSimilarIntentSuggestions',(req,res)=>{
  getSimilarIntentSuggestions(req,(err,data)=>{
    if (err) next(err);
    else res.send(data);
  })
})

router.get('/getAllIntentSuggestions',(req,res)=>{
  getAllIntentSuggestions(req,(err,data)=>{
    if (err) res.send({status: 400, message: err});
    else res.send(data);
  })
})

router.get('/getIntent', function (req, res, next) {
  getIntent(req, function (err, data) {
    if (err) next(err);
    else res.send(data);
  })
});

const getAllIntents = function (req,cb) {
  let sql = "SELECT * FROM `nlp_intents`";
  connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
      if (err) {
        commonFunctions.errorlogger.error("Error while fetching all intents: ",err);
        cb(err, null);
      } else {
        rows = rows.map(f => {
          f.intent = JSON.parse(f.intent)
          return f;
        }).reverse();
        cb(null,rows);
      }
  });
}

const checkIfIntentNameExists = function (req, cb) {
  console.log(req.body.intent)
  if (!req.body.id) {
    let char  = JSON.parse(req.body.intent).name;
    sql = `SELECT intent FROM nlp_intents ni WHERE JSON_EXTRACT(intent , '$.name') in ('${char}')`;
    connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
      if (err) {
          commonFunctions.errorlogger.error("Error while fetching intents ",err);
          cb(err, null);
      } else
          cb (null, rows);
    });
  }
  else cb(null, []);
}

router.get('/train',(req,res)=>{
  train(req,(result)=>{
    res.status(200).send(result)
  })
})


const train = (req,cb) => {
  connection[req.headers['tenant-id']].execute.query(`UPDATE nlp_intents SET trainStatus = 1`,(err,res)=>{
    commonFunctions.httpRequest('GET', `${mlCoreURL}/intent/train?tenantId=${req.headers['tenant-id']}&tpk=${req.headers.session.tpk}`,'', '', '', function (err, result) {
      connection[req.headers['tenant-id']].execute.query(`UPDATE nlp_intents SET last_train='${moment().format('YYYY-MM-DD HH:mm:ss')}' , trainStatus=0 `,(err,res)=>{
        cb(result);
      })
    })
  })
}

const addIntent = function (req, cb) {

  // req.body.intent = `{"name": "i5", 
  // "language": "en", 
  // "tenant_id": "11850a62-19ac-477d-9cd7-837f3d716885", 
  // "created_at": "2021-05-10T11:22:24.238Z", 
  // "updated_at": "2021-05-10T11:22:24.238Z", 
  // "utterances": [{"name": "box"}, {"name": "closed"}, {"name": "solid"}
  // ]}`

  if (typeof JSON.parse(req.body.intent) == 'object') {
    commonFunctions.errorlogger.info("the body for addintent api: ",req.body);
    req.body.intent = JSON.parse(req.body.intent);
    req.body.intent.name = req.body.intent.name.trim().toLowerCase()
    req.body.intent.utterances = req.body.intent.utterances.filter(l => l.name.trim().length) || [];
    req.body.intent.utterances.forEach(l => {l.name = l.name.trim().toLowerCase(); return});
    req.body.intent.created_at = !req.body.intent.created_at ? moment(Date.now()).format('YYYY-MM-DD HH:mm:ss') : req.body.intent.created_at;
    req.body.intent.updated_at = moment(Date.now()).format('YYYY-MM-DD HH:mm:ss');
    req.body.intent.language = req.body.intent.language || 'en';
    req.body.intent.tenant_id = req.body.intent.tenant_id || req.headers['tenant-id'];  
    req.body.intent = JSON.stringify(req.body.intent);
  }
  // console.log(moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
  // console.log(commonFunctions.dateConversion(Date()));
  // let now = new Date();
  // console.log(`${now.getUTCFullYear()}:${now.getUTCMonth() < 10? '0'+now.getUTCMonth() : now.getUTCMonth()}:${now.getUTCDate() < 10 ? '0'+now.getUTCDate() : now.getUTCDate()} ${now.getUTCHours() < 10 ? '0'+now.getUTCHours() : now.getUTCHours()}:${now.getUTCMinutes() < 10 ? '0'+now.getUTCMinutes() : now.getUTCMinutes()}:${now.getUTCSeconds() < 10 ? '0'+now.getUTCSeconds() : now.getUTCSeconds()}`);

  console.log( /^[a-zA-Z_]*$/.test(JSON.parse(req.body.intent).name));
  var validName = /^[a-zA-Z_]*$/.test(JSON.parse(req.body.intent).name);
  var values = JSON.parse(req.body.intent).utterances.map(function(item){ return item.name.toLowerCase() })
  var isDuplicate = JSON.parse(req.body.intent).utterances.some(function(item, idx){ 
    return values.indexOf(item.name.toLowerCase()) != idx 
  });
  if (isDuplicate) 
    cb({status: 400, message: 'Duplicate utterances exist'});
  else if (!validName)
    cb({status: 400, message: 'Invalid Intent Name. Name can only include alphabets and underscore'});
  else {
    let sql = `INSERT INTO nlp_intents(id, intent) values (?, ?) 
    ON DUPLICATE KEY UPDATE intent = values(intent)`;

    connection[req.headers['tenant-id']].execute.query(sql,[req.body.id, req.body.intent], function (err, rows) {
        if (err) {
          commonFunctions.errorlogger.error("Error while adding intents: ", err);
          cb(err, null);
        } else {
            try {
              let addObj = {
                ...req.body,
                type: 'add'
              }
              kafkaLib.publishMessage({
                topic: config.get('kafkaTopic.publishIntentData'),
                messages: [{
                  value: JSON.stringify(addObj),
                  key: req.headers['tenant-id']
                }]
              })
              console.log('KAFKA INTENT PUBLISH', addObj);

              commonFunctions.errorlogger.info(rows);
              cb(null,{ status: 200, "data": rows });
            } catch (error) {
              commonFunctions.errorlogger.error("Error while publishing", error);
            }
        }
    });
  }
}

const deleteIntent = function (req, cb) {

  let sql = `DELETE FROM nlp_intents WHERE id = ?`;
  console.log(req.body.id,"id of intent to delete @@@@@##############################%!!!!!!!!!!",req.body)
  connection[req.headers['tenant-id']].execute.query(sql,[req.body.intent.id], function (err, rows) {
      if (err) {
        commonFunctions.errorlogger.error("Error while deleting intent: ",err);
        cb(err, null);
      } else {
        try {
          let deleteObj = {
            ...req.body,
            type: 'delete'
          }
          kafkaLib.publishMessage({
            topic: config.get('kafkaTopic.publishIntentData'),
            messages: [{
              value: JSON.stringify(deleteObj),
              key: req.headers['tenant-id']
            }]
          })
          console.log('KAFKA INTENT-DELETE PUBLISH',deleteObj);

          commonFunctions.errorlogger.info(rows);
          cb(null,{ status: 200, "data": rows });
        } catch (error) {
          commonFunctions.errorlogger.error("Error while publishing", error);
        }
      }
  });
}

const moveUtterances = function (req, cb) {
    // req.body = {
    //     'id':3,
    //     'utterance' : [{
    //         '_id' : '60900cd904ba94001e031bac',
    //         'name' : 'boxes'
    //     },{
    //         '_id' : '60900cd904ba94001e031bad',
    //         'name': 'closed'
    //     }],
    //     // new_id : '60900dba7ca1d8002046d1ac'
    //     'new_id':2
    // }
    let sql = '';
    let data = [];
    req.body.utterance.forEach( ut => {
      sql = sql + ` UPDATE nlp_intents 
      SET intent = IFNULL( 
        JSON_REMOVE( intent,
          JSON_UNQUOTE(
            REPLACE( 
              JSON_SEARCH(intent, 'all', ? , NULL, '$**.utterances'),
              '.name',
              ''
            ) 
          ) 
        )
      , intent ) 
      where id = ?;
      UPDATE nlp_intents
      SET intent = IF(
        JSON_CONTAINS(intent, JSON_OBJECT('name', ?), '$.utterances'),
        intent,
        JSON_ARRAY_INSERT(intent, '$.utterances[0]', JSON_OBJECT('name', ?))
      )
      where id= ?;
      SELECT * from nlp_intents where id = ?;
      SELECT * from nlp_intents where id = ?;
      `
      data.push(ut.name, req.body.id, ut.name, ut.name, req.body.new_id, req.body.id, req.body.new_id);
    })
    connection[req.headers['tenant-id']].execute.query(sql, data, function (err, rows) {
        if (err) {
          commonFunctions.errorlogger.error("Error while moving utterances: ",err);
          cb(err, null);
        } else {

          const oldIntent = JSON.parse(rows[2][0].intent);
          const newIntent = JSON.parse(rows[3][0].intent);
          let moveUtterancesObj = {
            utterance: req.body.utterance,
            oldIntent : oldIntent.name,
            newIntent :newIntent.name,
            tenantId: req.headers['tenant-id'],
            type: 'MOVE'
          }
          kafkaLib.publishMessage({
            topic: config.get('kafkaTopic.publishIntentData'),
            messages: [{
              value: JSON.stringify(moveUtterancesObj),
              key: req.headers['tenant-id']
            }]
          })
          console.log('KAFKA INTENT-MOVE-UTTERANCES PUBLISH',moveUtterancesObj);
          
          commonFunctions.errorlogger.info(rows);
          rows[2][0].intent = JSON.parse(rows[2][0].intent);
          cb(null, rows[2][0]);
        }
    });
}

const importIntents = function (req, cb) {
  // req.body = {
  //   intent : [{name:'i4', description: 'intent 4'}, { name: 'i5', description: 'intent 5'}]
  // }
  console.log("received: ", req.body.intent)
  let intentsToAdd = req.body.intent.filter(f => /^[a-zA-Z_]*$/.test(f.name));
  console.log("left: ", req.body.intent)
  let sql = '';
  let data = [];
  try {
    let char = intentsToAdd.map(f => '\'"'+f.name.trim().toLowerCase()+'"\'').join(",");

    if (char.length) {
      sql = `SELECT intent FROM nlp_intents ni WHERE JSON_EXTRACT(intent , '$.name') in (${char})`;
      connection[req.headers['tenant-id']].execute.query(sql, data, function (err, rows) {
        if (err) {
            commonFunctions.errorlogger.error("Error while fetching intents ",err);
            cb(err, null);
        } else {
          
          let excludeIntents = rows.map(f => JSON.parse(f.intent).name);
          console.log("exclude: ",excludeIntents);
          intentsToAdd = intentsToAdd.filter(f => excludeIntents.indexOf(f.name.trim().toLowerCase()) == -1);
          console.log(excludeIntents, intentsToAdd);

          if (intentsToAdd && intentsToAdd.length) {

            let sql2 = `INSERT INTO nlp_intents(intent) values `;
            sql2 += intentsToAdd.map(f => {
              f.name = f.name.trim().toLowerCase();
              f.description = f.description || '';
              f.created_at = !f.created_at ? moment(Date.now()).format('YYYY-MM-DD HH:mm:ss') : f.created_at;
              f.updated_at = moment(Date.now()).format('YYYY-MM-DD HH:mm:ss');
              f.language = f.language || 'en';
              f.tenant_id = f.tenant_id || req.headers['tenant-id'];
              f.utterances = f.utterances || [];
              return `('${JSON.stringify(f)}')`
            }).join(",");
            
            connection[req.headers['tenant-id']].execute.query(sql2, null, function (err, rows) {
              if (err) {
                commonFunctions.errorlogger.error("Error while fetching intents ",err);
                cb(err, null);
              } else {
                commonFunctions.errorlogger.info(rows);
                cb(null,{status:200, data: {rows, 
                  duplicate_found: excludeIntents.join(','),
                  intent_success_upload: intentsToAdd.map(f => f.name).join(',')
                } });
              }
            });
          } else {
            commonFunctions.errorlogger.info("Already exists");
            cb(null, {status:400, message: "Already exists", data : {duplicate_found: excludeIntents.join(',')}});
          }
        }
      });
    }
    else 
      cb({status:400, message:'Invalid Intents'}, null ) 
  } catch(exception){
      commonFunctions.errorlogger.error(exception);
      cb(exception, null)
  }
  
//   let reqBody = {
//     uploadFile: fs.createReadStream(req.files.bulk_intent_csv.path),
//     filename : req.files.bulk_intent_csv.name
// }
  
}

const importUtterances = function (req, cb) {
  // req.body = {
  //   intent_id : '3',
  //   utterances : ['slot', 'same']
  // }

  
      // intentsToAdd = json.toObject(intentsToAdd, options);
  let intent_id = req.body.intent_id;  
  let sql = '';
  let data = [];
  let rejected = [];
  let added = [];
  commonFunctions.errorlogger.info("importing utterances: ",req.body.utterances);

  try {
    let utterancesToAdd = req.body.utterances.filter(f => f.hasOwnProperty('name') ? f.name.trim().length : f.trim().length);
    utterancesToAdd.forEach( (utterancesName, i) => {
      let name;
      if(typeof(utterancesName) === 'object'){
        name = utterancesName.name.trim().toLowerCase();
      }else{
        name = utterancesName.trim().toLowerCase();
      }
      sql = '';
      data = [];
      sql = `
      SELECT ? as term,
        JSON_CONTAINS(intent,  LOWER(JSON_OBJECT('name', ?)), '$.utterances') as flag
        from nlp_intents where id= ?;
      UPDATE nlp_intents
      SET intent = IF(
        JSON_CONTAINS(intent, JSON_OBJECT('name', ?), '$.utterances'),
        intent,
        JSON_ARRAY_INSERT(intent, '$.utterances[0]', JSON_OBJECT('name', ?))
      )
      where id= ?;
      `
      data.push(name, name, intent_id, name, name, intent_id);

      commonFunctions.errorlogger.info("SQL to import utterances: ",sql);
      connection[req.headers['tenant-id']].execute.query(sql, data, function (err, rows) {
          if (err) {
            commonFunctions.errorlogger.error("Error in importing utterances: ",err);
            cb(err, null);
          } else {
            commonFunctions.errorlogger.info(rows);
            if (rows[0] && rows[0][0] && rows[0][0].flag == 1)
              rejected.push(name);
            else if (rows[0] && rows[0][0] && rows[0][0].flag == 0)
              added.push(name);
            if (i == utterancesToAdd.length-1)
              cb(null,{ status:200, data:{
                duplicate_found: rejected.join(','),
                intent_success_upload: added.join(',')
              } });
          }
      });
    })
    if (!utterancesToAdd.length)
      cb(null,{ status:400, message: 'Invalid Utterances'});
  } catch(exception){
    commonFunctions.errorlogger.error(exception);
    cb(exception, null)
  }
}

const searchUtterances = function (req, cb) {
  let searchTerm = req.body.term;

  if (!searchTerm || !searchTerm.trim().length)
    cb(null,[]);
  else {
    let sql = `select 
    id,JSON_SEARCH(intent, 'all', '${searchTerm}%' , NULL, '$**.utterances') as location
    from nlp_suggestions ni`;
    connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
      if (err) {
        commonFunctions.errorlogger.error("Error while fetching all intents: ",err);
        cb(err, null);
      } else {
        if (!rows.length)
          cb(null, []);
        else {
          let sql2 = '';
          rows.forEach(r => {
            if (r.location){
              r.location = JSON.parse(r.location);
              if (Array.isArray(r.location)) {
                r.location.forEach(f=> {
                  sql2 += `select JSON_EXTRACT(intent, '${f}' ) as term from nlp_suggestions where id = ${r.id}; `;
                })
              }
              else 
                sql2 += `select JSON_EXTRACT(intent, '${r.location}' ) as term from nlp_suggestions where id = ${r.id}; `;
            }
          })
          commonFunctions.errorlogger.info("Query to search suggestions: ",sql2);
          if (!sql2.length) cb (null, []);
          else {
            connection[req.headers['tenant-id']].execute.query(sql2, function (err, rows) {
              if (err){
                commonFunctions.errorlogger.error("Error while fetching matching terms: ",err);
                cb(err, '');
              } else {
                var combined = [...new Set([].concat.apply([], 
                  rows.map(f=> {
                    if (Array.isArray(f)) return f.map(t=>JSON.parse(t.term));
                    else return JSON.parse(f.term); }) 
                  ))];
                cb(null,combined);
              }
            });
          }
        }
      }
    });
  }
}

const saveIntentSuggestions = function (req, cb) {
  // let url = config.get('intentDetection.url')+`/suggestion_cluster?startDate=2021-05-01&endDate=2021-05-24&searchClientId=aad875fa-3620-11eb-829c-0242ac12000b`;
  let url = mlCoreURL+`/intent/suggestion-cluster?startDate=${req.body.startDate}&endDate=${req.body.endDate}&searchClientId=${req.body.uid.join(',')}`;
  commonFunctions.httpRequest('GET', url, req.headers['tenant-id'], '', '', function (err, result) {
    if (!err && !result.Error && !result.Message) {
      let cluster = result.clusters || [];

      // console.log("cluster === ", cluster);
      let sql = `SELECT AUTO_INCREMENT FROM information_schema.TABLES where TABLE_SCHEMA = "m20" AND TABLE_NAME = "nlp_suggestions"`;
      connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
        if (err)
          cb(err);
        else {
          // commonFunctions.errorlogger.info("intentsuggestion from ML: ", clusters);
          if (!cluster.length) {
            cb(null,{ status: 200, message: "No suggestions available" });
          }
          else {
            let idCount = rows && rows[0] ? rows[0].AUTO_INCREMENT : 1;
            let sql2 = `INSERT INTO nlp_suggestions (intent) values `;
            cluster.forEach(c => {
              let obj = JSON.stringify({
                "name": `Undefined ${idCount++}`,
                "utterances": c[Object.keys(c)[0]].split(', ').map((u)=>{
                  return {
                    name:u
                  }
                })});
              sql2 += `('${obj}'),`
            })
            console.log(sql2);
            connection[req.headers['tenant-id']].execute.query(sql2.slice(0, -1), function (err, rows) {
                if (err) {
                  commonFunctions.errorlogger.error("Error while saving intent suggestion: ",err);
                  cb(err, null);
                } else
                  //getAllIntentSuggestions(cb);
                  cb(null, '');
            });
          }
        }
      })

    }
    else cb(err || result.Error || result.Message);
  })
}

const getAllIntentSuggestions = function(req,cb){
  let sql = `SELECT * FROM nlp_suggestions`;
  connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
    if (err) {
      commonFunctions.errorlogger.error("Error while getting intent suggestions: ",err);
      cb(err, null);
    } else {
      commonFunctions.errorlogger.info(rows);
      rows = rows.map(f => {
        f.intent = JSON.parse(f.intent)
        return f;
      });
      cb(null,{ status: 200, data: rows });
    }
  });
}

const updateIntentSuggestions = function (req, cb) {
  // req.body = {
  //   id : '3',
  //   intent : '{"name": "New name", "utterances": ["article", "analytic", "slot", "auto", "remove", "suggestion", "faq", "removal", "tune", "base", "tuning", "predictor", "escalation", "archive", "rich", "snippet", "kcs", "method", "knowledge", "category", "conversation", "usage", "halper", "month", "href"]}'
  // }
  let intent = JSON.parse(req.body.intent);
  console.log(intent);
  if (!intent.utterances.length)
    deleteIntentSuggestion(req, cb);
  else {
    let sql = `INSERT INTO nlp_suggestions(id, intent) values (?, ?) 
    ON DUPLICATE KEY UPDATE intent = values(intent)`;
    console.log("SQL for update intent suggestion: ",sql);
    connection[req.headers['tenant-id']].execute.query(sql,[req.body.id, req.body.intent], function (err, rows) {
        if (err) {
          commonFunctions.errorlogger.error("Error while adding intents: ", err);
          cb(err, null);
        } else {
          commonFunctions.errorlogger.info(rows);
          cb(null,{ status: 200, data: rows });
        }
    });
  }
}

const deleteIntentSuggestion = function (req, cb) {
  let sql = `DELETE FROM nlp_suggestions WHERE id = ?`;
  connection[req.headers['tenant-id']].execute.query(sql,[req.body.id], function (err, rows) {
      if (err) {
        commonFunctions.errorlogger.error("Error while deleting intent: ",err);
        cb(err, null);
      } else {
        commonFunctions.errorlogger.info(rows);
        cb(null,{ status: 200, data: rows });
      }
  });
}

// Getting similar intent suggestion from search
const getSimilarIntentSuggestions = (req,cb) => {
  connection[req.headers['tenant-id']].execute.query(`Select access_token FROM user WHERE user_email='<EMAIL>'`,(err,data)=>{
    if(!err){
      const options = {
        method:'POST',
        url:`${config.get('searchService.url')}/search/getSimilarSearches`,
        body:{
          searchString:req.body.similarSuggestionString,
          accessToken:data[0].access_token,
          intentSimilarSearch:true
        },
        headers: {
          'Content-Type': 'application/json'
        },
        rejectUnauthorized: false,
        json: true
      }
    
      request(options,(err,response,body)=>{
        if(err){
          console.log("**Error In fetching data from search Service**",err);
          cb(null,[])
        }else{
          cb(null,body);
        }
      })
    }
  })
}

const readFile = function (req, data, cb) {
  if (data)
    cb(null,data);
  else if (req.files){
    fs.readFile(req.files.bulk_upload_csv.path,(err,data) => {
      if(!err){
        console.log(data);
        data = data.toString('utf-8')
        var options = {
          delimiter: ',', // optional
          quote: '"' // optional
        };
        data = json.toObject(data, options);
        cb(null, data);
      }
      else cb(err, null);
    });
  }
  else
    cb({status:400, message: "No data to import"});
}

const getIntent = function (req, cb) {
  let sql = "SELECT * FROM `nlp_intents` where id = ?";
  connection[req.headers['tenant-id']].execute.query(sql, req.query.id, function (err, rows) {
      if (err) {
        commonFunctions.errorlogger.error("Error while fetching all intents: ",err);
        cb(err, null);
      } else {
        rows = rows.map(f => {
          f.intent = JSON.parse(f.intent)
          return f;
        });
        cb(null,rows);
      }
  });
}

module.exports = {
    router: router
}
