if [ $# -eq 0 ]
  then
    echo "No arguments supplied"
    exit
fi
indices=(`curl localhost:8035/_cat/indices | grep -i -E "((20[0-9][0-9]-[0-9][0-9]_$1 )|$1 )" | awk '{print $3}'`)
size=${#indices[@]}
echo "Total number of Indices: "$size
for index in ${indices[@]}
do
  echo "Executing analytics mapping update script for "$index
  /bin/bash ./analyticsMapping.sh $index
  echo "Analytics mapping updated for "$index
  let size--
  echo "remaining "$size
done