
module.exports = {
    "objects": [
        {
            "name": "file",
            "label": "File",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields": [{
                "name": "id",
                "label": "id",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "name",
                "label": "Title",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "path",
                "label": "Display Path",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "content_hash",
                "label": "Content Hash",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "view_href",
                "label": "View Href",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
                "isMasked": 0
            },
            {
                "name": "post_time",
                "label": "Created Date",
                "type": "datetime",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1,
                "isMasked": 0
            },
            {
                "name": "permissions",
                "label": "Permissions",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
                "isMerged": 1
            },
            {
                "name": "content",
                "label": "Description",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "size",
                "label": "File Size",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "modified_time",
                "label": "Modified Time",
                "type": "datetime",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1,
                "isMasked": 0
            },
            {
                "name": "access_type",
                "label": "Access Type",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            }]
        }
    ],
    "fields": []
};
