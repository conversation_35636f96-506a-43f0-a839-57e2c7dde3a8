const hasDocChanged = (currentDoc, previousDoc, fieldsToCheck) => {
  return fieldsToCheck.some(field => currentDoc[field] !== previousDoc[field]);
};

const getBoostingDocs = (currentDocs, previousDocs, savedKeys, uniqueFields, fieldsToCheck) => {
  let added = [];
  let deleted = [];
  let updated = [];

  // Filter currentDocs to include only documents with a unique identifier in savedKeys
  const filteredCurrentDocs = currentDocs.filter(doc => savedKeys.includes(doc[uniqueFields[1]]));

  // Create a Map of previousDocs with a combined key for the uniqueFields for fast lookup
  const previousDocsMap = new Map(
    previousDocs.map(doc => [`${doc[uniqueFields[0]]}_${doc[uniqueFields[1]]}`, doc])
  );

  // Iterate over filteredCurrentDocs to determine added and updated docs
  filteredCurrentDocs.forEach(doc => {
    const key = `${doc[uniqueFields[0]]}_${doc[uniqueFields[1]]}`;
    const previousDoc = previousDocsMap.get(key);

    if (previousDoc) {
      // Check if the document fields have changed
      if (hasDocChanged(doc, previousDoc, fieldsToCheck)) {
        updated.push(doc);
      }
      // Remove from map to keep track of documents processed
      previousDocsMap.delete(key);
    } else {
      // If the document is only in filteredCurrentDocs, add it to the added array
      added.push(doc);
    }
  });

  // Remaining documents in previousDocsMap are those in previousDocs but not in filteredCurrentDocs, hence deleted
  deleted = Array.from(previousDocsMap.values());

  return { added, updated, deleted };
};

module.exports = {
  getBoostingDocs,
};
