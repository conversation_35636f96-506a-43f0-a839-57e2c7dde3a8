

module.exports = {
  "objects": [{
      "name": "stack_question",
      "label": "Question",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": []

  }],
  "fields": [{
      "name": "id",
      "label": "id",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "tag",
      "label": "Tag",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "title",
      "label": "Title",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "post_time",
      "label": "Created Date",
      "type": "datetime",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 0,
      "isActive": 1,
      "isMerged": 1
  },
  {
      "name": "description",
      "label": "Description",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
    "name": "is_answered",
    "label": "Answered",
    "type": "string",
    "isFilterable": 1,
    "isSortable": 1,
    "isSearchable": 1,
    "isActive": 1
  },
  {
    "name": "comment_count",
    "label": "Comment Count",
    "type": "string",
    "isFilterable": 1,
    "isSortable": 1,
    "isSearchable": 1,
    "isActive": 1
  },
  {
      "name": "view_href",
      "label": "View Href",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  }]
}
