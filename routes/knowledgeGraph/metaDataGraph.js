var path = require('path');
environment = require('./../environment');
var express = require('express');
var router = express.Router();
var request = require('request');
var async = require('async');
var connection_sql = require('./../../utils/connection');
var commonFunctions = require('./../../utils/commonFunctions');
var knowledgeGraphKafkaConfig = require('./knowledgeGraphKafkaConfig');
var fs = require('fs');

router.post('/insertAndUpdateMetaDataGraph', function (req, res, data) {
    commonFunctions.CleanRedisCache();
    insertAndUpdateMetaDataGraph(req.body.metaDetails,req, function (err, data) {
        if (err) {
            commonFunctions.errorlogger.error("Error while inserting obj Relation " ,err);
            res.send({ 'status': 0 })
        }
        else {
            res.send({ 'status': 1 })
        }
    })
});

router.get('/getSavedMetaGraphArray', function (req, res, data) {
    getSavedMetaGraphArray(req,function (err, data) {
        res.send(data);
    });
});

router.get('/deleteMetaDataEntry', function (req, res, next) {
    deleteMetaDataEntry(req.query,req, function (err, data) {
        res.send({ "status": data });
    })
});

router.post('/addMetaField', function (req, res, next) {
    addMetaField(req.body.metaField, req.body.metaId,req, function (err, data) {
        res.send({ "status": data });
    })
})

router.get('/getMetaFieldsArray', function (req, res, next) {
    getMetaFieldsArray(req.query.id,req, function (err, data) {
        res.send(data);
    })
});

router.get('/deleteMetaField', function (req, res, next) {
    deleteMetaField(req.query,req, function (err, data) {
        res.send({ "status": data });
    })
});

const insertAndUpdateMetaDataGraph = function (objRel,req, callback) {
    let colums = [];
    let parameters = [];

    for (var key in objRel) {
        colums.push(key)
        parameters.push(objRel[key])
    }
    let sqlCS = "INSERT INTO `meta_data_relation`(" + colums +
        ") VALUES (" + colums.map(x => {
            return '?'
        }).join(',') + ") " +
        "ON DUPLICATE KEY UPDATE " + colums.map(x => {
            return x + "=values(" + x + ")"
        }).join(',');
    let q = connection[req.headers['tenant-id']].execute.query(sqlCS, parameters, function (errAsync, rows) {
        if (errAsync) {
            callback(errAsync, null)
        }
        else {
            let configObj = { platformId: objRel.search_client_id, id: objRel.id, tableName: "meta_data_relation"};
            sendDataToKafka(configObj,req);
            callback(null, 'insertUpdated');
        }
    })
}

const getSavedMetaGraphArray = function (req,callback) {
    const sql = 'SELECT mdr.id as id, mdr.name as name, mdr.search_client_id as search_client_id, sc.name as search_client_name, mdr.content_source_id content_source_id , cs.label content_source_label, cs.elasticIndexName content_source_elasticIndex, mdr.object_id, cso.label object_label, cso.name as object_name, mdr.field_id, csof1.name as field_name, csof1.label as field_label, mdr.title_id, csof2.name as title_name, csof2.label as title_label, mdr.subtitle_id, csof3.name as subtitle_name, csof3.label as subtitle_label, mdr.link_id, csof4.name as link_name, csof4.label as link_label, mdr.description_id, csof5.name as description_name, csof5.label as description_label, mdr.img_id, csof6.name as img_name, csof2.label as img_label, mdr.layout as layout from meta_data_relation as mdr '
        + 'LEFT JOIN search_clients sc ON sc.id = mdr.search_client_id '
        + 'LEFT JOIN content_sources as cs on mdr.content_source_id = cs.id '
        + 'LEFT JOIN content_source_objects cso ON cso.id = mdr.object_id '
        + 'LEFT JOIN content_source_object_fields csof1 ON csof1.id = mdr.field_id '
        + 'LEFT JOIN content_source_object_fields csof2 ON csof2.id = mdr.title_id '
        + 'LEFT JOIN content_source_object_fields csof3 ON csof3.id = mdr.subtitle_id '
        + 'LEFT JOIN content_source_object_fields csof4 ON csof4.id = mdr.link_id '
        + 'LEFT JOIN content_source_object_fields csof5 ON csof5.id = mdr.description_id '
        + 'LEFT JOIN content_source_object_fields csof6 ON csof6.id = mdr.img_id';
        connection[req.headers['tenant-id']].execute.query(sql, function (err, data) {
        if (err) {
            callback(null, []);
        }
        else {
            callback(null, data);
        }
    })
}

const deleteMetaDataEntry = function (query,req, callback) {
    var sql = "DELETE FROM meta_data_relation WHERE id=" + query.id;
    connection[req.headers['tenant-id']].execute.query(sql, function (err, data) {
        if (err) {
            commonFunctions.errorlogger.error(err);
            callback(null, 0);
        }
        else {
            let configObj = { platformId: query.searchClientId};
            sendDataToKafka(configObj,req);
            callback(null, 1);
        }
    })
}

const addMetaField = function (metaField, metaId,req, callback) {
    var temp = [metaField.label, metaId, metaField.content_source_object_field_id];
    var sql = "INSERT INTO meta_data_relation_fields (label, meta_data_relation_id, content_source_object_field_id) VALUES (?,?,?)";
    connection[req.headers['tenant-id']].execute.query(sql, temp, function (err, data) {
        if (err) {
            commonFunctions.errorlogger.error(err);
            callback(null, 0);
        }
        else {
            let configObj = { id: metaId, tableName: "meta_data_relation"}
            sendDataToKafka(configObj,req);
            callback(null, 1);
        }
    })
}

const getMetaFieldsArray = function (id,req, callback) {
    var sql = "SELECT mdrf.id, mdrf.label, mdrf.content_source_object_field_id, csof.name metafield_name, csof.label metafield_label FROM meta_data_relation_fields mdrf LEFT JOIN content_source_object_fields csof ON mdrf.content_source_object_field_id = csof.id where mdrf.meta_data_relation_id = ?";
    connection[req.headers['tenant-id']].execute.query(sql,[id], function (err, data) {
        if (err) {
            commonFunctions.errorlogger.error(err);
            callback(null, []);
        }
        else {
            callback(null, data);
        }
    })
}

const deleteMetaField = function (query,req, callback) {
    var sql = "DELETE FROM meta_data_relation_fields WHERE id=" + query.id;
    connection[req.headers['tenant-id']].execute.query(sql, function (err, data) {
        if (err) {
            commonFunctions.errorlogger.error(err);
            callback(null, 0);
        }
        else {
            let configObj = { id: query.relationId, tableName: "meta_data_relation"};
            sendDataToKafka(configObj,req);
            callback(null, 1);
        }
    })
}

const getAllSearchClients = function (callback) {
    const sql = 'SELECT * FROM search_clients';
    connection.query(sql, function (err, data) {
        if (err) {
            callback(null, []);
        }
        else {
            callback(null, data);
        }
    });
}

const sendDataToKafka = (configObj,req) => {
    if (config.get("kafkaTopic.enable") && ( configObj.platformId || (configObj.id && configObj.tableName) ) ) {
        knowledgeGraphKafkaConfig.knowledgeGraphSettings(configObj,req, function (err, result) {
            commonFunctions.errorlogger.info("Knowledge Graph settings updated through kafka ", err);
        })
    }
}

module.exports = {
    router: router
}