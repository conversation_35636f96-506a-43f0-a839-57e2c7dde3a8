const commonFunctions = require('../../utils/commonFunctions');
const { Connect } = require('./../../utils/mysql.connect');

const save = async (addon, tenantId) => {
    return new Promise(async (resolve, reject) => { 
        let query = `INSERT INTO marketplace_addons SET ? ON DUPLICATE KEY UPDATE id=values(id),is_installed=values(is_installed),installed_at=values(installed_at),addon_id=values(addon_id),name=values(name),name=values(name),url=values(url),api_name=values(api_name),installed_by=values(installed_by)`;
        connection[tenantId].execute.query(query, [addon], function (err, data) { 
        if (err) { 
            reject(err) 
        }
        resolve(data)
    })
    })
    
}

const activate = async (addon, tenantId) => {
    return new Promise(async (resolve, reject) => { 
        let query = `UPDATE marketplace_addons SET is_installed = ?, installed_at = ? where uid = ?`;
        connection[tenantId].execute.query(query, [1,addon.installed_at,addon.uid], function (err, data) { 
        if (err) { 
            reject(err) 
        }
        resolve(data)
    })
    })
    
}

const getInstalledAddons =  (tenantId) => {
    return new Promise(async (resolve, reject) => { 
        const query = `SELECT * FROM marketplace_addons`;
       connection[tenantId].execute.query(query, function (err, data) {
        if (err) {
            reject(err)
        }
        commonFunctions.errorlogger.info(`Installed marketing addons are: ${JSON.stringify(data)}`);
        resolve(data)
    });
    })
    
}

const getSearchClientAddons = async (uid, tenantId) => {
    return new Promise(async (resolve, reject) => { 
        const query = `SELECT * FROM marketplace_addons WHERE uid = ?`;
        connection[tenantId].execute.query(query, [uid], function (err, data) { 
            if (err) {
                 reject(err)
            }
            commonFunctions.errorlogger.info(`Search Client for  addons are: ${JSON.stringify(data)}`);
            resolve(data)
        });
    })
   
}

const deleteAddon = async (id, tenantId) => {
    return new Promise(async (resolve, reject) => { 
        let query = `DELETE FROM marketplace_addons WHERE uid = ?`;
        connection[tenantId].execute.query(query, [id], function (err, data) {
            if (err) {
            reject(err);
            }
            resolve(data);
     });
    })
    
}

module.exports = {
    save,
    activate,
    getInstalledAddons,
    deleteAddon,
    getSearchClientAddons
}
