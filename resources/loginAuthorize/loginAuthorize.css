body {
  font-family: Open Sans, Helvetica Neue, Helvetica, Arial, sans-serif;
}

@media (min-width: 200px) and (max-width:900px){
  .searchunilogosm {
    top: 54px;
    position: relative;
    text-align: center;
    }
    .login-pagesm{
      padding: 5em 0em 5em 0em;
      width: 100%;
      padding-top: 50px;
    }
    .splashSectionsm{
      margin-top: 100px;
      width: 80%;
      position: relative;
      margin-left: 10%;
    }
    .login-page .form-contentsm{
        padding: .375rem .5rem;
        font-size: 1rem;
        line-height: 1.5;
        background-color: #fff;
        border-radius: 4px;
        margin-top: 57px;
        text-align: left;
        width: 260px;
        height: auto;
    }
    .footersm {
      font-size: 12px;
      color: rgb(255, 255, 255);
      line-height: 1.75;
      margin-left: 38px;
      margin-bottom: 40px;
    }
}

  @media (min-width: 900px) and (max-width:2100px){
    .searchunilogo{
      top: 54px;
      position: relative;
    }
    .login-page {
      padding: 5em 8em 8em 0em;
      width: 100%;
    }
    .login-page .form-content{
      padding: .375rem .5rem;
      font-size: 1rem;
      line-height: 1.5;
      background-color: #fff;
      border-radius: 4px;
      width: 285px;
      margin-top: 58px;
    }
    .splashSection{
    margin-top: 100px;
    float: left;
    width: 40%;
    }
    .footer{
      margin-left: 80px;
    }

}

  .splashScreen{
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    position: absolute;
    background: linear-gradient(rgba(29, 29, 29, 0), rgba(16, 16, 23, 0.5)), url(../Assets/splash.png);
    overflow-x: hidden;
    background-color: #2D9EA6;
    background-size: cover;
    background-position: center;
  }
.login-page .form-group {
    padding: 1px 0;
   }
   .login-page .input-lg {
     border-radius: 0
   }
   .login-page .input-underline {
     background: 0 0;
     border: none;
     box-shadow: none;
     border-bottom: 1.5px solid hsla(0, 0%, 100%, .5);
     border-radius: 4px;
     transition: .25s ease
   }
   .login-page .input-underline:focus {
     box-shadow: none
   }

   .login-page .rounded-btn {
    border-radius: 5px;
    color: hsla(0, 0%, 100%, .8);
    border: 1px solid hsla(0, 0%, 100%, .8);
    font-size: 18px;
    line-height: 40px;
    padding: 0 21px;
    margin: 3px;
    background-color: #2179B8;
    box-shadow: 0 5px 30px -2px rgba(0,0,0,.2);
   }
   .login-page .rounded-btn:active,
   .login-page .rounded-btn:focus,
   .login-page .rounded-btn:hover,
   .login-page .rounded-btn:visited {
     color: #fff;
     border: 1px solid #fff;
     outline: 0
   }
   .login-page .form-class{
     padding: 5px 0px 0 9px;
   }

   .login-page label{
     font-size: 14px;
     color: rgb(69, 69, 69);
   }
   .login-page h3{
    font-size: 20px;
    color: #454545;
    font-weight: bold;
    line-height: 1.2;
    text-align: left;
    padding-bottom: 15px;
   }
   .login-page .headerSpan {
     background: none repeat scroll 0 0 #CCCCCC;
     bottom: -11px;
     display: block;
     height: 2px;
     position: relative;
     width: 100%;
   }
   .footer{
    font-size: 12px;
    /* font-family: "Open Sans"; */
    color: rgb(255, 255, 255);
    line-height: 1.75;
     margin-bottom: 40px;
 }
 .searchUnifyNewLogo{
  width: 225px;
}
.login-anchor {
  color: rgb(255, 255, 255) !important;
  text-decoration: none !important;
}
.footer-above{
  margin-bottom: 12px;
}
.footer hr {
    margin-left: 0px;
    background: #E8E8EA;
    width: 94%;
}
.center-align{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}
.password{
  margin-top: 10px;
}
.form-control{
  height: 40px;
  width: 100%;
  padding: .375rem .75rem;
  font-size: 1rem;
  border: 1px solid #ccc;
  border-radius: .25rem;
}
td{
  display:block;
}