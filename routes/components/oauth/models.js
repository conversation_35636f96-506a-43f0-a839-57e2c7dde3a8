/**
 * Created by <PERSON><PERSON><PERSON> on 14-05-2016.
 */

var _ = require('lodash');
var sqldb = require('./sqldb');
var md5 = require('md5');
var commonFunctions = require('../../../utils/commonFunctions');
var User = sqldb.User;
var OAuthClient = sqldb.OAuthClient;
var OAuthAccessToken = sqldb.OAuthAccessToken;
var OAuthAuthorizationCode = sqldb.OAuthAuthorizationCode;
var OAuthRefreshToken = sqldb.OAuthRefreshToken;

function getAccessToken(bearerToken) {
  return OAuthAccessToken
    .findOne({
      where: {access_token: bearerToken},
      attributes: [['access_token', 'accessToken'], ['expires', 'accessTokenExpiresAt'],'scope'],
      include: [
        {
          model: User,
          attributes: ['id', ['user_email','username'],'timezone'],
        }, OAuthClient
      ],
    })
    .then(function (accessToken) {
      if (!accessToken) return false;
      var token = accessToken.toJSON();
      token.user = token.User;
      token.client = token.OAuthClient;
      token.scope = token.scope
      return token;
    })
    .catch(function (err) {
      commonFunctions.errorlogger.error("getAccessToken - Err: ")
    });
}

function getClient(clientId, clientSecret) {
  const options = {
    where: {client_id: clientId},
    attributes: ['id', 'client_id', 'redirect_uri', 'scope'],
  };
  if (clientSecret) options.where.client_secret = clientSecret;

  return sqldb.OAuthClient
    .findOne(options)
    .then(function (client) {
      if (!client) return new Error("client not found");
      var clientWithGrants = client.toJSON()
      clientWithGrants.grants = ['authorization_code', 'password', 'refresh_token', 'client_credentials']
      // Todo: need to create another table for redirect URIs
      clientWithGrants.redirectUris = [clientWithGrants.redirect_uri]
      delete clientWithGrants.redirect_uri
      //clientWithGrants.refreshTokenLifetime = integer optional
      //clientWithGrants.accessTokenLifetime  = integer optional
      return clientWithGrants
    }).catch(function (err) {
      commonFunctions.errorlogger.error("getClient - Err: ", err)
    });
}


function getUser(username, password) {
  return User
    .findOne({
      where: {user_email: username},
      attributes: ['id', 'user_email', 'access_token', 'scope'],
    })
    .then(function (user) {
      return user.access_token == md5(username + password) ? user.toJSON() : false;
    })
    .catch(function (err) {
      commonFunctions.errorlogger.error("getUser - Err: ", err)
    });
}

function revokeAuthorizationCode(code) {
  return OAuthAuthorizationCode.findOne({
    where: {
      authorization_code: code.code
    }
  }).then(function (rCode) {
    //if(rCode) rCode.destroy();
    /***
     * As per the discussion we need set older date
     * revokeToken will expected return a boolean in future version
     * https://github.com/oauthjs/node-oauth2-server/pull/274
     * https://github.com/oauthjs/node-oauth2-server/issues/290
     */
    var expiredCode = code
    expiredCode.expiresAt = new Date('2015-05-28T06:59:53.000Z')
    return expiredCode
  }).catch(function (err) {
    commonFunctions.errorlogger.error("getUser - Err: ", err)
  });
}

function revokeToken(token) {
  return OAuthRefreshToken.findOne({
    where: {
      refresh_token: token.refreshToken
    }
  }).then(function (rT) {
    if (rT) rT.destroy();
    /***
     * As per the discussion we need set older date
     * revokeToken will expected return a boolean in future version
     * https://github.com/oauthjs/node-oauth2-server/pull/274
     * https://github.com/oauthjs/node-oauth2-server/issues/290
     */
    var expiredToken = token
    expiredToken.refreshTokenExpiresAt = new Date('2015-05-28T06:59:53.000Z')
    return expiredToken
  }).catch(function (err) {
    commonFunctions.errorlogger.error("revokeToken - Err: ", err)
  });
}


function saveToken(token, client, user) {
  return Promise.all([
      OAuthAccessToken.create({
        access_token: token.accessToken,
        expires: token.accessTokenExpiresAt,
        client_id: client.id,
        user_id: user.id,
        scope: token.scope
      }),
      token.refreshToken ? OAuthRefreshToken.create({ // no refresh token for client_credentials
        refresh_token: token.refreshToken,
        expires: token.refreshTokenExpiresAt,
        client_id: client.id,
        user_id: user.id,
        scope: token.scope
      }) : [],

    ])
    .then(function (resultsArray) {
      return _.assign(  // expected to return client and user, but not returning
        {
          client: client,
          user: user,
          access_token: token.accessToken, // proxy
          refresh_token: token.refreshToken, // proxy
        },
        token
      )
    })
    .catch(function (err) {
      commonFunctions.errorlogger.error("revokeToken - Err: ", err)
    });
}

function getAuthorizationCode(code) {
  return OAuthAuthorizationCode
    .findOne({
      attributes: ['client_id', 'expires', 'user_id', 'scope'],
      where: {authorization_code: code},
      include: [User, OAuthClient]
    })
    .then(function (authCodeModel) {
      if (!authCodeModel) return false;
      var client = authCodeModel.OAuthClient.toJSON()
      var user = authCodeModel.User.toJSON()
      return reCode = {
        code: code,
        client: client,
        expiresAt: authCodeModel.expires,
        redirectUri: client.redirect_uri,
        user: user,
        scope: authCodeModel.scope,
      };
    }).catch(function (err) {
      commonFunctions.errorlogger.error("getAuthorizationCode - Err: ", err)
    });
}

function saveAuthorizationCode(code, client, user) {
  return OAuthAuthorizationCode
    .create({
      expires: code.expiresAt,
      client_id: client.id,
      authorization_code: code.authorizationCode,
      user_id: user.id,
      scope: code.scope
    })
    .then(function () {
      code.code = code.authorizationCode
      return code
    }).catch(function (err) {
      commonFunctions.errorlogger.error("saveAuthorizationCode - Err: ", err)
    });
}

function getUserFromClient(client) {
  var options = {
    where: {client_id: client.client_id},
    include: [User],
    attributes: ['id', 'client_id', 'redirect_uri'],
  };
  if (client.client_secret) options.where.client_secret = client.client_secret;

  return OAuthClient
    .findOne(options)
    .then(function (client) {
      if (!client) return false;
      if (!client.User) return false;
      return client.User.toJSON();
    }).catch(function (err) {
      commonFunctions.errorlogger.error("getUserFromClient - Err: ", err)
    });
}

function getRefreshToken(refreshToken) {
  if (!refreshToken || refreshToken === 'undefined') return false

  return OAuthRefreshToken
    .findOne({
      attributes: ['client_id', 'user_id', 'expires'],
      where: {refresh_token: refreshToken},
      include: [OAuthClient, User]

    })
    .then(function (savedRT) {
      // savedRT.OAuthClient.scope ===  APP SCOPE
      // savedRT.User.scope ==== USER SCOPE
      if(savedRT.OAuthClient.scope == 'All' && client.scope !== null){
         savedRT.scope = savedRT.User.scope ;
       }else if(savedRT.User.scope == 'All' && client.scope !== null){
          savedRT.scope = savedRT.OAuthClient.scope ; 
       }else{
         if(savedRT.OAuthClient.scope === savedRT.User.scope ){
            savedRT.scope = savedRT.User.scope ;
         }
        
       }

      var tokenTemp = {
        user: savedRT ? savedRT.User.toJSON() : {},
        client: savedRT ? savedRT.OAuthClient.toJSON() : {},
        refreshTokenExpiresAt: savedRT ? new Date(savedRT.expires) : null,
        refreshToken: refreshToken,
        refresh_token: refreshToken,
        scope: savedRT.scope
      };
      return tokenTemp;

    }).catch(function (err) {
      commonFunctions.errorlogger.error("getRefreshToken - Err: ", err)
    });
}

function validateScope(token, client , scope) {
    if(client.scope == 'All' && client.scope !== null){
     return client.scope = token.scope ;
    }else if(token.scope == 'All' && token.scope !== null){
      return token.scope = client.scope ;
    }else{
      return (token.scope === client.scope && client.scope !== null) ? client.scope : false;
    }
  
}

function verifyScope(token, scope) {
    return token.scope === scope
}

module.exports = {
  //generateOAuthAccessToken, optional - used for jwt
  //generateAuthorizationCode, optional
  //generateOAuthRefreshToken, - optional
  getAccessToken: getAccessToken,
  getAuthorizationCode: getAuthorizationCode, //getOAuthAuthorizationCode renamed to,
  getClient: getClient,
  getRefreshToken: getRefreshToken,
  getUser: getUser,
  getUserFromClient: getUserFromClient,
  //grantTypeAllowed, Removed in oauth2-server 3.0
  revokeAuthorizationCode: revokeAuthorizationCode,
  revokeToken: revokeToken,
  saveToken: saveToken,//saveOAuthAccessToken, renamed to
  saveAuthorizationCode: saveAuthorizationCode, //renamed saveOAuthAuthorizationCode,
  validateScope: validateScope,
  verifyScope: verifyScope,
}

