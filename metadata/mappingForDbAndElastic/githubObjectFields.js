module.exports = {
    "objects": [
        {
            "name": "issues",
            "label": "Issue",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields":  [
                {
                    "name": "id",
                    "label": "Id",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "title",
                    "label": "Title",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "repository",
                    "label": "Repository Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "repository_description",
                    "label": "Description",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "labels",
                    "label": "Label",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "post_time",
                    "label": "Created Date",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isMerged":1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1
                },
                {
                    "name": "comments",
                    "label": "Comments",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "issue_number",
                    "label": "Issue Number",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "assignee",
                    "label": "Assignee",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },        {
                    "name": "assignees",
                    "label": "Assignees",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "view_href",
                    "label": "View Href",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "issue_description",
                    "label": "Issue Description",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "issue_state",
                    "label": "Issue State",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "isPrivate",
                    "label": "Private",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                }
            ]
        },
        {
            "name": "repositories",
            "label": "Repositories",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields":  [
                {
                    "name": "id",
                    "label": "Id",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "name",
                    "label": "Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "full_name",
                    "label": "Full Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "view_href",
                    "label": "View Href",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "url",
                    "label": "Url",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "description",
                    "label": "Description",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "owner_username",
                    "label": "Owner Username",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "owner_id",
                    "label": "Owner Id",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "owner_type",
                    "label": "Owner Type",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "owner_html_url",
                    "label": "Owner Html Url",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },        
                {
                    "name": "post_time",
                    "label": "Created At",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1,
                    "isMerged":1,
                },
                {
                    "name": "updated_at",
                    "label": "Updated At",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1
                },
                {
                    "name": "pushed_at",
                    "label": "Pushed At",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1
                },
                {
                    "name": "watchers_count",
                    "label": "Watchers Count",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "primary_language",
                    "label": "Primary Language",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "forks_count",
                    "label": "Forks Count",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "open_issues_count",
                    "label": "Open Issues Count",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "license_type",
                    "label": "License Type",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "main_branch",
                    "label": "Main Branch",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "readme_name",
                    "label": "Readme Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "readme_content",
                    "label": "Readme Content",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "readme_url",
                    "label": "Readme Url",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "readme_html_url",
                    "label": "Readme HTML URL",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "readme_path",
                    "label": "Readme Path",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
            ]
        },
        {
            "name": "pull_requests",
            "label": "Pull Requests",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields":  [
                {
                    "name": "id",
                    "label": "Id",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "number",
                    "label": "Number",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "description",
                    "label": "Description",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "title",
                    "label": "Title",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "pull_request_url",
                    "label": "pull Request Url",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "view_href",
                    "label": "View Href",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "state",
                    "label": "State",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "post_time",
                    "label": "Created At",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1,
                    "isMerged":1,
                },
                {
                    "name": "updated_at",
                    "label": "Updated At",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1
                },
                {
                    "name": "merged_at",
                    "label": "Merged At",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1
                },
                {
                    "name": "author_id",
                    "label": "Author Id",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "author_login",
                    "label": "Author Login",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },        
                {
                    "name": "assignees",
                    "label": "Assignees",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "requested_reviewers",
                    "label": "Requested Reviewers",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "commits",
                    "label": "Commits",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "additions",
                    "label": "Additions",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "deletions",
                    "label": "Deletions",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "changed_files",
                    "label": "Changed Files",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "mergeable",
                    "label": "Mergeable",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "merged",
                    "label": "Merged",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "merged_by",
                    "label": "Merged By",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "draft",
                    "label": "Draft",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "base_ref",
                    "label": "Base Ref",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "head_ref",
                    "label": "head Ref",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "comments",
                    "label": "Comments",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "review_comments",
                    "label": "Review Comments",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "repository_name",
                    "label": "Repository Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                }             
            ]
        }
    ],
    "fields": []
}
