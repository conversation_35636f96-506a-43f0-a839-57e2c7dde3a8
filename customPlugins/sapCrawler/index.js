/**
 * <AUTHOR>
 * @version 1
 * @description API's to crawl sap documents
 */
var request = require("request");
var async = require('async');
var fs = require("fs");
var sapCrawlingRoutes = {};
var path = require('path');
var router = require('express').Router();
var commonFunctions = require('../../utils/commonFunctions');
var universal=require(commonFunctions.appVariables.plugins.universalPath);
var child_process = require('child_process');
var cronUtility = require('./cronUtility');
var elasticUtility = require('./elasticUtility');
var encryptorUtility = require('./../../routes/sessionCapturing/encryptorUtility');

var helpDocCrawlStatusFilePath = path.join(__dirname,'..','..','..','logs','helpdocs','crawlstatus.json');
var knowledgeCenterCrawlStatusFilePath = path.join(__dirname,'..','..','..','logs','knowledgecenter','crawlstatus.json');
var learningHubCrawlStatusFilePath = path.join(__dirname,'..','..','..','logs','learninghub','crawlstatus.json');

var helpDocConfigFilePath = path.join(__dirname,"crawler", "helpdoc", "config.json");
var knowledgeCenterConfigFilePath = path.join(__dirname,"crawler", "knowledgecenter", "config.json");
var learningHubConfigFilePath = path.join(__dirname,"crawler", "learninghub", "config.json");

var helpDocCrawlLogPath = path.join(__dirname,"..","..","..","logs", "learningHubCrawlerLog.out");
var knowledgeCenterCrawlLogPath = path.join(__dirname,"..","..","..","logs", "learningHubCrawlerLog.out");
var learningHubCrawlLogPath = path.join(__dirname,"..","..","..","logs", "learningHubCrawlerLog.out");

var learningHubCrawlScriptPath = path.join(__dirname,"crawler", "helpdocs", "crawl.sh");
var content_source_data;


function getStatusFile(type){
  var crawlstatusfile = helpDocCrawlStatusFilePath;
  if(type == 'knowledgecenter')
    type = knowledgeCenterCrawlStatusFilePath;
  else if(type == 'learninghub')
    type = learningHubCrawlStatusFilePath;
  return crawlstatusfile;
}
/*
  Method to get status of crawler
  possible values:-
    0 - crawler stopped
    1 - crawler is running
*/
function getCrawlingStatus(type) {
  var status = 0;
  try {
    var crawlstatusfile = getStatusFile(type);
    // checking if status file exists
    if (fs.existsSync(crawlstatusfile)) {
      var crawlingStatus = fs.readFileSync(crawlstatusfile);
      crawlingStatus = JSON.parse(crawlingStatus);
      status = crawlingStatus.status;
    }
  } catch (e) {
    console.log('problem while reading status file');
  }
  return status;
}

/*
  Method to get crawling information of crawler like status, processid
  possible values:-
    JSON object
    null
*/
function getCrawlingInfo(type) {
  var info = null;
  try {
    var crawlstatusfile = getStatusFile(type);
    // checking if status file exists
    if (fs.existsSync(crawlstatusfile)) {
      var crawlingStatus = fs.readFileSync(crawlstatusfile);
      crawlingStatus = JSON.parse(crawlingStatus);
      info = crawlingStatus;
    }else{
      console.log('file does not exists');
    }
  } catch (e) {
    console.log('problem while reading status file');
  }
  return info;
}

/*
  Method to get stop crawler
  possible values:-
    1 - success
    0 - error
*/
function stopHelpDocsCrawler(){
  var crawlingInfo = getCrawlingInfo();
  if(crawlingInfo!=null){
    var processId = crawlingInfo.processId;

  }
}

function startHelpDocsCrawler(contentSourceId,req, cbc){
  //  1. create a dummy index
  //  2. Index crawled data to that index
  //  3. delete live index
  //  4. recreate index
  //  5. reindex data from latest crawled index
  //  6. delete temporary index

  commonFunctions.getContentSourceDataById(contentSourceId, req, function(e, r){
    if (e) //error handled
      cbc(e);
    else {
      content_source_data = r.contentSource;
      var result = {};
      result.contentsources = r;
      result.elasticHost = config.get('elasticIndexCS.host');
      result.elasticPort = config.get('elasticIndexCS.port');

      elasticUtility.setupConnection(result);

      var tempIndex = 'sf_temp1';
      var username = result.contentsources.authorization.username;
      var password = result.contentsources.authorization.password;
      var encryptedUsername = '';
      var encryptedPassword = '';
      var crawlstatus = {};
      async.series([
        // 0. encrypt username and password
        cb=>{
          encryptorUtility.cryptED("encrypt", username, function(err, res){
            if (err) //error handled
              cb(err);
            else {
              encryptedUsername = res.trim();
              cb(null, 1);
            }
          });
        },
        cb=>{
          encryptorUtility.cryptED("encrypt", password, function(err, res){
            if (err) //error handled
              cb(err);
            else {
              encryptedPassword = res.trim();
              cb(null, 1);
            }
          });
        },
        // 1. get create dummy index
        cb =>{
          // check previous crawling status before crawling
          if(fs.existsSync(helpDocCrawlStatusFilePath)){
            console.log('fetching previous crawling status');
            try{
                crawlstatus = JSON.parse(fs.readFileSync(helpDocCrawlStatusFilePath));
                if(typeof crawlstatus.offset == 'undefined'){
                    crawlstatus.offset = 0;
                }
                crawlstatus.pid=crawlProcess.pid;
                crawlstatus.status=1;
                console.log(JSON.stringify(crawlstatus));
            }catch(e){
              crawlstatus.offset = 0;
              crawlstatus.status=1;
            }
          }else{
            crawlstatus.offset = 0;
            crawlstatus.status=1;
            console.log('No previous crawling info found. Starting fresh');
          }
          if(crawlstatus.offset == 0){
            console.log('creating dummy index');
            elasticUtility.dropIndex(tempIndex, function(err, res){
              if (err) //error handled
                cb(err);
              else {
                elasticUtility.createIndex(tempIndex,undefined, function(errin, resin){
                  console.log('dummy index created');
                  cb(null, 1);
                });
              }
            });
          }else{
            console.log('Continuing previous crawling');
            cb(null, 1);
          }
        },

        // 2. Crawl and index data to dummy index
        cb =>{
          console.log('modifying helpdoc crawling configurations');
          // modifying default help doc crawling configurations
          var helpdoc_config = fs.readFileSync(path.join(__dirname,"crawler", "helpdocs", "config.json"));
          helpdoc_config = JSON.parse(helpdoc_config);
          helpdoc_config.elasticHost = result.elasticHost;
          helpdoc_config.elasticPort = result.elasticPort;
          helpdoc_config.elasticIndex = tempIndex;
          helpdoc_config.elasticType = 'doc';
          fs.writeFileSync(path.join(__dirname,"crawler", "helpdocs", "config.json"), JSON.stringify(helpdoc_config));
          // ---------------------------------------------------

          console.log('starting crawler');
          var startTime = new Date();
          var outfile = fs.createWriteStream(path.join(__dirname,"..","..","..","logs", "helpDocsCrawlerLog.out"));
          var crawlProcess = child_process.spawn(path.join(__dirname,"crawler", "helpdocs", "crawl.sh"), [encryptedUsername, encryptedPassword]);

          crawlProcess.stdout.on('data', function (data) {
            fs.appendFileSync(outfile.path, data);
            fs.appendFile(`${path.join(process.cwd(), r.contentSource.logFile)}`, data); //appending to crawling log file to show logs on admin panel
          });

          crawlProcess.stderr.on('data', function (data) {
            fs.appendFileSync(outfile.path, data);
            fs.appendFile(`${path.join(process.cwd(), r.contentSource.logFile)}`, data); //appending to crawling log file to show logs on admin panel
          });

          crawlProcess.on('exit', function (code) {
            console.log('child process exited with code ' + code);
            universal.executeQuery('UPDATE content_sources SET pid = ? WHERE id =?', [0, result.contentsources.contentSource.id], req,function(){
              console.log('updated database entry ');
            });
            crawlstatus.status=0;
            fs.writeFileSync(helpDocCrawlStatusFilePath, JSON.stringify(crawlstatus));
            cb(null,"fetching done")
          });
          crawlstatus.contentSourceId=result.contentsources.contentSource.id;
          crawlstatus.pid=crawlProcess.pid;
          fs.writeFile(helpDocCrawlStatusFilePath,JSON.stringify(crawlstatus),function(error, response){

          });
          // update pid in database
          universal.executeQuery('UPDATE content_sources SET pid = ?, sync_start_date=?  WHERE id =?', [crawlProcess.pid, new Date(), result.contentsources.contentSource.id], req, function(){
            console.log('updated database entry ');
          });

          // start a cron job to check status of process every minute so that when process is finished
          cronUtility.createCron(path.join(__dirname,"crawler","test.sh")+ ' '+ crawlProcess.pid+' '+path.join(__dirname)+' "helpdocs" >> '+path.join(__dirname,'..','..','..','logs','help_docs_out.log'),'* * * * *','test_helpdoc_cron_node',function(status){
            console.log('cron status:' +status);
          });
          console.log(crawlProcess.pid);
        },
        // 3. delete live index
        cb=>{
          var index = result.contentsources.contentSource.elasticIndexName;
          elasticUtility.getDocCount(tempIndex, function(countObj){
            if(countObj.total && countObj.total > 0){
              elasticUtility.getDocCount(index, function(countObjIn){
                if(countObjIn.total < countObj.total || (countObjIn.total - countObj.total) < countObjIn.total/2){
                  elasticUtility.dropIndex(index, function(err, resp){
                    cb(null, 1);
                  });
                }else{
                  cb({error: "Possibility of error. There are less number of documents crawled. Exiting crawling process"}, null);
                }
              });
            }else{
              cb({error: "Possibility of error. There are less number of documents crawled.  Exiting crawling process"}, null);
            }
          });
        },
        // 4. recreate index with mapping
        cb =>{
          var index = result.contentsources.contentSource.elasticIndexName;
          commonFunctions.createMapping(contentSourceId, 0,req, function (MappingErr, MappingRsult) {
            cb(MappingErr, MappingRsult);
          });
        },
        // 5. copy data from dummy to live index
        cb =>{
          commonFunctions.reIndex(result.contentsources.contentSource.elasticIndexName, tempIndex, function(err, resp){
            if(err)
              cb(err, 0); //error handled
            else
              cb(null, 1);
          })
          crawlstatus.status = 0;
          crawlstatus.pid = 0;
          fs.writeFile(helpDocCrawlStatusFilePath,JSON.stringify(crawlstatus),function(error, response){

          });
        }
      ], function(error, response){
        if(error){
          console.log(error);
          return cbc(error); //error handled
        }
        console.log('Finished with crawling and reindexing..');
        cbc(null, 1);
      });
    }
  });
}

function startKnowledgeCenterCrawler(contentSourceId,req, cbc){
  //  1. create a dummy index
  //  2. Index crawled data to that index
  //  3. delete live index
  //  4. recreate index
  //  5. reindex data from latest crawled index
  //  6. delete temporary index

  commonFunctions.getContentSourceDataById(contentSourceId, req, function(e, r){
    if (e) //error handled
      cbc(e);
    else {
      content_source_data = r.contentSource;
      var result = {};
      result.contentsources = r;
      result.elasticHost = config.get('elasticIndexCS.host');
      result.elasticPort = config.get('elasticIndexCS.port');
      //console.log(JSON.stringify(result));
      elasticUtility.setupConnection(result);

      var tempIndex = 'sf_temp2';
      var username = result.contentsources.authorization.username;
      var password = result.contentsources.authorization.password;
      var encryptedUsername = '';
      var encryptedPassword = '';
      async.series([
        // 0. encrypt username and password
        cb=>{
          encryptorUtility.cryptED("encrypt", username, function(err, res){
            if (err) //error handled
              cb(err);
            else {
              encryptedUsername = res.trim();
              cb(null, 1);
            }
          });
        },
        cb=>{
          encryptorUtility.cryptED("encrypt", password, function(err, res){
            if (err) //error handled
              cb(err);
            else {
              encryptedPassword = res.trim();
              cb(null, 1);
            }
          });
        },
        // 1. get create dummy index
        cb =>{
          console.log('creating dummy index');
          elasticUtility.dropIndex(tempIndex, function(err, res){
            if (err) //error handled
              cb(err);
            else {
              elasticUtility.createIndex(tempIndex,undefined, function(errin, resin){
                if (errin)
                  cb(errin);
                else {
                  console.log('dummy index created');
                  cb(null, 1);
                }
              });
            }
          });
        },
        // 2. Crawl and index data to dummy index
        cb =>{
          try{
            console.log('modifying helpdoc crawling configurations');
            // modifying default help doc crawling configurations
            var helpdoc_config = fs.readFileSync(path.join(__dirname,"crawler", "knowledgecenter", "config.json"));
            helpdoc_config = JSON.parse(helpdoc_config);
            helpdoc_config.elasticHost = result.elasticHost;
            helpdoc_config.elasticPort = result.elasticPort;
            helpdoc_config.elasticIndex = tempIndex;
            helpdoc_config.elasticType = 'supportdoc';
            fs.writeFileSync(path.join(__dirname,"crawler", "knowledgecenter", "config.json"), JSON.stringify(helpdoc_config));
            // ---------------------------------------------------

            console.log('starting crawler', encryptedUsername, encryptedPassword);
            var startTime = new Date();
            var outfile = fs.createWriteStream(path.join(__dirname,"..","..","..","logs", "knowledgeCenterLog.out"));
            var crawlProcess = child_process.spawn(path.join(__dirname,"crawler", "knowledgecenter", "crawl.sh"), [encryptedUsername, encryptedPassword]);

            crawlProcess.stdout.on('data', function (data) {
              fs.appendFileSync(outfile.path, data);
              fs.appendFile(`${path.join(process.cwd(), r.contentSource.logFile)}`, data); //appending to crawling log file to show logs on admin panel
            });

            crawlProcess.stderr.on('data', function (data) {
              fs.appendFileSync(outfile.path, data);
              fs.appendFile(`${path.join(process.cwd(), r.contentSource.logFile)}`, data); //appending to crawling log file to show logs on admin panel
            });

            crawlProcess.on('exit', function (code) {
              if(code == 0){
                console.log('child process exited with code ' + code);
                universal.executeQuery('UPDATE content_sources SET pid = ? WHERE id =?', [0, result.contentsources.contentSource.id], req,function(){
                  console.log('updated database entry ');
                });
                cb(null,"fetching done");
              }else{
                cb({error: 'Error in crawling process.'}, null);
              }
            });

            fs.writeFile(knowledgeCenterCrawlStatusFilePath,'{"pid":'+crawlProcess.pid+', "status": 1}',function(error, response){

            });
            // update pid in database
            universal.executeQuery('UPDATE content_sources SET pid = ?, sync_start_date=?  WHERE id =?', [crawlProcess.pid, new Date(), result.contentsources.contentSource.id],req, function(){
              console.log('updated database entry ');
            });

            // start a cron job to check status of process every minute so that when process is finished
            cronUtility.createCron(path.join(__dirname,"crawler", "test.sh")+ ' '+ crawlProcess.pid+' '+path.join(__dirname)+' "knowledgecenter" >> '+path.join(__dirname,'..','..','..','logs','knowledge_center_out.log'),'* * * * *','test_knowledgecenter_cron_node',function(status){
              console.log('cron status:' +status);
            });
            console.log(crawlProcess.pid);
          }catch(e){
            cb({error: "Some error occured in crawling."}, null);
          }
        },
        // 3. delete live index
        cb=>{
          var index = result.contentsources.contentSource.elasticIndexName;
          elasticUtility.getDocCount(tempIndex, function(countObj){
            console.log('temp count: ', countObj);
            if(countObj.total && countObj.total > 0){
              elasticUtility.getDocCount(index, function(countObjIn){
                console.log('previous count: ', countObjIn);
                if(countObjIn.total < countObj.total || (countObjIn.total - countObj.total) < countObjIn.total/2){
                  elasticUtility.dropIndex(index, function(err, resp){
                    cb(null, 1);
                  });
                }else{
                  cb({error: "Possibility of error. There are less number of documents crawled. Exiting crawling process"}, null);
                }
              });
            }else{
              cb({error: "Possibility of error. There are less number of documents crawled.  Exiting crawling process"}, null);
            }
          });
        },
        // 4. recreate index with mapping
        cb =>{
          var index = result.contentsources.contentSource.elasticIndexName;
          commonFunctions.createMapping(contentSourceId, 0,req, function (MappingErr, MappingRsult) {
            cb(MappingErr, MappingRsult);
          });
        },
        // 5. copy data from dummy to live index
        cb =>{
          commonFunctions.reIndex(result.contentsources.contentSource.elasticIndexName, tempIndex, function(err, resp){
            if(err)
              cb(err, 0); //error handled
            else
              cb(null, 1);
          });
        }
      ], function(error, response){
        if(error){
          console.log('Error occurred while crawling: ', error);
          return cbc(error); //error handled
        }
        fs.writeFile(knowledgeCenterCrawlStatusFilePath,'{"pid": 0, "status": 0}',function(error, response){

        });
        cbc(null, 1);// 5. copy data from dummy to live index
      });
    }
  });

}

function startLearningHubCrawler(contentSourceId,req,cbc){
  //  1. create a dummy index
  //  2. Index crawled data to that index
  //  3. delete live index
  //  4. recreate index
  //  5. reindex data from latest crawled index
  //  6. delete temporary index
  async.auto({
    getContentSourceData: function(cb){
      // get content source meta data
      commonFunctions.getContentSourceDataById(contentSourceId, req, function(e, r){
        content_source_data = r.contentSource;
        cb(e,r);
      });
    },
    getContentSourceObjectsAndfFields: function(cb){
      // get content source object anf fields data
      commonFunctions.getContentSourceObjectsAndFieldsById(contentSourceId,req, function(e,r){
        cb(e,r);
      });
    },
    setupCrawler: ['getContentSourceData','getContentSourceObjectsAndfFields', function(csResults, cb){
      var result = {};
      result.contentsources = csResults.getContentSourceData;
      result.elasticHost = config.get('elasticIndexCS.host');
      result.elasticPort = config.get('elasticIndexCS.port');

      var objectFields = csResults.getContentSourceObjectsAndfFields;

      elasticUtility.setupConnection(result);

      var indexName = result.contentsources.contentSource.elasticIndexName;
      var tempIndex = indexName+'_temp';
      var username = result.contentsources.authorization.username;
      var password = result.contentsources.authorization.password;
      var encryptedUsername = '';
      var encryptedPassword = '';
      var crawlstatus = {};

      async.series([
        // 0. encrypt username and password
        cb=>{
          encryptorUtility.cryptED("encrypt", username, function(err, res){
            if (err) //error handled
              cb(err);
            else {
              encryptedUsername = res.trim();
              cb(null, 1);
            }
          });
        },
        cb=>{
          encryptorUtility.cryptED("encrypt", password, function(err, res){
            if (err) //error handled
              cb(err);
            else {
              encryptedPassword = res.trim();
              cb(null, 1);
            }
          });
        },
        // 1. get create dummy index
        cb =>{
          // check previous crawling status before crawling
          if(fs.existsSync(learningHubCrawlStatusFilePath)){
            console.log('fetching previous crawling status');
            try{
                crawlstatus = JSON.parse(fs.readFileSync(learningHubCrawlStatusFilePath));
                if(typeof crawlstatus.offset == 'undefined'){
                    crawlstatus.offset = 0;
                }
                crawlstatus.pid=crawlProcess.pid;
                crawlstatus.status=1;
                console.log(JSON.stringify(crawlstatus));
            }catch(e){
              crawlstatus.offset = 0;
              crawlstatus.status=1;
            }
          }else{
            crawlstatus.offset = 0;
            crawlstatus.status=1;
            console.log('No previous crawling info found. Starting fresh');
          }
          if(crawlstatus.offset == 0){
            console.log('creating dummy index');
            commonFunctions.deleteMapping(tempIndex, (e,r)=>{
              if(!e){
                commonFunctions.createMapping(indexName, 1,req, (e,r) =>{
                  if(!e)
                    cb(null, 1);
                  else
                    cb(e, null);
                });
              }else
                cb(e, null);
            });
          }else{
            console.log('Continuing previous crawling');
            cb(null, 1);
          }
        },

        // 2. Crawl and index data to dummy index
        cb =>{
          console.log('modifying learning hub crawling configurations');
          // modifying default help doc crawling configurations
          var learning_hub_config = fs.readFileSync(learningHubConfigFilePath);
          learning_hub_config = JSON.parse(learning_hub_config);
          learning_hub_config.elasticHost = result.elasticHost;
          learning_hub_config.elasticPort = result.elasticPort;
          learning_hub_config.elasticIndex = tempIndex;
          learning_hub_config.elasticType = 'doc';
          fs.writeFileSync(learningHubConfigFilePath, JSON.stringify(learning_hub_config));
          // ---------------------------------------------------

          console.log('starting crawler');
          var startTime = new Date();
          var outfile = fs.createWriteStream(learningHubCrawlLogPath);
          var crawlProcess = child_process.spawn(learningHubCrawlScriptPath, [encryptedUsername, encryptedPassword]);

          crawlProcess.stdout.on('data', function (data) {
            fs.appendFileSync(outfile.path, data);
            fs.appendFile(`${path.join(process.cwd(), csResults.getContentSourceData.contentSource.logFile)}`, data); //appending to crawling log file to show logs on admin panel
          });

          crawlProcess.stderr.on('data', function (data) {
            fs.appendFileSync(outfile.path, data);
            fs.appendFile(`${path.join(process.cwd(), csResults.getContentSourceData.contentSource.logFile)}`, data); //appending to crawling log file to show logs on admin panel
          });

          crawlProcess.on('exit', function (code) {
            console.log('child process exited with code ' + code);
            universal.executeQuery('UPDATE content_sources SET pid = ? WHERE id =?', [0, result.contentsources.contentSource.id],req, function(){
              console.log('updated database entry ');
            });
            crawlstatus.status=0;
            fs.writeFileSync(learningHubCrawlStatusFilePath, JSON.stringify(crawlstatus));
            cb(null,"fetching done")
          });
          crawlstatus.contentSourceId=result.contentsources.contentSource.id;
          crawlstatus.pid=crawlProcess.pid;
          fs.writeFile(learningHubCrawlStatusFilePath,JSON.stringify(crawlstatus),function(error, response){

          });
          // update pid in database
          universal.executeQuery('UPDATE content_sources SET pid = ?, sync_start_date=?  WHERE id =?', [crawlProcess.pid, new Date(), result.contentsources.contentSource.id], req,function(){
            console.log('updated database entry ');
          });

          // start a cron job to check status of process every minute so that when process is finished
          cronUtility.createCron(path.join(__dirname,"crawler","test.sh")+ ' '+ crawlProcess.pid+' '+path.join(__dirname)+' "learninghub" >> '+path.join(__dirname,'..','..','..','logs','learning_hub_out.log'),'* * * * *','test_learning_hub_cron_node',function(status){
            console.log('cron status:' +status);
          });
          console.log(crawlProcess.pid);
        },
        // 3. delete live index
        cb=>{
          var index = result.contentsources.contentSource.elasticIndexName;
          elasticUtility.getDocCount(tempIndex, function(countObj){
            if(countObj.total && countObj.total > 0){
              elasticUtility.getDocCount(index, function(countObjIn){
                if(countObjIn.total < countObj.total || (countObjIn.total - countObj.total) < 20){
                  elasticUtility.dropIndex(index, function(err, resp){
                    cb(null, 1);
                  });
                }else{
                  cb({error: "Possibility of error. There are less number of documents crawled. Exiting crawling process"}, null);
                }
              });
            }else{
              cb({error: "Possibility of error. There are less number of documents crawled.  Exiting crawling process"}, null);
            }
          });
        },
        // 4. recreate index with mapping
        cb =>{
          var index = result.contentsources.contentSource.elasticIndexName;
          commonFunctions.createMapping(contentSourceId, 0,req, function (MappingErr, MappingRsult) {
            cb(MappingErr, MappingRsult);
          });
        },
        // 5. copy data from dummy to live index
        cb =>{
          commonFunctions.reIndex(result.contentsources.contentSource.elasticIndexName, tempIndex, function(err, resp){
            if(err)
              cb(err, 0); //error handled
            else
              cb(null, 1);
          })
          crawlstatus.status = 0;
          crawlstatus.pid = 0;
          fs.writeFile(helpDocCrawlStatusFilePath,JSON.stringify(crawlstatus),function(error, response){

          });
        }
      ], function(error, response){
        if(error){
          console.log(error);
          cb(error); //error handled
        }
        else {
          cb(null, 1);
          console.log('Finished with crawling and reindexing..');
        }
      });
    }]
  }, (err, resu) => {
    cbc(err, 1); //error handled
  });
}

function getCrawlerType(contentSourceId,req, cb){
  if(contentSourceId){
    var sql = 'select url, elasticIndexName from content_sources where id=?';
    universal.executeQuery(sql,[contentSourceId],req, function(result){
      if(result.length > 0){
        console.log('starting sap crawler');
        var ctype = 'helpdocs';
        url = result[0].url;
        ctype = url.indexOf('https://help.sap.com') > -1?'helpdocs':(url.indexOf('https://support.sap.com') > -1?'knowledgecenter':(url.indexOf('https://saplearninghub.plateau.com') > -1?'learninghub':'invalid'));
        cb(null, ctype, result[0].elasticIndexName);
      }else
        cb(null, 'invalid');
    });
  }else
    cb(null, 'invalid');
}

router.get("/reIndex", function (req, res, next) {
  var contentSourceId = req.query.contentSourceId;
  reIndex(contentSourceId,req, function(response){
    res.send(response);
  });
});

function reIndex(contentSourceId,req, cb){
  getCrawlerType(contentSourceId, req,function(err, ctype){
    if (err) //error handled
      cb(err, content_source_data); //sending content source data along to kill process and send mail
    else{
      if(ctype != 'invalid'){
        if(getCrawlingStatus(ctype) == 0){
          if(ctype == 'helpdocs'){
            startHelpDocsCrawler(contentSourceId,req, function(err, msg){
              console.log(err, msg);
              //error handled
              return cb(err, content_source_data);
            });
          }else if(ctype == 'knowledgecenter'){
            startKnowledgeCenterCrawler(contentSourceId,req, function(err, msg){
              console.log(err, msg);
              //error handled
              return cb(err, content_source_data);
            });
          }else if(ctype == 'learninghub'){
            startLearningHubCrawler(contentSourceId,req, function(err, msg){
              console.log(err, msg);
              //error handled
              return cb(err, content_source_data);
            });
          }else{
            cb({message: 'Crawler type not valid'}, content_source_data);
            return;
          }
          // cb(null, content_source_data);
        }else{
          commonFunctions.getContentSourceDataById(contentSourceId, req, function(e, r){
            cb({message: 'Crawler is already running'}, r.contentSource);
          });
        }
      }else{
        cb({message: 'Invalid parameter values'}, content_source_data);
      }
    }
  });
}

router.get("/getPids", function (req, res, next) {
  universal.executeQuery('SELECT id, pid, elasticIndexName, url FROM  content_sources', [],req, function(result){
    res.send(result);
  });
});

router.get("/getDocCount", function (req, res, next) {
  var contentSourceId = req.query.contentSourceId;
  getCrawlerType(contentSourceId, req,function(err, ctype, indexname){
    if(ctype != 'invalid' && indexname){
      universal.getSuccessfactorConfig('https://help.sap.com',req, function(result){
        elasticUtility.setupConnection(result);
        elasticUtility.getDocCount(indexname, function(total){
          res.send(total);
        });
      });
    }else{
      res.send({msg: 'Invalid parameter values', total: 0});
    }
  });
});

router.get("/cancelIndex", function (req, res, next) {
  var contentSourceId = req.query.contentSourceId;
  getCrawlerType(contentSourceId,req, function(ctype){
    res.send('Crawler is already running');
  });
});

router.get("/stopIndex", function (req, res, next) {
  var contentSourceId = req.query.contentSourceId;

  // update pid in database
  universal.executeQuery('UPDATE content_sources SET pid = ? WHERE id =?', [0, contentSourceId], req,function(){
    console.log('updated database entry ');
  });
  res.send('Crawler stopped');

});

//console.log(getCrawlingInfo());

/* startHelpDocsCrawler(function(err, msg){
  console.log(err, msg);
}); */

/* cronUtility.createCron(path.join(__dirname,"crawler", "test.sh")+' &>> '+path.join(__dirname,'crawler','logs','out.log'),'* * * * *','testcron_node',function(status){
  console.log('cron status:' +status);
}); */

/* cronUtility.deleteCron('testcron_node', function(status){
  console.log('cron status:' +status);
}) */

module.exports = {
  reIndex: reIndex,
  index: router
};
