var express = require("express");
var router = express.Router();
const kafkaLib = require("../../utils/kafka/kafka-lib");
const { uuid } = require('uuidv4');
var config = require("config");

router.put("/ecosystem", async (req, res, next) => {        //createEcosystem
    const result = await createEcosystem(req);
    await newEcoScRelation(result.uid, req);
    await ecoAnalyticsRelation(result.uid, req);
    await updateScSettings(req);
    await new_analytics_cd_labels(result.uid, req);

    // Fetching the uid of search clients in ecosystem
    const searchClients = await getScUid(req, result.uid);
    
    const kafkaObj = {
        tenantId: req.headers['tenant-id'],
        updatedBy: req.headers.session,
        event: "create",
        ecoDetails: {
            "ecoId": result.uid,
            "ecoName": req.body.ecosystemName,
            "analyticsProps":req.body.analyticsProps,
            "searchClients": searchClients
        }
    }
    kafkaTopic = config.get("kafkaTopic.ecoSystemTopic");
    kafkaEvents(kafkaTopic,kafkaObj);
    res.send(result.data);
});

router.get("/ecosystems", async (req, res, next) => {       //get ecosystems list
    let result = await getEcosystems(req);
    for(let eco = 0; eco < result.length; eco++){
        result[eco].searchClients = await getSCinEco(req,result[eco].uid);
        result[eco].hasDeletedSc = result[eco].searchClients.some(sc => sc.is_deleted);
    }
    res.send(result);
});

router.post("/ecosystem", async (req, res, next) => {       //updateEcosystem
    const result = await updateEcosystem(req);
    await updateScSettings(req);
    await updateEcoScRelation(req);
    await ecoAnalyticsRelation(req.body.uid,req);

    const searchClients = await getScUid(req, req.body.uid);

    const kafkaObj = {
        updatedBy: req.headers.session,
        tenantId: req.headers['tenant-id'],
        event: "update",
        ecoDetails: {
            "ecoId": req.body.uid,
            "ecoName": req.body.ecosystemName,
            "analyticsProps":req.body.analyticsProps,
            "searchClients": searchClients
        }
    }
    kafkaTopic = config.get("kafkaTopic.ecoSystemTopic");
    kafkaEvents(kafkaTopic,kafkaObj);

    res.send(result);
});

router.get("/ecosystem", async (req, res, next) => {        //ecosystem details
    const uid = req.query.uid
    const eco = await getEcosystemDetails(req);
    const searchClients = await getSCinEco(req, uid);
    const analyticsReports = await getAnalyticsinEco(req);

    const result = {
        eco: eco[0],
        searchClients: searchClients,
        analyticsReports: analyticsReports
    }
    res.send(result);
});

router.delete("/ecosystem", async (req, res, next) => {     //delete Ecosystem
    let result = await deleteEcosystem(req);
    let kafkaObj = {
        updatedBy: req.headers.session,
        tenantId: req.headers['tenant-id'],
        event: "delete",
        ecoDetails: {
            "ecoId": req.query.uid,
        }
    }
    kafkaTopic = config.get("kafkaTopic.ecoSystemTopic");
    kafkaEvents(kafkaTopic,kafkaObj);
    res.send(result);
});

router.get("/eco-sc", async (req, res, next) => {           //ecosystem-seachClient relation
    const uid = req.body.uid;
    const result = await getSCinEco(req,uid)
    res.send(result);
});

router.get("/remove-sc", async (req, res, next) => {           //remove deleted sc 
    const uid = req.query.uid;
    const result = await removeDeletedSC(req,uid)
    res.send(result);
});


router.get("/getAnalyticsReports", async (req, res, next) => {           //Analytics reports
    let result = await getAnalyticsReports(req)
    let resToSend = [];
    for (let i =0; i<result.length; i++){
        resToSend.push({
            "analytics_report_id": result[i].id,
            "description": result[i].description,
            "is_enabled": false,
            "name": result[i].name,
            "label": result[i].name,
            "version": result[i].version
        })
    }
    res.send(resToSend);
});

router.get("/search-clients", async (req, res, next) => {           //get all seachClient
    const result = await getSearchClientList(req)
    res.send(result);
});

router.get("/ecosystems-for-analytics", async (req, res, next) => {           //All ecosystems List for Analytics tab
    try{
        const result = await getEcoList(req)
        res.send(result);
    } catch(err) {
        console.log(err);
        res.send(400).send({"Error": "something went wrong"})
    }
});

router.get("/ecosystems-for-analytics-with-sc", async (req, res, next) => {           //All ecosystems List for Analytics tab
    const result = await getEcoListWithSc(req);
    res.send(result);
});

router.get("/getActiveReportsInfo", async (req, res, next) => {           //Analytics reports for analytics tab
    const uid = req.query.uid;
    const result = await getActiveReportsInfo(req,uid)
    res.send({reports:result});
});

router.get("/getAddedSearchClients", async (req, res, next) => {           //All search_clients added in any ecosystem
    const result = await getAddedSearchClients(req);
    res.send(result);
});

router.post("/updatecasedeflectionlabels", async (req, res, next) => {          //update case deflection labels
    const result = await update_analytics_cd_labels(req);
    res.send(result);
})

router.get("/casedeflectionlabels", async (req, res, next) => {                //get case deflection labels
    const result = await get_analytics_cd_labels(req);
    res.send(result);
})

router.post("/updateSettingOnUninstallApp", async (req, res, next) => {   
    const result = await updateSettingOnUninstallApp(req, 'ecosystems');
    await updateSettingOnUninstallApp(req, 'deflection_formula');

    res.send(result);
});


const getScUid = async (req, uid) => {
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            `SELECT
                sc.uid,
                sc.name,
                ecosc.is_deleted,
                sc.search_client_type_id as searchClientTypeId
            FROM
                ecosystem_to_sc ecosc
            INNER JOIN
                search_clients sc 
            ON
                ecosc.search_client_id = sc.id 
            WHERE
                eco_uid = ?`,
            [uid],
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        )
    });
}

const createEcosystem = async (req) => {
    return new Promise((resolve, reject) => {
        let ecosystemName = req.body.ecosystemName;
        let uid = uuid();
        let analyticsProps = req.body.analyticsProps;
        connection[req.headers['tenant-id']].execute.query(
            `Insert INTO ecosystems(uid, 
                name, 
                session_idle_timeout, 
                email_tracking_enabled, 
                external_user_enabled, 
                support_url, 
                stage2_deflection, 
                directly_viewed_results, 
                user_entitlements, 
                account_name, 
                viewed_results, 
                cumulative, 
                stage1,
                true_deflection_enabled) 
            values
                (?,?,?,?,?,?,?,?,?,?,?,?,?,?)`, 
            [uid, 
            ecosystemName, 
            analyticsProps.session_timeout, 
            analyticsProps.email_tracking, 
            analyticsProps.isinternal_tracking, 
            analyticsProps.support_page_url, 
            analyticsProps.stage2_deflection, 
            analyticsProps.diract_pageview, 
            analyticsProps.user_entitlements, 
            analyticsProps.account_name, 
            analyticsProps.viewed_results, 
            analyticsProps.cumulative, 
            analyticsProps.stage1,
            analyticsProps.true_deflection_enabled],
            (e, r) => {
                if (e){
                    return reject(e)
                } else {
                    return resolve({data:r,uid});
                }
            }
        );
    })
}
const updateEcosystem = async (req) => {
    return new Promise((resolve, reject) => {
        let ecosystemName = req.body.ecosystemName;
        let uid = req.body.uid;
        let analyticsProps = req.body.analyticsProps;
        connection[req.headers['tenant-id']].execute.query(
            `UPDATE ecosystems
            SET 
                name = ?, 
                session_idle_timeout = ?, 
                email_tracking_enabled = ?, 
                external_user_enabled = ?, 
                support_url =?,
                stage2_deflection = ?, 
                directly_viewed_results = ?, 
                user_entitlements=?, 
                account_name=?, 
                viewed_results=?, 
                cumulative=?, 
                stage1=?,
                true_deflection_enabled=?
            WHERE 
                uid = ?`, 
            [
                ecosystemName, 
                analyticsProps.session_timeout, 
                analyticsProps.email_tracking, 
                analyticsProps.isinternal_tracking, 
                analyticsProps.support_page_url, 
                analyticsProps.stage2_deflection, 
                analyticsProps.diract_pageview, 
                analyticsProps.user_entitlements, 
                analyticsProps.account_name, 
                analyticsProps.viewed_results, 
                analyticsProps.cumulative, 
                analyticsProps.stage1, 
                analyticsProps.true_deflection_enabled, 
                uid
            ],
            (e, r) => {
                if (e){
                    return reject(e)
                }
                return resolve(r);
            }
        )
    })
}

const getSCinEco = async (req,uid) => {
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            `SELECT
                ecosc.search_client_id,
                sc.name,
                ecosc.is_deleted
            FROM
                ecosystem_to_sc ecosc
            INNER JOIN
                search_clients sc 
            ON
                ecosc.search_client_id = sc.id 
            WHERE
                eco_uid = ?`,
            [uid],
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        )
    });
}

const getAnalyticsinEco = async (req) => {
    let uid = req.body.uid || req.query.uid;
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            `SELECT
                *
            FROM 
                analytics_reports as reports
            LEFT JOIN
                ecosystem_to_analytics_report eco_reports
            ON
                reports.id = eco_reports.analytics_report_id
            WHERE
                reports.is_ecosystem = true AND eco_reports.eco_uid = ?;`,
            [uid],
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        )
    })
}

const newEcoScRelation = async (uid, req) => {
    return new Promise((resolve, reject) => {
        let ecosystemName = req.body.ecosystemName;
        let searchClients = req.body.searchClients;
        let query = "Insert INTO ecosystem_to_sc(eco_uid,search_client_id,eco_name, is_deleted) VALUES "
        let placeHolders = [];
        let values = [];
        for (let sc = 0; sc < searchClients.length; sc++) {
            placeHolders.push("(?,?,?,?)");
            values.push(uid);
            values.push(searchClients[sc].id);
            values.push(ecosystemName);
            values.push(searchClients[sc].is_deleted);
        }
        query += placeHolders.join(",");
        connection[req.headers['tenant-id']].execute.query(
            query,
            values,
            (e, r) => {
                if (e) {
                    reject(e);
                } else {
                    resolve(r);
                }                
            }
            )
    });
}

const updateEcoScRelation = async (req) => {
    return new Promise((resolve, reject) => {
        let uid = req.body.uid;
        let ecosystemName = req.body.ecosystemName;
        let searchClients = req.body.searchClients;
        for (let sc = 0; sc < searchClients.length; sc++) {
            connection[req.headers['tenant-id']].execute.query(
                `Insert 
                    INTO  
                        ecosystem_to_sc(
                            eco_uid,
                            search_client_id,
                            eco_name,
                            is_deleted
                        ) 
                    VALUES 
                        (?,?,?,?) 
                    ON DUPLICATE KEY 
                    UPDATE 
                        is_deleted = VALUES(is_deleted),
                        eco_name = VALUES(eco_name)`,
                [uid, searchClients[sc].id, ecosystemName, searchClients[sc].is_deleted],
                (e, r) => {
                    if (e) {
                        reject(e);
                    } else {
                        resolve(r);
                    }              
                }
            );
        }
    });
}

const ecoAnalyticsRelation = async (uid , req) => {
    let analyticsReports = req.body.analyticsReports

    let placeHolders = [];
    let values = [];


    for (let r = 0; r < analyticsReports.length; r++) {
        placeHolders.push("(?,?,?,?)");
        values.push(uid);
        values.push(analyticsReports[r].id);
        values.push(analyticsReports[r].is_enabled);
        values.push(analyticsReports[r].label);
    }
    let query = `
        Insert 
            INTO 
                ecosystem_to_analytics_report(
                    eco_uid,
                    analytics_report_id,
                    is_enabled,
                    label
                ) 
            VALUES 
        ${placeHolders.join(",")} 
        ON DUPLICATE KEY UPDATE
          eco_uid             = values(eco_uid),
          analytics_report_id = values(analytics_report_id),
          is_enabled          = values(is_enabled),
          label               = values(label);
        `
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            query,
            values,
            (e, r) => {
                if (e) {
                    reject(e);
                } else {
                    resolve(r);
                }                
            }
        )
    });
}

const new_analytics_cd_labels = async (uid, req) => {
    let query =
        "INSERT INTO eco_cd_labels (eco_id ,report_title_name ,custom_title_name ,tooltip ) VALUES" +
        `(?,'Search Sessions (stage1)', 'Search Sessions', 'Number of sessions in which <br />users visited the search page.' ),` +
        `(?,'Click Sessions (stage1)', 'Click Sessions', 'Successful Searches Who didn\\\'t Visit Support' ),` +
        `(?,'No Clicks Sessions (stage1)', 'No Clicks Sessions', 'Unsuccessful Searches' ),` +
        `(?,'Exit (Stage1)', 'Exit', 'Same as Total Sessions.' ),` +
        `(?,'Total Sessions (Stage1)', 'Total Sessions', 'Total number<br />of unique sessions.' ),` +
        `(?,'No Search Sessions (Stage1)', 'No Search Sessions', 'Number of sessions in which <br/>users didn\\\'t visit the search page.' ),` +
        `(?,'Support Sessions (Stage1)', 'Support Sessions', 'Number of sessions in which users <br />visited the support page.' ),` +
        `(?,'Search Sessions (stage2)', 'Search Sessions', 'Number of sessions in which <br />users visited the search page.' ),` +
        `(?,'Support Sessions (Stage2)', 'Support Sessions', 'Number of sessions in which users <br />visited the support page.' ),` +
        `(?,'No Search Sessions (Stage2)', 'No Search Sessions', 'Number of sessions in which users <br />visited the support page <br />and did not search.' ),` +
        `(?,'Click Sessions (Stage2)', 'Click Sessions', 'Number of sessions in which users <br />searched and clicked on results.' ),` +
        `(?,'No Click Sessions (Stage2)', 'No Click Sessions', 'Number of sessions in which users <br />searched but did not click.' ),` +
        `(?,'Case Created Sessions (Stage2)', 'Case Created Sessions', 'Number of sessions in which users <br />created a case.' ),` +
        `(?,'Exit (Stage2)', 'Exit', 'Number of sessions who left the support <br />or have no activity on support' ),` +
        `(?,'Direct Pageview', 'Direct Pageview', 'Number of sessions who directly viewed the results' )`;

    let array = [];
    while (array.length < 15) {
        array.push(uid);
    }

    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            query,
            array,
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        )
    })
}

const update_analytics_cd_labels = async (req) => {
    let data = req.body.data;
    let deleteQuery = `Delete from eco_cd_labels where eco_id = ?`

    let query =
        "INSERT INTO eco_cd_labels (eco_id ,report_title_name ,custom_title_name ,tooltip ) VALUES" +
        `(?,'Search Sessions (stage1)', ?, ? ),` +
        `(?,'Click Sessions (stage1)', ?, ?),` +
        `(?,'No Clicks Sessions (stage1)', ?, ? ),` +
        `(?,'Exit (Stage1)', ?, ? ),` +
        `(?,'Total Sessions (Stage1)', ?, ? ),` +
        `(?,'No Search Sessions (Stage1)', ?, ? ),` +
        `(?,'Support Sessions (Stage1)', ?, ? ),` +
        `(?,'Search Sessions (stage2)', ?, ? ),` +
        `(?,'Support Sessions (Stage2)', ?, ? ),` +
        `(?,'No Search Sessions (Stage2)', ?, ? ),` +
        `(?,'Click Sessions (Stage2)', ?, ? ),` +
        `(?,'No Click Sessions (Stage2)', ?, ? ),` +
        `(?,'Case Created Sessions (Stage2)', ?, ? ),` +
        `(?,'Exit (Stage2)', ?, ? ),` +
        `(?,'Direct Pageview', ?, ? )`;

    let structuredArray = [];

    for (let p = 0; p < data.length; p++) {
        structuredArray.push(
            req.body.uid,
            data[p].custom_title_name,
            data[p].tooltip
        );
    }



    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            deleteQuery,
            [req.body.uid],
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(new Promise((resolve, reject) => {
                        connection[req.headers['tenant-id']].execute.query(
                            query,
                            structuredArray,
                            (e, r) => {
                                if (e) {
                                    return reject(e);
                                } else {
                                    return resolve(r);
                                }
                            }
                        )
                    }));
                }
            }
        )
    })
}

const get_analytics_cd_labels = async (req) => {
    let query = `SELECT * FROM eco_cd_labels where eco_id = ?`
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            query,
            [req.query.uid],
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        );
    })
}

const getEcosystems = async (req) => {
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            `SELECT 
                uid,
                name
            FROM 
                ecosystems
            ORDER BY
                created_date DESC;
            `,
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        )
    })
}
const getEcosystemDetails = async (req) => {
    let uid = req.query.uid
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            `SELECT * FROM ecosystems WHERE uid = ?`,
            [uid],
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        )
    })
}

const deleteEcosystem = async (req) => {
    let uid = req.query.uid;
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            `DELETE FROM ecosystems WHERE uid = ?`,
            [uid],
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        )
    });
}

const removeDeletedSC = async (req,uid) => {
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            `DELETE 
                FROM 
                    ecosystem_to_sc 
                WHERE 
                    eco_uid = ? 
                AND 
                    is_deleted = true`,
            [uid],
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        )
    });
}

const updateScSettings = async (req) => { 
    return new Promise((resolve, reject) => {
        let searchClients = req.body.searchClients;
        let analyticsProps = req.body.analyticsProps;
        updateStage2Status(req);
        for (let sc = 0; sc < searchClients.length; sc++) {
            connection[req.headers['tenant-id']].execute.query(
                `UPDATE 
                    deflection_formula
                SET 
                    session_idle_timeout = ?, 
                    email_tracking_enabled = ?, 
                    external_user_enabled = ?, 
                    support_url =?,
                    directly_viewed_results = ?, 
                    account_name=?, 
                    viewed_results=?, 
                    cumulative=?, 
                    stage1=?,
                    true_deflection_enabled=?
                WHERE 
                    search_client_id = ?`,
                [
                    analyticsProps.session_timeout, 
                    analyticsProps.email_tracking, 
                    analyticsProps.isinternal_tracking, 
                    analyticsProps.support_page_url, 
                    analyticsProps.diract_pageview, 
                    analyticsProps.account_name, 
                    analyticsProps.viewed_results, 
                    analyticsProps.cumulative, 
                    analyticsProps.stage1,
                    analyticsProps.true_deflection_enabled, 
                    searchClients[sc].id
                ],
                (e, r) => {
                    if (e) {
                        return reject(e);
                    } else {
                        return resolve(r);
                    }
                }
            );
            
        }
    });
}

const updateStage2Status = async (req) => { 
    return new Promise((resolve, reject) => {
        let searchClients = req.body.searchClients;
        let analyticsProps = req.body.analyticsProps;
        for (let sc = 0; sc < searchClients.length; sc++) {
            connection[req.headers['tenant-id']].execute.query(
                `UPDATE 
                    search_clients 
                SET 
                    is_case_deflected_shown = ?
                WHERE
                    id = ?`,
                [
                    analyticsProps.stage2_deflection, 
                    searchClients[sc].id
                ],
                (e, r) => {
                    if (e) {
                        return reject(e);
                    } else {
                        return resolve(r);
                    }
                }
            );
            
        }
    });
}

const kafkaEvents = async (topic, data) => {
    let objData = data
    kafkaLib.publishMessage({
        topic: topic,
        messages: [
          {
            value: JSON.stringify(objData),
            key: objData.ecoDetails.ecoId
          },
        ],
    });
}

const getAnalyticsReports = async (req) => {
    return new Promise((resolve, reject) => {
        sql = `SELECT * FROM analytics_reports where is_ecosystem = true;`;
        connection[req.headers['tenant-id']].execute.query(
            {
                sql: sql,
            },
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(JSON.parse(JSON.stringify(r)));
                }
              }
        )
    });
}

const getSearchClientList = async (req) => {
    let allSearcheClients = await getAllSearchClients(req);
    let addedSearcheClients = await getAddedSearchClients(req);
    let searchClients = [];
    for (let i = 0; i < allSearcheClients.length; i++) {
        searchClients.push(allSearcheClients[i])
        for (var j = 0; j <addedSearcheClients.length; j++) {
            if (allSearcheClients[i].id == addedSearcheClients[j].search_client_id && !allSearcheClients[i].is_deleted) {
                searchClients[i].eco_name=addedSearcheClients[j].eco_name
            }
        } 
    }
    return searchClients;
}

const getAllSearchClients = async (req) => {
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            `SELECT id, name FROM search_clients WHERE ab_test_parent IS NULL OR ab_test_parent = '';`,
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        )
    });
}

const getAddedSearchClients = async (req) => {
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            `SELECT 
                search_client_id, 
                eco_name 
            FROM 
                ecosystem_to_sc 
            WHERE 
                is_deleted=false;`,
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        )
    });
}

const getEcoList = async (req) => {
    try {
        return new Promise((resolve, reject) => {
            connection[req.headers['tenant-id']].execute.query(
                `SELECT
                    uid,
                    name,
                    email_tracking_enabled,
                    external_user_enabled,
                    user_entitlements,
                    stage2_deflection
                FROM 
                    ecosystems;`,
                (e, r) => {
                    if (e) {
                        return reject(e);
                    } else {
                        return resolve(r);
                    }
                }
            )
        })
    } catch(err) {
        console.log(err);
    }
}

const getEcoListWithSc = async (req) => {
    return new Promise((resolve, reject) => {
        let ecoQuery = `select
                            e.uid as uid,
                            e.name,
                            e.email_tracking_enabled,
                            e.external_user_enabled,
                            e.user_entitlements,
                            e.stage2_deflection,
                            ecosc.search_client_id,
                            ecosc.is_deleted,
                            sc.uid as search_client_uid,
                            sc.search_client_type_id
                        from
                            ecosystems e
                        left join ecosystem_to_sc ecosc 
                        on
                            e.uid = ecosc.eco_uid
                        left join search_clients sc 
                        on
                            sc.id = ecosc.search_client_id`
        if((req.query.excludeFlag !== true || req.query.excludeFlag !== 'true') && (req.query.ecoEditFlag === true || req.query.ecoEditFlag === 'true')){
            ecoQuery += ` where e.uid != '${req.query.ecoToEdit}'`;
        }
        connection[req.headers['tenant-id']].execute.query(ecoQuery,
            (e, r) => {
                if (e) {
                    return reject(e);
                } else {
                    return resolve(r);
                }
            }
        )
    })
}

const getActiveReportsInfo = async (req, uid) => {
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
            `SELECT
                id,
                analytics_report_id,
                is_enabled,
                label
            FROM 
                ecosystem_to_analytics_report
            WHERE
                eco_uid = ?;`,
            [uid],
            (e, r) => {
                if (e) {
                    reject(e);
                } else {
                    resolve(r);
                }
            }
        )
    })
}

const updateSettingOnUninstallApp = async (req, tableName) => {
    if (req.body.addonId === 8) {
        return new Promise((resolve, reject) => {
            connection[req.headers['tenant-id']].execute.query(
                `UPDATE 
                    ${tableName}
                SET 
                    email_tracking_enabled = false, 
                    external_user_enabled = false, 
                    account_name= '';`, 
                (e, r) => {
                    if (e) {
                        reject(e);
                    } else {
                        resolve(r);
                    }
                }
            )
        });
    } else if (req.body.addonId === 7 && tableName === 'ecosystems') {
        return new Promise((resolve, reject) => {
            connection[req.headers['tenant-id']].execute.query(
                `UPDATE 
                    ${tableName}
                SET 
                    support_url = null,
                    directly_viewed_results = false,
                    stage2_deflection = false,
                    viewed_results= null;`, 
                (e, r) => {
                    if (e) {
                        reject(e);
                    } else {
                        resolve(r);
                    }
                }
            )
        });
    } else if (req.body.addonId === 7 && tableName === 'deflection_formula') {
        return new Promise((resolve, reject) => {
            connection[req.headers['tenant-id']].execute.query(
                `UPDATE 
                    ${tableName}
                SET 
                    support_url = null,
                    directly_viewed_results = false,
                    viewed_results= null;`, 
                (e, r) => {
                    if (e) {
                        reject(e);
                    } else {
                        return new Promise((resolve, reject) => {
                            connection[req.headers['tenant-id']].execute.query(
                                `UPDATE 
                                    search_clients
                                SET 
                                    is_case_deflected_shown = false;`, 
                                (e, r) => {
                                    if (e) {
                                        reject(e);
                                    } else {
                                        resolve(r);
                                    }
                                }
                            )
                        });
                    }
                }
            )
        });
    }
}


module.exports.router= router;
