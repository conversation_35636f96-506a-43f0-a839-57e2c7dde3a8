/* Created By <PERSON><PERSON>
    On 06/02/18
*/


var express = require('express');
var request = require('request');
var async = require('async');
const commonFunctions = require('./../../utils/commonFunctions');
var fs = require('fs');
var router = express.Router();

//************************ Start of Confluence Permissions ***************************'
router.get('/getContent', function (req, res, next) {
    var obj = {
        "contentSources": ["148"],
        "email": "<EMAIL>"
    }
    getPermissionObjects(obj,req, function (err, data) {
        res.send(data);
    })
});

const getPermissionObjects = function (contentSources,req, callback) {
    var asyncTasks = [];
    var contentSourceIds = contentSources['contentSources']
    var email = contentSources['email'];
    contentSourceIds.map(x => {
        asyncTasks.push(jiraPermissions.bind(null, x, email,req));
    })
    async.series(asyncTasks, function (err, data) {
        if (err) {
            callback(err, null);
        }
        else
            callback(null, data);
    })
}

const jiraPermissions = function (content_source_id, email,req, callback) {
    commonFunctions.getContentSourceDataById(content_source_id,req, function (err, resultData) {
        var username = email;
        var options = {
            method: 'GET',
            url: resultData.contentSource.url + '/rest/api/2/user/search?username=' + username,
            /*  oauth: {
                          consumer_key: resultData.authorization.publicKey,
                          private_key: resultData.authorization.privateKey,
                          token: resultData.authorization.accessToken,
                          token_secret: resultData.authorization.refreshToken,
                          signature_method: 'RSA-SHA1'
                      }, */
            headers:
            {
                'content-type': 'application/json'
              //  authorization: 'Basic ' + new Buffer(resultData.authorization.username + ":" + resultData.authorization.password).toString("base64")
            }
        };
        if (resultData.authorization.authorization_type=="OAuth") {
            options["oauth"] = {
                consumer_key: resultData.authorization.publicKey,
                private_key: resultData.authorization.privateKey,
                token: resultData.authorization.accessToken,
                token_secret: resultData.authorization.refreshToken,
                signature_method: 'RSA-SHA1'
            }
        } else {
            options["headers"]["authorization"] = 'Basic ' + new Buffer(resultData.authorization.username + ":" + resultData.authorization.password).toString("base64");

        }
        request(options, function (error, response, body) {
            if (error || !JSON.parse(body).length) {
                commonFunctions.errorlogger.error("Error", error);
                callback("Some Error", []);
            }
            else {
                if (commonFunctions.checkJSON(body)) {
                    var body = JSON.parse(body)[0];
                    var ObjToReturn = {
                        "id": content_source_id,
                        "perms": []
                    };

                    getJiraGroups(body.key, resultData, 0, 200, [], function (errGroups, dataGroups) {

                        ObjToReturn["perms"] = dataGroups;
                        ObjToReturn["perms"].push(body.self);
                        callback(errGroups, ObjToReturn);
                    });

                } else {
                    callback('Invalid Creds', []);
                }
            }
        });
    });
}

const getJiraGroups = function (username, resultData, start, limit, initialArr, callback) {
    var options = {
        method: 'GET',
        url: resultData.contentSource.url + '/rest/api/2/user/groups?key=' + username + '&size=' + limit + '&start=' + start,
        /*  oauth: {
                      consumer_key: resultData.authorization.publicKey,
                      private_key: resultData.authorization.privateKey,
                      token: resultData.authorization.accessToken,
                      token_secret: resultData.authorization.refreshToken,
                      signature_method: 'RSA-SHA1'
                  }, */
        headers:
        {
            'content-type': 'application/json'
           // authorization: 'Basic ' + new Buffer(resultData.authorization.username + ":" + resultData.authorization.password).toString("base64")
        }
    };
    if (resultData.authorization.authorization_type=="OAuth") {
        options["oauth"] = {
            consumer_key: resultData.authorization.publicKey,
            private_key: resultData.authorization.privateKey,
            token: resultData.authorization.accessToken,
            token_secret: resultData.authorization.refreshToken,
            signature_method: 'RSA-SHA1'
        }
    } else {
        options["headers"]["authorization"] = 'Basic ' + new Buffer(resultData.authorization.username + ":" + resultData.authorization.password).toString("base64");

    }
    request(options, function (error, response, body) {
        if (error) {
            commonFunctions.errorlogger.error("Error", error);
            callback(error, null);
        }
        else {
            if (commonFunctions.checkJSON(body)) {
                var body = JSON.parse(body);
                for (var i = 0; i < body.length; i++) {
                    initialArr.push(body[i].self);
                }
                if (body._links && body._links.next) {
                    start = start + limit;
                    getJiraGroups(username, resultData, start, limit, initialArr, function (errGRP, grps) {
                        if (errGRP) {
                            callback(errGRP, null);
                        } else {
                            callback(null, grps);
                        }
                    });
                } else {
                    callback(null, initialArr);
                }

            } else {
                callback('Invalid Creds', null);
            }
        }
    });

}

//************************ End of Confluence Permissions ***************************

module.exports = {
    getPermissionObjects: getPermissionObjects,
    router: router
}
