/**
 * Created by harman on 19/10/16.
 */
var request = require('request');
var https = require('https');
var http = require('http');
var url = require('url');
var htmlparser = require("htmlparser2");
var async=require("async");
var fs=require("fs");
var universal=require('./../universal');
var constants=require('./../../constants/constants');
var commonFunctions = require('../../utils/commonFunctions');


getRecentContent = function(req, res) {
    var authObj= {
    };

    var fromDate=new Date(req.query.fromDate?req.query.fromDate: new Date()).getTime();
    var toDate=new Date(req.query.fromDate?req.query.toDate: new Date()).getTime();

    var api_crawling_id=req.query.api_crawling_id
  universal.getDataAuthLithium(api_crawling_id,function (errAuth, data) {
    if (errAuth == 0) {
      callback({"error":"Lithium credentials are not saved"});
    } else {
      authObj.communityUrl = data[0].community_url;
      authObj.username = data[0].username;
      authObj.password = data[0].password;
      authObj.popup_username = data[0].popup_username;
      authObj.popup_password = data[0].popup_password;
      authObj.basicAuthEnabled = data[0].basicAuthEnabled;
      authObj.indexName=data[0].indexName;
      commonFunctions.errorlogger.info(JSON.stringify(authObj));
      // authObj.communityUrl = config.get('lithium.baseUrl');
      // authObj.username = config.get('lithium.communityUsername');
      // authObj.password = config.get('lithium.communityPassword');
      // authObj.popup_username = config.get('lithium.httpUsername');
      // authObj.popup_password = config.get('lithium.httpPassword');
      // console.log(JSON.stringify(authObj));
      var sessionKey = ""
      getSessionKey(authObj, function (sessionKeyResult) {
        sessionKey = sessionKeyResult;
        async.parallel(
          [
            function (cb) {
              crawlRecentMessages(authObj,sessionKey, 100, 1, fromDate, toDate, [], function (data) {

                crawlMessageArray(authObj,sessionKey, data, function (result) {
                  cb(null, 2)
                })
              });
            }], function (err, result) {

          })

        res.send(authObj);

      })

    }
  });
}


crawlMessageArray=function (authObj,sessionKey,data,callback) {
    var taskThread=[]
    for(var t=0;t<data.length;t++)
    {
        taskThread.push((function (t) {
            return function (cbInternal) {

                //get thread data in this message




                async.series([
                    function (cbInternal1) {
                      commonFunctions.errorlogger.info("data",data[t])
                        getThreadData(authObj,sessionKey,data[t],function (threadData) {
                            data[t]=threadData
                            cbInternal1(null,1)
                        })
                    },function (cbInternal1) {

                        getThreadTags(authObj,sessionKey,data[t],function (tags) {


                            data[t].tags=tags
                            cbInternal1(null,2)
                        })
                    },function (cbInternal1) {
                        if(data[t].interaction_style['$']=="group") {
                            getGroupMembers(authObj,sessionKey, data[t].boardId, function (groupMembers) {
                                data[t].groupUser=groupMembers
                                cbInternal1(null, 3)
                            })
                        }
                        else {
                            data[t].groupUser=[]
                            cbInternal1(null, 3)
                        }
                    }
                  ,function (cbInternal1) {

                    getbulkcontentNew.getAttachmentData(authObj, sessionKey, data[t].href.split('/')[3], function (dataImage) {
                      if(dataImage!=0)
                      {
                        data[t]["attachment"]=1
                        var length =  dataImage.length > 4 ? 4: dataImage.length;
                        for(var da=0;da<length;da++)
                        {
                          data[t]["file"+da]=dataImage[da]
                        }
                        cbInternal1(null,4)
                      }
                      else  cbInternal1(null,4)

                    })

                  }
                ],function (err,result) {
                    cbInternal(null,data[t])
                })

            }
        })(t))
    }

    async.series(taskThread,function (err,result) {

      var threads = data;
      //  console.log(threads);
      var bulkDataNoImage = ""
      var bulkDataImage = ""
      for (var s = 0; s < threads.length; s++) {

        if(!threads[s].attachment)
          getbulkcontentNew.saveDataFormatLithium(authObj,threads[s], threads[s].interaction_style['$'], function (resultBulkData) {
            // console.log("Done--->>>---")
            // res.send(authObj);
            bulkDataNoImage += resultBulkData + "\n"
          })
        else
        {
          getbulkcontentNew.saveDataFormatLithiumObject(authObj,threads[s], threads[s].interaction_style['$'], function (resultBulkData) {
            // console.log("Done--->>>---")
            // res.send(authObj);
            getbulkcontentNew.singleUpload(resultBulkData)
          })
        }


      }

      if(bulkDataNoImage)
        getbulkcontentNew.BulkUpload(bulkDataNoImage)


    })
    callback(1)
}



function getThreadTags(authObj,sessionKey,thread,callback) {

var threadId=thread.id['$']
    var options = { method: 'GET',
        url:  authObj.communityUrl+config.get('lithium.restPath')+'/messages/id/'+threadId+'/tagging/tags/all',
        qs:
        { 'restapi.session_key': sessionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view',
            page_size: '100',
            page: '1' },
    };
  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }

    request(options, function (error, response, body) {
         if (error) {
          commonFunctions.errorlogger.error( error)
        return;
      }

        body=JSON.parse(body)

        var tagsArr=body.response.tags.tag;
        var tags=[]

        for (var l = 0; l < tagsArr.length; l++) {
            tags.push( tagsArr[l].text['$'])
        }
        callback(tags)
    });

}


function getGroupMembers(authObj,sessionKey,groupId,callback) {
    var options = { method: 'GET',
        url:  authObj.communityUrl+config.get('lithium.restPath')+'/groups/id/'+groupId+'/members',
        qs:
        { 'restapi.session_key': sessionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view',
            page_size: '100',
            page: '1' },
    };
  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
    request(options, function (error, response, body) {
         if (error) {
          commonFunctions.errorlogger.error( error)
        return;
      }

        body=JSON.parse(body)

        var userArr=body.response.users.user;
        var users=[]

        for (var l = 0; l < userArr.length; l++) {
            users.push( userArr[l].id['$'])
        }
        callback(users)
    });
}

var crawlRecentMessages=function(authObj,sessionKey,page_size,page,fromDate,toDate,initialarr,callback){

    var options = { method: 'GET',
        url: authObj.communityUrl+config.get('lithium.restPath')+'/search/messages',
        qs:
        { 'restapi.session_key':sessionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view',
            page_size: page_size,
            page: page ,
            q: 'date:['+fromDate+' TO '+toDate+'] '},
    };
  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }

    request(options, function (error, response, body) {
        if (error) callback(0)

        body=JSON.parse(body)
        for(var i=0;i<body.response.messages.message.length;i++)
        {
            initialarr.push(body.response.messages.message[i]);
        }
        if(body.response.messages.message.length==100)
        {
            page++;
            crawlRecentMessages(authObj,sessionKey,100,page,fromDate,toDate,initialarr,function (result) {
                callback(result)
            })
        }
        else
        {
            callback(initialarr)
        }
    });


}


function getThreadData(authObj,sessionKey,thread,callback) {


    var id = thread.thread.href.split('/')[3];
    var messagesArry = []
    var options = {
        method: 'GET',
        url: authObj.communityUrl + config.get('lithium.restPath') + '/threads/id/' + id,
        qs: {
            'restapi.session_key':sessionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view',
        },
    };

  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }



    request(options, function (error, response, body) {
        if (error) callback(0);

        body = JSON.parse(body)

        thread=body.response.thread //this extraline
        var boards = []
        async.parallel([
            //get board on thread

            function (cbOuter) {

                getBoardData(authObj, id,sessionKey, function (board) {

                    thread.boardName = board.response.board.title['$']
                    thread.boardId = board.response.board.id['$']
                    thread.boardHref = board.response.board.view_href
                    cbOuter(null, 1)
                })
            },//get ctaegory on thread
            function (cbOuter) {
                getCategoryData(authObj, id,sessionKey, function (category) {
                    thread.categoryName = category.response.category.title['$']
                    thread.categoryId = category.response.category.id['$']

                    cbOuter(null, 2)
                })
            }, function (cbOuter) {
                if(thread.interaction_style['$']=="group") {
                    getGroupData(authObj,thread.board.href.split('/')[3], sessionKey, function (privacy) {

                        thread.privacy = privacy

                        cbOuter(null,thread)
                    })
                }
                else {
                    thread.privacy = "open"
                    cbOuter(null, thread)
                }
            }
        ], function (err, result) {
            var messages = body.response.thread.messages.linear.message
            var taskThreadMessage = [];
            for (var m = 0; m < messages.length; m++) {
                taskThreadMessage.push((function (m) {
                    return function (cb) {
                        if (messages[m].id['$'] != id) {
                            getMessageData(authObj,sessionKey, messages[m], function (message) {
                                    if (message != 0)
                                    {
                                        universal.parsehtmltoText('<body>' + message.response.message.body['$'] + '</body>', function (err,txt ) {
                                            messagesArry.push(txt)

                                        })
                                    }
                                cb(null)
                            })
                        }
                        else {
                            universal.parsehtmltoText('<body>' + body.response.thread.messages.topic.body['$'] + '</body>', function (err, txt) {
                                thread.body = txt;
                                thread.kudos = body.response.thread.messages.topic.kudos.count['$']
                                thread.labels = []
                                if(body.response.thread.messages.topic.labels) {
                                    for (var l = 0; l < body.response.thread.messages.topic.labels.label.length; l++) {
                                        thread.labels.push( body.response.thread.messages.topic.labels.label[l].text['$'])
                                    }
                                }
                                thread.viewCount=body.response.thread.messages.topic.views.count['$']
                                thread.authorHref = body.response.thread.messages.topic.author.view_href
                                thread.author = body.response.thread.messages.topic.author.login['$']
                                thread.post_time = body.response.thread.messages.topic.post_time['$']
                                thread.subject = body.response.thread.messages.topic.subject['$']
                                thread.view_href = body.response.thread.view_href;
                                cb(null)
                            });
                        }

                    }
                })(m))
            }
            async.parallel(taskThreadMessage, function (err, result) {
                thread.comments = messagesArry;
                callback(thread)
            })
        })
    });

}

function getGroupData(authObj,boardId,sessionKey,callback) {
    var options = { method: 'GET',
        url:  authObj.communityUrl+config.get('lithium.restPath')+'/groups/id/'+boardId,
        qs:
        {   'restapi.session_key':sessionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view',
        }
    };
  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
    request(options, function (error, response, body) {
         if (error) {
          commonFunctions.errorlogger.error( error)
        return;
      }

        body=JSON.parse(body)
        return callback(body.response.group.privacy['$'])
    });
}

function getBoardData(authObj,threadId,sessionKey,callback) {
    var options = { method: 'GET',
        url:  authObj.communityUrl+config.get('lithium.restPath')+'/threads/id/'+threadId+'/board',
        qs:
        {   'restapi.session_key':sessionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view',
        }
    };
  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
    request(options, function (error, response, body) {
         if (error) {
          commonFunctions.errorlogger.error( error)
        return;
      }

        body=JSON.parse(body)
        return callback(body)
    });
}


function getBoardRoles(authObj,BoardId,sessionKey,callback) {
    var options = { method: 'GET',
        url:  authObj.communityUrl+config.get('lithium.restPath')+'/boards/id/'+BoardId+'/roles',
        qs:
        {   'restapi.session_key':sessionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view',
        }
    };
  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
    request(options, function (error, response, body) {
         if (error) {
          commonFunctions.errorlogger.error( error)
        return;
      }

        body=JSON.parse(body)
        var roles=[]
        var rolesTemp=body.response.roles.role
        for(var i=0;i<rolesTemp.length;i++)
        {
            roles.push({id:rolesTemp[i].id['$'],name:rolesTemp[i].name['$']})
        }
        return callback(roles)
    });
}

function getCategoryData(authObj,threadId,sessionKey,callback) {
    var options = { method: 'GET',
        url:  authObj.communityUrl+config.get('lithium.restPath')+'/threads/id/'+threadId+'/board/category',
        qs:
        {   'restapi.session_key':sessionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view',
        }
    };
  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
    request(options, function (error, response, body) {
         if (error) {
          commonFunctions.errorlogger.error( error)
        return;
      }

        body=JSON.parse(body)
        return callback(body)
    });
}


function getMessageData(authObj,sessionKey,message,callback) {

    var id=message.id['$'];
    commonFunctions.errorlogger.info("id",id)
    var options = { method: 'GET',
        url: authObj.communityUrl+config.get('lithium.restPath')+'/messages/id/'+id,
        qs:
        { 'restapi.session_key':sessionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view',
        }
    };
  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
    request(options, function (error, response, body) {
         if (error) {
          commonFunctions.errorlogger.error( error)
        return;
      }

        // console.log(body);
        try{
            body=JSON.parse(body)
            callback(body)
        }
        catch (exception)
        {
            callback(0)
        }



    });


}


//get session key should be condition based will do in future

const getSessionKey=function (authObj,callback) {



    var options = { method: 'GET',
        url: authObj.communityUrl+config.get('lithium.restPath')+'/authentication/sessions/login',
        qs:
        { 'user.login': authObj.username,
            'user.password': authObj.password,
            'restapi.response_format': 'json',
        }
    };
  if(authObj.basicAuthEnabled)
  {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
  commonFunctions.errorlogger.info("options are",options)

    request(options, function (error, response, body) {
         if (error) {
          commonFunctions.errorlogger.error( error)
        return;
      }


      commonFunctions.errorlogger.info("body",body)
        body=JSON.parse(body)
        return callback((body).response.value['$']);
    });
}



function deleteMapping(indexName,callback){
    var optionsDel = { method: 'DELETE',
        url: "http://"+config.get('elasticIndexCS.host')+":"+config.get('elasticIndexCS.port')+"/"+indexName+'/',
    };

    request(optionsDel, function (error, responseDel, bodyDel) {
      if (error) {
        commonFunctions.errorlogger.error( error)
        return;
      }
      commonFunctions.errorlogger.info(bodyDel);
        bodyDel=JSON.parse(bodyDel);
        //index not found handles the case when no index is present previously
        if(bodyDel.acknowledged==true||bodyDel.error.type=="index_not_found_exception"){
            callback(null);
            return;
        }
        callback(bodyDel.error);
    });

}

const getUserDataSync = function(req, res) {
    var authObj= {
    };


  universal.getDataAuthLithium(function (errAuth, data) {
    if (errAuth == 0) {
      callback({"error":"Lithium credentials are not saved"});
    } else {
      authObj.communityUrl = data[0].community_url;
      authObj.username = data[0].username;
      authObj.password = data[0].password;
      authObj.popup_username = data[0].popup_username;
      authObj.popup_password = data[0].popup_password;
      authObj.basicAuthEnabled = data[0].basicAuthEnabled;
      commonFunctions.errorlogger.info(JSON.stringify(authObj));
      var sessionKey = ""

      getSessionKey(authObj, function (sessionKeyResult) {
        sessionKey = sessionKeyResult;


        async.series(
          [
            function (cb) {
              getAllUsers(authObj,sessionKeyResult, 10, 1, [], function (data) {

                var taskUsers = []
                for (var t = 0; t < data.length; t++) {
                  taskUsers.push((function (t) {
                    return function (cbInternal) {

                      async.parallel([
                        function (internalCb) {
                          getUserRank(authObj,sessionKey, data[t], function (rank) {
                            internalCb(null, rank)
                          })
                        },
                        function (internalCb) {
                          getPostCounts(authObj,sessionKey, data[t], function (postCounts) {
                            internalCb(null, postCounts)
                          })
                        }
                      ], function (err, result) {

                        data[t].userRank = result[0]
                        data[t].postCount = result[1]
                        cbInternal(null, "1")
                      })
                    }
                  })(t))
                }

                async.series(taskUsers, function (err, result) {

                  commonFunctions.errorlogger.info(data);
                  var bulkData = ""
                  for (var s = 0; s < data.length; s++) {
                    updateObject(data[s], function (result) {

                      getbulkcontentNew.saveDataFormatLithium(result, "User", function (resultBulkData) {
                        // console.log("Done--->>>---")
                        // res.send(authObj);
                        bulkData += resultBulkData + "\n"
                      })
                    })


                  }

                  BulkUpload(bulkData)


                })
                cb(null, 2)

              });
            }], function (err, result) {

          })

        res.send(authObj);

      })
    }
  })
}


function updateObject(data,cb) {
    var user={}
    user.id={}
    user.id['$']=data.id['$']
    user.userRank=data.userRank;
    user.view_href=data.view_href;
    user.registration_time=data.registration_time['$']
    user.postCount=data.postCount
    user.login=data.login['$']
    user.email=data.email['$']
    cb(user);
}

function getAllUsers(authObj,seesionKey,page_size,page,initialarr,callback) {
    var options = { method: 'GET',
        url: authObj.communityUrl+config.get('lithium.restPath')+'/users',
        qs:
       {
         //'restapi.session_key':seesionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view',
            page_size: page_size,
            page: page }
    };
  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }

    request(options, function (error, response, body) {
        if (error) callback(0)

        body=JSON.parse(body)
        for(var i=0;i<body.response.users.user.length;i++)
        {
            initialarr.push(body.response.users.user[i]);
        }
        if(body.response.users.user.length==10)
        {
            page++;
            getAllUsers(authObj,seesionKey,10,page,initialarr,function (result) {
                callback(result)
            })
        }
        else
        {
            callback(initialarr)
        }
    });

}


function getUserRank(authObj,sessionKey,user,callback) {
    var id=user.id['$'];
    var options = { method: 'GET',
        url: config.get('lithium.baseUrl')+config.get('lithium.restPath')+'/users/id/'+id+'/ranking/name',
        qs:
        {  'restapi.session_key':sessionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view'}
    };
  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }

    request(options, function (error, response, body) {
        if (error) callback(0)

        body=JSON.parse(body)
        callback(body.response.value["$"])
    });


}

function getPostCounts(authObj,sessionKey,user,callback) {
    var id=user.id['$'];
    var options = { method: 'GET',
        url: authObj.communityUrl+config.get('lithium.restPath')+'/users/id/'+id+'/posts/count',
        qs:
        {  'restapi.session_key':sessionKey,
            'restapi.response_format': 'json',
            'restapi.response_style': 'view'
        }
    };
  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }

    request(options, function (error, response, body) {
        if (error) callback(0)

        body=JSON.parse(body)
        callback(body.response.value["$"])
    });
}

const getAllboards=function (api_crawling_id,callback) {
  var authObj = {
    'communityUrl': '',
    'username': '',
    'password': '',
    'popup_username': '',
    'popup_password': ''
  };
  universal.getDataAuthLithium(api_crawling_id,function (errAuth, data) {
    if (errAuth == 0) {
      callback({"error":"Lithium credentials are not saved"});
    } else {
      authObj.communityUrl = data[0].community_url;
      authObj.username = data[0].username;
      authObj.password = data[0].password;
      authObj.popup_username = data[0].popup_username;
      authObj.popup_password = data[0].popup_password;
      authObj.basicAuthEnabled = data[0].basicAuthEnabled;
      commonFunctions.errorlogger.info(JSON.stringify(authObj));
      var sessionKey=""
      async.series(
        [
          function (cb) {
          //getSessionKey(authObj, function (sessionKeyResult) {
            sessionKey =''// sessionKeyResult;
            cb(null, "1")
         // })
        }, function (cb) {
          getBoards(authObj,sessionKey,function (data) {
            cb(null,data)
          })

        }], function (err, result) {
          if(err){
            callback(err);
          }else {
            callback(null,result[1].response.boards);
          }
        }
      )
    }
  })
}

function getBoards(authObj,seesionKey,callback) {
  var options = { method: 'GET',
    url: authObj.communityUrl+config.get('lithium.restPath')+'/boards/nested',
    qs:
    {
      //'restapi.session_key':seesionKey,
      'restapi.response_format': 'json',
      'restapi.response_style': 'view' }
  };

  if(authObj.basicAuthEnabled)
    {

    options.headers={}
    options.headers={
      authorization: 'Basic '+  new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }

  commonFunctions.errorlogger.info("Options are",options);
  request(options, function (error, response, body) {
     if (error) {
      commonFunctions.errorlogger.error( error)
        return;
      }

   // console.log("body is",body)
    body=JSON.parse(body)

    return callback(body);

  });
}

module.exports={

}
