const router = require('express').Router();
const commonFunctions = require('../../../utils/commonFunctions');
const oauthFunctions = require('../../../routes/admin/oAuthorization');
const communityHelperSettings = require('../communityHelperSettings/communityHelperSettings');

router.get('/oAuthCallback', function (req, res) {
    const code = req.query.code;
    const botId = req.query.state;
    const tenantId = req.headers["tenant-id"];
    communityHelperSettings.getBotSettings(botId, tenantId, function (err, resultData) {
        let platformId = resultData.platform_type_id;
        delete resultData.platform_type_id;
        switch (platformId) {
            case 3: {//salesforce
                oauthFunctions.connectSalesforceLocally(code, resultData.url, resultData.client_id, resultData.client_secret, function (resultConnection) {
                    if (resultConnection != 0) {
                        resultData.accessToken = resultConnection.accessToken;
                        resultData.refreshToken = resultConnection.refreshToken;
                        resultData.instanceURL = resultConnection.instanceUrl;
                        communityHelperSettings.insertUpdateBotSettings(resultData, 1, req ,function (error, resultSave) {
                            if (!error) {
                                res.render('success.ejs', { id: "btnNextBotSalesforce" });
                            }
                            else {
                             //   communityHelperSettings.deleteCommunityHelperSettings(botId, 0, x => { })
                                res.render('fail.ejs', {});
                            }
                        })
                    }
                    else {
                        // communityHelperSettings.deleteCommunityHelperSettings(botId, 0, x => { })
                        res.render('fail.ejs', {});
                    }
                })
                break;
            }
            case 2: { //Lithium
                let url = resultData.url;
                if (resultData.authorization_type.indexOf('htaccess') > -1 && resultData.htaccessUsername && resultData.htaccessPassword){
                    let start = url.indexOf('//')+2;
                    url = url.substring(0,start)+resultData.htaccessUsername+':'+resultData.htaccessPassword+'@'+url.substring(start);
                }
                oauthFunctions.connectLithiumLocally(code, url, resultData.client_id, resultData.client_secret, function (resultConnection) {
                    if (resultConnection != 0 && resultConnection.http_code == 200) {
                        resultData.accessToken = resultConnection.data.access_token;
                        resultData.refreshToken = resultConnection.data.refresh_token;
                        if (tenantId)
                            resultData.tenantId = tenantId
                        resultData.username = resultConnection.data.userId;
                        communityHelperSettings.insertUpdateBotSettings(resultData, 1, req,function (error, resultSave) {
                            if (!error) 
                                res.render('success.ejs', { id: "btnNextBotLithium" });
                            else {
                               // communityHelperSettings.deleteCommunityHelperSettings(botId, 0, x => { })
                                res.render('fail.ejs', {});
                            }
                        })
                    }
                    else {
                        //communityHelperSettings.deleteCommunityHelperSettings(botId, 0, x => { })
                        res.render('fail.ejs', {});
                    }
                })
                break;
            }
            default: {
                //communityHelperSettings.deleteCommunityHelperSettings(botId, 0, x => { })
                res.render('fail.ejs', {})
                break;
            }
        }
    })
})

module.exports = { router };