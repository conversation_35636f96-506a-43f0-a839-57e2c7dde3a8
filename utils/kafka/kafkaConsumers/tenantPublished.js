const kafkaLib = require("../kafka-lib");
const config = require('config');
const kafkaStatusLib = require("../../../utils/kafka-status-page/kafka-lib");

const commonFunctions = require("../../commonFunctions");
const { updateRateLimit } = require("../../../routes/admin/rateLimit");
const { publishDeleteSCEvent } = require("../../../routes/admin/searchClient");
const { tenantSqlConnection } = require("../../../auth/sqlConnection");
const marketplace = require('../../../routes/marketplace/marketplace.service');
const { featurePublisher } = require("../kafkaPublishers/featurePublisher");

const subscribeConsumer = async () => {
  try{
      const tenantConsumer = await kafkaLib.getConsumer({
        groupId: `admin ${config.get("kafkaTopic.publishTenantInfo")}`,
        topic: config.get("kafkaTopic.publishTenantInfo"),
        fromBeginning: false
    })
  await tenantConsumer.run({
      eachMessage: async ({
        topic, parition, message, heartBeat
      }) => {
        try {
          let tenantObj = message.value ? JSON.parse(message.value) : {};
          commonFunctions.errorlogger.info("Tenant published: " + JSON.stringify(tenantObj));
          const { tenantId, searchClients } = tenantObj.tenantInfo;
          if(!!(tenantId && searchClients)){
            console.log(" if(!!(tenantId && searchClients))");
            searchClients.forEach((sc)=>{
              publishDeleteSCEvent(tenantId, sc.uid)
            })
          }
          console.log(" updateRateLimit()");
           updateRateLimit();
          console.log(" featurePublisher(tenantId)");
          await featurePublisher(tenantId);
          console.log(" kafkaStatusLib  ");
          await kafkaStatusLib.publishMessage({ 
            topic:config.get("statusPageService.kafka.publishTenantInfo"),
            messages: [{ 
              value : JSON.stringify(tenantObj)
            }]
          });
          console.log(" tenantSqlConnection");
          await tenantSqlConnection(tenantObj.tenantInfo.tenantId, tenantObj.tenantInfo.databaseName);
          console.log(" tenantSqlConnection");
        } catch (e) {
          commonFunctions.errorlogger.error(e);
        }
      }
    });
  }catch(error){
    if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
      kafkaLib.createTopic(config.get("kafkaTopic.publishTenantInfo"));
    }
  }
}

module.exports = {
    subscribeConsumer
}