const url = require("url");
const setCookie = require('set-cookie-parser');

/*
    App information

    appInfo = {name, id}
*/

const {
    authMiddlewareRoute,
    adminLogin,
    fetchTenant,
    fetchTenantFromSubdomain,
    sendAdminResponse,
    generateTableauToken,
    authTokenMiddlewareRoute,
    getTenantInfoFromUid,
    getTenantInfoFromTenantId,
    getTenantsRateLimitInfo,
    getAccessTokenFromTenantId,
    getLoginDetailsFromAuth,
    getTenantInfoFromTenantHash,
    getSandboxToProdMappingData
} = require('./src/api');

const { setTenantToHeaders, checkIfTenantIdExists } = require('./src/commonFunctions');

const routesToAuth = [/^\/resendOTPMail/,
    /^\/resendEmail/,
    /^\/signUp/,/^\/registerEmail/,
    /^\/admin\/userManagement\/unblockEmail/,
    /^\/admin\/userManagement\/getSignupSettings/, 
    /^\/admin\/userManagement\/getAllUsers/,
    /^\/admin\/userManagement\/updateQuestion/,
    /^\/admin\/userManagement\/editUser/,
    /^\/admin\/userManagement\/deleteRegisteredUser/,
    /^\/admin\/userManagement\/usersConfigurations/,
    /^\/admin\/userManagement\/userMetricsLastSelections/,
    /^\/forgetPassword/,
    /^\/logout/,
    /^\/oauth\/token/,
    /^\/admin\/userManagement\/getSearchUnifySession/,
    /^\/admin\/userManagement\/changePassword/,
    /^\/admin\/userManagement\/getLastLogin/,
    /^\/oauthClients\//,
    /^\/getAutoProvisionToken/,
];

const oAuthSecure = [
    /^\/api/,
    /^\/api\/analytics/,
    /^\/api\/v2\//,
    /^\/api\/v3\//,
    /^\/api\/v2_search/,
    /^\/authorise/,
    /^\/agentHelper\/query-stage/,
    /^\/search\/searchResultByPost/
];

const openUrls = [
    /^\/addons\/verifyJwt/,
    /\/checkToken/,
    /^\/signup/,
    /^\/resources/,
    /^\/analytics\/tableauConnector\/authTableau/,
    /^\/analytics\/tableauConnectorRawApi\/authTableau/,
    /^\/assets/,
    /^\/ext_resources/,
    /^\/favicon.ico$/,
    /^\/html/,
    /^\/mainStaticResource/,
    /^\/node_modules/,
    /^\/readMain/,
    /^\/admin\/searchClient\/readAdHTML/,
    /^\/oauthApiRate[/\W].*/,
    /^\/admin\/contentSources\/subscriptions/,
    /^\/kcs/,
    /^\/api-docs/,
    /^\/api-docs\/analytics/,
    /^\/tenant-connections/,
    /^\/utility\/credentials-cron/,
    /^\/admin\/notifications\/sendReport/,
    /^\/admin\/notifications\/sendNotifications/,
    /^\/agentHelper\/trainingStatus/,
    /\/saml\/logout/,
    /^\/pollApiCheck/
];

const samlUrls = [/\/saml\/login/, /\/saml\/auth/, /^\/admin\/userManagement\/checkSsoEnable/];

const uidUrls= [/^\/chatbot\/api\/chat_client_conf/,/^\/chatbot\/api\/languages/,/\/getPageRatingData/,/^\/agentHelper\/query/, /^\/agentHelper\/case-sentiments/]



const tenantUrls = [/\/updateCronModel/, 
    /^\/admin\/cron\//, 
    /^\/synonyms\/dymTrainingStatus/, 
    /^\/admin\/searchClient\/getSearchFieldSettings/,
    /^\/admin\/searchClient\/getContentSourceFields/,
    /^\/stopwords\/read/,
    /^\/synonyms\/suggestion/,
    /^\/agentHelper/,
    /\/mlConversion/,
    /^\/getContentAuthData/
];

const hashUrls =[/\/contentSources\/subscriptions/];


exports.authenticate = (appInfo) => {
    return async function(req, res, next){
        req.body.appId = appInfo.id;
        const urlObject = url.parse(req.url, true);

        if(!req.headers['csrf-token']){
            const splitCookie = setCookie.splitCookiesString(req.headers.cookie);
            const cookies = setCookie.parse(splitCookie);
            cookies.forEach((item) => {
                if (item._csrf) {
                    req.headers['csrf-token'] = item._csrf;
                } else if (item.name === '_csrf') {
                    req.headers['csrf-token'] = cookies[0].value;
                }
            });
        }

    //Check for open gloabally
    let isRedirectToAuth = false;
    for (let i = 0; i < routesToAuth.length; i++) {
        const p = routesToAuth[i];
        if (p.test(urlObject.path) && !urlObject.path.includes('saml')) {
            isRedirectToAuth = true;
            break;
        }
    }

    let isOpenUrl = false
    for (let i = 0; i < openUrls.length; i++) {
        const p = openUrls[i];
        if (p.test(urlObject.path)) {
            isOpenUrl = true;
            break;
        }
    }

    let isSamlUrl = false
    for (let i = 0; i < samlUrls.length; i++) {
        const p = samlUrls[i];
        if (p.test(urlObject.path)) {
            isSamlUrl = true;
            break;
        }
    }
    let isOAuthSecure = false;
    for (let i = 0; i < oAuthSecure.length; i++) {
        if (oAuthSecure[i].test(urlObject.path)) {
            isOAuthSecure = true;
            break;
        }
    }

    let isTenantUrl = false
    for (let i = 0; i < tenantUrls.length; i++) {
        const p = tenantUrls[i];
        if (p.test(urlObject.path)) {
            isTenantUrl = true;
            break;
        }
    }

    let isUidUrl = false
    for (let i = 0; i < uidUrls.length; i++) {
        const p = uidUrls[i];
        if (p.test(urlObject.path)) {
            isUidUrl = true;
            break;
        }
    }

    let isHashUrl = false
    for (let i = 0; i < hashUrls.length; i++) {
        const p = hashUrls[i];
        if (p.test(urlObject.path)) {
            isHashUrl = true;
            break;
        }
    }
    
    if(isOpenUrl){
        if (/^\/analytics\/tableauConnector\/authTableau/.test(urlObject.path) || /^\/analytics\/tableauConnectorRawApi\/authTableau/.test(urlObject.path)) {
            generateTableauToken(req, res, (error, body) => {
                if(error){
                    next(error);
                }else{
                    next();
                }
            });
        }
        else next();
    } else if (/\/login/.test(urlObject.path) || /\/validate/.test(urlObject.path)) {
        const url = req.url;
        adminLogin(req, res, url ,(err, body) => {
            if(err){
                res.send(err);
            }else{
                if(appInfo.name !== 'admin'){
                    sendAdminResponse(req, res);
                }else{
                    next();
                }
            }
        });
    } else if(isRedirectToAuth) {
        const url = req.url;
        authMiddlewareRoute(req, url, (error, body) => {
            if(error){
                res.send(error);
            }else{
                if(url === '/logout'){
                    res.clearCookie("CSRF-Token");
                    res.clearCookie("_csrf");
                    res.clearCookie("connect.admin_sid");
                }
                res.send(body);
            }
        });
    } else if(isSamlUrl){
        fetchTenantFromSubdomain(req, (error,body)=>{
            if(error){
                next(error);
            }else{
                next();
            }
        })
    } else if (isOAuthSecure) {
        req.isoAuthSec = true;
        authTokenMiddlewareRoute(req, res, (error, body) => {
            if(error){
                next(error);
            }else{
                next();
            }
        })
    } else if(isTenantUrl && checkIfTenantIdExists(req)){
        const tenantId = req.headers['tenant-id'] || req.query['tenant-id'] || req.body['tenant-id'];
        const data = await getTenantInfoFromTenantId(tenantId)
        setTenantToHeaders(req, data[0],() => {
            next();
        });
    } else if(isUidUrl){
        const uid = req.headers['uid'] || req.query['uid'] || req.body['uid'];
        const data = await getTenantInfoFromUid(uid)
        setTenantToHeaders(req, data,() => {
            next();
        });
    } else if(isHashUrl){
        const tenantHash = req.headers['tenant-hash'] || req.query['tenant-hash'] || req.body['tenant-hash'];
        const data = await getTenantInfoFromTenantHash(tenantHash);
        setTenantToHeaders(req, data,() => {
            next();
        });
    } else {
        fetchTenant(req, (error, body)=>{
            if(error){
                next(error)
            }else{
                next();
            }
        });
    }
    }
}

exports.fetchTenantInfoFromUid = async (uid) => {
    return getTenantInfoFromUid(uid);
};

exports.getTenantInfoFromTenantId = async (tenantId) => {
    return getTenantInfoFromTenantId(tenantId);  
};

exports.getTenantsRateLimitInfo = async () => {
    return getTenantsRateLimitInfo();
}

exports.getAccessTokenFromTenantId = async (tenantId) => {
    return getAccessTokenFromTenantId(tenantId);
}

exports.getLoginDetailsFromAuth = async (email, accessToken) => {
    return getLoginDetailsFromAuth(email, accessToken);
}

exports.getSandboxToProdMappingData = async (tenantId) => {
    return getSandboxToProdMappingData(tenantId);
}
