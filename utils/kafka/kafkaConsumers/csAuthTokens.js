var async = require('async');
const kafkaLib = require("../kafka-lib");
const commonFunctions = require("../../commonFunctions");
const { getTenantInfoFromTenantId } = require('auth-middleware');
const { tenantSqlConnection  } = require('../../../auth/sqlConnection');

const subscribeConsumer = async () => {
    try{
        const csConsumer = await kafkaLib.getConsumer({
            groupId: `admin ${config.get("kafkaTopic.publishCSTokensTopic")}`,
            topic: config.get("kafkaTopic.publishCSTokensTopic"),
            fromBeginning: false
        })
        await csConsumer.run({
            eachMessage: async ({
              message
            }) => {
                try {
                    let data = message.value ? JSON.parse(message.value) : {};
                    if(data.tenantId){
                        const tenantInfo = await getTenantInfoFromTenantId(data.tenantId);
                        await tenantSqlConnection(tenantInfo[0].tenantId, tenantInfo[0].database_name);
                        data.authorization = {
                            accessToken: data.accessToken,
                            refreshToken: data.refreshToken
                        };
                        updateTokens(data, function (err, res) {
                            if (err)
                                commonFunctions.errorlogger.error(`Error while updating Auth-Tokens for CS ${err}`);
                            else 
                                commonFunctions.errorlogger.info(`Auth-Token updated for CS ID: ${res} through search kafka`);
                        });
                    } else {
                        commonFunctions.errorlogger.info(`Kafka message not processed | Data: ${data}`);
                    }
                } catch (e) {
                    console.log(e)
                }
            }
        });
    } catch(error) {
        if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
            kafkaLib.createTopic(config.get("kafkaTopic.publishCSTokensTopic"));
        }
    }
    try {
        const csAllTenantConsumer = await kafkaLib.getConsumer({
            groupId: `admin ${config.get("kafkaTopic.publishAllCSInfo")}`,
            topic: config.get("kafkaTopic.publishAllCSInfo"),
            fromBeginning: false
        })
        await csAllTenantConsumer.run({
            eachMessage: async ({
              message
            }) => {
                try {
                    const csArray = JSON.parse(message.value || '[]');
                    if (csArray && csArray.length) {
                        const tenantInfo = await getTenantInfoFromTenantId(csArray[0].tenantId);
                        if (tenantInfo && tenantInfo.length) {
                            await tenantSqlConnection(tenantInfo[0].tenantId, tenantInfo[0].database_name);
                            commonFunctions.errorlogger.info(`Migration for TenantID: ${tenantInfo[0].tenantId} DB:${tenantInfo[0].database_name}`);
                            commonFunctions.errorlogger.info(`CS Data received for ${csArray.map(c => c.contentSourceId)}`);
                        }
                    }
                    let asyncTasks = [];
                    csArray.forEach((cs) => {
                        asyncTasks.push(updateTokens.bind(null, cs));
                    });
                    async.series(asyncTasks, function (err, data) {
                        if (err)
                            commonFunctions.errorlogger.error(`Error while migrating cs data ${err}`);
                        else 
                            commonFunctions.errorlogger.info(`Migration completed for CS ID: ${data}`);
                    });
                } catch (e) {
                    commonFunctions.errorlogger.error(`Error while migrating CS-tenant data | Data: ${e}`);
                }
            }
        });
    } catch(error) {
        if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
            kafkaLib.createTopic(config.get("kafkaTopic.publishAllCSInfo"));
        }
    }
}

const updateTokens = function (cs, callback) {
    if(cs.tenantId && cs.contentSourceId && cs.authorization.accessToken){
        const authorization = {};
        commonFunctions.getCSAuthId(cs.contentSourceId, {headers: {'tenant-id': cs.tenantId }}, function (err, id){
            if (err || !id) {
                commonFunctions.errorlogger.error("Error while getting csAuth id for : ", cs.contentSourceId);
                callback(null, cs.contentSourceId);
            } else {
                authorization.id = id;
                authorization.content_source_id = cs.contentSourceId;
                if ( cs.authorization.refreshToken ) authorization.refreshToken = cs.authorization.refreshToken;
                authorization.accessToken = cs.authorization.accessToken;
                commonFunctions.insertUpdateAuthorization(authorization, {headers: {'tenant-id': cs.tenantId }} ,function (errAsync, rows) {
                    if (errAsync) {
                        commonFunctions.errorlogger.error("Error while updating Auth-Tokens through kafka: ",errAsync);
                    }
                    else {
                        commonFunctions.errorlogger.info('Updated Auth-Tokens for : ', authorization.content_source_id, ' through Kafka' );
                    }
                    callback(null, cs.contentSourceId);
                })
            };
        });
    } else {
        commonFunctions.errorlogger.error(`Error while consuming kafka for cs data for | Data: tenant: ${cs.tenantId}  id:${cs.contentSourceId} accessToken:${cs.authorization.accessToken}`);
        callback(null, cs.contentSourceId);
    }
}

module.exports = {
    subscribeConsumer
}
