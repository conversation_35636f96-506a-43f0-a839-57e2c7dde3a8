var express = require('express');
var router = express.Router();
var moment = require('moment');
var appVariables = require('./../constants/appVariables');

function createMessage(email, body, r, type) {
    var msg = "";
    // msg += "---BANNER--- <br />";
    // msg += "Hi "+email+"<br />";
    // msg += "Please find below the report for search queries subscribed on keyword"+r.searchkeywords+"<br />";
    // msg += `<table><tr><th>Date</th><th>Search Query</th></tr>`;
    // body.hits.hits.forEach(el=>{
    //     console.log("el._source",el._source);
    //     msg += "<tr><td>"+moment(+el._source.search_date).format("YYYY-MM-DD HH:MM:ss")+"</td><td>"+el._source.text_entered+"</td></tr>";
    // });
    // msg += "</table>";
    // msg += `From, <br /><EMAIL>`;
    var template = `
    <table align="center"; width="480" cellspacing="0" cellpadding="0" style="margin: 0 auto; border: 1px solid #dcdcdc; width: 480px; font-family: Verdana, Geneva, sans-serif;color: #666666;">
            <tr>
                <td style="text-align: center;padding: 15px;">
                    <img src="${config.get('adminURL')}/resources/Assets/emailLogo.png" alt="logo.png">
                </td>
            </tr>
            <tr>
                <td style="text-align: center; padding: 15px; color: #FC8102; background-color: #000;font-size: 25px;">
                    ${type} Email Alert
                </td>
            </tr>
            <tr>
                <td style="padding: 15px;">
                Hi ${email}, <br /><br/>
                Please find below the report for search queries subscribed on keyword ${r.searchkeywords}
                </td>
            </tr>
            <tr><td style="padding: 10px 25px;">
                <table align="center" width="100%" style="margin: 0 auto;width: 100%;border: 1px solid #e2e2e2;border-collapse:collapse !important;">
                    <tr>
                        <td style="padding: 10px 15px; text-align: left;font-weight: bold; background-color: #EDEDEE">Date</td>
                        <td style="padding: 10px 15px; text-align: left;font-weight: bold; background-color: #EDEDEE">Search Query</td>
                    </tr>`;
    body.forEach(el => {
        // console.log("el._source",el._source);
        template += "<tr><td style='padding: 9px 15px; width: 55%; border: 1px solid #e2e2e2;'>" + moment(el.search_date).format("YYYY-MM-DD HH:MM:ss") + "</td><td style='padding: 9px 15px; width: 45%; border: 1px solid #e2e2e2;'>" + el.text_entered + "</td></tr>";
    });
    template += `</table>
                </td>
            </tr>
        <tr style="padding: 8px 0;color: #ffff;background-color: #000;font-size: 12px;width:100%;text-align: center;">
            <td colspan="2" style="padding: 8px;">
                Powered by © ${new Date().getFullYear()}
                <a href="https://www.grazitti.com/" target="_blank" style="color: #fff;text-decoration: none;">Grazitti Interactive</a>. All rights reserved.
            </td>
        </tr>
    </table>`
    return template;
}

function demoRequestTemplate(userDetails) {
    let template =`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            * {
                box-sizing: border-box;
                font-family: 'Montserrat',sans-serif !important;
            }
            .template-su-logo{
                height: 40px;
                width: 220px;
            }
            .template-header-section {
                border-bottom: 1px solid #E0E8EC;
                padding: 30px 50px ;
            }
            .template-title{
                font: normal normal normal 18px/22px Montserrat;
                color: #707070;
            }
            .template-body-section{
                padding: 25px 50px;
            }
            .request-details {
                margin: 20px 0;
            }
            .request-details .request-details-heading, .request-user-email, .account-details .account-heading {
                font: normal normal normal 12px/15px Montserrat;
                color: #707070;
            }
            .request-details .request-username, .account-details .account-name {
                font: normal normal medium 14px/18px Montserrat;
                color: #000000;
                padding: 2px 0;
            }
        </style>
    </head>
    <body>
        <div class="main-section">
            <div class="template-header-section">
                <img src="${config.get('adminURL')}/resources/Assets/su-logo-black.png" class="template-su-logo" alt="su-logo">
            </div>
            <div class="template-body-section">
                <div class="template-title">Demo requested for ${userDetails.app}</div>
                <div class="request-details">
                    <div class="request-details-heading">Demo requested by details:</div>
                    <div class="request-username">${userDetails.name}</div>
                    <div class="request-user-email">${userDetails.email}</div>
                </div>
                <div class="account-details">
                    <div class="account-heading">Account</div>
                    <div class="account-name">${userDetails.account}</div>
                </div>
            </div>
        </div>
    </body>
    </html>
    `;

    return template;
}

function createVersionMessage(name, currentVersion) {

    var template = `<html>
        <header style="width : 480px; margin: 0 auto; border: 1px solid #dcdcdc"; ">
        <div style="background-color:#fff; padding: 10px; text-align: center;">
            <div id="banner" style="width: 110px; display: inline-block">
                <img src="${config.get('adminURL')}/resources/Assets/emailLogo.png" style="width: 100%;">
            </div>
        </div>
    </header>
            <body>
                <div style="width:100%;color: #FC8102; font-family: Verdana, Geneva, sans-serif;">
                    <div style="width : 480px; margin: 0 auto; border: 1px solid #dcdcdc"; ">
                        <div style="padding: 10px; background-color: #000;">
                            <div style="font-size: 16px; padding: 3px; font-weight: bold;">
                               ${currentVersion.versionName} (${currentVersion.versionId}) 
                          </div>
                        </div>
                        <section>
                            <div style="padding:15px; color: #6e7681; font-size: 14px;">
                               <div> Hi ${name}, </div>
                        <br />  A new version has been released! 
                        <br />
                                <div style="font-weight: bold; font-style: oblique;font-size: 16px;margin: 10px 1px;"> FEATURES
                            <span style="background: none repeat scroll 0 0 #ccc; top: -3px; display: inline-block; height: 1px; position: relative; width: 350px;">
                                    </span>
                                </div>
                                <div style="padding: 1px; margin: 0.3em;">
                                    <div style="color:#FC8102; width: 100%;">
                                        1. Manage synonyms:
                            </div>
                                    <div>
                                        Ability to create a set of words with their abbreviated forms and the words having similar meanings. This will search the
                                        whole set even if one of the word in the set is queried!
                                <br /> Click on
                                <a href="${config.get('adminURL')}/dashboard/manage-synonyms" style="text-decoration:none">'Manage Synonyms'</a> tab from sidebar to define synonyms.
                            </div>
                                </div>
                                <br/>
                                <div style="padding: 1px; margin: 0.3em;">
                                    <div style="color:#FC8102;">
                                        2. Historical reporting
                                <span style="color: #6e7681; font-size: 11px;">(for specified time frame)</span>:
                            </div>
                             <div>
                                The historical reporting works for more that top 10 results and is now based on date range. Click 'Download CSV' to get all
                                records int the specified date range and not just top 10.
                            </div>
                                </div>
                                <br/>
                                <div style="padding: 1px; margin: 0.3em;">
                                    <div style="color:#FC8102;">
                                        3. New graphic conversion funnel:
                            </div>
                            <div>
                                    Simple analytical funnel graph which gives an overview of total searches, searches with results and number of them converted.
                            </div>
                                </div>
                                <!--${currentVersion.Description}-->
                    </div>
                        </section>
                        <section>
                            <div style="background-color: #F8F8F8; text-align: center;font-size: 17px; padding: 15px">
                                <div style="margin-bottom: 10px;">You can check release notes in detail</div>
                                <span style="background-color: #FC8102; padding: 7px;">
                                    <a href="${config.get('adminURL')}/dashboard/releaseNotes" style="color: #fff; text-decoration:none">Go To Release Note</a>
                                </span>
                            </div>
                        </section>
                <div id="footer" style="padding: 8px 0;color: #ffff;background-color: #000;font-size: 11px;text-align: center;">
                        Powered by © ${new Date().getFullYear()}
                    <a href="https://www.grazitti.com/" target="_blank" style="color: #fff;text-decoration: none;">Grazitti Interactive</a>. All rights reserved.
                </div>
                         </div>
                         </div>
                        </body>
                        </html>`
    return template;
}


// function analyticsTemplate(type, email, range, interMediateTemplate) {
//     var msg = "";
//     var template = ` <table align="center"; width="550" cellspacing="0" cellpadding="0" style="margin: 0 auto; border: 1px solid #dcdcdc; width: 550px; font-family: Verdana, Geneva, sans-serif; color: #666666; ">
//                 <tr>
//                     <td style="text-align: center;padding: 15px;">
//                         <img src="${config.get('adminURL')}/resources/Assets/emailLogo.png" alt="logo.png">
//                     </td>
//                 </tr>
//                 <tr>
//                     <td style="text-align: center; padding: 8px; color: #FC8102; background-color: #000; font-size: 20px;text-transform: capitalize;">
//                         ${type} Analytics Report
//               </td>
//                 </tr>
//                 <tr>
//                     <p>Data Period</p>
//                     <td style="text-align: center; padding: 8px; color: #fff; background-color: #000; font-size: 15px;">
//                         Data Period </br> ${(range.from)} To ${(range.to)}
//                     </td>
//                 </tr>
//                 <tr>
//                     <td style="padding: 15px;">
//                         Hi, ${email} <br /><br />
//                         Please find below Analytics reports from Admin Panel </td>
//                 </tr>
//          ${interMediateTemplate}
//         <tr style="padding: 8px 0;color: #ffff;background-color: #000;font-size: 12px;width:100%;text-align: center;">
//             <td colspan="2" style="padding: 8px;">
//                 Powered by © 2024
//                 <a href="https://www.grazitti.com/" target="_blank" style="color: #fff;text-decoration: none;">Grazitti Interactive</a>. All rights reserved.
//             </td>
//         </tr>
//     </table>`;
//     return template;
// }
function analyticsTemplate(type, email, range, interMediateTemplate) {
    var msg = "";
    var template = `<!DOCTYPE html>
    <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
        xmlns:o="urn:schemas-microsoft-com:office:office">
    
    <head>
        <meta charset="utf-8"> <!-- utf-8 works for most cases -->
        <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
        <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
        <title>User Registration</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
    
        <!-- Web Font / @font-face : BEGIN -->
        <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
    
        <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
        <!--[if mso]>
            <style>
                * {
                    font-family: 'Montserrat',sans-serif !important;
                }
            </style>
        <![endif]-->
    
        <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. More on that here: http://stylecampaign.com/blog/2015/02/webfont-support-in-email/ -->
        <!-- [if !mso] -->
        <!-- insert web font reference, eg: <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'> -->
        <link
            href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
            rel="stylesheet">
        <!-- [endif] -->
    
        <!-- Web Font / @font-face : END -->
    
        <!-- CSS Reset -->
        <style>
            /* What it does: Remove spaces around the email design added by some email clients. */
            /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
            html,
            body {
                margin: 0 auto !important;
                padding: 0 !important;
                height: 100% !important;
                width: 100% !important;
                font-family: 'Montserrat',sans-serif !important;
            }
    
            /* What it does: Stops email clients resizing small text. */
            * {
                -ms-text-size-adjust: 100%;
                -webkit-text-size-adjust: 100%;
            }
    
            /* What it does: Centers email on Android 4.4 */
            div[style*="margin: 16px 0"] {
                margin: 0 !important;
            }
    
            /* What it does: Stops Outlook from adding extra spacing to tables. */
            table,
            td {
                mso-table-lspace: 0pt !important;
                mso-table-rspace: 0pt !important;
            }
    
            /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
            table {
                border-spacing: 0 !important;
                border-collapse: collapse !important;
                table-layout: fixed !important;
                margin: 0 auto !important;
            }
    
            table table table {
                table-layout: auto;
            }
    
            /* What it does: Uses a better rendering method when resizing images in IE. */
            img {
                -ms-interpolation-mode: bicubic;
            }
    
            /* What it does: A work-around for email clients meddling in triggered links. */
            *[x-apple-data-detectors],
            /* iOS */
            .x-gmail-data-detectors,
            /* Gmail */
            .x-gmail-data-detectors *,
            .aBn {
                border-bottom: 0 !important;
                cursor: default !important;
                color: inherit !important;
                text-decoration: none !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
            }
    
            /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
            .a6S {
                display: none !important;
                opacity: 0.01 !important;
            }
            
            td {
                width: 100%;
            }
    
            /* If the above doesn't work, add a .g-img class to any image in question. */
            img.g-img+div {
                display: none !important;
            }
    
            /* What it does: Prevents underlining the button text in Windows 10 */
            .button-link {
                text-decoration: none !important;
            }
    
            /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
            /* Create one of these media queries for each additional viewport size you'd like to fix */
            /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
            @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
    
                /* iPhone 6 and 6+ */
                .email-container {
                    min-width: 375px !important;
                }
            }
            .Email_content:after{
                content:'\A';
                position:absolute;
                background:black;
                top:0; bottom:0;
                left:0; 
                width:50%;

            }
        </style>
    
    </head>
    
    <body width="100%"
        style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
        <center style="width: 100%; class="Email_content">
    
            <!--
                Set the email width. Defined in two places:
                1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
                2. MSO tags for Desktop Windows Outlook enforce a 600px width.
            -->
            <div style="max-width: 600px; margin: auto;" class="email-container">
                <!--[if mso]>
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
                <tr>
                <td>
                <![endif]-->
    
                <!-- Email Header : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
                    <tr>
                        <td style="padding: 20px 0; text-align: left">
                            <!-- IF logo.src -->
                            <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                            <!-- ELSE -->
                            &nbsp;
                            <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                                style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <!-- ENDIF logo.src -->
                        </td>
                    </tr>
                </table>
                <!-- Email Header : END -->
    
                <!-- Email Body : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px; background: white;">

                    <!-- Hero Image, Flush : BEGIN -->
                    <tr bgcolor="#F6F7FB">
                        <td
                            style="padding: 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <h3
                                style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500;">
                                Hi ${email},</h3>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;">
                            <img src="${config.get('adminURL')}/resources/Assets/Analytic_Reports.png" width="300" height="300" border="0" align="center"
                                style="width: 300px; height: 300px; max-width: 300px;padding-left:4px; height: auto; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;"
                                class="g-img">
    
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;height:30px;">
                        </td>
                    </tr>
    
                    <!-- Hero Image, Flush : END -->
    
                    <!-- 1 Column Text + Button : BEGIN -->
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                            border-radius: 0% 0% 100% 100%;
                            border-bottom: 35px solid #F6F7FB;
                            border-collapse: separate !important;">
                                <tr >
                                    <td bgcolor="#F6F7FB" style="padding: 20px 0px 10px 0px; font-family: 'Montserrat', sans-serif;;font-size: 24px; font-weight: 600; line-height: 20px;text-align: center !important; color: #43425d;">
                                        <p style="margin: 0;">
                                            Analytics Report
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                        <td style="text-align: center; padding: 3px; color:#43425D; background: #F6F7FB 0% 0% no-repeat padding-box;
                                        ; font-size: 12px;">
                                        Data Period ${(range.from)} To ${(range.to)}
                                    </td>
                                    </tr>
                                <tr>
                            </table>
                        </td>
                    </tr>
                                ${interMediateTemplate}
                            
                              </tr>
                                <tr>
                                    <td bgcolor="#F6F7FB">
                                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                    <tr>
                                        <td style="text-align:center;font-size: 18px;font-weight: 500;">
                                            <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                                                Feel free to reach out to the <br /> SearchUnify team for any
                                                further support.
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="padding: 8px;">
                                        <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                                            <a href='https://community.searchunify.com/support/' target="_blank"
                                                style="/* text-decoration: none; */font-family: 'Montserrat', sans-serif;font-size: 13px;color: #6da7fb;">
                                                https://community.searchunify.com/support/
                                            </a>
                                        </td>
                                    </tr>
  
                                </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                    &copy; ${new Date().getFullYear()} SearchUnify. All rights reserved
                                                </td>
                                            </tr>
                                            <tr>
                                                <td
                                                    style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif;; font-size: 12px; color: #a6a6a6;text-align: center">
                                                    Email: <a style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:<EMAIL>"><EMAIL></a>, <a
                                                        style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
    
                            </table>
                        </td>
                    </tr>
                    <!-- 1 Column Text + Button : END -->
    
                </table>
                <!-- Email Body : END -->
    
                <!-- Email Footer : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
                    style="max-width: 680px;">
                    <tr>
                        <td style="padding: 10px 0px 10px 0px;">
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                                    <td style="width: 25px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/contact-us/">Contact </a>|
                                    </td>
                                    <td style="width: 40px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/privacy-policy/">Privacy
                                            Policy </a>|
                                    </td>
                                    <td style="width: 68px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                            Conditions</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td>
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.facebook.com/SearchUnify/"><img
                                                src="${config.get('adminURL')}/resources/Assets/analytics_fb.png" style="width: 30px; margin-right: 15px;"></a>
                                    </td>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.linkedin.com/showcase/25048226/"><img
                                            src="${config.get('adminURL')}/resources/Assets/analytics_linkedin.png" style="width: 30px; margin-right: 15px;"></a>
                                    </td>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://twitter.com/SearchUnify"><img
                                            src="${config.get('adminURL')}/resources/Assets/twitter.png" style="width: 30px; margin-right: 15px;"></a>
                                    </td>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.youtube.com/channel/UCHQdHTFzWRKj5xg1nFoZPTg"><img
                                            src="${config.get('adminURL')}/resources/Assets/analytics_youtube.png" style="width: 30px;"></a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td
                            style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                            <br><br>
    
                        </td>
                    </tr>
                </table>
                <!-- Email Footer : END -->
    
                <!--[if mso]>
                </td>
                </tr>
                </table>
                <![endif]-->
            </div>
    
        </center>
    </body>
    </html>
`;
    return template;
}

function analyticsEmailTemplate(sessionInfo, email, range, interMediateTemplate) {
    var msg = "";
    var template = `<link
    href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
    rel="stylesheet">
<style>
    /* What it does: Remove spaces around the email design added by some email clients. */
    /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
    html,
    body {
        margin: 0 auto !important;
        padding: 0 !important;
        height: 100% !important;
        width: 100% !important;
    }

    /* What it does: Stops email clients resizing small text. */
    * {
        -ms-text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%;
    }

    /* What it does: Centers email on Android 4.4 */
    div[style*="margin: 16px 0"] {
        margin: 0 !important;
    }

    /* What it does: Stops Outlook from adding extra spacing to tables. */
    table,
    td {
        mso-table-lspace: 0pt !important;
        mso-table-rspace: 0pt !important;
    }

    /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
    table {
        border-spacing: 0 !important;
        border-collapse: collapse !important;
        table-layout: fixed !important;
        margin: 0 auto !important;
    }

    table table table {
        table-layout: auto;
    }
    img {
        -ms-interpolation-mode: bicubic;
    }
    
    .x-gmail-data-detectors *,
    .aBn {
        border-bottom: 0 !important;
        cursor: default !important;
        color: inherit !important;
        text-decoration: none !important;
        font-size: inherit !important;
        font-family: inherit !important;
        font-weight: inherit !important;
        line-height: inherit !important;
    }

    /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
    .a6S {
        display: none !important;
        opacity: 0.01 !important;
    }
    img.g-img+div {
        display: none !important;
    }
    .button-link {
        text-decoration: none !important;
    }

    @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
        .email-container {
            min-width: 375px !important;
        }
    }
</style>
</head>
<body width="100%"
style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
<center style="width: 100%; ">
    <div style="max-width: 600px; margin: auto;" class="email-container">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
            style="max-width: 600px;">
            <tr>
                <td style="padding: 20px 0; text-align: left">
                    &nbsp;
                    <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                        style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                </td>
            </tr>
        </table>
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
            style="max-width: 600px;">
            <tr bgcolor="#F6F7FB">
                <td
                    style="padding: 20px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                    <h2 style = "text-align: center;line-height: 30px; font-family: 'Montserrat', sans-serif;text-transform: capitalize;">${sessionInfo.label} Report</h2>
                    <hr>
                    <h3 style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500; margin-top: 20px;padding-left: 25px;">
                        Hi ${email},</h3>
                </td>
            </tr>
            <tr>
            <td bgcolor="#ffffff">
                <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                border-radius: 0% 0% 100% 100%;
                border-bottom: 60px solid #F6F7FB;
                border-collapse: separate !important;">
                    <tr>
                        <td bgcolor="#F6F7FB" style="font-weight: 500;color: #43425d;font-size: 14px;">
                            <div style="margin: 0px 30px 0px 30px;border-radius: 5px; display: ${(range.from && range.to && sessionInfo.searchclient && sessionInfo.internalUser) ? 'block' : 'none'}">
                                <ol type="1" style="text-align: left;font-family: 'Montserrat', sans-serif;font-size: 14px;color:#43425D;">
                                    <li style='display: inline;'>The report you have requested is ready</li>
                                </ol>
                                <ol type="1" style="text-align: left;font-family: 'Montserrat', sans-serif;font-size: 14px;color:#43425D;">
                                    <li style='display: inline;'>Download Period</li>
                                    <li style='display: inline;padding-left:70px'>${range.from} ${sessionInfo.between} ${range.to}</li>
                                </ol>
                                <ol type="1" style="text-align: left;font-family: 'Montserrat', sans-serif;font-size: 14px;color:#43425D;">
                                    <li style='display: inline;'>Search Client</li>
                                    <li style='display: inline;padding-left: 95px;'>${sessionInfo.searchclient ? sessionInfo.searchclient: ''}</li>
                                </ol>
                                <ol type="1" style="text-align: left;font-family: 'Montserrat', sans-serif;font-size: 14px;color:#43425D;">
                                    <li style='display: inline;'>User</li>
                                    <li style='display: inline;padding-left: 149px;text-transform: capitalize'> ${sessionInfo.internalUser ? sessionInfo.internalUser:''}</li>
                                </ol>
                            </div>
                        </td>                                           
                    </tr>
                </table>
            </td>
        </tr>
            <tr>
                <td bgcolor="#ffffff">
                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                        <tr bgcolor="#ffffff">
                            <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                                &nbsp;
                                
                            </td>
                        </tr>
                        <tr bgcolor="#ffffff">
                            <td
                                style="padding: 9px 40px 40px 40px;font-family: 'Montserrat', sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 400;">
                                <p style="margin: 0 0 10px 0; font-family: 'Montserrat', sans-serif;">Click Below To Download Report</p>      
                                <a href='${interMediateTemplate}' alt="Download"><img src="${config.get('adminURL')}/resources/Assets/download-report.png"></a>
                            </td>
                        </tr>
                        <tr>
                            <td bgcolor="#F6F7FB">
                                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                    <tr>
                                        <td style="text-align:center">
                                            <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                                                Feel free to reach out to the <br /> SearchUnify team for any
                                                further support .
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="padding: 8px;">
                                        <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                                            <a href='https://help.searchunify.com/portal/home' target="_blank"
                                                style="text-decoration: none;font-family: 'Montserrat', sans-serif;font-size: 12px;color:#43425D;">
                                                SearchUnify Support
                                            </a>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                    <tr>
                                        <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                            &copy; ${new Date().getFullYear()}. All rights reserved
                                        </td>
                                    </tr>
                                    <tr>
                                        <td
                                            style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif; font-size: 12px; text-align: center; font-weight: 600;">
                                            Email: <a style="text-decoration: underline;"
                                                href="mailto:<EMAIL>"><EMAIL></a>,<a
                                                style="text-decoration: underline;"
                                                href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                    </table>
                </td>
            </tr>

        </table>
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
            style="max-width: 680px;">
            <tr>
                <td style="padding: 10px 0px 10px 0px;">
                    <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                        <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                            <td style="width: 25px !important;padding:20px 0 0 0;">
                                <a style="text-decoration: none;font-weight: 600;"
                                    href="mailto:<EMAIL>">Contact </a>
                            </td>
                            <td style="width: 40px !important;padding:20px 0 0 0;">
                                <a style="text-decoration: none;font-weight: 600;"
                                    href="https://www.searchunify.com/privacy-policy/">Privacy
                                    Policy </a>
                            </td>
                            <td style="width: 68px !important;padding:20px 0 0 0;">
                                <a style="text-decoration: none;font-weight: 600;"
                                    href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                    Conditions</a>
                            </td>
                        </tr>
                    </table>
                </td>
                <td>
                    <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                        <tr>
                            <td style="text-align:right;padding:20px 0 0 0; ">
                                <a href="https://www.facebook.com/SearchUnify/"><img
                                        src="${config.get('adminURL')}/resources/Assets/analytics_fb.png" style="width: 30px; margin-right: 15px;"></a>
                            </td>
                            <td style="text-align:right;padding:20px 0 0 0; ">
                                <a href="https://www.linkedin.com/showcase/25048226/"><img
                                    src="${config.get('adminURL')}/resources/Assets/analytics_linkedin.png" style="width: 30px; margin-right: 15px;"></a>
                            </td>
                            <td style="text-align:right;padding:20px 0 0 0; ">
                                <a href="https://twitter.com/SearchUnify"><img
                                    src="${config.get('adminURL')}/resources/Assets/twitter.png" style="width: 30px; margin-right: 15px;"></a>
                            </td>
                            <td style="text-align:right;padding:20px 0 0 0; ">
                                <a href="https://www.youtube.com/channel/UCHQdHTFzWRKj5xg1nFoZPTg"><img
                                    src="${config.get('adminURL')}/resources/Assets/analytics_youtube.png" style="width: 30px;"></a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td
                    style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                    <br><br>

                </td>
            </tr>
        </table>
    </div>

</center>
</body>`;
    return template;
}


function analyticsIntermediateTemplate(type, email, range, name, body) {
    // body = [[{Query:'Lorem Ipsum is simply dummy text of the printingtypesetting industry. Lorem Ipsum',Count:53},{Query:'kjdggyk',Count:28},{Query:'kjdggyk',Count:29}],
    // [{Query:'rtetretfgd',Count:25},{Query:'kjdggyk',Count:30},{Query:'kjdggyk',Count:75}],
    // [{Url:'fggLorem Ipsum is simply dummy text of the printingtypesetting industry. Lorem Ipsumggsfg',Subject:'sdfgsfgsfg',Clicks:52}],
    // [{Url:'sgfdggsfg',Subject:'erteytgsg',Clicks:31}],
    // [{Url:'sgsdsgfgds',Subject:'bsfdsfgsg',Clicks:98}]]
    var template = `
    </div>
    <div style="max-width: 800px; margin: auto; background-color: #f5f5f5;">
    <div style="width: 600px; margin: auto;">
    <table>
    <tr style="margin: 10px 0;background-color: white;"> 

    <td style="padding: 30px 0px 0px 0px;">
        <table align="center" cellspacing="0" cellpadding="0" width="100%" style="margin: 0 auto;width: 100%;">
        <tr>
        <td style="font-size: 12px; color: #FFFFFF; padding: 6px 10px; background-color: #56C5FF; width: 50%; font-weight: 500;"> Search Client Name: ${name}
        </td>
        <td style="font-size: 12px; color: #FFFFFF; padding: 10px 10px; width: 50%;">
        </td>
        </td>
        </tr>
        </table>
        </td>
        </tr>
    </table>
        <tr style=" background-color: white; "><td style="padding: 10px 0px;">
        <table align="center" cellspacing="0" cellpadding="0" width="100%" style="margin: 0 auto;width: 100%; ">
        <tr><td colspan="2" style="padding: 8px;background-color: #E1E2E9;color:#000000;text-align: left;font-size:12px;font-family: 'Montserrat',sans-serif;"> <b> Top search queries </b> </td> </tr>    
        <tr style='border-bottom: 1px solid #E8E8E8; color: #43425D;'>
            <td style="padding: 8px 15px; text-align: left;font-weight: bold; font-size: 14px; line-height: 18px; font-family: 'Montserrat',sans-serif; background-color: #FFFFFF"">Query</td>
            <td style="padding: 8px 40px; text-align: right;font-weight: bold; font-size: 14px; line-height: 18px; font-family: 'Montserrat',sans-serif; background-color: #FFFFFF"">Count</td>
        </tr>`;
    body[0].forEach(el => {
        template += "<tr style='border-bottom: 1px solid #E8E8E8;'><td style='padding: 14px 15px; width: 55%;font-family: Montserrat,sans-serif;'>" + el.query + "</td><td style='padding: 8px 40px; width: 45%; text-align: right;font-family: Montserrat,sans-serif;'> <span style='padding: 2px 6px;'>" + el.count + "</span></td></tr>";
    });
    template += `</table>
    </td></tr >

        <tr style=" background-color: white; "><td style="padding: 10px 0px;">
            <table cellspacing="0" cellpadding="0" align="center" width="100%" style="margin: 0 auto;width: 100%; ">
            <tr><td colspan="2" style="padding: 8px;background-color: #E1E2E9;color:#000000;text-align: left;font-size:12px; font-family: 'Montserrat',sans-serif;"> <b> Top search queries without result </b> </td> </tr>    
            <tr style='border-bottom: 1px solid #E8E8E8;color: #43425D;'>
                <td style="padding: 8px 15px; text-align: left;font-weight: bold; font-size: 14px; line-height: 18px; font-family: 'Montserrat',sans-serif; background-color: #FFFFFF"">Query</td>
                <td style="padding: 8px 40px; text-align: right;font-weight: bold; font-size: 14px; line-height: 18px; font-family: 'Montserrat',sans-serif; background-color: #FFFFFF"">Count</td>
            </tr>`;
    body[1].forEach(el => {
        // console.log("el._source",el._source);
        template += "<tr style='border-bottom: 1px solid #E8E8E8;'><td style='padding: 14px 15px; width: 55%;font-family: Montserrat,sans-serif;'>" + el.query + "</td><td style='padding: 8px 40px; text-align: right; width: 45%;font-family: Montserrat,sans-serif;'> <span style='padding: 2px 6px;'>" + el.count + "</span></td></tr>";
    });
    template += `</table>
    </td></tr >

        <tr style=" background-color: white; "><td style="padding: 10px 0px;">
            <table cellspacing="0" cellpadding="0" align="center" width="100%" style="margin: 0 auto;width: 100%; ">
            <tr><td colspan="2" style="padding: 8px;background-color: #E1E2E9;color:#000000;text-align: left;font-size:12px; font-family: 'Montserrat',sans-serif;"> <b> Top Conversions </b> </td> </tr>    

            <tr style='border-bottom: 1px solid #E8E8E8; color: #43425D;'>
                <td style="padding: 8px 15px; text-align: left;font-weight: bold; font-size: 14px; line-height: 18px; font-family: 'Montserrat',sans-serif; background-color: #FFFFFF"">Subject</td>
                <td style="padding: 8px 40px; text-align: right;font-weight: bold; font-size: 14px; line-height: 18px; font-family: 'Montserrat',sans-serif; background-color: #FFFFFF"">Clicks</td>
            </tr>`;
    body[2].forEach(el => {
        // console.log("el._source",el._source);
        template += "<tr style='border-bottom: 1px solid #E8E8E8;'><td style='padding: 14px 15px; width: 55%;font-family: Montserrat,sans-serif;'><a href='" + el.url + "' target='_blank'>" + el.subject + "</a></td><td style='padding: 8px 40px; text-align: right; width: 45%; font-family: Montserrat,sans-serif;'> <span style='padding: 2px 6px;'>" + el.clicks + "</span></td></tr>";
    });
    template += `</table>
    </td></tr >

        <tr style=" background-color: white; "><td style="padding: 10px 0px;">
            <table cellspacing="0" cellpadding="0" align="center" width="100%" style="margin: 0 auto;width: 100%; ">
            <tr><td colspan="2" style="padding: 8px;background-color: #E1E2E9;color:#000000;text-align: left;font-size:12px; font-family: 'Montserrat',sans-serif;"> <b> Results with least number of clicks </b> </td> </tr>    

            <tr style='border-bottom: 1px solid #E8E8E8;color: #43425D;'>
                <td style="padding: 8px 15px; text-align: left;font-weight: bold; font-size: 14px; line-height: 18px; font-family: 'Montserrat',sans-serif; background-color: #FFFFFF"">Subject</td>
                <td style="padding: 8px 40px; text-align: right;font-weight: bold; font-size: 14px; line-height: 18px; font-family: 'Montserrat',sans-serif; background-color: #FFFFFF"">Clicks</td>

            </tr>`;
    body[3].forEach(el => {
        // console.log("el._source",el._source);
        template += "<tr style='border-bottom: 1px solid #E8E8E8;'><td style='padding: 14px 15px; width: 55%; font-family: Montserrat,sans-serif;'><a href='" + el.url + "' target='_blank'>" + el.subject + "</a></td><td style='padding: 8px 40px; text-align: right; width: 45%; font-family: Montserrat,sans-serif;'> <span style='padding: 2px 6px;'>" + el.clicks + "</span></td></tr>";
    });
    template += `</table>
    </td></tr >

        <tr style=" background-color: white; "><td style="padding: 10px 0px;">
            <table cellspacing="0" cellpadding="0" align="center" width="100%" style="margin: 0 auto;width: 100%;">
            <tr><td colspan="2" style="padding: 8px;background-color: #E1E2E9;color:#000000;text-align: left;font-size:12px; font-family: 'Montserrat',sans-serif;"> <b> Discussion ready to become help articles </b> </td> </tr>    

            <tr style='border-bottom: 1px solid #E8E8E8; color: #43425D;'>
                <td style="padding: 8px 15px; text-align: left;font-weight: bold; font-size: 14px; line-height: 18px; font-family: 'Montserrat',sans-serif; background-color: #FFFFFF"">Subject</td>
                <td style="padding: 8px 40px; text-align: right;font-weight: bold; font-size: 14px; line-height: 18px; font-family: 'Montserrat',sans-serif; background-color: #FFFFFF"">Clicks</td>
            </tr>`;
    body[4].forEach(el => {
        // console.log("el._source",el._source);
        template += "<tr style='border-bottom: 1px solid #E8E8E8;'><td style='padding: 14px 15px; width: 55%; font-family: Montserrat,sans-serif;'><a href='" + el.url + "' target='_blank'>" + el.title + "</a></td><td style='padding: 8px 40px; text-align: right; width: 45%; font-family: Montserrat,sans-serif;'> <span style='padding: 2px 6px;'>" + el.count + "</span></td></tr>";
    });
    template += `</table>
                </td>
                </tr>
                </div>
                `
    return template;
}

function registerMessage(email, token) {
    var msg = "";
    var template = `<!DOCTYPE html>
    <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
        xmlns:o="urn:schemas-microsoft-com:office:office">
    
    <head>
        <meta charset="utf-8"> <!-- utf-8 works for most cases -->
        <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
        <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
        <title>User Registration</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
    
        <!-- Web Font / @font-face : BEGIN -->
        <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
    
        <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
        <!--[if mso]>
            <style>
                * {
                    font-family: 'Montserrat',sans-serif !important;
                }
            </style>
        <![endif]-->
    
        <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. More on that here: http://stylecampaign.com/blog/2015/02/webfont-support-in-email/ -->
        <!-- [if !mso] -->
        <!-- insert web font reference, eg: <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'> -->
        <link
            href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
            rel="stylesheet">
        <!-- [endif] -->
    
        <!-- Web Font / @font-face : END -->
    
        <!-- CSS Reset -->
        <style>
            /* What it does: Remove spaces around the email design added by some email clients. */
            /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
            html,
            body {
                margin: 0 auto !important;
                padding: 0 !important;
                height: 100% !important;
                width: 100% !important;
            }
    
            /* What it does: Stops email clients resizing small text. */
            * {
                -ms-text-size-adjust: 100%;
                -webkit-text-size-adjust: 100%;
            }
    
            /* What it does: Centers email on Android 4.4 */
            div[style*="margin: 16px 0"] {
                margin: 0 !important;
            }
    
            /* What it does: Stops Outlook from adding extra spacing to tables. */
            table,
            td {
                mso-table-lspace: 0pt !important;
                mso-table-rspace: 0pt !important;
            }
    
            /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
            table {
                border-spacing: 0 !important;
                border-collapse: collapse !important;
                table-layout: fixed !important;
                margin: 0 auto !important;
            }
    
            table table table {
                table-layout: auto;
            }
    
            /* What it does: Uses a better rendering method when resizing images in IE. */
            img {
                -ms-interpolation-mode: bicubic;
            }
    
            /* What it does: A work-around for email clients meddling in triggered links. */
            *[x-apple-data-detectors],
            /* iOS */
            .x-gmail-data-detectors,
            /* Gmail */
            .x-gmail-data-detectors *,
            .aBn {
                border-bottom: 0 !important;
                cursor: default !important;
                color: inherit !important;
                text-decoration: none !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
            }
    
            /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
            .a6S {
                display: none !important;
                opacity: 0.01 !important;
            }
    
            /* If the above doesn't work, add a .g-img class to any image in question. */
            img.g-img+div {
                display: none !important;
            }
    
            /* What it does: Prevents underlining the button text in Windows 10 */
            .button-link {
                text-decoration: none !important;
            }
    
            /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
            /* Create one of these media queries for each additional viewport size you'd like to fix */
            /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
            @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
    
                /* iPhone 6 and 6+ */
                .email-container {
                    min-width: 375px !important;
                }
            }
        </style>
    
        <!-- Progressive Enhancements -->
        <!-- <style>
            /* What it does: Hover styles for buttons */
            .button-td,
            .button-a {
                transition: all 100ms ease-in;
            }
    
            .button-td:hover,
            .button-a:hover {
                background: linear-gradient(263deg, #55c7ff, #7886f7) !important;
                border-color: linear-gradient(263deg, #55c7ff, #7886f7) !important;
            }
    
            /* Media Queries */
            @media screen and (max-width: 600px) {
    
                /* What it does: Adjust typography on small screens to improve readability */
                .email-container p {
                    font-size: 17px !important;
                    line-height: 22px !important;
                }
    
            }
        </style> -->
    
        <!-- What it does: Makes background images in 72ppi Outlook render at correct size. -->
        <!--[if gte mso 9]>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
        <![endif]-->
    
    </head>
    
    <body width="100%"
        style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
        <center style="width: 100%; ">
    
            <!--
                Set the email width. Defined in two places:
                1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
                2. MSO tags for Desktop Windows Outlook enforce a 600px width.
            -->
            <div style="max-width: 600px; margin: auto;" class="email-container">
                <!--[if mso]>
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
                <tr>
                <td>
                <![endif]-->
    
                <!-- Email Header : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
                    <tr>
                        <td style="padding: 20px 0; text-align: left">
                            <!-- IF logo.src -->
                            <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                            <!-- ELSE -->
                            &nbsp;
                            <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                                style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <!-- ENDIF logo.src -->
                        </td>
                    </tr>
                </table>
                <!-- Email Header : END -->
    
                <!-- Email Body : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;background-color: white;">

                    <!-- Hero Image, Flush : BEGIN -->
                    <tr bgcolor="#F6F7FB">
                        <td
                            style="padding: 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <h3
                                style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500;">
                                Hi ${email},</h3>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;">
                            <img src="${config.get('adminURL')}/resources/Assets/register_user.png" width="300" height="300" border="0" align="center"
                                style="width: 300px; height: 300px; max-width: 300px;padding-left:4px; height: auto; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;"
                                class="g-img">
    
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;height:30px;">
                        </td>
                    </tr>
    
                    <!-- Hero Image, Flush : END -->
    
                    <!-- 1 Column Text + Button : BEGIN -->
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                            border-radius: 0% 0% 100% 100%;
                            border-bottom: 60px solid #F6F7FB;
                            border-collapse: separate !important;">
                                <tr >
                                    <td bgcolor="#F6F7FB" style="padding: 20px 0px 10px 0px; font-family: 'Montserrat', sans-serif;;font-size: 24px; font-weight: 600; line-height: 20px;text-align: center; color: #43425d;">
                                        <p style="margin: 0;">
                                            New user Registration
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr bgcolor="#ffffff">
                                    <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                                        &nbsp;
                                        
                                    </td>
                                </tr>
                                <tr bgcolor="#ffffff">
                                    <td
                                        style="padding: 9px 40px 10px 40px;font-family: 'Montserrat', sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 12px;font-weight: 400;border-radius: 0% 0% 32% 32%;">
                                        <p style = "margin: 0;padding: 10px 10px 10px 10px; font-weight: 600; color:#03a9f4c2; font-size: 18px;" >
                                          Welcome to SearchUnify! </p>
                                        <p style="margin: 0; font-family: 'Montserrat', sans-serif;;">Please follow the link below to complete your</p>
                                        <p style="margin: 0 0 10px 0; padding-bottom: 15px;">SearchUnify registration</p>
                                        <a href="${config.get('adminURL') + appVariables.registerURL + 'token=' + token}" style="font-family: 'Montserrat', sans-serif; padding: 12px 28px; background-color: #55c7ff; background-image: linear-gradient(to left, #55c7ff, #7886f7);color:#fff; font-size:14px;font-weight:600;  border:0px; border-radius:5px; text-decoration: none;">Continue Registration</a>
                                        <p style="padding: 5px;">The link will only be active for ${config.get('linkExpiration')} hours</p>
                                    </td>
    
                                </tr>
                              </tr>
                                <tr>
                                    <td bgcolor="#F6F7FB">
                                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                    <tr>
                                        <td style="text-align:center;font-size: 18px;font-weight: 500;">
                                            <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                                                Feel free to reach out to the <br /> SearchUnify team for any
                                                further support.
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="padding: 8px;">
                                        <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                                            <a href='https://community.searchunify.com/support/' target="_blank"
                                                style="/* text-decoration: none; */font-family: 'Montserrat', sans-serif;font-size: 13px;color: #6da7fb;">
                                                https://community.searchunify.com/support/
                                            </a>
                                        </td>
                                    </tr>
  
                                </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                    &copy; ${new Date().getFullYear()}. All rights reserved
                                                </td>
                                            </tr>
                                            <tr>
                                                <td
                                                    style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif;; font-size: 12px; color: #a6a6a6;text-align: center">
                                                    Email: <a style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:<EMAIL>"><EMAIL></a>, <a
                                                        style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
    
                            </table>
                        </td>
                    </tr>
                    <!-- 1 Column Text + Button : END -->
    
                </table>
                <!-- Email Body : END -->
    
                <!-- Email Footer : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
                    style="max-width: 680px;">
                    <tr>
                        <td style="padding: 10px 0px 10px 0px;">
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                                    <td style="width: 25px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/contact-us/">Contact </a>|
                                    </td>
                                    <td style="width: 40px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/privacy-policy/">Privacy
                                            Policy </a>|
                                    </td>
                                    <td style="width: 68px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                            Conditions</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td>
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.facebook.com/SearchUnify/" alt="facebook"><img src="${config.get('adminURL')}/resources/Assets/fb.png"></a>
                                        <a href="https://www.linkedin.com/showcase/searchunify/" alt="linkedin"><img src="${config.get('adminURL')}/resources/Assets/linkedin.png"></a>
                                        <a href="https://twitter.com/SearchUnify" alt="tweeter"><img src="${config.get('adminURL')}/resources/Assets/tweeter.png"></a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td
                            style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                            <br><br>
    
                        </td>
                    </tr>
                </table>
                <!-- Email Footer : END -->
    
                <!--[if mso]>
                </td>
                </tr>
                </table>
                <![endif]-->
            </div>
    
        </center>
    </body>
    
    </html>
      `;
    return template;
}

function forgetMessage(email, token) {
    var msg = "";
    var template = `<!DOCTYPE html>
    <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
        xmlns:o="urn:schemas-microsoft-com:office:office">
    
    <head>
        <meta charset="utf-8"> <!-- utf-8 works for most cases -->
        <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
        <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
        <title>Reset Password</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
    
        <!-- Web Font / @font-face : BEGIN -->
        <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
    
        <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
        <!--[if mso]>
            <style>
                * {
                    font-family: 'Montserrat',sans-serif !important;
                }
            </style>
        <![endif]-->
    
        <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. More on that here: http://stylecampaign.com/blog/2015/02/webfont-support-in-email/ -->
        <!-- [if !mso] -->
        <!-- insert web font reference, eg: <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'> -->
        <link
            href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
            rel="stylesheet">
        <!-- [endif] -->
    
        <!-- Web Font / @font-face : END -->
    
        <!-- CSS Reset -->
        <style>
            /* What it does: Remove spaces around the email design added by some email clients. */
            /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
            html,
            body {
                margin: 0 auto !important;
                padding: 0 !important;
                height: 100% !important;
                width: 100% !important;
            }
    
            /* What it does: Stops email clients resizing small text. */
            * {
                -ms-text-size-adjust: 100%;
                -webkit-text-size-adjust: 100%;
            }
    
            /* What it does: Centers email on Android 4.4 */
            div[style*="margin: 16px 0"] {
                margin: 0 !important;
            }
    
            /* What it does: Stops Outlook from adding extra spacing to tables. */
            table,
            td {
                mso-table-lspace: 0pt !important;
                mso-table-rspace: 0pt !important;
            }
    
            /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
            table {
                border-spacing: 0 !important;
                border-collapse: collapse !important;
                table-layout: fixed !important;
                margin: 0 auto !important;
            }
    
            table table table {
                table-layout: auto;
            }
    
            /* What it does: Uses a better rendering method when resizing images in IE. */
            img {
                -ms-interpolation-mode: bicubic;
            }
    
            /* What it does: A work-around for email clients meddling in triggered links. */
            *[x-apple-data-detectors],
            /* iOS */
            .x-gmail-data-detectors,
            /* Gmail */
            .x-gmail-data-detectors *,
            .aBn {
                border-bottom: 0 !important;
                cursor: default !important;
                color: inherit !important;
                text-decoration: none !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
            }
    
            /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
            .a6S {
                display: none !important;
                opacity: 0.01 !important;
            }
    
            /* If the above doesn't work, add a .g-img class to any image in question. */
            img.g-img+div {
                display: none !important;
            }
    
            /* What it does: Prevents underlining the button text in Windows 10 */
            .button-link {
                text-decoration: none !important;
            }
    
            /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
            /* Create one of these media queries for each additional viewport size you'd like to fix */
            /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
            @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
    
                /* iPhone 6 and 6+ */
                .email-container {
                    min-width: 375px !important;
                }
            }
        </style>
    
        <!-- Progressive Enhancements -->
        <!-- <style>
            /* What it does: Hover styles for buttons */
            .button-td,
            .button-a {
                transition: all 100ms ease-in;
            }
    
            .button-td:hover,
            .button-a:hover {
                background: linear-gradient(263deg, #55c7ff, #7886f7) !important;
                border-color: linear-gradient(263deg, #55c7ff, #7886f7) !important;
            }
    
            /* Media Queries */
            @media screen and (max-width: 600px) {
    
                /* What it does: Adjust typography on small screens to improve readability */
                .email-container p {
                    font-size: 17px !important;
                    line-height: 22px !important;
                }
    
            }
        </style> -->
    
        <!-- What it does: Makes background images in 72ppi Outlook render at correct size. -->
        <!--[if gte mso 9]>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
        <![endif]-->
    
    </head>
    
    <body width="100%"
        style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
        <center style="width: 100%; ">
    
            <!--
                Set the email width. Defined in two places:
                1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
                2. MSO tags for Desktop Windows Outlook enforce a 600px width.
            -->
            <div style="max-width: 600px; margin: auto;" class="email-container">
                <!--[if mso]>
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
                <tr>
                <td>
                <![endif]-->
    
                <!-- Email Header : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
                    <tr>
                        <td style="padding: 20px 0; text-align: left">
                            <!-- IF logo.src -->
                            <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                            <!-- ELSE -->
                            &nbsp;
                            <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                                style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <!-- ENDIF logo.src -->
                        </td>
                    </tr>
                </table>
                <!-- Email Header : END -->
    
                <!-- Email Body : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
    
                    <!-- Hero Image, Flush : BEGIN -->
                    <tr bgcolor="#F6F7FB">
                        <td
                            style="padding: 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <h3
                                style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500;">
                                Hi ${email},</h3>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;">
                            <img src="${config.get('adminURL')}/resources/Assets/password_reset.png" width="300" height="300" border="0" align="center"
                                style="width: 300px; height: 300px; max-width: 300px;padding-left:4px; height: auto; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;"
                                class="g-img">
    
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;height:30px;">
                        </td>
                    </tr>
    
                    <!-- Hero Image, Flush : END -->
    
                    <!-- 1 Column Text + Button : BEGIN -->
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                            border-radius: 0% 0% 100% 100%;
                            border-bottom: 60px solid #F6F7FB;
                            border-collapse: separate !important;">
                                <tr >
                                    <td bgcolor="#F6F7FB" style="padding: 20px 0px 10px 0px; font-family: 'Montserrat', sans-serif;;font-size: 24px; font-weight: 600; line-height: 20px;text-align: center; color: #43425d;">
                                        <p style="margin: 0;">
                                            Password Reset Request
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr bgcolor="#ffffff">
                                    <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                                        &nbsp;
                                        
                                    </td>
                                </tr>
                                <tr bgcolor="#ffffff">
                                    <td
                                        style="padding: 9px 40px 10px 40px;font-family: 'Montserrat', sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 12px;font-weight: 400;border-radius: 0% 0% 32% 32%;">
                                        <p style="margin: 0; font-family: 'Montserrat', sans-serif;;">Please follow the link below to set</p>
                                        <p style="margin: 0 0 10px 0; padding-bottom: 15px;">your new password</p>
                                        <a href="${config.get('adminURL') + appVariables.registerURL + 'token=' + token}" style="font-family: 'Montserrat', sans-serif; padding: 12px 28px; background-color: #55c7ff; background-image: linear-gradient(to left, #55c7ff, #7886f7);color:#fff; font-size:14px;font-weight:600;  border:0px; border-radius:5px; text-decoration: none;">Reset Password</a>
                                        <p style="padding: 5px;">The link will only be active for ${config.get('linkExpiration')} hours</p>
                                    </td>
    
                                </tr>
                              </tr>
                                <tr>
                                    <td bgcolor="#F6F7FB">
                                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                    <tr>
                                        <td style="text-align:center;font-size: 18px;font-weight: 500;">
                                            <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                                                Feel free to reach out to the <br /> SearchUnify team for any
                                                further support.
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="padding: 8px;">
                                        <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                                            <a href='https://community.searchunify.com/support/' target="_blank"
                                                style="/* text-decoration: none; */font-family: 'Montserrat', sans-serif;font-size: 13px;color: #6da7fb;">
                                                https://community.searchunify.com/support/
                                            </a>
                                        </td>
                                    </tr>
  
                                </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                    &copy; ${new Date().getFullYear()}. All rights reserved
                                                </td>
                                            </tr>
                                            <tr>
                                                <td
                                                    style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif;; font-size: 12px; color: #a6a6a6;text-align: center">
                                                    Email: <a style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:<EMAIL>"><EMAIL></a>, <a
                                                        style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
    
                            </table>
                        </td>
                    </tr>
                    <!-- 1 Column Text + Button : END -->
    
                </table>
                <!-- Email Body : END -->
    
                <!-- Email Footer : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
                    style="max-width: 680px;">
                    <tr>
                        <td style="padding: 10px 0px 10px 0px;">
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                                    <td style="width: 25px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/contact-us/">Contact </a>|
                                    </td>
                                    <td style="width: 40px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/privacy-policy/">Privacy
                                            Policy </a>|
                                    </td>
                                    <td style="width: 68px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                            Conditions</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td>
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.facebook.com/SearchUnify/" alt="facebook"><img src="${config.get('adminURL')}/resources/Assets/fb.png"></a>
                                        <a href="https://www.linkedin.com/showcase/searchunify/" alt="linkedin"><img src="${config.get('adminURL')}/resources/Assets/linkedin.png"></a>
                                        <a href="https://twitter.com/SearchUnify" alt="tweeter"><img src="${config.get('adminURL')}/resources/Assets/tweeter.png"></a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td
                            style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                            <br><br>
    
                        </td>
                    </tr>
                </table>
                <!-- Email Footer : END -->
    
                <!--[if mso]>
                </td>
                </tr>
                </table>
                <![endif]-->
            </div>
    
        </center>
    </body>
    
    </html>
      `;
    return template;
  }

function welcomeMessage(email) {

    var template = `<!DOCTYPE html>
    <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
        xmlns:o="urn:schemas-microsoft-com:office:office">
    
    <head>
        <meta charset="utf-8"> <!-- utf-8 works for most cases -->
        <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
        <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
        <title>Welcome to SearchUnify</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
    
        <!-- Web Font / @font-face : BEGIN -->
        <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
    
        <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
        <!--[if mso]>
            <style>
                * {
                    font-family: 'Montserrat',sans-serif !important;
                }
            </style>
        <![endif]-->
    
        <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. More on that here: http://stylecampaign.com/blog/2015/02/webfont-support-in-email/ -->
        <!-- [if !mso] -->
        <!-- insert web font reference, eg: <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'> -->
        <link
            href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
            rel="stylesheet">
        <!-- [endif] -->
    
        <!-- Web Font / @font-face : END -->
    
        <!-- CSS Reset -->
        <style>
            /* What it does: Remove spaces around the email design added by some email clients. */
            /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
            html,
            body {
                margin: 0 auto !important;
                padding: 0 !important;
                height: 100% !important;
                width: 100% !important;
            }
    
            /* What it does: Stops email clients resizing small text. */
            * {
                -ms-text-size-adjust: 100%;
                -webkit-text-size-adjust: 100%;
            }
    
            /* What it does: Centers email on Android 4.4 */
            div[style*="margin: 16px 0"] {
                margin: 0 !important;
            }
    
            /* What it does: Stops Outlook from adding extra spacing to tables. */
            table,
            td {
                mso-table-lspace: 0pt !important;
                mso-table-rspace: 0pt !important;
            }
    
            /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
            table {
                border-spacing: 0 !important;
                border-collapse: collapse !important;
                table-layout: fixed !important;
                margin: 0 auto !important;
            }
    
            table table table {
                table-layout: auto;
            }
    
            /* What it does: Uses a better rendering method when resizing images in IE. */
            img {
                -ms-interpolation-mode: bicubic;
            }
    
            /* What it does: A work-around for email clients meddling in triggered links. */
            *[x-apple-data-detectors],
            /* iOS */
            .x-gmail-data-detectors,
            /* Gmail */
            .x-gmail-data-detectors *,
            .aBn {
                border-bottom: 0 !important;
                cursor: default !important;
                color: inherit !important;
                text-decoration: none !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
            }
    
            /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
            .a6S {
                display: none !important;
                opacity: 0.01 !important;
            }
    
            /* If the above doesn't work, add a .g-img class to any image in question. */
            img.g-img+div {
                display: none !important;
            }
    
            /* What it does: Prevents underlining the button text in Windows 10 */
            .button-link {
                text-decoration: none !important;
            }
    
            /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
            /* Create one of these media queries for each additional viewport size you'd like to fix */
            /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
            @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
    
                /* iPhone 6 and 6+ */
                .email-container {
                    min-width: 375px !important;
                }
            }
        </style>
    
        <!-- Progressive Enhancements -->
        <!-- <style>
            /* What it does: Hover styles for buttons */
            .button-td,
            .button-a {
                transition: all 100ms ease-in;
            }
    
            .button-td:hover,
            .button-a:hover {
                background: linear-gradient(263deg, #55c7ff, #7886f7) !important;
                border-color: linear-gradient(263deg, #55c7ff, #7886f7) !important;
            }
    
            /* Media Queries */
            @media screen and (max-width: 600px) {
    
                /* What it does: Adjust typography on small screens to improve readability */
                .email-container p {
                    font-size: 17px !important;
                    line-height: 22px !important;
                }
    
            }
        </style> -->
    
        <!-- What it does: Makes background images in 72ppi Outlook render at correct size. -->
        <!--[if gte mso 9]>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
        <![endif]-->
    
    </head>
    
    <body width="100%"
        style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
        <center style="width: 100%; ">
    
            <!--
                Set the email width. Defined in two places:
                1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
                2. MSO tags for Desktop Windows Outlook enforce a 600px width.
            -->
            <div style="max-width: 600px; margin: auto;" class="email-container">
                <!--[if mso]>
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
                <tr>
                <td>
                <![endif]-->
    
                <!-- Email Header : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
                    <tr>
                        <td style="padding: 20px 0; text-align: left">
                            <!-- IF logo.src -->
                            <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                            <!-- ELSE -->
                            &nbsp;
                            <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                                style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <!-- ENDIF logo.src -->
                        </td>
                    </tr>
                </table>
                <!-- Email Header : END -->
    
                <!-- Email Body : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
    
                    <!-- Hero Image, Flush : BEGIN -->
                    <tr bgcolor="#F6F7FB">
                        <td
                            style="padding: 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <h3
                                style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500;">
                                Hi ${email},</h3>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;">
                            <img src="${config.get('adminURL')}/resources/Assets/welcome_user.png" width="300" height="300" border="0" align="center"
                                style="width: 300px; height: 300px; max-width: 300px;padding-left:4px; height: auto; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;"
                                class="g-img">
    
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;height:30px;">
                        </td>
                    </tr>
    
                    <!-- Hero Image, Flush : END -->
    
                    <!-- 1 Column Text + Button : BEGIN -->
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                            border-radius: 0% 0% 100% 100%;
                            border-bottom: 60px solid #F6F7FB;
                            border-collapse: separate !important;">
                                <tr >
                                    <td bgcolor="#F6F7FB" style="padding: 20px 0px 10px 0px; font-family: 'Montserrat', sans-serif;;font-size: 24px; font-weight: 600; line-height: 20px;text-align: center; color: #43425d;">
                                        <p style="margin: 0;">
                                            Welcome to SearchUnify
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr bgcolor="#ffffff">
                                    <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                                        &nbsp;
                                        
                                    </td>
                                </tr>
                                <tr bgcolor="#ffffff">
                                    <td
                                        style="padding: 9px 40px 10px 40px;font-family: 'Montserrat', sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 12px;font-weight: 400;border-radius: 0% 0% 32% 32%;">
                                        <p style="margin: 0; font-family: 'Montserrat', sans-serif;;">Your account has been registered. Use the link mentioned</p>
                                        <p style="margin: 0 0 10px 0; padding-bottom: 15px;">below to login to the admin panel.</p>
                                        <a href="${config.get('adminURL')}" style="font-family: 'Montserrat', sans-serif; padding: 12px 28px; background-color: #55c7ff; background-image: linear-gradient(to left, #55c7ff, #7886f7);color:#fff; font-size:14px;font-weight:600;  border:0px; border-radius:5px; text-decoration: none;">Log In</a>
                                    </td>
    
                                </tr>
                              </tr>
                                <tr>
                                    <td bgcolor="#F6F7FB">
                                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                    <tr>
                                        <td style="text-align:center;font-size: 18px;font-weight: 500;">
                                            <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                                                Feel free to reach out to the <br /> SearchUnify team for any
                                                further support.
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="padding: 8px;">
                                        <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                                            <a href='https://community.searchunify.com/support/' target="_blank"
                                                style="/* text-decoration: none; */font-family: 'Montserrat', sans-serif;font-size: 13px;color: #6da7fb;">
                                                https://community.searchunify.com/support/
                                            </a>
                                        </td>
                                    </tr>
  
                                </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                    &copy; ${new Date().getFullYear()}. All rights reserved
                                                </td>
                                            </tr>
                                            <tr>
                                                <td
                                                    style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif;; font-size: 12px; color: #a6a6a6;text-align: center">
                                                    Email: <a style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:<EMAIL>"><EMAIL></a>, <a
                                                        style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
    
                            </table>
                        </td>
                    </tr>
                    <!-- 1 Column Text + Button : END -->
    
                </table>
                <!-- Email Body : END -->
    
                <!-- Email Footer : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
                    style="max-width: 680px;">
                    <tr>
                        <td style="padding: 10px 0px 10px 0px;">
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                                    <td style="width: 25px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/contact-us/">Contact </a>|
                                    </td>
                                    <td style="width: 40px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/privacy-policy/">Privacy
                                            Policy </a>|
                                    </td>
                                    <td style="width: 68px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                            Conditions</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td>
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.facebook.com/SearchUnify/" alt="facebook"><img src="${config.get('adminURL')}/resources/Assets/fb.png"></a>
                                        <a href="https://www.linkedin.com/showcase/searchunify/" alt="linkedin"><img src="${config.get('adminURL')}/resources/Assets/linkedin.png"></a>
                                        <a href="https://twitter.com/SearchUnify" alt="tweeter"><img src="${config.get('adminURL')}/resources/Assets/tweeter.png"></a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td
                            style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                            <br><br>
    
                        </td>
                    </tr>
                </table>
                <!-- Email Footer : END -->
    
                <!--[if mso]>
                </td>
                </tr>
                </table>
                <![endif]-->
            </div>
    
        </center>
    </body>
    
    </html>
    `;
    return template;

}

function contentDuplicacyNotification(email) {
    var msg = "";
    var template = `<table align="center"; width="450" style="margin: 0 auto; border: 1px solid #dcdcdc"; width: 450px; font-family: Verdana, Geneva, sans-serif; color: #666666; ">
      <tr>
          <td style="text-align: center;padding: 5px;">
              <img src="${config.get('adminURL')}/resources/Assets/emailLogo.png" alt="logo.png">
          </td>
      </tr>
            <tr>
                <td style="text-align: center; padding: 8px; color: #FC8102; background-color: #000; font-size: 19px;">
                   Content Duplicacy Notification
                </td>
            </tr>
            <tr>
                <td style="padding: 13px;color: #615c5c">
                Hi, <p style="text-decoration: none; display: inline; margin: 0;font-size: 15px;"> ${email}</p>
               <p style="margin: 17px 0 10px 0; font-size: 15px;">Your content duplicacy analysis is done </p>
                </td>
            </tr>
            <tr style="padding: 8px 0;color: #ffff;background-color: #000;font-size: 12px;width:100%;text-align: center;">
            <td colspan="2" style="padding: 8px;">
                Powered by © ${new Date().getFullYear()}
                <a href="https://www.grazitti.com/" target="_blank" style="color: #fff;text-decoration: none;">Grazitti Interactive</a>. All rights reserved.
            </td>
        </tr>
        </table>
        `;
    return template;
  }


  function sucesscrawltemplate(data){

    var template=`
    <!DOCTYPE html>
  <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
      xmlns:o="urn:schemas-microsoft-com:office:office">
  
  <head>
      <meta charset="utf-8"> <!-- utf-8 works for most cases -->
      <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
      <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
      <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
      <title>Crawl Successful</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
  
      <!-- Web Font / @font-face : BEGIN -->
      <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
  
      <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
      <!--[if mso]>
          <style>
              * {
                  font-family: 'Montserrat',sans-serif !important;
              }
          </style>
      <![endif]-->
  
      <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. More on that here: http://stylecampaign.com/blog/2015/02/webfont-support-in-email/ -->
      <!-- [if !mso] -->
      <!-- insert web font reference, eg: <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'> -->
      <link
          href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
          rel="stylesheet">
      <!-- [endif] -->
  
      <!-- Web Font / @font-face : END -->
  
      <!-- CSS Reset -->
      <style>
          /* What it does: Remove spaces around the email design added by some email clients. */
          /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
          html,
          body {
              margin: 0 auto !important;
              padding: 0 !important;
              height: 100% !important;
              width: 100% !important;
          }
  
          /* What it does: Stops email clients resizing small text. */
          * {
              -ms-text-size-adjust: 100%;
              -webkit-text-size-adjust: 100%;
          }
  
          /* What it does: Centers email on Android 4.4 */
          div[style*="margin: 16px 0"] {
              margin: 0 !important;
          }
  
          /* What it does: Stops Outlook from adding extra spacing to tables. */
          table,
          td {
              mso-table-lspace: 0pt !important;
              mso-table-rspace: 0pt !important;
          }
  
          /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
          table {
              border-spacing: 0 !important;
              border-collapse: collapse !important;
              table-layout: fixed !important;
              margin: 0 auto !important;
          }
  
          table table table {
              table-layout: auto;
          }
  
          /* What it does: Uses a better rendering method when resizing images in IE. */
          img {
              -ms-interpolation-mode: bicubic;
          }
  
          /* What it does: A work-around for email clients meddling in triggered links. */
          *[x-apple-data-detectors],
          /* iOS */
          .x-gmail-data-detectors,
          /* Gmail */
          .x-gmail-data-detectors *,
          .aBn {
              border-bottom: 0 !important;
              cursor: default !important;
              color: inherit !important;
              text-decoration: none !important;
              font-size: inherit !important;
              font-family: inherit !important;
              font-weight: inherit !important;
              line-height: inherit !important;
          }
  
          /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
          .a6S {
              display: none !important;
              opacity: 0.01 !important;
          }
  
          /* If the above doesn't work, add a .g-img class to any image in question. */
          img.g-img+div {
              display: none !important;
          }
  
          /* What it does: Prevents underlining the button text in Windows 10 */
          .button-link {
              text-decoration: none !important;
          }
  
          /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
          /* Create one of these media queries for each additional viewport size you'd like to fix */
          /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
          @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
  
              /* iPhone 6 and 6+ */
              .email-container {
                  min-width: 375px !important;
              }
          }
      </style>
  
      <!-- Progressive Enhancements -->
      <!-- <style>
          /* What it does: Hover styles for buttons */
          .button-td,
          .button-a {
              transition: all 100ms ease-in;
          }
  
          .button-td:hover,
          .button-a:hover {
              background: linear-gradient(263deg, #55c7ff, #7886f7) !important;
              border-color: linear-gradient(263deg, #55c7ff, #7886f7) !important;
          }
  
          /* Media Queries */
          @media screen and (max-width: 600px) {
  
              /* What it does: Adjust typography on small screens to improve readability */
              .email-container p {
                  font-size: 17px !important;
                  line-height: 22px !important;
              }
  
          }
      </style> -->
  
      <!-- What it does: Makes background images in 72ppi Outlook render at correct size. -->
      <!--[if gte mso 9]>
      <xml>
          <o:OfficeDocumentSettings>
              <o:AllowPNG/>
              <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
      </xml>
      <![endif]-->
  
  </head>
  
  <body width="100%"
      style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
      <center style="width: 100%; ">
  
          <!--
              Set the email width. Defined in two places:
              1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
              2. MSO tags for Desktop Windows Outlook enforce a 600px width.
          -->
          <div style="max-width: 600px; margin: auto;" class="email-container">
              <!--[if mso]>
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
              <tr>
              <td>
              <![endif]-->
  
              <!-- Email Header : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                  style="max-width: 600px;">
                  <tr>
                      <td style="padding: 20px 0; text-align: left">
                          <!-- IF logo.src -->
                          <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                          <!-- ELSE -->
                          &nbsp;
                          <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                              style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                          <!-- ENDIF logo.src -->
                      </td>
                  </tr>
              </table>
              <!-- Email Header : END -->
  
              <!-- Email Body : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                  style="max-width: 600px;">
  
                  <!-- Hero Image, Flush : BEGIN -->
                  <tr bgcolor="#F6F7FB">
                      <td
                          style="padding: 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                          <h3
                              style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500;">
                              Hi Admin</h3>
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#F6F7FB" style="text-align: center;">
                          <img src="${config.get('adminURL')}/resources/Assets/CrawlSuccessful.png" width="300" height="300" border="0" align="center"
                              style="width: 300px; height: 300px; max-width: 300px;padding-left:4px; height: auto; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;"
                              class="g-img">
  
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#F6F7FB" style="text-align: center;height:30px;">
                      </td>
                  </tr>
  
                  <!-- Hero Image, Flush : END -->
  
                  <!-- 1 Column Text + Button : BEGIN -->
                  <tr>
                      <td bgcolor="#ffffff">
                          <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                          border-radius: 0% 0% 100% 100%;
                          border-bottom: 60px solid #F6F7FB;
                          border-collapse: separate !important;">
                              <tr >
                                  <td bgcolor="#F6F7FB" style="padding: 20px 0px 10px 0px; font-family: 'Montserrat', sans-serif;;font-size: 24px; font-weight: 600; line-height: 20px;text-align: center; color: #43425d;">
                                      <p style="margin: 0;">
                                          Crawl Successful
                                      </p>
                                  </td>
                              </tr>
                              <tr>
                                  <td bgcolor="#F6F7FB" style="padding: 10px; font-family: 'Montserrat', sans-serif;;font-size: 14px; font-weight: normal; text-align: center; color: #43425d;">
                                      <p style="margin: 0;">
                                          on <span style="font-size:16px;">Content Source '${data.label}'</span> at ${new Date()}
                                      </p>
                                      <p style ="margin: 0;padding: 10px 10px 10px 10px;color: #6ba9fb;font-weight: 600;">
                                        Number of Documents Crawled: <br/> <span style="color:#43425d">${data.count}</span> 
                                         </p>
                                  </td>
                              </tr>
                          </table>
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#ffffff">
                          <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                              <tr bgcolor="#ffffff">
                                  <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                                      &nbsp;
                                      
                                  </td>
                              </tr>
                              <tr bgcolor="#ffffff">
                                  <td
                                      style="padding: 9px 40px 40px 40px;font-family: 'Montserrat', sans-serif;line-height: 20px;color: #43425D;text-align: center;font-size: 14px;font-weight: 500;">
                                      <p style="margin: 0 0 10px 0; font-family: 'Montserrat', sans-serif;;"> Refresh
                                          search client page if you cannot</p>
                                      <p style="margin: 0;">view the updated results immediately</p>
                                  </td>
  
                              </tr>
                              <tr>
                                  <td bgcolor="#F6F7FB">
                                  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                  <tr>
                                      <td style="text-align:center;font-size: 18px;font-weight: 500;">
                                          <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                                              Feel free to reach out to the <br /> SearchUnify team for any
                                              further support .
                                          </p>
                                      </td>
                                  </tr>
                                  <tr style="padding: 8px;">
                                      <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                                          <a href='https://community.searchunify.com/support/' target="_blank"
                                              style="/* text-decoration: none; */font-family: 'Montserrat', sans-serif;font-size: 13px;color: #6da7fb;">
                                              https://community.searchunify.com/support/
                                          </a>
                                      </td>
                                  </tr>

                              </table>
                                  </td>
                              </tr>
                              <tr>
                                  <td>
                                      <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                          <tr>
                                              <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                  &copy; ${new Date().getFullYear()}. All rights reserved
                                              </td>
                                          </tr>
                                          <tr>
                                              <td
                                                  style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif;; font-size: 12px; color: #a6a6a6;text-align: center">
                                                  Email: <a style="text-decoration: none;color:#a6a6a6;"
                                                      href="mailto:<EMAIL>"><EMAIL></a>,<a
                                                      style="text-decoration: none;color:#a6a6a6;"
                                                      href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                              </td>
                                          </tr>
                                      </table>
                                  </td>
                              </tr>
  
                          </table>
                      </td>
                  </tr>
                  <!-- 1 Column Text + Button : END -->
  
              </table>
              <!-- Email Body : END -->
  
              <!-- Email Footer : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
                  style="max-width: 680px;">
                  <tr>
                      <td style="padding: 10px 0px 10px 0px;">
                          <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                              <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                                  <td style="width: 25px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/contact-us/">Contact </a>|
                                  </td>
                                  <td style="width: 40px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/privacy-policy/">Privacy
                                          Policy </a>|
                                  </td>
                                  <td style="width: 68px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                          Conditions</a>
                                  </td>
                              </tr>
                          </table>
                      </td>
                      <td>
                          <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                              <tr>
                                  <td style="text-align:right;padding:20px 0 0 0; ">
                                      <a href="https://www.facebook.com/SearchUnify/" alt="facebook"><img src="${config.get('adminURL')}/resources/Assets/fb.png"></a>
                                      <a href="https://www.linkedin.com/showcase/searchunify/" alt="linkedin"><img src="${config.get('adminURL')}/resources/Assets/linkedin.png"></a>
                                      <a href="https://twitter.com/SearchUnify" alt="tweeter"><img src="${config.get('adminURL')}/resources/Assets/tweeter.png"></a>
                                  </td>
                              </tr>
                          </table>
                      </td>
                  </tr>
                  <tr>
                      <td
                          style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                          <br><br>
  
                      </td>
                  </tr>
              </table>
              <!-- Email Footer : END -->
  
              <!--[if mso]>
              </td>
              </tr>
              </table>
              <![endif]-->
          </div>
  
      </center>
  </body>
  
  </html>
  
    `;


    return template;
  }


  function failcrawltemplate(faildata){
    var template = `<!DOCTYPE html>
    <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
        xmlns:o="urn:schemas-microsoft-com:office:office">
    
    <head>
        <meta charset="utf-8"> <!-- utf-8 works for most cases -->
        <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
        <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
        <title>Crawl Unsuccessful</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
    
        <!-- Web Font / @font-face : BEGIN -->
        <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
    
        <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
        <!--[if mso]>
            <style>
                * {
                    font-family: 'Montserrat',sans-serif !important;
                }
            </style>
        <![endif]-->
    
        <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. More on that here: http://stylecampaign.com/blog/2015/02/webfont-support-in-email/ -->
        <!-- [if !mso] -->
        <!-- insert web font reference, eg: <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'> -->
        <link
            href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
            rel="stylesheet">
        <!-- [endif] -->
    
        <!-- Web Font / @font-face : END -->
    
        <!-- CSS Reset -->
        <style>
            /* What it does: Remove spaces around the email design added by some email clients. */
            /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
            html,
            body {
                margin: 0 auto !important;
                padding: 0 !important;
                height: 100% !important;
                width: 100% !important;
            }
    
            /* What it does: Stops email clients resizing small text. */
            * {
                -ms-text-size-adjust: 100%;
                -webkit-text-size-adjust: 100%;
            }
    
            /* What it does: Centers email on Android 4.4 */
            div[style*="margin: 16px 0"] {
                margin: 0 !important;
            }
    
            /* What it does: Stops Outlook from adding extra spacing to tables. */
            table,
            td {
                mso-table-lspace: 0pt !important;
                mso-table-rspace: 0pt !important;
            }
    
            /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
            table {
                border-spacing: 0 !important;
                border-collapse: collapse !important;
                table-layout: fixed !important;
                margin: 0 auto !important;
            }
    
            table table table {
                table-layout: auto;
            }
    
            /* What it does: Uses a better rendering method when resizing images in IE. */
            img {
                -ms-interpolation-mode: bicubic;
            }
    
            /* What it does: A work-around for email clients meddling in triggered links. */
            *[x-apple-data-detectors],
            /* iOS */
            .x-gmail-data-detectors,
            /* Gmail */
            .x-gmail-data-detectors *,
            .aBn {
                border-bottom: 0 !important;
                cursor: default !important;
                color: inherit !important;
                text-decoration: none !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
            }
    
            /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
            .a6S {
                display: none !important;
                opacity: 0.01 !important;
            }
    
            /* If the above doesn't work, add a .g-img class to any image in question. */
            img.g-img+div {
                display: none !important;
            }
    
            /* What it does: Prevents underlining the button text in Windows 10 */
            .button-link {
                text-decoration: none !important;
            }
    
            /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
            /* Create one of these media queries for each additional viewport size you'd like to fix */
            /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
            @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
    
                /* iPhone 6 and 6+ */
                .email-container {
                    min-width: 375px !important;
                }
            }
        </style>
    
        <!-- Progressive Enhancements -->
        <!-- <style>
            /* What it does: Hover styles for buttons */
            .button-td,
            .button-a {
                transition: all 100ms ease-in;
            }
    
            .button-td:hover,
            .button-a:hover {
                background: linear-gradient(263deg, #55c7ff, #7886f7) !important;
                border-color: linear-gradient(263deg, #55c7ff, #7886f7) !important;
            }
    
            /* Media Queries */
            @media screen and (max-width: 600px) {
    
                /* What it does: Adjust typography on small screens to improve readability */
                .email-container p {
                    font-size: 17px !important;
                    line-height: 22px !important;
                }
    
            }
        </style> -->
    
        <!-- What it does: Makes background images in 72ppi Outlook render at correct size. -->
        <!--[if gte mso 9]>
      <xml>
        <o:OfficeDocumentSettings>
          <o:AllowPNG/>
          <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
      </xml>
      <![endif]-->
    
    </head>
    
    <body width="100%"
        style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
        <center style="width: 100%; ">
    
            <!--
          Set the email width. Defined in two places:
          1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
          2. MSO tags for Desktop Windows Outlook enforce a 600px width.
        -->
            <div style="max-width: 600px; margin: auto;" class="email-container">
                <!--[if mso]>
          <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
          <tr>
          <td>
          <![endif]-->
    
                <!-- Email Header : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
                    <tr>
                        <td style="padding: 20px 0; text-align: left">
                            <!-- IF logo.src -->
                            <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                            <!-- ELSE -->
                            &nbsp;
                            <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                                style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <!-- ENDIF logo.src -->
                        </td>
                    </tr>
                </table>
                <!-- Email Header : END -->
    
                <!-- Email Body : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
    
                    <!-- Hero Image, Flush : BEGIN -->
                    <tr bgcolor="#F6F7FB">
                        <td
                            style="padding: 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <h3
                                style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500;">
                                Hi Admin</h3>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;">
                            <img src="${config.get('adminURL')}/resources/Assets/UnsuccessfulCrawl.png" width="300" height="300" border="0" align="center"
                                style="width: 300px; height: 300px; max-width: 300px;padding-left:4px; height: auto; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;"
                                class="g-img">
    
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;height:30px;">
                        </td>
                    </tr>
    
                    <!-- Hero Image, Flush : END -->
    
                    <!-- 1 Column Text + Button : BEGIN -->
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                            border-radius: 0% 0% 100% 100%;
                            border-bottom: 60px solid #F6F7FB;
                            border-collapse: separate !important;">
                                <tr >
                                    <td bgcolor="#F6F7FB" style="padding: 20px 0px 10px 0px; font-family: 'Montserrat', sans-serif;;font-size: 24px; font-weight: 600; line-height: 20px;text-align: center; color: #43425d;">
                                        <p style="margin: 0;">
                                            Unsuccessful Crawl
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <td bgcolor="#F6F7FB" style="padding: 10px; font-family: 'Montserrat', sans-serif;;font-size: 14px; font-weight: normal; text-align: center; color: #43425d;">
                                        <p style="margin: 0;">
                                          on <span style="font-size:16px;">Content Source '${faildata.label}'</span> at ${new Date()}
                                        </p>
                                        <p style = "margin: 0;padding: 10px 10px 10px 10px;color: red;font-weight: 600;">
                                        Number of Documents Crawled: <br/> <span style = "color:#43425d">${faildata.count}</span>  
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <td bgcolor="#F6F7FB" style="border-radius: 0% 0% 100% 100%;">
                                    <p style="text-align: center;font-size: 14px;font-weight: 600;font-family:'Montserrat',sans-serif;">${ faildata.headerline }</p>
                                      <ol type="1" style="text-align:center;font-family:'Montserrat',sans-serif;font-size:12px;color:#43425d;padding:0;margin: 0px 40px;">
                                        ${ faildata.searchclientshtml }  
                                      </ol>  
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr bgcolor="#ffffff">
                                    <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                                        &nbsp;
                                        
                                    </td>
                                </tr>
                                <tr bgcolor="#ffffff">
                                <td style="padding: 9px 40px 40px 40px;font-family: 'Montserrat', sans-serif;line-height: 24px;color: #43425D;text-align: center;">
                                    <p style="margin: 0 0 10px 0;font-family: 'Montserrat', sans-serif;font-size: 16px;font-weight: 700;"> 
                                        The error message was:
                                    </p>
                                    <p style="margin: 0;font-size: 12px;">`;
                                        template += faildata.isScheduled ? `The scheduled crawl refresh for a content source ` : `Last crawl for the content source `;
                                        template += faildata.searchClients > 0 ? `included in the Search Client/s <br/> named above ` : ``;
                                        template +=`was not completed successfully.`;
                                        template +=`
                                    </p>
                                </td>
    
                                </tr>
                                <tr>
                                    <td bgcolor="#F6F7FB">
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <td style="text-align:center;font-size: 18px;font-weight: 400;">
                                                    <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                                                      To resolve this error, please reach out to <br/>
                                                      SearchUnify Support team.
                                                    </p>
                                                </td>
                                            </tr>
                                            <tr style="padding: 8px;">
                                                <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                                                    <a href='https://community.searchunify.com/support/' target="_blank"
                                                        style="text-decoration: none;font-family: 'Montserrat', sans-serif;font-size: 12px;color: #6da7fb;">
                                                        https://community.searchunify.com/support/
                                                    </a>
                                                </td>
                                            </tr>
    
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                    &copy; ${new Date().getFullYear()}. All rights reserved
                                                </td>
                                            </tr>
                                            <tr>
                                                <td
                                                    style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif;; font-size: 12px; color: #a6a6a6;text-align: center">
                                                    Email: <a style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:<EMAIL>"><EMAIL></a>,<a
                                                        style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
    
                            </table>
                        </td>
                    </tr>
                    <!-- 1 Column Text + Button : END -->
    
                </table>
                <!-- Email Body : END -->
    
                <!-- Email Footer : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
                    style="max-width: 680px;">
                    <tr>
                        <td style="padding: 10px 0px 10px 0px;">
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                                    <td style="width: 25px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/contact-us/">Contact </a>|
                                    </td>
                                    <td style="width: 40px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/privacy-policy/">Privacy
                                            Policy </a>|
                                    </td>
                                    <td style="width: 68px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                            Conditions</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td>
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.facebook.com/SearchUnify/" alt="facebook"><img src="${config.get('adminURL')}/resources/Assets/fb.png"></a>
                                        <a href="https://www.linkedin.com/showcase/searchunify/" alt="linkedin"><img src="${config.get('adminURL')}/resources/Assets/linkedin.png"></a>
                                        <a href="https://twitter.com/SearchUnify" alt="tweeter"><img src="${config.get('adminURL')}/resources/Assets/tweeter.png"></a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td
                            style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                            <br><br>
    
                        </td>
                    </tr>
                </table>
                <!-- Email Footer : END -->
    
                <!--[if mso]>
          </td>
          </tr>
          </table>
          <![endif]-->
            </div>
    
        </center>
    </body>
    
    </html>
    `;

    return template;
  }

  function crawlkilledtemplate(data){

    var template=`
    <!DOCTYPE html>
  <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
      xmlns:o="urn:schemas-microsoft-com:office:office">
  
  <head>
      <meta charset="utf-8"> <!-- utf-8 works for most cases -->
      <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
      <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
      <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
      <title>Crawl stopped</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
  
      <!-- Web Font / @font-face : BEGIN -->
      <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
  
      <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
      <!--[if mso]>
          <style>
              * {
                  font-family: 'Montserrat',sans-serif !important;
              }
          </style>
      <![endif]-->
  
      <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. More on that here: http://stylecampaign.com/blog/2015/02/webfont-support-in-email/ -->
      <!-- [if !mso] -->
      <!-- insert web font reference, eg: <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'> -->
      <link
          href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
          rel="stylesheet">
      <!-- [endif] -->
  
      <!-- Web Font / @font-face : END -->
  
      <!-- CSS Reset -->
      <style>
          /* What it does: Remove spaces around the email design added by some email clients. */
          /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
          html,
          body {
              margin: 0 auto !important;
              padding: 0 !important;
              height: 100% !important;
              width: 100% !important;
          }
  
          /* What it does: Stops email clients resizing small text. */
          * {
              -ms-text-size-adjust: 100%;
              -webkit-text-size-adjust: 100%;
          }
  
          /* What it does: Centers email on Android 4.4 */
          div[style*="margin: 16px 0"] {
              margin: 0 !important;
          }
  
          /* What it does: Stops Outlook from adding extra spacing to tables. */
          table,
          td {
              mso-table-lspace: 0pt !important;
              mso-table-rspace: 0pt !important;
          }
  
          /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
          table {
              border-spacing: 0 !important;
              border-collapse: collapse !important;
              table-layout: fixed !important;
              margin: 0 auto !important;
          }
  
          table table table {
              table-layout: auto;
          }
  
          /* What it does: Uses a better rendering method when resizing images in IE. */
          img {
              -ms-interpolation-mode: bicubic;
          }
  
          /* What it does: A work-around for email clients meddling in triggered links. */
          *[x-apple-data-detectors],
          /* iOS */
          .x-gmail-data-detectors,
          /* Gmail */
          .x-gmail-data-detectors *,
          .aBn {
              border-bottom: 0 !important;
              cursor: default !important;
              color: inherit !important;
              text-decoration: none !important;
              font-size: inherit !important;
              font-family: inherit !important;
              font-weight: inherit !important;
              line-height: inherit !important;
          }
  
          /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
          .a6S {
              display: none !important;
              opacity: 0.01 !important;
          }
  
          /* If the above doesn't work, add a .g-img class to any image in question. */
          img.g-img+div {
              display: none !important;
          }
  
          /* What it does: Prevents underlining the button text in Windows 10 */
          .button-link {
              text-decoration: none !important;
          }
  
          /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
          /* Create one of these media queries for each additional viewport size you'd like to fix */
          /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
          @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
  
              /* iPhone 6 and 6+ */
              .email-container {
                  min-width: 375px !important;
              }
          }
      </style>
  
      <!-- Progressive Enhancements -->
      <!-- <style>
          /* What it does: Hover styles for buttons */
          .button-td,
          .button-a {
              transition: all 100ms ease-in;
          }
  
          .button-td:hover,
          .button-a:hover {
              background: linear-gradient(263deg, #55c7ff, #7886f7) !important;
              border-color: linear-gradient(263deg, #55c7ff, #7886f7) !important;
          }
  
          /* Media Queries */
          @media screen and (max-width: 600px) {
  
              /* What it does: Adjust typography on small screens to improve readability */
              .email-container p {
                  font-size: 17px !important;
                  line-height: 22px !important;
              }
  
          }
      </style> -->
  
      <!-- What it does: Makes background images in 72ppi Outlook render at correct size. -->
      <!--[if gte mso 9]>
      <xml>
          <o:OfficeDocumentSettings>
              <o:AllowPNG/>
              <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
      </xml>
      <![endif]-->
  
  </head>
  
  <body width="100%"
      style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
      <center style="width: 100%; ">
  
          <!--
              Set the email width. Defined in two places:
              1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
              2. MSO tags for Desktop Windows Outlook enforce a 600px width.
          -->
          <div style="max-width: 600px; margin: auto;" class="email-container">
              <!--[if mso]>
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
              <tr>
              <td>
              <![endif]-->
  
              <!-- Email Header : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                  style="max-width: 600px;">
                  <tr>
                      <td style="padding: 20px 0; text-align: left">
                          <!-- IF logo.src -->
                          <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                          <!-- ELSE -->
                          &nbsp;
                          <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                              style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                          <!-- ENDIF logo.src -->
                      </td>
                  </tr>
              </table>
              <!-- Email Header : END -->
  
              <!-- Email Body : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                  style="max-width: 600px;">
  
                  <!-- Hero Image, Flush : BEGIN -->
                  <tr bgcolor="#F6F7FB">
                      <td
                          style="padding: 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                          <h3
                              style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500;">
                              Hi Admin</h3>
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#F6F7FB" style="text-align: center;">
                          <img src="${config.get('adminURL')}/resources/Assets/Crawlstop2.png" width="300" height="300" border="0" align="center"
                              style="width: 300px; height: 300px; max-width: 300px;padding-left:4px; height: auto; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;"
                              class="g-img">
  
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#F6F7FB" style="text-align: center;height:30px;">
                      </td>
                  </tr>
  
                  <!-- Hero Image, Flush : END -->
  
                  <!-- 1 Column Text + Button : BEGIN -->
                  <tr>
                      <td bgcolor="#ffffff">
                          <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                          border-radius: 0% 0% 100% 100%;
                          border-bottom: 60px solid #F6F7FB;
                          border-collapse: separate !important;">
                              <tr >
                                  <td bgcolor="#F6F7FB" style="padding: 20px 0px 10px 0px; font-family: 'Montserrat', sans-serif;;font-size: 24px; font-weight: 600; line-height: 20px;text-align: center; color: #43425d;">
                                      <p style="margin: 0;">
                                          Crawling stopped
                                      </p>
                                  </td>
                              </tr>
                              <tr>
                                  <td bgcolor="#F6F7FB" style="padding: 10px; font-family: 'Montserrat', sans-serif;;font-size: 14px; font-weight: normal; text-align: center; color: #43425d;">
                                      <p style="margin: 0;">
                                          on <span style="font-size:16px;">Content Source '${data.label}'</span> at ${new Date()}
                                      </p>
                                      <p style = "margin: 0;padding: 10px 10px 10px 10px;color: red;font-weight: 600;" >
                                        Number of Documents Crawled: <br/> <span style = "color:#43425d" >${data.count}</span>  </p>
                                  </td>
                              </tr>
                          </table>
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#ffffff">
                          <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                              <tr bgcolor="#ffffff">
                                  <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                                      &nbsp;
                                      
                                  </td>
                              </tr>
                              <tr bgcolor="#ffffff">
                                  <td
                                      style="padding: 9px 40px 40px 40px;font-family: 'Montserrat', sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 12px;font-weight: 400;border-radius: 0% 0% 32% 32%;">
                                      <p style="margin: 0 0 10px 0; font-family: 'Montserrat', sans-serif;;"> Refresh
                                          search client page if you cannot</p>
                                      <p style="margin: 0;">view the updated results immediately</p>
                                  </td>
  
                              </tr>
                              <tr>
                                  <td bgcolor="#F6F7FB">
                                  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                  <tr>
                                      <td style="text-align:center;font-size: 18px;font-weight: 500;">
                                          <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                                              Feel free to reach out to the <br /> SearchUnify team for any
                                              further support .
                                          </p>
                                      </td>
                                  </tr>
                                  <tr style="padding: 8px;">
                                      <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                                          <a href='https://community.searchunify.com/support/' target="_blank"
                                              style="/* text-decoration: none; */font-family: 'Montserrat', sans-serif;font-size: 13px;color: #6da7fb;">
                                              https://community.searchunify.com/support/
                                          </a>
                                      </td>
                                  </tr>

                              </table>
                                  </td>
                              </tr>
                              <tr>
                                  <td>
                                      <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                          <tr>
                                              <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                  &copy; ${new Date().getFullYear()}. All rights reserved
                                              </td>
                                          </tr>
                                          <tr>
                                              <td
                                                  style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif;; font-size: 12px; color: #a6a6a6;text-align: center">
                                                  Email: <a style="text-decoration: none;color:#a6a6a6;"
                                                      href="mailto:<EMAIL>"><EMAIL></a>,<a
                                                      style="text-decoration: none;color:#a6a6a6;"
                                                      href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                              </td>
                                          </tr>
                                      </table>
                                  </td>
                              </tr>
  
                          </table>
                      </td>
                  </tr>
                  <!-- 1 Column Text + Button : END -->
  
              </table>
              <!-- Email Body : END -->
  
              <!-- Email Footer : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
                  style="max-width: 680px;">
                  <tr>
                      <td style="padding: 10px 0px 10px 0px;">
                          <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                              <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                                  <td style="width: 25px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/contact-us/">Contact </a>|
                                  </td>
                                  <td style="width: 40px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/privacy-policy/">Privacy
                                          Policy </a>|
                                  </td>
                                  <td style="width: 68px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                          Conditions</a>
                                  </td>
                              </tr>
                          </table>
                      </td>
                      <td>
                          <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                              <tr>
                                  <td style="text-align:right;padding:20px 0 0 0; ">
                                      <a href="https://www.facebook.com/SearchUnify/" alt="facebook"><img src="${config.get('adminURL')}/resources/Assets/fb.png"></a>
                                      <a href="https://www.linkedin.com/showcase/searchunify/" alt="linkedin"><img src="${config.get('adminURL')}/resources/Assets/linkedin.png"></a>
                                      <a href="https://twitter.com/SearchUnify" alt="tweeter"><img src="${config.get('adminURL')}/resources/Assets/tweeter.png"></a>
                                  </td>
                              </tr>
                          </table>
                      </td>
                  </tr>
                  <tr>
                      <td
                          style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                          <br><br>
  
                      </td>
                  </tr>
              </table>
              <!-- Email Footer : END -->
  
              <!--[if mso]>
              </td>
              </tr>
              </table>
              <![endif]-->
          </div>
  
      </center>
  </body>
  
  </html>
  
    `;


    return template;
  }

  function chattranscripttemplate(user, sessionId){

    var template=`
    <!DOCTYPE html>
  <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
      xmlns:o="urn:schemas-microsoft-com:office:office">
  
  <head>
      <meta charset="utf-8"> <!-- utf-8 works for most cases -->
      <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
      <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
      <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
      <title>Chat Transcript</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
  
      <!-- Web Font / @font-face : BEGIN -->
      <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
  
      <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
      <!--[if mso]>
          <style>
              * {
                  font-family: 'Montserrat',sans-serif !important;
              }
          </style>
      <![endif]-->
  
      <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. More on that here: http://stylecampaign.com/blog/2015/02/webfont-support-in-email/ -->
      <!-- [if !mso] -->
      <!-- insert web font reference, eg: <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'> -->
      <link
          href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
          rel="stylesheet">
      <!-- [endif] -->
  
      <!-- Web Font / @font-face : END -->
  
      <!-- CSS Reset -->
      <style>
          /* What it does: Remove spaces around the email design added by some email clients. */
          /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
          html,
          body {
              margin: 0 auto !important;
              padding: 0 !important;
              height: 100% !important;
              width: 100% !important;
          }
  
          /* What it does: Stops email clients resizing small text. */
          * {
              -ms-text-size-adjust: 100%;
              -webkit-text-size-adjust: 100%;
          }
  
          /* What it does: Centers email on Android 4.4 */
          div[style*="margin: 16px 0"] {
              margin: 0 !important;
          }
  
          /* What it does: Stops Outlook from adding extra spacing to tables. */
          table,
          td {
              mso-table-lspace: 0pt !important;
              mso-table-rspace: 0pt !important;
          }
  
          /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
          table {
              border-spacing: 0 !important;
              border-collapse: collapse !important;
              table-layout: fixed !important;
              margin: 0 auto !important;
          }
  
          table table table {
              table-layout: auto;
          }
  
          /* What it does: Uses a better rendering method when resizing images in IE. */
          img {
              -ms-interpolation-mode: bicubic;
          }
  
          /* What it does: A work-around for email clients meddling in triggered links. */
          *[x-apple-data-detectors],
          /* iOS */
          .x-gmail-data-detectors,
          /* Gmail */
          .x-gmail-data-detectors *,
          .aBn {
              border-bottom: 0 !important;
              cursor: default !important;
              color: inherit !important;
              text-decoration: none !important;
              font-size: inherit !important;
              font-family: inherit !important;
              font-weight: inherit !important;
              line-height: inherit !important;
          }
  
          /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
          .a6S {
              display: none !important;
              opacity: 0.01 !important;
          }
  
          /* If the above doesn't work, add a .g-img class to any image in question. */
          img.g-img+div {
              display: none !important;
          }
  
          /* What it does: Prevents underlining the button text in Windows 10 */
          .button-link {
              text-decoration: none !important;
          }
  
          /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
          /* Create one of these media queries for each additional viewport size you'd like to fix */
          /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
          @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
  
              /* iPhone 6 and 6+ */
              .email-container {
                  min-width: 375px !important;
              }
          }
      </style>
  
      <!-- Progressive Enhancements -->
      <!-- <style>
          /* What it does: Hover styles for buttons */
          .button-td,
          .button-a {
              transition: all 100ms ease-in;
          }
  
          .button-td:hover,
          .button-a:hover {
              background: linear-gradient(263deg, #55c7ff, #7886f7) !important;
              border-color: linear-gradient(263deg, #55c7ff, #7886f7) !important;
          }
  
          /* Media Queries */
          @media screen and (max-width: 600px) {
  
              /* What it does: Adjust typography on small screens to improve readability */
              .email-container p {
                  font-size: 17px !important;
                  line-height: 22px !important;
              }
  
          }
      </style> -->
  
      <!-- What it does: Makes background images in 72ppi Outlook render at correct size. -->
      <!--[if gte mso 9]>
      <xml>
          <o:OfficeDocumentSettings>
              <o:AllowPNG/>
              <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
      </xml>
      <![endif]-->
  
  </head>
  
  <body width="100%"
      style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
      <center style="width: 100%; ">
  
          <!--
              Set the email width. Defined in two places:
              1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
              2. MSO tags for Desktop Windows Outlook enforce a 600px width.
          -->
          <div style="max-width: 600px; margin: auto;" class="email-container">
              <!--[if mso]>
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
              <tr>
              <td>
              <![endif]-->
  
              <!-- Email Header : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                  style="max-width: 600px;">
                  <tr>
                      <td style="padding: 20px 0; text-align: left">
                          <!-- IF logo.src -->
                          <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                          <!-- ELSE -->
                          &nbsp;
                          <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                              style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                          <!-- ENDIF logo.src -->
                      </td>
                  </tr>
              </table>
              <!-- Email Header : END -->
  
              <!-- Email Body : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                  style="max-width: 600px;">
  
                  <!-- Hero Image, Flush : BEGIN -->
                  <tr bgcolor="#F6F7FB">
                      <td
                          style="padding: 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                          <h3
                              style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500;">
                              Hi ${user},</h3>
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#F6F7FB" style="text-align: center;">
                          <img src="${config.get('adminURL')}/resources/Assets/ChatTanscript.png" width="300" height="300" border="0" align="center"
                              style="width: 300px; height: 300px; max-width: 300px;padding-left:4px; height: auto; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;"
                              class="g-img">
  
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#F6F7FB" style="text-align: center;height:30px;">
                      </td>
                  </tr>
  
                  <!-- Hero Image, Flush : END -->
  
                  <!-- 1 Column Text + Button : BEGIN -->
                  <tr>
                      <td bgcolor="#ffffff">
                          <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                          border-radius: 0% 0% 100% 100%;
                          border-bottom: 60px solid #F6F7FB;
                          border-collapse: separate !important;">
                              <tr >
                                  <td bgcolor="#F6F7FB" style="padding: 20px 0px 10px 0px; font-family: 'Montserrat', sans-serif;;font-size: 24px; font-weight: 600; line-height: 20px;text-align: center; color: #43425d;">
                                      <p style="margin: 0;font-weight: 700;">
                                          Chat Transcript
                                      </p>
                                  </td>
                              </tr>
                          </table>
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#ffffff">
                          <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                              <tr bgcolor="#ffffff">
                                  <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                                      &nbsp;
                                      
                                  </td>
                              </tr>
                              <tr bgcolor="#ffffff">
                                  <td
                                      style="padding: 9px 40px 40px 40px;font-family: 'Montserrat', sans-serif;line-height: 20px;color: #43425D;text-align: center;font-size: 14px;font-weight: 500;">
                                      <p style="color:#56C5FF;">${sessionId}</p>
                                      <p style="margin: 0 0 10px 0; font-family: 'Montserrat', sans-serif;;"> Kindly 
                                         find a copy of the chat requested for</p>
                                      <p style="margin: 0;">session ID ${sessionId} attached.</p>
                                  </td>
  
                              </tr>
                              <tr>
                                  <td bgcolor="#F6F7FB">
                                  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                  <tr>
                                      <td style="text-align:center;font-size: 18px;font-weight: 500;">
                                          <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                                              Feel free to reach out to the <br /> SearchUnify team for any
                                              further support .
                                          </p>
                                      </td>
                                  </tr>
                                  <tr style="padding: 8px;">
                                      <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                                          <a href='https://community.searchunify.com/support/' target="_blank"
                                              style="/* text-decoration: none; */font-family: 'Montserrat', sans-serif;font-size: 13px;color: #6da7fb;">
                                              https://community.searchunify.com/support/
                                          </a>
                                      </td>
                                  </tr>

                              </table>
                                  </td>
                              </tr>
                              <tr>
                                  <td>
                                      <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                          <tr>
                                              <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                  &copy; ${new Date().getFullYear()} SearchUnify. All rights reserved
                                              </td>
                                          </tr>
                                          <tr>
                                              <td
                                                  style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif;; font-size: 12px; color: #a6a6a6;text-align: center">
                                                  Email: <a style="text-decoration: none;color:#a6a6a6;"
                                                      href="mailto:<EMAIL>"><EMAIL></a>,<a
                                                      style="text-decoration: none;color:#a6a6a6;"
                                                      href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                              </td>
                                          </tr>
                                      </table>
                                  </td>
                              </tr>
  
                          </table>
                      </td>
                  </tr>
                  <!-- 1 Column Text + Button : END -->
  
              </table>
              <!-- Email Body : END -->
  
              <!-- Email Footer : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
                  style="max-width: 680px;">
                  <tr>
                      <td style="padding: 10px 0px 10px 0px;">
                          <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                              <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                                  <td style="width: 25px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/contact-us/">Contact </a>|
                                  </td>
                                  <td style="width: 40px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/privacy-policy/">Privacy
                                          Policy </a>|
                                  </td>
                                  <td style="width: 68px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                          Conditions</a>
                                  </td>
                              </tr>
                          </table>
                      </td>
                      <td>
                          <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                              <tr>
                                  <td style="text-align:right;padding:20px 0 0 0; ">
                                      <a href="https://www.facebook.com/SearchUnify/" alt="facebook"><img src="${config.get('adminURL')}/resources/Assets/fb.png"></a>
                                      <a href="https://www.linkedin.com/showcase/searchunify/" alt="linkedin"><img src="${config.get('adminURL')}/resources/Assets/linkedin.png"></a>
                                      <a href="https://twitter.com/SearchUnify" alt="tweeter"><img src="${config.get('adminURL')}/resources/Assets/tweeter.png"></a>
                                  </td>
                              </tr>
                          </table>
                      </td>
                  </tr>
                  <tr>
                      <td
                          style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                          <br><br>
  
                      </td>
                  </tr>
              </table>
              <!-- Email Footer : END -->
  
              <!--[if mso]>
              </td>
              </tr>
              </table>
              <![endif]-->
          </div>
  
      </center>
  </body>
  
  </html>
  
    `;


    return template;
  }

function verifyUser(email, token) {

    var template = `<!DOCTYPE html>
    <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
        xmlns:o="urn:schemas-microsoft-com:office:office">
    
    <head>
        <meta charset="utf-8"> <!-- utf-8 works for most cases -->
        <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
        <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
        <title>Welcome to SearchUnify</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
    
        <!-- Web Font / @font-face : BEGIN -->
        <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
    
        <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
        <!--[if mso]>
            <style>
                * {
                    font-family: 'Montserrat',sans-serif !important;
                }
            </style>
        <![endif]-->
    
        <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. More on that here: http://stylecampaign.com/blog/2015/02/webfont-support-in-email/ -->
        <!-- [if !mso] -->
        <!-- insert web font reference, eg: <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'> -->
        <link
            href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
            rel="stylesheet">
        <!-- [endif] -->
    
        <!-- Web Font / @font-face : END -->
    
        <!-- CSS Reset -->
        <style>
            /* What it does: Remove spaces around the email design added by some email clients. */
            /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
            html,
            body {
                margin: 0 auto !important;
                padding: 0 !important;
                height: 100% !important;
                width: 100% !important;
            }
    
            /* What it does: Stops email clients resizing small text. */
            * {
                -ms-text-size-adjust: 100%;
                -webkit-text-size-adjust: 100%;
            }
    
            /* What it does: Centers email on Android 4.4 */
            div[style*="margin: 16px 0"] {
                margin: 0 !important;
            }
    
            /* What it does: Stops Outlook from adding extra spacing to tables. */
            table,
            td {
                mso-table-lspace: 0pt !important;
                mso-table-rspace: 0pt !important;
            }
    
            /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
            table {
                border-spacing: 0 !important;
                border-collapse: collapse !important;
                table-layout: fixed !important;
                margin: 0 auto !important;
            }
    
            table table table {
                table-layout: auto;
            }
    
            /* What it does: Uses a better rendering method when resizing images in IE. */
            img {
                -ms-interpolation-mode: bicubic;
            }
    
            /* What it does: A work-around for email clients meddling in triggered links. */
            *[x-apple-data-detectors],
            /* iOS */
            .x-gmail-data-detectors,
            /* Gmail */
            .x-gmail-data-detectors *,
            .aBn {
                border-bottom: 0 !important;
                cursor: default !important;
                color: inherit !important;
                text-decoration: none !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
            }
    
            /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
            .a6S {
                display: none !important;
                opacity: 0.01 !important;
            }
    
            /* If the above doesn't work, add a .g-img class to any image in question. */
            img.g-img+div {
                display: none !important;
            }
    
            /* What it does: Prevents underlining the button text in Windows 10 */
            .button-link {
                text-decoration: none !important;
            }
    
            /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
            /* Create one of these media queries for each additional viewport size you'd like to fix */
            /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
            @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
    
                /* iPhone 6 and 6+ */
                .email-container {
                    min-width: 375px !important;
                }
            }
        </style>
    
        <!-- Progressive Enhancements -->
        <!-- <style>
            /* What it does: Hover styles for buttons */
            .button-td,
            .button-a {
                transition: all 100ms ease-in;
            }
    
            .button-td:hover,
            .button-a:hover {
                background: linear-gradient(263deg, #55c7ff, #7886f7) !important;
                border-color: linear-gradient(263deg, #55c7ff, #7886f7) !important;
            }
    
            /* Media Queries */
            @media screen and (max-width: 600px) {
    
                /* What it does: Adjust typography on small screens to improve readability */
                .email-container p {
                    font-size: 17px !important;
                    line-height: 22px !important;
                }
    
            }
        </style> -->
    
        <!-- What it does: Makes background images in 72ppi Outlook render at correct size. -->
        <!--[if gte mso 9]>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
        <![endif]-->
    
    </head>
    
    <body width="100%"
        style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
        <center style="width: 100%; ">
    
            <!--
                Set the email width. Defined in two places:
                1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
                2. MSO tags for Desktop Windows Outlook enforce a 600px width.
            -->
            <div style="max-width: 600px; margin: auto;" class="email-container">
                <!--[if mso]>
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
                <tr>
                <td>
                <![endif]-->
    
                <!-- Email Header : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
                    <tr>
                        <td style="padding: 20px 0; text-align: left">
                            <!-- IF logo.src -->
                            <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                            <!-- ELSE -->
                            &nbsp;
                            <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                                style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <!-- ENDIF logo.src -->
                        </td>
                    </tr>
                </table>
                <!-- Email Header : END -->
    
                <!-- Email Body : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
    
                    <!-- Hero Image, Flush : BEGIN -->
                    <tr bgcolor="#F6F7FB">
                        <td
                            style="padding: 40px 40px 20px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <h3
                                style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500;">
                                Hi ${email},</h3>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;">
                            <img src="${config.get('adminURL')}/resources/Assets/otp.png" width="300" height="300" border="0" align="center"
                                style="width: 300px; height: 300px; max-width: 300px;padding-left:4px; height: auto; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;"
                                class="g-img">
    
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;height:30px;">
                        </td>
                    </tr>
    
                    <!-- Hero Image, Flush : END -->
    
                    <!-- 1 Column Text + Button : BEGIN -->
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                            border-radius: 0% 0% 100% 100%;
                            border-bottom: 40px solid #F6F7FB;
                            border-collapse: separate !important;">
                                <tr >
                                    <td bgcolor="#F6F7FB" style="padding: 10px 0px 0px 0px; font-family: 'Montserrat', sans-serif;;font-size: 24px; font-weight: 600; line-height: 20px;text-align: center; color: #43425d;">
                                        <p style="margin: 0; font-family: 'Montserrat', sans-serif; font-size: 13px;">Please use the verification code below </p>
                                        <p style="margin: 0; font-family: 'Montserrat', sans-serif; font-size: 13px;">on the SearchUnify Admin Portal to confirm your identity</p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr bgcolor="#ffffff">
                                    <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                                        &nbsp;
                                        
                                    </td>
                                </tr>
                                <tr bgcolor="#ffffff">
                                    <td
                                        style="padding: 9px 40px 10px 40px;font-family: 'Montserrat', sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 12px;font-weight: 400;border-radius: 0% 0% 32% 32%;">
                                        <table>
                                        <tr>
                                            <td style="margin: 10; margin: 10px; font-size: 26px; font-weight: 600; letter-spacing: 2px;background: #eaeaea;border-radius: 4px;padding: 8px 15px;">
                                                ${token}
                                            </td>
                                        </tr>
                                        </table>
                                        <p style="margin: 0; padding: 0px;"></p>(The verification code will expire in 5 minutes)</p>
                                    </td>
    
                                </tr>
                              </tr>
                                <tr>
                                    <td bgcolor="#F6F7FB">
                                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                    <tr>
                                        <td style="text-align:center;font-size: 18px;font-weight: 500;">
                                            <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat', sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                                                Feel free to reach out to the <br /> SearchUnify team for any
                                                further support.
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="padding: 8px;">
                                        <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                                            <a href='https://community.searchunify.com/support/' target="_blank"
                                                style="/* text-decoration: none; */font-family: 'Montserrat', sans-serif;font-size: 13px;color: #6da7fb;">
                                                https://community.searchunify.com/support/
                                            </a>
                                        </td>
                                    </tr>
  
                                </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                    &copy; ${new Date().getFullYear()}. All rights reserved
                                                </td>
                                            </tr>
                                            <tr>
                                                <td
                                                    style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif;; font-size: 12px; color: #a6a6a6;text-align: center">
                                                    Email: <a style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:<EMAIL>"><EMAIL></a>, <a
                                                        style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
    
                            </table>
                        </td>
                    </tr>
                    <!-- 1 Column Text + Button : END -->
    
                </table>
                <!-- Email Body : END -->
    
                <!-- Email Footer : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
                    style="max-width: 680px;">
                    <tr>
                        <td style="padding: 10px 0px 10px 0px;">
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                                    <td style="width: 25px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/contact-us/">Contact </a>|
                                    </td>
                                    <td style="width: 40px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/privacy-policy/">Privacy
                                            Policy </a>|
                                    </td>
                                    <td style="width: 68px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                            Conditions</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td>
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.facebook.com/SearchUnify/" alt="facebook"><img src="${config.get('adminURL')}/resources/Assets/fb.png"></a>
                                        <a href="https://www.linkedin.com/showcase/searchunify/" alt="linkedin"><img src="${config.get('adminURL')}/resources/Assets/linkedin.png"></a>
                                        <a href="https://twitter.com/SearchUnify" alt="tweeter"><img src="${config.get('adminURL')}/resources/Assets/tweeter.png"></a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td
                            style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                            <br><br>
    
                        </td>
                    </tr>
                </table>
                <!-- Email Footer : END -->
    
                <!--[if mso]>
                </td>
                </tr>
                </table>
                <![endif]-->
            </div>
    
        </center>
    </body>
    
    </html>
    `;
    return template;

}
function botTrain(email, name) {
    var msg = "";
    var template = `<!DOCTYPE html>
    <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
        xmlns:o="urn:schemas-microsoft-com:office:office">
    
    <head>
        <meta charset="utf-8"> <!-- utf-8 works for most cases -->
        <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
        <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
        <title>Reset Password</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
    
        <!-- Web Font / @font-face : BEGIN -->
        <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
    
        <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
        <!--[if mso]>
            <style>
                * {
                    font-family: 'Montserrat',sans-serif !important;
                }
            </style>
        <![endif]-->
    
        <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. More on that here: http://stylecampaign.com/blog/2015/02/webfont-support-in-email/ -->
        <!-- [if !mso] -->
        <!-- insert web font reference, eg: <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'> -->
        <link
            href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
            rel="stylesheet">
        <!-- [endif] -->
    
        <!-- Web Font / @font-face : END -->
    
        <!-- CSS Reset -->
        <style>
            /* What it does: Remove spaces around the email design added by some email clients. */
            /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
            html,
            body {
                margin: 0 auto !important;
                padding: 0 !important;
                height: 100% !important;
                width: 100% !important;
            }
    
            /* What it does: Stops email clients resizing small text. */
            * {
                -ms-text-size-adjust: 100%;
                -webkit-text-size-adjust: 100%;
            }
    
            /* What it does: Centers email on Android 4.4 */
            div[style*="margin: 16px 0"] {
                margin: 0 !important;
            }
    
            /* What it does: Stops Outlook from adding extra spacing to tables. */
            table,
            td {
                mso-table-lspace: 0pt !important;
                mso-table-rspace: 0pt !important;
            }
    
            /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
            table {
                border-spacing: 0 !important;
                border-collapse: collapse !important;
                table-layout: fixed !important;
                margin: 0 auto !important;
            }
    
            table table table {
                table-layout: auto;
            }
    
            /* What it does: Uses a better rendering method when resizing images in IE. */
            img {
                -ms-interpolation-mode: bicubic;
            }
    
            /* What it does: A work-around for email clients meddling in triggered links. */
            *[x-apple-data-detectors],
            /* iOS */
            .x-gmail-data-detectors,
            /* Gmail */
            .x-gmail-data-detectors *,
            .aBn {
                border-bottom: 0 !important;
                cursor: default !important;
                color: inherit !important;
                text-decoration: none !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
            }
    
            /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
            .a6S {
                display: none !important;
                opacity: 0.01 !important;
            }
    
            /* If the above doesn't work, add a .g-img class to any image in question. */
            img.g-img+div {
                display: none !important;
            }
    
            /* What it does: Prevents underlining the button text in Windows 10 */
            .button-link {
                text-decoration: none !important;
            }
    
            /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
            /* Create one of these media queries for each additional viewport size you'd like to fix */
            /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
            @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
    
                /* iPhone 6 and 6+ */
                .email-container {
                    min-width: 375px !important;
                }
            }
        </style>
    
        <!-- Progressive Enhancements -->
        <!-- <style>
            /* What it does: Hover styles for buttons */
            .button-td,
            .button-a {
                transition: all 100ms ease-in;
            }
    
            .button-td:hover,
            .button-a:hover {
                background: linear-gradient(263deg, #55c7ff, #7886f7) !important;
                border-color: linear-gradient(263deg, #55c7ff, #7886f7) !important;
            }
    
            /* Media Queries */
            @media screen and (max-width: 600px) {
    
                /* What it does: Adjust typography on small screens to improve readability */
                .email-container p {
                    font-size: 17px !important;
                    line-height: 22px !important;
                }
    
            }
        </style> -->
    
        <!-- What it does: Makes background images in 72ppi Outlook render at correct size. -->
        <!--[if gte mso 9]>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
        <![endif]-->
    
    </head>
    
    <body width="100%"
        style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
        <center style="width: 100%; ">
    
            <!--
                Set the email width. Defined in two places:
                1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
                2. MSO tags for Desktop Windows Outlook enforce a 600px width.
            -->
            <div style="max-width: 600px; margin: auto;" class="email-container">
                <!--[if mso]>
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
                <tr>
                <td>
                <![endif]-->
    
                <!-- Email Header : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
                    <tr>
                        <td style="padding: 20px 0; text-align: left">
                            <!-- IF logo.src -->
                            <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                            <!-- ELSE -->
                            &nbsp;
                            <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                                style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <!-- ENDIF logo.src -->
                        </td>
                    </tr>
                </table>
                <!-- Email Header : END -->
    
                <!-- Email Body : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
    
                    <!-- Hero Image, Flush : BEGIN -->
                    <tr bgcolor="#F6F7FB">
                        <td
                            style="padding: 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                            <h3
                                style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500;">
                                Hi ${email},</h3>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;">
                            <img src="${config.get('adminURL')}/resources/Assets/bot_train.png" width="300" height="300" border="0" align="center"
                                style="width: 300px; height: 300px; max-width: 300px;padding-left:4px; height: auto; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;"
                                class="g-img">
    
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#F6F7FB" style="text-align: center;height:30px;">
                        </td>
                    </tr>
    
                    <!-- Hero Image, Flush : END -->
    
                    <!-- 1 Column Text + Button : BEGIN -->
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                            border-radius: 0% 0% 100% 100%;
                            border-bottom: 60px solid #F6F7FB;
                            border-collapse: separate !important;">
                                <tr >
                                    <td bgcolor="#F6F7FB" style="padding: 20px 0px 10px 0px; font-family: 'Montserrat', sans-serif;;font-size: 24px; font-weight: 600; line-height: 20px;text-align: center; color: #43425d;">
                                        <p style="margin: 0;">
                                            Bot Trained Successfully
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr bgcolor="#ffffff">
                                    <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                                        &nbsp;
                                        
                                    </td>
                                </tr>
                                <tr bgcolor="#ffffff">
                                    <td
                                        style="padding: 9px 40px 10px 40px;font-family: 'Montserrat', sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 12px;font-weight: 400;border-radius: 0% 0% 32% 32%;">
                                        <p style="margin: 0; font-family: 'Montserrat', sans-serif;color:#43425D;">Your training for the agent name called</p>
                                        <p style="margin: 0; color: #56C5FF;font-size:16px">${name}</p>
                                        <p style="margin: 0; font-family: 'Montserrat', sans-serif;color:#43425D;">has been completed successfully. You may now run your</p>
                                        <p style="margin: 0; font-family: 'Montserrat', sans-serif;color:#43425D;">stories successfully on the respective platform</p>
                                    </td>
    
                                </tr>
                              </tr>
                                <tr>
                                    <td bgcolor="#F6F7FB">
                                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                    <tr>
                                        <td style="text-align:center;font-size: 18px;font-weight: 500;">
                                            <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                                                Feel free to reach out to the <br /> SearchUnify team for any
                                                further support.
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="padding: 8px;">
                                        <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                                            <a href='https://community.searchunify.com/support/' target="_blank"
                                                style="/* text-decoration: none; */font-family: 'Montserrat', sans-serif;font-size: 13px;color: #6da7fb;">
                                                https://community.searchunify.com/support/
                                            </a>
                                        </td>
                                    </tr>
  
                                </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                    &copy; ${new Date().getFullYear()} SearchUnify. All rights reserved
                                                </td>
                                            </tr>
                                            <tr>
                                                <td
                                                    style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif;; font-size: 12px; color: #a6a6a6;text-align: center">
                                                    Email: <a style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:<EMAIL>"><EMAIL></a>, <a
                                                        style="text-decoration: none;color:#a6a6a6;"
                                                        href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
    
                            </table>
                        </td>
                    </tr>
                    <!-- 1 Column Text + Button : END -->
    
                </table>
                <!-- Email Body : END -->
    
                <!-- Email Footer : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
                    style="max-width: 680px;">
                    <tr>
                        <td style="padding: 10px 0px 10px 0px;">
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                                    <td style="width: 25px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/contact-us/">Contact </a>|
                                    </td>
                                    <td style="width: 40px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/privacy-policy/">Privacy
                                            Policy </a>|
                                    </td>
                                    <td style="width: 68px !important;padding:20px 0 0 0;">
                                        <a style="text-decoration: none;color:#2B2B2B;"
                                            href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                            Conditions</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td>
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.facebook.com/SearchUnify/" alt="facebook"><img src="${config.get('adminURL')}/resources/Assets/fb.png"></a>
                                        <a href="https://www.linkedin.com/showcase/searchunify/" alt="linkedin"><img src="${config.get('adminURL')}/resources/Assets/linkedin.png"></a>
                                        <a href="https://twitter.com/SearchUnify" alt="tweeter"><img src="${config.get('adminURL')}/resources/Assets/tweeter.png"></a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td
                            style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                            <br><br>
    
                        </td>
                    </tr>
                </table>
                <!-- Email Footer : END -->
    
                <!--[if mso]>
                </td>
                </tr>
                </table>
                <![endif]-->
            </div>
    
        </center>
    </body>
    
    </html>
      `;
    return template;
  }

 function reindexingSuccessTemplate(email, searchClientName) {
    const template = `<link
    href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
    rel="stylesheet">
    <style>
    / What it does: Remove spaces around the email design added by some email clients. /
    / Beware: It can remove the padding / margin and add a background color to the compose a reply window. /
    html,
    body {
    margin: 0 auto !important;
    padding: 0 !important;
    height: 100% !important;
    width: 100% !important;
    }
    
    / What it does: Stops email clients resizing small text. /
    * {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    }
    
    / What it does: Centers email on Android 4.4 /
    div[style*="margin: 16px 0"] {
    margin: 0 !important;
    }
    
    / What it does: Stops Outlook from adding extra spacing to tables. /
    table,
    td {
    mso-table-lspace: 0pt !important;
    mso-table-rspace: 0pt !important;
    }
    
    / What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. /
    table {
    border-spacing: 0 !important;
    border-collapse: collapse !important;
    table-layout: fixed !important;
    margin: 0 auto !important;
    }
    
    table table table {
    table-layout: auto;
    }
    img {
    -ms-interpolation-mode: bicubic;
    }
    
    .x-gmail-data-detectors *,
    .aBn {
    border-bottom: 0 !important;
    cursor: default !important;
    color: inherit !important;
    text-decoration: none !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    }
    
    / What it does: Prevents Gmail from displaying an download button on large, non-linked images. /
    .a6S {
    display: none !important;
    opacity: 0.01 !important;
    }
    img.g-img+div {
    display: none !important;
    }
    .button-link {
    text-decoration: none !important;
    }
    
    @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
    .email-container {
    min-width: 375px !important;
    }
    }
    </style>
    </head>
    <body width="100%"
    style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
    <center style="width: 100%; ">
    <div style="max-width: 600px; margin: auto;" class="email-container">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
    style="max-width: 600px;">
    <tr>
    <td style="padding: 20px 0; text-align: left">
    &nbsp;
    <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
    style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
    </td>
    </tr>
    </table>
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
    style="max-width: 600px;">
    <tr bgcolor="#F6F7FB">
    <td
    style="padding:30px 30px 10px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555; font-weight: 500;">
    
    Hi ${email},</h3>
    </td>
    </tr>
    <tr>
    <td bgcolor="#ffffff">
    <table role="presentation" cellspacing="0" cellpadding="0" width="100%">
    <tr>
    <td bgcolor="#F6F7FB" style="font-weight: 500;color: #43425d;font-size: 14px;">
    <div style="margin: 00px 40px 0px 40px;border-radius: 5px; display:block;text-align: left;font-family: 'Montserrat', sans-serif;font-size: 13px;color:#43425D;">
    
    
    <p style='min-height: 25px;'>Content reindexing for Merge results on ${searchClientName} Search Client is complete</p>
    <p style="font-style: italic;min-height: 25px;">Note:  Search results are grouped based on the versioning field set from the Admin Panel.</p>
    
    <ol type="1" style="padding-left: 0px !important;text-align: left;font-family: 'Montserrat', sans-serif;font-size: 13px;color:#43425D;margin-bottom: 30px;">
    <li style='display: inline;font-weight: 600;margin-left:0px !important'>Search Client</li>
    <li style='display: inline;padding-left: 95px;'>${searchClientName}</li>
    </ol>
    </div>
    </td>
    </tr>
    </table>
    </td>
    </tr>
    <tr>
    <td bgcolor="#ffffff">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
    <tr bgcolor="#ffffff">
    <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
    &nbsp;
    
    </td>
    </tr>
    <tr>
    <td bgcolor="#F6F7FB">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="padding: 15px;">
    <tr>
    <td style="text-align:center;font-weight: 500;">
    <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 15px;font-weight: 600;margin-bottom: 0;">
    Feel free to reach out to the <br /> SearchUnify team for any
    further support .
    </p>
    </td>
    </tr>
    <tr style="padding: 8px;">
    <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
    <a href='https://help.searchunify.com/portal/home' target="_blank"
    style="text-decoration: none;font-family: 'Montserrat', sans-serif;font-size: 12px;color:#43425D;">
    SearchUnify Support
    </a>
    </td>
    </tr>
    
    </table>
    </td>
    </tr>
    <tr>
    <td style="padding: 20px;">
    <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
    <tr>
    <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
    &copy; ${new Date().getFullYear()} SearchUnify. All rights reserved
    </td>
    </tr>
    <tr>
    <td
    style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif; font-size: 12px; text-align: center; font-weight: 600;">
    Email: <a style="text-decoration: underline;"
    href="mailto:<EMAIL>"><EMAIL></a>,<a
    style="text-decoration: underline;"
    href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
    </td>
    </tr>
    </table>
    </td>
    </tr>
    
    </table>
    </td>
    </tr>
    
    </table>
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
    style="max-width: 680px;">
    <tr>
    <td style="padding: 10px 0px 10px 0px;">
    <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
    <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
    <td style="width: 25px !important;padding:20px 0 0 0;">
    <a style="text-decoration: none;font-weight: 600;"
    href="mailto:<EMAIL>">Contact </a>
    </td>
    <td style="width: 40px !important;padding:20px 0 0 0;">
    <a style="text-decoration: none;font-weight: 600;"
    href="https://www.searchunify.com/privacy-policy/">Privacy
    Policy </a>
    </td>
    <td style="width: 68px !important;padding:20px 0 0 0;">
    <a style="text-decoration: none;font-weight: 600;"
    href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
    Conditions</a>
    </td>
    </tr>
    </table>
    </td>
    <td>
    <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
    <tr>
    <td style="text-align:right;padding:20px 0 0 0; ">
    <a href="#" alt="facebook"><img src="${config.get('adminURL')}/resources/Assets/fb.png"></a>
    <a href="#" alt="linkedin"><img src="${config.get('adminURL')}/resources/Assets/linkedin.png"></a>
    <a href="#" alt="tweeter"><img src="${config.get('adminURL')}/resources/Assets/tweeter.png"></a>
    </td>
    </tr>
    </table>
    </td>
    </tr>
    <tr>
    <td
    style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
    <br><br>
    
    </td>
    </tr>
    </table>
    </div>
    
    </center>
    </body>`;
    return template;
 }
  const searchResultsEmailTemplate = (
    email,
    dateFrom,
    dateTo,
    dateBetween,
    contentSourceName,
    reportLabel,
    fromTestTuning
  ) => {
    const template = `<link
      href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
      rel="stylesheet">
      <style>
      / What it does: Remove spaces around the email design added by some email clients. /
      / Beware: It can remove the padding / margin and add a background color to the compose a reply window. /
      html,
      body {
      margin: 0 auto !important;
      padding: 0 !important;
      height: 100% !important;
      width: 100% !important;
      }
      
      / What it does: Stops email clients resizing small text. /
      * {
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
      }
      
      / What it does: Centers email on Android 4.4 /
      div[style*="margin: 16px 0"] {
      margin: 0 !important;
      }
      
      / What it does: Stops Outlook from adding extra spacing to tables. /
      table,
      td {
      mso-table-lspace: 0pt !important;
      mso-table-rspace: 0pt !important;
      }
      
      / What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. /
      table {
      border-spacing: 0 !important;
      border-collapse: collapse !important;
      table-layout: fixed !important;
      margin: 0 auto !important;
      }
      
      table table table {
      table-layout: auto;
      }
      img {
      -ms-interpolation-mode: bicubic;
      }
      
      .x-gmail-data-detectors *,
      .aBn {
      border-bottom: 0 !important;
      cursor: default !important;
      color: inherit !important;
      text-decoration: none !important;
      font-size: inherit !important;
      font-family: inherit !important;
      font-weight: inherit !important;
      line-height: inherit !important;
      }
      
      / What it does: Prevents Gmail from displaying an download button on large, non-linked images. /
      .a6S {
      display: none !important;
      opacity: 0.01 !important;
      }
      img.g-img+div {
      display: none !important;
      }
      .button-link {
      text-decoration: none !important;
      }
      
      @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
      .email-container {
      min-width: 375px !important;
      }
      }
      </style>
      </head>
      <body width="100%"
      style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
      <center style="width: 100%; ">
      <div style="max-width: 600px; margin: auto;" class="email-container">
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
      style="max-width: 600px;">
      <tr>
      <td style="padding: 20px 0; text-align: left">
      &nbsp;
      <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
      style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
      </td>
      </tr>
      </table>
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
      style="max-width: 600px;">
      <tr bgcolor="#F6F7FB">
      <td
      style="padding: 20px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
      <h2 style = "text-align: center;line-height: 30px; font-family: 'Montserrat', sans-serif;text-transform: capitalize;">${reportLabel} Report</h2>
      <hr>
      <h3 style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500; margin-top: 20px;padding-left: 25px;">
      Hi ${email},</h3>
      </td>
      </tr>
      <tr>
      <td bgcolor="#ffffff">
      <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="
      border-radius: 0% 0% 100% 100%;
      border-bottom: 60px solid #F6F7FB;
      border-collapse: separate !important;">
      <tr>
      <td bgcolor="#F6F7FB" style="font-weight: 500;color: #43425d;font-size: 14px;">
      <div style="margin: 0px 30px 0px 30px;border-radius: 5px; display: ${
    (!fromTestTuning && dateFrom && dateTo && contentSourceName) || (fromTestTuning && contentSourceName) ? 'block' : 'none' }">
      <ol type="1" style="text-align: left;font-family: 'Montserrat', sans-serif;font-size: 14px;color:#43425D;">
      <li style='display: inline;'>The report you have requested is ready</li>
      </ol>
      <ol type="1" style="text-align: left;font-family: 'Montserrat', sans-serif;font-size: 14px;color:#43425D;display:${fromTestTuning?'none':'block'};">
      <li style='display: inline;'>Download Period</li>
      <li style='display: inline;padding-left:70px'>${dateFrom} ${dateBetween} ${dateTo}</li>
      </ol>
      <ol type="1" style="text-align: left;font-family: 'Montserrat', sans-serif;font-size: 14px;color:#43425D;">
      <li style='display: inline;'>${fromTestTuning ? 'Search Client' : 'Content Source'}</li>
      <li style='display: inline;padding-left: 95px;'>${contentSourceName || ''} </li>
      </ol>
      </div>
      </td>
      </tr>
      </table>
      </td>
      </tr>
      <tr>
      <td bgcolor="#ffffff">
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
      <tr bgcolor="#ffffff">
      <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
      &nbsp;
      
      </td>
      </tr>
      <tr bgcolor="#ffffff">
      <td
      style="padding: 9px 40px 40px 40px;font-family: 'Montserrat', sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 400;">
      <p style="margin: 0 0 10px 0; font-family: 'Montserrat', sans-serif;">Download the Attached Report</p>
      </td>
      </tr>
      <tr>
      <td bgcolor="#F6F7FB">
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
      <tr>
      <td style="text-align:center">
      <p style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
      Feel free to reach out to the <br /> SearchUnify team for any
      further support .
      </p>
      </td>
      </tr>
      <tr style="padding: 8px;">
      <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
      <a href='https://help.searchunify.com/portal/home' target="_blank"
      style="text-decoration: none;font-family: 'Montserrat', sans-serif;font-size: 12px;color:#43425D;">
      SearchUnify Support
      </a>
      </td>
      </tr>
      
      </table>
      </td>
      </tr>
      <tr>
      <td>
      <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
      <tr>
      <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
      &copy; ${new Date().getFullYear()} SearchUnify. All rights reserved
      </td>
      </tr>
      <tr>
      <td
      style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif; font-size: 12px; text-align: center; font-weight: 600;">
      Email: <a style="text-decoration: underline;"
      href="mailto:<EMAIL>"><EMAIL></a>,<a
      style="text-decoration: underline;"
      href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
      </td>
      </tr>
      </table>
      </td>
      </tr>
      
      </table>
      </td>
      </tr>
      
      </table>
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
      style="max-width: 680px;">
      <tr>
      <td style="padding: 10px 0px 10px 0px;">
      <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
      <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
      <td style="width: 25px !important;padding:20px 0 0 0;">
      <a style="text-decoration: none;font-weight: 600;"
      href="mailto:<EMAIL>">Contact </a>
      </td>
      <td style="width: 40px !important;padding:20px 0 0 0;">
      <a style="text-decoration: none;font-weight: 600;"
      href="https://www.searchunify.com/privacy-policy/">Privacy
      Policy </a>
      </td>
      <td style="width: 68px !important;padding:20px 0 0 0;">
      <a style="text-decoration: none;font-weight: 600;"
      href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
      Conditions</a>
      </td>
      </tr>
      </table>
      </td>
      <td>
        <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
            <tr>
                <td style="text-align:right;padding:20px 0 0 0; ">
                    <a href="https://www.facebook.com/SearchUnify/"><img
                            src="${config.get('adminURL')}/resources/Assets/analytics_fb.png" style="width: 30px; margin-right: 15px;"></a>
                </td>
                <td style="text-align:right;padding:20px 0 0 0; ">
                    <a href="https://www.linkedin.com/showcase/25048226/"><img
                        src="${config.get('adminURL')}/resources/Assets/analytics_linkedin.png" style="width: 30px; margin-right: 15px;"></a>
                </td>
                <td style="text-align:right;padding:20px 0 0 0; ">
                    <a href="https://twitter.com/SearchUnify"><img
                        src="${config.get('adminURL')}/resources/Assets/twitter.png" style="width: 30px; margin-right: 15px;"></a>
                </td>
                <td style="text-align:right;padding:20px 0 0 0; ">
                    <a href="https://www.youtube.com/channel/UCHQdHTFzWRKj5xg1nFoZPTg"><img
                        src="${config.get('adminURL')}/resources/Assets/analytics_youtube.png" style="width: 30px;"></a>
                </td>
            </tr>
        </table>
    </td>
      </tr>
      <tr>
      <td
      style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
      <br><br>
      
      </td>
      </tr>
      </table>
      </div>
      
      </center>
      </body>`;
      return template;
  };

  const mergeFacetEmailTemplate = (
    mergeFacetParentName,
    email,
    searchClientName
  ) => {
    const template = `<link href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
    rel="stylesheet">
  <style>
    / What it does: Remove spaces around the email design added by some email clients. / / Beware: It can remove the padding / margin and add a background color to the compose a reply window. / html,
    body {
      margin: 0 auto !important;
      padding: 0 !important;
      height: 100% !important;
      width: 100% !important;
    }
  
    / What it does: Stops email clients resizing small text. / * {
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
    }
  
    / What it does: Centers email on Android 4.4 / div[style*="margin: 16px 0"] {
      margin: 0 !important;
    }
  
    / What it does: Stops Outlook from adding extra spacing to tables. / table,
    td {
      mso-table-lspace: 0pt !important;
      mso-table-rspace: 0pt !important;
    }
  
    / What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. / table {
      border-spacing: 0 !important;
      border-collapse: collapse !important;
      table-layout: fixed !important;
      margin: 0 auto !important;
    }
  
    table table table {
      table-layout: auto;
    }
  
    img {
      -ms-interpolation-mode: bicubic;
    }
  
    .x-gmail-data-detectors *,
    .aBn {
      border-bottom: 0 !important;
      cursor: default !important;
      color: inherit !important;
      text-decoration: none !important;
      font-size: inherit !important;
      font-family: inherit !important;
      font-weight: inherit !important;
      line-height: inherit !important;
    }
  
    / What it does: Prevents Gmail from displaying an download button on large,
    non-linked images. / .a6S {
      display: none !important;
      opacity: 0.01 !important;
    }
  
    img.g-img+div {
      display: none !important;
    }
  
    .button-link {
      text-decoration: none !important;
    }
  
    @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
      .email-container {
        min-width: 375px !important;
      }
    }
  </style>
  </head>
  
  <body width="100%"
    style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
    <center style="width: 100%; ">
      <div style="max-width: 600px; margin: auto;" class="email-container">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
          style="max-width: 600px;">
          <tr>
            <td style="padding: 20px 0; text-align: left">
              &nbsp;
              <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo"
                border="0"
                style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
            </td>
          </tr>
        </table>
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
          style="max-width: 600px;background: white;">
          <tr bgcolor="#F6F7FB">
            <td
              style="text-align: center;
        border-collapse: separate !important;padding: 20px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
              <h3
                style="text-align: left;margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500; margin-top: 20px;padding-left: 25px;">
                Hi ${email},</h3>
                <img src="${config.get('adminURL')}/resources/Assets/mergeFacetEmail.png"
                alt="Merge facet Email logo">
            </td>
          </tr>
          <tr bgcolor="#F6F7FB">
          <td
            style="border-radius: 0% 0% 40% 40%;
      border-collapse: separate !important;padding: 20px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555; text-align: center">
              <span style="font-size: 19px;
              color: #43425D;
              font-weight: 600;">Merged Facets</span>
          </td>
        </tr>
          <tr>
            <td bgcolor="#ffffff">
              <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="
        display: none;
        border-radius: 0% 0% 100% 100%;
        border-bottom: 60px solid #F6F7FB;
        border-collapse: separate !important;">
                <tr>
                  <td bgcolor="#F6F7FB" style="font-weight: 500;color: #43425d;font-size: 14px;">
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
            <td bgcolor="#ffffff">
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                <tr bgcolor="#ffffff">
                  <td>
                    <div style="margin: 0px 30px 0px 30px;border-radius: 5px;padding: 25px; ">
                        <table style="margin: auto;">
                            <tr>
                                <td style="padding: 10px 45px 0px 10px;">Search Client</td>
                                <td style="padding: 10px 45px 0px 10px;">: ${searchClientName || ''}</td>
                            </tr>
                            <tr>
                                <td style="padding: 10px 45px 0px 10px;">Merged Facets</td>
                                <td style="padding: 10px 45px 0px 10px;"><div style="height: 24px;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                /* width: 374px; */
                                max-width: 400px;"> :  ${mergeFacetParentName || ''}</div></td>
                            </tr>
                    </table>
                    </div>
  
                  </td>
                </tr>
                <tr>
                  <td bgcolor="#F6F7FB">
                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                      <tr>
                        <td style="text-align:center;font-weight: 500;">
                          <p
                            style="padding:15px 40px 5px 40px;font-family: 'Montserrat'; sans-serif;line-height: 24px;color: #43425D;text-align: center;font-size: 18px;font-weight: 500;margin-bottom: 0;">
                            Feel free to reach out to the <br /> SearchUnify team for any
                            further support .
                          </p>
                        </td>
                      </tr>
                      <tr style="padding: 8px;">
                        <td style="text-align: center;color:#43425D;padding: 0px 10px 25px 10px">
                          <a href='https://help.searchunify.com/portal/home' target="_blank"
                            style="text-decoration: none;font-family: 'Montserrat', sans-serif;font-size: 12px;color:#43425D;">
                            Email : <EMAIL>
                          </a>
                        </td>
                      </tr>
  
                    </table>
                  </td>
                </tr>
                <tr>
                  <td>
                    <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                      <tr>
                        <td
                          style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 15px 10px;font-weight: 600;">
                          Thank You, <br>
                          The SearchUnify Team
                        </td>
                      </tr>
                      <tr>
                        <td
                          style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif; font-size: 12px; text-align: center;">
                          &copy; ${new Date().getFullYear()} SearchUnify. All rights reserved
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
  
              </table>
            </td>
          </tr>
  
        </table>
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="max-width: 680px;">
          <tr>
            <td style="padding: 10px 0px 10px 0px;">
              <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                  <td style="width: 25px !important;padding:20px 0 0 0;">
                    <a style="text-decoration: none;font-weight: 600;" href="mailto:<EMAIL>">Contact </a>
                  </td>
                  <td style="width: 40px !important;padding:20px 0 0 0;">
                    <a style="text-decoration: none;font-weight: 600;"
                      href="https://www.searchunify.com/privacy-policy/">Privacy
                      Policy </a>
                  </td>
                  <td style="width: 68px !important;padding:20px 0 0 0;">
                    <a style="text-decoration: none;font-weight: 600;"
                      href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                      Conditions</a>
                  </td>
                </tr>
              </table>
            </td>
            <td>
                <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                    <tr>
                        <td style="text-align:right;padding:20px 0 0 0; ">
                            <a href="https://www.facebook.com/SearchUnify/"><img
                                    src="${config.get('adminURL')}/resources/Assets/analytics_fb.png" style="width: 30px; margin-right: 15px;"></a>
                        </td>
                        <td style="text-align:right;padding:20px 0 0 0; ">
                            <a href="https://www.linkedin.com/showcase/25048226/"><img
                                src="${config.get('adminURL')}/resources/Assets/analytics_linkedin.png" style="width: 30px; margin-right: 15px;"></a>
                        </td>
                        <td style="text-align:right;padding:20px 0 0 0; ">
                            <a href="https://twitter.com/SearchUnify"><img
                                src="${config.get('adminURL')}/resources/Assets/twitter.png" style="width: 30px; margin-right: 15px;"></a>
                        </td>
                        <td style="text-align:right;padding:20px 0 0 0; ">
                            <a href="https://www.youtube.com/channel/UCHQdHTFzWRKj5xg1nFoZPTg"><img
                                src="${config.get('adminURL')}/resources/Assets/analytics_youtube.png" style="width: 30px;"></a>
                        </td>
                    </tr>
                </table>
            </td>
          </tr>
          <tr>
            <td
              style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
              <br><br>
  
            </td>
          </tr>
        </table>
      </div>
    </center>
  </body>`;
      return template;
  };
  function searchunifyAdminQueries(name, accountName, subject, description, fileName) {
    var msg = "";
    var template = `<!DOCTYPE html>
    <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
        xmlns:o="urn:schemas-microsoft-com:office:office">
    <head>
        <meta charset="utf-8"> <!-- utf-8 works for most cases -->
        <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
        <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
        <title>SearchUnify Support</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
    
        <link href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
            rel="stylesheet">
        
        <style>
            /* What it does: Remove spaces around the email design added by some email clients. */
            /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
            html,
            body {
                margin: 0 auto !important;
                padding: 0 !important;
                height: 100% !important;
                width: 100% !important;
            }
    
            /* What it does: Stops email clients resizing small text. */
            * {
                -ms-text-size-adjust: 100%;
                -webkit-text-size-adjust: 100%;
            }
    
            /* What it does: Centers email on Android 4.4 */
            div[style*="margin: 16px 0"] {
                margin: 0 !important;
            }
    
            /* What it does: Stops Outlook from adding extra spacing to tables. */
            table,
            td {
                mso-table-lspace: 0pt !important;
                mso-table-rspace: 0pt !important;
            }
    
            /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
            table {
                border-spacing: 0 !important;
                border-collapse: collapse !important;
                table-layout: fixed !important;
                margin: 0 auto !important;
            }
    
            table table table {
                table-layout: auto;
            }
    
            /* What it does: Uses a better rendering method when resizing images in IE. */
            img {
                -ms-interpolation-mode: bicubic;
            }
    
            /* What it does: A work-around for email clients meddling in triggered links. */
            *[x-apple-data-detectors],
            /* iOS */
            .x-gmail-data-detectors,
            /* Gmail */
            .x-gmail-data-detectors *,
            .aBn {
                border-bottom: 0 !important;
                cursor: default !important;
                color: inherit !important;
                text-decoration: none !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
            }
    
            /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
            .a6S {
                display: none !important;
                opacity: 0.01 !important;
            }
    
            /* If the above doesn't work, add a .g-img class to any image in question. */
            img.g-img+div {
                display: none !important;
            }
    
            /* What it does: Prevents underlining the button text in Windows 10 */
            .button-link {
                text-decoration: none !important;
            }
    
            /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
            /* Create one of these media queries for each additional viewport size you'd like to fix */
            /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
            @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
    
                /* iPhone 6 and 6+ */
                .email-container {
                    min-width: 375px !important;
                }
            }
        </style>
        <script type="text/javascript">
            function hideDiv(elem) {
                if(elem.value == "Mac")
                document.getElementById('hideDiv').style.display = "none";
                else
                    document.getElementById('hideDiv').style.display = 'block';
            }
            </script>
    </head>
    
    <body width="100%"
        style="margin: 0; mso-line-height-rule: exactly;background-color:#FFF;">
        <center style="width: 100%; ">
    
            <!--
                    Set the email width. Defined in two places:
                    1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
                    2. MSO tags for Desktop Windows Outlook enforce a 600px width.
                -->
            <div style="max-width: 600px; margin: auto;border-top: 6px solid #56C5FF;" class="email-container">
                <!-- Email Header : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
                    <tr>
                        <td style="padding: 40px 30px 0px; text-align: left">
                            <!-- IF logo.src -->
                            <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                            <!-- ELSE -->
                            &nbsp;
                            <img src="${config.get('adminURL')}/resources/Assets/su-logo-black.png" height="35px" width="195px" alt="SearchUnify" border="0">
                            <!-- ENDIF logo.src -->
                        </td>
                    </tr>
                </table>
                <!-- Email Header : END -->
    
                <!-- Email Body : BEGIN -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                    style="max-width: 600px;">
                    <!-- Hero Image, Flush : BEGIN -->
                    <tr bgcolor="#FFFFFF">
                        <td
                            style="padding: 25px 40px 10px 40px;">
                            <h3
                                style="margin:0;font-family: 'Montserrat', sans-serif; font-size: 16px; line-height: 27px; color: #6F6F70; font-weight: 500;">
                                Hi Support Team,</h3>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr bgcolor="#ffffff">
                                    <td
                                        style="padding: 10px 40px 10px 40px;line-height: 24px;text-align: left;font-size: 16px;font-weight: 500;">
                                        <p style="margin: 0; font-family: 'Montserrat', sans-serif;color:#6F6F70;">Subject:
                                            <span style="margin: 0; font-family: 'Montserrat', sans-serif;color:#2A2A2A;">
                                                ${subject}
                                            </span>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr bgcolor="#ffffff">
                                    <td
                                        style="padding: 10px 40px 10px 40px;text-align: left;font-size: 16px;font-weight: 500;">
                                        <p style="margin: 0; font-family: 'Montserrat', sans-serif;color:#6F6F70;">Description:</p>
                                        <p style="margin: 0; font-family: 'Montserrat', sans-serif;color:#2A2A2A;">
                                            ${description}
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
    
                    <tr bgcolor="#FFFFFF" style="${fileName ? 'display:block' : 'display:none'}">
                        <td
                            style="padding: 10px 40px 8px 40px;">
                            <p
                                style="margin:0;font-family:'Montserrat',sans-serif;font-size:16px;line-height:24px;color:#6F6F70;font-weight: 500;">
                                Attachments</p>
                        </td>
                    </tr>
                    <tr bgcolor="#FFFFFF" style="${fileName ? 'display:block' : 'display:none'}">
                        <td
                            style="padding: 0px 40px 0px 40px;">
                            <span
                                style="margin:0;font-family:'Montserrat',sans-serif;font-size:16px;line-height:24px;color: #2A2A2A;font-weight: 500;background: #EEF4FF;border: 1px solid #6C9CFA;border-radius: 2px;padding: 5px 10px;">
                                ${fileName}</span>
                        </td>
                    </tr>
                    <tr bgcolor="#FFFFFF">
                        <td
                            style="padding: 30px 40px 0px 40px;">
                            <h3
                                style="margin:0;font-family: 'Montserrat', sans-serif; font-size: 16px; line-height: 24px; color: #6F6F70; font-weight: 500;">
                                Thank You!</h3>
                        </td>
                    </tr>
                    <tr bgcolor="#FFFFFF">
                        <td
                            style="padding: 0px 40px 0px 40px;">
                            <p
                                style="margin:0;font-family: 'Montserrat', sans-serif; font-size: 16px; line-height: 24px; color: #2A2A2A; font-weight: 500;">
                                ${name}</p>
                        </td>
                    </tr>
                    <tr bgcolor="#FFFFFF">
                        <td
                            style="padding: 0px 40px 20px 40px;">
                            <p
                                style="margin:0;font-family: 'Montserrat', sans-serif; font-size: 16px; line-height: 24px; color: #2A2A2A; font-weight: 500;">
                                ${accountName}</p>
                        </td>
                    </tr>
                </table>
                <!-- Email Body : END -->
            </div>
    
        </center>
    </body>
    
    </html>
      `;
    return template;
  }
function articleFeeback(data){

    var template=`
    <!DOCTYPE html>
  <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
      xmlns:o="urn:schemas-microsoft-com:office:office">
  
  <head>
      <meta charset="utf-8"> <!-- utf-8 works for most cases -->
      <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
      <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
      <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
      <title>Feedback Received</title> <!-- The title tag shows in email notifications, like Android 4.4. -->
  
      <!-- Web Font / @font-face : BEGIN -->
      <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
  
      <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
      <!--[if mso]>
          <style>
              * {
                  font-family: 'Montserrat',sans-serif !important;
              }
          </style>
      <![endif]-->
  
      <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. More on that here: http://stylecampaign.com/blog/2015/02/webfont-support-in-email/ -->
      <!-- [if !mso] -->
      <!-- insert web font reference, eg: <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'> -->
      <link
          href="https://fonts.googleapis.com/css?family=Montserrat:300i,400,400i,500,500i,600,600i,700,700i&display=swap"
          rel="stylesheet">
      <!-- [endif] -->
  
      <!-- Web Font / @font-face : END -->
  
      <!-- CSS Reset -->
      <style>
          /* What it does: Remove spaces around the email design added by some email clients. */
          /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
          html,
          body {
              margin: 0 auto !important;
              padding: 0 !important;
              height: 100% !important;
              width: 100% !important;
          }
  
          /* What it does: Stops email clients resizing small text. */
          * {
              -ms-text-size-adjust: 100%;
              -webkit-text-size-adjust: 100%;
          }
  
          /* What it does: Centers email on Android 4.4 */
          div[style*="margin: 16px 0"] {
              margin: 0 !important;
          }
  
          /* What it does: Stops Outlook from adding extra spacing to tables. */
          table,
          td {
              mso-table-lspace: 0pt !important;
              mso-table-rspace: 0pt !important;
          }
  
          /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
          table {
              border-spacing: 0 !important;
              border-collapse: collapse !important;
              table-layout: fixed !important;
              margin: 0 auto !important;
          }
  
          table table table {
              table-layout: auto;
          }
  
          /* What it does: Uses a better rendering method when resizing images in IE. */
          img {
              -ms-interpolation-mode: bicubic;
          }
  
          /* What it does: A work-around for email clients meddling in triggered links. */
          *[x-apple-data-detectors],
          /* iOS */
          .x-gmail-data-detectors,
          /* Gmail */
          .x-gmail-data-detectors *,
          .aBn {
              border-bottom: 0 !important;
              cursor: default !important;
              color: inherit !important;
              text-decoration: none !important;
              font-size: inherit !important;
              font-family: inherit !important;
              font-weight: inherit !important;
              line-height: inherit !important;
          }
  
          /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
          .a6S {
              display: none !important;
              opacity: 0.01 !important;
          }
  
          /* If the above doesn't work, add a .g-img class to any image in question. */
          img.g-img+div {
              display: none !important;
          }
  
          /* What it does: Prevents underlining the button text in Windows 10 */
          .button-link {
              text-decoration: none !important;
          }
  
          /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
          /* Create one of these media queries for each additional viewport size you'd like to fix */
          /* Thanks to Eric Lepetit (@ericlepetitsf) for help troubleshooting */
          @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
  
              /* iPhone 6 and 6+ */
              .email-container {
                  min-width: 375px !important;
              }
          }
      </style>
  
      <!-- Progressive Enhancements -->
      <!-- <style>
          /* What it does: Hover styles for buttons */
          .button-td,
          .button-a {
              transition: all 100ms ease-in;
          }
  
          .button-td:hover,
          .button-a:hover {
              background: linear-gradient(263deg, #55c7ff, #7886f7) !important;
              border-color: linear-gradient(263deg, #55c7ff, #7886f7) !important;
          }
  
          /* Media Queries */
          @media screen and (max-width: 600px) {
  
              /* What it does: Adjust typography on small screens to improve readability */
              .email-container p {
                  font-size: 17px !important;
                  line-height: 22px !important;
              }
  
          }
      </style> -->
  
      <!-- What it does: Makes background images in 72ppi Outlook render at correct size. -->
      <!--[if gte mso 9]>
      <xml>
          <o:OfficeDocumentSettings>
              <o:AllowPNG/>
              <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
      </xml>
      <![endif]-->
  
  </head>
  
  <body width="100%"
      style="margin: 0; mso-line-height-rule: exactly;background-color:#7886F7 ;background:url('${config.get('adminURL')}/resources/Assets/emailbackground.png') no-repeat center;background-position: top;">
      <center style="width: 100%; ">
  
          <!--
              Set the email width. Defined in two places:
              1. max-width for all clients except Desktop Windows Outlook, allowing the email to squish on narrow but never go wider than 600px.
              2. MSO tags for Desktop Windows Outlook enforce a 600px width.
          -->
          <div style="max-width: 600px; margin: auto;" class="email-container">
              <!--[if mso]>
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
              <tr>
              <td>
              <![endif]-->
  
              <!-- Email Header : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                  style="max-width: 600px;">
                  <tr>
                      <td style="padding: 20px 0; text-align: left">
                          <!-- IF logo.src -->
                          <!-- <img src="{logo.src}" height="{logo.height}" width="{logo.width}" alt="{site_title}" border="0" style="height: {logo.height}px; width: {logo.width}px; background: #222222; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;"> -->
                          <!-- ELSE -->
                          &nbsp;
                          <img src="${config.get('adminURL')}/resources/Assets/whitelogo.png" height="40px" width="200px" alt="su-logo" border="0"
                              style="font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                          <!-- ENDIF logo.src -->
                      </td>
                  </tr>
              </table>
              <!-- Email Header : END -->
  
              <!-- Email Body : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%"
                  style="max-width: 600px;">
  
                  <!-- Hero Image, Flush : BEGIN -->
                  <tr bgcolor="#F6F7FB">
                      <td
                          style="padding: 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                          <h3
                              style="margin:0;font-family: 'Montserrat', sans-serif;; font-size: 16px; line-height: 27px; color: #333333; font-weight: 500;">
                              Hi Admin</h3>
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#F6F7FB" style="text-align: center;">
                          <img src="${config.get('adminURL')}/resources/Assets/email.png" width="300" height="300" border="0" align="center"
                              style="width: 300px; height: 300px; max-width: 300px;padding-left:4px; height: auto; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;"
                              class="g-img">
  
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#F6F7FB" style="text-align: center;height:30px;">
                      </td>
                  </tr>
  
                  <!-- Hero Image, Flush : END -->
  
                  <!-- 1 Column Text + Button : BEGIN -->
                  <tr>
                      <td bgcolor="#ffffff">
                          <table role="presentation" cellspacing="0" cellpadding="0" width="100%" style="    
                          border-radius: 0% 0% 100% 100%;
                          border-bottom: 60px solid #F6F7FB;
                          border-collapse: separate !important;">
                              <tr >
                                  <td bgcolor="#F6F7FB" style="padding: 20px 0px 10px 0px; font-family: 'Montserrat', sans-serif;;font-size: 24px; font-weight: 600; line-height: 20px;text-align: center; color: #43425d;">
                                      <p style="margin: 0;">
                                          Please find details of the received feedback
                                      </p>
                                  </td>
                              </tr>
                              <tr>
                                  <td bgcolor="#F6F7FB" style="padding: 10px; font-family: 'Montserrat', sans-serif;;font-size: 14px; font-weight: normal; text-align: center; color: #43425d;">
                                      <p style="${data.searchString ? 'display:block; margin: 0;' : 'display:none'}">
                                          <span style="font-size:16px;">Query Searched: '${data.searchString}'</span>
                                      </p>
                                      <p style="margin: 0;">
                                          <span style="font-size:16px;">Result Clicked: '${data.resultClicked}'</span>
                                      </p>
                                      <p style="margin: 0;">
                                          <span style="font-size:16px;">Feedback: '${data.articlefeedback}'</span>
                                      </p>
                                  </td>
                              </tr>
                          </table>
                      </td>
                  </tr>
                  <tr>
                      <td bgcolor="#ffffff">
                          <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                              <tr bgcolor="#ffffff">
                                  <td style="padding: 0 40px; font-family: 'Montserrat', sans-serif;; font-size: 15px; line-height: 20px; color: #555555;">
                                      &nbsp;
                                      
                                  </td>
                              </tr>
                              <tr>
                                  <td>
                                      <table bgcolor="#ffffff" cellspacing="0" cellpadding="0" border="0" width="100%">
                                          <tr>
                                            <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                  Thank You, The SearchUnify Team
                                              </td>
                                              <td style="font-family: 'Montserrat', sans-serif; font-size: 12px; color: #2C2C2C;text-align: center;padding:15px 10px 0px 10px">
                                                  &copy; ${new Date().getFullYear()}. All rights reserved
                                              </td>
                                          </tr>
                                          <tr>
                                              <td
                                                  style="padding: 5px 10px 15px 10px; font-family: 'Montserrat', sans-serif;; font-size: 12px; color: #a6a6a6;text-align: center">
                                                  Email: <a style="text-decoration: none;color:#a6a6a6;"
                                                      href="mailto:<EMAIL>"><EMAIL></a>,<a
                                                      style="text-decoration: none;color:#a6a6a6;"
                                                      href="mailto:${config.get('salesEmail').join(';')}"><EMAIL></a>
                                              </td>
                                          </tr>
                                      </table>
                                  </td>
                              </tr>
  
                          </table>
                      </td>
                  </tr>
                  <!-- 1 Column Text + Button : END -->
  
              </table>
              <!-- Email Body : END -->
  
              <!-- Email Footer : BEGIN -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%"
                  style="max-width: 680px;">
                  <tr>
                      <td style="padding: 10px 0px 10px 0px;">
                          <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                              <tr style="font-family: 'Montserrat', sans-serif;font-size:12px;">
                                  <td style="width: 25px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/contact-us/">Contact </a>|
                                  </td>
                                  <td style="width: 40px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/privacy-policy/">Privacy
                                          Policy </a>|
                                  </td>
                                  <td style="width: 68px !important;padding:20px 0 0 0;">
                                      <a style="text-decoration: none;color:#2B2B2B;"
                                          href="https://www.searchunify.com/terms-and-conditions/">Terms &#38;
                                          Conditions</a>
                                  </td>
                              </tr>
                          </table>
                      </td>
                      <td>
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.facebook.com/SearchUnify/"><img
                                                src="${config.get('adminURL')}/resources/Assets/analytics_fb.png" style="width: 30px; margin-right: 15px;"></a>
                                    </td>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.linkedin.com/showcase/25048226/"><img
                                            src="${config.get('adminURL')}/resources/Assets/analytics_linkedin.png" style="width: 30px; margin-right: 15px;"></a>
                                    </td>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://twitter.com/SearchUnify"><img
                                            src="${config.get('adminURL')}/resources/Assets/twitter.png" style="width: 30px; margin-right: 15px;"></a>
                                    </td>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.youtube.com/channel/UCHQdHTFzWRKj5xg1nFoZPTg"><img
                                            src="${config.get('adminURL')}/resources/Assets/analytics_youtube.png" style="width: 30px;"></a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                  </tr>
                  <tr>
                      <td
                          style="width: 100%;font-size: 12px; font-family: 'Montserrat', sans-serif;; line-height:18px; text-align: center; color: #888888;">
                          <br><br>
  
                      </td>
                  </tr>
              </table>
              <!-- Email Footer : END -->
  
              <!--[if mso]>
              </td>
              </tr>
              </table>
              <![endif]-->
          </div>
  
      </center>
  </body>
  
  </html>`;


    return template;
  }

const llmEmailTemplate = (email) => {
    const template = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>SearchUnify LLM emailer</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet">
        <style type="text/css">
            *{
                margin: 0;
                padding: 0;
            }
            table{
                border-collapse: collapse;
            }
        </style>
    </head>
    
    <body>
        <!--Outer table start -->
        <table width="840px" style="margin: auto;border-collapse: collapse; border-spacing: 0;" border="0" cellpadding="0" cellspacing="0" bgcolor="#62ACFB" min-scale="1">
            <tbody>
            <tr>
                <td height="163px" bgcolor="#62ACFB" style="background-color: #62ACFB;"></td>
                <td height="163px" bgcolor="#62ACFB" style="background-color: #62ACFB;">
                    <a href="https://searchunify.com" target="_blank">
                      <img id="stripe" src="${config.get('adminURL')}/resources/Assets/searchunify.png" border="0" alt="su_logo"/>
                    </a>
                </td>
                <td height="155px" bgcolor="#62ACFB" style="background-color: #62ACFB;"></td>
            </tr>
           
            <tr>
                <td width="150px" bgcolor="#F6F7FB" valign="top">
                    <img width="150px" height="164px" src="${config.get('adminURL')}/resources/Assets/side-stripe.png" alt="left-stripe"/>
                </td>
    
                <!--main table body start -->
                <td width="540px" bgcolor="#ffffff"> 
                    <table width="540px" border="0" cellpadding="0" cellspacing="0" style="box-shadow: 0px 0px 24px 0px #1818181A;clip-path: inset(-24px -24px -24px -24px);">
                        <tr>
                            <td height="25px"></td>
                        </tr>
                        <tr>
                            <td align="left" style="padding-left:25px;font-style:400;font-variant: normal;font-weight: 500;font-size: 16px;line-height:19px;font-family:'Montserrat',sans-serif;">
                                Hi <a href="#" style="color: #1155CC">${email}</a>
                            </td>
                        </tr>
                        <tr><td height="30px" colspan="3"></td></tr>
                        
                        <tr>
                            <td colspan="4" align="left" style="padding-left:25px;font-style:normal;font-variant: normal;font-weight:600;font-size: 20px;line-height:24px;font-family: 'Montserrat',sans-serif;color: #2A2A2A">
                                Authentication Failure: API key expired
                            </td>
                        </tr>
    
                        <tr>
                            <td height="15px"></td>
                        </tr>    
                        
                        <tr>
                            <td colspan="4" align="left" style="padding:0px 25px 0px 25px;font-style:normal;font-variant: normal;font-weight: 400;font-size: 14px;line-height:22px;font-family: 'Montserrat',sans-serif;letter-spacing:0px;color: #2A2A2A">
                            The key used to authenticate OpenAI LLM integration has expired. SearchUnify is not integrated with any LLM intergration hence SearchUnifyGPT is currently disabled.
                            </td>
                        </tr>
    
                        <tr>
                            <td height="21px"></td>
                        </tr>    
                        
                        <tr>
                            <td colspan="4" align="left" style="padding:0px 25px 0px 25px;font-style:normal;font-variant: normal;font-weight: 500;font-size: 14px;line-height:25px;font-family: 'Montserrat',sans-serif;letter-spacing:0px;color: #707070">
                                SearchUnify team is aware of the issue and is taking steps to address it.<br/> In case of further queries, please contact at <a href="mailto:<EMAIL>" title="<EMAIL>" style=" font-weight: 400;font-size: 14px;line-height:30px;font-family: 'Montserrat',sans-serif;color: #6DA7FB;text-decoration: none;"><EMAIL></a>
                            </td>
                        </tr>
                        <tr>
                            <td height="30px"></td>
                        </tr>
        
                        <tr>
                            <td height="22px" colspan="3" align="center" bgcolor="#F6F7FB"></td>
                        </tr>
    
                        <tr>
                            <td colspan="3" align="center" bgcolor="#F6F7FB">
                                <p style="font-style:normal;font-variant: normal;font-weight: 500;font-size: 16px;line-height:30px;font-family: 'Montserrat',sans-serif;color: #222222">If you need any assitance, feel free to connect with the</p>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="3" align="center" bgcolor="#F6F7FB">
                                <a href="https://community.searchunify.com/support" target="_blank" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:30px;font-family: 'Montserrat',sans-serif;color: #6DA7FB;text-decoration: none;">https://community.searchunify.com/hc/en-us/p/support/</a>
                            </td>
                        </tr>   
                        <tr>
                            <td height="20px" colspan="3" align="center" bgcolor="#F6F7FB"></td>
                        </tr>
    
                        <tr>
                            <td height="20px" colspan="3" align="center"></td>
                        </tr>
                        <tr>
                            <td colspan="3" align="center">
                                <p style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #222222">© ${new Date().getFullYear()} SearchUnify. All rights reserved</p>
                            </td>
                        </tr>
                        <tr>
                            <td height="8px" colspan="3" align="center"></td>
                        </tr>
                        <tr>
                            <td colspan="3" align="center">
                                <p style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6">Email: <a href="mailto:<EMAIL>" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6;text-decoration: none;"><EMAIL></a>,<a href="mailto:<EMAIL>" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6;text-decoration: none;"><EMAIL></a></p>
                            </td>
                        </tr>
                        <tr>
                            <td height="20px" colspan="3"></td>
                        </tr>
                    </table> 
                    <table width="540px" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td height="38px" colspan="3" bgcolor="#F6F7FB">
                                <table width="540px" border="0" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td height="15px" colspan="3"></td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <a href="https://www.searchunify.com/contact-us/" title="Contact" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;pointer-events: none;">Contact | </a>
                                            <a href="https://www.searchunify.com/privacy-policy/" title="Privacy Policy" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;pointer-events: none;">Privacy Policy | </a>
                                            <a href="https://www.searchunify.com/terms-and-conditions/" title="Terms &amp; Conditions" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;pointer-events: none;"> Terms &amp; Conditions</a>
                                        </td>
                                        <td>
                                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                                <tr>
                                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                                        <a href="https://www.facebook.com/SearchUnify/"><img
                                                                src="${config.get('adminURL')}/resources/Assets/analytics_fb.png" style="width: 30px; margin-right: 15px;"></a>
                                                    </td>
                                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                                        <a href="https://www.linkedin.com/showcase/25048226/"><img
                                                            src="${config.get('adminURL')}/resources/Assets/analytics_linkedin.png" style="width: 30px; margin-right: 15px;"></a>
                                                    </td>
                                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                                        <a href="https://twitter.com/SearchUnify"><img
                                                            src="${config.get('adminURL')}/resources/Assets/twitter.png" style="width: 30px; margin-right: 15px;"></a>
                                                    </td>
                                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                                        <a href="https://www.youtube.com/channel/UCHQdHTFzWRKj5xg1nFoZPTg"><img
                                                            src="${config.get('adminURL')}/resources/Assets/analytics_youtube.png" style="width: 30px;"></a>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                     <tr>
                                        <td height="20px" colspan="3"></td>
                                    </tr>
                                </table>
                            </td>
                        </tr> 
                    </table> 
                </td>
                
                <!--main table body end -->
                <td bgcolor="#F6F7FB" valign="top">
                    <img id="stripe" width="150px" height="164px" src="${config.get('adminURL')}/resources/Assets/side-stripe.png" alt="right-stripe" />
                </td>
            </tr>
        </tbody>
        </table>
        <!--outer table end -->
    </body>
    </html>`;
    return template;
};

const llmEmailTemplateGemini = (email) => {
    const template = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>SearchUnify LLM emailer</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet">
        <style type="text/css">
            *{
                margin: 0;
                padding: 0;
            }
            table{
                border-collapse: collapse;
            }
        </style>
    </head>
    
    <body>
        <!--Outer table start -->
        <table width="840px" style="margin: auto;border-collapse: collapse; border-spacing: 0;" border="0" cellpadding="0" cellspacing="0" bgcolor="#62ACFB" min-scale="1">
            <tbody>
            <tr>
                <td height="163px" bgcolor="#62ACFB" style="background-color: #62ACFB;"></td>
                <td height="163px" bgcolor="#62ACFB" style="background-color: #62ACFB;">
                    <a href="https://searchunify.com" target="_blank">
                      <img id="stripe" src="${config.get('adminURL')}/resources/Assets/searchunify.png" border="0" alt="su_logo"/>
                    </a>
                </td>
                <td height="155px" bgcolor="#62ACFB" style="background-color: #62ACFB;"></td>
            </tr>
           
            <tr>
                <td width="150px" bgcolor="#F6F7FB" valign="top">
                    <img width="150px" height="164px" src="${config.get('adminURL')}/resources/Assets/side-stripe.png" alt="left-stripe"/>
                </td>
    
                <!--main table body start -->
                <td width="540px" bgcolor="#ffffff"> 
                    <table width="540px" border="0" cellpadding="0" cellspacing="0" style="box-shadow: 0px 0px 24px 0px #1818181A;clip-path: inset(-24px -24px -24px -24px);">
                        <tr>
                            <td height="25px"></td>
                        </tr>
                        <tr>
                            <td align="left" style="padding-left:25px;font-style:400;font-variant: normal;font-weight: 500;font-size: 16px;line-height:19px;font-family:'Montserrat',sans-serif;">
                                Hi <a href="#" style="color: #1155CC">${email}</a>
                            </td>
                        </tr>
                        <tr><td height="30px" colspan="3"></td></tr>
                        
                        <tr>
                            <td colspan="4" align="left" style="padding-left:25px;font-style:normal;font-variant: normal;font-weight:600;font-size: 20px;line-height:24px;font-family: 'Montserrat',sans-serif;color: #2A2A2A">
                                Authentication Failure: API key expired
                            </td>
                        </tr>
    
                        <tr>
                            <td height="15px"></td>
                        </tr>    
                        
                        <tr>
                            <td colspan="4" align="left" style="padding:0px 25px 0px 25px;font-style:normal;font-variant: normal;font-weight: 400;font-size: 14px;line-height:22px;font-family: 'Montserrat',sans-serif;letter-spacing:0px;color: #2A2A2A">
                            The key used to authenticate Gemini LLM integration has expired. SearchUnify is not integrated with any LLM intergration hence SearchUnifyGPT is currently disabled.
                            </td>
                        </tr>
    
                        <tr>
                            <td height="21px"></td>
                        </tr>    
                        
                        <tr>
                            <td colspan="4" align="left" style="padding:0px 25px 0px 25px;font-style:normal;font-variant: normal;font-weight: 500;font-size: 14px;line-height:25px;font-family: 'Montserrat',sans-serif;letter-spacing:0px;color: #707070">
                                SearchUnify team is aware of the issue and is taking steps to address it.<br/> In case of further queries, please contact at <a href="mailto:<EMAIL>" title="<EMAIL>" style=" font-weight: 400;font-size: 14px;line-height:30px;font-family: 'Montserrat',sans-serif;color: #6DA7FB;text-decoration: none;"><EMAIL></a>
                            </td>
                        </tr>
                        <tr>
                            <td height="30px"></td>
                        </tr>
        
                        <tr>
                            <td height="22px" colspan="3" align="center" bgcolor="#F6F7FB"></td>
                        </tr>
    
                        <tr>
                            <td colspan="3" align="center" bgcolor="#F6F7FB">
                                <p style="font-style:normal;font-variant: normal;font-weight: 500;font-size: 16px;line-height:30px;font-family: 'Montserrat',sans-serif;color: #222222">If you need any assitance, feel free to connect with the</p>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="3" align="center" bgcolor="#F6F7FB">
                                <a href="https://community.searchunify.com/support" target="_blank" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:30px;font-family: 'Montserrat',sans-serif;color: #6DA7FB;text-decoration: none;">https://community.searchunify.com/hc/en-us/p/support/</a>
                            </td>
                        </tr>   
                        <tr>
                            <td height="20px" colspan="3" align="center" bgcolor="#F6F7FB"></td>
                        </tr>
    
                        <tr>
                            <td height="20px" colspan="3" align="center"></td>
                        </tr>
                        <tr>
                            <td colspan="3" align="center">
                                <p style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #222222">© ${new Date().getFullYear()} SearchUnify. All rights reserved</p>
                            </td>
                        </tr>
                        <tr>
                            <td height="8px" colspan="3" align="center"></td>
                        </tr>
                        <tr>
                            <td colspan="3" align="center">
                                <p style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6">Email: <a href="mailto:<EMAIL>" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6;text-decoration: none;"><EMAIL></a>,<a href="mailto:<EMAIL>" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6;text-decoration: none;"><EMAIL></a></p>
                            </td>
                        </tr>
                        <tr>
                            <td height="20px" colspan="3"></td>
                        </tr>
                    </table> 
                    <table width="540px" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td height="38px" colspan="3" bgcolor="#F6F7FB">
                                <table width="540px" border="0" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td height="15px" colspan="3"></td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <a href="https://www.searchunify.com/contact-us/" title="Contact" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;pointer-events: none;">Contact | </a>
                                            <a href="https://www.searchunify.com/privacy-policy/" title="Privacy Policy" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;pointer-events: none;">Privacy Policy | </a>
                                            <a href="https://www.searchunify.com/terms-and-conditions/" title="Terms &amp; Conditions" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;pointer-events: none;"> Terms &amp; Conditions</a>
                                        </td>
                                        <td>
                                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                                <tr>
                                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                                        <a href="https://www.facebook.com/SearchUnify/"><img
                                                                src="${config.get('adminURL')}/resources/Assets/analytics_fb.png" style="width: 30px; margin-right: 15px;"></a>
                                                    </td>
                                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                                        <a href="https://www.linkedin.com/showcase/25048226/"><img
                                                            src="${config.get('adminURL')}/resources/Assets/analytics_linkedin.png" style="width: 30px; margin-right: 15px;"></a>
                                                    </td>
                                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                                        <a href="https://twitter.com/SearchUnify"><img
                                                            src="${config.get('adminURL')}/resources/Assets/twitter.png" style="width: 30px; margin-right: 15px;"></a>
                                                    </td>
                                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                                        <a href="https://www.youtube.com/channel/UCHQdHTFzWRKj5xg1nFoZPTg"><img
                                                            src="${config.get('adminURL')}/resources/Assets/analytics_youtube.png" style="width: 30px;"></a>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                     <tr>
                                        <td height="20px" colspan="3"></td>
                                    </tr>
                                </table>
                            </td>
                        </tr> 
                    </table> 
                </td>
                
                <!--main table body end -->
                <td bgcolor="#F6F7FB" valign="top">
                    <img id="stripe" width="150px" height="164px" src="${config.get('adminURL')}/resources/Assets/side-stripe.png" alt="right-stripe" />
                </td>
            </tr>
        </tbody>
        </table>
        <!--outer table end -->
    </body>
    </html>`;
    return template;
};

const llmEmailTemplateClaude = (email) => {
    const template = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>SearchUnify LLM emailer</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet">
        <style type="text/css">
            *{
                margin: 0;
                padding: 0;
            }
            table{
                border-collapse: collapse;
            }
        </style>
    </head>
    
    <body>
        <!--Outer table start -->
        <table width="840px" style="margin: auto;border-collapse: collapse; border-spacing: 0;" border="0" cellpadding="0" cellspacing="0" bgcolor="#62ACFB" min-scale="1">
            <tbody>
            <tr>
                <td height="163px" bgcolor="#62ACFB" style="background-color: #62ACFB;"></td>
                <td height="163px" bgcolor="#62ACFB" style="background-color: #62ACFB;">
                    <a href="https://searchunify.com" target="_blank">
                      <img id="stripe" src="${config.get('adminURL')}/resources/Assets/searchunify.png" border="0" alt="su_logo"/>
                    </a>
                </td>
                <td height="155px" bgcolor="#62ACFB" style="background-color: #62ACFB;"></td>
            </tr>
           
            <tr>
                <td width="150px" bgcolor="#F6F7FB" valign="top">
                    <img width="150px" height="164px" src="${config.get('adminURL')}/resources/Assets/side-stripe.png" alt="left-stripe"/>
                </td>
    
                <!--main table body start -->
                <td width="540px" bgcolor="#ffffff"> 
                    <table width="540px" border="0" cellpadding="0" cellspacing="0" style="box-shadow: 0px 0px 24px 0px #1818181A;clip-path: inset(-24px -24px -24px -24px);">
                        <tr>
                            <td height="25px"></td>
                        </tr>
                        <tr>
                            <td align="left" style="padding-left:25px;font-style:400;font-variant: normal;font-weight: 500;font-size: 16px;line-height:19px;font-family:'Montserrat',sans-serif;">
                                Hi <a href="#" style="color: #1155CC">${email}</a>
                            </td>
                        </tr>
                        <tr><td height="30px" colspan="3"></td></tr>
                        
                        <tr>
                            <td colspan="4" align="left" style="padding-left:25px;font-style:normal;font-variant: normal;font-weight:600;font-size: 20px;line-height:24px;font-family: 'Montserrat',sans-serif;color: #2A2A2A">
                                Invalid key
                            </td>
                        </tr>
    
                        <tr>
                            <td height="15px"></td>
                        </tr>    
                        
                        <tr>
                            <td colspan="4" align="left" style="padding:0px 25px 0px 25px;font-style:normal;font-variant: normal;font-weight: 400;font-size: 14px;line-height:22px;font-family: 'Montserrat',sans-serif;letter-spacing:0px;color: #2A2A2A">
                               The key used to authenticate Claude LLM integration has expired. SearchUnify is not integrated with Claude hence SearchUnifyGPT is currently disabled. To integrate again, validate with an updated key.
                            </td>
                        </tr>
    
                        <tr>
                            <td height="21px"></td>
                        </tr>    
                        
                        <tr>
                            <td colspan="4" align="left" style="padding:0px 25px 0px 25px;font-style:normal;font-variant: normal;font-weight: 500;font-size: 14px;line-height:25px;font-family: 'Montserrat',sans-serif;letter-spacing:0px;color: #707070">
                               The possible reason for an "Invalid key" is that your API key is invalid, expired, or has been revoked.
                            </td>
                        </tr>
                        <tr>
                            <td height="30px"></td>
                        </tr>
        
                        <tr>
                            <td height="22px" colspan="3" align="center" bgcolor="#F6F7FB"></td>
                        </tr>
    
                        <tr>
                            <td colspan="3" align="center" bgcolor="#F6F7FB">
                                <p style="font-style:normal;font-variant: normal;font-weight: 500;font-size: 16px;line-height:30px;font-family: 'Montserrat',sans-serif;color: #222222">If you need any assitance, feel free to connect with the</p>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="3" align="center" bgcolor="#F6F7FB">
                                <a href="https://community.searchunify.com/support" target="_blank" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:30px;font-family: 'Montserrat',sans-serif;color: #6DA7FB;text-decoration: none;">https://community.searchunify.com/hc/en-us/p/support/</a>
                            </td>
                        </tr>   
                        <tr>
                            <td height="20px" colspan="3" align="center" bgcolor="#F6F7FB"></td>
                        </tr>
    
                        <tr>
                            <td height="20px" colspan="3" align="center"></td>
                        </tr>
                        <tr>
                            <td colspan="3" align="center">
                                <p style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #222222">© ${new Date().getFullYear()} SearchUnify. All rights reserved</p>
                            </td>
                        </tr>
                        <tr>
                            <td height="8px" colspan="3" align="center"></td>
                        </tr>
                        <tr>
                            <td colspan="3" align="center">
                                <p style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6">Email: <a href="mailto:<EMAIL>" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6;text-decoration: none;"><EMAIL></a>,<a href="mailto:<EMAIL>" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6;text-decoration: none;"><EMAIL></a></p>
                            </td>
                        </tr>
                        <tr>
                            <td height="20px" colspan="3"></td>
                        </tr>
                    </table> 
                    <table width="540px" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td height="38px" colspan="3" bgcolor="#F6F7FB">
                                <table width="540px" border="0" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td height="15px" colspan="3"></td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <a href="https://www.searchunify.com/contact-us/" title="Contact" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;pointer-events: none;">Contact | </a>
                                            <a href="https://www.searchunify.com/privacy-policy/" title="Privacy Policy" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;pointer-events: none;">Privacy Policy | </a>
                                            <a href="https://www.searchunify.com/terms-and-conditions/" title="Terms &amp; Conditions" style="font-style:normal;font-variant: normal;font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;pointer-events: none;"> Terms &amp; Conditions</a>
                                        </td>
                                        <td>
                            <table cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0!important;">
                                <tr>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.facebook.com/SearchUnify/"><img
                                                src="${config.get('adminURL')}/resources/Assets/analytics_fb.png" style="width: 30px; margin-right: 15px;"></a>
                                    </td>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.linkedin.com/showcase/25048226/"><img
                                            src="${config.get('adminURL')}/resources/Assets/analytics_linkedin.png" style="width: 30px; margin-right: 15px;"></a>
                                    </td>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://twitter.com/SearchUnify"><img
                                            src="${config.get('adminURL')}/resources/Assets/twitter.png" style="width: 30px; margin-right: 15px;"></a>
                                    </td>
                                    <td style="text-align:right;padding:20px 0 0 0; ">
                                        <a href="https://www.youtube.com/channel/UCHQdHTFzWRKj5xg1nFoZPTg"><img
                                            src="${config.get('adminURL')}/resources/Assets/analytics_youtube.png" style="width: 30px;"></a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                                    </tr>
                                     <tr>
                                        <td height="20px" colspan="3"></td>
                                    </tr>
                                </table>
                            </td>
                        </tr> 
                    </table> 
                </td>
                
                <!--main table body end -->
                <td bgcolor="#F6F7FB" valign="top">
                    <img id="stripe" width="150px" height="164px" src="${config.get('adminURL')}/resources/Assets/side-stripe.png" alt="right-stripe" />
                </td>
            </tr>
        </tbody>
        </table>
        <!--outer table end -->
    </body>
    </html>`;
    return template;
};

module.exports = {
  analyticsTemplate: analyticsTemplate,
  createVersionMessage: createVersionMessage,
  createMessage: createMessage,
  analyticsIntermediateTemplate: analyticsIntermediateTemplate,
  registerMessage: registerMessage,
  contentDuplicacyNotification:contentDuplicacyNotification,
  forgetMessage: forgetMessage,
  sucesscrawltemplate: sucesscrawltemplate,
  failcrawltemplate :failcrawltemplate,
  crawlkilledtemplate: crawlkilledtemplate,
  analyticsEmailTemplate:analyticsEmailTemplate,
  chattranscripttemplate: chattranscripttemplate,
  welcomeMessage: welcomeMessage,
  verifyUser: verifyUser,
  botTrain:botTrain,
  searchResultsEmailTemplate: searchResultsEmailTemplate,
  mergeFacetEmailTemplate:mergeFacetEmailTemplate,
  reindexingSuccessTemplate: reindexingSuccessTemplate,
  searchunifyAdminQueries: searchunifyAdminQueries,
  articleFeeback:articleFeeback,
  demoRequestTemplate: demoRequestTemplate,
  llmEmailTemplate: llmEmailTemplate,
  llmEmailTemplateGemini:llmEmailTemplateGemini,
  llmEmailTemplateClaude:llmEmailTemplateClaude 
}
