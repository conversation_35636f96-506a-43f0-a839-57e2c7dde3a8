var fs = require("fs");
var path = require("path");
var async = require('async');
var request = require("request");
var through = require('through');
var split = require('split');
var sy_routes = {};
var commonFunctions = require('./../../utils/commonFunctions');
const kafkaLib = require('../../utils/kafka/kafka-lib');
const PassThrough = require('stream').PassThrough;
const config = require("config");
const { exec } = require("child_process");
let isolateSynonymStopwords = config ? config.get('isolateSynonymStopwords') : false;
let pocName = config ? config.get('poc_name') : '';
let elStopwordsPath = "stemwords.txt";
const isWhitelisted = new Set(config.get('feature').get('stemwords').get('whitelisted'))
const secretKey = config.get('indexService.sharedSecret')
const logger = require('tracer').console({
  level: 'info',
  format: "{{timestamp}} <{{title}}> {{message}} (in {{file}}:{{line}}) @crawlinglogs@@"
});
// create synonym file if not exists
if ((isolateSynonymStopwords && pocName) && !fs.existsSync(path.join(__dirname, '../../synonyms'))) {
  try {
    fs.mkdirSync('../../synoyms');
  } catch (e) {
  }
}


let dictFile = fs.existsSync(path.join(__dirname + '/dictionaryWords.dic.ignore'));
if (!dictFile) {
  fs.open(path.join(__dirname + '/dictionaryWords.dic.ignore'), 'w', function (err, file) {
    if (err) throw err;
  });
}


exports.index = function (req, res, next) {
  try {
    req.method = req.method == "OPTIONS" ? req.headers["access-control-request-method"] : req.method
    sy_routes[req.params.path][req.method](req, res, next);
  } catch (error) {
    logger.error("error", error);
    res.send(404);
  }
};

sy_routes['read'] = {};
sy_routes['read']['GET'] = (req, res, next) => {
  res.setHeader("Content-Type", "text/plain");
  if (config.get("onPremises")) {
    var body = {

    }
    commonFunctions.httpRequest('GET', config.get('onPremisesUrl') + '/getSynonymsStopwords?', '', body, '', function (err, result) {
      if (!err)
        res.send(result);
      else
        res.end();
    })
  } else {
    if ((isolateSynonymStopwords && pocName) && !fs.existsSync(path.join(__dirname, '../../synonyms', `${req.headers.session.tpk}_${elStopwordsPath}`))) {
      fs.writeFileSync(path.join(__dirname, '../../synonyms', `${req.headers.session.tpk}_${elStopwordsPath}`), '');
    }
    fs.createReadStream(path.join(__dirname, '../../synonyms', `${req.headers.session.tpk}_${elStopwordsPath}`)).pipe(res);
  }

};
sy_routes['updateStemwordsInTenant'] = {};
sy_routes['updateStemwordsInTenant']['GET'] = (req, res, next) => {
  logger.info('--update stemwords----', req)
    const tenantId = req.headers['tenant-id'];
    const indexsecret = secretKey
    const options = {
        method: 'GET',
        url: config.get("indexService.url") + "/index-service/open-search/getStemwordTenant",
        headers: {
            'tenant-id': tenantId,
            'index-service-secret': indexsecret
        }
    };
    // Check if tenantId is whitelisted
    if (!isWhitelisted.has(tenantId)) {
      logger.error(`Unauthorized tenant ID: ${tenantId}`);
      return res.status(403).send('Unauthorized tenant ID.');
    }
    // Make the API request
    request(options, (error, response, body) => {
        if (error) {
          logger.error('Error in API call to index-service:', error);
            return res.status(500).send('Error in API call to index-service.');
        }
        logger.info('Response from index-service:', body);
        res.status(response.statusCode).send(body);
    });
};
sy_routes['write'] = {}
sy_routes['write']['POST'] = (req, res, next) => {
  logger.info('--write file----', req)
  if (!fs.existsSync(path.join(__dirname, '../../synonyms', `${req.headers.session.tpk}_${elStopwordsPath}`))) {
    logger.info("what is the prefix",req.headers.session.tpk)
    fs.writeFileSync(path.join(__dirname, '../../synonyms', `${req.headers.session.tpk}_${elStopwordsPath}`), '');
  }
  // logger.info(req.body);
  // req.pipe(process.stdout);
  /**
   * 1. Save file
   * 2. Send response
   * 3. Get list of all indexes to re-open
   * 4. All indexes
   *      Open
   *      Close
   */
  var indexes = [];
  logger.info("Fetching Indexes");
  connection[req.headers['tenant-id']].execute.query(
    `SELECT CONCAT(cs.elasticIndexName, '__', cso.name) AS content_source_name FROM content_sources cs JOIN content_source_objects cso ON (cso.content_source_id = cs.id)`,(err, rows)=>{
    indexes = rows.map(r=>{return r.content_source_name;});
    logger.info("rows", indexes);
    if (config.get("onPremises")) {
      var options = {
        method: "POST",
        url: config.get('onPremisesUrl') + '/updateSynonymsStopWords?indexes=' + indexes.join(','),
        headers: { 'Content-Type': 'text/plain' }
      };
      req.pipe(split())
        .pipe(through(function (buff) {
          this.queue(buff.toString().toLowerCase() + "\n");
        }))
        .pipe(request(options).on("finish", function () {
          res.end()
        })
        )
    }
    else {
      const elasticConfig = new PassThrough();
      const stopwords = new PassThrough();
      req.pipe(elasticConfig);
      req.pipe(stopwords);
      async.series([
        cb => {
          stopwords.pipe(split())
            .pipe(through(function (buff) {
              this.queue(buff.toString().toLowerCase() + "\n");
            }))
            .pipe(fs.createWriteStream(path.join(__dirname, '../../synonyms', `${req.headers.session.tpk}_${elStopwordsPath}`)).on("finish", function () {
              res.end();
              cb();
            }));
        },
        cb => {
          let stpw = '';
          elasticConfig.on('data', function (chunk) {
            stpw += chunk.toString().toLowerCase() + "\n"
          }).on("end", () => {
            const clusterUrls = req.headers.session && req.headers.session.esClusterIp ? req.headers.session.esClusterIp.split(",") : '';
            let taskArray = [];
            clusterUrls.forEach((url) => {
              const esClusterUrl = `${url.split(':')[0]}:${config.get('clusterServiceUrl')}`;
              taskArray.push(saveElasticConfig.bind(null, esClusterUrl, stpw, req.headers.session.tpk));
            })
            async.parallel(taskArray, (err, data) => {
              cb();
            })
          });
        },
        cb => {
          const elasticUrls = req.headers.session.esClusterIp.split(',');
          async.series(elasticUrls.map(url => {
            return cb => {
              async.series([
                cb => {
                  logger.info("Closing index on url", url);
                  request({
                    "method": "POST",
                    "url": `${url}/${indexes.join(',')}/_close`,
                    "body": JSON.stringify({}),
                    "headers": { 'Content-Type': 'application/json' }
                  }, (error, response, body) => {
                    cb(error, body);
                  })
                },
                cb => {
                  logger.info("Opening index on url", url);
                  request({
                    "method": "POST",
                    "url": `${url}/${indexes.join(',')}/_open`,
                    "body": JSON.stringify({}),
                    "headers": { 'Content-Type': 'application/json' }
                  }, (error, response, body) => {
                    cb(error, body);
                  });
                }
              ], cb);
            };
          }), cb);
        }
      ], (error, result) => {
        logger.info(error || result);
      });

    }
  });
};

const saveElasticConfig = (url, data, tpk, cb) => {
  var options = {
    method: 'POST',
    url: `${url}/saveStopwords?tpk=${tpk}`,
    headers: { 'content-type': 'application/json' },
    body: { data },
    json: true
  };

  request(options, (err, response, docs) => {
    cb({});
  })
};

sy_routes['getStemmingTenant'] = {};
sy_routes['getStemmingTenant']['POST'] = (req, res, next) => {
  const getStemmingTenant = req.headers['tenant-id'];
  
  if (getStemmingTenant) {
    const whitelistedTenants = isWhitelisted.has(getStemmingTenant);
    res.json({ tenantId: getStemmingTenant, whitelistedTenants: whitelistedTenants });
  }
}
//     res.json({ tenantId: getStemmingTenant, whitelistedTenants: isWhitelisted.has(req.headers['tenant-id']) });
// };
