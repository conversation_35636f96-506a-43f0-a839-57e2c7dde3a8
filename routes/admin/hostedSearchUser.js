var express = require('express');
var router = express.Router();
var commonFunctions = require('./../../utils/commonFunctions');


router.get('/getHostedUsers', function (req, res) {
    getHostedUsers("", req,(error, result) => {
        res.send(result);
    })
})

router.post('/addHostedUser', function (req, res) {
    var validateHtmlrequest = commonFunctions.htmlvalidate(req.body.user);
    if(validateHtmlrequest == true){
        addHostedUsers(req.body.user,req, (error, result) => {
            res.send(result);
        });
    }else{
        res.send({
            flag: 403,
            message: "Only string values are allowed"
        });
    }
})

router.post('/deleteHostedUser', function (req, res) {
    var user = req.body.user;
    let sql = `DELETE FROM hosted_search_users WHERE id = ${user.id}`;
    connection[req.headers['tenant-id']].execute.query(sql, user, function (err, result) {
        if (!err) {
            res.send(result);
        }
        else {
            commonFunctions.errorlogger.error(err);
        }
    })
})

function getHostedUsers(user,req, cb) {
    var sql = "";
    if (user)
        sql = "select * from hosted_search_users where email=?"
    else
        sql = "select * from hosted_search_users"
        connection[req.headers['tenant-id']].execute.query(sql, user, function (err, rows) {
        if (!err) {
            cb(null, rows);
        }
        else {
            cb(null, []);
        }
    })
}

function addHostedUsers(user,req, cb) {
    let sql = `INSERT INTO hosted_search_users (id, name, email, status)
                VALUES (?,?,?,?) ON DUPLICATE KEY UPDATE id = VALUES(id), name = VALUES(name), email = VALUES(email), status = VALUES(status)`
                connection[req.headers['tenant-id']].execute.query(sql, [user.id, user.name, user.email, user.status], function (err, result) {
        if (!err) {
            cb(null, result);
        }
        else {
            cb(null, []);
        }
    })
}

module.exports = {
    router: router,
    getHostedUsers: getHostedUsers,
    addHostedUsers: addHostedUsers
};
