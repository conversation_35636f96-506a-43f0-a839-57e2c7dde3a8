module.exports = {
    objects: [
        {
            name: 'fs_ticket',
            label: 'Ticket',
            status: 1,
            boosting_factor: 1,
            base_url: '',
            fields: [
              {
                name: 'view_href',
                label: 'View Href',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'id',
                label: 'Id',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'subject',
                label: 'Subject',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'description',
                label: 'Description',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'department_ids',
                label: 'Department Id',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1,
                isMerged: 1
              },
              {
                name: 'category_category_nested',
                label: 'Category',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'requester_id',
                label: 'Requester Id',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'responder_id',
                label: 'Responder Id',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'status_name',
                label: 'Status',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'status',
                label: 'Status ID',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'department_name',
                label: 'Department Name',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'priority_name',
                label: 'Priority',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'priority',
                label: 'Priority ID',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'created_at',
                label: 'Created At',
                type: 'datetime',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'updated_at',
                label: 'Updated At',
                type: 'datetime',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'due_by',
                label: 'Due By',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'tags',
                label: 'Tags',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'attachment_multiple',
                label: 'Attachments',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'workspace_id',
                label: 'Workspace ID',
                type: 'string',
                isMerged: 1,
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'group_ids',
                label: 'Group ID',
                type: 'string',
                isMerged: 1,
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                  name: "conversations",
                  label: "Conversations",
                  type: "string",
                  isFilterable: 1,
                  isSortable: 1,
                  isSearchable: 1,
                  isActive: 1,
              }
            ]
        },
        {
            name: 'fs_article',
            label: 'Article',
            status: 1,
            boosting_factor: 1,
            base_url: '',
            fields: [
              {
                name: 'view_href',
                label: 'View Href',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'id',
                label: 'Id',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'title',
                label: 'Title',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'description',
                label: 'Description',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'status',
                label: 'Status',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'category_id',
                label: 'Category Id',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'folder_id',
                label: 'Folder Id',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'created_at',
                label: 'Created At',
                type: 'datetime',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'updated_at',
                label: 'Updated At',
                type: 'datetime',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'tags',
                label: 'Tags',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'attachment_multiple',
                label: 'Attachments',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
              },
              {
                name: 'group_ids',
                label: 'Group IDs',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1,
                isMerged: 1
              },
              {
                name: 'department_ids',
                label: 'Department IDs',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1,
                isMerged: 1
              },
              {
                name: 'visibility',
                label: 'Visibility',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1,
                isMerged: 1
              },
              {
                name: 'workspace_id',
                label: 'Workspace ID',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1,
                isMerged: 1
              }
        
            ]
        }
    ],
};

