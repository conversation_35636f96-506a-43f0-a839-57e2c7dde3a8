var express = require("express");
var router = express.Router();
const { accountDetails } = require("./accountDetails");
const {
    botAnalyticsConsumer,
} = require("../../customPlugins/communityHelper/analytics/botAnalyticsConsumer");
const {
    createDictionary,
} = require("../../customPlugins/semanticSearch/cron/createDictionary");
const {
    keywordBoostingCron,
} = require("../../customPlugins/semanticSearch/cron/keywordBoostingCron");
const {
    faceInterpreter,
} = require("../../customPlugins/semanticSearch/cron/facetInterpreter");
const {
    preProcessFeedPost,
} = require("../../customPlugins/communityHelper/salesforceUtilties/feedPost");

const {
    preProcessForumPost,
} = require("../../customPlugins/communityHelper/lithiumUtilities/lithiumPosts");
const botAnalytics = require("../../customPlugins/communityHelper/analytics/botAnalyticsLib");

router.get("/accountDetails", (req, res) => {
    res.send({ status: 200});
    accountDetails(req, (err, data) => {
        console.log("accountDetails cron ran, err: ", err, " data ", data);
    });
});

router.get("/botAnalyticsConsumer", (req, res) => {
    res.send({ status: 200});
    botAnalyticsConsumer(req, (err, data) => {
        console.log("botAnalyticsConsumer cron ran, err: ", err, " data ", data);
    });
});

router.get("/createDictionary", (req, res) => {
    res.send({ status: 200});
    createDictionary(req, (err, data) => {
        console.log("createDictionary cron ran, err: ", err, " data ", data);
    });
});

router.get("/keywordBoostingCron", (req, res) => {
    res.send({ status: 200});
    keywordBoostingCron(req, (err, data) => {
        console.log("keywordBoostingCron cron ran, err: ", err, " data ", data);
    });
});

router.get('/faceInterpreter', (req, res) => {
    res.send({ status: 200});
    faceInterpreter(req, (err, data) => { 
        console.log("faceInterpreter cron ran, err: ", err, " data ", data);
    });
});

router.get("/feedPost", function (req, res) {
    var botId = req.query.botId;
    res.send({ status: 200});
    preProcessFeedPost(botId, req, (error, result) => {
        if (result) {
            botAnalytics.insertBotAnalytics(
                result,
                1,
                req,
                (aError, aResult) => {
                    console.log("feedPost cron aResult: ", aResult)
                }
            );
        } else {
            console.log("feedPost: Sorry Error Occured!!");
        }
    });
});

router.get("/lithiumForumPost", function (req, res) {
    var botId = req.query.botId;
    res.send({ status: 200});
    preProcessForumPost(botId, req, (error, result) => {
        if (result) {
            botAnalytics.insertBotAnalytics(
                result,
                0,
                req,
                (aError, aResult) => {
                    console.log("lithiumForumPost cron aResult: ", aResult)
                }
            );
        } else {
            console.log("lithiumForumPost: Sorry Error Occured!!", error);
        }
    });
});
module.exports = {
    router: router,
};
