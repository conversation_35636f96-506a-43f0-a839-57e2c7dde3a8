module.exports={
    "objects":[
      {
        "name":"ideas",
        "label":"Ideas",
        "status":1,
        "boosting_factor":1,
        "base_url":"",
        "fields":[
            {
              "name":"title",
              "label":"Name",
              "type":"string",
              "isFilterable":1, //indexing method analyzed OR not analyzed
              "isSortable":1,
              "isSearchable":1, //analyzer
              "isActive":1
            },
            {
              "name":"body",
              "label":"Description",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name":"data_category_nested",
              "label":"Data Category Nested",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1,
              "isMerged": 1
            },
            {
              "name":"data_category_flat",
              "label":"Data Category Flat",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1,
              "isMerged": 1
            },
            {
              "name": "comment_attachments",
              "label": "Comment Attachments",
              "type": "string",
              "isFilterable": 1, //indexing method analyzed OR not analyzed
              "isSortable": 1,
              "isSearchable": 1, //analyzer
              "isActive": 1
            },
            {
              "name": "description_attachments",
              "label": "Description Attachments",
              "type": "string",
              "isFilterable": 1, //indexing method analyzed OR not analyzed
              "isSortable": 1,
              "isSearchable": 1, //analyzer
              "isActive": 1
            },
            {
              "name":"created_by_name",
              "label":"Creator Name",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name":"aha_created_by_email",
              "label":"Creator Email",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1,
              "isMerged": 1
            },
            {
              "name":"status",
              "label":"Status",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name":"created_date",
              "label":"Created Date",
              "type":"datetime",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":0,
              "isActive":1
            },
            {
              "name":"updated_date",
              "label":"Last Updated Date",
              "type":"datetime",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":0,
              "isActive":1
            },
            {
              "name":"aha_workspaces",
              "label":"Aha Workspaces ",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1,
              "isMerged": 1
            },
            {
              "name":"reference_num",
              "label":"Idea Reference",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1,
              "isMerged": 1
            },
            {
              "name":"product_id",
              "label":"Product Id",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name":"product_name",
              "label":"Product Name",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name":"tags",
              "label":"Tags",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name":"assigned_user_name",
              "label":"Assigned user name",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name":"assigned_user_email",
              "label":"Assigned user email",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name":"view_href",
              "label":"Url",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name":"vote_count",
              "label":"No of Votes",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name":"comments_count",
              "label":"Comments Count",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name": "comments",
              "label": "Comments",
              "type": "string",
              "isFilterable": 1, //indexing method analyzed OR not analyzed
              "isSortable": 1,
              "isSearchable": 1, //analyzer
              "isActive": 1
            },
            {
              "name":"id",
              "label":"Id",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":1,
              "isActive":1
            },
            {
              "name":"aha_visibility",
              "label":"Visibility",
              "type":"string",
              "isFilterable":1,
              "isSortable":1,
              "isSearchable":0,
              "isActive":1,
              "isMerged": 1
            },
            {
              "name": 'private_comments',
              "label": 'Private Comments',
              "type": 'string',
              "isFilterable": 1,
              "isSortable": 1,
              "isSearchable": 1,
              "isActive": 1,
              "isMerged": 1
            },
            {
              "name": 'private_comment_count',
              "label": 'Private Comments Count',
              "type": 'string',
              "isFilterable": 1,
              "isSortable": 1,
              "isSearchable": 1,
              "isActive": 1,
              "isMerged": 1
            },
            {
              "name": 'private_comment_attachments',
              "label": 'Private Comment Attachments',
              "type": 'string',
              "isFilterable": 1,
              "isSortable": 1,
              "isSearchable": 1,
              "isActive": 1,
              "isMerged": 1
            },
            {
              "name": 'description_private_attachments',
              "label": 'Description Private Attachments',
              "type": 'string',
              "isFilterable": 1,
              "isSortable": 1,
              "isSearchable": 1,
              "isActive": 1,
              "isMerged": 1
            },
            {
              name: 'attachment_multiple',
              label: 'Attachment Multiple',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            }
        ]
  
      }
    ],
    "fields":[
    ]
  }
  
  