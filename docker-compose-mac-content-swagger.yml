# analytics docker service
version: "3"
services:
  content-swagger:
    container_name: content-swagger
    build:
      context: .
      dockerfile: mac.Dockerfile
      args:
        - BASE_IMAGE=${MUL_ARCH_BASE_IMAGE}
    image: backend:latest
    volumes:
      - ".:/home/<USER>"
      - "/home/<USER>/node_modules"
    ports:
      - ${SWAGGER_PORT}:${SWAGGER_PORT}
    networks:
      - shared_network
    restart: always
    deploy:
      resources:
        limits:
          memory: 2G
    command: >
      sh -c "npm install 
      && npm run swagger"
    extra_hosts:
      - "master:**********"

networks:
  shared_network:
    external:
      name: shared_network
