var express = require('express');
var router = express.Router();
var path = require('path');
var elasticsearch = require('elasticsearch');
var async = require('async');
environment = require(path.join(__dirname, '../environment.js'));
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
const pako = require('pako');
var ssrType = "recommend";
var isDelete = false; //When true index will be deleted and recreated  new index with mapping

const kafkaLib = require("../../utils/kafka/kafka-lib");

const { getEsClient } = require('../../utils/elastic');
const { getTenantInfoFromTenantId } = require('auth-middleware');

var stopWords = ["a", "about", "above", "across", "after", "again", "against", "all", "almost", "alone", "along", "already", "also", "although", "always", "among", "an", "and", "another", "any", "anybody", "anyone", "anything", "anywhere", "are", "area", "areas", "around", "as", "ask", "asked", "asking", "asks", "at", "away", "b", "back", "backed", "backing", "backs", "be", "became", "because", "become", "becomes", "been", "before", "began", "behind", "being", "beings", "best", "better", "between", "big", "both", "but", "by", "c", "came", "can", "cannot", "case", "cases", "certain", "certainly", "clear", "clearly", "come", "could", "d", "did", "differ", "different", "differently", "do", "does", "done", "down", "down", "downed", "downing", "downs", "during", "e", "each", "early", "either", "end", "ended", "ending", "ends", "enough", "even", "evenly", "ever", "every", "everybody", "everyone", "everything", "everywhere", "f", "face", "faces", "fact", "facts", "far", "felt", "few", "find", "finds", "first", "for", "four", "from", "full", "fully", "further", "furthered", "furthering", "furthers", "g", "gave", "general", "generally", "get", "gets", "give", "given", "gives", "go", "going", "good", "goods", "got", "great", "greater", "greatest", "group", "grouped", "grouping", "groups", "h", "had", "has", "have", "having", "he", "her", "here", "herself", "high", "high", "high", "higher", "highest", "him", "himself", "his", "how", "however", "i", "if", "important", "in", "interest", "interested", "interesting", "interests", "into", "is", "it", "its", "itself", "j", "just", "k", "keep", "keeps", "kind", "knew", "know", "known", "knows", "l", "large", "largely", "last", "later", "latest", "least", "less", "let", "lets", "like", "likely", "long", "longer", "longest", "m", "made", "make", "making", "man", "many", "may", "me", "member", "members", "men", "might", "more", "most", "mostly", "mr", "mrs", "much", "must", "my", "myself", "n", "necessary", "need", "needed", "needing", "needs", "never", "new", "new", "newer", "newest", "next", "no", "nobody", "non", "noone", "not", "nothing", "now", "nowhere", "number", "numbers", "o", "of", "off", "often", "old", "older", "oldest", "on", "once", "one", "only", "open", "opened", "opening", "opens", "or", "order", "ordered", "ordering", "orders", "other", "others", "our", "out", "over", "p", "part", "parted", "parting", "parts", "per", "perhaps", "place", "places", "point", "pointed", "pointing", "points", "possible", "present", "presented", "presenting", "presents", "problems", "put", "puts", "q", "quite", "r", "rather", "really", "right", "right", "room", "rooms", "s", "said", "same", "saw", "say", "says", "second", "seconds", "see", "seem", "seemed", "seeming", "seems", "sees", "several", "shall", "she", "should", "show", "showed", "showing", "shows", "side", "sides", "since", "small", "smaller", "smallest", "so", "some", "somebody", "someone", "something", "somewhere", "state", "states", "still", "still", "such", "sure", "t", "take", "taken", "than", "that", "the", "their", "them", "then", "there", "therefore", "these", "they", "thing", "things", "think", "thinks", "this", "those", "though", "thought", "thoughts", "three", "through", "thus", "to", "today", "together", "too", "took", "toward", "turn", "turned", "turning", "turns", "two", "u", "under", "until", "up", "upon", "us", "use", "used", "uses", "v", "very", "w", "want", "wanted", "wanting", "wants", "was", "way", "ways", "we", "well", "wells", "went", "were", "what", "when", "where", "whether", "which", "while", "who", "whole", "whose", "why", "will", "with", "within", "without", "would", "x", "y", "year", "years", "yet", "you", "young", "younger", "youngest", "your", "yours", "z", "a's", "able", "about", "above", "according", "accordingly", "across", "actually", "after", "afterwards", "again", "against", "ain't", "all", "allow", "allows", "almost", "alone", "along", "already", "also", "although", "always", "am", "among", "amongst", "an", "and", "another", "any", "anybody", "anyhow", "anyone", "anything", "anyway", "anyways", "anywhere", "apart", "appear", "appreciate", "appropriate", "are", "aren't", "around", "as", "aside", "ask", "asking", "associated", "at", "available", "away", "awfully", "be", "became", "because", "become", "becomes", "becoming", "been", "before", "beforehand", "behind", "being", "believe", "below", "beside", "besides", "best", "better", "between", "beyond", "both", "brief", "but", "by", "c'mon", "c's", "came", "can", "can't", "cannot", "cant", "cause", "causes", "certain", "certainly", "changes", "clearly", "co", "com", "come", "comes", "concerning", "consequently", "consider", "considering", "contain", "containing", "contains", "corresponding", "could", "couldn't", "course", "currently", "definitely", "described", "despite", "did", "didn't", "different", "do", "does", "doesn't", "doing", "don't", "don’t", "done", "down", "downwards", "during", "each", "edu", "eg", "eight", "either", "else", "elsewhere", "enough", "entirely", "especially", "et", "etc", "even", "ever", "every", "everybody", "everyone", "everything", "everywhere", "ex", "exactly", "example", "except", "far", "few", "fifth", "first", "five", "followed", "following", "follows", "for", "former", "formerly", "forth", "four", "from", "further", "furthermore", "get", "gets", "getting", "given", "gives", "go", "goes", "going", "gone", "got", "gotten", "greetings", "had", "hadn't", "happens", "hardly", "has", "hasn't", "have", "haven't", "having", "he", "he's", "hello", "help", "hence", "her", "here", "here's", "hereafter", "hereby", "herein", "hereupon", "hers", "herself", "hi", "him", "himself", "his", "hither", "hopefully", "how", "howbeit", "however", "i'd", "i'll", "i'm", "i've", "ie", "if", "ignored", "immediate", "in", "inasmuch", "inc", "indeed", "indicate", "indicated", "indicates", "inner", "insofar", "instead", "into", "inward", "is", "isn't", "it", "it'd", "it'll", "it's", "its", "itself", "just", "keep", "keeps", "kept", "know", "knows", "known", "last", "lately", "later", "latter", "latterly", "least", "less", "lest", "let", "let's", "like", "liked", "likely", "little", "look", "looking", "looks", "ltd", "mainly", "many", "may", "maybe", "me", "mean", "meanwhile", "merely", "might", "more", "moreover", "most", "mostly", "much", "must", "my", "myself", "name", "namely", "nd", "near", "nearly", "necessary", "need", "needs", "neither", "never", "nevertheless", "new", "next", "nine", "no", "nobody", "non", "none", "noone", "nor", "normally", "not", "nothing", "novel", "now", "nowhere", "obviously", "of", "off", "often", "oh", "ok", "okay", "old", "on", "once", "one", "ones", "only", "onto", "or", "other", "others", "otherwise", "ought", "our", "ours", "ourselves", "out", "outside", "over", "overall", "own", "particular", "particularly", "per", "perhaps", "placed", "please", "plus", "possible", "presumably", "probably", "provides", "que", "quite", "qv", "rather", "rd", "re", "really", "reasonably", "regarding", "regardless", "regards", "relatively", "respectively", "right", "said", "same", "saw", "say", "saying", "says", "second", "secondly", "see", "seeing", "seem", "seemed", "seeming", "seems", "seen", "self", "selves", "sensible", "sent", "serious", "seriously", "seven", "several", "shall", "she", "should", "shouldn't", "since", "six", "so", "some", "somebody", "somehow", "someone", "something", "sometime", "sometimes", "somewhat", "somewhere", "soon", "sorry", "specified", "specify", "specifying", "still", "sub", "such", "sup", "sure", "t's", "take", "taken", "tell", "tends", "th", "than", "thank", "thanks", "thanx", "that", "that's", "thats", "the", "their", "theirs", "them", "themselves", "then", "thence", "there", "there's", "thereafter", "thereby", "therefore", "therein", "theres", "thereupon", "these", "they", "they'd", "they'll", "they're", "they've", "think", "third", "this", "thorough", "thoroughly", "those", "though", "three", "through", "throughout", "thru", "thus", "to", "together", "too", "took", "toward", "towards", "tried", "tries", "truly", "try", "trying", "twice", "two", "un", "under", "unfortunately", "unless", "unlikely", "until", "unto", "up", "upon", "us", "use", "used", "useful", "uses", "using", "usually", "value", "various", "very", "via", "viz", "vs", "want", "wants", "was", "wasn't", "way", "we", "we'd", "we'll", "we're", "we've", "welcome", "well", "went", "were", "weren't", "what", "what's", "whatever", "when", "whence", "whenever", "where", "where's", "whereafter", "whereas", "whereby", "wherein", "whereupon", "wherever", "whether", "which", "while", "whither", "who", "who's", "whoever", "whole", "whom", "whose", "why", "will", "willing", "wish", "with", "within", "without", "won't", "wonder", "would", "would", "wouldn't", "yes", "yet", "you", "you'd", "you'll", "you're", "you've", "your", "yours", "yourself", "yourselves", "zero"];

var ssrMapping = {
    "mappings": {
        "recommend": {
            "properties": {
                "counter": {
                    "type": "long"
                },
                "id": {
                    "type": "string",
                    "index": "not_analyzed"
                },
                "cookie": {
                    "type": "nested",
                    "properties": {
                        "search_keyword": {
                            "type": "string"
                        },
                        "clean_search_keyword": {
                            "type": "string"
                        },
                        "search_date": {
                            "type": "date",
                            "format": "strict_date_optional_time||epoch_millis"
                        },
                        "isClicked": {
                            "type": "boolean"
                        },
                        "clickCount": {
                            "type": "long"
                        },
                        "taid": {
                            "type": "string",
                            "index": "not_analyzed"
                        }
                    }
                },
                "cluster_clicks": {
                    "type": "long"
                },
                "uid": {
                    "type": "string",
                    "index": "not_analyzed"
                },
                "cookieId": {
                    "type": "string",
                    "index": "not_analyzed"
                }
            }
        }
    }
}

/*var today = new Date();
var currYear = today.getFullYear();
var currMonth = today.getMonth()+1;
var currDate = today.getDate()+1;
var endDate = currYear+'-'+currMonth+'-'+currDate;
var startDate = (currYear-1)+'-'+currMonth+'-'+currDate;*/


const subscribeConsumer = async () =>{
    try{
        const similarSearchConsumer = await kafkaLib.getConsumer({
            groupId: `admin ${config.get("kafkaTopic.similarSearchTopic")}`,
            topic: config.get("kafkaTopic.similarSearchTopic"),
            fromBeginning: false
        })
        
        await similarSearchConsumer.run({
            eachMessage: async ({
              topic, parition, message, heartBeat
            }) => {
              try {
                console.log('Data from similar Saerch Topic');
                let similarSearchMessage = JSON.parse(message.value);
                const tenantId = similarSearchMessage.key
                if (similarSearchMessage.compressed) {
                    console.log('Recieved GZIP format');
                    similarSearchMessage.data = pako.ungzip(similarSearchMessage.data, { to: 'string' });
                }
                start(JSON.parse(similarSearchMessage.data)['similarSearches'],tenantId);
              } catch (e) {
                  console.log(e)
              }
            }
          });
    }catch(error){
        if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
            kafkaLib.createTopic(config.get("kafkaTopic.similarSearchTopic"));
        }
    }
}

function start(sessionData,tenantId) {
    console.log('In Start() Function');
    checkAndCreateIndex(tenantId, function (err, data) {
        getAnalyticsData(sessionData,tenantId, function (err, data) {
            if (err) {
                console.log(err);
            }
            console.log("DONE");
        });
    })
}

function getAnalyticsData(data, tenantId, cb) {
    if (data.length == 0) {
        console.log('No data received from kafka topic');
        cb("no data", null);
    }
    else {
        updateCountIndocument(data,tenantId, function (errUp, dataUp) {
            cb(null, "All Done");
        });
    }

}

async function updateCountIndocument(searchEntries,tenantId, cb) {
    console.log('In Function() updateCountIndocument');
    const tenantInfo = await getTenantInfoFromTenantId(tenantId);
    const tpk = tenantInfo[0].tpk;
    const ssrIndex = `${tpk}_similarsearchrecommender`;
    bulkArray = [];
    for (var i = 0; i < searchEntries.length; i++) {
        var clean_keyword = preprocessSearch(searchEntries[i].text_entered);
        bulkArray.push({
            "update": {
                "_id": searchEntries[i].cookie + "__" + searchEntries[i].uid,
                "_type": ssrType, "_index": ssrIndex,
                "_retry_on_conflict": 3
            }
        });
        bulkArray.push({
            "script": {
                "lang":"painless",
                "source": "ctx._source.cookie.add(params.new_entry);ctx._source.counter+=1;ctx._source.cluster_clicks+=params.new_entry.clickCount;",
                "params": {
                    "new_entry": {
                        "search_keyword": searchEntries[i].text_entered,
                        "clean_search_keyword": clean_keyword,
                        "search_date": searchEntries[i].search_date,
                        "isClicked": searchEntries[i].is_clicked,
                        "clickCount": searchEntries[i].click_count,
                        "taid": searchEntries[i].taid
                    }
                }
            },
            "upsert": {
                cookie: [{
                    "search_keyword": searchEntries[i].text_entered,
                    "clean_search_keyword": clean_keyword,
                    "search_date": searchEntries[i].search_date,
                    "isClicked": searchEntries[i].is_clicked,
                    "clickCount": searchEntries[i].click_count,
                    "taid": searchEntries[i].taid
                }],
                "counter": 1,
                "id": searchEntries[i].cookie + "__" + searchEntries[i].uid,
                "cluster_clicks": searchEntries[i].click_count,
                "uid": searchEntries[i].uid,
                "cookieId": searchEntries[i].cookie
            }
        });
    }
    const esClient = await getEsClient(tenantId)
    esClient.bulk({
        body: bulkArray
    }, function (err, resp) {
        if (err) {
            console.log(`Error in bulk push of Similar Searches ${err}`);
        }
        cb(null, "");
    });
};

async function checkAndCreateIndex(tenantId,cb) {

    const esClient = await getEsClient(tenantId)
    const tenantInfo = await getTenantInfoFromTenantId(tenantId);
    const tpk = tenantInfo[0].tpk;
    const ssrIndex = `${tpk}_similarsearchrecommender`;

    console.log('In checkAndCreateIndex() function');
    async.auto({
        "checkIndex": cb => {
            esClient.indices.exists({
                index: ssrIndex
            }, function (err, data) {
                if (err) {
                    console.log(err);
                    cb(err, null);
                }
                else {
                    console.log(`Check Index data ${data.body}`);
                    cb(null, data.body);
                }
            })
        },
        "deleteIndex": ["checkIndex", function (DFA, cb) {
            console.log(`Value of isDELETE ${isDelete}`);
            console.log(DFA.checkIndex)
            if (DFA.checkIndex && isDelete) {
                console.log('deleting Index');
                esClient.indices.delete({
                    index: ssrIndex
                }, function (err, data) {
                    if (err) {
                        console.log(`Error in deleting index ${err}`);
                    }
                    cb(null, 1);
                });
            }
            else {
                cb(null, 0);
            }
        }],
        "createIndex": ["deleteIndex", function (DFA, cb) {
            if (DFA.deleteIndex || !DFA.checkIndex) {
                console.log("creating new index " + ssrIndex);
                esClient.indices.create({
                    index: ssrIndex,
                    body: ssrMapping
                }, function (err, data) {
                    if (err) {
                        console.log(`Error in Creating new Index ${err}`);
                        cb(err, null);
                    }
                    else {
                        console.log(ssrIndex + " Index Created");
                        cb(null, data.body);
                    }
                });
            }
            else {
                cb(null, null);
            }
        }]
    }, function (err, data) {
        if (err) {
            console.log(err);
        }
        console.log("DONE");
        cb(null, null);
    });
}

function preprocessSearch(search_text) {
    if (search_text) {
        search_text = search_text.trim();
        search_text = search_text.toLowerCase();
        var arr = search_text.split(/[0-9\s.:\t;,|#()\[\]\{\}!"/\\<>*=+\^?_`~\\&-]+/);
        arr = arr.filter(function (item) {
            item = item.trim();
            if (item.length > 0) {
                if (stopWords.indexOf(item) >= 0)
                    return false;
                else {
                    return true;
                }
            } else {
                return false;
            }
        });
        search_text = arr.join(' ').trim();
        search_text = search_text.trim();
    } else {
        search_text = '';
    }
    return search_text;
}

module.exports = {
    router: router,
    subscribeConsumer
}