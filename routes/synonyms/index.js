var fs = require("fs");
var path = require("path");
var async = require('async');
var request = require("request");
var through = require('through');
var split = require('split');
const config = require('config');
var commonFunctions = require('./../../utils/commonFunctions');
var searchClient = require('./../../routes/admin/searchClient');
const PassThrough = require('stream').PassThrough;
var sy_routes = {};
var searchResultKafkaConfig = require('../admin/searchResultKafkaConfig');
const { startTraining } = require('../../customPlugins/semanticSearch/cron/didYouMean');
const { getEsClient } = require('../../utils/elastic');
const { streamUtils } = require('../../utils/nlp');
const { 
  publishManageIndexesKafka,
  publishSynonymsDictionaryKafka,
  publishSynonymsDictionaryMPKafka,
} = require('../admin/service/nlpKafkaService');

var cron = require('node-cron');

var elastic = require('elasticsearch');
const socketio = require('socket.io');
var client;
let socket;

let isolateSynonymStopwords = config?config.get('isolateSynonymStopwords'):false;
let pocName = config?config.get('poc_name'):'';
let elSynonymPath = "synonyms_org.txt";
let orginalFileElSyn = "synonyms.txt";
// create synonym file if not exists
if( (isolateSynonymStopwords && pocName) &&  !fs.existsSync(path.join(__dirname,'../../synonyms') ) ){
  try{
    fs.mkdirSync('../../synonyms');
  }catch(e){

  }
}

var similar = [];
client = new elastic.Client({
  host: config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port')
});

const kafkaLib = require('../../utils/kafka/kafka-lib');
const { tenantGlobals } = require("../../utils/kafka/kafkaPublishers/tenantGlobals");

exports.index = function (req, res, next) {
  try {
    req.method = req.method == "OPTIONS" ? req.headers["access-control-request-method"] : req.method
    sy_routes[req.params.path][req.method](req, res, next);
  } catch (error) {
    commonFunctions.errorlogger.error("error", error);
    res.send(404);
  }
};

sy_routes['read'] = {};
sy_routes['read']['GET'] = (req, res, next) => {
  res.setHeader("Content-Type", "text/plain");
  if (config.get("onPremises")) {
    var body = {

    }
    commonFunctions.httpRequest('GET', config.get('onPremisesUrl') + '/getSynonymsStopwords?isSynonyms=1', '', body, '', function (err, result) {
      if (!err)
        res.send(result);
      else
        res.end();
    })
  }
  else {

    if(!fs.existsSync(path.join(__dirname,'../../synonyms',`${req.headers.session.tpk}_${elSynonymPath}`))){
      fs.writeFileSync(path.join(__dirname,'../../synonyms', `${req.headers.session.tpk}_${elSynonymPath}`),'');
    }
    
    fs.createReadStream(path.join(__dirname,'../../synonyms', `${req.headers.session.tpk}_${elSynonymPath}`)).pipe(res);


  }
};

sy_routes["write"] = {};
sy_routes["write"]["POST"] = async (req, res, next) => {
  try {
    const tenantId = req.headers["tenant-id"];
    const sessionTpk = req.headers.session.tpk;

    const originalSynPath = path.join(__dirname, "../../synonyms", `${sessionTpk}_${orginalFileElSyn}`);
    const elSynonymFilePath = path.join(__dirname, "../../synonyms", `${sessionTpk}_${elSynonymPath}`);

    // Ensure files exist before writing
    if (!fs.existsSync(originalSynPath)) fs.writeFileSync(originalSynPath, "");
    if (!fs.existsSync(elSynonymFilePath)) fs.writeFileSync(elSynonymFilePath, "");

    // Create PassThrough streams
    const synonymsDict = new PassThrough();
    const synonymsElas = new PassThrough();
    const synonymsElasMultiWord = new PassThrough();

    // Pipe request to streams
    req.pipe(synonymsDict);
    req.pipe(synonymsElas);
    req.pipe(synonymsElasMultiWord);

    let KafkaObject = {};
    let synonymsNew = "";

    // Process synonymsDict stream
    synonymsDict.on("data", (chunk) => {
      KafkaObject = multwordSynAndNonMultiwordSyn(chunk);
      synonymsNew += chunk.toString();
    });

    synonymsDict.on("end", async () => {
      try {
        await Promise.all([
          publishSynonymsDictionaryKafka({ data: synonymsNew, tpk: sessionTpk, tenantId }),
          publishSynonymsDictionaryMPKafka({
            data: KafkaObject.multWordSynonym || [],
            tpk: sessionTpk,
          }),
        ]);
      } catch (error) {
        commonFunctions.errorlogger.error("Error while publishing synonym Kafkas : ", error);
      } finally {
        // Destroy stream after processing
        synonymsDict.destroy();
      }
    });

    await Promise.all([
      streamUtils.processStream(
        synonymsElasMultiWord,
        originalSynPath,
        "synonymsElasMultiWord processing complete"
      ),
      streamUtils.processStream(
        synonymsElas,
        elSynonymFilePath,
        "synonymsElas processing complete"
      ),
    ]);

    // Process Kafka Refresh Message
    await publishManageIndexesKafka({ OPERATION: "REFRESH", OPERATOR: "SYNONYMS", tpk: sessionTpk, tenantId });

    res.end();
  } catch (error) {
    commonFunctions.errorlogger.error("Error in route write synonyms : ", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

const multwordSynAndNonMultiwordSyn = (chunk)=>{
  let dictObject = {
    multWordSynonym: [],
    nonMultiWordSynonym: []
  }

  let CleanMultiArray = chunk.toString().split("\n").filter(s => s != '');

  //rows
  CleanMultiArray.map((item)=>{
    //columns
    let word_col = item.split(',')
    // removed empty space from start
    word_col = word_col.map((element)=> { return element.toLowerCase().trim() });

    let multiwordRemoval = word_col.filter((col)=>{
      //check for multiword 
      if(col.trim().split(' ').length >= 2){
        if(!searchForArray(dictObject.multWordSynonym, word_col)){
          dictObject.multWordSynonym.push(word_col)
        }
      }else{
        return col
      }
      
    })

    dictObject.nonMultiWordSynonym.push(multiwordRemoval.toString())
  })


  return dictObject;
}

function searchForArray(synonymMultiArray, arrayCheck){
  var i, j, current;
  for(i = 0; i < synonymMultiArray.length; ++i){
    if(arrayCheck.length === synonymMultiArray[i].length){
      current = synonymMultiArray[i];
      for(j = 0; j < arrayCheck.length && arrayCheck[j] === current[j]; ++j);
      if(j === arrayCheck.length)
        return true;
    }
  }
  return false;
}


const saveElasticConfig = (url,data, tpk, cb) => {
  //split => node -> 3000
  var options = {
    method: 'POST',
    url: `${url}/saveSynonyms?tpk=${tpk}`,
    headers: { 'content-type': 'application/json' },
    body: { data },
    json: true
};

request(options,(err, response, docs)=>{
  cb({});
})
}

sy_routes['suggestion'] = {};
sy_routes['suggestion']['POST'] = (req, res, next) => {
  if(req.body.type === 'synonym'){
    let tpk = ''
      if (req.headers.session && req.headers.session.tpk) {
        tpk = req.headers.session.tpk;
      }
    let elasticIndex = config.get("elasticIndexCS.synonymIndex")
    if(config.get("multiTenantSupport")){
        elasticIndex  = tpk+'_'+elasticIndex
      }
    body= {
      tpk :`${tpk}`,
      index: elasticIndex,
      type: req.body.type,
      tenantId: req.headers["tenant-id"],
    };
  let options = {
      method: "GET",
      url:config.get("synonymUrl") + `/fetchSynonym?tenantId=${body.tenantId}&tpk=${body.tpk}&type=synonyms`,
      headers: {
          "content-type": "application/json",
      },
      json: true,
    }
    request(options, (error, response, resBody) => {
      if (error || resBody.error)
        res.send({ "status": "ERROR", "message": "Something went wrong. please try again later." });
      else {
        var synonym = resBody.map(x => {
          return {
            "typeList": x._type,
            "type": (x._type == "synonym") ? true : false,
            "removed_terms": [],
            "term": x.synonyms.word.toLowerCase(),
            "values": x.synonyms.synonyms.map(function (x) {
              return x.toLowerCase()
            }),
            "deletedValues": x.synonyms.deleted_synonyms.map(function (x) {
              return x.toLowerCase()
            }),
            "isCheckedValues": x.synonyms.synonyms,
            "movedValues": [],
            "extra_added_synonym": [],
            "selectedValues": [],
            "isOpened": false,
            "activeHover": false,
            "activeMore": false,
            "activeHover": false
          }
        })
        for (var i = 0; i < synonym.length; i++) {
          if (synonym[i].deletedValues.length) {
            for (var j = 0; j < synonym[i].deletedValues.length; j++) {
              const indexNumber = synonym[i].values.indexOf(synonym[i].deletedValues[j]);
              if (indexNumber >= 0) {
                synonym[i].values.splice(indexNumber, 1);
              }
            }
          }
        }
        res.send({ "status": "OK", "synonymList": synonym });
      }
    });
  }
  else if (req.body.type === 'accronym') {
    let tpk = ''
    if (req.headers.session && req.headers.session.tpk) {
      tpk = req.headers.session.tpk;
    }
    let elasticIndex = config.get("elasticIndexCS.accronymIndex")
    if(config.get("multiTenantSupport")){
      elasticIndex  = tpk+'_'+elasticIndex
    }
    body= {
      tpk :tpk,
      index: elasticIndex,
      type: req.body.type,
      tenantId: req.headers["tenant-id"],
    };
  let options = {
      method: "GET",
      url:config.get("synonymUrl") + `/fetchSynonym?tenantId=${body.tenantId}&tpk=${body.tpk}&type=accronyms`,
      headers: {
          "content-type": "application/json",
      },
      json: true,
    }
    request(options, (error, response, resBody) => {
      if (resBody.error)
        res.send({ "status": "ERROR", "message": "Something went wrong. please try again later." });
      else {
        resBody = resBody.filter(x => { return !x._source.is_stopword });
        // Filter To Discard if list has more than 10 synonym
        resBody= resBody.filter(x => { return x._source.accronymlist.length <= 10 });
        var accronym = resBody.map(x => { return { "typeList": x._type, "type": (x._type == "synonym") ? true : false, "removed_terms": [], "term": x._source.word.toLowerCase(), "values": x._source.accronymlist.map(function (x) { return x.toLowerCase() }), "deletedValues": x._source.deleted_accronyms.map(function (x) { return x.toLowerCase() }), "isCheckedValues": x._source.accronymlist, "movedValues": [], "extra_added_synonym": [], "selectedValues": [], "isOpened": false, "activeHover": false, "activeMore": false, "activeHover": false } })
        res.send({ "status": "OK", "synonymList": accronym });
      }
    });
  }
};

/* sy_routes['saveSynonymList'] = {};
sy_routes['saveSynonymList']['POST'] = (req, res, next) => {
  commonFunctions.errorlogger.info("Synonym List for Saving: ", req.body.synonym)
  var bulkArray = [];
  for (var i = 0; i < req.body.synonym.length; i++) {
    bulkArray.push({
      "update": {
        "_id": req.body.synonym[i].word,
        "_type": req.body.synonym[i].type,
        "_index": config.get('elasticIndexCS.synonymIndex'),
        "_retry_on_conflict": 3
      }
    });
    var upsertBody = {
      "script": {
        //"inline": "if(ctx._source.containsKey(\"synonyms\") && ctx._source.synonyms.containsKey(\"savedSynonyms\")){ctx._source.docs.synonyms.savedSynonyms=ctx._source.docs.synonyms.savedSynonyms+item}",
        "inline": "ctx._source.synonyms.savedSynonyms = item",
        "params": {
          "item": req.body.synonym[i].synonym
        }
      },
      "upsert": {
        "synonyms": {
          "synonyms": [],
          "savedSynonyms": req.body.synonym[i].synonym,
          "word": req.body.synonym[i].word,
          "active": true
        }
      }
    }
    bulkArray.push(upsertBody);
  }

  client.bulk({
    body: bulkArray
  }, (err, result) => {
    res.send({ "status": "ok" });
  });

} */

sy_routes['deleteSynonym'] = {};
sy_routes['deleteSynonym']['POST'] = (req, res, next) => {
  commonFunctions.errorlogger.info("Deleted Synonym List for Saving: ", req.body.synonym)
  let tpk = ''
  if (req.headers.session && req.headers.session.tpk) {
    tpk = req.headers.session.tpk;
  }
  var bulkArray = [];
  let indexName;
  if(req.body.synonym && req.body.synonym.length && req.body.synonym[0].type == 'synonym'){
    indexName = "synonyms";    
    for (var i = 0; i < req.body.synonym.length; i++) {
      console.log("deleted array", req.body)
      bulkArray.push({
          "_id": req.body.synonym[i].word.toLowerCase(),
          "deleted_synonyms": req.body.synonym[i].synonym
      });
    }
    console.log(bulkArray)
  }
  if(req.body.synonym && req.body.synonym.length && req.body.synonym[0].type == `${tpk}_accronyms`){
    indexName = "accronyms";
    for (var i = 0; i < req.body.synonym.length; i++) {
      bulkArray.push({
        "_id": req.body.synonym[i].word.toLowerCase(),
        "deleted_accronyms": req.body.synonym[i].synonym
    });
    }
  }


  let options = {
    method: "POST",
    url: `${config.get("synonymUrl")}/deleteSynonym`,
    body: {
      tenantId: req.headers["tenant-id"],
      tpk:`${tpk}`,
      type:indexName,
      data: bulkArray
    },
    headers: {
        "content-type": "application/json",
    },
    json: true,
  }

  request(options, (error, response, resBody) => {
    if (error || resBody.errors)
      res.send({ "status": "Got Some Error", "statuscode": 405 });
    else
      res.send({ "status": "List Deleted", "statuscode": 200 });
    });
}

sy_routes['readSynonymSettings'] = {};
sy_routes['readSynonymSettings']['POST'] = (req, res, next) => {

  // var data = req.body;

  getSynonymSettings(req,function( err, response){
    if(!err){
      res.send(response)
    }
    
  });

}

const getSynonymSettings = (req,cb)=>{
  let sql = `select * from synonym_settings`;
  connection[req.headers['tenant-id']].execute.query(sql, function (err, result) {
    if (!err) {
      cb(null, result);
    }
    else {
      commonFunctions.errorlogger.error(err);
      cb(err, null);
    }
  });
}

sy_routes['updateSynonymSettings'] = {};
sy_routes['updateSynonymSettings']['POST'] = async (req, res, next) => {
  const tenantId  = req.headers['tenant-id'];
  const esClient = await getEsClient(tenantId);
  var data = req.body;
  if(!fs.existsSync(path.join(__dirname,'../../synonyms',`${req.headers.session.tpk}_${orginalFileElSyn}`))){
    fs.writeFileSync(path.join(__dirname,'../../synonyms',`${req.headers.session.tpk}_${orginalFileElSyn}`));
  }
  if(!fs.existsSync(path.join(__dirname,'../../synonyms',`${req.headers.session.tpk}_${elSynonymPath}`))){
    fs.writeFileSync(path.join(__dirname,'../../synonyms',`${req.headers.session.tpk}_${elSynonymPath}`),'');
  }
  async.waterfall([
    //update Sql
    function (callback) {
      let sql = `UPDATE synonym_settings set exact_match = ? WHERE  id = 1`;
      connection[req.headers['tenant-id']].execute.query(sql, data.flag, function (err, result) {
        if (!err) {
          callback(null, result);
        } else {
          callback(err, null);
        }
      })
    },
    function (SqlResult, callback) {
      // write file on the basis of on/off toggle 
      fs.copyFile(path.join(__dirname,'../../synonyms',`${req.headers.session.tpk}_${elSynonymPath}`), path.join(__dirname, "../../synonyms", `${req.headers.session.tpk}_${orginalFileElSyn}`), (err) => { 
        if (err) { 
          commonFunctions.errorlogger.info("Error Found:", err); 
          callback(err);
        } 
        else { 
          commonFunctions.errorlogger.log('Synonym.txt file replaced with org file');
          callback(null, 'File Copied')
        } 
      }); 
    },
    function (resultFromABove,callback) {
      tenantGlobals(req.headers['tenant-id'], { exactMatch : data.flag == 1 })
      callback(null, {msg: 'Message Published'});
    }
  ], function (err, results) {
    res.send(results)
  })
}

/* sy_routes['deleteSynonym'] = {};
sy_routes['deleteSynonym']['POST'] = (req, res, next) => {
  commonFunctions.errorlogger.info("Deleting Synonym", req.body.synonym)
  var updateString = "";
  for (var i = 0; i < req.body.synonym.length; i++) {
    var line1 = {
      "update": {
        "_id": req.body.synonym[i].word,
        "_index": config.get("elasticIndexCS.synonymIndex")
      }
    }
    if (req.body.synonym[i].type == "synonym") {
      line1.update["_type"] = "synonym";
    }
    else {
      line1.update["_type"] = "accronym";
    }
    var line2 = { "doc": { "synonyms": { "deleted_synonyms": req.body.synonym[i].synonym } } }
    updateString += JSON.stringify(line1) + "\n" + JSON.stringify(line2) + "\n";
  }
  updateString = updateString.slice(0, updateString.length - 1);
  client.bulk({
    body: updateString
  }, (err, result) => {
    if (err)
      res.send({ "status": "Got Some Error", "statuscode": 405 });
    else
      res.send({ "status": "List Deleted", "statuscode": 200 });
  });
} */

// suggestions removal

sy_routes['getSuggestionsRemovedPost'] = {};
sy_routes['getSuggestionsRemovedPost']['POST'] = (req, res, next) => {
  var data = req.body;
  let sql = `select * from suggestions_removed WHERE search_client_id = ${data.search_client_id}`;
  connection[req.headers['tenant-id']].execute.query(sql, data, function (err, result) {
    if (!err) {
      res.send(result);
    }
    else {
      commonFunctions.errorlogger.error(err);
    }
  })
};

sy_routes['addSuggestionsRemoved'] = {};
sy_routes['addSuggestionsRemoved']['POST'] = (req, res, next) => {
  var data = req.body;

  deleteSuggestionsRemoved(data.search_client_id,req, function (err, delres) {

    if(delres && data.values != 0){
    let sql = `INSERT INTO suggestions_removed ( search_client_id, text) VALUES `

     let queryArray = []
    data.values.map((item)=>{
      sql += `(?,?),`;
      queryArray.push(data.search_client_id, item)
    })

    sql = sql.substring(0, sql.length - 1)
    
    connection[req.headers['tenant-id']].execute.query(sql, queryArray, function (err, result) {
      if (!err) {
        commonFunctions.CleanRedisCache();
        let configObj = {
          "platformId": data.search_client_id
        };
        searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
          res.send(result);
        });
      } else {
        commonFunctions.errorlogger.error(err);
      }
    })

  }else{
    res.send([])
  }

  });
  
};

// sy_routes['deleteSuggestionsRemoved'] = {};
// sy_routes['deleteSuggestionsRemoved']['POST'] = (req, res, next) => {
//   var data = req.body.data;
//   let sql = `DELETE FROM suggestions_removed WHERE id = ${data.id}`;
//   connection[req.headers['tenant-id']].execute.query(sql, data, function (err, result) {
//     if (!err) {
//       commonFunctions.CleanRedisCache();
//       let configObj = { "platformId": data.search_client_id };
//       searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj, function (err, searchConfig) {
//         res.send(result);
//       });
//     }
//     else {
//       commonFunctions.errorlogger.error(err);
//     }
//   })
// };

function deleteSuggestionsRemoved(search_client_id,req,cb){
  let sql = `DELETE FROM suggestions_removed WHERE search_client_id = ?`;
  connection[req.headers['tenant-id']].execute.query(sql, [search_client_id], function (err, result) {
    if (!err) {
      commonFunctions.CleanRedisCache();
      let configObj = { "platformId": search_client_id };
      searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
        cb(err, result);
      });
    }
    else {
      commonFunctions.errorlogger.error(err);
    }
  })
}

sy_routes['getAllContentSourceData'] = {};
sy_routes['getAllContentSourceData']['GET'] = (req, res, next) => {
  let sql = "SELECT "
    + " cs.id as content_source_id, cs.name as content_source_name, cs.label as content_source_label,"
    + " cso.id as object_id , cso.name as object_name, cso.label as object_label, "
    + " csof.id as field_id, csof.name as field_name, csof.label as field_label "
    + " FROM content_source_object_fields as csof"
    + " JOIN content_source_objects as cso ON csof.content_source_object_id = cso.id "
    + " JOIN content_sources cs On cs.id = cso.content_source_id "
    if(!req.query.mergedFields) {
      sql = sql.concat("and csof.merge_field_id = 0");
    }
    connection[req.headers['tenant-id']].execute.query(sql, function (err, result) {
    if (err) {
      commonFunctions.errorlogger.error(err);
      res.send({ "status": 400, "error": err });
    }
    else if (result.length == 0) {
      res.send({ "status": 200, "res": [] });
    }
    else {
      var csArr = [], csObj = {};
      var csoArr = [], csoObj = {};
      var csofArr = [], csofObj = {};
      for (var i = 0; i < result.length; i++) {
        if (!csObj[result[i].content_source_id]) {
          csObj[result[i].content_source_id] = {};
          csArr.push({
            "csId": result[i].content_source_id,
            "csName": result[i].content_source_name,
            "csLabel": result[i].content_source_label
          });
        }
        if (!csoObj[result[i].object_id]) {
          csoObj[result[i].object_id] = {};
          csoArr.push({
            "csoId": result[i].object_id,
            "csoName": result[i].object_name,
            "csoLabel": result[i].object_label,
            "csId": result[i].content_source_id
          });
        }
        if (!csofObj[result[i].field_id]) {
          csofObj[result[i].field_id] = {};
          csofArr.push({
            "csofId": result[i].field_id,
            "csofName": result[i].field_name,
            "csofLabel": result[i].field_label,
            "csoId": result[i].object_id
          });
        }
      }
      res.send({
        "status": 200,
        "res": {
          "csArr": csArr,
          "csoArr": csoArr,
          "csofArr": csofArr
        }
      });
    }
  })
};

sy_routes['getDidYouMeanSavedEntries'] = {};
sy_routes['getDidYouMeanSavedEntries']['GET'] = (req, res, next) => {
  let sql1 = "SELECT DISTINCT content_source_object_id FROM `content_source_object_fields` WHERE did_you_mean = 1"
  connection[req.headers['tenant-id']].execute.query(sql1, function (err1, result1) {
    if (err1) {
      commonFunctions.errorlogger.error(err1);
      res.send({ "status": 400, "error": "Error In Fetching Saved Data" });
    }
    else if (result1.length == 0) {
      res.send({ "status": 400, "error": "No  Entries for Did You Mean" });
    }
    else {
      var objectIdArray = [];
      for (var i = 0; i < result1.length; i++) {
        objectIdArray.push(result1[i].content_source_object_id);
      }
      objectIdArray = objectIdArray.join(',');
      let sql = "SELECT "
        + " cs.id as content_source_id, cs.name as content_source_name, cs.label as content_source_label,"
        + " cso.id as object_id , cso.name as object_name, cso.label as object_label, "
        + " csof.id as field_id, csof.name as field_name, csof.label as field_label, csof.did_you_mean "
        + " FROM content_source_object_fields as csof"
        + " JOIN content_source_objects as cso ON csof.content_source_object_id = cso.id "
        + " JOIN content_sources cs On cs.id = cso.content_source_id "
        + " WHERE cso.id IN (" + objectIdArray + ") ;";
        connection[req.headers['tenant-id']].execute.query(sql, function (err, result) {
        if (err) {
          commonFunctions.errorlogger.error(err);
          res.send({ "status": 400, "error": "Error In Fetching Saved Data" });
        }
        else {
          var cs_csoObj = {};
          for (var i = 0; i < result.length; i++) {
            if (!cs_csoObj[result[i].content_source_id + "_" + result[i].object_id]) {
              cs_csoObj[result[i].content_source_id + "_" + result[i].object_id] = {
                "id": result[i].content_source_id + "_" + result[i].object_id,
                "csId": result[i].content_source_id,
                "csName": result[i].content_source_name,
                "csLabel": result[i].content_source_label,
                "csoId": result[i].object_id,
                "csoName": result[i].object_name,
                "csoLabel": result[i].object_label,
                "isDisabled": true,
                "allFieldsArray": [{
                  "csofId": result[i].field_id,
                  "csofName": result[i].field_name,
                  "csofLabel": result[i].field_label,
                  "csofDidYouMean": result[i].did_you_mean
                }],
                "fieldsArray": []
              };
            }
            else {
              cs_csoObj[result[i].content_source_id + "_" + result[i].object_id].allFieldsArray.push({
                "csofId": result[i].field_id,
                "csofName": result[i].field_name,
                "csofLabel": result[i].field_label,
                "csofDidYouMean": result[i].did_you_mean
              });
            }
            if (result[i].did_you_mean) {
              cs_csoObj[result[i].content_source_id + "_" + result[i].object_id].fieldsArray.push({
                "csofId": result[i].field_id,
                "csofName": result[i].field_name,
                "csofLabel": result[i].field_label,
                "csofDidYouMean": result[i].did_you_mean
              });
            }
          }
          var cs_csoArr = [];
          for (var i = 0; i < Object.keys(cs_csoObj).length; i++) {
            cs_csoArr.push(cs_csoObj[Object.keys(cs_csoObj)[i]]);
          }
          res.send({ "status": 200, "res": cs_csoArr });
        }
      })
    }
  });
};

sy_routes['saveDidYouMeanEntries'] = {};
sy_routes['saveDidYouMeanEntries']['POST'] = (req, res, next) => {
  commonFunctions.errorlogger.info(req.body.data);
  var enabled_dym_Entries = [];
  req.body.data.map(x => {
    x.fieldsArray.map(y => {
      enabled_dym_Entries.push(y.csofId);
    })
  });
  if (enabled_dym_Entries.length > 0) {
    var sql = "UPDATE content_source_object_fields "
      + " SET did_you_mean = CASE "
      + " WHEN id IN (" + enabled_dym_Entries.map(() => '?').join(',') + ") THEN "
      + " 1 "
      + " ELSE "
      + " 0 "
      + " END;"
  }
  else {
    var sql = "UPDATE content_source_object_fields "
      + " SET did_you_mean = 0"
  }
  connection[req.headers['tenant-id']].execute.query(sql, enabled_dym_Entries ,function (err, result) {
    if (err) {
      commonFunctions.errorlogger.error("ERR_IN_UPDATE" + err);
      res.send({ "status": 400, "error": err });
    }
    else {
      res.send({ "status": 200, "res": "Updated" });
    }
  })
};

const didYouMeanTrainingConfiguration = (date,req) => {
  return new Promise((resolve,reject)=>{
    startTraining(req,() => {
      var sql = "UPDATE dym_train_dictionary "
          + " SET is_enable = 1, last_train_date = ?, pid = ? WHERE id = 0;"
      const trainingDate = date ? date : new date();
      var currDate = new Date(trainingDate);
      connection[req.headers['tenant-id']].execute.query(sql, [currDate, -1], function (error, data) {
          if (error) {
              commonFunctions.errorlogger.error(error);
              reject({ "status": 400, "res": "Not Updated" })
          }
          else {
              var dropEventSql = "DROP Event update_status"
              connection[req.headers['tenant-id']].execute.query(dropEventSql, function (error2, data2) {
                  var eventSql = "CREATE EVENT update_status ON SCHEDULE AT "
                      + "CURRENT_TIMESTAMP + INTERVAL 12 HOUR "
                      + "DO "
                      + "UPDATE dym_train_dictionary "
                      + "SET is_enable = 0, pid = 0 where id = 0;"
                      connection[req.headers['tenant-id']].execute.query(eventSql, function (error1, data1) {
                      if (error1) {
                          commonFunctions.errorlogger.error(error1);
                      }
                      resolve({ "status": 200, "res": "Updated" });
                  });
              });
          }
      });
  })
  })
}

sy_routes['getDYMServiceStatus'] = {};
sy_routes['getDYMServiceStatus']['GET'] = async (req, res, next) => {
  const options = {
    method:"GET",
    url:config.get('didYouMean.url')
  }
  request(options,(error,body,response)=>{
    if(error){
      return res.send({"ServiceUp":0});
    }
    return res.send({"ServiceUp":1});
  })
}

sy_routes['trainDymDictionary'] = {};
sy_routes['trainDymDictionary']['GET'] = async (req, res, next) => {
  try{
    const date = req.query.date;
    const response = await didYouMeanTrainingConfiguration(date,req)
    res.send(response)
  }catch(error){
    res.send(error)
  }
};

if(config.get('didYouMean.enableCron')){
  // crontab run everyweek on sunday
  cron.schedule(config.get('didYouMean.cronTimer'), () => {
    didYouMeanTrainingConfiguration(new Date(),req);
  });
}



sy_routes['dymTrainingStatus'] = {};
sy_routes['dymTrainingStatus']['POST'] = async (req, res, next) => {
  try{
    await didYouMeanTrainingCompleted(req);
    res.send({"updated":1})
  }catch(err){
    res.send({"updated":0})
  }
}

const didYouMeanTrainingCompleted = (data) => {
  return new Promise((resolve,reject)=>{
    var sql = "UPDATE dym_train_dictionary "
    + " SET is_enable = 0, pid = ? WHERE id = 0;"
    connection[data.tenantId].execute.query(sql, [0], function (error, data) {
      if (error) {
        console.error('Error during train update:', error);
        return reject(error);  // Reject the promise with the error
      }
      console.log('dym train status update response: ', data);
      resolve(); // Resolve the promise if no error
    })
  })
}


sy_routes['getDymTrainButtonInfo'] = {};
sy_routes['getDymTrainButtonInfo']['GET'] = (req, res, next) => {
  var sql = "Select * from dym_train_dictionary"
  connection[req.headers['tenant-id']].execute.query(sql, function (error, data) {
    if (error) {
      commonFunctions.errorlogger.error(error);
      res.send({ "status": 400, "res": "Some Error in Getting Dictionary Info" });
    }
    else {
      res.send({ "status": 200, "res": data[0] });
    }
  });
};


sy_routes['exactSynSettings'] = {};
sy_routes['exactSynSettings']['POST'] = (req, res, next) => {

}
function isStopWord(word) {
  return false;
}

function excludeWordForSynonym(word) {
  if (!isNaN(word.trim()))
    return true;
  if (isStopWord(word))
    return true;
  return false;
}

function tokenizer(str) {
  var str0 = str.split(/[\s.:\t;,|#()\[\]\{\}!"/\\<>*=+\^?_`~\\&-]+/).join(" ");
  var s1 = str0.toLowerCase().replace(/(\b(\w{1,3})\b(\s|$))/g, '').split(" ");
  return s1;
}

function findSimilarWords(strs) {
  if (strs.length >= 2) {
    for (var i = 0; i < strs.length; i++) {
      for (var j = i + 1; j < strs.length; j++) {
        var s1 = tokenizer(strs[i]);
        commonFunctions.errorlogger.info("s1", s1);

        var s2 = tokenizer(strs[j]);
        commonFunctions.errorlogger.error("s2", s2);

        var atLeastOneMatches = 0;
        var k = 0, l = 0;
        var flag = 0;
        while (k < s1.length) {
          var match = 0;
          while (l < s2.length) {
            if (k >= s1.length)
              break;
            s1[k] = excludeWordForSynonym(s1[k]) ? '' : s1[k];
            s2[l] = excludeWordForSynonym(s2[l]) ? '' : s2[l];
            if (s1[k] != '' && s2[l] != '') {
              if (s1[k] != s2[l]) {
                if (s1[k].indexOf(s2[l]) >= 0 || s2[l].indexOf(s1[k]) >= 0) {
                  s1 = s1.map(function (s) {
                    return s.indexOf(s1[k]) >= 0 ? s1[k] : s;
                  });
                  var idx = s1.lastIndexOf(s1[k]);
                  s1.splice(k, (idx - k) + 1);
                  s2 = s2.map(function (s) {
                    return s.indexOf(s2[l]) >= 0 ? s2[l] : s;
                  });
                  idx = s2.lastIndexOf(s2[l]);
                  s2.splice(l, (idx - l) + 1);
                  match++;
                  flag = 1;
                } else {
                  l++;
                }
              } else {
                var idx = s1.lastIndexOf(s1[k]);
                s1.splice(k, (idx - k) + 1);
                idx = s2.lastIndexOf(s2[l]);
                s2.splice(l, (idx - l) + 1);
                match++;
                flag = 1;
              }
            } else {
              if (s1[k] == '') {
                s1.splice(k, 1);
                flag = 1;
              }

              if (s2[l] == '') {
                s2.splice(l, 1);
              } else {
                l++;
              }
            }
          }
          if (!flag)
            k++;
          flag = 0;
          l = 0;
          if (match)
            atLeastOneMatches = 1;
        }
        if (atLeastOneMatches && s1.length == 1 && s2.length == 1) {
          //console.log(s1[0]+' ~ '+s2[0]);
          if (editDist(s1[0], s2[0], s1[0].length, s2[0].length) > 2) {
            similar.push([s1[0], s2[0], strs[i], strs[j], editDist(s1[0], s2[0], s1[0].length, s2[0].length)]);
          }
        }
      }
    }
  }
}

function getMoreUntilDone(error, response, index, counter, callback1) {
  if (!error && typeof response.hits != 'undefined' &&
    response.hits.hits != 'undefined' && response.hits.hits.length > 0) {
    // preprocess and store to prediction IO event server
    var clusters = response.hits.hits;
    var clustersFunc = clusters.map(function (cluster) {
      return function (cb) {
        findSimilarWords(cluster._source.text);
        cb(null, 'success');
      }

    });
    async.parallelLimit(clustersFunc, 20, function (er, re) {
      commonFunctions.errorlogger.info('finished with ' + counter + ' batch');
      counter++;
      commonFunctions.errorlogger.warn('requesting for next batch of data');
      client.scroll({
        scrollId: response._scroll_id,
        scroll: '30s'
      }, function (err, data) {
        return getMoreUntilDone(err, data, index, counter, callback1);
      });
    })

    /////////////////////////////////////////////////////

  } else {
    commonFunctions.errorlogger.info('response end for request no. ', counter);
    callback1();
    commonFunctions.errorlogger.info('number of similar items found: ', similar.length);
  }
}


function editDist(str1, str2, m, n) {
  // Create a table to store results of subproblems
  var dp = [];

  // Fill d[][] in bottom up manner
  for (i = 0; i <= m; i++) {
    dp.push([]);
    for (j = 0; j <= n; j++) {
      // If first string is empty, only option is to
      // isnert all characters of second string
      if (i == 0) {
        dp[i][j] = j;  // Min. operations = j
      }
      // If second string is empty, only option is to
      // remove all characters of second string
      else if (j == 0) {

        dp[i][j] = i; // Min. operations = i
      }
      // If last characters are same, ignore last char
      // and recur for remaining string
      else if (str1.charAt(i - 1) == str2.charAt(j - 1))
        dp[i][j] = dp[i - 1][j - 1];
      // If last character are different, consider all
      // possibilities and find minimum
      else
        dp[i][j] = 1 + Math.min(dp[i][j - 1],  // Insert
          dp[i - 1][j],  // Remove
          dp[i - 1][j - 1]); // Replace
    }
  }

  return dp[m][n];
}

const listen = (server) => {
  const io = socketio(server, { path: '/socket.io' });
  io.on('connection', (sock) => {
    socket = sock;
    socket.on('disconnect', () => {
      console.log('User disconnected');
    });
  });
}

exports.listen = listen;
exports.didYouMeanTrainingCompleted = didYouMeanTrainingCompleted;
