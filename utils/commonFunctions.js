var request = require('request');
var axios = require('axios');
var async = require('async');
var metadata = require('../metadata/mappingForDbAndElastic')
var constants = require('../constants/constants');
var appVariables = require('../constants/appVariables')
var xml2js = require('xml2js');
var htmlparser = require('htmlparser');
var parser = new xml2js.Parser();
var builder = new xml2js.Builder();
var sanitizeHtml = require('sanitize-html');
var jsforce = require('jsforce');
var striptags = require('striptags');
const aes256 = require('nodejs-aes256');
var path = require('path');
var clickScorePath;
var statusPageAuth = require('../routes/admin/statusPageAuth');
var elastic = require('elasticsearch');
var emailTemplates = require('../routes/emailTemplates');
var clientCS = new elastic.Client({
  host: 'http://' + config.get('elasticIndexCS.host') + ':' + config.get('elasticIndexCS.port')
});
const { getEsClient } = require('../utils/elastic');
const searchunifyEmail = require('../Lib/email.js');
const setCookie = require('set-cookie-parser');
const { promisify } = require('util');
const dns = require('dns');
const dnsResolve = promisify(dns.resolve);
const url = require('url');
const ipRangeCheck = require('ip-range-check');

const redis = require('redis');
const redisclient = redis.createClient(config.get("redis.redisPort"), config.get("redis.redisHost"));

var fs = require("fs");
var errorlogger = require('tracer').console({
  level: config.get('debuglevel'),
  format : "{{timestamp}} <{{title}}> {{message}} (in {{file}}:{{line}})"

});

var crawlerlogger = require('tracer').console({
  level: config.get('debuglevel'),
  format : "{{timestamp}} <{{title}}> {{message}} (in {{file}}:{{line}}) @crawlinglogs@@"

});
var jwt = require('jsonwebtoken');
const { getTenantsRateLimitInfo } = require("auth-middleware");
const RateLimitInfoStorage = require('./singleton-storage/rate-limit-storage.js');
redisclient.hincrbyAsync = promisify(redisclient.hincrby).bind(redisclient);
const modelContentSource = ['id',
  'name',
  'label',
  'elasticIndexName',
  'url',
  'content_source_type_id',
  'sync_start_date',
  'sync_frequency',
  'sync_status',
  'pid',
  'last_sync_date',
  'language',
  'depth',
  'sync_frequency_name',
  'searchunifyIndexUrl',
  'isFileUploaded',
  'logFile',
  'thread',
  'batch',
  'crawl_status',
  'current_crawl_mode',
  'refreshIndex',
  'adminLogFile',
  'current_crawl_start_time',
  'is_paused'
]
const modelContentSourceAuthorization = ['id',
  'authorization_type',
  'client_secret',
  'client_id',
  'username',
  'password',
  'htaccessUsername',
  'htaccessPassword',
  'accessToken',
  'refreshToken',
  'tenantId',
  'callBackUrl',
  'instanceURL',
  'organization_user_type',
  'sessionId',
  'publicKey',
  'privateKey',
  'content_source_id',
  'connection_status',
  'usernameselector',
  'passwordselector',
  'buttonselector',
  'loginlink',
  'is_authenticated',
  'multi_form_auth'
]

const modelFields = [
  'id',
  'content_source_object_id',
  'name',
  'label',
  'type',
  'isFilterable',
  'isSortable',
  'isSearchable',
  'isActive',
  'selector',
  'single_multiple',
  'isMerged',
  'regex',
  'regex_value',
  'annotated',
  "merge_field_id"
]
const modelFilters = [
  'id',
  'priority'
]
const suDomains = config.get('suDomains');

const BLOCKED_IP_RANGES = [
  '10.0.0.0/8',
  '**********/12',
  '***********/16',
  '*********/8',
  '0.0.0.0/8'
];

async function validateUrl(userUrl) {
  // 1. Schema Validation
  if (!/^https:\/\//i.test(userUrl)) {
    console.log('Only HTTPS protocols allowed');
    return false;
  }

  // 2. URL Parsing
  const parsed = url.parse(userUrl);
  if (!parsed.hostname) {
    console.log('Invalid URL format');
    return false;
  }

  // 3. DNS Resolution
  const ips = await dnsResolve(parsed.hostname);
  
  // 4. IP Validation
  for (const ip of ips) {
    if (isPrivateIP(ip)) {
      console.log(`Access to private IP (${ip}) blocked`);
      return false;
    }
  }

  return true;
}

function isPrivateIP(ip) {
  return BLOCKED_IP_RANGES.some(range => ipRangeCheck(ip, range));
}

async function urlValidityCheck(testUrl) {
    const isValid = await validateUrl(testUrl);
    return isValid;
}

var getCountIndexName = function (indexname, req, callback) {
  // console.log(JSON.stringify(JiveMappingObject));
  if (config.get("onPremises")) {
    var body = {
      index: indexname,
    }
    httpRequest('POST', config.get('onPremisesUrl') + '/count', '', body, '', function (err, result) {
      if (!err) {
        result = result.body.count ? result.body.count : 0
        callback(null, result)
      }
      else
        callback(err)
    })
  }
  else {
    let esNode = getRandomClusterNode(req.headers.session.esClusterIp);
    var options = {
      method: 'GET',
      url: `${esNode}/${indexname}/_count`,
    };

    request(options, function (error, response, body) {
      if (error) {
        errorlogger.error('error',error)
        callback(0);
      }
      else {
        // errorlogger.info('body',body);
        body = JSON.parse(body);
        body.count = body.count ? body.count : 0
        callback(null, body.count)
      }
    });
  }
}

const getRandomClusterNode = (clusterHosts) => {
  clusters = clusterHosts.split(',');
  let index = Math.floor(Math.random() * clusters.length);
  return clusters[index];
};

const getAllIndexCount = function(req, callback){
  let tenantPrefix = "";
  if(config.get('multiTenantSupport') && config.get('multiTenantSupport') === true){
    tenantPrefix = `${req.headers.session.tpk}_`;
  }
  let esNode = getRandomClusterNode(req.headers.session.esClusterIp);

  const options = {
    method: 'POST',
    url: `${esNode}/${tenantPrefix}*/_search`,
    body: JSON.stringify({
      "aggs": {
        "by_index": {
          "terms": {
            "field": "_index",
            "size": 500
          }
        }
      },
      "size": 0
    })
  };

  request(options, function (error, response, body) {
    if (error) {
      errorlogger.error('Error in getAllIndexCount', error)
      callback("Some error occurred while fetching elastic index count");
    }
    else {
      try{
        body = JSON.parse(response.body);
        if(body && body.aggregations && body.aggregations.by_index){
          return callback(null, body.aggregations.by_index.buckets);
        }
      }
      catch(errs){
        errorlogger.error('Error while parsing body in getAllIndexCount',error);
      }
      callback(null, []);
    }
  });
}

var getCountIndexSize = async (req ,indexname, callback) => {
  const esClient = await getEsClient(req.headers['tenant-id']);
  esClient.indices.stats({
    index: indexname
  }, 
function (error, response, body) {
    if (error) {
      errorlogger.error('error',error)
      callback(0);
    }
    else {
      body = response.body;
      callback(null, body._all ? bytesToSize(body._all.primaries.store ? body._all.primaries.store.size_in_bytes : 0) : 0);
    }
  }
)
}


function bytesToSize(bytes) {
  var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes == 0) return '0 Byte';
  var i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
  return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i];
};

const insertUpdateContentSource = function (contentSource,req, callback) {  
  const colums = []
  const parameters = []
  let d = new Date();
  // console.log('ksts', Object.keys(contentSource));
  let contentSourceAddedFirstTime = false;
  contentSource["sync_start_date"] = contentSource["sync_start_date"] ? new Date(contentSource["sync_start_date"]) : new Date(d);
  contentSource["sync_start_date"] = new Date(new Date(contentSource["sync_start_date"]).setHours(0,0,0,0));
  // contentSource["sync_frequency"] = contentSource["sync_frequency"] ? (contentSource["sync_frequency"]) : 0;

  let updateSyncFreq = shouldFreqBeUpdated(contentSource["sync_frequency"], contentSource["sync_frequency_name"], contentSource["id"])

  contentSource.sync_frequency = Array.isArray(contentSource.sync_frequency) ? contentSource.sync_frequency.join(','): contentSource.sync_frequency;
  contentSource.sync_frequency_index = Array.isArray(contentSource.sync_frequency_index) ? contentSource.sync_frequency_index.join(',') : contentSource.sync_frequency_index;

  let addContentSourceData = {
    sync_frequency: contentSource.sync_frequency,
    sync_frequency_name: contentSource.sync_frequency_name,
    pid: contentSource.pid || 0,
    crawl_status: contentSource.crawl_status || 0,
    current_crawl_start_time: contentSource.current_crawl_start_time,
    sync_frequency_index: contentSource.sync_frequency_index,
    sync_frequency_name_index: contentSource.sync_frequency_name_index
  }

  delete contentSource.pid;
  delete contentSource.crawl_status;
  delete contentSource.current_crawl_start_time;
  if (!updateSyncFreq){
    delete contentSource["sync_frequency"]
    delete contentSource["sync_frequency_name"]
  }
//  if (contentSource["sync_frequency_index"] && contentSource['sync_frequency_name_index']) {
    delete contentSource["sync_frequency_index"];
    delete contentSource['sync_frequency_name_index'];
//  }
  delete contentSource["isOauthUpdated"];

  contentSource.created_by = JSON.stringify({email:req.headers.session.email, name:req.headers.session.name});
  
  for (var key in contentSource) {
    if(key =="objectIds"){
      continue;
    }
    if (contentSource.hasOwnProperty(key) && key != "img" && key != "count") {

      if (key == "label" && !contentSource.hasOwnProperty("name")) {
        colums.push("name")
        parameters.push(contentSource["label"].replace(/ /g, '_').toLowerCase())
      }

      if (key == "label" && !contentSource.hasOwnProperty("elasticIndexName")) {
        colums.push("elasticIndexName")
        parameters.push('');
      }
      if (!["last_sync_date", "elasticIndexName", "crawl_status", "pid", "adminLogFile", "logFile", "index_name"].includes(key)) {
        colums.push(key)
        parameters.push(contentSource[key])
      }
    }
  }
  
  let sqlCS = "INSERT INTO `content_sources`(" + colums +
    ") VALUES (" + colums.map(x => {
      return '?'
    }).join(',') + ") " +
    "ON DUPLICATE KEY UPDATE " 
    /**removing email part from duplicate */
    colums.splice(colums.indexOf('email'),1)
    colums.splice(colums.indexOf('created_by'),1);
   let duplicatePart = colums.map(x => {
        return x + "=values(" + x + ")"
    });
    sqlCS+=duplicatePart.join(',');
//  errorlogger.debug("sqlCS", sqlCS);
console.log('sqlCS--->',sqlCS);
console.log('parameters--->',parameters);
  const q = connection[req.headers['tenant-id']].execute.query(sqlCS, parameters, function (errAsync, rows) {
    // console.log(q.sql);
    if (errAsync) {
      errorlogger.error("Error inside AddedContentSources",errAsync);
      callback(errAsync, [])
    }
    else {
      if (!contentSource.id) {
        contentSource.id = rows.insertId;
        contentSource.crawl_status = 0;
        contentSource.sync_frequency_name = "Never";
          let ename = contentSource["label"].replace(/[^a-zA-Z0-9]+/g, '_').toLowerCase();
        try {
          const randomDigit = Math.floor(Math.random() * 10);
          const stringsToModify = ['nested', 'flat', 'navigation', 'body__s', 'attachment'];
          stringsToModify.forEach((n) => {
            const midpoint = Math.ceil(n.length / 2);
            const first = n.slice(0,midpoint);
            const second = n.slice(midpoint);
            const regex = new RegExp(n, 'g'); 
            ename = ename.replace(regex, `${first}${randomDigit}${second}`);
          });
          ename = req.headers.session.tpk + '_' + contentSource.id + '_' + ename;
              if (ename.length > 70) {
                ename = ename.substring(0,70);
              }
       } catch (e) { }
       contentSource.elasticIndexName = ename
       contentSourceAddedFirstTime = true;
            let q = "UPDATE content_sources SET elasticIndexName=? where id=?";
            const sql = connection[req.headers['tenant-id']].execute.query(q, [contentSource.elasticIndexName, contentSource.id], function (errAsync, rows){
              if(errAsync){
                errorlogger.error("Error while updating in indexName inside AddedContentSources",errAsync);
              }else{
                errorlogger.info("upadted IndexName inside AddedContentSources", rows);
              }
            })
    }
      contentSource = { ...contentSource, ...addContentSourceData, contentSourceAddedFirstTime };
      callback(null, contentSource)
    }
  })
}

const resetCsFrequencyToNever = function (tenantId) {
  try {
    let q = `UPDATE content_sources SET sync_frequency = NULL, sync_frequency_name = ?, sync_frequency_value_index = NULL, sync_frequency_type_index = ?`;
    const sql = connection[tenantId].execute.query(q, ['Never', 'Never'], function (errAsync, rows) {
      if (errAsync) {
        errorlogger.info("errAsync", errAsync);
        return false;
      }
      else {
        errorlogger.info("updated frequency for ContentSources ", rows);   
        return true;
      }
    })
  } catch (e) {
    errorlogger.error("Error while updating in frequency for ContentSources ", e);
  }
}
const uploadClickBoostingScoresAfterCrawling = function (elasticIndex, callback) {


  const sql = "select clickScorePath from content_sources WHERE elasticIndexName =?";
  var q = connection.query(sql, [elasticIndex], function (err, result) {
    if (err || result.length == 0 || result[0].clickScorePath == "" || result[0].clickScorePath == null) {
      callback(err, null);
    } else {
      let clickPath = (result[0].clickScorePath);
      fs.readFile(clickPath, 'utf8', (error, txt) => {
        if (error) {
          errorlogger.error('error',error);
          callback(error, null);
        } else {
          clientCS.bulk({
            body: txt
          }, function (err, res) {
            if (err) {
              callback(err, null);
            } else {
              const sql = "UPDATE content_sources SET clickScorePath =? WHERE content_sources.elasticIndexName =?";
              var q = connection.query(sql, ["", elasticIndex], function (err, result) {
                if (err || result.length == 0) {
                  callback(null, null);
                } else {
                  callback(null, res);
                }
              });
            }

          });

        }
      });
    }
  });



};

const insertUpdateAuthorization = function (authorization,req, callback) {
  const colums = []
  const parameters = [];
  if (Object.keys(authorization).includes('authVerification')) {
    authorization.is_authenticated = authorization.authVerification || authorization.oauthAuthVerification;
  } else {
    delete authorization.is_authenticated;
  }
  delete authorization.authVerification;
  delete authorization.changeScopePermissions;
  for (var key in authorization) {
    if (authorization.hasOwnProperty(key)) {
      colums.push(key)
      if (authorization[key] && (key == "client_secret" || key == "password" || key == "htaccessPassword" || key == "accessToken" || key == "refreshToken" || key == "sessionId")) {
        parameters.push(aes256.encrypt(appVariables.analytics.encryptionKey, authorization[key]))
      }
      else {
        parameters.push(authorization[key]);
      }
    }
  }
  const sqlCS = "INSERT INTO `content_source_authorization`(" + colums +
    ") VALUES (" + colums.map(x => {
      return '?'
    }).join(',') + ") " +
    "ON DUPLICATE KEY UPDATE " + colums.map(x => {
      return x + "=values(" + x + ")"
    }).join(',');
  const q = connection[req.headers['tenant-id']].execute.query(sqlCS, parameters, function (errAsync, rows) {
    errorlogger.debug("query", q.sql)
    if (errAsync) {
      callback(errAsync, [])
    }
    else {
      if (Object.keys(authorization).includes('is_authenticated')) {
        let q = "UPDATE content_sources SET is_paused=? where id=?";
        connection[req.headers['tenant-id']].execute.query(q, [!authorization.is_authenticated, authorization.content_source_id], function (errAsyncCs, csRows){
          if(errAsyncCs){
            errorlogger.error("Error while updating in isPaused inside AddedContentSources",errAsyncCs);
            callback(errAsyncCs, [])
          }else{
            errorlogger.info("upadted isPaused inside AddedContentSources", csRows);
            callback(null, rows)
          }
        })
      } else {
        callback(null, rows)
      }
    }
  })
}

const insertSpacesBoards = function (content_source_id, spacesOrBoards,req, callback) {

  var colums = []
  var data = []
  for (var fi = 0; fi < spacesOrBoards.length; fi++) {
    colums = []//initialize columns everytime

    spacesOrBoards[fi].content_source_id = content_source_id

    const parameters = []
    for (var key in spacesOrBoards[fi]) {

      if (spacesOrBoards[fi].hasOwnProperty(key)) {
        colums.push(key)
        parameters.push(spacesOrBoards[fi][key])
      }

    }
    data.push(parameters)

  }



  const sqlCS = "INSERT INTO `content_source_spaces`(" + colums +
    ") VALUES " + data.map(x => { return "(" + x.map(y => { return "?" }) + ")" }).join(",") +
    " ON DUPLICATE KEY UPDATE " + colums.map(x => {
      return x + "=values(" + x + ")"
    }).join(',');


  if (colums.length)
    var q = connection[req.headers['tenant-id']].execute.query(sqlCS, data.reduce((sum, x) => { return sum.concat(x); }, []), function (errAsync, rows) {
      if (errAsync) {
        errorlogger.debug("Error inside AddedContentSourceSpacess",errAsync);
        callback(errAsync, [])
      }
      else {
        getContentSourceSpaceBoardsById(content_source_id,req,function (err, resultSpaces) {
          if (!err)
            callback(null, resultSpaces)
          else
            callback(err, [])
        })
      }
    })
  else {
    callback(null, [])
  }
}


const getContentSourceDataById = function (contentSourceId, req, callback) {
  const sql = 'SELECT *,csa.id authId, cs.id contentsourceId FROM `content_sources` cs left join content_source_authorization csa on cs.id=csa.content_source_id WHERE cs.id=?'
  var q = connection[req.headers['tenant-id']].execute.query(sql, [contentSourceId], function (err, result) {
    if (err || result.length == 0) {
      if(err){
        errorlogger.error("error while fetching csid ", err);
      }
      callback(err, [])
    }
    else {
      result = result[0]
      var dataToSend = { contentSource: {}, authorization: {} }
      for (var key in result) {
        if (result.hasOwnProperty(key)) {
          if (modelContentSource.indexOf(key) != -1) {
            dataToSend.contentSource[key] = result[key]
          } else {
            if (modelContentSourceAuthorization.indexOf(key) != -1) {
              dataToSend.authorization[key] = result[key]
              if (key == "client_secret" || key == "password" || key == "htaccessPassword" || key == "accessToken" || key == "refreshToken" || key == "sessionId") {
                //    dataToSend.authorization[key] =dataToSend.authorization[key]?aes256.decrypt(appVariables.analytics.encryptionKey, dataToSend.authorization[key]):"";
                dataToSend.authorization[key] = dataToSend.authorization[key] ? aes256.decrypt(appVariables.analytics.encryptionKey, dataToSend.authorization[key]) : ""
              }

            }
          }
        }
        dataToSend.contentSource['id']                 = result['contentsourceId']
        dataToSend.contentSource['url']                = result['url']
        dataToSend.contentSource['editing']            = result['editing']
        dataToSend.authorization['id']                 = result['authId']
        dataToSend.authorization['connection_status']  = dataToSend.authorization['connection_status'] ? true : false;
        dataToSend['loginlink']                        = result["loginlink"];
        dataToSend['passwordselector']                 = result["passwordselector"];
        dataToSend['usernameselector']                 = result["usernameselector"];
        dataToSend['buttonselector']                   = result["buttonselector"];
        dataToSend['authorization_type']               = result["authorization_type"];
        dataToSend.authorization['permission']         = result["permission"];
        dataToSend['multi_form_auth']                  = result["multi_form_auth"];
      }

      dataToSend.contentSource['sync_frequency'] = `${dataToSend.contentSource['sync_frequency']}`.includes(',') ? dataToSend.contentSource['sync_frequency'].split(',').map(val => Number(val.trim())) : dataToSend.contentSource['sync_frequency'];

      callback(null, dataToSend)

    }
  })
}

const getContentSourceDataByToken = function (accessToken,req, callback) {
  const sql = 'SELECT *,csa.id authId, cs.id contentsourceId FROM `content_sources` cs left join content_source_authorization csa on cs.id=csa.content_source_id WHERE csa.accessToken=?'
  connection[req.headers['tenant-id']].execute.query(sql, [accessToken], function (err, result) {
    if (err) {
      callback([])
    }
    else {
      result = result[0]
      var dataToSend = { contentSource: {}, authorization: {} }
      for (var key in result) {
        if (result.hasOwnProperty(key)) {
          if (modelContentSource.indexOf(key) != -1) {
            dataToSend.contentSource[key] = result[key]
          } else {
            if (modelContentSourceAuthorization.indexOf(key) != -1) {
              dataToSend.authorization[key] = result[key]
            }

          }
        }
        dataToSend.contentSource['id'] = result['contentsourceId']
        dataToSend.authorization['id'] = result['authId']
      }
      callback(dataToSend)

    }
  })
}

const getContentSourceSpaceBoardsById = function (contentSourceId,req, callback) {
  const sql = 'SELECT * FROM `content_source_spaces` WHERE content_source_id=?'
  connection[req.headers['tenant-id']].execute.query(sql, [contentSourceId], function (err, result) {
    if (err) {
      callback(err, [])
    }
    else {
      callback(null, result)

    }
  })
}

const getContentSourceObjectsAndFieldsById = function (contentSourceId,req, callback) {
  const sql = 'SELECT * FROM `content_source_objects` where content_source_id=?'
  connection[req.headers['tenant-id']].execute.query(sql, [contentSourceId], function (err, resultObject) {
    if (err) {
      errorlogger.error("error while fetching getContentSourceObjectsAndFieldsById ", err);
      callback(err, [])
    }
    else {
      var taskObjects = []

      for (var i = 0; i < resultObject.length; i++) {
        taskObjects.push((function (i) {
          return function (cb) {
            getObjectFieldsById(resultObject[i].id,req, function (err, resultFields) {

              if (!err) {
                resultObject[i].fields = resultFields
                cb(null, resultObject)
              }
              else {
                resultObject[i].fields = []
                cb(err, [])
              }
            })
          }
        })(i))
      }

      async.parallel(taskObjects, function (err, result) {
        if (err)
          callback(err, [])
        callback(null, resultObject)

      })

    }
  })
}

const insertObject = function (object,req, callback) {
  let colums = []
  let parameters = []
  for (var key in object) {
    if (object.hasOwnProperty(key) && key != "fields" && key != "valid" && key != "isAvailable" && key != "disabled" && key != "boosting_factor" && key != "objectLogFile" && key != "objectAdminLogFile" && key != "object_pid" && key != "current_crawl_end_time" && key != 'emptyPath' && key != 'samePathExists' && key != 'editPath') {
      colums.push(key)
      if (key == "name") {
        object[key] = object[key].toLowerCase()
      }
      parameters.push(object[key])
    }
  }
  let sqlCS = "INSERT INTO `content_source_objects` (" + colums +
    ") VALUES (" + colums.map(x => {
      return '?'
    }).join(',') + ") " +
    "ON DUPLICATE KEY UPDATE " + colums.map(x => {
      return x + "=values(" + x + ")"
    }).join(',');
  let q = connection[req.headers['tenant-id']].execute.query(sqlCS, parameters, function (errAsync, rows) {
    // errorlogger.debug('query',q.sql);
    if (errAsync) {
      errorlogger.error("Error inside AddedContentSourceObjects",errAsync);
      callback(errAsync, [])
    }
    else {
      if (!object.id)
        object.id = rows.insertId;
      callback(null, object)
    }
  })
}

const insertFields = function (allData, objectWithfieldArr,req, callback) {

  var fieldArr = objectWithfieldArr.fields || [];

  var colums = []
  var data = []
  for (var fi = 0; fi < fieldArr.length; fi++) {
    colums = []//initialize columns everytime

    fieldArr[fi].content_source_object_id = objectWithfieldArr.id

    let parameters = []
    //for (var key in fieldArr[fi]) {
    for (var f = 0; f < modelFields.length; f++) {

      colums.push(modelFields[f]);
      let param = getObjectFieldNames(allData.insert_content_source_id.content_source_type_id, fieldArr[fi], modelFields[f]);
      if (param) {
        //   if (modelFields[f] == "name" && allData.insert_content_source_id.content_source_type_id == 3 && !fieldArr[fi].isMerged && !fieldArr[fi][modelFields[f]].includes("attachment_") && !fieldArr[fi][modelFields[f]].includes("_nested") && !fieldArr[fi][modelFields[f]].includes("_flat") && !fieldArr[fi][modelFields[f]].toLowerCase().includes("accountid") && !fieldArr[fi][modelFields[f]].toLowerCase().includes("contactid")) {
        if (allData.insert_content_source_id.content_source_type_id == 3 && modelFields[f] == "name" && fieldArr[fi][modelFields[f]] && fieldArr[fi][modelFields[f]].includes('.')) {
          fieldArr[fi][modelFields[f]] = fieldArr[fi][modelFields[f]].replace(/\./g,'_');
        }
        parameters.push(allData.insert_content_source_id.elasticIndexName + "___" + objectWithfieldArr.name + "___" + fieldArr[fi][modelFields[f]])
      }
      else
        if (modelFields[f] == "isFilterable" || modelFields[f] == "isSortable" || modelFields[f] == "isSearchable" || modelFields[f] == "isActive" || modelFields[f] == "isTag") {
          parameters.push(fieldArr[fi][modelFields[f]] ? fieldArr[fi][modelFields[f]] : false)
        }
        else if (modelFields[f] == "id" || modelFields[f] == "content_source_object_id") {
          parameters.push(fieldArr[fi][modelFields[f]] ? fieldArr[fi][modelFields[f]] : null)
        }
        else if (modelFields[f] == "selector" || modelFields[f] == "single_multiple") {
          parameters.push(fieldArr[fi][modelFields[f]] ? fieldArr[fi][modelFields[f]] : "single")
        }
        else if(modelFields[f] == 'merge_field_id') {
          parameters.push(fieldArr[fi][modelFields[f]] === null ? 0: fieldArr[fi][modelFields[f]]);
        }
        else
          parameters.push(fieldArr[fi][modelFields[f]])
    }
    let fieldNameIndex = modelFields.findIndex(item => item === 'name');
    let mergeFieldIdIndex = modelFields.findIndex(item => item === 'merge_field_id');
    let annotationIndex = modelFields.findIndex(item => item === 'annotated');

    if(annotationIndex > -1 && parameters[annotationIndex]) {
      parameters[mergeFieldIdIndex] = 0;
    }

    if(fieldNameIndex > -1 && mergeFieldIdIndex > -1 && parameters[mergeFieldIdIndex] !== 0) {
      parameters[fieldNameIndex]  = fieldArr[fi].name;
    }
    data.push(parameters)
  }

  const sqlCS = "INSERT INTO `content_source_object_fields`(" + colums +
    ") VALUES " + data.map(x => { return "(" + x.map(y => { return "?" }) + ")" }).join(",") +
    " ON DUPLICATE KEY UPDATE " + colums.map(x => {
      return x + "=values(" + x + ")"
    }).join(',');


  // errorlogger.debug("query", sqlCS)
  if (colums.length)
    var q = connection[req.headers['tenant-id']].execute.query(sqlCS, data.reduce((sum, x) => { return sum.concat(x); }, []), function (errAsync, rows) {
      // errorlogger.debug("query", q.sql)
      if (errAsync) {
        errorlogger.error("Error inside AddedContentSourceObjects",errAsync);
        callback(errAsync, [])
      }
      else {
        getObjectFieldsById(objectWithfieldArr.id,req, function (err, resultFields) {
          if (!err) {
            resultFields.forEach(y => {
              if (!y.annotated && y.name.split("___").length > 1) {
                y.name = y.name.split("___")[2]
              }
            })
            callback(null, resultFields)
          }
          else
            callback(err, [])
        })
      }
    })
  else {
    callback(null, [])
  }


}


const getObjectFieldsById = function (objectId, req, callback) {
  let sql = "SELECT * FROM `content_source_object_fields` where content_source_object_id=?";
  if (req.query && !req.query.mergedFields) {
    sql = sql.concat(" AND (merge_field_id IS NULL OR merge_field_id = 0)");
  }
  connection[req.headers['tenant-id']].execute.query(sql, [objectId], function (err, resultFields) {
    if (err) { callback(err, []) }
    else {
      callback(null, resultFields)
    }
  })
}

const checkArray = function (my_arr) {
  for (var i = 0; i < my_arr.length; i++) {
    if (my_arr[i] === "" || (!my_arr[i]))
      return false;
  }
  return true;
}

const getUserfromAccessToken = function (accTokn, callback) {
  var sql = "select u.id id,r.id roleId,u.user_email email,u.name name,r.role role from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where access_token= ? Limit 1";
  var q = connection.query(sql, [accTokn], function (err, rows) {
    errorlogger.error('query',q.sql);
    if (!err) {
      if (rows.length > 0) {
        return callback(rows[0])
      }
      else {
        return callback(0)
      }
    }
  })
}
const  save_boosting_details2 = async (elasticIndex, cb)=>{
  const clickScorePath = path.resolve(__dirname + '/../tmp/' + elasticIndex + '_' + (new Date()).toISOString() + '.txt.ignore');
  const sql = "UPDATE content_sources SET clickScorePath =? WHERE content_sources.elasticIndexName =?";
  await new Promise((resolve, reject) => {
    connection.query(sql, [clickScorePath, elasticIndex],(error, result)=>{
      if (error) reject(error);
      else resolve(result);
    });
  });
  const responseQueue = [];
  const response = await clientCS.search({
    index: elasticIndex,
    from: 0,
    size: 25,
    scroll: '2h',
    body: {
      "query":{"bool":{"should":[
        {"exists":{"field":"click"}},
        {"exists":{"field":"cluster"}}
      ]}},
      "_source":["click","cluster"]}
  });
  responseQueue.push(response);
  const outFile = fs.createWriteStream(clickScorePath);
  while (responseQueue.length) {
    const data = responseQueue.shift();
    for (let i = 0; i < data.hits.hits.length; i++) {
      let updateBoostingScript = {
        "update": {
          "_index": data.hits.hits[i]._index,
          "_type": data.hits.hits[i]._type,
          "_id": data.hits.hits[i]._id
        }
      };
      let boostingScript = {
        "script": `"ctx._source.click = ${(data.hits.hits[i]._source.click || 0)};ctx._source.cluster = ${(data.hits.hits[i]._source.cluster || 0)}`
      };
      outFile.write((JSON.stringify(updateBoostingScript) + "\n"));
      outFile.write((JSON.stringify(boostingScript) + "\n"));
    }
    if (data.hits.hits.length) {
      responseQueue.push(
        await clientCS.scroll({
          scrollId: data._scroll_id,
          scroll: '2h'
        })
      );
    }
  }
  return null;
}
const save_boosting_details = function (elasticIndex, callback) {
  connection.query("SELECT * FROM `boosting_custom`", (error, results) => {
    if (error) {
      errorlogger.error('error',error);
      callback(error,null);
    }
    else {
      if (results[6].value != "0" && results[6].value != 0) {
        try {
          clickScorePath = path.resolve(__dirname + '/../tmp/' + elasticIndex + '_' + (new Date()).toISOString() + '.txt.ignore');
          // fs.createWriteStream(clickScorePath);
          const sql = "UPDATE content_sources SET clickScorePath =? WHERE content_sources.elasticIndexName =?"
          var q = connection.query(sql, [clickScorePath, elasticIndex], function (err, result) {
            if (err || result.length == 0) {
              callback(err, [])
            }
            else {
              clientCS.search(
                {
                  index: elasticIndex,
                  from: 0,
                  size: 25,
                  scroll: '2h',
                  body: {
                    "query": {
                      "exists": { "field": "click" }
                    },
                    "_source": ["click"]
                  }
                }, function (err, data) {
                  if (err) {
                    // callback(err, null);
                    fs.writeFile(clickScorePath, "", 'utf8', (err, result) => {
                      callback(null, "FirstTime");
                    });
                  } else {
                    getContentSourceDataFromElastic(data, clickScorePath, elasticIndex, 1, function (err, res) {
                      if (err) {
                        errorlogger.error("error while fetching click boosting score", err);
                        callback(null, null);
                      } else {
                        callback(null, res);
                      }
                    });
                  }
                });
            }
          });


        } catch (e) {
          errorlogger.error("error", e);
        }
      } else {
        callback(null, "Click tuning disabled");
      }
    }
  });
};


const getContentSourceDataFromElastic = function (data, path, elasticIndex, counter, callback) {
  if (data.hits.hits.length > 0) {
    if (data.hits.total) {
      try {
        var str = '';
        for (let i = 0; i < data.hits.hits.length; i++) {
          let updateBoostingScript = {
            "update": {
              "_index": data.hits.hits[i]._index,
              "_type": data.hits.hits[i]._type,
              "_id": data.hits.hits[i]._id
            }
          };
          let boostingScript = {
            "script": "ctx._source.click = " + (data.hits.hits[i]._source.click || 0)
          };
          str = str.concat(JSON.stringify(updateBoostingScript) + "\n");
          str = str.concat(JSON.stringify(boostingScript) + "\n");
        }
        if (counter == 1) {
          counter++;
          fs.writeFile(path, str, 'utf8', (err, result) => {
            if (err) {
              callback(err, null);
            } else {
              clientCS.scroll({
                scrollId: data._scroll_id,
                scroll: '2h'
              }, function (err, dataFromScroll) {
                if (err) {
                  callback(err, null);
                } else {
                  getContentSourceDataFromElastic(dataFromScroll, path, elasticIndex, counter, function (err, data) {
                    if (err) {
                      callback(err, null);
                    } else {
                      callback(null, data);
                    }
                  });
                }
              });
            }
          });
        } else {
          counter++;
          fs.appendFile(path, str, 'utf8', (err, result) => {
            if (err) {
              callback(err, null);
            } else {
              clientCS.scroll({
                scrollId: data._scroll_id,
                scroll: '2h'
              }, function (err, dataFromScroll) {
                if (err) {
                  callback(err, null);
                } else {
                  getContentSourceDataFromElastic(dataFromScroll, path, elasticIndex, counter, function (err, data) {
                    if (err) {
                      callback(err, null);
                    } else {
                      callback(null, data);
                    }
                  });
                }
              });
            }
          });
        }

      } catch (e) {
        errorlogger.error('error while trying to write file.',e);
      }
    }
  }
  else {
    callback(null, 'success');
  }
}
const getPlacesById = function (objectId,req, callback) {
  const sql = 'SELECT * FROM `content_source_spaces` where content_source_id=?'
  connection[req.headers['tenant-id']].execute.query(sql, [objectId], function (err, resultFields) {
    if (err) { callback(err, []) }
    else
      callback(null, resultFields)
  })
}
async function getGptRulesFromCache(req, searchClientUid){
  const key = `${req.headers['tenant-id']}_${searchClientUid}_gpt_rules`
  return redisCachePromise(key, getGPTRules, req, searchClientUid);
}
function redisCachePromise(key, getGPTRules, req, searchClientUid) {
   return new Promise((resolve, reject) => {
      redisCache(key, getGPTRules, req, searchClientUid, (err, data) => {
          if (err) {
              reject(err);
          } else {
              resolve(data);
          }
      });
  });
}
function deleteGptRulesFromCache(req, searchClientUid){
  const key = `${req.headers['tenant-id']}_${searchClientUid}_gpt_rules`;
  errorlogger.debug("deleteGptRulesFromCache", key);
  redisclient.del(key, (err, response) => {
    if (err) {
        errorlogger.error('Error deleting gpt rules from cache:', err);
    } else {
      errorlogger.info('cache cleared for gpt rules:', response); // response is 1 if key was deleted, 0 if key didn't exist
    }
});

}
const getGPTRules = function (req,uid, callback) {
  errorlogger.debug(">>>>>> getGPTRules", uid);
  const sql = 'SELECT gptRules FROM `search_clients` where uid=?'
  connection[req.headers['tenant-id']].execute.query(sql,uid,function (err, resultFields) {
    if (err) { callback(err, []) }
    else
      callback(null, resultFields[0])
  })
}


const getActivePlacesBycontentSourceId = function (objectId, callback) {
  const sql = 'SELECT * FROM `content_source_spaces` where content_source_id=? AND isSelected=1'
  connection.query(sql, [objectId], function (err, resultFields) {
    if (err) { callback(err, []) }
    else
      callback(null, resultFields)
  })
}

const saveMapping = function (indexName, MappingToSave, callback) {
  if (config.get("onPremises")) {
    var body = {
      indexName: indexName,
      MappingToSave: MappingToSave
    }
    httpRequest('PUT', config.get('onPremisesUrl') + '/saveMapping', '', body, '', function (err, result) {
      if (!err)
        callback(null, result.body)
      else
        callback(err)
    })
  }
  else {

    var options = {
      method: 'PUT',
      url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + indexName + '/',
      body: JSON.stringify(MappingToSave)
    };

    async.auto({
      deleteMapping: function (cb) {
        if (!indexName.includes('_temp'))
          deleteMapping(indexName, function (err) {
            cb(null)
          });
        else
          cb(null)
      },
      createMapping: ['deleteMapping', function (dataFromAbove, cb) {
        request(options, function (error, response, body) {
          if (error) {
            errorlogger.error('error',error)

          }
          errorlogger.info("body",body);
          body = JSON.parse(body);
          if (body.acknowledged == true) {
            cb(null);
          }
          else {
            cb(body.error);
          }

        });
      }]
    }, function (err, result) {
      if (err)
        callback(err)
      else
        callback(null)
    })
  }
}

const deleteMapping = function (indexName, callback) {

  if (config.get("onPremises")) {
    var body = {
      indexName: indexName,
      MappingToSave: {}
    }
    httpRequest('POST', config.get('onPremisesUrl') + '/saveMapping', '', body, '', function (err, result) {
      if (!err)
        callback(null, result.body)
      else
        callback(err)
    })
  }
  else {
    var optionsDel = {
      method: 'DELETE',
      url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + indexName + '/',
    };

    request(optionsDel, (error, responseDel, bodyDel) => {
      if (error) {
        errorlogger.error('error',error)
        return;
      }
      ;

      errorlogger.info("body",bodyDel);
      bodyDel = JSON.parse(bodyDel);
      callback(null);
    });
  }
}

const reIndex = function (indexName, tempIndex, callback) {

  var options = {
    method: 'POST',
    url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/_reindex",
    headers:
    {
      'content-type': 'application/json'
    },
    body: { source: { index: tempIndex }, dest: { index: indexName } },
    json: true
  };

  request(options, function (error, response, body) {

    errorlogger.error('error',error)
    if (error) {
      callback(error, null)
    }
    else {
      // timeout for waiting for reindexing to complete before deleting temp index
      setTimeout(function () {
        deleteMapping(tempIndex, function (err) {
          callback(null, body)
        })
      }, 10000)
    }
  });

}

const BulkUpload = function (data, callback) {

  //console.log("Data sent", data)

  if(typeof data == 'undefined' || data == ''){
    return callback(null, "")
  }
  else
    if (config.get("onPremises")) {
      var body = {
        data: data
      }
      httpRequest('POST', config.get('onPremisesUrl') + '/bulkUpload', '', body, '', function (err, result) {
        if (!err)
          callback(null, result.body)
        else
          callback(err)
      })
    }
    else {
      var options = {
        "method": "POST",
        "url": "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/_bulk",
        "headers": {
          "content-type": "application/binary"
        },
        "body": data
      };
      request(options, function (err, response, body) {
        body = JSON.parse(body);
        for (var i = 0; i < body.items.length; i++) {
          if ((body.items[i].index && body.items[i].index.status != 200 && body.items[i].index.status != 201)
            || (body.items[i].create && body.items[i].create.status != 200 && body.items[i].create.status != 201)) {
            errorlogger.debug(JSON.stringify(body.items[i]));
          }
        }
        if (!err)
          callback(null, body);
        else
          callback(err)

      })
    }
}

const BulkUploadElasticAnalytics = function (data, callback) {

  //console.log("Data sent", data)

  if (data == "") {
    callback(null, "")
  }
  else{
    var options = {
      "method": "POST",
      "url": "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/_bulk",
      "headers": {
        "content-type": "application/binary"
      },
      "body": data
    };
    request(options, function (err, response, body) {
      body = JSON.parse(body);
      for (var i = 0; i < body.items.length; i++) {
        if ((body.items[i].index && body.items[i].index.status != 200 && body.items[i].index.status != 201)
          || (body.items[i].create && body.items[i].create.status != 200 && body.items[i].create.status != 201)) {
          errorlogger.debug(JSON.stringify(body.items[i]));
        }
      }
      if (!err)
        callback(null, body);
      else
        callback(err)

    })
  }
}


const getPlacesBySortParam = function (content_source_id, sort,req, callback) {
  let data = []
  if (!sort || sort == 'numeric') {
    sql = "SELECT * FROM `content_source_spaces` WHERE `spaceName` regexp '^[^a-zA-Z ]+.*' and content_source_id=?"
  } else if (sort == "not_applicable") {
    sql = "SELECT * FROM `content_source_spaces` WHERE  content_source_id=?";
  } else {
    sql = "SELECT * FROM `content_source_spaces` WHERE UPPER(`spaceName`) regexp '^[" + sort + "]+'  and content_source_id=?";
  }
  data = [content_source_id]
  var q = connection[req.headers['tenant-id']].execute.query(sql, data, function (errGet, dataGet) {
    errorlogger.debug("query", q.sql);
    errorlogger.error('error',errGet);
    errorlogger.debug('data',dataGet);
    if (errGet) {
      return callback(errGet);
    }
    return callback(null, dataGet);
  });
}

const checkJSON = function (jsonSTR) {
  if (jsonSTR.length == 0) {
    return 0;
  }
  if (/^[\],:{}\s]*$/.test(jsonSTR.replace(/\\["\\\/bfnrtu]/g, '@').
    replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g, ']').
    replace(/(?:^|:|,)(?:\s*\[)+/g, ''))) {

    //the json is ok
    return 1;

  } else {

    //the json is not ok
    return 0;
  }
}

const SiteMapGetUrl = function (url, userName, Password, callback) {

  if (userName && Password)
    var auth = 'Basic ' + new Buffer(userName + ':' + Password).toString('base64');
  var options = {
    method: 'GET',
    url: url
    // headers:{authorization: auth }
  };
  if (auth)
    options.headers.authorization = auth
  request(options, (error, response, body) => {
    if (error) callback(new Error(error));
    var parser = new xml2js.Parser();
    parser.parseString(body, (err, xmldata) => {
      errorlogger.info('data',xmldata)
      var urls = []
      for (var i = 0; i < xmldata.urlset.url.length; i++) {
        var temp = xmldata.urlset.url[i].loc[0];
        urls.push(temp)
      }
      callback(null, urls);
    })
  });
}

const getMappingObject = function (contentSourceid, mappingWithSetting, objectandFields,cb) {
  // errorlogger.info("inside mapping creation---");
  getLanguageForContentSource(contentSourceid,(err, languages) => { //languages array
      if (err) {
          console.log("Error in fetching languages", err);
          cb(err,null);
      } else {
          let finalDataObject = mappingWithSetting
          let fields = []
          let object = objectandFields
          finalDataObject["mappings"] = {}
          for (var obj = 0; obj < object.length; obj++) {
              finalDataObject["mappings"][object[obj].name] = {}
              finalDataObject["mappings"][object[obj].name]["properties"] = {};

              fields = object[obj].fields;
              for (var i = 0; i < fields.length; i++) {
                if(fields[i].name.includes('.'))
                  fields[i].name = fields[i].name.replace(/\./g,'_');
                  finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name] = {}
                  //finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name + "_copy"] = {}
                  //finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name + "_copy"]["type"] = "text"
                  //finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name + "_copy"]["analyzer"] = "custom_lowercase_stemmed"

                  if (fields[i].name.slice(-7) == "_nested" || fields[i].name.slice(-11) == "_navigation") {
                      delete finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name]
                      fields[i].name = fields[i].name.split("___").length > 1 ? fields[i].name = fields[i].name.split("___")[2] : fields[i].name
                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name] = {}
                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name] = createCategoryNestedObject(fields[i].name, 0)
                  }
                  else if (fields[i].name.includes("attachment_") || fields[i].name.includes("__Body__s")) {
                      delete finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name]
                      fields[i].name = fields[i].name.split("___").length > 1 ? fields[i].name = fields[i].name.split("___")[2] : fields[i].name
                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name] = {}


                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name]["type"] = "nested"
                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name]["properties"] = {
                          body: {
                              "analyzer": "custom_lowercase_stemmed",
                              "search_analyzer": "custom_lowercase_synonym",
                              type: "text"
                          },
                          name: {
                              "analyzer": "custom_lowercase_stemmed",
                              "search_analyzer": "custom_lowercase_synonym",
                              type: "text"
                          },
                          url: {
                              type: "keyword"
                          }
                      }
                  }
                  else if (fields[i].name.includes("file_attachment_")) {
                    delete finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name]
                    fields[i].name = fields[i].name.split("___").length > 1 ? fields[i].name = fields[i].name.split("___")[2] : fields[i].name
                    finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name] = {}


                    finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name]["type"] = "nested"
                    finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name]["properties"] = {
                      body: {
                        "analyzer": "custom_lowercase_stemmed",
                        "search_analyzer": "custom_lowercase_synonym",
                        type: "text"
                      },
                      name: {
                        "analyzer": "custom_lowercase_stemmed",
                        "search_analyzer": "custom_lowercase_synonym",
                        type: "text"
                      },
                      url: {
                        type: "keyword"
                      }
                    }
                  }
                  else if (fields[i].type == "datetime" || fields[i].type == "date") {
                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name]["type"] = "date"
                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name]["format"] = "dateOptionalTime"
                  }
                  else if (fields[i].type == "double" || fields[i].type == "number") {
                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name]["type"] = "text";
                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name]["fields"] = { "keyword": { "type": "keyword" } };
                  }
                  else {
                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name] = searchableFieldAnalyzer(languages, object[obj].name, fields[i].name)[object[obj].name].properties[fields[i].name];
                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name + "_copy"] = searchableFieldAnalyzer(languages, object[obj].name, fields[i].name + "_copy")[object[obj].name].properties[fields[i].name + "_copy"];
                      finalDataObject["mappings"][object[obj].name]["properties"][fields[i].name + "_special"] = searchableFieldAnalyzer(languages, object[obj].name, fields[i].name + "_special")[object[obj].name].properties[fields[i].name + "_special"];
                  }
              }
              finalDataObject["mappings"][object[obj].name]["properties"]["Categories"] = {}
              finalDataObject["mappings"][object[obj].name]["properties"]["Categories"]["type"] = "text";
              finalDataObject["mappings"][object[obj].name]["properties"]["Categories"]["fields"] = { "keyword": { "type": "keyword" } };

              finalDataObject["mappings"][object[obj].name]["properties"]["post_time"] = {}
              finalDataObject["mappings"][object[obj].name]["properties"]["post_time"]["type"] = "date"
              finalDataObject["mappings"][object[obj].name]["properties"]["post_time"]["format"] = "dateOptionalTime"

              ///----------------------------- brands--------------------------------------------///
              finalDataObject["mappings"][object[obj].name]["properties"]["brands"] = {}
              finalDataObject["mappings"][object[obj].name]["properties"]["brands"]["type"] = "text";
              finalDataObject["mappings"][object[obj].name]["properties"]["brands"]["fields"] = { "keyword": { "type": "keyword" } };

              finalDataObject["mappings"][object[obj].name]["properties"]["language"] = {}
              finalDataObject["mappings"][object[obj].name]["properties"]["language"]["type"] = "text"
              finalDataObject["mappings"][object[obj].name]["properties"]["language"]["fields"] = { "keyword": { "type": "keyword" } };

              //------------------------------------------------------type merge--------------------//

              finalDataObject["mappings"][object[obj].name]["properties"]["objType"] = {}
              finalDataObject["mappings"][object[obj].name]["properties"]["objType"]["type"] = "text"
              finalDataObject["mappings"][object[obj].name]["properties"]["objType"]["fields"] = { "keyword": { "type": "keyword" } };

              //for boosting
              finalDataObject["mappings"][object[obj].name]["properties"]["id"] = {}
              finalDataObject["mappings"][object[obj].name]["properties"]["id"]["type"] = "text"
              finalDataObject["mappings"][object[obj].name]["properties"]["id"]["fields"] = { "en": {
                  "type": "text",
                  "analyzer": "custom_lowercase_stemmed",
                  "search_analyzer": "custom_lowercase_synonym"
                },
                "keyword": {
                  "type": "keyword",
                  "ignore_above": 256
                }};
              finalDataObject["mappings"][object[obj].name]["properties"]["Id"] = {}
              finalDataObject["mappings"][object[obj].name]["properties"]["Id"]["type"] = "text"
              finalDataObject["mappings"][object[obj].name]["properties"]["Id"]["fields"] = { "en": {
                  "type": "text",
                  "analyzer": "custom_lowercase_stemmed",
                  "search_analyzer": "custom_lowercase_synonym"
                },
                "keyword": {
                  "type": "keyword",
                  "ignore_above": 256
                } };

              //For salesforce Case Comments
              if (object[obj].content_source_type_id == 3 &&
                  (object[obj].name == "case"
                      || object[obj].name == "feeditem_1"
                      || object[obj].name == "feeditem"
                      || object[obj].name == "idea"
                  )
              ) {
                  finalDataObject["mappings"][object[obj].name]["properties"]["caseComments"] = { "type": "nested", "properties": { "body": { "type": "keyword" }, "createdBy": { "type": "keyword"/*,"index":"not_analyzed"*/ }, "createdDate": { "type": "date", "format": "strict_date_optional_time||epoch_millis" } } };
              }

              // errorlogger.info("done mapping creation for", object[obj].name)
          }
          // return finalDataObject;
          cb(null,finalDataObject)
      }

  })
}


const getLanguageForContentSource = function(contentSourceid,cb,req){
  let queryLanguages = "SELECT * from content_source_languages WHERE content_source_id = ?";
  if(req && req.headers && req.headers['tenant-id']){
    connection[req.headers['tenant-id']].execute.query(queryLanguages, [contentSourceid], (err, languages) => { //languages array
      if(err){
          cb(err,null);
      }else{
          cb(null,languages)
      }

  })
  }else{
    connection.query(queryLanguages, [contentSourceid], (err, languages) => { //languages array
      if(err){
          cb(err,null);
      }else{
          cb(null,languages)
      }

  })
  }
}

const searchableFieldAnalyzer = function (languageArray,finalDataObject, objectName) {
  languageArray = languageArray.length <= 0 ? [{"code":"en"}] : languageArray;
  var mapping = {}
  mapping[finalDataObject] = { "properties": {} }
  mapping[finalDataObject]["properties"][objectName] = {}
  var mappingToFields = {};
  if(objectName.split("_").indexOf("copy") >=0 ){
    for(var i =0;i<languageArray.length;i++){
      if(languageArray[i].code == "en"){
        mappingToFields[languageArray[i].code] = {
          "type":"text",
          "analyzer": "custom_lowercase_stemmed",    //MAPPING FOR  copy field
        }
      }else if(languageArray[i].code == "zh"){
        mappingToFields[languageArray[i].code] = {
          "type":"text",
          "analyzer":languageArray[i].analyzer
        }
      }else{
        mappingToFields[languageArray[i].code] = {
          "type":"text",
          "analyzer":languageArray[i].analyzer + "_stemmed"
        }
      }
  }
  } else if (objectName.split('_').indexOf('special') >= 0) {
    for (let i = 0; i < languageArray.length; i++) {
      if (languageArray[i].code == 'en') {
        mappingToFields[languageArray[i].code] = {
          type    : 'text',
          analyzer: 'custom_special_analyzer'  //MAPPING FOR  special field
        };
      } else if (languagesWithoutStemmedInAnalyzers.includes(languageArray[i].code)) {
        mappingToFields[languageArray[i].code] = {
          type    : 'text',
          analyzer: languageArray[i].analyzer
        };
      } else {
        mappingToFields[languageArray[i].code] = {
          type    : 'text',
          analyzer: languageArray[i].analyzer + '_stemmed'
        };
      }
    }
  } else{
    for(var i =0;i<languageArray.length;i++){
      if(languageArray[i].code == "en"){
        mappingToFields[languageArray[i].code] = {
          "type":"text",
          "analyzer": "custom_lowercase_stemmed",
          "search_analyzer": "custom_lowercase_synonym",     //MAPPING FOR
        }
      }else if(languageArray[i].code == "zh"){
        mappingToFields[languageArray[i].code] = {
          "type":"text",
          "analyzer":languageArray[i].analyzer
        }
      }else{
        mappingToFields[languageArray[i].code] = {
          "type":"text",
          "analyzer":languageArray[i].analyzer + "_stemmed",
          "search_analyzer": languageArray[i].analyzer

        }
      }
  }
  }

  mappingToFields["keyword"] = {
    "type":"keyword",
    "ignore_above":256
  }


  //object of different languages added to fields
  if(objectName.split("_").indexOf("copy") >=0 || objectName.split("_").indexOf("special") >= 0){
    mapping[finalDataObject]["properties"][objectName] = {
      "type": "text",
      "fields": mappingToFields
    }
  }else{
    mapping[finalDataObject]["properties"][objectName] = {
      "type": "text",
      "fields": mappingToFields,
      copy_to: [
        objectName+"_copy",
        objectName+"_special",
      ]
    }
  }
  return mapping;
}

const createCategoryNestedObject = function (fieldName, level) {

  if (level < 5) {
    level = level + 1
    var arr = {
      type: 'nested',
      properties: {
        name: {
          type: "keyword",
          // index: "not_analyzed",
          doc_values: true
        },
        [fieldName + '_' + level]: createCategoryNestedObject(fieldName, level)
      }
    }

    return arr;
  }
}

const parsehtmltoText = function (rawHtml, callback) {
  let clean = sanitizeHtml(rawHtml, {
    allowedTags: appVariables.allowedTags,
    allowedAttributes: []
  });
  callback(null, clean);
}

const getMapping = function (indexName, callback) {

  var options = {
    method: 'GET',
    url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + indexName + '/_mapping',
  };
  request(options, function (error, response, body) {
    if (error) {
      errorlogger.error('error',error)
      return;
    }
    ;
    console.log(body);
    body = JSON.parse(body);
    if (body.error) {
      errorlogger.error("Mapping NOt Found",body.error);
      callback({ 'createMapping': 1 });
      return;
    }
    errorlogger.info("Mapping Found");
    callback({ 'createMapping': 0 });
  });
}
const getAutoprovisonToken = function (key, accessToken,req, callback) {

  var sql = "";
  let token = accessToken ? accessToken : key;
   if (key)
    sql = "select u.access_token from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where FIND_IN_SET(right(u.user_email,19),'<EMAIL>')!=0 and provisionKey=?"
  if (accessToken)
    sql = "select u.access_token from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where FIND_IN_SET(right(u.user_email,19),'<EMAIL>')!=0 and u.access_token=?"
  var q = connection[req.headers['tenant-id']].execute.query(sql, token, function (err, rows) {
    if (!err) {
      console.log(q.sql)
      if (rows.length)
        callback(rows[0])
      else
        callback(0)

    }
    else
      callback(0)
  })
}


const getAccessToken = function (req,callback) {

  var sql = "select u.access_token from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where FIND_IN_SET(right(u.user_email,19),'<EMAIL>')!=0 and provisionKey!='' Limit 1"
  var q = connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
    if (!err) {
      console.log(q.sql)
      if (rows.length)
        callback(rows[0])
      else
        callback(0)

    }
    else
      callback(0)
  })
}


const getIndexAndObjectNames = function (uid, cb) {
  let sql = "SELECT cs.elasticIndexName indexName, cs.label contentSourceLabel, cso.name objectName,cso.label objectLabel,cs.content_source_type_id, scco.base_href FROM `search_clients` sc " +
    "left join search_clients_to_content_objects scco on sc.id=scco.search_client_id " +
    "left join content_source_objects cso on scco.content_source_object_id=cso.id " +
    "left join content_sources cs on cso.content_source_id=cs.id " +
    "where sc.uid=? group by cso.name,cs.elasticIndexName"
  connection.query(sql, [uid], function (err, rows) {
    var data = { indexName: [], objects: [] }
    if (!err) {
      if (rows.length) {
        rows.forEach(x => {
          data.indexName.push(x.indexName)
          data.objects.push({ objectName: x.objectName, content_source_type_id: x.content_source_type_id, objectLabel: x.objectLabel, base_href: x.base_href })
        })
        data.indexName = data.indexName.filter((x, i, a) => a.indexOf(x) == i) // for unique

      }
      cb(data)
    }
    else
      cb(data)
  })
}

const getcontentsourceIdsbyUid = function (uid, cb) {
  let sql = "SELECT cs.id,cs.content_source_type_id FROM `search_clients` sc " +
    "left join search_clients_to_content_objects scco on sc.id=scco.search_client_id " +
    "left join content_source_objects cso on scco.content_source_object_id=cso.id " +
    "left join content_sources cs on cso.content_source_id=cs.id " +
    "where sc.uid=? group by cso.name,cs.elasticIndexName"
  connection.query(sql, [uid], function (err, rows) {
    var data = []
    if (!err) {
      if (rows.length) {
        rows.forEach(x => { data.push({ id: x.id, type_id: x.content_source_type_id }) })
        // data = data.filter((x, i, a) => a.indexOf(x) == i) // for unique
      }
      cb(null, data)
    }
    else
      cb(null, data)
  })
}

const getMetadataFieldsInObjects = function (uid,req, cb) {
  let sql = "SELECT csof.merge_field_id, cs.elasticIndexName indexName,cso.name object,csof.name field, csof.label fieldLabel,csof.type fieldType,scmf.metaData, scmf.autosuggestField FROM `search_clients` sc left join search_clients_to_content_objects scco on sc.id=scco.search_client_id left JOIN content_source_objects cso on scco.content_source_object_id=cso.id left join search_client_metadata_fields scmf on scco.id=scmf.search_client_to_content_object_id left join content_source_object_fields csof on scmf.field_id=csof.id left join content_sources cs on cso.content_source_id=cs.id WHERE sc.uid=?"
  connection[req.headers['tenant-id']].execute.query(sql, [uid], function (err, data) {
    errorlogger.error("error", err)
    if (!err && data.length) {
      var output = []
      var indexes = data.map(x => x.indexName).filter((x, i, a) => a.indexOf(x) == i)
      for (var i = 0; i < indexes.length; i++) {
        var objects = data.filter(x => { if (indexes[i] == x.indexName) return x }).map(y => y.object).filter((x, i, a) => a.indexOf(x) == i)
        for (var o = 0; o < objects.length; o++) {
          var fields = data.filter(x => { if (indexes[i] == x.indexName && objects[o] == x.object) return x }).map(y => { if(y && y.field) { return { name: y.field, label: y.fieldLabel, type: y.fieldType, metaData: y.metaData, autosuggestField: y.autosuggestField, autoSuggestField: y.autosuggestField, merged: y.merge_field_id !== 0 }}});
          fields = fields.filter(x=> x);
          if(fields.length){
            output.push({
              "index": indexes[i],
              "object": objects[o],
              "fields": fields
            })
        }
        }
      }
      errorlogger.info("output", output)

      cb(null,output)
    }
    else
      cb(null,[])
  })
}

const getPreviewFieldsInObjects = function (scId,req, cb) {
  let sql = "SELECT cs.elasticIndexName indexName,cso.name object,csof.name field, csof.label fieldLabel,csof.type fieldType,scpf.preview_order, scpf.sc_id FROM `search_clients` sc left join search_clients_to_content_objects scco on sc.id=scco.search_client_id left JOIN content_source_objects cso on scco.content_source_object_id=cso.id left join search_client_preview_fields scpf on scco.id=scpf.search_client_to_content_object_id left join content_source_object_fields csof on scpf.field_id=csof.id left join content_sources cs on cso.content_source_id=cs.id WHERE sc.id=?"
  connection[req.headers['tenant-id']].execute.query(sql, [scId], function (err, data) {
    errorlogger.error("error", err)
    if (!err && data.length) {
      var output = []
      var indexes = data.map(x => x.indexName).filter((x, i, a) => a.indexOf(x) == i)
      for (var i = 0; i < indexes.length; i++) {
        var objects = data.filter(x => { if (indexes[i] == x.indexName) return x }).map(y => y.object).filter((x, i, a) => a.indexOf(x) == i)
        for (var o = 0; o < objects.length; o++) {
          var fields = data.filter(x => { if (indexes[i] == x.indexName && objects[o] == x.object) return x }).map(y => { if(y && y.field) { return { name: y.field, label: y.fieldLabel, type: y.fieldType, previewOrder: y.preview_order, sc_id: y.sc_id }}});
          fields = fields.filter(x=> x);
          if(fields.length){
            output.push({
              "index": indexes[i],
              "object": objects[o],
              "fields": fields
            })
        }
        }
      }
      errorlogger.info("output", output)

      cb(null,output)
    }
    else
      cb(null,[])
  })
}

const getSearchableFilterableSortableFields = function (uid, cb) {
  let sql = "SELECT scf.id id, csof.name,csof.label,csof.type,case when scf.use_as='Filter' OR scf.use_as='Tag'" +
    "then true else false end as isFilterable,csof.fragment_size,priority " +
    "FROM `search_clients` sc left join search_clients_to_content_objects scco on sc.id=scco.search_client_id " +
    "left join search_clients_filters scf on scco.id=scf.search_clients_to_content_object_id " +
    "left join content_source_object_fields csof on scf.content_source_object_field_id=csof.id " +
    "WHERE  sc.uid=? and scf.id is not null GROUP by BINARY csof.name"

 var q =   connection.query(sql, [uid], function (err, rows) {
    var data = []
    if (!err) {
      if (rows.length) {

        data = rows
      }
      cb(data)

    }
    else
      cb(data)
  })
}

const getDisplayFields = function (uid,req,cb) {
  let sql = "SELECT cs.elasticIndexName,cs.label indexLabel, cso.label elastic_object_label, cso.name elastic_object_name,csof.name elastic_field_name,case " +
    "when sco.title_field_id=scf.content_source_object_field_id " +
    "then 'Title' else scf.use_as end as display_field_name,scf.search_priority " +
    "FROM search_clients sc left join search_clients_to_content_objects sco on sc.id=sco.search_client_id " +
    "LEFT join content_source_objects cso on sco.content_source_object_id=cso.id " +
    "LEFT JOIN search_clients_filters scf on sco.id=scf.search_clients_to_content_object_id " +
    "left join content_source_object_fields csof on scf.content_source_object_field_id=csof.id " +
    "left join content_sources cs on cso.content_source_id=cs.id " +
    "where  scf.use_as!='Filter' and sc.uid=?"
  var q = connection[req.headers['tenant-id']].execute.query(sql, [uid], function (err, rows) {
    //errorlogger.debug('query',q.sql)
    var data = []
    if (!err) {
      if (rows.length) {

        data = rows
      }
      cb(data)

    }
    else
      cb(data)
  })
}

const getDisplayMapping = function (uid,req, cb) {
  let sql;
  if (uid == "") {
    sql = "SELECT cs.elasticIndexName contentSource, cs.label contentSourceLabel, cso.name objectName, "
      + "cso.label objectLabel, csof.name fieldName, csof.label fieldLabel "
      + "FROM search_clients_to_content_objects scco "
      + "LEFT JOIN search_clients sc ON scco.search_client_id = sc.id "
      + "LEFT JOIN content_source_object_fields csof ON scco.content_source_object_id = csof.content_source_object_id "
      + "LEFT JOIN content_source_objects cso ON scco.content_source_object_id = cso.id "
      + "LEFT JOIN content_sources cs ON cso.content_source_id = cs.id "
      + "GROUP BY contentSource, objectName, fieldName";
  } else {
    sql = "SELECT cs.elasticIndexName contentSource, cs.label contentSourceLabel, cso.name objectName, "
      + "cso.label objectLabel, csof.name fieldName, csof.label fieldLabel "
      + "FROM search_clients_to_content_objects scco "
      + "LEFT JOIN search_clients sc ON scco.search_client_id = sc.id "
      + "LEFT JOIN content_source_object_fields csof ON scco.content_source_object_id = csof.content_source_object_id "
      + "LEFT JOIN content_source_objects cso ON scco.content_source_object_id = cso.id "
      + "LEFT JOIN content_sources cs ON cso.content_source_id = cs.id "
      + "WHERE sc.uid=? "
      + "GROUP BY contentSource, objectName, fieldName";
  }
  connection[req.headers['tenant-id']].execute.query(sql, [uid], function (err, rows) {
    var data = []
    if (!err) {
      if (rows.length) {

        data = rows
      }
      cb(data)

    }
    else
      cb(data)
  })
}

const insertFilters = function (objectWithFilter,req, callback) {

  var fieldArr = objectWithFilter
  var colums = []
  var data = []
  for (var fi = 0; fi < fieldArr.length; fi++) {
    colums = []//initialize columns everytime
    const parameters = []
    //for (var key in fieldArr[fi]) {
    for (var f = 0; f < modelFilters.length; f++) {

      colums.push(modelFilters[f])

      parameters.push(parseInt(fieldArr[fi][modelFilters[f]] ? fieldArr[fi][modelFilters[f]] : 0)) //// add priority Plus
    }
    if (parameters[0])
      data.push(parameters)
  }

  errorlogger.info("data", data);


  const sqlCS = "INSERT INTO `search_clients_filters`(" + colums +
    ") VALUES " + data.map(x => { return "(" + x.map(y => { return y }) + ")" }) +
    "ON DUPLICATE KEY UPDATE " + colums.map(x => {
      return x + "=values(" + x + ")"
    }).join(',');
  if (colums.length)
    if (data.length)
      var q = connection[req.headers['tenant-id']].execute.query(sqlCS, function (errAsync, rows) {
        if (errAsync) {
          errorlogger.error("Error inside filter",errAsync);
          callback(errAsync, [])
        }
        else {

          callback(null, [])
        }
      })
    else {
      callback(null, [])
    }
  else callback(null, []);
}

const isObject = function (a) {
  return (!!a) && (a.constructor === Object);
};

const isArray = function (a) {
  return (!!a) && (a.constructor === Array);
};

const getSalesforcePermissionObjects = function (userObject, sfContentSource, callback) {


  var task = []
  for (var i = 0; i < sfContentSource.length; i++) {

    task.push((function (i) {
      return function (cb) {
        getSalesforcePermissionObjectsIndividually(sfContentSource[i].id, userObject, sfContentSource[i].objects.filter(x => x.enabled).map(y => { return "'" + y.name + "'" }), function (err, result) {
          // result.push('case')
          sfContentSource[i].verifiedObjects = []
          result.forEach(x => { sfContentSource[i].verifiedObjects.push(sfContentSource[i].objects.find(y => { if (y.name == x) return y }) ? sfContentSource[i].objects.find(y => { if (y.name == x) return y }) : "") })
          cb(null)
        })

      }
    })(i))
  }
  async.parallel(task, function (err, result) {
    if (!err)
      callback(null, sfContentSource)
    else
      callback(err)
  })

}

const reduceObject = function (final, arr) {
  return final.concat(arr)
}
const getSalesforcePermissionObjectsIndividually = function (contentSourceId, userObject, SFobjectsAdded, callback) {

  async.auto({

    create_salesforce_connection: function (cb) {
      initializeSalesforceConnectionSearchCall(contentSourceId, cb)
    },
    salesforcePermissionSetsOnUserId: ['create_salesforce_connection', function (dataFromAbove, cb) {
      let soql = "SELECT PermissionSetId FROM PermissionSetAssignment WHERE AssigneeId = '" + userObject.UserId + "'"
      errorlogger.debug("query", soql)
      dataFromAbove.create_salesforce_connection.query(soql, function (err, result) {
        errorlogger.error("err", err)

        if (!err) {
          var permissions = []
          for (var i = 0; i < result.records.length; i++) {
            permissions.push(result.records[i].PermissionSetId)
          }
          permissions = permissions.filter((x, i, a) => a.indexOf(x) == i)
          cb(null, permissions)
        }
        else {
          cb(null, [])
        }
      })
    }],
    salesforcePermissionSetsOnProfileId: ['create_salesforce_connection', function (dataFromAbove, cb) {
      let soql = "Select Id from PermissionSet where ProfileId ='" + userObject.ProfileId + "'"
      console.log("sql is", soql)
      dataFromAbove.create_salesforce_connection.query(soql, function (err, result) {
        console.log("err---", err)

        if (!err) {
          var permissions = []
          for (var i = 0; i < result.records.length; i++) {
            permissions.push(result.records[i].Id)
          }
          permissions = permissions.filter((x, i, a) => a.indexOf(x) == i)
          cb(null, permissions)
        }
        else {
          cb(null, [])
        }
      })
    }],
    salesforce_Objects: ['create_salesforce_connection', 'salesforcePermissionSetsOnUserId', 'salesforcePermissionSetsOnProfileId', function (dataFromAbove, cb) {
      let soql = "SELECT SobjectType,PermissionsRead FROM ObjectPermissions where SobjectType in (" + SFobjectsAdded.join(",") + ") AND PermissionsRead=true AND ParentId in (" + dataFromAbove.salesforcePermissionSetsOnUserId.map(x => "'" + x + "'").join(',') + (dataFromAbove.salesforcePermissionSetsOnProfileId.length ? "," : "") + dataFromAbove.salesforcePermissionSetsOnProfileId.map(x => "'" + x + "'").join(',') + ")"
      errorlogger.debug("sql", soql)
      dataFromAbove.create_salesforce_connection.query(soql, function (err, result) {
        errorlogger.error("err", err)

        if (!err) {
          var objects = []
          for (var i = 0; i < result.records.length; i++) {
            objects.push(result.records[i].SobjectType.toLowerCase())
          }
          objects = objects.filter((x, i, a) => a.indexOf(x) == i)
          cb(null, objects)
        }
        else {
          cb(null, [])
        }
      })
    }],
    feed_item_1_question: ['create_salesforce_connection', function (dataFromAbove, cb) {

      var soql = "SELECT CollaborationGroupId FROM CollaborationGroupMember WHERE (MemberId='" + userObject.UserId + "') OR (CollaborationGroup.CollaborationType='Public' )" //AND NetworkId='"+config.get('networkId')+"') "
      errorlogger.debug("sql", soql)
      if (SFobjectsAdded.indexOf("'feeditem_1'") != -1) {
        dataFromAbove.create_salesforce_connection.query(soql, function (err, result) {
          errorlogger.error("err", err)

          if (!err) {
            var groupIds = []
            for (var i = 0; i < result.records.length; i++) {
              groupIds.push(result.records[i].CollaborationGroupId)
            }
            groupIds = groupIds.filter((x, i, a) => a.indexOf(x) == i)
            cb(null, groupIds)
          }
          else {
            cb(null, [])
          }
        })
      }
      else
        cb(null, [])
    }],
    getCategoryLevelPermissions: ['create_salesforce_connection', 'salesforcePermissionSetsOnUserId', function (dataFromAbove, cb) {
      var options = {
        method: 'GET',
        url: dataFromAbove.create_salesforce_connection.instanceUrl + '/services/data/v44.0/tooling/sobjects/Profile/' + userObject.ProfileId,
        headers:
        {
          authorization: 'Bearer ' + dataFromAbove.create_salesforce_connection.accessToken
        }
      };

      //connSF.sobject("contact").describe(function (err, meta) { //only to intialize data
      request(options, function (error, response, body) {
        if (error) {
          errorlogger.error('error',error);
          cb(error, [])
        }
        else {
          errorlogger.info('Body',body);
          body = JSON.parse(body);
          if (body[0] ? body[0].errorCode == 'NOT_FOUND' : false) {
            cb(null, [])
          } else {
            cb(null, body.Metadata.categoryGroupVisibilities)
          }
        }
      })
    }],
    get_navi_topics: ['create_salesforce_connection', function (getDataAbove, cb) {
      getNavigationalTopics(getDataAbove.create_salesforce_connection, function (err, allTopics) {
        if (err)
          cb(err, [])
        else
          cb(null, allTopics)
      })
    }],
    entitlements_for_sessions:['create_salesforce_connection', function (dataFromAbove, cb) {
      if(userObject.ContactId!='null'){
      let soql = "select id,Entitlement.Product_Family__c from EntitlementContact where contactId = '" + userObject.ContactId + "'"
      console.log("sql is", soql)
      dataFromAbove.create_salesforce_connection.query(soql, function (err, result) {
        console.log("err---", err)

        if (!err) {
          var entitlements = []
          for (var i = 0; i < result.records.length; i++) {
            entitlements.push(result.records[i].Entitlement.Product_Family__c)
          }
          entitlements = entitlements.filter((x, i, a) => a.indexOf(x) == i)
          cb(null, entitlements)
        }
        else {
          cb(null, [])
        }
      })
    }
    else
      cb(null, [])
    }]
  }, function (err, result) {

    if (!err)
    callback(null, { feedRecordId: result.feed_item_1_question, object: result.salesforce_Objects, categoryGroups: result.getCategoryLevelPermissions, topics: result.get_navi_topics,entitlements:result.entitlements_for_sessions }) //{dataCategoryGrop,dataCaegories}
    else callback(null, [])
  })

}

// Get record types for lightning object present in salesforce
const getRecordTypeFromSalesforce = function(objectName , contentSourceId,cb){
  async.auto({
    create_salesforce_connection: function (cb) {
      initializeSalesforceConnectionSearchCall(contentSourceId, cb)
    },
    getRecordTypes:['create_salesforce_connection', function(dataFromAbove,cb){
      console.log("dataFromAbove",dataFromAbove);
      console.log("******************objectName",objectName);
      var sqlDataCategory = "Select id, name, SobjectType,IsActive from Recordtype where SobjectType=\'"+objectName+"\'";
      let connSF = dataFromAbove.create_salesforce_connection;
      connSF.query(sqlDataCategory, function (err, recordType) {
          if(err){
              cb(err,null);
          }else{
            cb(null,recordType.records);
          }
      });
    }]
  },function(err, result){
    if(err){
      cb(err,null);
    }else{
      cb(null,result.getRecordTypes);
    }

  })
}

const createMapping = function (contentSourceId, isTemp,req, callback) {

  var tempArr = []
  tempArr.push(contentSourceId)
  var check = checkArray(tempArr)
  var response = {
    status: constants.responseFlags.PARAMETER_MISSING,
    message: "PARAMETER MISSING"
  };
  if (!check) {
    callback("err", response)
  }
  else {
    var finalDataObject = {}
    finalDataObject.settings = metadata.elasticSetting

    async.auto({

      get_content_source_data: function (cb) {
        getContentSourceDataById(contentSourceId,req, function (err, contentSourceData) {
          if (!err)
            cb(null, contentSourceData)
          else
            cb(err, [])
        });
      },
      get_content_source_objects_and_fields: function (cb) {
        getContentSourceObjectsAndFieldsById(contentSourceId,req,function (err, dataObjectFields) {
          if (!err)
            cb(null, dataObjectFields)
          else
            cb(err, [])
        })
      },
      create_mapping_object: ['get_content_source_data', 'get_content_source_objects_and_fields', function (dataFromAbove, cb) {
        dataFromAbove.get_content_source_objects_and_fields.map(obj => obj.content_source_type_id = dataFromAbove.get_content_source_data.contentSource.content_source_type_id)
        console.log("ABOVEEEEEEEEEEEEE DATA",dataFromAbove.get_content_source_data);
        getMappingObject(contentSourceId, finalDataObject, dataFromAbove.get_content_source_objects_and_fields,(err,mapping)=>{
          if(err){
            cb(err,null);
          }else{
            finalDataObject = mapping;
            cb(null, finalDataObject);
          }
        });
      }],
      insert_mapping_to_elastic: ['create_mapping_object', function (dataFromAbove, cb) {

        let indexName = dataFromAbove.get_content_source_data.contentSource.elasticIndexName
        if (isTemp)
          indexName = indexName + "_temp"

        saveMapping(indexName, finalDataObject, function (result) {
          errorlogger.info("result",result)
          cb(null, "Done")
        })
      }]
    }, function (err, result) {
      if (!err)
        callback(null, { flag: constants.SUCCESS, message: "Mapping Done" })
      else
        callback(err, { flag: constants.ACTION_FAILED, message: "ACTION FAILED" + err })

    })
  }
}

const getMappingString = function (contentSourceId, isTemp, callback) {

  var tempArr = []
  tempArr.push(contentSourceId)
  var check = checkArray(tempArr)
  var response = {
    status: constants.responseFlags.PARAMETER_MISSING,
    message: "PARAMETER MISSING"
  };
  if (!check) {
    callback("err", response)
  }
  else {
    var finalDataObject = {}
    finalDataObject.settings = metadata.elasticSetting

    async.auto({

      get_content_source_data: function (cb) {
        getContentSourceDataById(contentSourceId,req, function (err, contentSourceData) {
          if (!err)
            cb(null, contentSourceData)
          else
            cb(err, [])
        });
      },
      get_content_source_objects_and_fields: function (cb) {
        getContentSourceObjectsAndFieldsById(contentSourceId, function (err, dataObjectFields) {
          if (!err)
            cb(null, dataObjectFields)
          else
            cb(err, [])
        })
      },
      create_mapping_object: ['get_content_source_data', 'get_content_source_objects_and_fields', function (dataFromAbove, cb) {
        dataFromAbove.get_content_source_objects_and_fields.map(obj => obj.content_source_type_id = dataFromAbove.get_content_source_data.contentSource.content_source_type_id)
        console.log("ABOVEEEEEEEEEEEEE DATA",dataFromAbove.get_content_source_data);
        let indexName = dataFromAbove.get_content_source_data.contentSource.elasticIndexName;
        getMappingObject(contentSourceId, finalDataObject, dataFromAbove.get_content_source_objects_and_fields,(err,mapping)=>{
          if(err){
            cb(err,null);
          }else{
            finalDataObject = {indexName: indexName, mapping: mapping};
            cb(null, finalDataObject);
          }
        });
      }]
    }, function (err, result) {
      if (!err)
        callback(null, finalDataObject)
      else
        callback(null, null);

    })
  }
}

const addUIDinFilter = function (uid, dslQuery) {
  if (!dslQuery.query.bool.must)
    dslQuery.query.bool.must = [];
  if (uid)
    dslQuery.query.bool.must.push(
      {
        term: {
          uid: uid
        }
      }
    )
  return dslQuery
}

const singleUpload = function (data, cb) {

  errorlogger.info("id is", data.index["_id"])
  var options = {
    "method": "PUT",
    "url": "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + data.index.index["_index"] + "/" + data.index.index["_type"] + "/" + data.index.index["_id"],
    "headers": {
      "content-type": "application/json"
    },
    "body": data.field,
    "json": true
  };
  request(options, function (err, response, body) {

    errorlogger.info("body", body)
    cb(null, body)
  })

}

const setLanguage = function (laguageCode) {
  let language = "English"
  switch (laguageCode) {
    case 'en': language = "English"
      break;
    case 'fr': language = "French"
      break;
    case 'es': language = "Spanish"
      break;
    case 'it': language = "Italian"
      break;
    case 'nl': language = "Dutch"
      break;
    case 'pl': language = "Polish"
      break;
    default: language = "English"
  }
  return language
}

const extensionAttachment = function (name) {
  if (name.split('.').pop() == 'pdf' ||
    name.split('.').pop() == 'doc' ||
    name.split('.').pop() == 'docx' ||
    name.split('.').pop() == 'ppt' ||
    name.split('.').pop() == 'pptx' ||
    name.split('.').pop() == 'potx' ||
    name.split('.').pop() == 'csv' ||
    name.split('.').pop() == 'xsl' ||
    name.split('.').pop() == 'txt' ||
    name.split('.').pop() == 'rtf' ||
    name.split('.').pop() == 'evtx' ||
    name.split('.').pop() == 'log'
  )
    return true
  else
    return false
}
const deleteDriveSpace = function (objectId,req,callback) {
  const sql = 'DELETE FROM `content_source_spaces` WHERE `content_source_spaces`.`content_source_id` = ?'
  connection[req.headers['tenant-id']].execute.query(sql, [objectId], function (err, resultFields) {
    if (err) {
      callback(err, [])
    } else callback(null, resultFields)
  })
}

const initializeSalesforceConnection = function (contentSourceId,req, callback) {
  getContentSourceDataById(contentSourceId,req, function (err, contentsourceData) {

    let connSF = meanSalesforceFunction(contentsourceData)

    connSF.identity(function (err, meta) {
      if(err){
        errorlogger.error(`error while creating SF conn: ${err}`);
        callback(err); 
      }
      else
        callback(null, connSF)
      // })
    });

  })
}

function meanSalesforceFunction(contentsourceData) {
  const connSF = new jsforce.Connection({
    "oauth2": {
      loginUrl: contentsourceData.contentSource.url,
      clientId: contentsourceData.authorization.client_id,
      clientSecret: contentsourceData.authorization.client_secret,
      redirectUri: appVariables.oAuthRedirectURI //hard coded for localdev redirect URI 'https://localhost/backend'+appVariables.salesforce.redirectUri
    },
    "instanceUrl": contentsourceData.authorization.instanceURL,
    "accessToken": contentsourceData.authorization.accessToken,
    "refreshToken": contentsourceData.authorization.refreshToken,
    "version":"47.0"
  });
  return connSF
}

const initializeSalesforceConnectionSearchCall = function (contentSourceId, callback) {
  getContentSourceDataById(contentSourceId, function (err, contentsourceData) {
    if(err) //error handled
      callback(err);
    else {
      let connSF = meanSalesforceFunction(contentsourceData)
      callback(null, connSF)
    }
  })
}
const getFieldsApiName = function (dataObjectFields) {
  dataObjectFields.forEach(x => {
    x.fields.forEach(y => {
      y.trueKey = y.name;
      if (!y.annotated) {
        if (y.name.split("___").length > 1) {
          y.name = y.name.split("___")[2]
        }
      }
    })
  })
  return dataObjectFields
}

const getContentsourceType = function (contentSourcetypeId,req, callback) {
  const sql = 'select * FROM `content_source_types` WHERE `id` = ?'
  connection[req.headers['tenant-id']].execute.query(sql, [contentSourcetypeId], function (err, resultFields) {
    if (err) {
      callback(err, [])
    } else callback(null, resultFields[0])
  })
}

const getAllContentSourcesAndFields = function (callback) {
  const sql = 'select * from (SELECT * FROM ( SELECT id cs_id, name cs_name,elasticIndexName e_name, label cs_label FROM content_sources)cs JOIN ( SELECT id cso_id, name cso_name, label cso_label, content_source_id csid FROM content_source_objects)cso ON cs.cs_id = cso.csid) t1 join (select content_source_object_id csoid, name csof_name, label csof_label from content_source_object_fields where isFilterable=1) csof on t1.cso_id=csof.csoid'
  connection.query(sql, function (err, resultFields) {
    if (err) {
      callback(err, [])
    } else callback(null, resultFields)
  })
}

const httpRequest = function (method, url, qs, body, header, callback) {
  var options = {
    timeout:header.timeout || 12000,
    method: method,
    rejectUnauthorized: false,
    url: url,
    qs:
    {
      q: qs
    },
    json: true
  };
  if (body)
    options.body = body
  if (header)
    options.headers = header
  request(options, function (error, response, body) {
    if(error) {
      errorlogger.error("error is for url ",url, error)
      error = {
        code: 500,
        msg: 'Internal Server Error'
      }
      callback(error)
     }
    else callback(null, body, response);
  })
}

function stripHtml(bulkStringFieldsData) {
  return striptags(bulkStringFieldsData, appVariables.allowedTags)
}


function getByteLen(normal_val) {
  // Force string type
  normal_val = String(normal_val);

  var byteLen = 0;
  for (var i = 0; i < normal_val.length; i++) {
    var c = normal_val.charCodeAt(i);
    byteLen += c < (1 << 7) ? 1 :
      c < (1 << 11) ? 2 :
        c < (1 << 16) ? 3 :
          c < (1 << 21) ? 4 :
            c < (1 << 26) ? 5 :
              c < (1 << 31) ? 6 : Number.NaN;
  }
  return byteLen;
}

const deleteByQuery = (indexName ,query, cb) => {
  var options = {
    method: 'POST',
    url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + indexName + "/_delete_by_query",
    headers: {
      'content-type': 'application/json'
    },
    body: query,
    json : true
  };

  request(options, function (error, response) {
    if (error) cb(error);
    else
      errorlogger.info("successDelte")
    cb(1)
  });
};

function deleteIdFromIndex(indexName, Idarray, callback) {

  var options = {
    method: 'POST',
    url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + indexName + "/_delete_by_query",
    headers: {
      'content-type': 'application/json'
    },
    body: {
      query: {
        bool: {
          must:  Idarray
        }
      }
    },
    json: true
  };
  request(options, function (error, response) {
    if (error){
    errorlogger.info("error while deleting sfdata", JSON.stringify(error), indexName, Idarray);
    callback(error);
    }
    else
      callback(1)
  });
}
function dateConversion(dateTime) {
  errorlogger.info("date is", dateTime)
  if (dateTime && dateTime.length) {
    dateTime = dateTime[0]
    var date = new Date(dateTime);
    if (!date.getTime()) {
      str = dateTime.replace(/-/g, '/');
      date = new Date(str);
    }
    var date_appointment = (date.getUTCMonth() + 1) + '/' + date.getUTCDate() + '/' + date.getUTCFullYear();
    var hours = date.getUTCHours();
    var minutes = date.getUTCMinutes();
    if (minutes < 10)
      minutes = "0" + minutes;
    var suffix = "AM";
    if (hours >= 12) {
      suffix = "PM";
      hours = hours - 12;
    }
    if (hours == 0) {
      hours = 12;
    }
    var current_time = hours + ":" + minutes + " " + suffix;
    return date_appointment + " " + current_time;
  } else {
    return [];
  }
}

const getAllSearchClients = function (callback) {
  const sql = 'SELECT * FROM search_clients';
  connection.query(sql, function (err, data) {
    if (err) {
      callback(null, []);
    }
    else {
      callback(null, data);
    }
  });
}

const getKnowledgeGraphRelation = function (callback) {
  const sql = `SELECT * FROM knowledge_graph_relation`
  connection.query(sql, function (err, data) {
    if (err) {
      callback(null, []);
    }
    else {
      callback(null, data);
    }
  })
}

const insertUpdateApiCrawlerFields = function (apiCrawlerFields, req,callback) {
  const colums = []
  const parameters = []

  for (var key in apiCrawlerFields) {
    if (apiCrawlerFields.hasOwnProperty(key)) {
      colums.push(key)
      parameters.push(apiCrawlerFields[key])
    }
  }
  const sqlCS = "INSERT INTO `api_crawler_fields`(" + colums +
    ") VALUES (" + colums.map(x => {
      return '?'
    }).join(',') + ") " +
    "ON DUPLICATE KEY UPDATE " + colums.map(x => {
      return x + "=values(" + x + ")"
    }).join(',');
  const q = connection[req.headers['tenant-id']].execute.query(sqlCS, parameters, function (errAsync, rows) {
    errorlogger.debug("query", q.sql)
    if (errAsync) {
      callback(errAsync, [])
    }
    else {
      callback(null, rows)
    }
  })
}

const getApiCrawlerFieldsById = function (contentSourceId,req, callback) {
  const sql = 'SELECT * FROM `api_crawler_fields` WHERE content_source_id=?'
  connection[req.headers['tenant-id']].execute.query(sql, [contentSourceId], function (err, result) {
    if (err) {
      callback(err, {})
    }
    else {
      result = result[0];
      callback(null, result)

    }
  })
}


const getAllContentSource = function (req,callback) {
  const sql = 'SELECT * FROM content_sources';
  connection[req.headers['tenant-id']].execute.query(sql, function (err, data) {
    if (err) {
      errorlogger.error("error in fetching all cs ", err);
      callback(err, []);
    }
    else {
      callback(null, data);
    }
  })
}

const getSupportUrl = function (callback, searchclientId) {
  const sql = 'SELECT support_url FROM `deflection_formula` WHERE `search_client_id`=?';
  connection.query(sql, [searchclientId], function (err, data) {
    if (err || !data.length) {
      callback(err, "");
    }
    else {
      callback(null, data[0].support_url);
    }
  })
}

const getSearchClientIdUsingUid = function (uid,req, callback) {
  const sql = 'SELECT id FROM `search_clients` WHERE `uid`=?';
  connection[req.headers['tenant-id']].execute.query(sql, [uid], function (err, data) {
    if (err || !data.length) {
      callback(err, "");
    }
    else {
      callback(null, data[0].id);
    }
  })
}

const getUidAndSupportUrl = function (callback) {
  const sql = 'SELECT uid, support_url FROM `search_clients` left join deflection_formula on search_clients.id = deflection_formula.search_client_id';
  connection.query(sql, function (err, data) {
    if (err || !data.length) {
      callback(err, {});
    }
    else {
      var result = {};
      data.map(d => {
        result[d.uid] = d.support_url;
      });
      callback(null, result);
    }
  })
}

function getAddonsStatus(req ,cb) {
  connection[req.headers['tenant-id']].execute.query(`SELECT * FROM addons_status`, (error, rows) => {
    if (rows && rows.length) {
      let addonRows = [];
      rows.map(x => {
        if (x.is_installed == 1)
          addonRows[x.addon_id] = x;
      })
      cb(null, addonRows);
    } else {
      cb(null, [])
    }
  });
}

function clearConfigCache() {
  return delete require.cache[require.resolve('config')];
}

function getNavigationalTopics(conn, callback) {

  getCommunityNetworkNames(conn,(err,communities)=>{
    if(err){ //error handled
      console.log(err);
      callback(null,[])
    }
    else {
      var taskJSonTopics=[]
      for(var i=0;i<communities.length;i++)
      {
        taskJSonTopics.push(function (i) {
          return function (cb) {
            getNavigationalTopicsWithCategories(conn,communities[i],cb)
          }
        }(i))
      }
      async.parallel(taskJSonTopics,function (err,result) {
        if(!err)
        {
          result=result.filter(n => { if(n && n.community) return n })
          callback(null,result)
        }
        else
          callback(null,[])
      })
    }
  })

}

function getNavigationalTopicsWithCategories(connSF, community, callback) { //communityId is network id starting with 0D
  var options = {
    method: 'GET',
    url: connSF.instanceUrl + '/services/data/v45.0/connect/communities/' + community.id + '/managed-topics',
    qs:
    {
      depth: '5',
      managedTopicType: 'Navigational'
    },
    headers:
    {
      authorization: 'Bearer ' + connSF.accessToken
    }
  };

  //connSF.sobject("contact").describe(function (err, meta) { //only to intialize data
  request(options, function (error, response, body) {
    if (error  ) {
      errorlogger.error(error);
      callback(null, {})
    }
    else {
      body = JSON.parse(body)
      if (body[0] ? body[0].errorCode == 'NOT_FOUND' : false) {
        callback(null, {})
      }
      else
        callback(null, { topics: body["managedTopics"], community: community })
    }
  })
}

getCommunityNetworkNames = function (conn, callback) {
  let soql = "select id, name from network"
  errorlogger.debug("sql", soql)
  conn.query(soql, function (err, result) {
    errorlogger.error("err", err)

    if (!err) {
      var network = []
      for (var c = 0; c < result.records.length; c++) {
        network.push({ name: result.records[c].Name.replace(/\s/g, '_') + "_community_topic_navigation", label: result.records[c].Name, id: result.records[c].Id })
      }
      callback(null, network)
    }
    else callback(null, [])
  })
}

getObjectFieldNames = function (content_source_type_id, fieldArr, modelField) {
  if (modelField == "name" && 
  constants.CONTENT_SOURCES_WITH_UNIQUE_FIELD_NAMES.includes(content_source_type_id) 
  && !fieldArr.annotated
  && !fieldArr.isMerged 
  && fieldArr[modelField]
  && !fieldArr[modelField].toLowerCase().includes("attachment_") 
  && !fieldArr[modelField].toLowerCase().includes("_nested") 
  && !fieldArr[modelField].toLowerCase().includes("_navigation") 
  && !fieldArr[modelField].toLowerCase().includes("_flat")
  && !fieldArr[modelField].toLowerCase().includes("__body__s")
  && !fieldArr[modelField].toLowerCase().includes("accountid") 
  && !fieldArr[modelField].toLowerCase().includes("contactid")
  && !fieldArr[modelField].toLowerCase().includes("isvisiblein")
  && !fieldArr[modelField].toLowerCase().includes("__typecasted__"))
    return true;
  else
    return false;
}

const deleteData = function (indexName, objectName, callback) {
  var optionsDel = {
    method: 'POST',
    url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + indexName + '/_delete_by_query',
    headers: { 'content-type': 'application/json' },
    body: { "query": { "term": { "_type": objectName } } },
    json: true
  };
  request(optionsDel, (error, responseDel, bodyDel) => {
    if (!error && bodyDel) {
      //bodyDel = JSON.parse(bodyDel);
      callback(null);
    } else {
      console.log(error)
      return
    }
  });
}

const executeQuery = function(query,callback){
  connection.query(query, function (err, data) {
    callback(err,data);
  });
}

const updateObjectMapping = function (indexName, objectName, fields, callback) {
  async.auto({
      getMapping: cb => {
          getMapping(indexName, (res) => {
              if (res.createMapping)
                  cb(null, 1)
              else cb(null, 0)
          })
      },
      createMapping: ['getMapping', (dataFromAbove, cb) => {
          var options;
          if (dataFromAbove.getMapping) {
            let SendMapping = {};
            SendMapping.settings = metadata.elasticSetting;
            SendMapping.mappings = {};
            SendMapping.mappings[objectName] = fields;
              options = {
                  method: 'PUT',
                  url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + indexName + '/',
                  body: JSON.stringify(SendMapping)
              };
          } else {
              options = {
                  method: 'POST',
                  url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + indexName + '/_mapping' + "/" + objectName,
                  headers: { 'content-type': 'application/json' },
                  body: fields,
                  json: true
              };
          }
          request(options, function (error, response, body) {
              if (error) {
                  errorlogger.error('error', error)
              }
              errorlogger.info("body", body);
              if (body.acknowledged == true) {
                  cb(null);
              }
              else {
                  cb(body.error);
              }
          });
      }]
  }, function (err, res) {
      if (!err)
          callback(null, []);
  })
}

function updateObjectPidAndStatus(pid, objectDiff, status, callback) {
  let sqlCS = '';
  if (status)
    sqlCS = "UPDATE content_source_objects SET object_pid=?, object_status=null WHERE id in (" + objectDiff.map(x => { return x }).join(',') + ") ";
  else
    sqlCS = "UPDATE content_source_objects SET object_pid=? WHERE id in (" + objectDiff.map(x => { return x }).join(',') + ") ";

  let q = connection.query(sqlCS, [pid], function (errAsync, rows) {
    callback(null, []);
  })
}


const getDisplayFieldsInSearch = function (uid,req, cb) {
  let sql = "SELECT csof.merge_field_id, cs.elasticIndexName,cs.label indexLabel, cso.name elastic_object_name,csof.name elastic_field_name,case " +
    "when sco.title_field_id=scf.content_source_object_field_id " +
    "then 'Title' else scf.use_as end as display_field_name,scf.use_as summary_display_name,scf.search_priority,scf.summary_length,scf.extra_field, scf.track_analytics " +
    "FROM search_clients sc left join search_clients_to_content_objects sco on sc.id=sco.search_client_id " +
    "LEFT join content_source_objects cso on sco.content_source_object_id=cso.id " +
    "LEFT JOIN search_clients_filters scf on sco.id=scf.search_clients_to_content_object_id " +
    "left join content_source_object_fields csof on scf.content_source_object_field_id=csof.id " +
    "left join content_sources cs on cso.content_source_id=cs.id " +
    "where sc.uid=?"
  var q = connection[req.headers['tenant-id']].execute.query(sql, [uid], function (err, rows) {
    console.log(q.sql)
    var data = []
    if (!err) {
      if (rows.length) {

        data = rows
      }
      cb(null,data)

    }
    else
      cb(null,data)
  })
}

const getPriorityFieldsInFilter = function (uid,req, cb) {
  let sql = "SELECT scf.id id, csof.name,csof.label,csof.type,case when scf.use_as='Filter' OR scf.use_as='Tag' OR scf.use_as='SearchFilter' OR scf.use_as='SummaryFilter'" +
    "then true else false end as isFilterable,csof.fragment_size,priority " +
    "FROM `search_clients` sc left join search_clients_to_content_objects scco on sc.id=scco.search_client_id " +
    "left join search_clients_filters scf on scco.id=scf.search_clients_to_content_object_id " +
    "left join content_source_object_fields csof on scf.content_source_object_field_id=csof.id " +
    "WHERE  sc.uid=? and scf.id is not null"
// GROUP by BINARY (CONCAT(csof.label, csof.name))
 var q =   connection[req.headers['tenant-id']].execute.query(sql, [uid], function (err, rows) {
    var data = []
    if (!err) {
      if (rows.length) {

        data = rows
      }
      cb(data)

    }
    else
      cb(data)
  })
  console.log(q.sql);
}

sendCrawlingStatusEmail = function (data, status, callback){

  //let sql = "Select DISTINCT subscriberEmails from email_notification_contentsources where contentSources = ? OR contentSources = 'All' OR contentSources like '"+data.label+",%' OR contentSources like '%,"+data.label+"' OR contentSources like '%,"+data.label+",%'";
  let sqlquery = "Select DISTINCT subscriberEmails from email_notification_contentsources where contentSources = ? OR contentSources = 'all' OR contentSources like '"+data.id+",%' OR contentSources like '%,"+data.id+"' OR contentSources like '%,"+data.id+",%'";
  connection.query(sqlquery, [data.id] ,function (err, result) {
    if (err) {
      callback(err, '');
    }
    else if(!result.length) {
      callback(null, 'no subscribers found');
    }
    else {
      let subscriberEmails = result.map((mail) => mail.subscriberEmails);
     // let body = status == "success" ? `<p>Crawling for <b>${data.label}</b> has successfully completed.</p>` : ( status == "failed" ? `<p>Crawling for <b>${data.label}</b> has failed.</p>`: `<p>Crawling process for <b>${data.label}</b> was stopped.</p>`)
     if(status == "success") {
        var body = emailTemplates.sucesscrawltemplate(data);
          searchunifyEmail.sendEmail(subscriberEmails, `${data.label} Crawling Status`, body, (response) => {
            if(!response){
              callback('mail could not be sent');
            }
            else{
              callback(null, 'mail sent');
            }
        });
      }else if(status == "killed"){
        var body = emailTemplates.crawlkilledtemplate(data);
          searchunifyEmail.sendEmail(subscriberEmails, `${data.label} Crawling Status`, body, (response) => {
            if(!response){
              callback('mail could not be sent');
            }
            else{
              callback(null, 'mail sent');
            }
        });
      }else{
          console.log('i am crawl',data.id);
         getcontentsourcesQuery = "SELECT name FROM search_clients where id IN (SELECT search_client_id from search_clients_to_content_objects where content_source_object_id IN (SELECT id from content_source_objects where content_source_id = ? ) )";
         connection.query(getcontentsourcesQuery, [data.id] ,function (err, result) {
           if (err) {
             console.log('query error',err)
           }else{
              data.searchclientshtml='';
              if(result.length == 0) {
                data.searchClients = 0;
                data.headerline = 'No search clients affected';
                data.searchclientshtml='';
              }else{
                data.searchClients = result.length;
                  data.headerline = `Search experience on the following search clients might be affected:`;
                  for(let contentsource_index = 0;contentsource_index<result.length;contentsource_index++){
                  data.searchclientshtml+=`<li style='display: inline-grid;padding: 3px 10px;'>${contentsource_index+1 +'. '+result[contentsource_index].name }</li>`;
                }
              }
             var body = emailTemplates.failcrawltemplate(data);
             searchunifyEmail.sendEmail(subscriberEmails, `${data.label} Crawling Status`, body, (response) => {
                if(!response){
                  callback('mail could not be sent');
                }
                else{
                  callback(null, 'mail sent');
                }
              });
           }
        });
      }

      // callback(null, 'mail sent');
    }
  })
}

function registerEmailJwt(data, cb){
  if (data.email) {
    let dataToEncode = {email: data.email, forget_password_token: data.forget_password_token};
    let token = jwt.sign(dataToEncode, 'secret123');
    cb(null, {token});
  }
  else if (data.token){
    let decoded = jwt.verify(data.token, 'secret123');
    cb(null, {email : decoded.email , forget_password_token: decoded.forget_password_token});
  }
  else {
    cb('No data received', {});
  }
}

htmlvalidate = function checkobjectforhtml(value)
{
    for (var key in value) {
      var htmlRegex = new RegExp("<\/?[^>]+(>|$)");
        var check_htmlflag = true;
      if(htmlRegex.test(value[key]) == true){

        check_htmlflag =  false;
          break;
      }else{
        check_htmlflag = true;
      }
    }
       return check_htmlflag;
}


function redisCache(key,fname, ...parameter){
  //let funname = "custom_boostingQuery";
let callback = parameter.pop();
  if(config.get('setSearchRedis'))
  {
    redisclient.get(key, (error, response) => {

        if(response && response !== 'undefined'){
          callback(null,JSON.parse(response));

        }else{
          fname(...parameter, function(err,response){

            redisclient.set(key, JSON.stringify(response), "EX", 60 * 24);
            callback(null,response);
          });

        }

    });
  }else{
    fname(...parameter, function(err,response){
      callback(err,response);
    });
  }
}
/*This function clear the cache for only search results keys */
function CleanRedisCache(keysToDelete = []){
  const key = 'SUR*';
  redisclient.keys(key, function (err, keys) {
    if (keysToDelete.length > 0) {
      keys = keysToDelete;
    }
    redisclient.del(keys, function(err, o) {
      errorlogger.trace(`redis data cleared for keys: ${keys}`,o)
    });
  });
}

/*This function clear the cache for only search formula keys */
function CleanSearchRedisCache(){
  var key = 'SUPerm*'
  redisclient.keys(key, function (err, keys) {

    redisclient.del(keys, function(err, o) {
      errorlogger.trace("redis SUR data cleared",o)
    });
  });
}

function getSplitIndexes(startDate, endDate, callback){
  async.auto({
    get_custom_date: function (cb) {
        let startDateS = new Date(startDate);
        let endDateS = new Date(endDate);
        let datesArr = [];
        let todaysDate = new Date();
        if (endDateS > todaysDate) {
            endDateS = todaysDate;
        }
        console.log(startDateS, endDateS);
        if (startDateS > todaysDate) {
            return callback("Invalid Date", null);
        } else if (startDateS.getMonth() == endDateS.getMonth()) {
            let getMonth = (parseInt(startDateS.getMonth() + 1) > 9 ? (parseInt(startDateS.getMonth() + 1)) : "0" + (parseInt(startDateS.getMonth() + 1)))
            datesArr.push(startDateS.getFullYear() + "-" + getMonth);
            cb(null, datesArr);

        }
        else {
            var ss = new Date(startDateS);
            let getMonth = (parseInt(ss.getMonth() + 1) > 9 ? (parseInt(ss.getMonth() + 1)) : "0" + (parseInt(ss.getMonth() + 1)))
            datesArr.push(ss.getFullYear() + "-" + getMonth);
            while (ss <= endDateS) {
                getMonth = (parseInt(ss.getMonth() + 1) > 9 ? (parseInt(ss.getMonth() + 1)) : "0" + (parseInt(ss.getMonth() + 1)))
                if (datesArr[datesArr.length - 1] != ss.getFullYear() + "-" + getMonth) {
                    getMonth = (parseInt(ss.getMonth() + 1) > 9 ? (parseInt(ss.getMonth() + 1)) : "0" + (parseInt(ss.getMonth() + 1)))
                    datesArr.push(ss.getFullYear() + "-" + getMonth)
                }
                var newDate = ss.setDate(ss.getDate() + 1);
                ss = new Date(newDate);
            }
            cb(null, datesArr);
        }
    },
    check_index_exitstence: ['get_custom_date', function (date_range, cb) {

        var taskArr = [];
        for (var mo = 0; mo < date_range.get_custom_date.length; mo++) {
            taskArr.push(checkAnalyticsIndexExitstence.bind(null, date_range.get_custom_date[mo]));
        }
        async.series(taskArr, function (err, data) {
            if (err) {
                cb(err, []);
            }
            else {
                var indexArr = [];
                for (var arr = 0; arr < data.length; arr++) {
                    if (data[arr]) {
                        indexArr.push(data[arr]);
                    }
                }
                cb(null, indexArr);
            }
        });
    }]
  }, function (err, result) {
      var index = '';
      for (var i = 0; i < result.check_index_exitstence.length; i++) {
          index = index + result.check_index_exitstence[i] + ",";
      }
      index = index.slice(0, -1);
      errorlogger.info("Selected Analytics Index:", index);
      if(index.trim() == '')
          index = config.get('elasticIndex.analytics');
      callback(null, index);
  });
}

const checkAnalyticsIndexExitstence = function (index, cb) {
  let todaysDate = new Date();
  let latest_date = todaysDate.getFullYear() + "-" + ((todaysDate.getMonth() + 1) > 9 ? "" + (todaysDate.getMonth() + 1) : "0" + (todaysDate.getMonth() + 1));
  if (latest_date == index) {
      var new_index = config.get('elasticIndex.analytics');
  } else {
      var new_index = index + "_" + config.get('elasticIndex.analytics');
  }
  var options = {
      method: 'GET',
      url: "http://" + config.get('elasticIndex.host') + ":" + config.get('elasticIndex.port') + '/' + new_index,
      headers:
      {
          'content-type': 'application/json',
      }
  };
  request(options, function (error, response, body) {
      if (error) {
          cb(error, null);
      }
      else {
          if (JSON.parse(body).error) {
              cb(null, false);
          }
          else {
              cb(null, new_index);
          }
      }
  });
}

function unblockUser(email,req, callback) {
  let sql = 'Update user set is_blocked = 0 where user_email = ?';
  connection[req.headers['tenant-id']].execute.query(sql, email, function (err, rows) {
    if (!err && rows.affectedRows){
      redisclient.del(`${email}_resend_count`);
      redisclient.del(`${email}_incorrect_count`);
      redisclient.del(`node-admin-login-${email}`);
      callback(null, {flag: 200, message: "User unblocked"});
    }
    else 
      callback(null, {flag: 400, message: "Invalid User"});
  })
}

function shouldFreqBeUpdated(syncFrequency, syncFrequencyName, id){

  if(!id){
    return true;
  }

  let shouldUpdate = false;
  if((syncFrequency === null && syncFrequencyName === "Never")
  || (syncFrequency === null && syncFrequencyName ===  "Yearly")
  || (syncFrequency !== null && syncFrequency !== undefined )){
    shouldUpdate = true;
  }
  return shouldUpdate;
}

function checkIfCSUsedInSearchClients(contentSourceId,req, callback) {

  let csPresentInSC = false;

  let sqlQuery = `
    SELECT
    sc.name
    FROM
    search_clients sc
    LEFT JOIN search_clients_to_content_objects scco
    ON scco.search_client_id = sc.id
    LEFT JOIN content_source_objects cso
    ON cso.id = scco.content_source_object_id
    WHERE cso.content_source_id = ?`;

    connection[req.headers['tenant-id']].execute.query(sqlQuery, contentSourceId, function (err, result) {

    if (result.length > 0) {
      csPresentInSC = true;
    }

  })

  callback(null, csPresentInSC)

}
const getContentSourceFields = (contentSourceId, cso_name, req) => {

  return new Promise((resolve, reject) => {
    try {
      const sql = 
        `select
          name, label
        from
          content_source_object_fields csof
        join (
          select
            cso.id as csoid, cs.id as csid
          from
            content_source_objects cso
          join content_sources cs
          on cso.content_source_id = cs.id
          where cs.id=${parseInt(contentSourceId, 10)}
              and cso.name ='${cso_name}') cs_cso
        on csof.content_source_object_id = cs_cso.csoid`;

      // connection.query(sql, function (err, result) {
      connection[req.headers['tenant-id']].execute.query(sql, function (err, result) { 
        if (err || (result && !result.length)) {
          reject(err)
        } else {
          resolve(result)
        }
      })
    } catch (err) {
      reject(err)
    }
  })
}
const getFieldData = (elasticIndex, object, fields, startDate, startDateField, scroll, size, offset) => {
  return new Promise((resolve, reject) => {
    try {
      startDate = startDate.split(new RegExp('[T ]+'))[0];
      startDate = startDate.split('"').join('');
      let queryObject = {
        "_source": {
          "includes": fields
        }
      };
      queryObject.query = {
        bool: {
          must: {
            range: {
            }
          }
        }
      }
      queryObject.query.bool.must.range[startDateField] = { gte: startDate + '||/d', format: "yyyy-MM-dd" };
      let elasticQuery = {
        index: elasticIndex,
        type: object,
        from: offset,
        size: size,
        body: queryObject
      }
      if(scroll){
        elasticQuery.scroll = scroll;
      }
      clientCS.search(elasticQuery, (err, data) => {
        if (err) {
          reject([])
        } else {
          data.hits.hits.map(d => {
            Object.keys(d._source).map(k => {
              if (typeof d._source[k] == 'string') {
                d._source[k] = d._source[k].split(new RegExp('[\r\n\t ]+')).join(' ');
              }
            })
          })
          resolve(data)
        }
      })
    } catch (err) {
      reject(err)
    }
  })
}

const getScrollData = (scrollId, scroll) => {
  return new Promise((resolve, reject) => {
    clientCS.scroll({
      scrollId,
      scroll
    }, (err, data) => {
      if (err) {
        return reject(err)
      } else {
        return resolve(data)
      }
    })
  })
}

const fetchClientInfo = (tenantId, cb) => {
  try{
    statusPageAuth.createToken(tenantId,(err, token) => {
      const options = {
          method: 'GET',
          url: `${config.get('statusPageService.url')}/admin/clients/getClient?tenantId=${tenantId}`,
          timeout:5000,
          headers: {
              'Authorization': 'Bearer ' + token
          },
      }
      request(options, function (error, response, body) {
          if (!error && body) {
              cb(null, JSON.parse(body));
          } else {
              cb(error, null);
          }
      });
    });
  }catch(e){
    cb(e,null)
  }
};

const fetchApiLimitInfo = (tenantId, cb) => {
  statusPageAuth.createToken(tenantId,(err, token) => {
      const options = {
          method: 'GET',
          url: `${config.get('statusPageService.url')}/admin/clients/getApiLimits?tenantId=${tenantId}`,
          timeout:5000,
          headers: {
              'Authorization': 'Bearer ' + token
          },
      }
      request(options, function (error, response, body) {
          if (!error && body) {
              cb(null, JSON.parse(body));
          } else {
              cb(error, null);
          }
      });
  });
};

const fetchApiLimitInfoPromisified = (tenantId) => {
  return new Promise((resolve, reject) => {
    fetchApiLimitInfo(tenantId, (err, data) => {
      if (err) {
        reject(err);
      } else {
        
        if(data==null){
          console.log(">>>Setting Default Limts for tenant",tenantId)
         resolve(constants.DEFAULT_CONSUMPTION_LIMTS);
         return;
        }
        resolve(data);
      }
    });
  });
};

function createDefaultLimitObject (tenantIds){
  return tenantIds.map(id =>({tenantId:id, data:constants.DEFAULT_CONSUMPTION_LIMTS}));
}
const fetchApiLimitInfoForAllTenants = async (tenantIds) => {
  try {
    const results = await Promise.all(
      tenantIds.map(tenantId => fetchApiLimitInfoPromisified(tenantId).then(data => ({ tenantId, data })))
    ).catch(err=>{
      throw err;
    });
    return results;
  } catch (error) {
    errorlogger.error(">>Error fetching API limit info for tenants:", error);
  }
  const defaultLimits = createDefaultLimitObject(tenantIds);
  errorlogger.info(">>Using defaultLimits Quota Limits:", JSON.stringify(defaultLimits));
  return defaultLimits;
};

const encryptDecryptCreds = (data, type = 'encrypt') => {
  try {
    if (type == 'encrypt') return aes256.encrypt(appVariables.analytics.encryptionKey, data);
    else return aes256.decrypt(appVariables.analytics.encryptionKey, data);
  } catch(e) {
    errorlogger.error(e);
    return data;
  }
};
const usersForAccessSettings = async (req) => {
    try {
        const cookie = req.headers.cookie;
        const headers = {
            "content-type": "application/json",
            Cookie: cookie,
            referer: req.headers.referer,
        };
        const body = {
            appId: req.body.appId,
        };
        const url = config.get("authUrl") + "/user/shareAccessSettings";
        const response = await axios.post(url, body, { headers: headers });
        return response.data;
    } catch (error) {
        console.log(error);
    }
};

const getSearchClientIdTypeUsingUid = function (uid, callback) {
  const sql = 'SELECT id, search_client_type_id as type, language FROM `search_clients` WHERE `uid`=?';
  connection.query(sql, [uid], function (err, data) {
    if (err || !data.length) {
      callback(err, "");
    }
    else {
      callback(null, data[0]);
    }
  })
};

const findUidAndSendSCSettings = (contentSourcesId,req, cb) => {
  const queryObjectId = 'SELECT id from content_source_objects WHERE content_source_id =?'
  connection[req.headers['tenant-id']].execute.query(queryObjectId, [contentSourcesId], (err, response) => {
      if(!response || !response[0]){
          return cb();
      }
      const objectId = response[0].id;
      const query = 'SELECT uid,search_client_id FROM search_clients sc INNER JOIN search_clients_to_content_objects sctco ON sc.id = sctco.search_client_id WHERE content_source_object_id = ?'
      connection[req.headers['tenant-id']].execute.query(query, [objectId], (err, docs) => {
          cb();
          let uids = docs;
          let asyncTask = [];
          uids.forEach(doc => {
              asyncTask.push(sendDataToKafka.bind(null, doc.uid, doc.search_client_id,req));
          });
          async.series(asyncTask, (err, data) => {
              console.log("done");
          })
      })
  })
};

const getCSAuthId = function (csId, req, callback) {
  const sql = 'SELECT id from content_source_authorization csa where content_source_id = ?';
  connection[req.headers['tenant-id']].execute.query(sql, [csId], function (err, data) {
    if (err || !data.length) {
      callback(null);
    }
    else {
      callback(null, data[0] && data[0].id);
    }
  })
};

function cookiesToString(cookiesArray) {
  return cookiesArray.map(cookie => {
    const parts = [`${cookie.name}=${cookie.value}`]; // Add name=value pair

    if (cookie.maxAge) {
      parts.push(`Max-Age=${cookie.maxAge}`);
    }

    if (cookie.expires) {
      parts.push(`Expires=${cookie.expires.toUTCString()}`);
    }

    if (cookie.path) {
      parts.push(`Path=${cookie.path}`);
    }

    if (cookie.httpOnly) {
      parts.push('HttpOnly');
    }

    if (cookie.secure) {
      parts.push('Secure');
    }
    if (cookie.sameSite) {
      parts.push(`sameSite=${cookie.sameSite}`);
    }

    if (cookie.sameSite && config.get("instanceType") != 'production') {
      parts.push(`SameSite=${cookie.sameSite}`);
    }

    return parts.join('; '); // Combine all parts for this cookie
  }); // Combine multiple cookies into one string if necessary
}

/**
 * @funciton getSameSiteCookie
 * - gets sameSite cookie based on confuguration and strict list
 * @param {void}
 * @returns {string} - returns 'Strict' or 'None'
 */
const getSameSiteCookie = () => {
  const strictSameSiteAdminUrls = [
    'https://vapt.searchunify.com',
    'https://pen.searchunify.com'
  ]
  // return config.get("instanceType") === 'production' || strictSameSiteAdminUrls.includes(config.get("adminURL")) ? 'Strict' : 'None';
  return strictSameSiteAdminUrls.includes(config.get("adminURL")) ? 'Strict' : 'None';
}

/**
 * setSecureHttpOnlyFlagsForCookies -  sets secure & httpOnly flags for sensitive cookies
 * @param {*} req - express Request
 * @param {*} res - express Response
 * @param {*} next - express NextFunction
 */
const setSecureHttpOnlyFlagsForCookies = (req, res, next) => {
  const sensitiveCookies = ['connect.admin', 'connect.admin_sid', '_csrf'];

  // Store a reference to the original res.setHeader function
  const originalSetHeader = res.setHeader;

  // Overwrite the res.setHeader function to modify the Set-Cookie header
  res.setHeader = function (name, value) {
    if (name.toLowerCase() === 'set-cookie') {
      // Split the Set-Cookie header into individual cookies
      const splitCookie = setCookie.splitCookiesString(value);
      const cookies = setCookie.parse(splitCookie);
      const modifiedCookies = cookies.map(cookie => {
        const cookieName = cookie.name.trim(); // Get the cookie name

        if (sensitiveCookies.includes(cookieName)) {
          cookie.secure = true;
          cookie.httpOnly = true;
          cookie.sameSite = getSameSiteCookie();
          cookie.maxAge = 60 * config.get("loginSessionTime");
          let d = new Date();
          d.setMinutes(d.getMinutes() + config.get("loginSessionTime"));
          cookie.expires = d;
        }

        return cookie; // Return unmodified cookie
      });
      // Join the modified cookies back into a single string
      value = cookiesToString(modifiedCookies);
    }
    // Call the original res.setHeader function with the modified value
    originalSetHeader.call(res, name, value);
  };
  next();
};

const fetchIndexingConfig = ({ tenantId, RETRY_COUNT=30, projection }, cb) => {
  let reqBody = {
    "tenantId" : tenantId,
    projection
  };
  let headers = {
    "Content-Type": "application/json",
    "index-service-secret": config.get("indexService.sharedSecret"),
  }
  httpRequest('GET', config.get("indexService.url") + '/index-service/is-config/getIndexingConfiguration', {}, reqBody, headers, function (err, res) {
    if (!err) {
      errorlogger.info('Received indexingConfig in getIndexingConfiguration: ', res);
      return cb(res);
    } else {
      errorlogger.error('Error while fetching indexing config', JSON.stringify(err));
      errorlogger.info('Retrying in 30 seconds');
      RETRY_COUNT--;

      if(RETRY_COUNT != 0) setTimeout(() => fetchIndexingConfig({ tenantId, RETRY_COUNT }) , 60000);
      else {
        errorlogger.error("retry limit exhausted for fetching indexing config")
        return cb({isError:true});
      };
    }
  });
}

async function fetchRateLimits(){
  errorlogger.info(">>>>fetchRateLimits");
  const rateLimitInfo = await getTenantsRateLimitInfo().then((result) => {
      let tenantObj = {};
      for (i in result.data)
          if (Object.keys(result.data[i]).length)
              tenantObj[i] = result.data[i];
      return tenantObj;
  }).catch(err=>{
      errorlogger.error("Error While Fetching Rate Limit Information for tenants:",err)
  });
  errorlogger.info("Tenant rateLimits Info: ",rateLimitInfo);
  RateLimitInfoStorage.set("BURST",rateLimitInfo);
  const tenantIds = Object.keys(rateLimitInfo); 
  const tenantsApiQuotaLimitInfo  = await fetchApiLimitInfoForAllTenants(tenantIds);
  errorlogger.info("tenantsApiQuotaLimitInfo", JSON.stringify(tenantsApiQuotaLimitInfo));
  RateLimitInfoStorage.set("QUOTA",tenantsApiQuotaLimitInfo);
  errorlogger.info("fetchRateLimits");
}

function fetchAllTenantsApiLogs(query,tenantIds) {
 const promises = tenantIds.map(tenantId => 
       fetchApiLogsPromise(query, tenantId)
  );

  return Promise.all(promises)
      .then(results => results).catch((err)=>{
    errorlogger.debug("Could not fetch monthlly consumption, Using Default consumption;",err);
    return tenantIds.map(tenantId => ( {tenantId,consumption:0} ));
  })
}

function fetchApiLogsPromise(option, tenantId) {
  return new Promise((resolve, reject) => {
      let bodyValue = {
          offset: option.offset || 1,
          limit: option.count || 10,
          sort: "desc",
          tenantId
      };

      if (option.object) {
          bodyValue.object = option.object;
      }

      if (option.sortType) {
          bodyValue.sortType = option.sortType;
      }

      if (option.timeFormat) {
          bodyValue.timeFormat = JSON.stringify(option.timeFormat);
      }

      if (option.startDate && option.endDate) {
          bodyValue.from = option.startDate;
          bodyValue.to = option.endDate;
      }

      let options = {
          method: 'POST',
          rejectUnauthorized: false,
          headers: {
            'Content-Type': 'application/json'
          },
          url: config.get('analyticsService.url') + '/api-logs/fetchApiLogs',
          body: bodyValue,
          json: true
      };

      request(options, (error, response,body) => {
          if (error) {
            reject(error);
          } else {
            let consumption = 0;
            body.data.result.forEach((x) => {
                  consumption += parseInt(x.consumedrate)
          });
          resolve({tenantId,consumption});
        }
      });
  });
}

function createParamsWithDates(params) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const today = now.toISOString().slice(0, 19).replace("T", " ");

  params.startDate = startOfMonth.toISOString().slice(0, 19).replace("T", " ");
  params.endDate = today;

  return params;
}
async function updateMonthlyConsumption({ tenantId,consumption }){
  let key = `${tenantId}_gpt_quota_remaining`;
  await redisclient.hincrbyAsync(key, 'monthly.tokens', consumption*-1);
}
async function fetchConsumedMonthlyLimitsForAllTenants(){
  errorlogger.debug(">>>>>>>>fetchConsumedMonthlyLimitsForAllTenants");
    const rateLimitInfo = await getTenantsRateLimitInfo().then((result) => {
      let tenantObj = {};
      for (i in result.data)
          if (Object.keys(result.data[i]).length)
              tenantObj[i] = result.data[i];
      return tenantObj;
  }).catch(err=>{
      errorlogger.error("Error While Fetching Rate Limit Information for tenants:",err)
  });
  errorlogger.info("Tenant rateLimits Info: ",rateLimitInfo);
  const tenantIds = Object.keys(rateLimitInfo); 
  const query = createParamsWithDates({
    offset: 1,
    object: "SearchUnifyGPT",
    sortType: "",
    timeFormat: { minuteWise: true, hourly: true, dateWise: true, monthWise: false }
});
  let consumptionData;
  await fetchAllTenantsApiLogs(query,tenantIds).then(info => {;
    consumptionData = info ; // Output of all tenants' API logs
  });
  
  consumptionData.map((obj)=>{
    updateMonthlyConsumption(obj)
  });
  errorlogger.debug("fetchConsumedMonthlyLimitsForAllTenants",consumptionData);
  return consumptionData;
}

const isValidAndSecureUrl = function (url) {
  const urlRegex = /^(?:(?:https|ftps|sftp):\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:[/?#]\S*)?$/i; 
  return urlRegex.test(url);
}
const accessCheck = async (req, res, next) => {
  const { email, roleHierarchy } = req.headers.session;
  let emailDomain = email && email.split("@").reverse()[0];
	if ((!suDomains.includes(emailDomain)) ||
      (suDomains.includes(emailDomain) && roleHierarchy == constants.USER_ROLES.MODERATOR)) {
    return next({
      statusCode: 402,
      message: 'Unauthorized Access'
    })
  };

  return next();
};
const getContentSourceAuthorizationData = async (contentSourceId, tenantId, callback) => {
  const sql = 'SELECT * FROM `content_source_authorization` WHERE content_source_id=?';
  connection[tenantId].execute.query(sql, [contentSourceId], function (err, data) {
    if (err || !data.length) {
      callback(null);
    }
    else {
      const authData = data[0];
      for(const key of Object.keys(authData)) {
        if (key == "client_secret" || key == "password" || key == "htaccessPassword" || key == "accessToken" || key == "refreshToken" || key == "sessionId") {
          authData[key] = authData[key] ? aes256.decrypt(appVariables.analytics.encryptionKey, authData[key]) : null
        }
      }
      callback(null, authData);
    }
  });
}

function fetchAbTestData(tenantId, abTestId) {
  return new Promise((resolve, reject) => {
    const fetchJsonQuery = `
      SELECT JSON_OBJECT(
        'id', ab.uid_ab_test,
        'uid', ab.sc_uid,
        'abTestName', ab.name,
        'testDuration', ab.test_duration,
        'abTestStatus', ab.ab_test_status,
        'startDate', ab.start_date,
        'endDate', ab.end_date,
        'tenantId',ab.tenant_id,
        'createdAt', ab.created_at,
        'updatedAt', ab.updated_at,
        'childScs', JSON_ARRAYAGG(
          JSON_OBJECT(
            'scUid', child.uid,
            'trafficSplit', child.traffic_split
          )
        ),
        'searchClientName', sc.name
      ) AS result
      FROM ab_test AS ab
      LEFT JOIN ab_test_children AS child ON ab.uid_ab_test = child.ab_test_id
      LEFT JOIN search_clients AS sc ON sc.uid = ab.sc_uid
      WHERE ab.tenant_id = ? AND ab.uid_ab_test = ?
      GROUP BY ab.uid_ab_test;
    `;

    connection[tenantId].execute.query(fetchJsonQuery, [tenantId, abTestId], (error, resultData) => {
      if (error) {
        errorlogger.error("Error fetching AB Test data:", error);
        return reject(new Error("Failed to fetch AB Test data."));
      }
      if (resultData && resultData[0] && resultData[0].result) {
        try {
          const parsedResult = JSON.parse(resultData[0].result);
          resolve(parsedResult);
        } catch (parseError) {
          errorlogger.error("Error parsing result data:", parseError);
          reject(new Error("Failed to parse AB Test data."));
        }
      } else {
        reject(new Error("No AB Test data found."));
      }
    });
  });
}

const deleteABTestData = async (tenant_id, abTestId, useScUid = false) => {
  return new Promise((resolve, reject) => {
    errorlogger.info("Delete AB Test function triggered for tenant_id:", tenant_id, "and abTestId:", abTestId);

    const deleteSearchClientsQuery = `
      DELETE FROM search_clients 
      WHERE ab_test_parent = ?
    `;

    const deleteABTestQuery = `
      DELETE FROM ab_test 
      WHERE ${useScUid ? 'sc_uid' : 'uid_ab_test'} = ? AND tenant_id = ?
    `;

    const executeDeleteABTest = () => {
      connection[tenant_id].execute.query(deleteABTestQuery, [abTestId, tenant_id], (error, result) => {
        if (error) {
          errorlogger.error("Database operation failed (ab_test):", error);
          return reject({ status: 500, message: "Failed to delete AB Test." });
        }

        if (result.affectedRows === 0) {
          return resolve({ status: 200, message: "AB Test not found." });
        }

        errorlogger.info("AB Test deleted successfully.");
        resolve({ status: 200, message: "AB Test and associated child search clients successfully deleted." });
      });
    };

    if (useScUid) {
      connection[tenant_id].execute.query(deleteSearchClientsQuery, [abTestId], (error, result) => {
        if (error) {
          errorlogger.error("Database operation failed (search_clients):", error);
          return reject({ status: 500, message: "Failed to delete child search clients." });
        }
        if (result.affectedRows === 0) {
          errorlogger.info("No data found to delete from search_clients table for ab_test_parent:", abTestId);
        }

        errorlogger.info("Child search clients deleted successfully.");
        executeDeleteABTest();
      });
    } else {
      executeDeleteABTest();
    }
  });
};


const getAbTestStatus = (req, uid) => {
  return new Promise((resolve, reject) => {
    connection[req.headers['tenant-id']].execute.query(
      'SELECT * FROM ab_test WHERE sc_uid = ? AND ab_test_status = 1',
      [uid],
      (error, results) => {
        if (error) {
          errorlogger.error("Database error", error);
          reject({ flag: 500, error: "Database operation failed" }); 
        } else {
          resolve({ 
            flag: 200,
            data: results[0] || null 
          });
        }
      }
    );
  });
};

module.exports = {
  setSecureHttpOnlyFlagsForCookies,
  getCountIndexName: getCountIndexName,
  insertUpdateContentSource: insertUpdateContentSource,
  uploadClickBoostingScoresAfterCrawling: uploadClickBoostingScoresAfterCrawling,
  insertUpdateAuthorization: insertUpdateAuthorization,
  getContentSourceDataById: getContentSourceDataById,
  getContentSourceDataByToken: getContentSourceDataByToken,
  getUserfromAccessToken: getUserfromAccessToken,
  getContentSourceObjectsAndFieldsById: getContentSourceObjectsAndFieldsById,
  checkArray: checkArray,
  insertObject: insertObject,
  insertFields: insertFields,
  metadata: metadata,
  getObjectFieldsById: getObjectFieldsById,
  getPlacesById: getPlacesById,
  getPlacesBySortParam: getPlacesBySortParam,
  insertSpacesBoards: insertSpacesBoards,
  save_boosting_details: save_boosting_details,
  getContentSourceSpaceBoardsById: getContentSourceSpaceBoardsById,
  constants: constants,
  appVariables: appVariables,
  saveMapping: saveMapping,
  deleteMapping: deleteMapping,
  BulkUpload: BulkUpload,
  BulkUploadElasticAnalytics: BulkUploadElasticAnalytics,
  checkJSON: checkJSON,
  getMappingObject: getMappingObject,
  parsehtmltoText: parsehtmltoText,
  getMapping: getMapping,
  getAutoprovisonToken: getAutoprovisonToken,
  getIndexAndObjectNames: getIndexAndObjectNames,
  getSearchableFilterableSortableFields: getSearchableFilterableSortableFields,
  getDisplayFields: getDisplayFields,
  getDisplayMapping: getDisplayMapping,
  insertFilters: insertFilters,
  isArray: isArray,
  isObject: isObject,
  getSalesforcePermissionObjects: getSalesforcePermissionObjects,
  createMapping: createMapping,
  addUIDinFilter: addUIDinFilter,
  reIndex: reIndex,
  singleUpload: singleUpload,
  setLanguage: setLanguage,
  getMetadataFieldsInObjects: getMetadataFieldsInObjects,
  getActivePlacesBycontentSourceId: getActivePlacesBycontentSourceId,
  extensionAttachment: extensionAttachment,
  deleteDriveSpace: deleteDriveSpace,
  getCountIndexSize: getCountIndexSize,
  initializeSalesforceConnection: initializeSalesforceConnection,
  reduceObject: reduceObject,
  getSalesforcePermissionObjectsIndividually: getSalesforcePermissionObjectsIndividually,
  getFieldsApiName: getFieldsApiName,
  getContentsourceType: getContentsourceType,
  httpRequest: httpRequest,
  stripHtml: stripHtml,
  getByteLen: getByteLen,
  deleteIdFromIndex: deleteIdFromIndex,
  dateConversion: dateConversion,
  getAllContentSourcesAndFields: getAllContentSourcesAndFields,
  getAllSearchClients: getAllSearchClients,
  getcontentsourceIdsbyUid: getcontentsourceIdsbyUid,
  getKnowledgeGraphRelation: getKnowledgeGraphRelation,
  insertUpdateApiCrawlerFields: insertUpdateApiCrawlerFields,
  getApiCrawlerFieldsById: getApiCrawlerFieldsById,
  getAllContentSource: getAllContentSource,
  getSupportUrl: getSupportUrl,
  getSearchClientIdUsingUid: getSearchClientIdUsingUid,
  getUidAndSupportUrl: getUidAndSupportUrl,
  getAddonsStatus: getAddonsStatus,
  clearConfigCache: clearConfigCache,
  getNavigationalTopics:getNavigationalTopics,
  getCommunityNetworkNames:getCommunityNetworkNames,
  htmlvalidate:htmlvalidate,
  getObjectFieldNames: getObjectFieldNames,
  modelFields: modelFields,
  deleteData: deleteData,
  updateObjectMapping: updateObjectMapping,
  updateObjectPidAndStatus: updateObjectPidAndStatus,
  getRecordTypeFromSalesforce:getRecordTypeFromSalesforce,
  getDisplayFieldsInSearch: getDisplayFieldsInSearch,
  getPriorityFieldsInFilter: getPriorityFieldsInFilter,
  updateObjectPidAndStatus: updateObjectPidAndStatus,
  errorlogger:errorlogger,
  crawlerlogger:crawlerlogger,
  sendCrawlingStatusEmail: sendCrawlingStatusEmail,
  registerEmailJwt: registerEmailJwt,
  getAccessToken:getAccessToken,
  save_boosting_details2:save_boosting_details2,
  redisCache:redisCache,
  CleanRedisCache:CleanRedisCache,
  getMappingString:getMappingString,
  getSplitIndexes: getSplitIndexes,
  unblockUser: unblockUser,
  CleanSearchRedisCache: CleanSearchRedisCache,
  checkIfCSUsedInSearchClients: checkIfCSUsedInSearchClients,
  deleteByQuery: deleteByQuery,
  getContentSourceFields: getContentSourceFields,
  getFieldData: getFieldData,
  getScrollData: getScrollData,
  deleteByQuery:deleteByQuery,
  fetchClientInfo,
  executeQuery,
  getAllIndexCount,
  encryptDecryptCreds,
  getSearchClientIdTypeUsingUid,
  getRandomClusterNode,
  usersForAccessSettings: usersForAccessSettings,
  findUidAndSendSCSettings,
  fetchApiLimitInfo: fetchApiLimitInfo,
  getCSAuthId: getCSAuthId,
  fetchIndexingConfig,
  resetCsFrequencyToNever,
  getGPTRules,
  getGptRulesFromCache,
  fetchApiLimitInfoPromisified,
  fetchApiLimitInfoForAllTenants,
  deleteGptRulesFromCache,
  fetchRateLimits,
  getSameSiteCookie,
  fetchApiLogsPromise,
  createParamsWithDates,
  fetchConsumedMonthlyLimitsForAllTenants,
  isValidAndSecureUrl,
  urlValidityCheck,
  getPreviewFieldsInObjects,
  accessCheck,
  fetchAbTestData,
  deleteABTestData,
  getAbTestStatus,
  getContentSourceAuthorizationData,
  getLanguageForContentSource
}

