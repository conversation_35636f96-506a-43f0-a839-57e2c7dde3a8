const commonFunctions = require('./commonFunctions');
let crawlerCacheData = new Map();

const configsRequired = ['zoomin', 'nonCrawlerFile', 'lithium'];
const getCrawlerConfig = (tenantId, configKey, callback) => {
  let reqBody = {};
  let headers = {
    "Content-Type": "application/json",
    "su-crawler-secret" : config.get("crawler.sharedSecret")
  }
  if (tenantId) {
    headers['tenant-id'] = tenantId;
  }
  if (configKey) {
    reqBody.config_key = configKey;
  }
  commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/crawler-config/get', {}, reqBody, headers, function (error, result) {
    if (error) {
        commonFunctions.errorlogger.error("Error found: ", error);
        callback(error, null);
    }
    else
        callback(null, result)
  });
}

const updateCrawlerConfig = (updatedData, tenantId, callback) => {
  let headers = {
    "Content-Type": "application/json",
    "su-crawler-secret" :  config.get("crawler.sharedSecret"),
    "tenant-id": tenantId
  }
  commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/update', '', updatedData, headers, ( err, result) =>{
    if(!result.status){
      callback(result, null)
    } else{
      callback(null, result);
    }
  });
}

const fetchCrawlerConfig = ({ tenantId, RETRY_COUNT=30, configKey }, cb) => {
  getCrawlerConfig(tenantId, configKey, (err, res) => {
    if(!err){
      commonFunctions.errorlogger.info('Received crawlerConfig in getCrawlerConfig');
      return cb(res.data.data);
  }else {
    commonFunctions.errorlogger.error('Error while fetching crawler config', JSON.stringify(err));
    commonFunctions.errorlogger.info('Retrying in 30 seconds');
    RETRY_COUNT--;

    if(RETRY_COUNT != 0) setTimeout(() => fetchCrawlerConfig({ tenantId, RETRY_COUNT, configKey }) , 60000);
    else commonFunctions.errorlogger.error("retry limit exhausted for fetching crawlerConfig");
  }});
}

const setConfig = (configs) => {
  let crawlerConfig = configs.data;
  if (!Array.isArray(configs.data)) {
    crawlerConfig = [configs.data];
  }
  for (const key of crawlerConfig) {
    const configKey   = key.config_key;
    if (configsRequired.includes(configKey)) {
      const configValue = key.config_value;
      const defaultValue = key.default_value;
      if (configs.event === 'reset') {
        crawlerCacheData.set(configKey, defaultValue);
      } else {
          crawlerCacheData.set(configKey, configValue);
      }
    }
  }
}

const getSync = (configKey, callback) => {
  if (crawlerCacheData.has(configKey)) {
    const crawlerConfig = crawlerCacheData.get(configKey);
    callback(null, JSON.parse(crawlerConfig));
  } else {
    callback('getSync err', null)
  }
};

const getConfig = () => {
  let cache = {};
  if(!crawlerCacheData || crawlerCacheData.size === 0){
    return cache;
  }
  for (const [key,value] of crawlerCacheData){
    cache[key] = value;
  }
  return cache;
};

module.exports = {
  getSync,
  updateCrawlerConfig,
  getConfig,
  setConfig,
  fetchCrawlerConfig
}
