<html>

<head>
    <meta name="viewport" content="width=device-width, user-scalable=no" />
</head>

<body>
    <div>Login successfull</div>
    <script>
        function getParameterByName(name, url) {
            if (!url) url = window.location.href;
            name = name.replace(/[\[\]]/g, "\\$&");
            var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, " "));
        }
        localStorage.setItem("_csrf", '<%=csrf%>');
        document.cookie = "searchUnify_roleid=<%=roleId%>";
        document.cookie = "searchUnify_name=<%=name%>";
        document.cookie = "searchUnify_email=<%=email%>";
        document.cookie = "searchUnify_userId=<%=userId%>"
        window.location.href = '<%=redirect%>';
    </script>
</body>

</html>
