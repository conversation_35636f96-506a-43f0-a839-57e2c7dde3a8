<html>

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-store" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js" type="text/javascript"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/js-cookie/2.0.2/js.cookie.min.js"
        type="text/javascript"></script>
    <script src="https://connectors.tableau.com/libs/tableauwdc-2.3.latest.js" type="text/javascript"></script>
    <script type="text/javascript">
            var config = {
                redirectUri: <%- JSON.stringify(redirect_uri) %>,
                authUrl:  <%- JSON.stringify(authUrl) %>,
                searchClientIds:  <%- JSON.stringify(searchClientIds) %>
            };
    </script>
    <script src="../../resources/tableau/webDataConnectorRawApi.js" type="text/javascript"></script>
    <style>
        /* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

.modalRange {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content-EndDate {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 80%;
}

.modal-content-Range {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 80%;
}

/* The Close Button */
.close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.closeRange {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.closeRange:hover,
.closeRange:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}
    </style>
</head>

<body>
    <div style="text-align: center">
        <div>
            <div>
                <p class="signedin">PLEASE SELECT THE PERIOD TO FETCH DATA FROM RAW API INTO TABLEAU</p>
                <div class="signedin" >
                    Select search client UID: <select id="searchClientNumber">
                </select>
                </div>
                <script>
                    var select = document.getElementById("searchClientNumber");
                    var options = config.searchClientIds;
                    for (var i = 0; i < options.length; i++) {
                        var opt = options[i];
                        var el = document.createElement("option");
                        el.textContent = opt;
                        el.value = opt;
                        select.appendChild(el);
                    }
                </script>
                <div class="notsignedin">
                  <p>PLEASE LOGIN TO YOUR SEARCHUNIFY ADMIN CONSOLE</p>
                  <p><label>Client Id  </label><input type="test" id="clientId" style="width: 28%; margin: 0 auto;"></p>
                </div>
                <button type="button" id="connectbutton" style="padding: 5px;width: 95px;border-radius: 2px;background-color: ffff;background-color: #ffff;border-color: #e2e2e2;">Login</button>
                <input type="date" id="start" value="2022-04-01" style="width: 25%; margin: 0 auto;">
                <input type="date" id="end" value="2022-05-01" style="width: 25%">
                <!-- The Modal -->
                <div id="myModal" class="modal">

                    <!-- Modal content -->
                        <div class="modal-content-EndDate">
                            <span class="close">&times;</span>
                            <p>Start Date is greater then End Date</p>
                        </div>
      
                </div>
                <div id="myModalRange" class="modalRange">
                    <div class="modal-content-Range">
                        <span class="closeRange">&times;</span>
                        <p>Start Date and End Date should not be older than 1 year from current Date</p>
                    </div>
                </div>
                <button type="button" id="fetchSuAnalytics" onclick="modalLogic()" style="margin: 10px; padding: 5px;width: 95px;border-radius: 2px;background-color: ffff;background-color: #ffff;border-color: #e2e2e2;">Get Data</button>
            </div>
        </div>
    </div>
</body>
</html>