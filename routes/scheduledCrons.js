const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const config = require('config');
const kafkaStatusLib = require("../utils/kafka-status-page/kafka-lib");


router.get('/publishtokentostatuspage', function(req, res, next){
  console.log(">>>>>>>>publishtokentostatuspage");
  const adminSecret = req.headers['admin-secret'];
  if(adminSecret !== config.get('adminSecretCode')) {
    return res.sendStatus(401);
  }

  let jwtPayload = {
    domain: config.get('adminURL'),
    timestamp: new Date().getTime(),
  };

  const jwtOptions = {
    algorithm: 'HS256',
    expiresIn: config.get('statusPageTokenOptions.expiry'),
  };

  const token = jwt.sign(jwtPayload, config.get('statusPageTokenOptions.key'), jwtOptions);

  kafkaStatusLib.publishMessage({
    topic: config.get("kafkaTopic.domainConfigurations"),
    messages:[{
      value:JSON.stringify({ token, domain: jwtPayload.domain }),
    }]
  })
  console.log("publishtokentostatuspage", token);
  res.sendStatus(200);
});

module.exports = router;
