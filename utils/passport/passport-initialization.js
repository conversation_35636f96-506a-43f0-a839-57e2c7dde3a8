const passport = require('passport');
const SamlStrategy = require('passport-saml').Strategy;
const fs = require('fs');
const { IDP_TYPES } = require('./../../constants/constants');
const md5 = require('md5');

const passportInitializationForAllTenants = async (tenant) => {
    return new Promise ((resolve, reject) => {
        const { tenant_id, tenant_hash, subdomain } = tenant;
        const sql = `SELECT * from saml_auth where idp_type = ?`;
        connection[tenant_id].execute.query(sql, [IDP_TYPES.ADMIN_SSO], (error, ssoData) => {
            if (error) {
                console.log('-----------error-----------', error);
                reject(error);
            } else {
                if (ssoData && !ssoData.length) {
                    console.log('-----------Admin SSO data not found-----------');
                    return resolve('SSO data not found');
                } else {
                    const ssoInfo = ssoData[0];
                    if (ssoInfo.isActivated) {
                        const strategy = new SamlStrategy({
                            entryPoint: ssoInfo.saml_sso_url,
                            callbackUrl: `https://${subdomain}` + `/saml/auth/${tenant_hash}`,
                            signatureAlgorithm:'sha256',
                            digestAlgorithm:'sha256',
                            issuer: `https://${subdomain}`,
                            identifierFormat: null,
                            decryptionPvk: fs.readFileSync(__dirname + '/../../cert/key.pem', 'utf8'),
                            privateCert: fs.readFileSync(__dirname + '/../../cert/key.pem', 'utf8'),
                            cert: ssoInfo.certificate,
                            validateInResponseTo: false,
                            acceptedClockSkewMs: -1,
                            disableRequestedAuthnContext: true,
                            logoutUrl: ssoInfo.saml_logout_url,
                            logoutCallbackUrl: `https://${subdomain}/saml/logout`
                        }, function (profile, done) {
                            done(null, profile);
                        });
                        passport.use(tenant_hash, strategy);
                        return resolve();
                    } else {
                        return resolve('SSO deactivated');
                    }
                }
            }
        });
    });
};

const passportInitializationForAllTenantsAndSCUids = async (tenant) => {
    return new Promise((resolve, reject) => {
        const { tenant_id, tenant_hash } = tenant;
        const sql = `SELECT * FROM search_client_sso_config`;
        connection[tenant_id].execute.query(sql, (error, ssoData) => {
            if (error) {
                console.log('-----------error-----------', error);
                reject(error);
            } else {
                if (ssoData && !ssoData.length) {
                    console.log('-----------Search-Client SSO data not found-----------');
                    return resolve('SSO data not found');
                } else {
                    // Use Promise.all to ensure all strategies are set up before resolving
                    Promise.all(ssoData.map((ssoDataItem) => {
                        return new Promise((resolveStrategy) => {
                            let ssoInfo = ssoDataItem.sso_config ? JSON.parse(ssoDataItem.sso_config) : {};
                            if(ssoInfo.certificate !== '' && ssoInfo.saml_sso_url !== '') {
                                const strategy = new SamlStrategy({
                                    entryPoint: ssoInfo.saml_sso_url,
                                    callbackUrl: `${config.get("adminURL")}/saml/auth/${tenant_hash}`,
                                    signatureAlgorithm: 'sha256',
                                    digestAlgorithm: 'sha256',
                                    issuer: ssoDataItem.sc_uid,
                                    identifierFormat: null,
                                    decryptionPvk: fs.readFileSync(__dirname + '/../../cert/key.pem', 'utf8'),
                                    privateCert: fs.readFileSync(__dirname + '/../../cert/key.pem', 'utf8'),
                                    cert: ssoInfo.certificate,
                                    validateInResponseTo: false,
                                    acceptedClockSkewMs: -1,
                                    disableRequestedAuthnContext: true,
                                    logoutUrl: ssoInfo.saml_logout_url,
                                    logoutCallbackUrl: `${config.get("adminURL")}/saml/logout`
                                }, function (profile, done) {
                                    done(null, profile);
                                });
                                passport.use(md5(tenant_hash + ssoDataItem.sc_uid), strategy);
                                resolveStrategy(); // Resolve the promise for this strategy
                            } else {
                                resolveStrategy();
                            }
                        });
                    })).then(() => {
                        resolve(); // Resolve the main promise after all strategies are set up
                    }).catch(reject);
                }
            }
        });
    });
};

exports.passportInitializationForAllTenants = passportInitializationForAllTenants;
exports.passportInitializationForAllTenantsAndSCUids = passportInitializationForAllTenantsAndSCUids;