var express = require("express");
var router = express.Router();
const config = require("config");
var request = require("request");
const commonFunctions = require("../../utils/commonFunctions");

router.post("/chat", async (req, res) => {
    try {
        const { uid, perPage = 5 } = req.body;
        const { accessToken } = req.headers.session;
        const { message } = req.body;
        console.log("Received message:", message);

        var options = {
            method: "POST",
            rejectUnauthorized: false,
            url: config.get("searchService.url") + "/search/searchResultByPost",
            headers: {
                "content-type": "application/json",
            },
            body: {
                searchString: message,
                from: 0,
                sortby: "_score",
                orderBy: "desc",
                resultsPerPage: perPage,
                accessToken: accessToken,
                uid: uid,
            },
            json: true,
        };
        
        request(options, function (err, response, docs) {
            if (err) {
                commonFunctions.errorlogger.error(
                    "Error from search service:",
                    err
                );
                return next(err); // Pass error to Express error handler
            }
            if (response.statusCode !== 200) {
                commonFunctions.errorlogger.error(
                    "Search service responded with status:",
                    response.statusCode,
                    docs
                );
                return res
                    .status(response.statusCode)
                    .json({ error: "Search service error", details: docs });
            }

            const hits = (docs && docs.result && docs.result.hits) || [];

            const formattedHits = hits.map((hit) => ({
                title:
                    (hit.highlight &&
                        hit.highlight.TitleToDisplay &&
                        hit.highlight.TitleToDisplay[0]) ||
                    "No Title",
                summary:
                    (hit.highlight &&
                        hit.highlight.SummaryToDisplay &&
                        hit.highlight.SummaryToDisplay.join(" ")) ||
                    "No Summary",
                link: hit.href || "#",
            }));

            const reply =
                formattedHits.length > 0
                    ? formattedHits
                          .map(
                              (item, index) =>
                                  `${index + 1}. *${item.title}*\n${
                                      item.summary
                                  }\n🔗 ${item.link}`
                          )
                          .join("\n\n")
                    : "No results found.";

            return res.json({ response: reply });
        });
    } catch (error) {
        commonFunctions.errorlogger.error("API Error in /chat:", error);
        return res.status(500).json({ error: "Internal server error" });
    }
});

module.exports = {
    router: router
}