
const async = require('async');
const commonFunctions = require('../../../utils/commonFunctions');
const fetchFeedSettings = require('../salesforceUtilties/feedPost');
const communityBotProducer = require("./botAnalyticsProducer");
const communityHelperSettings = require('../communityHelperSettings/communityHelperSettings')
const kafkaLib = require("../../../utils/kafka/kafka-lib");


const insertBotAnalytics = function (allPosts, sf, req, callback) {
    let finalDataToReturn = [];
    const botData = [];
    const tenantId = req.headers['tenant-id'];
    for (var i = 0; i < allPosts.length; i++) {
        if (allPosts[i] && allPosts[i].postData) {
            botData.push(communityBotProducer.alterPostInfo(allPosts[i], sf, tenantId));
            finalDataToReturn.push(allPosts[i].postData);
        }
    }
    try{
        kafkaLib.publishMessage({
            topic:config.get("kafkaTopic.communityHelper"),
            messages:[{
                value:JSON.stringify(botData),
            }]
        })
    }catch(e){
        console.log(e);
    }
        callback(null, finalDataToReturn);
}


const processSolvedPostsLT = async (botInfo, cb) => {
    let toDate = new Date().toISOString();
    toDate = new Date(toDate).getTime();
    communityHelperSettings.getBotSettings(botInfo.botId, botInfo.tenantId, function (err, resultData) {
      var query = "SELECT * from messages WHERE is_solution=true AND post_time <=" + toDate + "AND id in(" + botInfo.feedCommentids.map((x) =>"'"+ x + "'").join(',')       + ")";
    let headers = {
        'authorization': 'Bearer ' + resultData.accessToken,
        'content-type': 'application/json'
    }
    let body = "";
    let url = resultData.url + '/api/2.0/search'
    commonFunctions.errorlogger.info(query);
    commonFunctions.httpRequest('GET', url, query, body, headers, function (error, result) {
        if (!error) {
            if (result.data && result.data.items && result.data.items.length) {
                communityBotProducer.publishHelpfulContent(result.data.items, 0, botInfo.tenantId, (kErr, kRes) => {
                    cb(null, result);
                });
            }
            else
                cb(error, null);
        }
        else
            cb("Unauthorised user", null);
    });
})
};


const processSolvedPostsSF = (botInfo, callback) => {
    let { botId, feedCommentids, tenantId } = botInfo;
    async.auto({
        initializeConnection: (cb) => {
            if (botId)
                fetchFeedSettings.initializeSFConnection(botId, tenantId, cb);
            else
                cb([], null);
        },
        getConnectedAgent: ["initializeConnection", (dataFromAbove, cb) => {
            let connSF = dataFromAbove.initializeConnection.initializeConnection;
            connSF.identity((error, result) => {
                if (result) cb(null, result);
                else {
                    commonFunctions.errorlogger.info('error in connection', error);
                    cb(null, null)
                };
            });
        }],
        fetchKudos: ["initializeConnection", "getConnectedAgent", (dataFromAbove, cb) => {
            let connSF = dataFromAbove.initializeConnection.initializeConnection;
            let userId = dataFromAbove.getConnectedAgent ? dataFromAbove.getConnectedAgent.user_id : '';
            if (connSF && userId && feedCommentids.length) {
                let toDate = new Date().toISOString();
                let soql = "SELECT BestCommentId, LastModifiedDate FROM FeedItem WHERE BestCommentId!='' AND CreatedDate < " + toDate + " AND BestCommentId IN (SELECT ID FROM FeedComment where CreatedBy.Id='" + userId + "' AND ID IN (" + feedCommentids.map(x => "'" + x + "'").join(',') + "))";
                connSF.query(soql, function (err, result) {
                    if (result && result.records && result.records.length) {
                        commonFunctions.errorlogger.info("result of sql", JSON.stringify(result));
                        communityBotProducer.publishHelpfulContent(result.records, 1, tenantId,(kErr, kRes) => {
                            cb(null, result);
                        });
                    } else {
                        commonFunctions.errorlogger.info("err of soql", err)
                        cb(err, '');
                    }
                });
            } else {
                commonFunctions.errorlogger.info('user in posts', userId);
                commonFunctions.errorlogger.info('ids in posts', feedCommentids);
                cb([], null);
            }

        }]
    }, function (error, result) {
        callback(null, result)
    })
}


const preProcessFeedPosts = (allPosts) => {
    let taskArr = [];
    for (let i = 0; i < allPosts.length; i++) {
        if (allPosts[i].platform_id === '3')
            taskArr.push(processSolvedPostsSF.bind(null, allPosts[i]));
        if (allPosts[i].platform_id === '2')
            taskArr.push(processSolvedPostsLT.bind(null, allPosts[i]));
    }
    async.parallel(taskArr, function (err, data) {
        if (err) {
            commonFunctions.errorlogger.info('error in updating solved posts', err);
        } else {
            commonFunctions.errorlogger.info('updated solved posts', data);
        }
    });
}



module.exports = {
    insertBotAnalytics,
    preProcessFeedPosts
}
