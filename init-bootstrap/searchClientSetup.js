const path = require('path');
environment = require('../routes/environment');
process.env.NODE_ENV = environment.configuration;
const configPath = path.join(__dirname, '../config');
process.env.NODE_CONFIG_DIR = configPath;
const { getTenantInfoFromTenantId } = require('auth-middleware');
const { tenantSqlConnection  } = require('../auth/sqlConnection');
const connection_sql = require('../utils/connection');

config = require('config');
var async = require('async');

process.on("uncaughtException", (err) => {
    console.error("Unhandled Exception ", JSON.stringify(err));
    process.exit();
});

const searchResultKafkaConfig = require('../routes/admin/searchResultKafkaConfig');

const updateSearchClient = async (cb) => {
    const tenantId = process.argv[2];
    const tenantInfo = await getTenantInfoFromTenantId(process.argv[2]);
    await tenantSqlConnection(tenantInfo[0].tenant_id, tenantInfo[0].database_name);
    const getSearchClient = `SELECT id from search_clients;`
    let q = connection[tenantId].execute.query(getSearchClient, (err, result) => {
        console.log(err, result)
        if (err) cb(err, null);
        if (result && result.length) {
            let taskArray = [];
            result.forEach(x => {
                let configObj = { "platformId": x.id };
                taskArray.push(searchResultKafkaConfig.getSearchClientSettingsViaKafka.bind(null,configObj,{ headers: {'tenant-id': tenantId} }));
            });
            async.series(taskArray, (err, rows) => {
                cb(err, 'Completed');
            })
        }
    })
}

const clientUpdate = {
    get clientUpdate() {
        connection_sql.handleDisconnect().then(function (result) {
            // This function need's to be discussed to check about SQL Connection.
                    // SQL ISSUE
            updateSearchClient((err, result) => {
                if(result){
                    console.log('setup finished');
                } else console.log("Error: ",err);
                process.exit();
            });
        })
    }
}

clientUpdate.clientUpdate;