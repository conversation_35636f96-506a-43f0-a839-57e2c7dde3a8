
module.exports = {
    "objects": [{
        "name": "activity",
        "label": "Activity",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [
        {
            "name": "id",
            "label": "id",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 0,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "name",
            "label": "Activity Name",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 0,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "description",
            "label": "Description",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 0,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "primaryAttributeValue",
            "label": "Primary Attribute Value",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 0,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "marketoGUID",
            "label": "Marketo GUID",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 0,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "attributeValue",
            "label": "Attribute Value",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 0,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "attributeName",
            "label": "Attribute Name",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 0,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },     
        {
            "name": "view_href",
            "label": "View Href",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 0,
            "isSearchable": 0, //analyzer
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "post_time",
            "label": "Created Date",
            "type": "datetime",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1,
            "isMasked": 0
        }
    ]

}
