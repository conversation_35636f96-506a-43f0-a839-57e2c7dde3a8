module.exports = {
  "objects": [
    {
      "name": "discussions",
      "label": "Discussions",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "url",
          "label": "URL",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "id",
          "label": "Discussion Id",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "title",
          "label": "Discussion Title",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "count_comments",
          "label": "Comment Count",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "dateInserted",
          "label": "Created At",
          "type": "datetime",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 0,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "category_id",
          "label": "Category Id",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "views",
          "label": "Views Count",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "description",
          "label": "Description",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "updatedAt",
          "label": "Updated At",
          "type": "datetime",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 0,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "authorName",
          "label": "Author",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "authorId",
          "label": "Author Id",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 0,
          "isActive": 1
        },
        {
          "name": "category",
          "label": "Category",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "authorLabel",
          "label": "Author Label",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "discussionType",
          "label": "Discussion Type",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "discussionStatus",
          "label": "Discussion Status",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "breadcrumbs",
          "label": "Breadcrumbs",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "tags",
          "label": "Tags",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "comments",
          "label": "Comments",
          "type": 'string',
          "isFilterable": 0,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "roles",
          "label": "Roles",
          "type": "string",
          "isFilterable": 0,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        }
      ]
    },
    {
      "name": "articles",
      "label": "Articles",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "articleId",
          "label": "Article Id",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "category",
          "label": "Category",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "title",
          "label": "Title",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "body",
          "label": "Body",
          "type": "string",
          "isFilterable": 0,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "url",
          "label": "URL",
          "type": "string",
          "isFilterable": 0,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "dateInserted",
          "label": "Inserted Date",
          "type": "datetime",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "updatedAt",
          "label": "Updated Date",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "authorName",
          "label": "Author",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "authorLabel",
          "label": "Author Label",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "updatedUser",
          "label": "Update User",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": 'articleStatus',
          "label": 'Article Status',
          "type": 'string',
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "breadcrumbs",
          "label": "Breadcrumbs",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": 'roles',
          "label": 'Roles',
          "type": 'string',
          "isFilterable": 0,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        },
         {
          "name": 'locale',
          "label": 'Language',
          "type": 'string',
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1 
        },
        {
          "name": 'knowledgeBase',
          "label": 'knowledge Base',
          "type": 'string',
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1 
        },
        {
          "name": 'knowledgeCategoryName',
          "label": 'knowledge Category Name',
          "type": 'string',
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1 
        }
      ]
    },
    {
      "name": "events",
      "label": "Events",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "eventId",
          "label": "Event Id",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "name",
          "label": "Name",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "description",
          "label": "Description",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "url",
          "label": "URL",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "post_time",
          "label": "Created Date",
          "type": "datetime",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 0,
          "isActive": 1,
          "isMerged":1
        },
        {
          "name": "location",
          "label": "Location",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "locationUrl",
          "label": "Location Url",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "breadcrumbs",
          "label": "Breadcrumbs",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "category_id",
          "label": "Category Id",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "category_name",
          "label": "Category Name",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "group_name",
          "label": "Group Name",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        }
      ]
    },
    {
      "name": "groups",
      "label": "Groups",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "groupId",
          "label": "Group Id",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        }, 
        {
          "name": "name",
          "label": "Name",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "description",
          "label": "Description",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
         {
          "name": "members",
          "label": "Members",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "post_time",
          "label": "Created Date",
          "type": "datetime",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 0,
          "isActive": 1,
          "isMerged":1
        },
         {
          "name": "updatedAt",
          "label": "Updated At",
          "type": "datetime",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 0,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "breadcrumbs",
          "label": "Breadcrumbs",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "privacy",
          "label": "Privacy",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "countMembers",
          "label": "Count Members",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "countDiscussions",
          "label": "Count Discussions",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "moderator",
          "label": "Moderator",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "url",
          "label": "URL",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 0,
          "isSearchable": 1,
          "isActive": 1
        }
      ]
    }
  ],
  
}
