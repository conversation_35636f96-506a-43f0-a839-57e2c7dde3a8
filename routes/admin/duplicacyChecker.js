var path = require('path');
environment = require('./../environment');
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, './../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
var express = require('express');
var router = express.Router();
var request = require('request');
var async = require('async');
const searchunifyEmail = require('../../Lib/email');
const emailTemplates = require('./../emailTemplates');
var commonFunctions = require('./../../utils/commonFunctions');


router.get('/getObject', function (req, res, next) {
    commonFunctions.getContentSourceObjectsAndFieldsById(req.query.content_source_id,req, function (err, objects) {
        res.send({ "objects": objects });
    });
});

// Getting duplicate data field names  
router.get("/getDuplicateDataNames", function (req, res, next) {
    getAddedDuplicacyContent(req,function (err, result) {
        res.send(result);
    })
});

const getAddedDuplicacyContent = function (req,cb) {
    var query = `SELECT * FROM duplicacy_checker`;
    var sql = connection[req.headers['tenant-id']].execute.query(query, function (err, docs) {
        if (err) {
            commonFunctions.errorlogger.error(err);
            cb(err, []);
        } else {
            cb(null, docs);
        }
    });
}

router.get("/deleteDuplicateContent", function (req, res, next) {
    var query = `DELETE FROM duplicacy_checker WHERE id = ?`;
    var sql = connection[req.headers['tenant-id']].execute.query(query,[req.query.id],function (err, docs) {
        if (err) {
            res.send(err);
        } else {
            getAddedDuplicacyContent(req,function (err, result) {
                res.send(result);
            })
        }
    });
});

const sendDuplicateQueryToDatabase = function (data, req,cb) {
    var createDuplicacyTable = `CREATE TABLE IF NOT EXISTS duplicacy_checker (id INT(20) AUTO_INCREMENT PRIMARY KEY, 
    base_id VARCHAR(30) NOT NULL, compared_id VARCHAR(30) NOT NULL, base_type VARCHAR(100) NOT NULL,
    baseElasticName VARCHAR(100) NOT NULL, compare_type VARCHAR(100) NOT NULL, compareElasticName VARCHAR(100) NOT NULL,
    base_label VARCHAR(100) NOT NULL,compared_label VARCHAR(100) NOT NULL,percentage VARCHAR(100) NOT NULL)`;
    var q = connection[req.headers['tenant-id']].execute.query(createDuplicacyTable, function (err, tableC) {
        if (err) {
            commonFunctions.errorlogger.error(err);
            cb(err, null);
        } else {
            var qu = `SELECT base_type,baseElasticName,compare_type,compareElasticName,percentage FROM duplicacy_checker WHERE base_type="${data.base_type}" AND baseElasticName="${data.baseElasticName}" AND compare_type="${data.compare_type}" AND compareElasticName="${data.compareElasticName}" AND percentage="${data.percentage}"`;
            var Aa = connection[req.headers['tenant-id']].execute.query(qu, function (err1, docs) {
                if (err1) {
                    commonFunctions.errorlogger.error(err1)
                    cb(err1, null);
                } else {
                    if (docs.length != 1) {
                        var a = `INSERT INTO duplicacy_checker (base_id,compared_id,base_type,baseElasticName,compare_type,compareElasticName,base_label,compared_label,percentage) VALUES ("${data.base_id}","${data.compare_id}","${data.base_type}","${data.baseElasticName}","${data.compare_type}","${data.compareElasticName}","${data.baseLabel}","${data.compareLabel}","${data.percentage}")`;
                        var sql = connection[req.headers['tenant-id']].execute.query(a, function (err2, response) {
                            if (err2) {
                                cb(err2, null);
                            } else {
                                cb(null, response.message);
                            }
                        });
                    } else {
                        cb(null, []);
                    }
                }
            });
        }
    })
}

router.get('/getDuplicateContent', function (req, res, next) {

    async.auto({
        save_query_parameter: function (cb) {
            sendDuplicateQueryToDatabase(req.query, req,function (err, docs) {
                if (err) {
                    cb(err, []);
                } else {
                    cb(null, []);
                }
            });
        },
        get_elastic_data: ['save_query_parameter', function (above_res, cb) {
            //send notifications 
            res.send({ "objects": "mail_notification", "status": "ok" });
            getDatafromElastic(0, req.query, "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + '/' + req.query.baseElasticName + '/' + req.query.base_type + '/_search?scroll=10h', undefined, function (err, result) {
                //send email to customer
                sendNotification(req.query.email, function (err1, result1) {
                    commonFunctions.errorlogger.warn("All data compared");
                })
            });
        }]
    }, function (err, result) {
        commonFunctions.errorlogger.warn("Done");
    });
});

router.get("/getExistingIndex", function (req, res, next) {
    getDuplicateContent(req.query, function (err, result) {
        if (result && result.length > 0) {
            var finalArr = [];
            for (var i = 0; i < result.length; i++) {
                if (result[i].length > 0) {
                    for (var j = 0; j < result[i].length; j++) {
                        if (j != 0) {
                            result[i][j]["parent"] = "";
                        }

                        finalArr.push(result[i][j]);
                    }
                }
            }
            res.send({ "objects": finalArr, "status": "Duplicate Data" });
        } else {
            res.send({ "objects": [], "status": "somthing wrong" });
        }
    })
});

const getDuplicateContent = function (requireData, cb) {

    var options = {
        url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + '/' + requireData.baseElasticName + '/' + requireData.base_type + '/_search',
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        },
        body: {
            "sort": [
                { "duplicacy_score.score_value": { "order": "desc" } },
            ],
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "duplicacy_score.type": requireData.compare_type
                            }
                        }
                    ]
                }
            },
            "size": 5,
            "from": requireData.page
        },
        json: true
    };
    request(options, function (error, response, body) {
        if (error) {
            cb(error, []);
        }
        else {
            if (body.status == 400 || body.status == 404) {
                cb(body.status, []);
            }
            else {
                var finalArr = [];
                for (var mo = 0; mo < body.hits.hits.length; mo++) {
                    var arr = [];
                    var parent = body.hits.hits[mo]["_source"].title;
                    var parent_url = body.hits.hits[mo]["_source"].view_href;
                    for (var ra = 0; ra < body.hits.hits[mo]["_source"].duplicacy_score.length; ra++) {
                        body.hits.hits[mo]["_source"].duplicacy_score[ra].total = body.hits.total;
                        body.hits.hits[mo]["_source"].duplicacy_score[ra].remaining_page = (body.hits.total - parseInt(requireData.page));
                        body.hits.hits[mo]["_source"].duplicacy_score[ra].parent = parent;
                        body.hits.hits[mo]["_source"].duplicacy_score[ra].parent_url = parent_url;
                        arr.push(body.hits.hits[mo]["_source"].duplicacy_score[ra]);
                    }
                    finalArr.push(arr);
                }
                cb(null, finalArr);
            }
        }
    });
}

const getDatafromElastic = function (start, front_end_details, url, scrollId, cb) {

    if (scrollId) {
        var bodyData = {
            "scroll": "10h",
            "scroll_id": scrollId
        }
    }
    else {
        var bodyData = {
            "size": 500,
            "from": start
        }
    }
    var options = {
        url: url,
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        },
        body: bodyData,
        json: true
    };
    request(options, function (error, response, body) {
        if (error) {
            cb(error, []);
        }
        else {
            var taskArr = [];
            if (!body.error) {
                scrollId = body["_scroll_id"];
                for (var i = 0; i < body.hits.hits.length; i++) {
                    if (body.hits.hits)
                        taskArr.push(checkDuplicacy.bind(null, body.hits.hits[i]["_id"], front_end_details, body.hits.hits[i]["_source"]["duplicacy_score"]));
                }
                async.series(taskArr, function (err, updated_document) {
                    if (err) {
                        commonFunctions.errorlogger.error("### ERROR DURING GETTING DUPLICATE CONTENT ###");
                        cb(err, []);
                    }
                    else {
                        commonFunctions.errorlogger.info("Total document updated: ", updated_document.length);
                        if ((start + 500) > body.hits.total) {
                            cb(null, []);
                        } else {
                            start = start + 500;
                            commonFunctions.errorlogger.info("Getting content from: ", start);
                            getDatafromElastic(start, front_end_details, "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + '/_search/scroll', scrollId, function (err_inside, result) {
                                if (err_inside) {
                                    cb(err_inside, []);
                                }
                                else {
                                    return cb(null, []);
                                }
                            })

                        }
                    }
                });
            }
            else {
                commonFunctions.errorlogger.error("ERROR FOUND ");
                cb(body.error, []);
            }
        }
    });
}

const checkDuplicacy = function (id, data, score_value, callback1) {
    //var dataString = { "query": { "more_like_this": { "fields": data.fieldsName.split(","), "ids": [id], "min_term_freq": 1, "max_query_terms": 12, "fail_on_unsupported_field": false, "minimum_should_match": data.percentage, "include": true, "min_doc_freq": 1 } } };
    var dataString = { "query": { "more_like_this": { "fields": data.fieldsName.split(","), "like": [{ "_index": data.baseElasticName, "_type": data.base_type, "_id": id }], "minimum_should_match": data.percentage, "min_term_freq": 1, "fail_on_unsupported_field": false, "max_query_terms": 12, "min_doc_freq": 1 } } };
    var options = {
        url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + '/' + data.compareElasticName + "/" + data.compare_type + '/_search',
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        },
        body: dataString,
        json: true
    };

    request(options, function (error, response, body) {
        if (error) {
            return callback1(error, []);
        }
        else {
            var taskArr = [];
            var score_value = [];
            if (body.status == 404) {
                return callback1(body.status, []);
            }
            for (var fo = 0; fo < body.hits.hits.length; fo++) {
                var score = (body.hits.hits[fo]["_score"] / body.hits.max_score)
                var obj = {
                    "score_value": Math.round(body.hits.hits[fo]["_score"] * score + parseInt(data.percentage.split("%"))),
                    "title": body.hits.hits[fo]["_source"].title,
                    "url": body.hits.hits[fo]["_source"].view_href,
                    "id": body.hits.hits[fo]["_id"],
                    "type": body.hits.hits[fo]["_type"],
                    "compareElasticName": data.compareElasticName,
                    "compare_type": data.compare_type
                };
                score_value.push(obj);
            }
            data["baseDocId"] = id;
            taskArr.push(updateElasticDocument.bind(null, data, score_value));
            async.series(taskArr, function (err, updatedDocs) {
                if (err) {
                    commonFunctions.errorlogger.error("ERROR DURING UPDATEING ELASTIC DOCUMENT");
                    callback1(err, []);
                }
                else {
                    callback1(null, updatedDocs);
                }
            });
        }
    });
}

const updateElasticDocument = function (data02, score_value, callback) {

    var options = {
        url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + '/' + data02["baseElasticName"] + "/" + data02["base_type"] + "/" + data02["baseDocId"] + '/_update',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: {
            "doc": {
                "duplicacy_score": score_value
            }
        },
        json: true
    };

    request(options, function (error, response, body) {
        if (error) {
            callback(error, []);
        }
        else {

            callback(null, []);
        }
    });
}

const sendNotification = function (mail, cb2) {
    var email = mail;
    var emailObject = {
        to: email,
        subject: "Content Duplicacy Notification",
        html: emailTemplates.contentDuplicacyNotification(email)
    }
    searchunifyEmail.sendEmail(emailObject.to, emailObject.subject, emailObject.html, (error, response) => {
        commonFunctions.errorlogger.warn("notification sent");
    });
    cb2(null, []);
}

module.exports = {
    router: router
}
