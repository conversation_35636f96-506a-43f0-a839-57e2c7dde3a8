var path = require('path');
environment = require('../../routes/environment');
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
var async = require('async');
const mysql = require("mysql");
const connection = mysql.createConnection({
    host: config.get("databaseSettings.host"),
    user: config.get("databaseSettings.user"),
    password: config.get("databaseSettings.password"),
    database: config.get("databaseSettings.database"),
    port: config.get("databaseSettings.mysqlPORT"),
    multipleStatements: true
});

const migrateData = function (cb) {
    var taskArray = [];
    let sql = `SELECT
        pr.search_client_uid as search_client_uid,
        CONCAT('[', GROUP_CONCAT(
        JSON_OBJECT(
            'instance_name', pr.name,
            'regex', pr.regex,
            'instance_regex', IFNULL(pri.instance_regex, '.*')
        )
        ),']') AS pageRatingInstance
        from
            page_rating pr
        LEFT JOIN page_rating_instance pri ON
            pr.search_client_uid = pri.search_client_uid
        AND
            pr.search_client_uid = pri.search_client_uid
        LEFT JOIN page_rating_instance pri2 ON
            pr.search_client_uid = pri.search_client_uid
        AND pri.search_client_uid IS NULL
        Group By
            pr.search_client_uid;`
            connection.query(sql, (err, data) => {
        data.forEach((s) => {
            taskArray.push(setSearchClientSettingsFields.bind(null, s))
        })
        async.series(taskArray, (err, docs) => {
            cb(err, 'Completed');
        })
    });
}


const setSearchClientSettingsFields = (data,cb) => {
    console.log("data: ", data);
    const query = `INSERT into user_feedback (search_client_uid, pageRatingInstance, contentSearchExp, pageRatingCustomization, searchFeedback) VALUES (?, ?, ?, ?, ?)`;
    connection.query(query, [
        data.search_client_uid,
        data.pageRatingInstance,
        JSON.stringify({createdDate: new Date(), enabled: true }),
        JSON.stringify({pageFeedbackToggle: false,
            searchToggle: false,
            selectedAck: "Thank You! Your feedback helps",
            selectedHeader: "Was this article helpful?",
            selectedPageTemplete: "Thumbs up/Down",
            selectedPageTextFeedback: "Would you like to say more ?",
            selectedPostion: "Center",
            submitButton: "Submit Feedback"}),
        JSON.stringify({followUpToggle: false,
            searchEmailId: "",
            searchFeedbackToggle: false,
            selectedSearchAcknowledgement: "Thank You! Your feedback helps.",
            selectedSearchFollowUp: "Can we follow up on the feedback?",
            selectedSearchHeader: "Give Feedback",
            selectedSearchSubHeader: "How was the search today?",
            selectedSearchTemplate: "Emoticons",
            selectedTextFeedback: "Would you like to say more ?",
            submitButton: "Submit Feedback"})

    ], (err, docs) => {
        if (err) console.log("Error for ", data.search_client_uid, " : ", err);
        else console.log("Inserted Data for ", data.search_client_uid);
        cb(null, docs);
    });
}

const startScript = function(cb) { 
    async.auto({
        createTable: function(cb) {
            var user_feedback = `CREATE TABLE IF NOT EXISTS user_feedback (
                id int(11) NOT NULL AUTO_INCREMENT,
                search_client_uid varchar(127) NOT NULL,
                contentSearchExp json DEFAULT NULL,
                searchExp json DEFAULT NULL,
                conversionExp json DEFAULT NULL,
                pageRatingInstance json DEFAULT NULL,
                pageRatingCustomization json DEFAULT NULL,
                searchFeedback json DEFAULT NULL,
                dropdowns json DEFAULT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY user_feedback_UN (search_client_uid)
            )`;
            connection.query(user_feedback, function (err, data) {
                if (err && err.code !== "ER_TABLE_EXISTS_ERROR") cb(err)
                else cb(null,{});
            });

        }, 
        migrate : function(cb) { migrateData(cb);}
    },
    function (err, data) {
        if (err) console.log(err);
        else console.log("Page Rating data migration is now complete.");
        cb(err, data);
    });
}

module.exports={
    startScript
}