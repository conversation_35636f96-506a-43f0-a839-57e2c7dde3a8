<p class="suanalyticscount"> <b>Total Activities: </b><%- array.length  %></p>
<table class="su_anytics_table" >
    <thead>
        <tr>
            <th class="suanalyticstime"> Time (DD/MM/YYYY) </th>
            <th class="suanalyticstype">
                <div class="multiselect">
                    <div class="selectBox" onclick="showCheckboxes()"> Activity Type <span class="suanalyticsdropdown"><img src = "<%-endpoint%>/resources/Assets/dropdown.png" width="10" height="10" style="float:center"></span>
                        <div class="overSelect"></div>
                    </div>
                    <div id="checkboxes">
                        <label for="all">
                        <input type="checkbox" id="all" onclick="checkUncheckAll()" />All</label>
                        <% for (var i = 0; i < filters.length; i++) { %>
                        <label onclick="checkUncheck()" for="<%- filters[i] %>".replace(" ","")  >
                        <input type="checkbox" id="<%- filters[i] %>".replace(" ","") onclick="checkUncheck()" /><%- filters[i] %></label>
                         <% } %>
                    </div>
                </div>
            </th>
            <th class="suanalyticsdata">Activity Details</th>
            <th class="suanalyticsdata">Interface</th>
        </tr>
    </thead>
    <tbody>

        <% for (var i = 0; i < array.length; i++) { %>
        <% if(array[i].ActivityType == 'Page Views' || array[i].ActivityType == 'Conversions'){ %>
        <tr>
            <td class="suanalyticstime"><%- array[i].StartDate %></td>
            <td class="suanalyticstype"><%- array[i].ActivityType %></td>
            <td class="suanalyticsdata">
                <a target="_blank" href='<%- array[i].ActivityValue %>'><%- array[i].ActivityValue %></a>
            </td>
            <td class="suanalyticsdata"><%- array[i].Interface %></td>
        </tr>
            <% } else { %>
        <tr>
            <td class="suanalyticstime"><%- array[i].StartDate %></td>
            <td class="suanalyticstype"><%- array[i].ActivityType %></td>
            <td class="suanalyticsdata">
                <%- array[i].ActivityValue %>
            </td>
            <td class="suanalyticsdata"><%- array[i].Interface %></td>
        </tr>

            <% }%>
            <% } %>

    </tbody>
</table>
<img style="display:none;" id="customScript" value="<%- filters %>" src="dfs.png" onerror="var filters = '<%- filters %>'.split(','); var sc = document.createElement('script'); sc.type='text/javascript'; sc.src='<%-endpoint%>/ext_resources/caseJourney.js'; document.getElementsByTagName('body')[0].appendChild(sc)">
<script type="text/javascript" id="suscript">



    
    console.log('searchunify script loaded internal');    
    var filters = "<%- filters %>".split(',');
</script>

<style>

    #su_anyticscontent table .suanalyticstype{
        width: 130px;
    }

    #su_anyticscontent .su_anytics_table {
        width: 100%;
    }

    #su_anyticscontent .su_anytics_table th{
	overflow: unset;
    }
    #su_anyticscontent{
	height:270px;
    overflow: auto;
    }   

    .multiselect {
        position:relative;
    }

    .selectBox {
        position: relative;
      
    }

    .selectBox select {
        width: 100%;
        font-weight: bold;
    }

    .overSelect {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
    }

    #checkboxes {
        display: none;
        text-align: left;
        position: absolute;
        border: 1px #dadada solid;
        background-color: white;
        padding: 0px;
        width: 100%;
        box-shadow: 4px 5px 6px -5px;
        top: 20px;
    }
    #checkboxes > label > input {
        margin-right: 10px;
        width: 15px; 
        height: 15px; 
    }

    #checkboxes label {
        display: flex;
        align-items: center;
        padding: 2px;
    }

    #checkboxes label:hover {
        background-color: #1e90ff;
    }

    .hide {
        display: none;
    }
    
    th.suanalyticstype {
    text-align: left;
    padding: 5px;
    } 

    th.suanalyticstime{
    /* width: 58%;  */
    text-align: left;
    padding-top: 5px;
    padding-bottom: 5px;
    padding-left: 5px;
    padding-right: 5px
    }
        table {
    border-collapse: collapse;
    }

    table, th, td {
    border: 1px solid #dadada;
    }
    #su_anyticscontent .suanalyticstype img {
        float: right;
        /* padding-right: 10px; */
        /* padding-top: 0px; */
    }
    td.suanalyticstime{
    padding-top: 5px;
    padding-bottom: 5px;
    padding-left: 5px;
    padding-right: 5px;
    white-space: nowrap;
    width: 140px;
    }
    td.suanalyticstype{
    padding-top: 5px;
    padding-bottom: 5px;
    padding-left: 5px;
    padding-right: 5px;
    }
    td.suanalyticsdata{
    padding-top: 5px;
    padding-bottom: 5px;
    padding-left: 5px;
    padding-right: 5px;
    word-break: break-word;
    }
    th.suanalyticsdata {
    padding: 5px;
    }
    #su_anyticscontent .suanalyticsdropdown{
    display: inline-block;
    margin-left: 40px;
    }
</style>
