var express = require('express');
var async = require('async');
var router = express.Router();
var commonFunctions = require('../../utils/commonFunctions');
var searchResultKafkaConfig = require("../admin/searchResultKafkaConfig");

router.use((req,res,next)=>{
    res.setHeader("Access-Control-Allow-Origin", "*");
    res.setHeader("Access-Control-Allow-Methods", "POST, GET")
    res.header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type");
    // res.header("Origin, Content-Type, Accept, Key, X-UserToken");
    next();
});

const updateLastUpdated = (req,res,next) => {
    var q = `update search_clients set last_updated_by = '${JSON.stringify({email:req.headers.session.email, name:req.headers.session.name})}' where uid = '${req.body.uid}'`;
    connection[req.headers['tenant-id']].execute.query(q, [], (err, res) => {
        if (err) {console.log('Error while updating Last updated by user for search client', err) } 
        else {console.log(`Last updated by user updated for '${req.body.uid}'`);}})
        next();
}

const updateLastUpdatedDeletePRI  = (req,res,next) => {
    var q = `update search_clients set last_updated_by = '${JSON.stringify({email:req.headers.session.email, name:req.headers.session.name})}'
            where uid =(SELECT search_client_uid from page_rating_instance where id = '${req.query.prId}')`;
    connection[req.headers['tenant-id']].execute.query(q, [], (err, res) => {
        if (err) { console.log('Error while updating Last updated by user for search client', err) } 
        else { console.log(`Last updated by user updated for '${req.body.uid}'`); }})
        next();
}
const updateLastUpdatedDeletePR = (req,res,next) => {
    var q = `update search_clients set last_updated_by = '${JSON.stringify({email:req.headers.session.email, name:req.headers.session.name})}'
                where uid =(SELECT search_client_uid from page_rating where id = '${req.query.prId}')`;
    connection[req.headers['tenant-id']].execute.query(q, [], (err, res) => {
        if (err) { console.log('Error while updating Last updated by user for search client', err) } 
        else { console.log(`Last updated by user updated for '${req.body.uid}'`);}})
        next();
}

router.post("/addNewPageRating" ,updateLastUpdated,function (req, res, next) {
    commonFunctions.errorlogger.warn("in Page Rating");    
    addNewPageRating(req.body.pageRating, req.body.sid, req.body.uid,req, function(err, data){
        if(err){
            res.send({ "err": err }).status(400);
        }
        else{
            res.send({"status":"inserted"});
        }
    })
});

addNewPageRating = function(pageRating, sid, uid,req, callback){
    var sql = 'INSERT INTO page_rating (name, regex, search_client_id, search_client_uid) VALUES (?, ?, ?, ?)';
    connection[req.headers['tenant-id']].execute.query(sql, [pageRating.name, pageRating.regex, sid, uid], function (err, data2) {
        if(err){
            callback(err, null);
        }
        else{
            callback(null, 'data');
        }
    });
}

router.post("/getPageRatingData", function(req, res, next){
    commonFunctions.errorlogger.warn("In Get Page Rating Data");
    getPageRatingData(req, function(err, data){
        if(err)
            res.send({ "err": err }).status(400);
        else
            res.send(data).status(200);
    })
});

getPageRatingData = function(req, callback){
    //var sql = "select * from page_rating where search_client_uid = ?";
    var sql = req.headers.referer && req.headers.referer.includes('generate-search-client') 
        ? "select * from user_feedback where search_client_uid = ?"
        : "select contentSearchExp, pageRatingInstance, pageRatingCustomization, searchFeedback, searchExp from user_feedback where search_client_uid = ?";
        connection[req.headers['tenant-id']].execute.query(sql, [req.body.uid], function(err, data){
        if(err)
            callback(err, null);
        else
            callback(null, data[0]);
    });
}

router.get('/deletePageRating', updateLastUpdatedDeletePR, function(req, res, data){
    commonFunctions.errorlogger.warn("delete Page Rating Component");
    deletePageRating(req.query.prId,req, function(err, data){
        if(err){
            res.send({ "err": err }).status(400);
        }
        else{
            res.send({"status":"deleted"});
        }
    })
});

deletePageRating = function(prId,req,callback){
    var sql = "Delete from page_rating where id = ?";
    connection[req.headers['tenant-id']].execute.query(sql, [prId], function(err, data){
        if(err){
            callback(err, null);
        }
        else{
            callback(null, 'deleted');
        }
    })
}

router.post("/addNewPageRatingInstance", updateLastUpdated, function (req, res, next) {
    commonFunctions.errorlogger.warn("in Page Rating");    
    addNewPageRatingInstance(req.body.pageRating, req.body.sid, req.body.uid,rreq, function(err, data){
        if(err){
            res.send({ "err": err }).status(400);
        }
        else{
            res.send({"status":"inserted"});
        }
    })
});

addNewPageRatingInstance = function(pageRating, sid, uid,req, callback){
    var sql = 'INSERT INTO page_rating_instance (instance_name, instance_regex, search_client_id, search_client_uid) VALUES (?, ?, ?, ?)';
    connection[req.headers['tenant-id']].execute.query(sql, [pageRating.instance_name, pageRating.instance_regex, sid, uid], function (err, data2) {
        if(err){
            callback(err, null);
        }
        else{
            callback(null, 'data');
        }
    });
}

router.post("/getPageRatingDataInstance", function(req, res, next){
    commonFunctions.errorlogger.warn("In Get Page Rating Data Instance");
    getPageRatingDataInstance(req.body.uid,req, function(err, data){
        if(err){
            res.send({ "err": err }).status(400);
        }
        else{
            res.send({"pageRatingInstanceData": data})
        }
    })
});

getPageRatingDataInstance = function(uid,req, callback){
    var sql = "select * from page_rating_instance where search_client_uid = ?";
    connection[req.headers['tenant-id']].execute.query(sql, [uid], function(err, data){
        if(err){
            callback(err, null);
        }
        else{
            callback(null, data);
        }
    });
}

router.get('/deletePageRatingInstance',updateLastUpdatedDeletePRI, function(req, res, data){
    console.log("delete Page Rating Instance Component");
    deletePageRatingInstance(req.query.prId,req,function(err, data){
        if(err){
            res.send({ "err": err }).status(400);
        }
        else{
            res.send({"status":"deleted"});
        }
    })
});

deletePageRatingInstance = function(prId,req, callback){
    var sql = "Delete from page_rating_instance where id = ?";
    connection[req.headers['tenant-id']].execute.query(sql, [prId], function(err, data){
        if(err){
            callback(err, null);
        }
        else{
            callback(null, 'deleted');
        }
    })
}

router.post("/updateFeedbackCustomisation", function(req, res){
    updateFeedbackCustomisation(req.body,req, function(err, data){
        if(err) res.send({ "err": err }).status(400);
        else res.send(
            {status: 200, message: 'Success::',
            insertedId:data.insertId
        });
    })
});

updateFeedbackCustomisation = function (data, req, callback) {
    console.log(data);

    // SQL query to insert/update user_feedback
    var sql = `
        INSERT INTO user_feedback (
            id,
            search_client_uid,
            pageRatingInstance,
            pageRatingCustomization,
            searchFeedback,
            contentSearchExp,
            searchExp,
            conversionExp,
            dropdowns
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) 
        ON DUPLICATE KEY UPDATE 
            id = VALUES(id),
            search_client_uid = VALUES(search_client_uid),
            pageRatingInstance = VALUES(pageRatingInstance),
            pageRatingCustomization = VALUES(pageRatingCustomization),
            searchFeedback = VALUES(searchFeedback),
            contentSearchExp = VALUES(contentSearchExp),
            searchExp = VALUES(searchExp),
            conversionExp = VALUES(conversionExp),
            dropdowns = VALUES(dropdowns)
    `;

    // Function to update user_feedback for a given search_client_uid
    const updateFeedback = (feedbackData, callback) => {
        connection[req.headers['tenant-id']].execute.query(
            sql,
            [
                feedbackData.id,
                feedbackData.search_client_uid,
                feedbackData.pageRatingInstance,
                feedbackData.pageRatingCustomization,
                feedbackData.searchFeedback,
                feedbackData.contentSearchExp,
                feedbackData.searchExp,
                feedbackData.conversionExp,
                feedbackData.dropdowns
            ],
            (err, data2) => {
                if (err) callback(err, null);
                else callback(null, data2);
            }
        );
    };

    // Step 1: Update feedback for the parent search_client_uid
    updateFeedback(data, (err, result) => {
        if (err) {
            callback(err, null);
            return;
        }

        // Step 2: Check for child search clients (AB test)
        const checkAbTestSql = `
            SELECT uid, id 
            FROM search_clients 
            WHERE ab_test_parent = ?
        `;

        connection[req.headers['tenant-id']].execute.query(
            checkAbTestSql,
            [data.search_client_uid],
            (abTestError, abTestResults) => {
                if (abTestError) {
                    commonFunctions.errorlogger.error("Error while getting Details from search_clients table",abTestError);
                    callback(abTestError, null);
                    return;
                }

                // Step 3: If child search clients exist, update feedback for each of them
                if (abTestResults && abTestResults.length > 0) {
                    let completedUpdates = 0;
                    let updateErrors = [];

                    abTestResults.forEach((child) => {
                        // Create a copy of the original data and adjust it for the child
                        const childData = { ...data }; // Shallow copy of the payload
                        childData.search_client_uid = child.uid; // Replace with child's uid
                        childData.id = child.id; // Replace with child's id (if needed)

                        // Adjust pageRatingInstance for the child (if necessary)
                        if (childData.pageRatingInstance) {
                            try {
                                const pageRatingInstance = JSON.parse(childData.pageRatingInstance);
                                pageRatingInstance.forEach((instance) => {
                                    instance.search_client_uid = child.uid; // Replace with child's uid
                                    instance.search_client_id = child.id; // Replace with child's id
                                });
                                childData.pageRatingInstance = JSON.stringify(pageRatingInstance);
                            } catch (error) {
                                commonFunctions.errorlogger.error("Error parsing pageRatingInstance:", error);
                            }
                        }

                        // Update feedback for the child
                        updateFeedback(childData, (err, result) => {
                            if (err) {
                                updateErrors.push(err);
                            }
                            completedUpdates++;

                            // Check if all updates are done
                            if (completedUpdates === abTestResults.length) {
                                if (updateErrors.length > 0) {
                                    callback(updateErrors, null);
                                } else {
                                    callback(null, "All updates completed successfully");
                                }
                            }
                        });
                    });
                } else {
                    // No child search clients found, return success for parent update
                    callback(null, result);
                }
            }
        );
    });
};

router.post("/enableExperiences", function(req, res) {
    enableExperiences(req.body,req,function (err, result) {
        if (err)
            res.send({Error: 'Something went wrong'}).status(400);
        else
            res.send({message:"Updated Successfully"}).status(200);
    });
});

enableExperiences = function(data, req, callback) {
    let validKeys = ['contentSearchExp', 'searchExp', 'conversionExp'];
    if (Object.keys(data) && Object.keys(data).length && validKeys.indexOf(Object.keys(data)[0]) > -1) {
        const updateFeedback = (searchClientUid, platformId, cb) => {
            var sql = `Update user_feedback set ${Object.keys(data)[0]} = ? where search_client_uid = ?`;
            connection[req.headers['tenant-id']].execute.query(sql, 
                [data[Object.keys(data)[0]], searchClientUid, platformId], 
                function(err, result) {
                    if (err) {
                        cb(err);
                    } else {
                        if (config.get("kafkaTopic.enable")) {
                            let configObj = {
                                "platformId": platformId,
                                "uid": searchClientUid
                            }
                            searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj, req, function(err) {
                                commonFunctions.errorlogger.info("Sending info to Search service");
                                cb(null);
                            });
                        } else {
                            cb(null);
                        }
                    }
                }
            );
        };

        // Check for AB test child search clients
        const checkAbTestSql = `
            SELECT uid, id as platformId 
            FROM search_clients 
            WHERE ab_test_parent = ?
        `;

        // First update the parent search client
        updateFeedback(data.search_client_uid, data.platformId, (parentError) => {
            if (parentError) {
                return callback(parentError, null);
            }

            // Then check and update child clients
            connection[req.headers['tenant-id']].execute.query(
                checkAbTestSql,
                [data.search_client_uid],
                (abTestError, abTestResults) => {
                    if (abTestError) {
                        commonFunctions.errorlogger.error("Error while getting details from the search_clients  table.", abTestError);
                        return callback(abTestError, null);
                    }

                    // If there are no child clients, we're done
                    if (!abTestResults || abTestResults.length === 0) {
                        return callback(null, {});
                    }

                    // Update all child search clients with their respective platformIds
                    let completedUpdates = 0;
                    let updateErrors = [];

                    abTestResults.forEach((child) => {
                        updateFeedback(child.uid, child.platformId, (err) => {
                            if (err) {
                                updateErrors.push(err);
                            }
                            completedUpdates++;

                            // Check if all updates are complete
                            if (completedUpdates === abTestResults.length) {
                                if (updateErrors.length > 0) {
                                    callback({
                                        message: 'Some updates failed',
                                        errors: updateErrors
                                    }, null);
                                } else {
                                    callback(null, {});
                                }
                            }
                        });
                    });
                }
            );
        });
    } else {
        callback('Invalid Data', null);
    }
}

router.post("/addPageRatingInstance", updateLastUpdated, function (req, res, next) {
    commonFunctions.errorlogger.warn("in Page Rating");    
    addPageRatingInstance(req.body,req, function(err, data){
        if(err){
            res.send({ "err": err }).status(400);
        }
        else{
            res.send({"status":"inserted",
             "insertedId":data.insertId});
        }
    })
});

addPageRatingInstance = function(data,req,callback){
    var sql = 'INSERT INTO user_feedback (id,pageRatingInstance,contentSearchExp,search_client_uid) VALUES(?,?,?,?) ON DUPLICATE KEY UPDATE id = VALUES(id), pageRatingInstance = VALUES(pageRatingInstance), contentSearchExp = VALUES(contentSearchExp), search_client_uid = VALUES(search_client_uid)';
    connection[req.headers['tenant-id']].execute.query(sql, [data.id, data.pageRatingInstance, data.contentSearchExp, data.search_client_uid], function (err, data2) {
        if(err){
            callback(err, null);
        }
        else{
            callback(null, data2);
        }
    });
}

router.post("/getSearchFeedbkIsEnabled", function (req, res) {
    getSearchFeedbkIsEnabled(req.body,req, function (err, data) {
        if (err) res.send({ "err": err }).status(400);
        else res.send(
            {
                status: 200, message: 'Success::',
                feedBackEnabled: data
            });
    })
});

getSearchFeedbkIsEnabled = function (data,req, callback) {
    let sql = 'select searchFeedback->"$.searchFeedbackToggle" as feedback from user_feedback';
    if (data.uid && data.uid != '') {
        sql += ' where search_client_uid = ?;';
    }
    connection[req.headers['tenant-id']].execute.query(sql, [data.uid], function (err, data2) {
        if (err) {
            callback(err, null);
        }
        else {
            let res = false;
            if (data2 && data2.length) {
                for (let i = 0; i < data2.length; i++) {
                    if (data2[i].feedback === true || data2[i].feedback === 'true') {
                        res = true;
                        break;
                    }
                }
            }
            callback(null, res);
        }
    });
}

module.exports = {
    router: router
}