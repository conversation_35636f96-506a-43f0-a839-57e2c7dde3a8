

var storage = chrome.storage.local;

// Get at the DOM controls 
var resetButton = document.querySelector('button.reset');
var submitButton = document.querySelector('button.submit');
var urlField = document.getElementById('url');
var showsavedUrl = document.querySelector('.showurl');



submitButton.addEventListener('click', saveChanges);
resetButton.addEventListener('click', reset);



loadChanges();



// submitButton.addEventListener("keydown", function (e) {
//   if (e.keyCode === 13) {  //checks enter key
//     saveChanges();
//   }
// });


function saveChanges() {
  
  var urlValue = urlField.value;
  if (!urlValue) {
    message('Error: No Url Provided','error');
    return;
  }else {
    storage.set({'url': urlValue}, function(items) {
      // Notify that we saved.
      console.log('in save items',items)
      message('Info: Settings saved','info');
    });
  }

}




function loadChanges() {
  storage.get('url', function(items) {
    // To avoid checking items.css we could specify storage.get({css: ''}) to
    // return a default value of '' if there is no css value yet.
    if (items.url) {
      urlField.value = items.url;
      //urlField.disabled = true;
      message('Info: Settings Loaded','info');
    }
  });
}

function reset() {
  // Remove the saved value from storage. storage.clear would achieve the same
  // thing.
  storage.remove('url', function(items) {
    message('Info: Stored url cleared','info');
  });
  // Refresh the text area.
  urlField.value = '';
}

function message(msg,type) {
  var message = document.querySelector('.message');
  message.innerText = msg;
  if(type=='error')
  {
    message.style.color = "red"
  }else{
    message.style.color = "green"
  }
  setTimeout(function() {
    message.innerText = '';
  }, 2000);
}

