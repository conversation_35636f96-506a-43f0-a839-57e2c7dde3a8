# docker compose for searchunify admin service
version: "3.0"

services:
  admin:
    container_name: ${COMPOSE_PROJECT_NAME}_admin
    build:
      context: .
      dockerfile: prod.Dockerfile
    image: admin:${COMPOSE_PROJECT_NAME}
    volumes:
      # - .:/home/<USER>
      # - /home/<USER>/node_modules
      # - /home/<USER>/dist
      - /usr/local/ssl/startCertificates:/usr/local/ssl/startCertificates
      # - search_clients:/home/<USER>/resources/search_clients_custom
      - admin_generic:/home/<USER>/resources/search_clients_custom
      - synonyms:/home/<USER>/synonyms
      - admin_crontab:/var/spool/cron/crontabs/
      - asset_library:/home/<USER>/resources/Asset-Library
      - analytics_reports:/home/<USER>/reports
    command: >
      sh -c "crond -b -l 8
      && node app.js"

    deploy:
      resources:
        limits:
          memory: 2G

    ports:
      - ${POC_EXPOSED_PORT}:6009
      
    networks:
      shared_network:
        aliases:
          - ${COMPOSE_PROJECT_NAME}_admin
    restart: always

volumes:
  # search_clients:
  admin_generic:
  admin_crontab:
  asset_library:
  analytics_reports:
  synonyms:
    external:
      name: poc_synonyms

networks:
  shared_network:
    external:
      name: shared_network
