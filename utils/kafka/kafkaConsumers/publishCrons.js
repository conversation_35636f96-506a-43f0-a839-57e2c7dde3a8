const config = require('config');
const kafkaLib = require('../kafka-lib');

const publishCrons = (body) => {
  kafkaLib.publishMessage({
    topic: config.get('kafkaTopic.cronManage'),
    messages: [
      {
        value: JSON.stringify(body),
        key: body.tenantId
      },
    ],
  });
  console.log('PUBLISH TENANT INFO KAFKA ', JSON.stringify(body));
};

module.exports = {
  publishCrons
};
