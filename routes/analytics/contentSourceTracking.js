var express = require('express');
var async = require('async');
var router = express.Router();
var commonFunctions = require('./../../utils/commonFunctions');

var elastic = require('elasticsearch');

let clientCS = new elastic.Client({
  host: 'http://' + config.get('elasticIndexCS.host') + ':' + config.get('elasticIndexCS.port')
});

router.post("/getDataByDocId", async function (req, res, next) {
  let body = req.body;
  let r = {}
  let groupbyIndex = body.map(function (item) {
    r[item.index_name] = r[item.index_name] || [];
     
    let objTopush = {
        conversion_id: item.conversion_id,
        ts: item.ts,
        uid: item.uid,
        cookie: item.cookie,
        is_internal: item.is_internal,
        tenant_id: item.tenant_id,
        doc_id: item.doc_id,
        fields: item.fields_name,
        index_type: item.index_type
      }
    r[item.index_name].push(objTopush);
    return item;
  });


  getDataElastic( r, (err, resp) => {
    if (!err) {
      res.send(resp);
    }

  });
});

const getDataElastic = function ( tempObj, cb) {
  let response = {};

  async.forEach(
    Object.keys(tempObj),
    function (item, callback) {

      getDataByIndexName(item, tempObj[item], function (err, resp) {
        response[item] = resp
        callback(err, resp);

      })
    },
    function (err) {
      if (!err) {
        cb(null, response)
      }
    }
  );
}

const getDataByIndexName = function (indexName, data, cb) {

  let indexItems = [];

  async.forEach(data, function (item, callback) {
      let tempIdarray = [];
      tempIdarray.push(decodeURIComponent(item.doc_id));
      let query = {
        "_source": {
          "includes": JSON.parse(item.fields)
        },
        "query": {
          "constant_score": {
            "filter": {
              "terms": {
                "_id": tempIdarray
              }
            }
          }
        }
      };

      clientCS.search({
        index: indexName,
        type: item.index_type,
        body: query
      }, (err, data) => {
        if (err) {
          callback(err, []);
        } else {

          // indexItems = indexItems.concat(data.hits.hits);
          item.fields_value = data.hits.hits
          indexItems.push(item)

           callback(null, data.hits.hits)
        }
      });

    },
    function (err) {
      if (!err) {
        cb(null, indexItems)
      }
    }
  );




 
}

module.exports = router;
