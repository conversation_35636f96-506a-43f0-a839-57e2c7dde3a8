var express = require('express');
var router = express.Router();
const config = require('config');
const { tenantSqlConnection } = require('../../auth/sqlConnection');
const { getAddedContentSources } = require("./contentSources");
const kafkaLib = require("./../../utils/kafka/kafka-lib");
const kafkaStatusLib = require("./../../utils/kafka-status-page/kafka-lib");
const { getTenantInfoFromTenantId } = require('auth-middleware');
var commonFunctions = require('./../../utils/commonFunctions');
const searchResultKafkaConfig = require('./searchResultKafkaConfig')
const { tenantGlobals } = require("../../utils/kafka/kafkaPublishers/tenantGlobals");
const aes256 = require('nodejs-aes256');
var appVariables = require('../../constants/appVariables');
const { featurePublisher } = require("../../utils/kafka/kafkaPublishers/featurePublisher");
const { getSearchClient } = require('./searchClient');

const keysToDecrypt = ['client_secret', 'password', 'htaccessPassword', 'accessToken', 'refreshToken', 'sessionId'];

const keysToRemove = {
    content_sources: ['logFile', 'sort_order', 'adminLogFile', 'crawl_status', 'sharedAccess', 'clickScorePath', 'cron_stuck_count', 'current_crawl_end_time', 'current_crawl_start_time', 'current_crawl_object_name', 'pid', 'updated', 'created'],
    content_source_objects: ['object_pid', 'object_status', 'updated', 'created']
}

const authKeysMap = {
    es_cluster_id: 'esClusterId',
    es_cluster_name: 'esClusterName',
    es_cluster_ip: 'esClusterIp',
    es_cluster_dns: 'esClusterDns',
    ml_cluster_id: 'mlClusterId',
    ml_cluster_name: 'mlClusterName',
    ml_cluster_dns: 'mlClusterDns',
    ml_cluster_ip: 'mlClusterIp'
};

const manipulateAuthMiddlewareData = (tenantInfo) => {
    let manipulatedTenantInfo = JSON.parse(JSON.stringify(tenantInfo));
    try {
        manipulatedTenantInfo.config = JSON.parse(tenantInfo.config || '{}');
        for(const info of Object.keys(tenantInfo)){
            if(authKeysMap[info]){
                manipulatedTenantInfo[authKeysMap[info]] = tenantInfo[info];
                delete manipulatedTenantInfo[info];
            }
        }
        return manipulatedTenantInfo;
    } catch (error) {
        commonFunctions.errorlogger.info("error in manipulating Auth Middleware Data", error);
        throw error;
    }
}

const decryptKeys = (object) => {
    try {
        for (let key of Object.keys(object)) {
            if (object.hasOwnProperty(key) && object[key]) {
                object[key] = keysToDecrypt.includes(key) ? aes256.decrypt(appVariables.analytics.encryptionKey, object[key]) : object[key];
            }
        }
    } catch (error) {
        commonFunctions.errorlogger.info("error in decrypting keys", error);
        throw error;
    }
}

const publishContentSources = (tenantId) => {
    return new Promise((resolve, reject) => {
        const sqlQuery = `
            SELECT 
                cs.*, 
                cst.name AS content_source_type_name,
                COALESCE(
                    (
                        SELECT 
                            JSON_ARRAYAGG(
                                JSON_OBJECT(
                                    'id', cso.id,
                                    'name', cso.name,
                                    'label', cso.label
                                )
                            )
                        FROM 
                            content_source_objects cso 
                        WHERE 
                            cso.content_source_id = cs.id
                    ), 
                    JSON_ARRAY()
                ) AS objectsAndFields
            FROM 
                content_sources cs
            JOIN 
                content_source_types cst 
            ON 
                cs.content_source_type_id = cst.id;

        `;

        connection[tenantId].execute.query(sqlQuery, async (err, rows) => {
            if (err) {
                return reject({ data: {}, message: `Error while fetching content sources for tenant id : ${tenantId} - ${err}` });
            } else {
                try {
                    console.log(`fetched cs data from mysql for tenant ${tenantId} - ${JSON.stringify(rows)}`)
                    const csPublishResponse = await kafkaStatusLib.publishMessage({
                        topic: config.get("statusPageService.kafka.contentSourceTopic"),
                        messages: [{
                            value: JSON.stringify({ type: kafkaLib.KAFKA_EVENT_TYPES.add, data: rows, tenantId }),
                        }]
                    });
                    console.log(`content sources publish response - ${JSON.stringify(csPublishResponse)}`);

                    // kafkaLib.publishMessage({ 
                    //     topic   : kafkaLib.SU_CRAWLER_TOPIC.contentSource,
                    //     messages: [{ 
                    //         value : JSON.stringify({ type:kafkaLib.KAFKA_EVENT_TYPES.add, data: rows, tenantId: req.headers['tenant-id'] })
                    //     }]
                    // });

                    resolve({ status: true, message: `Successfully published content sources for tenant id : ${tenantId}` });
                } catch (error) {
                    return reject({ data: {}, message: `Error while publishing content sources for tenant id : ${tenantId}` });
                }
            }
        });
    });
};

const publishSearchClients = (tenantId) => {
    return new Promise((resolve, reject) => {
        const sqlQuery = `
        SELECT 
            sc.*, 
            sct.name AS search_client_type_name
        FROM 
            search_clients sc
        JOIN 
            search_client_types sct 
        ON 
            sc.search_client_type_id = sct.id;
        `;

        connection[tenantId].execute.query(sqlQuery, async (err, rows) => {
            if (err) {
                reject({ data: {}, message: `Error while fetching search clients for tenant id : ${tenantId} - ${err}` });
            } else {
                try {
                    console.log(`fetched sc data from mysql for tenant ${tenantId} - ${JSON.stringify(rows)}`)
                    // kafkaLib.publishMessage({
                    //     topic: config.get("kafkaTopic.searchClientTopic"),
                    //     messages: [{
                    //         value: JSON.stringify({ type: kafkaLib.KAFKA_EVENT_TYPES.add, data: rows, tenantId }),
                    //     }]
                    // });
                    const scPublishResponse = await kafkaStatusLib.publishMessage({
                        topic: config.get("statusPageService.kafka.searchClientTopic"),
                        messages: [{
                            value: JSON.stringify({ type: kafkaLib.KAFKA_EVENT_TYPES.add, data: rows, tenantId }),
                        }]
                    });
                    console.log(`search clients publish response - ${JSON.stringify(scPublishResponse)}`);

                    resolve({ status: true, message: `Successfully published Search Clients for tenant id : ${tenantId}` });
                } catch (error) {
                    reject({ data: {}, message: `Error while publishing Search Clients for tenant id : ${tenantId}` });
                }
            }
        });
    });
};

router.get('/publish-content-sources-search-clients', async function (req, res, next) {
    try {
        const tenantId = req.headers['tenant-id'];
        console.log(`publishing content for tenant - ${tenantId}`);

        Promise.all([
            publishContentSources(tenantId),
            publishSearchClients(tenantId)
        ])
            .then(() => {
                res.send({ status: true, message: "content sources and search clients published" })
            })
            .catch(error => {
                console.error('Error in publishing:', error);
                res.send({ status: false, message: error.message });
            });
    } catch (error) {
        res.send({ status: false, message: error.message });
    }

});

const getTablesJoin = (tableColMapping) => {
    try {
        return `(
            'contentSource', JSON_OBJECT(${tableColMapping.content_sources}),
            'authorization', JSON_OBJECT(${tableColMapping.content_source_authorization}),
            'objectsAndFields', (
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ${tableColMapping.content_source_objects},
                        'fields', (
                            SELECT JSON_ARRAYAGG(
                                JSON_OBJECT(
                                    ${tableColMapping.content_source_object_fields}
                                )
                            )
                            FROM content_source_object_fields csof
                            WHERE csof.content_source_object_id = cso.id
                        )
                    )
                )
                FROM content_source_objects cso
                WHERE cso.content_source_id = cs.id
            ),
            'language', (
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                    ${tableColMapping.content_source_languages}
                    )
                )
                FROM content_source_languages csl
                WHERE csl.content_source_id = cs.id
            ),
            'spacesORboards', (
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                    ${tableColMapping.content_source_spaces}
                    )
                )
                FROM content_source_spaces css
                WHERE css.content_source_id = cs.id
            )
        `;
    } catch (error) {
        commonFunctions.errorlogger.info("error in getting table Join", error);
        throw error;
    }
}

// Retrieve all CS except Website and API CS
const getFilteredCS = (fieldsToQuery, tablesToJoin) => {
    return `SELECT 
    JSON_OBJECT ${fieldsToQuery})
    AS content_source_data 
    ${tablesToJoin}
    WHERE
    cs.content_source_type_id NOT IN (${commonFunctions.constants.CONTENT_SOURCE_TYPE.customContentSource}, ${commonFunctions.constants.CONTENT_SOURCE_TYPE.website}, ${commonFunctions.constants.CONTENT_SOURCE_TYPE.jsWeb})`
}

// Retrieve all Content Sources (CS) except Website and API types, based on the provided csId or csTypeId, excluding Website and API CS.
const getCSByTypeOrId = (fieldsToQuery, tablesToJoin, csId, csTypeId) => {
    let whereCond;
    if (csId) {
        whereCond = `cs.id = ${csId}`
    } else if(csTypeId){
        whereCond = `cs.content_source_type_id = ${csTypeId}`;
    }
    return `SELECT 
    JSON_OBJECT ${fieldsToQuery})
    AS content_source_data 
    ${tablesToJoin}
    WHERE
    ${whereCond}`
}

// Fetch Website Content Source (CS) data if csTypeId is provided, csId corresponds to the Website type, or neither is specified.
const getWebsiteQuery = (fieldsToQuery, tablesToJoin, tableColMapping, csId, csTypeId) => {
    let whereCond;
    if (csId) {
        whereCond = `cs.id = ${csId}`
    } else {
        whereCond = `cs.content_source_type_id IN (${commonFunctions.constants.CONTENT_SOURCE_TYPE.website}, ${commonFunctions.constants.CONTENT_SOURCE_TYPE.jsWeb})`;
    }
    return `SELECT 
    JSON_OBJECT ${fieldsToQuery},
    'webConf', JSON_OBJECT(${tableColMapping.website_setting}))
    AS content_source_data 
    ${tablesToJoin}
    LEFT JOIN website_setting ws ON
    ws.content_source_id = cs.id
    WHERE
    ${whereCond}
  `;
    
}

// Fetch API Content Source (CS) data if csTypeId is provided, csId corresponds to the API CS type, or neither is specified.
const getCustomCSQuery = (fieldsToQuery, tablesToJoin, tableColMapping, csId, csTypeId) => {
    let whereCond;
    if (csId) {
        whereCond = `cs.id = ${csId}`
    } else {
        whereCond = `cs.content_source_type_id = ${commonFunctions.constants.CONTENT_SOURCE_TYPE.customContentSource}`;
    }
    return `SELECT 
    JSON_OBJECT ${fieldsToQuery},
    'apiCrawlerFields', JSON_OBJECT(${tableColMapping.api_crawler_fields})) 
    AS content_source_data 
    ${tablesToJoin}
    LEFT JOIN api_crawler_fields acf ON
    acf.content_source_id = cs.id
    WHERE
    ${whereCond}`
}

const getQueriesToExecute = (tableColMapping, tenantInfo, csTypeId, csId) => {
    return new Promise((resolve, reject) => {
        let queries;
        const { tenantId } = tenantInfo;
        const fieldsToQuery = getTablesJoin(tableColMapping);

        let tablesToJoin = ` FROM 
                content_sources cs
            LEFT JOIN 
                content_source_authorization csa ON csa.content_source_id = cs.id`;

        // Use the map to retrieve data for Website and API Content Sources (CS).
        const queryFunctionsMap = {
            [commonFunctions.constants.CONTENT_SOURCE_TYPE.jsWeb]: () =>
                [getWebsiteQuery(fieldsToQuery, tablesToJoin, tableColMapping, csId, csTypeId)],
            [commonFunctions.constants.CONTENT_SOURCE_TYPE.customContentSource]: () =>
                [getCustomCSQuery(fieldsToQuery, tablesToJoin, tableColMapping, csId, csTypeId)],
        };
        
        // fetch data on the basis of csId, 
        if(csId){
            const sqlQuery = `SELECT content_source_type_id as csTypeId from content_sources where id = ${csId}`;
            connection[tenantId].execute.query(sqlQuery, async (err, row) => {
                if (err) {
                    commonFunctions.errorlogger.info("error in executing request", err);
                    return reject({ data: {}, message: `Error while fetching content sources for tenant id : ${tenantId} - ${err}` });
                } else {
                    if(!row.length){
                        commonFunctions.errorlogger.info('No data found');
                        return resolve({ status: true, message: 'No data found' });
                    } else {
                        const [{csTypeId}] = row;
                        // Retrieve data from the map if csId is of type Website or API; otherwise, fetch data for other Content Sources (CS).
                        queries = (queryFunctionsMap[Number(csTypeId)] && queryFunctionsMap[Number(csTypeId)]()) || 
                                    [getCSByTypeOrId(fieldsToQuery, tablesToJoin, csId, csTypeId)];
                        
                        resolve(queries);
                    }
                }
            })
        } else if(csTypeId){  // fetch data on the basis of csTypeId
            queries = (queryFunctionsMap[Number(csTypeId)] && queryFunctionsMap[Number(csTypeId)]()) || 
                      [getCSByTypeOrId(fieldsToQuery, tablesToJoin, csId, csTypeId)];
            resolve(queries);
        } else {  // fetch all CS data
            queries = [
                getFilteredCS(fieldsToQuery, tablesToJoin),
                getWebsiteQuery(fieldsToQuery, tablesToJoin, tableColMapping, csId, csTypeId),
                getCustomCSQuery(fieldsToQuery, tablesToJoin, tableColMapping, csId, csTypeId),
            ]
            resolve(queries);
        }
    })
}

const getContentSourceData = (tableColMapping, tenantInfo, csTypeId, csId) => {
    return new Promise(async (resolve1, reject1) => {
        const { id: tpk, tenantId } = tenantInfo;
        try {
            // get queries accordingly if csTypeId or csId or none is passed
            const queries = await getQueriesToExecute(tableColMapping, tenantInfo, csTypeId, csId);
            if (!queries.length) {
                commonFunctions.errorlogger.info("No data found");
                return resolve1({ status: true, message: 'No data found' });
            }

            const queryPromises = queries.map((sqlQuery) => {
                return new Promise((resolve, reject) => {
                    connection[tenantId].execute.query(sqlQuery, async (err, rows) => {
                        if (err) {
                            commonFunctions.errorlogger.info("error in executing request", err);
                            return reject({ data: {}, message: `Error while fetching content sources for tenant id : ${tenantId} - ${err}` });
                        }
                        try {
                            if (!rows.length) {
                                commonFunctions.errorlogger.info('No data found');
                                return resolve({ status: true, message: 'No data to send' });
                            }
                            for (const data of rows) {
                                const dataToSend = JSON.parse(data.content_source_data);
                                // Decrypt the keys in the authorization object
                                decryptKeys(dataToSend.authorization);

                                // Transform data before publishing to Kafka
                                dataToSend.spacesORboards = dataToSend.spacesORboards || [];
                                dataToSend.objectsAndFields = dataToSend.objectsAndFields || [];
                                dataToSend.authorization.connection_status = dataToSend.authorization.connection_status ? true : false;
                                dataToSend.tenantId = tenantId;
                                dataToSend.session = manipulateAuthMiddlewareData(tenantInfo);
                                dataToSend.contentSource.sync_frequency = Array.isArray(dataToSend.contentSource.sync_frequency) ?
                                    dataToSend.contentSource.sync_frequency :
                                    dataToSend.contentSource.sync_frequency !== null ?
                                        `${dataToSend.contentSource.sync_frequency}`.split(',').map(ele => Number(ele)) :
                                        [1];
                                dataToSend.contentSource.sync_start_date = new Date(dataToSend.contentSource.sync_start_date).toISOString();

                                commonFunctions.errorlogger.info(`Publishing data for CS: id: ${dataToSend.contentSource.id}, name: ${dataToSend.contentSource.name}, csType: ${dataToSend.contentSource.content_source_type_id}, tenantId: ${tenantId}`);

                                // publish CS Kafka
                                await kafkaLib.publishMessage({
                                    topic: kafkaLib.SU_CRAWLER_TOPIC.contentSource,
                                    messages: [{
                                        value: JSON.stringify({ type: kafkaLib.KAFKA_EVENT_TYPES.add, data: dataToSend, tenantId }),
                                        key: tenantId
                                    }]
                                });
                            }
                            resolve({ status: true, message: `Successfully published content sources for tenant id : ${tenantId}`, data: rows });
                        } catch (error) {
                            return reject({ data: {}, message: `Error while publishing content sources for tenant id : ${tenantId}`, err: error });
                        }
                    });
                });
            });

            // Wait for all promises to resolve or reject
            await Promise.all(queryPromises);
            resolve1();
        } catch (error) {
            commonFunctions.errorlogger.info("Error in executing queries", error);
            reject1(error);
        }
    });
};

const getColumnsFromTables = async (tenantId) => {
    return new Promise(async (resolve, reject) => {
        const [tenantInfo] = await getTenantInfoFromTenantId(tenantId);
        await tenantSqlConnection(tenantInfo.tenantId, tenantInfo.database_name);

        const tables = {
            content_sources: 'cs',
            content_source_authorization: 'csa',
            content_source_objects: 'cso',
            content_source_object_fields: 'csof',
            content_source_languages: 'csl',
            website_setting: 'ws',
            api_crawler_fields: 'acf',
            content_source_spaces: 'css'
        };

        const tableColMapping = {};
        const columnPromises = Object.entries(tables).map(([table, alias]) => {
            const keysToExclude = (keysToRemove[table] && `${keysToRemove[table].map(key => `'${key}'`)}`) || `'updated', 'created'`;

            let getColNameQuery = `SELECT 
            COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE 
            TABLE_SCHEMA = '${tenantInfo.database_name}' 
            AND TABLE_NAME = '${table}' 
            AND COLUMN_NAME 
            NOT IN (${keysToExclude})`;
            return new Promise((resolveQuery, rejectQuery) => {
                connection[tenantId].execute.query(getColNameQuery, async (err, rows) => {
                    if (err) {
                        commonFunctions.errorlogger.info("error in getting table names", err);
                        rejectQuery({ data: {}, message: `Error while fetching content sources for tenant id : ${tenantId} - ${err}` });
                    } else {
                        try {
                            let jsonColumns = rows.map(row => `'${row.COLUMN_NAME}', ${alias}.${row.COLUMN_NAME}`).join(', ');

                            tableColMapping[table] = jsonColumns;
                            resolveQuery();
                        } catch (error) {
                            rejectQuery({ data: {}, message: `Error while publishing content sources for tenant id : ${tenantId}` });
                            return reject({ data: {}, message: `Error while publishing content sources for tenant id : ${tenantId}` })
                        }
                    }
                });
            })
        })
        await Promise.all(columnPromises)

        commonFunctions.errorlogger.info("Successfully fetched colums names from tables");
        resolve({ status: true, message: `Successfully fetched column names from tables for tenant id : ${tenantId}`, data: { tableColMapping, tenantInfo } });
    })
}

router.post('/kafka/publish-content-source', async function (req, res) {
    try {
        const tenantId = req.query.tenantId || req.headers['tenant-id'];
        const { csTypeId, csId } = req.query;
        const result = await getColumnsFromTables(tenantId);
        await getContentSourceData(result.data.tableColMapping, result.data.tenantInfo, csTypeId, csId);

        commonFunctions.errorlogger.info("Content Source Data published successfully");
        res.send({ status: true, message: 'Data send successfully' });

    } catch (error) {
        commonFunctions.errorlogger.info("error in publishing data", error);
        res.send({ status: false, message: 'Failed to publish data' });
    }
})

const processSCData = (sqlQuery, tenantId, req, scTypeId, publishSearchClientTopic) => {
    return new Promise((resolve, reject) => {
        connection[tenantId].execute.query(sqlQuery, async (err, rows) => {
            if (err) {
                commonFunctions.errorlogger.info("Error in executing request", err);
                return reject({ data: {}, message: `Error while fetching content sources for tenant id: ${tenantId} - ${err}` });
            }
            
            if (!rows.length) {
                commonFunctions.errorlogger.info('No data found');
                return resolve({ status: true, message: 'No data to send' });
            }

            try {
                const tasks = rows.map(row => 
                    new Promise((resolveRow, rejectRow) => {
                        searchResultKafkaConfig.getSearchClientSettingsViaKafka(
                            { uid: row.uid, platformId: row.searchClientId }, 
                            req, 
                            (err, res) => {
                                if (err) {
                                    commonFunctions.errorlogger.info("Error in publishing Search Client data", err);
                                    rejectRow({ status: false, message: `Failed to publish data for SC: ${row.searchClientId}` });
                                    return;
                                }
                                
                                commonFunctions.errorlogger.info(
                                    `Search Client Data published successfully for SC: ${row.searchClientId}, uid: ${row.uid}, scTypeId: ${scTypeId || row.scTypeId}`
                                );
                                resolveRow({ status: true, message: `Data sent successfully for SC: ${row.searchClientId}` });
                            }
                        );
                    })
                );

                // Wait for all tasks to complete
                const results = await Promise.all(tasks);
                resolve({ status: true, message: 'All data sent successfully', results });
            } catch (error) {
                commonFunctions.errorlogger.info("Error in processing rows", error);
                return reject({ status: false, message: 'Failed to process all rows', error });
            }
        });
    });
};


router.post('/kafka/publish-search-client', async function (req, res) {
    try {
        const { searchClientId,  scTypeId } = req.query;        
        const tenantId = req.headers['tenant-id'] || req.query.tenantId;

        let sqlQuery = `SELECT id as searchClientId, search_client_type_id as scTypeId, uid FROM search_clients`;

        if (searchClientId) {
            sqlQuery += ` WHERE id = ${searchClientId}`;
        } else if (scTypeId) {
            sqlQuery += ` WHERE search_client_type_id = ${scTypeId}`;
        }

        await processSCData(sqlQuery, tenantId, req, null);

        commonFunctions.errorlogger.info(`Search Client Data published successfully`);
        res.send({ status: true, message: 'Data send successfully' });
    } catch (error) {
        commonFunctions.errorlogger.info("error in publishing data", error);
        res.send({ status: false, message: 'Failed to publish data' });
    }
});

router.post('/kafka/publish-tenant-globals', async function (req, res) {
    try {
        commonFunctions.errorlogger.info(`Received request to publish Tenant Features`);
        const { tenantId } = req.body;
        await tenantGlobals(tenantId);
        commonFunctions.errorlogger.info(`Tenant Features published successfully`);
        res.send({ status: true, message: 'Tenant Features sent successfully' });
    } catch (error) {
        commonFunctions.errorlogger.info("error in publishing data", error);
        res.send({ status: false, message: 'Failed to publish data' });
    }
})

router.post('/kafka/publish-feature-list', async function (req, res) {
    try {
        commonFunctions.errorlogger.info(`Received request to publish feature list`);
        await featurePublisher();
        commonFunctions.errorlogger.info(`Feature list published successfully`);
        res.send({ status: true, message: 'Feature list sent successfully' });
    } catch (error) {
        commonFunctions.errorlogger.info("error in publishing data", error);
        res.send({ status: false, message: 'Failed to publish data' });
    }
})

module.exports = {
    router: router,
    getColumnsFromTables,
    getContentSourceData,
    processSCData,
}