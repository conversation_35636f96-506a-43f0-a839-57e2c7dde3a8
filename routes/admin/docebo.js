var request = require("request");
var request = require('request');
const async = require('async');
var commonFunctions = require('./../../utils/commonFunctions');

const getAccesstoken = function (resultData) {
    return new Promise((resolve,reject)=>{

        var options = {
            method: 'POST',
            url: resultData.contentSource.url + '/oauth2/token',
            headers:
            {
                'content-type': 'application/json',
            },
            formData: {
                "grant_type": "password",
                "scope": "api",
                "client_id": resultData.authorization.client_id,
                "client_secret": resultData.authorization.client_secret,
                "username": resultData.authorization.username,
                "password": resultData.authorization.password
            }
        };

        request(options, function (error, response, body) {
            if (error){
                commonFunctions.errorlogger.error("Error found: ", error);
                return reject(error)
            }else {
                if (response.statusCode != 200) {
                    commonFunctions.errorlogger.error("Error found: ", body.error);
                    return reject({status_code:response.statusCode,status_message:body.error});
                }
                else {
                    var body = JSON.parse(body);

                    return resolve(body);
                }

            }
        });
    });
}

const requestTofetchObjectFields = function (authorization, objectName,req, callback) {
    var customFieldArray = [];
    var flag = 7;

    async.auto({
        getContentSourceDataById : function(cb) {
            commonFunctions.getContentSourceDataById(authorization.content_source_id,req, function (err, resultData) {
                if(!err)
                    cb(null, resultData);
                else
                    cb(err, null)
            });
        },
        getObjectsAndFields: ['getContentSourceDataById', (dataFromAbove, cb) => {
            commonFunctions.getContentSourceObjectsAndFieldsById(authorization.content_source_id, req, function (err, res) {
                if (err) {
                    callback(err, null)
                }
                else {
                    var standardObj = commonFunctions.metadata.loadMappingForDbAndElastic("32").objects;
                    var fieldsArr = [];

                    for (var i = 0; i < standardObj.length; i++) {
                        if (objectName == standardObj[i].name) {
                            flag = 0;
                            fieldsArr = commonFunctions.metadata.loadMappingForDbAndElastic("32").fields.concat(standardObj[i].fields);
                            break;
                        }
                    }
                    var listToAdd = [];
                    var listInt = -1;
                    for (var type = 0; type < res.length; type++) {
                        if (objectName == res[type].name) {
                            listInt = type;
                        }
                    }
                    if (listInt != -1) {
                        type = listInt;
                        listToAdd = res[type].fields;
                        for (var mn = 0; mn < fieldsArr.length; mn++) {
                            var found = 0;
                            for (var field = 0; field < res[type].fields.length; field++) {
                                if (fieldsArr[mn].name == res[type].fields[field].name) {
                                    found = 1;
                                    break;
                                }
                            }
                            if (found == 0) {
                                listToAdd.push(fieldsArr[mn]);
                            }
                        }
                        for (var field = 0; field < listToAdd.length; field++) {

                            customFieldArray.push({
                                "name": listToAdd[field].name,
                                "label": listToAdd[field].label,
                                "type": listToAdd[field].type
                            })
                        }

                    } else {
                        for (var field = 0; field < fieldsArr.length; field++) {

                            customFieldArray.push({
                                "name": fieldsArr[field].name,
                                "label": fieldsArr[field].label,
                                "type": fieldsArr[field].type
                            })
                        }
                    }
                    cb(null, { data: customFieldArray, flag: flag });
                }
            })
        }]

    }, function(err, res){
        callback(null, res.getObjectsAndFields);
    });
}

module.exports = {
    getAccesstoken: getAccesstoken,
    requestTofetchObjectFields: requestTofetchObjectFields
}

