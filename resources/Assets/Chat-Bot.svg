<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="38" height="38" viewBox="0 0 38 38">
  <defs>
    <clipPath id="clip-Header_ICon">
      <rect width="38" height="38"/>
    </clipPath>
  </defs>
  <g id="Header_ICon" data-name="Header ICon" clip-path="url(#clip-Header_ICon)">
    <g id="Group_2817" data-name="Group 2817" transform="translate(-14638 -2671.573)">
      <g id="Group_617" data-name="Group 617" transform="translate(7974 2588.568)">
        <g id="Group_607" data-name="Group 607" transform="translate(6665 88.005)">
          <g id="Group_606" data-name="Group 606" transform="translate(0)">
            <g id="Group_601" data-name="Group 601" transform="translate(11.073 24.137)">
              <rect id="Rectangle_1697" data-name="Rectangle 1697" width="14.402" height="4.29" rx="2.145" transform="translate(14.402 4.29) rotate(180)" fill="#abd1ed"/>
              <path id="Path_269" data-name="Path 269" d="M554.058,202.607a2.141,2.141,0,0,1,1.95-1.262H566.12a2.142,2.142,0,0,1,1.95,1.262Z" transform="translate(-553.863 -201.345)" fill="#3cbbea"/>
            </g>
            <g id="Group_602" data-name="Group 602" transform="translate(27.38 7.214)">
              <ellipse id="Ellipse_1" data-name="Ellipse 1" cx="4.584" cy="4.826" rx="4.584" ry="4.826" fill="#73a8d1"/>
              <path id="Path_270" data-name="Path 270" d="M606.99,149.1a4.388,4.388,0,0,1,2.5.792s.516,5.648.76,7.415a4.453,4.453,0,0,1-3.264,1.444,4.832,4.832,0,0,1,0-9.651Z" transform="translate(-602.406 -149.098)" fill="#1a3d7f" opacity="0.4"/>
            </g>
            <g id="Group_603" data-name="Group 603" transform="translate(0 7.214)">
              <ellipse id="Ellipse_2" data-name="Ellipse 2" cx="4.584" cy="4.826" rx="4.584" ry="4.826" fill="#73a8d1"/>
              <path id="Path_271" data-name="Path 271" d="M527.4,149.1a4.388,4.388,0,0,0-2.5.792s-.516,5.648-.76,7.415a4.453,4.453,0,0,0,3.264,1.444,4.832,4.832,0,0,0,0-9.651Z" transform="translate(-522.815 -149.098)" fill="#1a3d7f" opacity="0.4"/>
            </g>
            <g id="Group_604" data-name="Group 604" transform="translate(2.278)">
              <path id="Path_272" data-name="Path 272" d="M536.194,151.172h13.6a9.011,9.011,0,0,0,9.123-10.017l-.891-6.863A9.1,9.1,0,0,0,548.9,126.5H537.085a9.1,9.1,0,0,0-9.123,7.794l-.891,6.863A9.011,9.011,0,0,0,536.194,151.172Z" transform="translate(-526.998 -126.499)" fill="#abd1ed" stroke="#316894" stroke-width="2"/>
            </g>
            <g id="Group_605" data-name="Group 605" transform="translate(7.123 6.295)">
              <path id="Path_274" data-name="Path 274" d="M545.543,155.7H560.1c2.357,0,4.167-1.759,3.833-3.726l-.5-2.962a3.721,3.721,0,0,0-3.833-2.8H546.045a3.721,3.721,0,0,0-3.833,2.8l-.5,2.962C541.376,153.944,543.187,155.7,545.543,155.7Z" transform="translate(-541.67 -146.213)" fill="#eeedf6"/>
              <path id="Path_275" data-name="Path 275" d="M590.726,146.213h1.514a3.721,3.721,0,0,1,3.833,2.8l.214,1.259A13.248,13.248,0,0,0,590.726,146.213Z" transform="translate(-574.314 -146.213)" fill="#eeedf6"/>
              <path id="Path_276" data-name="Path 276" d="M541.71,152.134l.5-2.962a3.558,3.558,0,0,1,3-2.724c-2.2,1.052-3.147,3.912-1.691,5.706,1.922,2.368,4.26,3.132,7.533,3.4.259.021.276.016.06-.014q.472.061.943.126c.508.068,1.016.135,1.525.2h-8.041C543.187,155.86,541.376,154.1,541.71,152.134Z" transform="translate(-541.67 -146.369)" fill="#eeedf6"/>
            </g>
            <path id="Path_277" data-name="Path 277" d="M588,158.723a2.413,2.413,0,1,0-4.826,0" transform="translate(-562.186 -146.671)" fill="none" stroke="#006081" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2"/>
            <path id="Path_278" data-name="Path 278" d="M552.4,158.723a2.413,2.413,0,1,1,4.826,0" transform="translate(-541.668 -146.671)" fill="none" stroke="#006081" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2"/>
            <path id="Path_279" data-name="Path 279" d="M572.037,183.007a3.053,3.053,0,0,1-5.991,0" transform="translate(-550.767 -164.738)" fill="none" stroke="#006081" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
