var router = require('express').Router();
var path = require('path');
var searchunifyEmail = require('../Lib/email');
var emailTemplates = require('./emailTemplates');
const fs = require('fs');
var commonFunctions = require('./../utils/commonFunctions');

/*
* Sending the chat transcript file to the user mail
* @param {reqest file}
* @return {response}
*/

router.post("/sendSearchUnifySupport", function (req, res) {
    var attachment = {}
    if(req.body.data.fileName) {
        var filePath = path.resolve(process.cwd() + '/resources/Asset-Library/'+ req.body.data.fileName)
        attachment = { 
            filename: req.body.data.fileName,
            path: filePath
        }
    }
    var mailSubject = req.body.data.mailSubject;
    var accountName = req.body.data.accountName.replace( /(<([^>]+)>)/ig, '');
    var name = req.headers.session.name.replace( /(<([^>]+)>)/ig, '');
    var email = req.headers.session.email;
    var subject = req.body.data.subject.replace( /(<([^>]+)>)/ig, '');
    var description = req.body.data.description.replace( /(<([^>]+)>)/ig, '');
    var fileName = req.body.data.fileName;
    var emailObject = {
        to: config.get("instanceName").toLowerCase() == 'sandbox' || config.get("instanceName").toLowerCase() == 'production' ? '<EMAIL>' : email,
        cc: email, // user email
        subject: mailSubject,
        html: emailTemplates.searchunifyAdminQueries(name, accountName, subject, description, fileName),
        attachments: attachment,
    }

    searchunifyEmail.sendEmailWithCcWithAttachments(emailObject, emailObject.subject, emailObject.html, (error, response) => {
        if (error) {
            var response = { flag: 200, message: 'Mail Sent' }
            res.send((response));
            fs.unlink(attachment.path, function (err) {
                if (err)
                    commonFunctions.errorlogger.error(err);
            });
        }
    });
    return "DONE";
});


module.exports = router;
