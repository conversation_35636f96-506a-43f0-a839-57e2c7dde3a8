'use strict';
var path = require('path');
var notification = require(path.join(__dirname, '../../admin/notifications.js'));
var config = require('config');
var commonFunctions = require('../../../utils/commonFunctions');
var request = require("request");
var async = require('async');

var json2csv = require('json2csv').Parser;
var fs = require('fs');

exports.getTypesStatistics = function (req, res, next) {
    let tree = {}
    var session = req.body;
    var value = req.body.value;

    async.waterfall([
        (cb) => {
            let sql = "SELECT * FROM content_sources";
            connection[req.headers['tenant-id']].execute.query(sql, (error, rows) => {
                if (error) {
                    cb(error)
                }
                else {
                    let indexes = rows.map(r => { return [r.elasticIndexName, r.label] });
                    // dateField = {};
                    // rows.forEach(r=>{
                    //   dateField[r.elasticIndexName] = "post_time";
                    // })

                    cb(null, indexes);
                }
            });
        },
        //functions to get index size
        (indexes, cb) => {
            var asyncTasks = [];
            for (var i = 0; i < indexes.length; i++) {
                indexes[i]["size"] = 0;
                asyncTasks.push((function (i) {
                    return function (cb) {
                        commonFunctions.getCountIndexSize(req,indexes[i][0], cb)
                    }
                })(i))
            }
            async.parallel(asyncTasks, function (errAsync, dataAsync) {
                if (!errAsync) {
                    console.log("The data is: " + JSON.stringify(dataAsync));
                    for (var i = 0; i < dataAsync.length; i++) {
                        indexes[i]["size"] = dataAsync[i];
                    }
                }
                cb(null, indexes);
            });
        },
        (indexes, cb) => {
            let apis = [];
            async.parallel((() => {
                indexes.forEach(idx => {
                    apis.push(function (cb) {
                        var dsl = { "aggs": { "count_by_type": { "terms": { "field": "_type" } } } };

                        var options = {
                            method: 'POST',
                            url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + idx[0] + '/_search',
                            //qs: { search_type: 'count' },
                            headers: { 'content-type': 'application/json' },
                            body: dsl,
                            json: true
                        };
                        // console.log("options", JSON.stringify(options));
                        request(options, (error, response, body) => {
                            // console.log("body", JSON.stringify(body));
                            if (!body.error) {
                                tree[idx[1]] = { count: body.hits.total, types: {}, size: idx.size, name: idx[1] };
                                body.aggregations.count_by_type.buckets.forEach((b) => {
                                    tree[idx[1]]['types'][b.key] = { count: b.doc_count };
                                });
                            }
                            cb();
                        });
                    });
                });
                return apis;
            })(),
                // optional callback
                function (err, results) {
                    if (req.body.csv) {
                        let fields = ['Content Source Name', 'Count', 'Index size'];
                        var myData = [];
                        
                        for (let i = 0; i < Object.keys(tree).length; i++) {
                            myData.push({ "Content Source Name": Object.keys(tree)[i], "Count": tree[Object.keys(tree)[i]].count, "Index size": tree[Object.keys(tree)[i]].size });
                        }

                        if (value) {
                            var json2csvParser = new json2csv({ fields: fields });
                            var result = json2csvParser.parse(myData);
                            res.attachment('Search index by content source.csv');
                            res.status(200).send(result);
                            // excelReportGenerator.getExcelReport({ title: req.body.label, from: "", to: "", data: myData, fields: fields, isNested: false }, wb => {
                            //     wb.write('Searches-Count-Without-Result.xlsx', res);
                            // });
                        } else {

                            var json2csvParser = new json2csv({ fields: fields });
                            var result = json2csvParser.parse(myData);
                            res.attachment('Search index by content source.csv');

                            fs.writeFile('resources/reports/Search index by content source.csv', result, 'utf8', function (err) {
                                if (err) {
                                    console.log('Some error occured - file either not saved or corrupted file saved.');
                                } else {
                                    console.log('It\'s saved!');
                                    var name = 'resources/reports/Search index by content source.csv';

                                    if (session.email) {
                                        console.log('sending to ' + session.email);
                                        notification.sendAnalyticsTrackinReport(session, { "from": "", "to": "" }, config.get('adminURL') + '/' + name);
                                        res.send({ message: 'File will be sent via email' });
                                    } else {
                                        console.log('email not found');
                                    }
                                }
                            });


                            // excelReportGenerator.getExcelReport({ title: req.body.label, from: "", to: "", data: myData, fields: fields, isNested: false }, wb => {
                            //     // wb.write('TopConversion.xlsx', res);
                            //     var name = 'resources/reports/Searches-Count-Without-Result.xlsx';
                            //     wb.write(name);
                            //     if (session.email) {
                            //         console.log('sending to ' + session.email);
                            //         notification.sendAnalyticsTrackinReport(session.email, { "from": "", "to": "" }, config.get('adminURL') + '/' + name);
                            //         res.send({ message: 'File will be sent via email' });
                            //     } else {
                            //         console.log('email not found');
                            //     }
                            //     console.log('end time', new Date());
                            // });
                        }
                    } else {
                        res.send(tree);
                    }
                    cb();
                });
        }
    ], (error, results) => {
        if (error)
            console.log(error);
    });
}

exports.getAddedContent = function (req, res, next) {
    var session = req.body;
    var value = req.body.value;
    let tree = {};
    let dateRange = {};
    async.waterfall([
        (cb) => {
            let sql = "SELECT * FROM content_sources";
            connection[req.headers['tenant-id']].execute.query(sql, (error, rows) => {
                if (error) {
                    cb(error)
                }
                else {
                    var indexes
                    indexes = rows.map(r => { return [r.label, r.elasticIndexName] });
                    cb(null, indexes);
                }
            });

        },
        (indexes, cb) => {
            async.parallel((() => {
                let apis = [];
                indexes.forEach(idx => {
                    apis.push(function (cb) {
                        let dsl = { "aggs": { "doc_ranges": { "range": { "field": "tstamp", "keyed": true, "ranges": [] } } } };
                        dsl.aggs.doc_ranges.range.ranges = [
                            {
                                "from": moment().subtract(9, 'months').startOf("quarter").format("YYYY-MM-DD"),
                                "to": moment().subtract(6, 'months').startOf("quarter").subtract(1, "day").format("YYYY-MM-DD"),
                                "key": "Q" + moment().subtract(9, 'months').format("Q YY")
                            },
                            {
                                "from": moment().subtract(6, 'months').startOf("quarter").format("YYYY-MM-DD"),
                                "to": moment().subtract(3, 'months').startOf("quarter").subtract(1, "day").format("YYYY-MM-DD"),
                                "key": "Q" + moment().subtract(6, 'months').format("Q YY")
                            },
                            {
                                "from": moment().subtract(3, 'months').startOf("quarter").format("YYYY-MM-DD"),
                                "to": moment().subtract(0, 'months').startOf("quarter").subtract(1, "day").format("YYYY-MM-DD"),
                                "key": "Q" + moment().subtract(3, 'months').format("Q YY")
                            },
                            {
                                "from": moment().subtract(0, 'months').startOf("quarter").format("YYYY-MM-DD"),
                                "to": moment().subtract(-3, 'months').startOf("quarter").subtract(1, "day").format("YYYY-MM-DD"),
                                "key": "Q" + moment().subtract(0, 'months').format("Q YY")
                            }
                        ];
                        dsl.aggs.doc_ranges.range.field = "post_time";
                        let options = {
                            method: 'POST',
                            url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + idx[1] + '/_search',
                            qs: { search_type: 'count' },
                            headers: { 'content-type': 'application/json' },
                            body: dsl,
                            json: true
                        };
                        request(options, (error, response, body) => {
                            if (!body.error)
                                tree[idx[0]] = body.aggregations.doc_ranges.buckets;
                            cb();
                        });
                    });
                });
                return apis;
            })(),
                function (err, results) {
                    if (req.body.csv) {
                        var fields = ["Content Source", "Quarter", "Count"];
                        var myData = [];

                        var keys = Object.keys(tree);
                        for (var j = 0; j < keys.length; j++) {
                            var counter = 1;
                            var keys2 = Object.keys(tree[keys[j]]);
                            for (var k = 0; k < keys2.length; k++) {
                                var entry = {};
                                if (counter == 1) {
                                    entry[fields[0]] = keys[j]
                                } else {
                                    entry[fields[0]] = "";
                                }
                                entry[fields[1]] = keys2[k]
                                entry[fields[2]] = tree[keys[j]][keys2[k]].doc_count
                                myData.push(entry);
                                counter++
                            }
                        }
                        console.log(JSON.stringify(tree));
                        if (value) {
                            var json2csvParser = new json2csv({ fields: fields });
                            var result = json2csvParser.parse(myData);
                            res.attachment('Get-added-content.csv');
                            res.status(200).send(result);
                        } else {
                            var json2csvParser = new json2csv({ fields: fields });
                            var result = json2csvParser.parse(myData);
                            res.attachment('Get-added-content.csv');

                            fs.writeFile('resources/reports/Get-added-content.csv', result, 'utf8', function (err) {
                                if (err) {
                                    console.log('Some error occured - file either not saved or corrupted file saved.');
                                } else {
                                    console.log('It\'s saved!');
                                    var name = 'resources/reports/Get-added-content.csv';

                                    if (session.email) {
                                        console.log('sending to ' + session.email);
                                        notification.sendAnalyticsTrackinReport(session.email, { "from": "", "to": "" }, config.get('adminURL') + '/' + name);
                                        res.send({ message: 'File will be sent via email' });
                                    } else {
                                        console.log('email not found');
                                    }
                                }
                            });
                        }
                    } else {
                        res.send(tree);
                    }
                });
        }
    ], (error, results) => {
        if (error)
            console.log(error);
    });
}