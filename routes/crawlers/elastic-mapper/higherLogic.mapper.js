class HLDiscussion {
    constructor(discussion = [], comObj = {}, indexName,objsAndFields) {
        this.discussionsStr = '';
        this.discussionObj = {};
        let [discussionThreadKey,indexUniqueName]=['',Object.keys(objsAndFields)[0].split('___')[0]];
        for(let i=0;i<discussion.length;i++){
            
            if(discussion.length - 1 === i){
                for(let field in objsAndFields){
                 let splitField = field.split('___')[2];
                 discussion[i].Community = {CommunityName:comObj.community_name,CommunityKey:comObj.community_key};

                     if (Object.prototype.hasOwnProperty.call(discussion[i], splitField))
                        this.discussionObj[field] = discussion[i][splitField] || '';
                    else if (Object.prototype.hasOwnProperty.call(discussion[i].Author, splitField))
                        this.discussionObj[field] = discussion[i].Community[splitField] || '';
                    else if (Object.prototype.hasOwnProperty.call(discussion[i].Community, splitField))
                        this.discussionObj[field] = discussion[i].Community[splitField] || '';
                 }

                 discussionThreadKey = discussion[i].DiscussionThreadKey || '';
                 this.discussionObj.post_time= new Date(discussion[i].DatePosted) || new Date();
        
                }else{
                    this.discussionObj[`${indexUniqueName}___discussion___Comments`] = this.discussionObj[`${indexUniqueName}___discussion___Comments`] || '';
                    this.discussionObj[`${indexUniqueName}___discussion___Comments`] += this.discussionObj.Comments ? ` ${discussion[i].BodyWithoutMarkup || ''}` : '';
                }

            }

        this.discussionsStr += `{ "index" : { "_index" : "${indexName}", "_type" : "discussion", "_id" : "${discussionThreadKey}" } }\n`;
        this.discussionsStr += `${JSON.stringify(this.discussionObj)}\n`;

    }

    getObjectDiscussions() {
        return this.discussionObj;
    };

    getStringDiscussions(obj) {
        return this.discussionsStr;
    }
}

class HLEvent {
    constructor(event = [], indexName,objsAndFields) {
        this.eventsStr = '';
        event.forEach((evt) => {
            this.eventObj = {};
            for(let field in objsAndFields){
                let splitField = field.split('___')[2];

                if (Object.prototype.hasOwnProperty.call(evt, splitField)){
                    if(splitField === "EventDescription")
                        evt[splitField] = evt[splitField].replace( /(<([^>]+)>)/ig, '');
                    this.eventObj[field] = evt[splitField] || '';
                }
                else if (Object.prototype.hasOwnProperty.call(evt.Address, splitField))
                    this.eventObj[field] = evt.Address[splitField] || '';
                else if (Object.prototype.hasOwnProperty.call(evt.EventType, splitField))
                    this.eventObj[field] = evt.EventType[splitField] || '';
                else if (Object.prototype.hasOwnProperty.call(evt.Community, splitField))
                    this.eventObj[field] = evt.Community[splitField] || '';

            }
           
            this.eventObj.post_time = new Date(evt.CreatedOn) || new Date();
            
            this.eventsStr += `{ "index" : { "_index" : "${indexName}", "_type" : "event", "_id" : "${evt.EventKey || ''}" } }\n`;
            this.eventsStr += `${JSON.stringify(this.eventObj)}\n`;
        });

    }

    getStringEvents() {
        return this.eventsStr;
    }
}

class HLLibraryDocument {
    constructor(documents = [], indexName,comObj = {},objsAndFields) {
        this.documentsStr = '';
        documents.forEach((doc) => {
            this.documentObj = {};
            for(let field in objsAndFields){
            let splitField = field.split('___')[2];
            doc.Community = {CommunityName:comObj.community_name,CommunityKey:comObj.community_key};

            if (Object.prototype.hasOwnProperty.call(doc, splitField)){
                if(splitField === "Description")
                    doc[splitField] = doc[splitField].replace( /(<([^>]+)>)/ig, '');
                this.documentObj[field] = doc[splitField] || '';
            }
            else if (Object.prototype.hasOwnProperty.call(doc.CreatedByContact, splitField))
                this.documentObj[field] = doc.CreatedByContact[splitField] || '';
            else if (Object.prototype.hasOwnProperty.call(doc.Community, splitField))
                this.documentObj[field] = doc.Community[splitField] || '';

             }
            this.documentObj.post_time = new Date(doc.CreatedOn) || new Date();

            this.documentsStr += `{ "index" : { "_index" : "${indexName}", "_type" : "library", "_id" : "${doc.DocumentKey || ''}" } }\n`;
            this.documentsStr += `${JSON.stringify(this.documentObj)}\n`;
        });

    }

    getStringLibraries() {
        return this.documentsStr;
    }
}

class HLBlog {
    constructor(blogs = [], indexName,objsAndFields) {
        this.blogsStr = '';
        let indexUniqueName = Object.keys(objsAndFields)[0].split('___')[0] || '';
        blogs.forEach((blog) => {
            this.blogObj = {};
            for(let field in objsAndFields){
                let splitField = field.split('___')[2];

                    if (Object.prototype.hasOwnProperty.call(blog, splitField)){
                        if(splitField === "BlogText")
                            blog[splitField] = blog[splitField].replace( /(<([^>]+)>)/ig, '');
                        this.blogObj[field] = blog[splitField] || '';
                    }
                    else if (Object.prototype.hasOwnProperty.call(blog.Author, splitField))
                        this.blogObj[field] = blog.Author[splitField] || '';
                    else if (Object.prototype.hasOwnProperty.call(blog.Community, splitField))
                        this.blogObj[field] = blog.Community[splitField] || '';
                }

            this.blogObj.post_time = new Date(blog.CreatedOn) || new Date();
            this.blogObj[`${indexUniqueName}___blog___BlogTags`] = blog.BlogText.match(/(?<=data\-tag\-text\=\").*?(?=\")/gs) || [];
            this.blogObj[`${indexUniqueName}___blog___Comments`] = blog.Comments.reduce((prevComm,comment)=> prevComm +' '+comment.CommentBody.replace( /(<([^>]+)>)/ig, ''),'');

            this.blogsStr += `{ "index" : { "_index" : "${indexName}", "_type" : "blog" ,"_id" : "${blog.BlogKey || ''}" } }\n`;
            this.blogsStr += `${JSON.stringify(this.blogObj)}\n`;
        });

    }

    getStringBlogs() {
        return this.blogsStr;
    }

    getBlogObject(){
        return this.blogObj;
    }

}

class HLMember {
    constructor(members = [], indexName,objsAndFields) {
        this.membersStr = '';
        members.forEach((member) => {
            this.memberObj = {};
            for(let field in objsAndFields){
                let splitField = field.split('___')[2];

                    if (Object.prototype.hasOwnProperty.call(member, splitField))
                        this.memberObj[field] = member[splitField] || '';
                    else if (Object.prototype.hasOwnProperty.call(member.Community, splitField))
                        this.memberObj[field] = member.Community[splitField] || '';
                }

            this.memberObj.post_time = new Date(member.AcceptedOn) || new Date();

            this.membersStr += `{ "index" : { "_index" : "${indexName}", "_type" : "member" } }\n`;
            this.membersStr += `${JSON.stringify(this.memberObj)}\n`;
        });

    }

    getStringMembers() {
        return this.membersStr;
    }

}

class HLCommunity{
    constructor(){
        this.communities = [];
    }

    pushCommunity(community={}){
        this.communities.push(community);
    }

    getCommunityList(){
        return this.communities;
    }
}

module.exports = {
    HLDiscussion,
    HLEvent,
    HLLibraryDocument,
    HLBlog,
    HLMember,
    HLCommunity
}