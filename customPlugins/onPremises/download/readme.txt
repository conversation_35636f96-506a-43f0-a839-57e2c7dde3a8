Copyright ©2020 Grazitti Interactive - All Rights Reserved. NOTICE: All information contained herein is, and remains the property of Grazitti Interactive and its suppliers, if any. The intellectual and technical concepts contained herein are proprietary to Grazitti Interactive and its suppliers and may be covered by India, U.S. and Foreign Patents, patents in process, and are protected by trade secret or copyright law. Dissemination of this information or reproduction of this material is strictly forbidden unless prior written permission is obtained from Grazitti Interactive.

Instructions to setup On Premises:

1. Login to dockerhub
    Username: searchunify
    Password: ardxySearchUnify@9657@#4dyrz
    $ sudo docker login --username=searchunify

2. Fetch onpremise service from docker hub:
    $ sudo docker pull searchunify/onpremise:onpremiseservice

3. Fetch index service from docker hub:
    $ sudo docker pull searchunify/onpremise:indexservice

4. Run onpremise service:
Please make sure 3999 should be accessible externally or you can re-route 3999 port configuration by nginx.
	$ sudo docker run --net=host -v "config:/config" -d --name onpremiseservice searchunify/onpremise:onpremiseservice

5. Run index service:
	To run index service you have to follow two steps:
       a. $ sudo docker volume create --name esdata
       b. $ sudo docker run --name searchunifyindexservice --rm --net=host -d -v "config:/usr/share/suIndexService/config" -v "esdata:/usr/share/suIndexService/data" searchunify/onpremise:indexservice
