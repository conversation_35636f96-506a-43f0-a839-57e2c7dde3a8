const fs             = require('fs');
const moment         = require('moment');
const docSimilarity  = require('doc-similarity');
const path           = require('path');
const async          = require('async');
const elastic        = require('elasticsearch');
const sw             = require('stopword');
const jsep           = require("jsep").addBinaryOp("AND", 10).addBinaryOp("OR", 10);
process.env.NODE_ENV = require('../../routes/environment').configuration;
process.env.NODE_CONFIG_DIR = path.join(__dirname, '../../config');
let config           = require('config');
let  esclient = new elastic.Client({ host: `http://${config.get("elasticIndexCS.host")}:${config.get("elasticIndexCS.port")}` });

console.log(process.argv);
const uid = process.argv[2];
const minMatch = 0.76;
/**
 * 1. get configurations
 * 2. get Data from elastic
 * 3. createsets
 * 4. save groups
 */
async.auto({
    readConfiguration : cb=>{
        fs.readFile(__dirname + '/agentHelperConfig.json.ignore', 'utf8', (error, txt)=>{
            if(error)
                cb(error);
            else{
                let conf, err;
                try{
                    conf = JSON.parse(txt);
                    if(uid && conf)
                        conf = {[uid]: conf[uid]};
                } catch(ex){
                    err = ex;
                }
                cb(err, conf);
            }
        });
    },
    readTrainingData : cb=>{
        fs.readFile(__dirname + '/agentHelperGroups.json.ignore', 'utf8', (error, txt)=>{
            if(error){
                console.error(error);
                cb(null,{});
            }
            else{
                let conf, err;
                try{
                    conf = JSON.parse(txt);
                } catch(ex){
                    err = ex;
                }
                cb(err, conf);
            }
        });
    },
    processEachData : ["readConfiguration", "readTrainingData", (results, cb)=>{
        let listOfTrainingDataTasks = [];
        let trainingData = results.readTrainingData;
        for(let uuid in results.readConfiguration){
            let conf = results.readConfiguration[uuid];
            console.log("conf", conf);
            listOfTrainingDataTasks.push(cb=>{
                async.auto({
                    fetchFromElastic : cb=>{
                        console.log("fetching",`${conf.Source_object.parent}/${conf.Source_object.child}`);
                        let dsl = {"query":{"bool":{"must":[
                            {"exists":{"field":"caseComments"}}
                        ]}}};
                        let custom_condition = createConditions(conf);
                        if(custom_condition)
                            dsl.query.bool.must.push(custom_condition);
                        esclient.search({
                            index: conf.Source_object.parent,
                            type : conf.Source_object.child,
                            size: 2000,
                            body:dsl
                        }, (error,resp)=>{
                            if(error || resp.error)
                                cb(error || resp.error);
                            else{
                                console.log(`Fetched ${resp.hits.hits.length} documents`);
                                cb(null, resp.hits.hits);
                            }
                        });
                    },
                    diff : ["fetchFromElastic", (results, cb)=>{
                        let data = results.fetchFromElastic;
                        let diff = [];
                        let groups = [];
                        for (let inputField = 0; inputField < conf.Input_Fields.length; inputField++) {
                            diff[inputField] = [];
                            for (let i = 0; i < data.length; i++) {
                                diff[inputField][i] = [];
                                groups[i] = [data[i]];
                                for (let j = 0; j < i; j++) {
                                    let InputField = conf.Input_Fields[inputField].name;
                                    if (typeof data[i]["_source"][InputField] === "string" && typeof data[j]["_source"][InputField] === "string") {
                                        let s1 = sw.removeStopwords(data[i]["_source"][InputField].split(/\W+/g)).join(" ");
                                        let s2 = sw.removeStopwords(data[j]["_source"][InputField].split(/\W+/g)).join(" ");
                                        diff[inputField][i][j] = docSimilarity.wordFrequencySim(s1, s2, docSimilarity.cosineSim);
                                    }
                                }
                            }
                        }
                        cb(null,diff);
                    }],
                    groups : ["fetchFromElastic", "diff",(results, cb)=>{
                        let groups = [];
                        let diff = results.diff;
                        let cases = results.fetchFromElastic;
                        for (let inputField = 0; inputField < conf.Input_Fields.length; inputField++) {
                            for (let i = 0; i < cases.length; i++) {
                                groups[i] = [cases[i]];
                                for (let j = 0; j < i; j++) {
                                    if (diff[inputField][i][j] >= minMatch) {
                                        groups[j].forEach(c => {
                                            if (groups[i].indexOf(c) === -1) {
                                                groups[i].push(c);
                                            }
                                        });
                                        groups[j] = groups[i];
                                    }
                                }
                            }
                        }
                        cb(null, groups);
                    }],
                    uniqueGroups:["groups", (results, cb)=>{
                        let uniqueGroups = [];
                        results.groups.forEach(g => {
                            if (uniqueGroups.indexOf(g) === -1) {
                                uniqueGroups.push(g);
                            }
                        });
                        cb(null, uniqueGroups);
                    }],
                    createSets : ["uniqueGroups", (results, cb)=>{
                        let groups = results.uniqueGroups;
                        let Input_Fields = conf.Input_Fields.map(field=>field.name);
                        trainingData[uuid] = groups.map(group=>{
                            let words = new Set();
                            let outputs = [];
                            group.forEach(doc=>{
                                Input_Fields.forEach(field=>{
                                    if(!doc._source[field]){
                                        doc._source[field] = "";
                                    }
                                    doc._source[field].split(/\W+/g)
                                    .map(w=>w.toLowerCase())
                                    .forEach(w=>words.add(w));
                                });
                                if(conf.Output_Field==='caseComments' && typeof doc._source[conf.Output_Field] != "string"){
                                    doc._source[conf.Output_Field] = doc._source[conf.Output_Field].sort((a,b)=>moment(a.createdDate)-moment(b.createdDate))[0].body;
                                }
                                outputs.push({
                                    text : doc._source[conf.Output_Field],
                                    caseId : doc._source.id,
                                    CaseNumber : doc._source[`${conf.Source_object.parent}___${conf.Source_object.child}___CaseNumber`] || null,
                                    Subject : doc._source[`${conf.Source_object.parent}___${conf.Source_object.child}___Subject`] || null
                                });
                            });
                            words = Array.from(words).join(" ");
                            let resultDiffs = [], temp = [];
                            for (let i = 0; i < outputs.length; i++) {
                                resultDiffs[i]=[];
                                temp[i] = {"doc":outputs[i],sum:0};
                                for (let j = 0; j < outputs.length; j++) {
                                    if(i===j)
                                        resultDiffs[i][j] = 1;
                                    else if(j<i)
                                        resultDiffs[i][j] = resultDiffs[j][i];
                                    else
                                        resultDiffs[i][j] = docSimilarity.wordFrequencySim(outputs[i].text, outputs[j].text, docSimilarity.cosineSim);
                                    temp[i].sum += resultDiffs[i][j];
                                }
                            }
                            outputs = temp.sort((a,b)=>{return a.sum-b.sum;}).map(d=>d.doc);
                            return {words,outputs};
                        });
                        console.log("Sets Created");
                        cb();
                    }]
                }, (error, results)=>{
                    if(error)
                        cb(error);
                    else {
                        cb();
                    }
                });
            });
        }
        async.series(listOfTrainingDataTasks, (error, results)=>{
            if(error)
                cb(error);
            else
                cb(null, trainingData);
        });
    }],
    updateGroupsFile:["processEachData", (results, cb)=>{
        let GroupsData = JSON.stringify(results.processEachData, null, 4);
        fs.writeFile(__dirname + '/agentHelperGroups.json.ignore', GroupsData, 'utf8', cb);
    }]
},(error, result)=>{
    if(error)
        console.error(error);
    else
        console.log(result);
});

let createConditions = conf=>{
    let formula = conf.Formula;
    let createDSLExpression = (jstree)=>{
        if(jstree.type === "BinaryExpression"){
            let dsl = {bool:{}};
            let arr = [
                createDSLExpression(jstree.left),
                createDSLExpression(jstree.right)
            ];
            if(jstree.operator==="AND")
                dsl.bool = {must:arr};
            else if(jstree.operator==="OR")
                dsl.bool = {should:arr};
            return dsl;
        }
        else if(jstree.type === "Literal"){
            let condition = formula.Condition[jstree.value-1];
            let dsl_condition = {};
            if(condition.Operator === ">" || condition.Operator === "greater than"){
                dsl_condition = {range: {[condition.Field] : {gt : condition.Value}}};
            }
            else if(condition.Operator === "<" || condition.Operator === "less than"){
                dsl_condition = {range: {[condition.Field] : {lt : condition.Value}}};
            }
            else if(condition.Operator === "=" || condition.Operator === "equals"){
                dsl_condition = {term: {[condition.Field] : condition.Value}};
            }
            else if(condition.Operator === "Contains"){
                dsl_condition = {match_phrase: {[condition.Field] : condition.Value}};
            }
            else if(condition.Operator === "!=" || condition.Operator === "not equal"){
                dsl_condition = {bool:{must_not:{term:{[condition.Field]:condition.Value}}}};
            }
            return dsl_condition;
        }
    };
    let query;
    if(formula.Expression){
        query = createDSLExpression(jsep(formula.Expression));
    }
    else{
        query = createDSLExpression(jsep(formula.Condition.map((e,i)=>i+1).join(" AND ")));
    }
    return query;
};
