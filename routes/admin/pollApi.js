var express = require('express');
var router = express.Router();
var async = require('async');
let request = require( 'request' ).defaults({rejectUnauthorized:false});
let api = require("../../config/version")
let version = '0.0.0';
if(api.version){
  version = api.version;
}


router.get('/', function (req, res) {
  async.auto({
    checkAdminPanelStatus: cb => {
      cb(null, { serviceName: 'Admin Panel', currentStatus: 'Operational' , version: version})
    },
    checkSearchclientApiStatus: cb => {
      if(config.get('checkSearchClientStatus')){
      request({
        "method": "GET",
        "url": config.get('searchClient.url')+'/sc/pollApiCheck',
        "headers":
        {
          'content-type': 'application/json'
        }
      },  (error, response) => {
        if(!error){
          let result = JSON.parse(response.body)
          if (result.status == 200) {
            cb(null, { serviceName: 'Search Client', currentStatus: 'Operational' , version:result.version})
          } else if(result.status == 500){
            cb(null, { serviceName: 'Search Client', currentStatus: 'DB Outage' , version:result.version})
          }
        }else{
          cb(null, { serviceName: 'Search Client', currentStatus: 'Major Outage' , version: '0.0.0'})
        }
      })}
      else{
        cb(null, '')
      }
    },
    checkCrawlerApiStatus: cb => {
      request({
        "method": "GET",
        "url": config.get('crawler.crawlerUrl')+'/health/pollApiCheck',
        "headers":
        {
          'content-type': 'application/json'
        }
      },  (error, response) => {
        if(!error){
          let result = response;
          if(result.data) {
            result.version = data.version;
          }
          if (result.status) {
            cb(null, { serviceName: 'Crawler', currentStatus: 'Operational' , version:result.version})
          } else {
            cb(null, { serviceName: 'Crawler', currentStatus: 'Major Outage' , version:result.version})
          }
        }else{
          cb(null, { serviceName: 'Crawler', currentStatus: 'Major Outage' , version: '0.0.0'})
        }
      })
    },
    getAccessToken: cb => {
      let sql = "SELECT access_token FROM user WHERE provisionKey IS NOT NULL AND provisionKey <> ''"
      connection[req.headers['tenant-id']].execute.query(sql, (error, result) => {
        if (result && result.length)
          cb(null, result[0].access_token)
        else
          cb(null, '')
      })
    },
    getSearchClientUid: cb => {
      let sql = "SELECT uid FROM search_clients WHERE uid IS NOT NULL order by id asc"
      connection[req.headers['tenant-id']].execute.query(sql, (error, result) => {
        if (result && result.length)
          cb(null, result[0].uid)
        else
          cb(null, '')
      })
    },
    checkSearchApiStatus: ["getAccessToken", "getSearchClientUid", (results, cb) => {
      if (results.getAccessToken && results.getSearchClientUid) {
        let dataToSend = {}
        dataToSend = {
          "searchString": "",
          "from": 0,
          "sortby": "post_time",
          "orderBy": "desc",
          "pageNo": 1,
          "aggregations": [],
          "uid": results.getSearchClientUid,
          "resultsPerPage": 10,
          "accessToken": results.getAccessToken,
          "apiVersion": true
        }
        request({
          "method": "POST",
          "url": config.get('searchService.url') + '/search/searchResultByPost',
          "form": dataToSend,
          "headers":
          {
            'content-type': 'application/json'
          }
        }, (error, response) => {
         if (!error && response.statusCode == 200) {
            let result = JSON.parse(response.body)
	        if (result.statusCode === 200) {
              cb(null, { serviceName: 'Search API', currentStatus: 'Operational' , version: result.version})
            } else {
              cb(null, { serviceName: 'Search API', currentStatus: 'Major Outage' , version: result.version})
            }
          } else
            cb(null, { serviceName: 'Search API', currentStatus: 'Major Outage' , version: '0.0.0'})
        })
      } else {
        cb(null, { serviceName: 'Search API', currentStatus: 'Search Client Does Not Exist', version })
      }
    }],
    checkAnalyticsApiStatus: cb => {
	    let options = {
        method: 'GET',
        rejectUnauthorized: false,
        url: config.get('analyticsService.url') +'/dashboard/pollApiCheck?tenantId=' + config.get('tenantId'),
        "headers":{
          'content-type': 'application/json'
        }
      };
      //http://analytics:3000
	    request(options,  (error, response) => {
        if(!error){
          let result = JSON.parse(response.body);

	        if (result.status) {
            cb(null, { serviceName: 'Analytics API', currentStatus: 'Operational' , version:result.version})
          } else {
            cb(null, { serviceName: 'Analytics API', currentStatus: 'Major Outage' , version:result.version})
          }
        }else{
          cb(null, { serviceName: 'Analytics API', currentStatus: 'Major Outage' , version: '0.0.0'})
        }
      })
    },
    checkChatbotApiStatus: cb => {
      // Check if Addon is enabled or not
      let sql = "SELECT id FROM addons_status WHERE addon_id = 9"
      connection[req.headers['tenant-id']].execute.query(sql, (error, result) => {
        if (result && result.length) {
          request({
            "method": "GET",
            "url": config.get('chatBotService.url') +'/webhooks/bot/',
          }, (error, response) => {
            try {
              if(!error){
                let result = JSON.parse(response.body)
                if (result.status) {
                  cb(null, { serviceName: 'Chatbot', currentStatus: 'Operational' })
                } else {
                  cb(null, { serviceName: 'Chatbot', currentStatus: 'Major Outage' })
                }
              } else {
                cb(null, { serviceName: 'Chatbot', currentStatus: 'Major Outage'})
              }
            }  catch (error) {
              cb(null, { serviceName: 'Chatbot', currentStatus: 'Major Outage'})
            }
        })
      } else {
        cb(null, { serviceName: 'Chatbot', currentStatus: 'chatbot not enabled' })
      }   
      })
    }
  }, (error, result) => {
    let resultToSend = [];
    resultToSend.push(result.checkAdminPanelStatus, result.checkSearchApiStatus, result.checkAnalyticsApiStatus, result.checkCrawlerApiStatus, result.checkChatbotApiStatus);
    config.get('checkSearchClientStatus') && resultToSend.push(result.checkSearchclientApiStatus);
    res.send({ "data": resultToSend })
  })
})

module.exports = {
  router: router
}
