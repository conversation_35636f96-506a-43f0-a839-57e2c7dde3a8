
const url = require('url');
const axios = require('axios');
const appVariables = require('../../constants/appVariables');
const khorosAuroraAuthorisationIntermediate = function (contentSource, authorization, callback) {
    const redirect_uri = encodeURIComponent(appVariables.khorosAurora.redirectUri);
    const finalUrl = url.format({
        protocol: 'https',
        host: authorization.url,
        pathname: '/auth/oauth2/authorize',
        query: {
            client_id: authorization.client_id,
            redirect_uri
        }
    });
    callback(null, { status: true, url: finalUrl });
}

const getAuroraAccessToken = async function (contentSource, authorization, encodedCode, callback) {    
    const url = `${contentSource.url}/t5/s/api/2.1/auth/accessToken`;
    console.log(encodedCode, '===encodedCode');
    const code = decodeURIComponent(encodedCode);
    console.log('final Code', code);
    const data = {
        client_id: authorization.client_id,
        client_secret: authorization.client_secret,
        code: code,
        grant_type: 'authorization_code',
        redirect_uri: appVariables.khorosAurora.redirectUri
    };
    console.log(data, '=====data and url', url);

    try {
        const basicAuth = `Basic ${Buffer.from(`${authorization.htaccessUsername}:${authorization.htaccessPassword}`).toString('base64')}`;
        const finalHeaders = {
            headers: {
                'accept': 'application/json',
                'content-type': 'application/json'
            }
        };
        if(authorization.authorization_type.includes('htaccess')) {
            finalHeaders.headers['Authorization'] = basicAuth;
        }

        const response = await axios.post(url, data, finalHeaders);
        
        if (response.data) {
            return callback(null, response.data);
        } else {
            return callback('Missing access token', null);
        }
    } catch (error) {
        return callback(error.message, null);
    }
}


module.exports = {
    khorosAuroraAuthorisationIntermediate,
    getAuroraAccessToken
}