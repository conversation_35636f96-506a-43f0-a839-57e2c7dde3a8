'use strict';

var storage = chrome.storage.local;


chrome.omnibox.onInputEntered.addListener(
  function (text) {
    // Encode user input for special characters , / ? : @ & = + $ #
    storage.get('url', function (items) {
      console.log('on runtime', items.url);
      var newURL = items.url + encodeURIComponent(text);
      //chrome.tabs.create({ url: newURL });
      chrome.tabs.query({
        active: true,
        currentWindow: true
      }, function (tabs) {
        var tab = tabs[0];
        chrome.tabs.update(tab.id, {
          url: newURL
        });
      });


    });

  });
