/**
 * Created by man<PERSON><PERSON> on 30/1/17.
 */

 var pocName = config['poc_name'] ? config.get('poc_name') : '';
 const async = require("async");
 const crontab = require('crontab');
 
 
 const createCron = function (url, parameter, comment, command, callback) {
   crontab.load(function (err, crontab) {
     let allJobs = crontab.jobs({});
     crontab.remove(allJobs);
     let jobSet = Array.from(new Set(allJobs.toString().split(","))); //to get unique cron jobs
 
     for(let i = 0; i < jobSet.length; i++){
       let parsed = crontab.parse(jobSet[i]);
       let comm = parsed.command();
       let ren = parsed.render();
       let freq = ren.split(comm)[0];
       crontab.create(comm, freq, parsed.comment());
     }
 
     if (!command)
       url = '/usr/bin/wget -O - ' + url;
     var job = crontab.create(url, parameter, comment); //command,repetiotion,comment
     crontab.save(function (err, crontab) {
       if (!err)
         callback(1);
       else
         callback(0);
     });
 
   })
 }
 
 const deleteCron = function (parameter, callback) {
 
   crontab.load(function (err, crontab) {
     let job = crontab.jobs({ comment: parameter })
     crontab.remove(job);
 
 
     let allJobs = crontab.jobs({});
     crontab.remove(allJobs);
     let jobSet = Array.from(new Set(allJobs.toString().split(","))); //to get unique cron jobs
 
     for(let i = 0; i < jobSet.length; i++){
       let parsed = crontab.parse(jobSet[i]);
       if(parsed){
         let comm = parsed.command();
         let ren = parsed.render();
         let freq = ren.split(comm)[0];
         crontab.create(comm, freq, parsed.comment());
       }      
     }
     crontab.save(function (err, crontab) {
       if (!err)
         callback(1);
       else{
         console.error(err);
         callback(err);
       }
     });
 
   })
 }
 
 const editCron = function (url, parameter, comment, command, callback) {
   crontab.load(function (err, crontab) {
     let job = crontab.jobs({ comment: comment })
     crontab.remove(job);
 
     let allJobs = crontab.jobs({});
     crontab.remove(allJobs);
     if (!command)
       url = '/usr/bin/wget -O - ' + url;
     crontab.create(url, parameter, comment); //command,repetiotion,comment
     let jobSet = Array.from(new Set(allJobs.toString().split(","))); //to get unique cron jobs
 
     for(let i = 0; i < jobSet.length; i++){
       let parsed = crontab.parse(jobSet[i]);
       if(parsed){
         let comm = parsed.command();
         let ren = parsed.render();
         let freq = ren.split(comm)[0];
         crontab.create(comm, freq, parsed.comment());
       }
     }
     crontab.save(function (err, crontab) {
       if (!err)
         callback(1);
       else{
         console.error(err);
         callback(err);
       }
     });
 
   })
 }
 
 
 module.exports = {
   createCron: createCron,
   deleteCron: deleteCron,
   editCron  : editCron
 }
 
 //   https://vlocity.surchunify.com/getAllDataSF?accessToken=2d83bb21e9e0166b1293c0807651aa7a&startDate=2015-01-19T09:14:34.911Z&endDate='+new Date().toISOString()
 