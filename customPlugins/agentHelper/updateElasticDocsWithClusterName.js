const fs          = require('fs');
const env         = require('../../routes/environment');
const mainConfig  = require('../../config/' + env.configuration);
const host        = mainConfig.elasticIndexCS.host;
const port        = mainConfig.elasticIndexCS.port;
const csv         = require('csvtojson');
const elastic     = require('elasticsearch');
const csvFilePath = "clustered_cases.csv";
/**
 * CHUNKSIZE MUST be an even number
 */
const CHUNKSIZE = 200;
const esclientCS = new elastic.Client({ 
    host: `http://${host}:${port}`,
});
let bulkUpdateJson = [];
csv({
    noheader: true,
    output: "csv"
})
    .fromFile(csvFilePath)
    .on('done', (error) => {
        if(error)
            console.error(error);
        else if (bulkUpdateJson.length) {
            esclientCS.bulk({
                "body": bulkUpdateJson
            }, (error, response) => {
                bulkUpdateJson = [];
                if (error)
                    console.error(new Error(error));
                else
                    console.log("2::",response);
                console.log("5::DONE");
            });
        }
        else
            console.log("4::DONE");
    })
    .subscribe((doc) => {
        return new Promise((resolve, reject) => {
            // console.log("5::",doc);
            let id = doc[0].split("/");
            bulkUpdateJson.push({ "update": { "_index": id[0], "_type": id[1], "_id": decodeURIComponent(id[2]) } });
            bulkUpdateJson.push({ "doc": { "class": doc[1] } });
            if (bulkUpdateJson.length >= CHUNKSIZE) {
                esclientCS.bulk({
                    "body": bulkUpdateJson
                }, (error, response) => {
                    bulkUpdateJson = [];
                    if (error)
                        reject(new Error(error));
                    else
                        resolve(response);
                });
            }
            else
                resolve(null);
        });
    });

