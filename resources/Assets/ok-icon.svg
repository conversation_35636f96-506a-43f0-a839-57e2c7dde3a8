
	<svg class="ft-green-tick" xmlns="http://www.w3.org/2000/svg" height="100" width="100" viewBox="0 0 48 48" aria-hidden="true">
	    <style>
.tick{stroke-dasharray:29px;stroke-dashoffset:29px;-webkit-animation:draw .3s cubic-bezier(0.25,0.25,0.25,1) forwards;animation:draw .3s cubic-bezier(0.25,0.25,0.25,1) forwards;-webkit-animation-delay:.6s;animation-delay:.6s}.circle{fill-opacity:0;stroke:#219a00;stroke-width:16px;-webkit-transform-origin:center;transform-origin:center;-webkit-transform:scale(0);transform:scale(0);-webkit-animation:grow 1s cubic-bezier(0.25,0.25,0.25,1.25) forwards;animation:grow 1s cubic-bezier(0.25,0.25,0.25,1.25) forwards}@-webkit-keyframes grow{60%{-webkit-transform:scale(0.8);transform:scale(0.8);stroke-width:4px;fill-opacity:0}100%{-webkit-transform:scale(0.9);transform:scale(0.9);stroke-width:8px;fill-opacity:1;fill:#219a00}}@keyframes grow{60%{-webkit-transform:scale(0.8);transform:scale(0.8);stroke-width:4px;fill-opacity:0}100%{-webkit-transform:scale(0.9);transform:scale(0.9);stroke-width:8px;fill-opacity:1;fill:#219a00}}@-webkit-keyframes draw{100%{stroke-dashoffset:0}}@keyframes draw{100%{stroke-dashoffset:0}}body{display:table;width:100%;height:100%;position:absolute;top:0;left:0}.svg-container{display:table-cell;text-align:center;vertical-align:middle}
	</style>
        <circle class="circle" fill="#5bb543" cx="24" cy="24" r="22"/>
        <path class="tick" fill="none" stroke="#FFF" stroke-width="6" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M14 27l5.917 4.917L34 17"/>
    </svg>