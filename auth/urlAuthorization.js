const url = require('url');
const config = require('config');
const { USER_ROLES } = require('./../constants/constants');

const accessControlUrlsForAdminAndSuperAdmin = [
    /\/getIdpSettings/,
    /\/getAccountDetail/,
    /\/getHostedUsers/,
    /\/deleteHostedUser/,
    /\/updateIdpSettings/,
    /\/addHostedUser/
];

const accessControlUrlsForSuperAdmin = [
    /\/getAccessControlSettingData/,
    /\/saveAccessControlSettingData/
];

const accesControlForSecurityTabUrls = [
    /\/security/
]

const accessControlForNotificationUrls = [
    /\/notifications/
]

const accessControlForAnalyticsUrls = [
    /\/analytics/
]

const accesControlForNlpManagerUrls = [
    /\/nlpIntent/,
    /\/stopwords/,
    /\/synonyms/,
    /\/intentCustomSynonyms/,
    /\/taxonomyAnnotation/
]

const accessControlForMarketPlaceUrls = [
    /\/addons/
]

const accessControlForLlmIntegrationsUrls = [
    /\/llm-subscriptions/,
    /\/llm-config/,
    /\/removeKey/,
    /\/saveKey/,
    /\/get-llm-sc/,
    /\/getKey/
]

const accessControlForAHAppUrls = [
    /\/agentHelper/,
    /\/plugins/,
]

const accessControlForCommunityHelperApps = {
    "communityHelper": [
        /\/getAllBots/,
        /\/deleteBot/,
        /\/getBotSettings/
    ]
}

const isValidUrlOrNot = async (reqUrl, accessControlUrls) => {
    let valid = false; 
    for (const urlObj of accessControlUrls) {
        const urlObject = url.parse(reqUrl, true);
        if (urlObj.test(urlObject.path)) {
            valid = true;
            break;
        }
    }

    return valid;
}

const accessControlOnReqUrlForAdminAndSuperAdmin = async (req, res, next) => {
    const urlObject = url.parse(req.url, true);
    let valid = false;
    for (const urlObj of accessControlUrlsForAdminAndSuperAdmin) {
        if (urlObj.test(urlObject.path)) {
            valid = true;
            break;
        }
    }
    if (valid && 
        (req.headers && req.headers.session && ![USER_ROLES.ADMIN, USER_ROLES.SUPER_ADMIN].includes(Number(req.headers.session.roleHierarchy)))) {
       return res.send({ statusCode: 402, message: 'Unauthorised Request' });
    }
    return next();
};

function isSearchUnifyUser(email) {
    const allowedSUuser = config.get('suDomains');
    const suDomainPatterns = allowedSUuser.map(domain => new RegExp(domain, 'i'));
    return suDomainPatterns.some(regex => regex.test(email));
}

const accessControlReqUrlsForSuperAdmin = async (req, res, next) => {
    const urlObject = url.parse(req.url, true);
    let valid = false;
    for (const urlObj of accessControlUrlsForSuperAdmin) {
        if (urlObj.test(urlObject.path)) {
            valid = true;
            break;
        }
    }
    if (req.headers && req.headers.session && req.headers.session.email) {
        let searchUnifyUser = isSearchUnifyUser(req.headers.session.email);
        if (searchUnifyUser) {
            return next();
        }
    }
    if (valid && 
        (req.headers && req.headers.session && ![USER_ROLES.SUPER_ADMIN].includes(req.headers.session.roleHierarchy))) {
        return res.send({ statusCode: 402, message: 'Unauthorised Request' });
    }
    return next();
};

const accessControlForSecurityTabsModerator = async (req, res, next) => {
    if (req.headers && 
        req.headers.session && 
        ![USER_ROLES.MODERATOR].includes(Number(req.headers.session.roleHierarchy))) {
        return next();
    }
    const valid = await isValidUrlOrNot(req.url, accesControlForSecurityTabUrls);
    if (valid && (!req.headers.selectedtabs)) {
        return res.status(402).send({ message: 'Unauthorized Request' });
    }
    if (req.headers && req.headers.selectedtabs) {
        const selectTabs = JSON.parse(req.headers.selectedtabs);
        if (valid && (!selectTabs.includes('security') || selectTabs.length === 0)) {
            return res.send({ statusCode: 402, message: 'Unauthorised Request' });
        }
    }

    return next();
};

const accessControlForNotification = async (req, res, next) => {
    if (req.headers && 
        req.headers.session && 
        ![USER_ROLES.MODERATOR].includes(Number(req.headers.session.roleHierarchy))) {
        return next();
    }
    const valid = await isValidUrlOrNot(req.url, accessControlForNotificationUrls);
    if (valid && !req.headers.selectedtabs) {
        return res.status(402).send({ message: 'Unauthorized Request' });
    }
    if (req.headers && req.headers.selectedtabs) {
        const selectTabs = JSON.parse(req.headers.selectedtabs);
        if (valid && (!selectTabs.includes('notifications') || selectTabs.length === 0)) {
            return res.send({ statusCode: 402, message: 'Unauthorised Request' });
        }
    }

    return next();
};

const accessControlForAnalytics = async (req, res, next) => {
    if (req.headers && 
        req.headers.session && 
        ![USER_ROLES.MODERATOR].includes(Number(req.headers.session.roleHierarchy))) {
        return next();
    }
    const valid = await isValidUrlOrNot(req.url, accessControlForAnalyticsUrls);
    if (valid && !req.headers.selectedtabs) {
        return res.status(402).send({ message: 'Unauthorized Request' });
    }
    if (req.headers && req.headers.selectedtabs) {
        const selectTabs = JSON.parse(req.headers.selectedtabs);
        if (valid && (!selectTabs.includes('analytics-v2') || selectTabs.length === 0)) {
            return res.send({ statusCode: 402, message: 'Unauthorised Request' });
        }
    }

    return next();
};

const accessControlForNlpManager = async (req, res, next) => {
    if (req.headers && 
        req.headers.session && 
        ![USER_ROLES.MODERATOR].includes(Number(req.headers.session.roleHierarchy))) {
        return next();
    }
    const valid = await isValidUrlOrNot(req.url, accesControlForNlpManagerUrls);
    if (valid && !req.headers.selectedtabs ) {
        return res.status(402).send({ message: 'Unauthorized Request' });
    }
    if (req.headers && req.headers.selectedtabs) {
        const selectTabs = JSON.parse(req.headers.selectedtabs);
        if (valid && (!selectTabs.includes('manage-synonyms') || selectTabs.length === 0)) {
            return res.send({ statusCode: 402, message: 'Unauthorised Request' });
        }
    }

    return next();
};

const accessControlForMarketplace = async (req, res, next) => {
    if (req.url ==='/addons/verifyJwt') {
        return next();  
    }
    if (req.headers && 
        req.headers.session && 
        ![USER_ROLES.MODERATOR].includes(Number(req.headers.session.roleHierarchy))) {
        return next();
    }
    const valid = await isValidUrlOrNot(req.url, accessControlForMarketPlaceUrls);
    if (valid && !req.headers.selectedtabs) {
        return res.status(402).send({ message: 'Unauthorized Request' });
    }
    if (req.headers && req.headers.selectedtabs) {
        const selectTabs = JSON.parse(req.headers.selectedtabs);
        if (valid && (!selectTabs.includes('marketplace') || selectTabs.length === 0)) {
            return res.send({ statusCode: 402, message: 'Unauthorised Request' });
        }
    }

    return next();
};

const accessControlForLlmIntegrations = async (req, res, next) => {
    if (req.headers && 
        req.headers.session && 
        ![USER_ROLES.MODERATOR].includes(Number(req.headers.session.roleHierarchy))) {
        return next();
    }
    const valid = await isValidUrlOrNot(req.url, accessControlForLlmIntegrationsUrls);
    if (valid && !req.headers.selectedtabs) {
        return res.status(402).send({ message: 'Unauthorized Request' });
    }
    if (req.headers && req.headers.selectedtabs) {
        const selectTabs = JSON.parse(req.headers.selectedtabs);
        if (valid && (!selectTabs.includes('llm-integration') || selectTabs.length === 0)) {
            return res.send({ statusCode: 402, message: 'Unauthorised Request' });
        }
    }

    return next();
};

const accessControlForApps = async (req, res, next) => {
    if (
        req.headers &&
        req.headers.session &&
        ![USER_ROLES.MODERATOR].includes(Number(req.headers.session.roleHierarchy))
    ) {
        return next();
    }
    if (!req.headers || !req.headers["tenant-id"]) {
        return next();
    }
    const tenantId = req.headers["tenant-id"];
    connection[tenantId].execute.query(
        `SELECT * FROM addons_status WHERE is_installed = 1`,
        (error, rows) => {
            if (!rows) return next();
            const communityHelperData = rows.find((obj) => obj.addon_id == 13);
            if (!communityHelperData) return next();
            if (req.headers && req.headers.selectedtabs && req.headers.selectedtabs.length) {
                const selectedTabs = JSON.parse(req.headers.selectedtabs);
                const urlObject = url.parse(req.url, true);
                const communityHelperUrls = accessControlForCommunityHelperApps.communityHelper || [];
                const isValid = communityHelperUrls.some((regex) => regex.test(urlObject.path));
                if (isValid && !selectedTabs.includes('apps')) {
                    return res.status(402).send({
                        statusCode: 402,
                        message: 'Unauthorised Request',
                    });
                }
            }
            next();
        }
    );
};

const accessControlForAHApp = async (req, res, next) => {
    let valid = false;
    if (req.headers && 
        req.headers.session && 
        ![USER_ROLES.MODERATOR].includes(Number(req.headers.session.roleHierarchy))) {
        return next();
    }
    if (req.headers && req.headers.selectedtabs) {
        const selectTabs = JSON.parse(req.headers.selectedtabs);
        const urlObject = url.parse(req.url, true);
        for (const urlObj of accessControlForAHAppUrls) {
            if (urlObj.test(urlObject.path)) {
                valid = true;
                break;
            }
        }
        if (valid && !selectTabs.includes('apps')) {
            return res.send({ statusCode: 402, message: 'Unauthorised Request' });
        }
    }

    return next();
};


module.exports = {
    accessControlOnReqUrlForAdminAndSuperAdmin,
    accessControlReqUrlsForSuperAdmin,
    accessControlForSecurityTabsModerator,
    accessControlForNotification,
    accessControlForAnalytics,
    accessControlForNlpManager,
    accessControlForMarketplace,
    accessControlForLlmIntegrations,
    accessControlForApps,
    accessControlForAHApp
}