/**
 * <AUTHOR>
 * @version 1(17-05-2018)
 * @description Program to update document with boosting factor based on analytics
 */
var path = require('path');
var async = require('async');
environment = require('./../environment');
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, './../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
var commonFunctions = require('./../../utils/commonFunctions');
const metadata = require('../../metadata/mappingForDbAndElastic')
var connection_sql = require('./../../utils/connection');
var elastic = require('elasticsearch');
var outData = {};
var countThreshold = 30;
var maxBoostedItemsForSearch = 10;

commonFunctions.errorlogger.info(environment.configuration);


var stopWords = ["a","about","above","across","after","again","against","all","almost","alone","along","already","also","although","always","among","an","and","another","any","anybody","anyone","anything","anywhere","are","area","areas","around","as","ask","asked","asking","asks","at","away","b","back","backed","backing","backs","be","became","because","become","becomes","been","before","began","behind","being","beings","best","better","between","big","both","but","by","c","came","can","cannot","case","cases","certain","certainly","clear","clearly","come","could","d","did","differ","different","differently","do","does","done","down","down","downed","downing","downs","during","e","each","early","either","end","ended","ending","ends","enough","even","evenly","ever","every","everybody","everyone","everything","everywhere","f","face","faces","fact","facts","far","felt","few","find","finds","first","for","four","from","full","fully","further","furthered","furthering","furthers","g","gave","general","generally","get","gets","give","given","gives","go","going","good","goods","got","great","greater","greatest","group","grouped","grouping","groups","h","had","has","have","having","he","her","here","herself","high","high","high","higher","highest","him","himself","his","how","however","i","if","important","in","interest","interested","interesting","interests","into","is","it","its","itself","j","just","k","keep","keeps","kind","knew","know","known","knows","l","large","largely","last","later","latest","least","less","let","lets","like","likely","long","longer","longest","m","made","make","making","man","many","may","me","member","members","men","might","more","most","mostly","mr","mrs","much","must","my","myself","n","necessary","need","needed","needing","needs","never","new","new","newer","newest","next","no","nobody","non","noone","not","nothing","now","nowhere","number","numbers","o","of","off","often","old","older","oldest","on","once","one","only","open","opened","opening","opens","or","order","ordered","ordering","orders","other","others","our","out","over","p","part","parted","parting","parts","per","perhaps","place","places","point","pointed","pointing","points","possible","present","presented","presenting","presents","problem","problems","put","puts","q","quite","r","rather","really","right","right","room","rooms","s","said","same","saw","say","says","second","seconds","see","seem","seemed","seeming","seems","sees","several","shall","she","should","show","showed","showing","shows","side","sides","since","small","smaller","smallest","so","some","somebody","someone","something","somewhere","state","states","still","still","such","sure","t","take","taken","than","that","the","their","them","then","there","therefore","these","they","thing","things","think","thinks","this","those","though","thought","thoughts","three","through","thus","to","today","together","too","took","toward","turn","turned","turning","turns","two","u","under","until","up","upon","us","use","used","uses","v","very","w","want","wanted","wanting","wants","was","way","ways","we","well","wells","went","were","what","when","where","whether","which","while","who","whole","whose","why","will","with","within","without","work","worked","working","works","would","x","y","year","years","yet","you","young","younger","youngest","your","yours","z","a's","able","about","above","according","accordingly","across","actually","after","afterwards","again","against","ain't","all","allow","allows","almost","alone","along","already","also","although","always","am","among","amongst","an","and","another","any","anybody","anyhow","anyone","anything","anyway","anyways","anywhere","apart","appear","appreciate","appropriate","are","aren't","around","as","aside","ask","asking","associated","at","available","away","awfully","be","became","because","become","becomes","becoming","been","before","beforehand","behind","being","believe","below","beside","besides","best","better","between","beyond","both","brief","but","by","c'mon","c's","came","can","can't","cannot","cant","cause","causes","certain","certainly","changes","clearly","co","com","come","comes","concerning","consequently","consider","considering","contain","containing","contains","corresponding","could","couldn't","course","currently","definitely","described","despite","did","didn't","different","do","does","doesn't","doing","don't", "don’t","done","down","downwards","during","each","edu","eg","eight","either","else","elsewhere","enough","entirely","especially","et","etc","even","ever","every","everybody","everyone","everything","everywhere","ex","exactly","example","except","far","few","fifth","first","five","followed","following","follows","for","former","formerly","forth","four","from","further","furthermore","get","gets","getting","given","gives","go","goes","going","gone","got","gotten","greetings","had","hadn't","happens","hardly","has","hasn't","have","haven't","having","he","he's","hello","help","hence","her","here","here's","hereafter","hereby","herein","hereupon","hers","herself","hi","him","himself","his","hither","hopefully","how","howbeit","however","i'd","i'll","i'm","i've","ie","if","ignored","immediate","in","inasmuch","inc","indeed","indicate","indicated","indicates","inner","insofar","instead","into","inward","is","isn't","it","it'd","it'll","it's","its","itself","just","keep","keeps","kept","know","knows","known","last","lately","later","latter","latterly","least","less","lest","let","let's","like","liked","likely","little","look","looking","looks","ltd","mainly","many","may","maybe","me","mean","meanwhile","merely","might","more","moreover","most","mostly","much","must","my","myself","name","namely","nd","near","nearly","necessary","need","needs","neither","never","nevertheless","new","next","nine","no","nobody","non","none","noone","nor","normally","not","nothing","novel","now","nowhere","obviously","of","off","often","oh","ok","okay","old","on","once","one","ones","only","onto","or","other","others","otherwise","ought","our","ours","ourselves","out","outside","over","overall","own","particular","particularly","per","perhaps","placed","please","plus","possible","presumably","probably","provides","que","quite","qv","rather","rd","re","really","reasonably","regarding","regardless","regards","relatively","respectively","right","said","same","saw","say","saying","says","second","secondly","see","seeing","seem","seemed","seeming","seems","seen","self","selves","sensible","sent","serious","seriously","seven","several","shall","she","should","shouldn't","since","six","so","some","somebody","somehow","someone","something","sometime","sometimes","somewhat","somewhere","soon","sorry","specified","specify","specifying","still","sub","such","sup","sure","t's","take","taken","tell","tends","th","than","thank","thanks","thanx","that","that's","thats","the","their","theirs","them","themselves","then","thence","there","there's","thereafter","thereby","therefore","therein","theres","thereupon","these","they","they'd","they'll","they're","they've","think","third","this","thorough","thoroughly","those","though","three","through","throughout","thru","thus","to","together","too","took","toward","towards","tried","tries","truly","try","trying","twice","two","un","under","unfortunately","unless","unlikely","until","unto","up","upon","us","use","used","useful","uses","using","usually","value","various","very","via","viz","vs","want","wants","was","wasn't","way","we","we'd","we'll","we're","we've","welcome","well","went","were","weren't","what","what's","whatever","when","whence","whenever","where","where's","whereafter","whereas","whereby","wherein","whereupon","wherever","whether","which","while","whither","who","who's","whoever","whole","whom","whose","why","will","willing","wish","with","within","without","won't","wonder","would","would","wouldn't","yes","yet","you","you'd","you'll","you're","you've","your","yours","yourself","yourselves","zero"];

client = new elastic.Client({
  host: config.get('elasticIndex.host')+':'+config.get('elasticIndex.port')
});

commonFunctions.errorlogger.warn('Connecting to db');
//if (process.argv[2]){
  connection_sql.handleDisconnect().then(function (result) {
    commonFunctions.errorlogger.info('database connection status: ',result);
    start();
  })
//}

function start() {
  commonFunctions.errorlogger.warn('fetching analytics data');
  client.search({
      index: config.get('elasticIndex.analytics'),
      type: 'search_keyword',
      from: 0,
      size: 10000,
      scroll: '30s',
      body: {
        "query":{
          "bool":{
            "must":{
              "range":{
                "search_date": {
                  "gte": "now-90d",
                  "lte": "now"
                }
              }
            }
          }
        },
        "sort": {
          "cookie": {
            "order": "desc"
          },
          "search_date": {
            "order": "asc"
          }
        }
    },
    function (err, datain) {
      return getMoreUntilDone(err, datain);
    }
  });
}

// function to loop through analytics
function getMoreUntilDone(err, datain) {
  //console.log(JSON.stringify(err?err:data));
  //console.log(JSON.stringify(result));
  if (err)
    return;
  if (datain.hits.hits.length > 0) {
    commonFunctions.errorlogger.warn('received batch of analytics data.');
    // logic for defining
    var analyticsData = datain.hits.hits; 
    for(var i=0;i<analyticsData.length;i++){
      if(shouldBeBoosted(analyticsData[i]._source, 0)){
        var searchText = preprocessSearch(analyticsData[i]._source.text_entered);
        if(!outData[searchText]){
          outData[searchText] = {}; 
        }

        for(var j=0;j<analyticsData[i]._source.conversion.length;j++){
          if(!outData[searchText][analyticsData[i]._source.conversion[j].es_id])
            outData[searchText][analyticsData[i]._source.conversion[j].es_id] = 1;
          else
            outData[searchText][analyticsData[i]._source.conversion[j].es_id]++;
        }
      }
    }
    commonFunctions.errorlogger.warn('processed this batch of data. calling for next batch');
    client.scroll({
      scrollId: datain._scroll_id,
      scroll: '30s'
    }, function (errin, data) {
      return getMoreUntilDone(errin, data);
    });


    //console.log(JSON.stringify(sessions, undefined, 4));

  }else{
    commonFunctions.errorlogger.warn('no more data to fetch processing all retrieved data');
    var arrayOfSearchTerms = [];
    var searchTerms = Object.keys(outData);
    searchTerms.map(searchTerm =>{
      var arrayForConversions = [];
      var conversions = Object.keys(outData[searchTerm]);
      conversions.map(conversion =>{ 
        if(outData[searchTerm][conversion] >= countThreshold)
          arrayForConversions.push({searchTerm: searchTerm, conversion: conversion, count: outData[searchTerm][conversion]});
      });
      if(arrayForConversions.length>0){
        arrayForConversions.sort((a, b)=> {return b.count-a.count});
        arrayOfSearchTerms.push(arrayForConversions);
      }
    });
    if(arrayOfSearchTerms.length > 0){
      arrayOfSearchTerms.sort((a,b)=>{
        return b[0].count - a[0].count;
      });
      commonFunctions.errorlogger.warn('data rearranged/filtered for processing');
      processResultsForTuning(arrayOfSearchTerms);
    }
    commonFunctions.errorlogger.info(JSON.stringify(arrayOfSearchTerms));
  }
}

function shouldBeBoosted(source, manuallyBoostedCount){
  if(source && source.text_entered && source.text_entered.trim()!=''){
    for(var i=0;i<source.conversion.length>0;i++){
      if((source.page_no && source.page_no>0) || source.conversion[i].rank > manuallyBoostedCount+1){
        return true;
      }
    }
  }
  return false;
}

function preprocessSearch(search_text){
  if(search_text){
      search_text = search_text.trim();
      search_text = search_text.toLowerCase();
      var arr = search_text.split(/[\s.:\t;,|#()\[\]\{\}!"/\\<>*=+\^?_`~\\&-]+/);
      arr = arr.filter(function(item){
              item = item.trim();
              if(item.length>0){
                  if(stopWords.indexOf(item)>=0)
                      return false;
                  else{
                      return true;
                  }
              }else{
                  return false;
              }
          });
      search_text = arr.join(' ').trim();
      search_text = search_text.trim();
  }else{
      search_text = '';
  }
  return search_text;
}

/**
 * @method processResultsForTuning
 * @description Processes top converted documents on each search and add tuning if required to db
 * @param {Array of Arrays} mostConvertedSearches 
 * Each array in parameter contains list of top conversions and count for particular 
 * search(filtered with threshold).
 * Algorithm:
 * 1. for each search 
 * 2.   find tuning for search
 * 3.   for each conversion item
 * 4.     if no manual tuning available and (search - item) pair is not blacklisted from auto boosting
 * 5.       add item to require boosting
 * 6.   for each item in require boosting
 * 7.     calculate boosting level with the help of (conversion count)
 * 8.     store in db autoboosting entry for search item pair  
 * 9. finish
 */
function proceslsResultsForTuning(mostConvertedSearches){
  var sfuncs = mostConvertedSearches.map(function(searches){
    return function(cb){
      var searchText = searches[0].searchTerm;
      var savedTuning = [];
      async.series([
        (cbin) => {
          getSavedTuning(searchText, (est, rst) => {
            savedTuning = rst;
            cbin(null, 1);
          });
        }],
        (err, res) => {
          var requireBoosting = [];
          var cfuncs = searches.map(function(conversion){
            return function(cbin3){
              var indexName = conversion.conversion.split('/')[0];
              var indexType = conversion.conversion.split('/')[1];
              var recordId = conversion.conversion.split('/')[2];
              var minimumBoostingStored = 100;
              if(savedTuning.length>0)
                minimumBoostingStored = savedTuning[0].minBoostLevel;
              var alreadyTuned = false;
              async.series(
                [
                  cbin2=>{
                    if(savedTuning.length > 0){
                      isAlreadyTuned(searchText, indexName, indexType, recordId, function(result){
                        alreadyTuned = result;
                        cbin2(null, 1);
                      });
                    }else{
                      cbin2(null, 1);
                    }
                  }
                ],
                function(ein, rin){
                  if((savedTuning.length == 0 || !alreadyTuned) && !isBlacklisted(searchText, indexName, indexType, recordId)){
                    requireBoosting.push({
                      searchText: searchText,
                      indexName: indexName,
                      indexType: indexType,
                      recordId: recordId,
                      count: conversion.count,
                      maxBoostingLevel: minimumBoostingStored,
                      tunedCount: savedTuning.length
                    });
                  }
                  cbin3(null, 1);
                }
              );
            };
          });

          async.series(cfuncs, function(ein2, rin2){
            if(requireBoosting.length>0){
              calculateAndStoreBoosting(requireBoosting, cb);
            }
          });
          
        }
      )
    };
  });

  async.series(sfuncs, (err, res) => {
    commonFunctions.errorlogger.warn('finished whole process');
  });
}

function getSavedTuning(searchText, cb){
  var sql = "SELECT *, min(boosting_level) as minBoostLevel FROM `keyword_boost` WHERE `search_string`=?";
  connection.query(sql, [searchText], function (err, rows) {
    if (!err) {
      if (rows.length > 0) {
        cb(null, rows);
        return rows;
      }else{
        commonFunctions.errorlogger.info('No tuning found for search: ',searchText);
      }
    }else{
      commonFunctions.errorlogger.error('error while retrieving saved tuning for search: ',searchText);
    }
    cb(null, []);
    return [];
  });
}

function isAlreadyTuned(searchText, indexName, indexType, recordId, cb){
  var sql = "SELECT * FROM `keyword_boost` WHERE `record_id`=? and `search_string`=? and `index_name`=? and `index_type`=?";
  connection.query(sql, [recordId, searchText, indexName, indexType], function (err, rows) {
    if (!err) {
      if (rows.length == 0) {
        cb(false);
        return false;
      }else{
        commonFunctions.errorlogger.info('already tuned search: ',searchText);
      }
    }else{
      commonFunctions.errorlogger.error('error while retrieving saved tuning for search: '+searchText);
    }
    cb(true);
    return true;
  });
}

function isBlacklisted(searchText, indexName, indexType, recordId){
  return false;
}

function calculateAndStoreBoosting(requireBoosting, callback){
  var boostingScore = 1+maxBoostedItemsForSearch;
  var j=0;
  var storeBoostFuncs = requireBoosting.map(boostItem => {
    return function(cb){
      if(j<(maxBoostedItemsForSearch-requireBoosting[0].tunedCount)){
        boostingScore = (boostItem.maxBoostingLevel<boostingScore?boostItem.maxBoostingLevel:boostingScore)-1;
        if(boostingScore < 1){
          boostingScore = 1;
        }
        saveTuning(boostItem.searchText, boostItem.indexName, boostItem.indexType, boostItem.recordId, boostingScore, function(error, result){
          if(!error && result.increment)
            j++;  
          cb(null, 1);
        });
      }else{
        cb(null, 1);
      }
    };
  });
  async.series(storeBoostFuncs, (error, response) => {
    callback(null, 1);
  });
}

function saveTuning(searchString, indexName, indexType, recordId, boostingLevel, callback){
  var sql = "SELECT * FROM `keyword_boost` WHERE `record_id`=? and `search_string`=? and `index_name`=? and `index_type`=?";
  connection.query(sql, [recordId, searchString, indexName, indexType], function (err, rows) {
    if (!err) {
      if (rows.length > 0) {
        var sqlUpdate = "UPDATE  `keyword_boost` SET `boosting_level`=?, `auto_boosted`=1 WHERE  `keyword_boost`.`id` =?;";
        connection.query(sqlUpdate, [ boostingLevel, rows[0].id], function (errUpdate, rowsUpdate) {
          if (errUpdate) {
            console.log("error while updating, ", errUpdate);
            callback(errUpdate);
          } else {
            callback(null, {"increment": false});
          }
        });
      } else {
        var sqlInsert = "INSERT INTO `keyword_boost` (`search_string`, `index_name`, `index_type`, `record_id`,`boosting_level`, `auto_boosted`) " +
          "VALUES (?, ?, ?, ?, ?, 1);";
        connection.query(sqlInsert, [searchString, indexName, indexType, recordId, boostingLevel], function (errInsert, rowsInsert) {
          if (errInsert) {
            commonFunctions.errorlogger.error("error while updating, ", errInsert);
            callback(errInsert);
          } else {
            callback(null, {"increment": true});
          }
        });
      }
    }
  });
}