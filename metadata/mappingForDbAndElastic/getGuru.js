module.exports = {
    objects: [
        {
            name: "Cards",
            label: "Cards",
            status: 1,
            boosting_factor: 1,
            base_url: "",
            fields: [
                {
                    name: "content",
                    label: "Content",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "lastModified",
                    label: "last_modified",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "id",
                    label: "id",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "tags",
                    label: "tags",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "collection_color",
                    label: "collection_color",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "collection_id",
                    label: "collection_id",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 0,
                },
                {
                    name: "collection_roi_enabled",
                    label: "collection_roi_enabled",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "collection_public_cards_enabled",
                    label: "collection_public_cards_enabled",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "collection_type",
                    label: "collection_type",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "collection_name",
                    label: "collection_name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "owner_status",
                    label: "owner_status",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "owner_email",
                    label: "owner_email",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "owner_first_name",
                    label: "owner_first_name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "owner_last_name",
                    label: "owner_last_name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "owner_profile_pic_url",
                    label: "owner_profile_pic_url",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "boards",
                    label: "boards",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "preferredPhrase",
                    label: "title",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "lastVerified",
                    label: "lastVerified",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "last_verified_by_status",
                    label: "last_verified_by_status",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "last_verified_by_email",
                    label: "last_verified_by_email",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "last_verified_by_last_name",
                    label: "last_verified_by_last_name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "last_verified_by_first_name",
                    label: "last_verified_by_first_name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "last_verified_by_profile_pic_url",
                    label: "last_verified_by_profile_pic_url",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "last_modified_by_status",
                    label: "last_modified_by_status",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "last_modified_by_email",
                    label: "last_modified_by_email",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "last_modified_by_last_name",
                    label: "last_modified_by_last_name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "last_modified_by_first_name",
                    label: "last_modified_by_first_name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "last_modified_by_profile_pic_url",
                    label: "last_modified_by_profile_pic_url",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "htmlContent",
                    label: "htmlContent",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "verificationType",
                    label: "verificationType",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "verificationInterval",
                    label: "verificationInterval",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "shareStatus",
                    label: "shareStatus",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "slug",
                    label: "slug",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "cardType",
                    label: "cardType",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "verificationState",
                    label: "verificationState",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "original_owner_status",
                    label: "original_owner_status",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "original_owner_email",
                    label: "original_owner_email",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "original_owner_last_name",
                    label: "original_owner_last_name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "original_owner_first_name",
                    label: "original_owner_first_name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "original_owner_profile_pic_url",
                    label: "original_owner_profile_pic_url",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "nextVerificationDate",
                    label: "nextVerificationDate",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "verifiers",
                    label: "verifiers",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "post_time",
                    label: "created_at",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                    isMerged: 1,
                },
            ],
        },
    ],
    fields: [],
};
