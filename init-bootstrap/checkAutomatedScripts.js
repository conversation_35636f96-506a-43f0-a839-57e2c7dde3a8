const allFunctions = {
    ...require("./c21-dependency/index"),
    ...require("./m22-dependency/index"),
    searchSetup: {...require("./searchSetup")}
}
const async = require("async");

// For updating the record
const updateRecord = (record,cb) => {
    connection.query(`UPDATE automated_scripts_check SET status = 1 WHERE id=${record.id};`, (err, body) => {
        if (err) {
            console.log("######################## Unable to update document ", JSON.stringify(err));
        } else {
            console.log("######################## Completed run script for", record["script"]);
        }
        cb(null, record);
    });
};

// Invoke function
const startChecks = () => {
    connection.query(`SELECT * FROM automated_scripts_check;`, (err, records) => {
        if (err) {
            console.log("Error in checking automated scripts ", JSON.stringify(err));
        }
        if (records && records.length > 0) {
            const actionsPerIndex = [];
            for (let i = 0; i < records.length; i += 1) {
                if (records[i].status === 0) {
                    console.log("############ Automated scripts execution started for ", {
                        script: records[i].script,
                        method: records[i].method
                    });
                    actionsPerIndex.push(allFunctions[records[i].script][records[i].method].bind(null));
                    actionsPerIndex.push(updateRecord.bind(null, records[i]));
                } else {
                    console.log("############ Already executed script ", {
                        script: records[i].script,
                        method: records[i].method
                    });
                }
            }
            async.series(actionsPerIndex, (error, body) => {
                if (error) {
                    console.log("############ Unable to complete automated script ", JSON.stringify(err));
                } else {
                    console.log("############ Completed automated script execution");
                }
            });
        } else {
            console.log("############ No automated scripts to run!!!");
        }
    });
};

module.exports = {
    startChecks
};