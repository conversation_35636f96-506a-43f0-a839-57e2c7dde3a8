const config = require('config');
const redisCacheModule = require('../../utils/redis/redis-cache');
const redisClientProvider = require('../../utils/redis-client-provider');
var commonFunctions = require("../../utils/commonFunctions");
commonFunctions.errorlogger.log(process.env.NODE_ENV);
const constants  = require('../../constants/constants');
const RateLimitInfoStorage = require('../../utils/singleton-storage/rate-limit-storage');

// Mapping APIs to limit types
const apiLimitMapping  = config.get("routeMappingForQuota")



async function fetchUsageCounts(tenantId) {
    const query = commonFunctions.createParamsWithDates({
      offset: 1,
      object: "SearchUnifyGPT",
      sortType: "",
      timeFormat: { minuteWise: true, hourly: true, dateWise: true, monthWise: false }
  });
    const monthlyInfo =  await commonFunctions.fetchApiLogsPromise(query,tenantId).then(results => results).catch((err)=>{
      commonFunctions.errorlogger.debug("Could not fetch monthlly consumption, Using Default consumption;",err);
      return {tenantId,consumption:0} 
    });
    return {
      minute:  0,
      hourly:  0,
      monthly:  monthlyInfo.consumption || 0
    };
  }

async function setConsumedLimitsInCache(TenantRemainingLimitsKey,limits) {
    for (const [timeframe, values] of Object.entries(limits)) {
      for (const [field, value] of Object.entries(values)) {
        await redisCacheModule.setHkey(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,`${timeframe}.${field}`,value);
      }
    }
  }
async function getConsumedLimitsFromCache(TenantRemainingLimitsKey){
  const limitsinCache = {
    minute:{
      tokens: await redisCacheModule.getKeyHash(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,'minute.tokens'),
      lastRefill: await redisCacheModule.getKeyHash(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,'minute.lastRefill')
    },
    hourly:{
      tokens: await redisCacheModule.getKeyHash(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,'hourly.tokens'),
      lastRefill: await redisCacheModule.getKeyHash(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,'hourly.lastRefill')
    },
    monthly:{
      tokens: await redisCacheModule.getKeyHash(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,'monthly.tokens'),
      lastRefill: await redisCacheModule.getKeyHash(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,'monthly.lastRefill')
    }
  }
  return limitsinCache;
}
async function decrementAllowedLimits(TenantRemainingLimitsKey){
  await redisCacheModule.hDecByOne(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,'minute.tokens');
  await redisCacheModule.hDecByOne(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,'hourly.tokens');
  await redisCacheModule.hDecByOne(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,'monthly.tokens');
}
async function refillInCache(TenantRemainingLimitsKey,interval,bucket){
  await redisCacheModule.setHkey(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,`${interval}.tokens`,bucket.tokens);
  await redisCacheModule.setHkey(redisClientProvider.getRedisClient(),TenantRemainingLimitsKey,`${interval}.lastRefill`,bucket.lastRefill);
}
function hasNullValue(obj) {
  return obj.minute.tokens === null || 
         obj.minute.lastRefill === null ||
         obj.hourly.tokens === null || 
         obj.hourly.lastRefill === null ||
         obj.monthly.tokens === null || 
         obj.monthly.lastRefill === null;
}
// Middleware function
function rateLimitandQuota(rateLimitConfig) {
  commonFunctions.errorlogger.info(">>>rateLimitandQuota initialized with", rateLimitConfig);
  return async (req, res, next) => {
    const tenantId = req.headers['tenant-id'];
    const apiEndpoint = req.originalUrl;   
    if (!tenantId) {
      return res.status(400).json({ error: "Tenant ID is required." });
    }
    const TenantLimitsCacheKey = req.headers['tenant-id'] + "_quota_consumption_allowed";
    let tenantData = await redisCacheModule.getValue(redisClientProvider.getRedisClient(),TenantLimitsCacheKey,true);
    if (!tenantData || Object.keys(tenantData).length === 0) {
      // Retrieve tenant configuration data
      const tenantConfig = rateLimitConfig.find(config => config.tenantId === tenantId); 
      if (tenantConfig && tenantConfig.data && tenantConfig.data.usages) {
        tenantData = tenantConfig.data.usages;
      } else {
        commonFunctions.errorlogger.info(">>>rateLimitandQuota missing in rateLimitConfig using default limits for tenant id", tenantId);
        tenantData = constants.DEFAULT_CONSUMPTION_LIMTS["usages"];
      }
      await redisCacheModule.setKey(redisClientProvider.getRedisClient(),TenantLimitsCacheKey,3600,JSON.stringify(tenantData));
    }

    let limitType;
    if (apiLimitMapping.searchUnifyGPT.includes(apiEndpoint)) {
      limitType = tenantData.gptApiLimit;
      redisKeyPostfix = "_gpt";
    }else {
      return next();
    }
    
    const now = Date.now();
    const tenantUsedLimitsKey = `${req.headers['tenant-id']}${redisKeyPostfix}_quota_remaining`;
    let tenantLimits = await getConsumedLimitsFromCache(tenantUsedLimitsKey);
    if (hasNullValue(tenantLimits)) {
      // Fetch current usage counts from the database or analytics
      const usageCounts = await fetchUsageCounts(req.headers['tenant-id']);
      tenantLimits = {
        minute: { tokens: limitType.minute - usageCounts.minute, lastRefill: now },
        hourly: { tokens: limitType.hourly - usageCounts.hourly, lastRefill: now },
        monthly: { tokens: limitType.monthly - usageCounts.monthly, lastRefill: now }
      };
      await setConsumedLimitsInCache(tenantUsedLimitsKey,tenantLimits);
    }
    // Function to refill tokens dynamically
    function refillTokens(bucket, maxTokens, interval) {
      const timePassed = now - parseInt(bucket.lastRefill,10);
      const newTokens = Math.floor(timePassed / interval) * maxTokens;
      bucket.tokens = Math.min( parseInt(bucket.tokens ,10)+ newTokens, maxTokens);
      bucket.lastRefill = now;
    }

    const refillIntervals = {
      minute: 60000,
      hourly: 3600000,
      monthly: 2592000000
    };

    const intervals = ['minute', 'hourly', 'monthly'];
    for (const interval of intervals) {
        // looping through all intervals
      const bucket = tenantLimits[interval];
      const maxTokens = limitType[interval];
      refillTokens(bucket, maxTokens, refillIntervals[interval]);
      // if any of the bucket is empty, rate limit is exceeded. 
      await refillInCache(tenantUsedLimitsKey,interval,bucket);
      if (bucket.tokens <= 0) {
        return res.status(429).json({ error: "Rate limit exceeded." });
      }
      bucket.tokens -= 1;
     
    }
    await decrementAllowedLimits(tenantUsedLimitsKey);
    next();
  };
}

async function manageTenantRateLimitInfo (req, res, next) {
  // handle new tenants registration.
  commonFunctions.errorlogger.info(">>>>>>>>>>>>>>>manageTenantRateLimitInfo:",req.headers["tenant-id"]);
  const currentTenantId = req.headers["tenant-id"];
  if(currentTenantId in RateLimitInfoStorage.get("BURST")){
      next();
      return;
  }
  else{
      commonFunctions.errorlogger.info(`Rate limits are missing for tenant:${currentTenantId}`);
      await commonFunctions.fetchRateLimits();
      commonFunctions.errorlogger.info(`Rate limits updated!!`);
      next();
  }
};
module.exports = {
  rateLimitandQuota,
  manageTenantRateLimitInfo
}
