const moment = require("moment");
const dailyJobs = () => {
    updateConversionCount();
};
const updateConversionCount = (cb) => {
    /**
     * 1. 
     */
};
const init = () => {
    dailyJobs();
    setTimeout(() => {
        init();
    }, 24*60*60*1000);
};
setTimeout(() => {
    init();
}, 0-moment().diff(moment().add(1,"day").format("YYYY-MM-DD")));
module.exports = {
    dailyJobs : dailyJobs
}