const jwt = require('jsonwebtoken');
const config = require('config');
var express = require('express');
var router = express.Router();
var commonFunctions = require("../../utils/commonFunctions");
commonFunctions.errorlogger.log(environment.configuration);
const redisCacheModule = require('../../utils/redis/redis-cache');
const redisClientProvider = require('../../utils/redis-client-provider');
const RateLimitInfoStorage = require('../../utils/singleton-storage/rate-limit-storage');
function validateToken(token) {
  try {
    const decoded = jwt.verify(token, config.get('statusPageTokenOptions.key'));
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp && decoded.exp < currentTime) {
      return { valid: false, reason: 'Token has expired' };
    }
    return { valid: true, reason:"" };
  } catch (error) {
    // Handle token errors
    if (error.name === 'TokenExpiredError') {
      return { valid: false, reason: 'Token has expired' };
    } else if (error.name === 'JsonWebTokenError') {
      return { valid: false, reason: 'Token is invalid' };
    }
    return { valid: false, reason: 'Unknown error' };
  }
}
function tokenValidation (token){
    if(!token){
        commonFunctions.errorlogger.debug("Token is missing");
        return false;
    }
    const validationResult = validateToken(token);
    if (!validationResult.valid){
        commonFunctions.errorlogger.debug("Token InValid Reason:",validationResult.reason);
        return false;
    }
    return true;
} 
function parametersValidation(req){
  /* need to add validation for
  1. If tenant is present
  2. If SearchnifyGPT limits object is as expected. else it will break the code for GPT API.
  */
  if(!req.headers['tenant-id']){
    return false;
  }
  const { gptApiLimit }  = req.body;
  if(!(gptApiLimit
    && gptApiLimit.minute
    && Number.isInteger(gptApiLimit.minute)
    && gptApiLimit.hourly
    && Number.isInteger(gptApiLimit.hourly)
    && gptApiLimit.monthly
    && Number.isInteger(gptApiLimit.monthly))
    ){
      return false;
    }
  return true;
}
router.post('/updateLimits', async function (req, res) {
    commonFunctions.errorlogger.debug(">>>>>>>updateLimits");
    if(!tokenValidation(req.headers["status-page-token"])){
        return res.status(401).send({
            status: 401,
            success: false,
            message: "Invalid Token",
            data: null,
        });
    }
    if(!parametersValidation(req)){
      return res.status(400).send({
        status: 400,
        success: false,
        message: "Invalid Parameters Passed!!",
        data: null,
    }); 
    }
    const tenantId = req.headers['tenant-id'];
    commonFunctions.errorlogger.debug(`>>>>>>>updating limits for tenantId:${tenantId}. Limits: ${JSON.stringify(req.body)}`);
    const TenantLimitsCacheKey = tenantId + "_quota_consumption_allowed";
    await redisCacheModule.setKey(redisClientProvider.getRedisClient(),TenantLimitsCacheKey,3600,JSON.stringify(req.body));
    
    await redisCacheModule.deleteKey(redisClientProvider.getRedisClient(),`${tenantId}_gpt_quota_remaining`);
    const RateLimitInfoStorageInfo  = RateLimitInfoStorage.get('QUOTA');
   
    const indexToReplace = RateLimitInfoStorageInfo.findIndex(obj => obj.tenantId === tenantId);
    if (indexToReplace !== -1) {
      RateLimitInfoStorageInfo[indexToReplace] = { tenantId, data:{ usages:req.body}};
    }else{
      commonFunctions.errorlogger.debug('Tenant Limits Information Does not Exist on this server, adding it',tenantId);
      RateLimitInfoStorageInfo.push(req.body); 
    }
    RateLimitInfoStorage.set("QUOTA",RateLimitInfoStorageInfo);
    commonFunctions.errorlogger.debug("updateLimits");
    res.send({
        success:true
    }).status(200);
});

module.exports = {
    router: router
}

