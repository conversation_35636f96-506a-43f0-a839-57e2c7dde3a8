var express = require('express');
var router = express.Router();
var async = require('async');
var request = require("request");
var fs = require('fs');
var commonFunctions = require('./../../utils/commonFunctions');
var elasticsearch = require('elasticsearch');
var connection_sql = require('./../../utils/connection');


/**
 * metaObj = {'371':[
 * title, content]}
 * Input: searchText, searchClientid
 * @Output : Will return the Meta Graph details object
 * 
 */

var stopWords = ["a", "about", "above", "across", "after", "again", "against", "all", "almost", "alone", "along", "already", "also", "although", "always", "among", "an", "and", "another", "any", "anybody", "anyone", "anything", "anywhere", "are", "area", "areas", "around", "as", "ask", "asked", "asking", "asks", "at", "away", "b", "back", "backed", "backing", "backs", "be", "became", "because", "become", "becomes", "been", "before", "began", "behind", "being", "beings", "best", "better", "between", "big", "both", "but", "by", "c", "came", "can", "cannot", "case", "cases", "certain", "certainly", "clear", "clearly", "come", "could", "d", "did", "differ", "different", "differently", "do", "does", "done", "down", "down", "downed", "downing", "downs", "during", "e", "each", "early", "either", "end", "ended", "ending", "ends", "enough", "even", "evenly", "ever", "every", "everybody", "everyone", "everything", "everywhere", "f", "face", "faces", "fact", "facts", "far", "felt", "few", "find", "finds", "first", "for", "four", "from", "full", "fully", "further", "furthered", "furthering", "furthers", "g", "gave", "general", "generally", "get", "gets", "give", "given", "gives", "go", "going", "good", "goods", "got", "great", "greater", "greatest", "group", "grouped", "grouping", "groups", "h", "had", "has", "have", "having", "he", "her", "here", "herself", "high", "high", "high", "higher", "highest", "him", "himself", "his", "how", "however", "i", "if", "important", "in", "interest", "interested", "interesting", "interests", "into", "is", "it", "its", "itself", "j", "just", "k", "keep", "keeps", "kind", "knew", "know", "known", "knows", "l", "large", "largely", "last", "later", "latest", "least", "less", "let", "lets", "like", "likely", "long", "longer", "longest", "m", "made", "make", "making", "man", "many", "may", "me", "member", "members", "men", "might", "more", "most", "mostly", "mr", "mrs", "much", "must", "my", "myself", "n", "necessary", "need", "needed", "needing", "needs", "never", "new", "new", "newer", "newest", "next", "no", "nobody", "non", "noone", "not", "nothing", "now", "nowhere", "number", "numbers", "o", "of", "off", "often", "old", "older", "oldest", "on", "once", "one", "only", "open", "opened", "opening", "opens", "or", "order", "ordered", "ordering", "orders", "other", "others", "our", "out", "over", "p", "part", "parted", "parting", "parts", "per", "perhaps", "place", "places", "point", "pointed", "pointing", "points", "possible", "present", "presented", "presenting", "presents", "problem", "problems", "put", "puts", "q", "quite", "r", "rather", "really", "right", "right", "room", "rooms", "s", "said", "same", "saw", "say", "says", "second", "seconds", "see", "seem", "seemed", "seeming", "seems", "sees", "several", "shall", "she", "should", "show", "showed", "showing", "shows", "side", "sides", "since", "small", "smaller", "smallest", "so", "some", "somebody", "someone", "something", "somewhere", "state", "states", "still", "still", "such", "sure", "t", "take", "taken", "than", "that", "the", "their", "them", "then", "there", "therefore", "these", "they", "thing", "things", "think", "thinks", "this", "those", "though", "thought", "thoughts", "three", "through", "thus", "to", "today", "together", "too", "took", "toward", "turn", "turned", "turning", "turns", "two", "u", "under", "until", "up", "upon", "us", "use", "used", "uses", "v", "very", "w", "want", "wanted", "wanting", "wants", "was", "way", "ways", "we", "well", "wells", "went", "were", "what", "when", "where", "whether", "which", "while", "who", "whole", "whose", "why", "will", "with", "within", "without", "would", "x", "y", "year", "years", "yet", "you", "young", "younger", "youngest", "your", "yours", "z", "a's", "able", "about", "above", "according", "accordingly", "across", "actually", "after", "afterwards", "again", "against", "ain't", "all", "allow", "allows", "almost", "alone", "along", "already", "also", "although", "always", "am", "among", "amongst", "an", "and", "another", "any", "anybody", "anyhow", "anyone", "anything", "anyway", "anyways", "anywhere", "apart", "appear", "appreciate", "appropriate", "are", "aren't", "around", "as", "aside", "ask", "asking", "associated", "at", "available", "away", "awfully", "be", "became", "because", "become", "becomes", "becoming", "been", "before", "beforehand", "behind", "being", "believe", "below", "beside", "besides", "best", "better", "between", "beyond", "both", "brief", "but", "by", "c'mon", "c's", "came", "can", "can't", "cannot", "cant", "cause", "causes", "certain", "certainly", "changes", "clearly", "co", "com", "come", "comes", "concerning", "consequently", "consider", "considering", "contain", "containing", "contains", "corresponding", "could", "couldn't", "course", "currently", "definitely", "described", "despite", "did", "didn't", "different", "do", "does", "doesn't", "doing", "don't", "don’t", "done", "down", "downwards", "during", "each", "edu", "eg", "eight", "either", "else", "elsewhere", "enough", "entirely", "especially", "et", "etc", "even", "ever", "every", "everybody", "everyone", "everything", "everywhere", "ex", "exactly", "example", "except", "far", "few", "fifth", "first", "five", "followed", "following", "follows", "for", "former", "formerly", "forth", "four", "from", "further", "furthermore", "get", "gets", "getting", "given", "gives", "go", "goes", "going", "gone", "got", "gotten", "greetings", "had", "hadn't", "happens", "hardly", "has", "hasn't", "have", "haven't", "having", "he", "he's", "hello", "help", "hence", "her", "here", "here's", "hereafter", "hereby", "herein", "hereupon", "hers", "herself", "hi", "him", "himself", "his", "hither", "hopefully", "how", "howbeit", "however", "i'd", "i'll", "i'm", "i've", "ie", "if", "ignored", "immediate", "in", "inasmuch", "inc", "indeed", "indicate", "indicated", "indicates", "inner", "insofar", "instead", "into", "inward", "is", "isn't", "it", "it'd", "it'll", "it's", "its", "itself", "just", "keep", "keeps", "kept", "know", "knows", "known", "last", "lately", "later", "latter", "latterly", "least", "less", "lest", "let", "let's", "like", "liked", "likely", "little", "look", "looking", "looks", "ltd", "mainly", "many", "may", "maybe", "me", "mean", "meanwhile", "merely", "might", "more", "moreover", "most", "mostly", "much", "must", "my", "myself", "name", "namely", "nd", "near", "nearly", "necessary", "need", "needs", "neither", "never", "nevertheless", "new", "next", "nine", "no", "nobody", "non", "none", "noone", "nor", "normally", "not", "nothing", "novel", "now", "nowhere", "obviously", "of", "off", "often", "oh", "ok", "okay", "old", "on", "once", "one", "ones", "only", "onto", "or", "other", "others", "otherwise", "ought", "our", "ours", "ourselves", "out", "outside", "over", "overall", "own", "particular", "particularly", "per", "perhaps", "placed", "please", "plus", "possible", "presumably", "probably", "provides", "que", "quite", "qv", "rather", "rd", "re", "really", "reasonably", "regarding", "regardless", "regards", "relatively", "respectively", "right", "said", "same", "saw", "say", "saying", "says", "second", "secondly", "see", "seeing", "seem", "seemed", "seeming", "seems", "seen", "self", "selves", "sensible", "sent", "serious", "seriously", "seven", "several", "shall", "she", "should", "shouldn't", "since", "six", "so", "some", "somebody", "somehow", "someone", "something", "sometime", "sometimes", "somewhat", "somewhere", "soon", "sorry", "specified", "specify", "specifying", "still", "sub", "such", "sup", "sure", "t's", "take", "taken", "tell", "tends", "th", "than", "thank", "thanks", "thanx", "that", "that's", "thats", "the", "their", "theirs", "them", "themselves", "then", "thence", "there", "there's", "thereafter", "thereby", "therefore", "therein", "theres", "thereupon", "these", "they", "they'd", "they'll", "they're", "they've", "think", "third", "this", "thorough", "thoroughly", "those", "though", "three", "through", "throughout", "thru", "thus", "to", "together", "too", "took", "toward", "towards", "tried", "tries", "truly", "try", "trying", "twice", "two", "un", "under", "unfortunately", "unless", "unlikely", "until", "unto", "up", "upon", "us", "use", "used", "useful", "uses", "using", "usually", "value", "various", "very", "via", "viz", "vs", "want", "wants", "was", "wasn't", "way", "we", "we'd", "we'll", "we're", "we've", "welcome", "well", "went", "were", "weren't", "what", "what's", "whatever", "when", "whence", "whenever", "where", "where's", "whereafter", "whereas", "whereby", "wherein", "whereupon", "wherever", "whether", "which", "while", "whither", "who", "who's", "whoever", "whole", "whom", "whose", "why", "will", "willing", "wish", "with", "within", "without", "won't", "wonder", "would", "would", "wouldn't", "yes", "yet", "you", "you'd", "you'll", "you're", "you've", "your", "yours", "yourself", "yourselves", "zero"];

const metaGraph = function (searchClientUid, searchText, elasticResult, callback) {
    async.auto({
        "getSearchClient": cb0 => {
            var key = "SUR_getSearchClientSettingsFromUid_"+searchClientUid;
            commonFunctions.redisCache(key, getSearchClientSettingsFromUid, searchClientUid, (e, r) => {
                if(e){
                    cb0(e, null);
                }
                else if(r.length <= 0){
                    cb0('no searchClient', null);
                }
                else{
                    cb0(null, r[0].id);
                }
            });
        },
        "metaGraphEntry": ["getSearchClient", (dataFromAbove, cb1) => {
            var searchClientId = dataFromAbove.getSearchClient;                        
            //Check for any entry in `meta_data_relation` for the serach Client id
            var key = "SUR_getMetaGraphEntryData_"+searchClientId;
            commonFunctions.redisCache(key, getMetaGraphEntryData, searchClientId, (e, r) => {
                if (e) {
                    commonFunctions.errorlogger.error(e);
                    cb1(e, null);
                }
                else if (r.length == 0) {
                    commonFunctions.errorlogger.warn("No entry in Meta Graph for this search Client");
                    cb1('no entry', []);
                }
                else {
                    commonFunctions.errorlogger.info(r);
                    cb1(null, r);
                }
            });
        }],
        "findRelation":["metaGraphEntry", (data, cb2) => {
            var matchedRelation = {};
            var match = false;
            elasticResult = JSON.parse(elasticResult);
            for (var i = 0; i < elasticResult.items.length; i++) {
                for (var j = 0; j < data.metaGraphEntry.length; j++) {
                    if (data.metaGraphEntry[j].content_source_elasticIndex == elasticResult.items[i]._index && data.metaGraphEntry[j].object_name == elasticResult.items[i]._type) {
                        if(!Array.isArray(elasticResult.items[i]._source[data.metaGraphEntry[j].field_name])){
                            temp = elasticResult.items[i]._source[data.metaGraphEntry[j].field_name].toString();
                            temp = temp.replace(/[.,\/#!$%\^&\*;:{}=\-_`'"~()]/g,"");
                            temp = temp.replace(/  +/g, ' ');
                            temp = temp.toLowerCase();
                            searchText = searchText.replace(/[.,\/#!$%\^&\*;:{}=\-_`'"~()]/g,"");
                            searchText = searchText.replace(/  +/g, ' ');
                            searchText = searchText.toLowerCase();
                            var isSame = compareTwoStrings(temp, searchText);
                            if(isSame){
                                matchedRelation = { "elasticResult": elasticResult.items[i], "metaGraphRelation": data.metaGraphEntry[j] };
                                    match = true;
                                    break;
                            }
                            // if (temp == searchText) {
                            //     matchedRelation = { "elasticResult": elasticResult.items[i], "metaGraphRelation": data.metaGraphEntry[j] };
                            //     match = true;
                            //     break;
                            // }
                        }
                    }
                }
                if (match == true)
                    break;
            }
            if(match)
                cb2(null, matchedRelation);
            else   
                cb2('not match', null);
        }],
        get_view_href:["findRelation", (data, cb) => {
            var key = "SUR_getViewHrefData_"+data.findRelation.metaGraphRelation.search_client_id+"_"+data.findRelation.metaGraphRelation.object_id;
            commonFunctions.redisCache(key,getViewHrefData, data.findRelation.metaGraphRelation.search_client_id, data.findRelation.metaGraphRelation.object_id, function(e, r){
                if(e){
                    commonFunctions.errorlogger.error(e);
                }
                else if(r.length == 0){
                    commonFunctions.errorlogger.warn("No href is found");
                    data.findRelation.metaGraphRelation['view_link'] = null;
                }
                else{
                    data.findRelation.metaGraphRelation['view_link'] = r[0].base_href;
                }
                cb(null, {});
            });
        }],
        getMetaFields: ["get_view_href", (data, cb3) => {
            var key = "SUR_getknowledgeGraphMetaFields_"+data.findRelation.metaGraphRelation.id;
            commonFunctions.redisCache(key, getknowledgeGraphMetaFields,data.findRelation.metaGraphRelation.id, function(e,r){
                if (e) {
                    commonFunctions.errorlogger.error(e);
                    cb3(null, {});
                }
                else if (r.length == 0) {
                    commonFunctions.errorlogger.warn("No entry in Meta Graph field for this search Client");
                    cb3(null, {});
                }
                else {
                    var metaFeilds = [];
                    for (var j = 0; j < r.length; j++) {
                        var temp = {};
                        temp['key'] = r[j].display_label
                        temp['value'] = Array.isArray(data.findRelation.elasticResult._source[r[j].name]) ? getArrayElement(data.findRelation.elasticResult._source[r[j].name]) : data.findRelation.elasticResult._source[r[j].name];
                        if (temp['value'] != undefined || temp['value'] != null)
                            metaFeilds.push(temp);
                    }
                    cb3(null, metaFeilds);
                }
            });
        }],
        "searchInFields": ["getMetaFields", (data, cb3) => {
            var metaObj = data.findRelation.metaGraphRelation;
	        metaObj['metaFields'] = data.getMetaFields;
            metaObj['title'] = Array.isArray(data.findRelation.elasticResult._source[data.findRelation.metaGraphRelation.title_name]) ? getArrayElement(data.findRelation.elasticResult._source[data.findRelation.metaGraphRelation.title_name]) : data.findRelation.elasticResult._source[data.findRelation.metaGraphRelation.title_name];
            metaObj['subtitle'] = Array.isArray(data.findRelation.elasticResult._source[data.findRelation.metaGraphRelation.subtitle_name]) ? getArrayElement(data.findRelation.elasticResult._source[data.findRelation.metaGraphRelation.subtitle_name]) : data.findRelation.elasticResult._source[data.findRelation.metaGraphRelation.subtitle_name];
            // metaObj['link'] = data.findRelation.elasticResult._source[data.findRelation.metaGraphRelation.link_name];
            metaObj['img'] = data.findRelation.elasticResult._source[data.findRelation.metaGraphRelation.img_name];
            metaObj['description'] = Array.isArray(data.findRelation.elasticResult._source[data.findRelation.metaGraphRelation.description_name]) ? getArrayElement(data.findRelation.elasticResult._source[data.findRelation.metaGraphRelation.description_name]) : data.findRelation.elasticResult._source[data.findRelation.metaGraphRelation.description_name];
            /** Code to get base_href from saved search clients for Objects **/
            var tempLinkUrl = metaObj.view_link;
            metaObj['link'] = tempLinkUrl;
            var testRegex = /{{.*?}}/g;
            var str = metaObj['link'];
            var m;
            while ((m = testRegex.exec(str)) !== null) {
                if (m.index === testRegex.lastIndex) {
                    testRegex.lastIndex++;
                }
                m.forEach((match, groupIndex) => {
                    metaObj["link"] = metaObj["link"].replace(match, data.findRelation.elasticResult["_source"][match.replace("{{", "").replace("}}", "")])
                    commonFunctions.errorlogger.info(match);
                });
            }
            if (!metaObj["link"])
                metaObj["link"] = data.findRelation.elasticResult["_id"] || data.findRelation.elasticResult["_source"]['id'];
            /**/
            // if (metaObj['description'] != null && metaObj['description'] != undefined) {
            //     metaObj['description'] = metaObj['description'].substr(0, 200) + "...";
            // }
            cb3(null, metaObj);
        }]
    }, (error, results) => {
        if (error) {
            callback(error, null);
        }
        else {
            callback(null, results.searchInFields);
        }
    })
}

const relatedTiles = function (searchClientUid, searchText, elasticData, permissionsArray, callback) {
    async.auto({
        "getSearchClient": cb0 => {
            var key = "SUR_getSearchClientSettingsFromUid_"+searchClientUid;
            commonFunctions.redisCache(key, getSearchClientSettingsFromUid, searchClientUid, (e, r) => {
                if(e){
                    cb0(e, null);
                }
                else if(r.length <= 0){
                    cb0('no searchClient', null);
                }
                else{
                    cb0(null, r[0].id);
                }
            });
        },
        "getSearchClientSettings": ["getSearchClient", (dataFromAbove, cb01) => {
            elasticData = JSON.parse(elasticData);
            getSearchClient(dataFromAbove.getSearchClient, function(err, data){
                if(err){
                    cb01(err, null);
                }
                else{
                    try{
                        for(var i = 0; i<elasticData.items.length; i++){
                            var x = data.sources.filter(x=>{return x.index == elasticData.items[i]._index}).map(y=>{return y.objects});
                            if(x[0].length >0){
                                x = x.reduce(commonFunctions.reduceObject).filter(b => {return b.name == elasticData.items[i]._type}).map(z => { return z.fields});
                                if(x[0].length >0){
                                    x = x.reduce(commonFunctions.reduceObject).filter(k => k.title == true); 
                                }                       
                            }
                            if(x.length> 0){
                                elasticData.items[i]['topicObject'] = x[0];
                            }
                        }
                        cb01(null, elasticData);
                    }
                    catch(e){
                        commonFunctions.errorlogger.error(e);
                        cb01(e, null);
                    }
                }
            })
        }],
        "get_kgr": ["getSearchClientSettings", (dataFromAbove, cb1) => {
            var searchClientId = dataFromAbove.getSearchClient;  
            var key = "SUR_get_kgr_knowledgegraph_"+searchClientId;
            commonFunctions.redisCache(key, get_kgr_knowledgegraph, searchClientId, (e, r) => {
                if (e) {
                    cb1(e, null);
                }
                else if (r.length <= 0) {
                    cb1('no search client', null);
                }
                else {
                    cb1(null, r);
                }
            });
        }],
        "get_kgr_fields": ["get_kgr", (dataFromAbove, cb2) => {
            var s = dataFromAbove.get_kgr[0].id;
            for (var i = 1; i < dataFromAbove.get_kgr.length; i++) {
                s = s + "," + dataFromAbove.get_kgr[i].id;
            }
            const sql = 'SELECT sctco.base_href, kgfr.id, kgfr.relation_id, kgfr.tag_line, cso1.name mapper_obj_name, cso2.name mapping_obj_name, csof1.name mapper_field_name, csof2.name as mapping_field_name, csof3.name heading_1_name, csof4.name as heading_2_name, csof5.name img_name, csof6.name as link_name FROM `k_graph_fields_relation` kgfr LEFT JOIN content_source_objects cso1 ON kgfr.mapper_obj_id = cso1.id LEFT JOIN content_source_objects cso2 ON kgfr.mapping_obj_id = cso2.id LEFT JOIN content_source_object_fields csof1 ON csof1.id = kgfr.mapper_field_id LEFT JOIN content_source_object_fields csof2 ON csof2.id = kgfr.mapping_field_id LEFT JOIN content_source_object_fields csof3 ON csof3.id = kgfr.heading_1_id LEFT JOIN content_source_object_fields csof4 ON csof4.id = kgfr.heading_2_id LEFT JOIN content_source_object_fields csof5 ON csof5.id = kgfr.img_id LEFT JOIN content_source_object_fields csof6 ON csof6.id = kgfr.link_id LEFT JOIN search_clients_to_content_objects sctco ON sctco.search_client_id = '+dataFromAbove.get_kgr[0].search_client_id+' AND sctco.content_source_object_id = kgfr.mapping_obj_id WHERE kgfr.relation_id IN (' + s + ')';
            connection.query(sql, (e, r, f) => {
                if (e) {
                    cb2(e, null);
                }
                else if (r.length <= 0) {
                    cb2('No Fields are defined for this relation', null);
                }
                else {
                    cb2(null, r);
                }
            });
        }],
        "checkInTopResult": ["get_kgr_fields", (dataFromAbove, cb3) => {
            /**
             * iterate over the array from get_kgr_fields
             * 
             * Check every element of get_kgr_fields with the elastic result items array.
             * 
             * We check the following - 
             * Check the index of result items to be same as cs_source_elasticIndexName.
             * Check the type of result items to be same as mapper_obj_name.
             * To check if the search text matches any of the result (not excatly but mostly).
             * if the above three condition get fulfilled then we set two varibles for get_kgr_fields and break.
             * 
             * 
             * status = true;
             * passResult = respective result item.
             * 
             * 
             * 
             */
            searchText = searchText.replace(/[.,\/#!$%\^&\*;:{}=\-_`'"~()]/g,"");
            searchText = searchText.replace(/  +/g, ' '); 
            searchText = searchText.toLowerCase();           
            for (var i = 0; i < dataFromAbove.get_kgr_fields.length; i++) {
                dataFromAbove.get_kgr_fields[i].status = false;                
                detailedRelationData = dataFromAbove.get_kgr.filter(x => x.id == dataFromAbove.get_kgr_fields[i].relation_id);
                for(var j =0; j< elasticData.items.length; j++){                    
                    if(elasticData.items[j]._index == detailedRelationData[0].cs_source_elasticIndexName && elasticData.items[j]._type == dataFromAbove.get_kgr_fields[i].mapper_obj_name){
                        // var x = elasticData.items[j]._source[dataFromAbove.get_kgr_fields[i].mapper_field_name];
                        // var x = elasticData.items[j]._source[dataFromAbove.getSearchClientSettings[0].name];
                        if(elasticData.items[j].topicObject){
                            var x = elasticData.items[j]._source[elasticData.items[j].topicObject.name]
                            if(!Array.isArray(x)){
                                x = x.toString();
                                x = x.replace(/[.,\/#!$%\^&\*;:{}=\-_`'"~()]/g,"");                        
                                x = x.replace(/  +/g, ' ');    
                                x = x.toLowerCase();
                                var isSame = compareTwoStrings(x, searchText); 
                                if(isSame){
                                    dataFromAbove.get_kgr_fields[i].status = true;
                                    dataFromAbove.get_kgr_fields[i].passResult = elasticData.items[j];
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            cb3(null, dataFromAbove.get_kgr_fields);
        }],
        "searchInElastic": ["checkInTopResult", (dataFromAbove, cb3) => {
            /**
             * Here we iterate over the array from checkInTopResult.
             * we only consider those kgr_fields or (checkInTopResult) for which status is true.
             * 
             */
            var asyncTask = [];
            var relatedTilesArray = [];
            for (var i=0; i < dataFromAbove.checkInTopResult.length; i++){
                asyncTask.push(function (i) {
                    return function (cb) {
                        if(dataFromAbove.checkInTopResult[i].status){
                            var temp = dataFromAbove.get_kgr.filter(x => x.id == dataFromAbove.checkInTopResult[i].relation_id);
                            // searchRelatedTilesInDest(temp[0], dataFromAbove.checkInTopResult[i], searchText, permissionsArray, function (err1, data1) {
                            var mapperFieldValue = dataFromAbove.checkInTopResult[i].passResult._source[dataFromAbove.checkInTopResult[i].mapper_field_name];    
                            searchRelatedTilesInDest(temp[0], dataFromAbove.checkInTopResult[i], mapperFieldValue, permissionsArray, function (err1, data1) {        
                                if (!err1 && data1 != null) {
                                    var len = data1.length > 4 ? 5 : data1.length;
                                    var obj = {};
                                    // obj['tagLine'] = dataFromAbove.get_kgr_fields[i].tag_line;
                                    obj['tagLine'] = parseTagLine(dataFromAbove.get_kgr_fields[i].tag_line, dataFromAbove.checkInTopResult[i].passResult._source);
                                    obj.relatedFields = [];
                                    for (var j = 0; j < len; j++) {
                                        var objFields = {};
                                        objFields['heading1'] = Array.isArray(data1[j]._source[dataFromAbove.get_kgr_fields[i].heading_1_name]) ? getArrayElement(data1[j]._source[dataFromAbove.get_kgr_fields[i].heading_1_name]) : data1[j]._source[dataFromAbove.get_kgr_fields[i].heading_1_name];
                                        objFields['heading2'] = data1[j]._source[dataFromAbove.get_kgr_fields[i].heading_2_name];
                                        objFields['img'] = data1[j]._source[dataFromAbove.get_kgr_fields[i].img_name];
                                        // objFields['link'] = data1[j]._source[dataFromAbove.get_kgr_fields[i].link_name];
                                        /**/

                                        objFields['img_source'] = data1[j]._source['thumbnail'] ? data1[j]._source['thumbnail'] : null;
                                        objFields['indexName'] = temp[0].cs_destination_name;
                                        objFields['objectName'] = dataFromAbove.checkInTopResult[i].mapper_obj_name;


                                        var tempLinkUrl = dataFromAbove.get_kgr_fields[i].base_href;
                                        objFields['link'] = tempLinkUrl;
                                        var testRegex = /{{.*?}}/g;
                                        var str = objFields['link'];
                                        var m;
                                        while ((m = testRegex.exec(str)) !== null) {
                                            if (m.index === testRegex.lastIndex) {
                                                testRegex.lastIndex++;
                                            }
                                            m.forEach((match, groupIndex) => {
                                                objFields["link"] = objFields["link"].replace(match, data1[j]._source[match.replace("{{", "").replace("}}", "")])
                                                commonFunctions.errorlogger.info(match);
                                            });
                                        }
                                        if (!objFields["link"])
                                            objFields["link"] = data1[j]["_id"] || data1[j]._source['id'];
                                        //

                                        obj.relatedFields.push(objFields);
                                    }
                                    relatedTilesArray.push(obj);
                                }
                                cb(null, '');                                                            
                            })
                        }
                        else{
                            cb(null, '');                                                                                        
                        }
                    }
                }(i));
            }
            async.series(asyncTask, function (asyncErr, asyncRes) {
                cb3(null, relatedTilesArray);
            });
        }]
    }, function (error, result) {
        if (error) {
            callback(error, null);
        }
        else {
            callback(null, result.searchInElastic);
        }
    });
}

function parseTagLine(tagLine, data){
    try{
        if((tagLine.includes("{{")) && (tagLine.includes("}}"))){
            var placeHolder = tagLine.slice(tagLine.indexOf("{{"), tagLine.indexOf("}}")+2);
            placeHolderKey = placeHolder.slice(2, placeHolder.length -2);
            placeHolderValue = data[placeHolderKey];
            if(Array.isArray(placeHolderValue)){
                placeHolderValue1 = placeHolderValue[0];
            }
            else{
                placeHolderValue1 = placeHolderValue;
            }
            tagLine = tagLine.replace(placeHolder, placeHolderValue1); 
            return parseTagLine(tagLine, data);  
        }
        else{
            return tagLine;
        }
    }
    catch(e){
        commonFunctions.errorlogger.error(e);
        return tagLine;
    }
}

function searchRelatedTilesInDest( cs_info, relation_info, mapperFieldValue, permissionsArray, callback) {
    var arr = [];
    if(!Array.isArray(mapperFieldValue)){
        arr[0] = mapperFieldValue;
    }
    else{
        arr = mapperFieldValue;
    }
    permissionsArray = JSON.parse(permissionsArray);
    var sql = "select content_source_type_id from content_sources where id = " + cs_info.cs_destination_id
    connection.query(sql, (e, r, f) => {
        if (e) {
            callback(e, null);
        }
        else if (r.length <= 0) {
            callback('no searchClient', null);
        }
        else {
            var permission = permissionsArray.items.filter(x => x.typeId == r[0].content_source_type_id);
            let client = new elasticsearch.Client({
                host: 'http://' + config.get('elasticIndexCS.host') + ':' + config.get('elasticIndexCS.port')
            });
            var query = {
                "query": {
                    "constant_score": {
                        "filter": {
                            "bool": {
                                "should": [],
                                "must":[]
                            }
                        }
                    }
                }
            }

            for(var i =0; i<arr.length ; i++){
                var temp = {
                    "multi_match": {
                        "query": arr[i],
                        "fields": [
                            relation_info.mapping_field_name
                        ],
                        "type": "phrase"
                    }
                }
                query.query.constant_score.filter.bool.should.push(temp);   
            }

            if(permission.length > 0){
                query.query.constant_score.filter.bool.must.push(permission[0].permissions);                
            }
            query = JSON.stringify(query);
            client.search({
                index: cs_info.cs_destination_elasticIndexName,
                type: relation_info.mapper_obj_name,
                body: query
            }, (error, data) => {
                if (error) {
                    console.error(error);
                    callback(error, null);
                }
                else {
                    callback(null, data.hits.hits.length > 0 ? data.hits.hits : null);
                }
            });
        }
    });
}

function getArrayElement(array){
    var arrayToString = '';
    for(var i =0 ; i< array.length; i++){
        arrayToString += array[i]+",";
    }
    arrayToString = arrayToString.slice(0, arrayToString.length -1);
    return arrayToString;
}

function compareTwoStrings(s1, s2){
    var diff = 0;    
    s1 = s1.trim();
    s2 = s2.trim();
    arr1 = s1.split(' ');
    arr2 = s2.split(' ');
    arr1 = arr1.filter(x => !stopWords.includes(x));
    arr2 = arr2.filter(x => !stopWords.includes(x));
    commonFunctions.errorlogger.info(arr1);
    commonFunctions.errorlogger.info(arr2);
    arr1Length = arr1.length;
    arr2Length = arr2.length;

    if(arr1Length> arr2Length){
        gString = arr1;
        lString = arr2;
        diff = arr1Length - arr2Length;
    }
    else{
        gString = arr2;
        lString = arr1;
        diff = arr2Length - arr1Length;
    }

    var mismatchCount =  0;
    gString.forEach(element => {
        if(!lString.includes(element))
            mismatchCount++;
    });
    if(mismatchCount != 0){
        if(gString.length <=2){
            if(mismatchCount == 0)
                return true;
            else
                return false;
        }
        else if(gString.length <= 4){
            if(mismatchCount <= 1)
                return true;
            else
                return false;
        }
        else if(gString.length <= 7){
            if(mismatchCount <=2)
                return true;
            else
                return false;
        }
        else{
            if(mismatchCount <=3)
                return true;
            else
                return false;
        }
    }
    else{
        return true;
    }
}

const getSearchClient = function (platformId, cb) {
    let settings = {};
    async.auto({
      one: cb => {
        var key="SUR_getSearcClientForKnowledgeGraph";
        commonFunctions.redisCache(key, getSearcClientForKnowledgeGraph, (e, r) => {
          if (e)
            cb(e);
          else {
            settings.sources = [];
            r.forEach(e => {
              let a = settings.sources.find(x => { return x.id == e.cs_id; });
              if (!a) {
                a = {
                  id: e.cs_id,
                  label: e.cs_label,
                  enabled: false,
                  objects: [],
                  index: e.indexName,
                  content_source_type_id: e.content_source_type_id,
                  sort_order: e.sort_order
                };
                settings.sources.push(a);
              }
              let b = a.objects.find(x => { return x.id == e.cso_id; });
              if (!b) {
                b = {
                  base_url: e.cso_base_url,
                  content_source_id: e.cso_content_source_id,
                  id: e.cso_id,
                  label: e.cso_label,
                  name: e.cso_name,
                  status: e.cso_status,
                  enabled: false,
                  internal_access: e.cso_internal_access,
                  external_access: e.cso_external_access,
                  fields: []
                };
                a.objects.push(b);
              }
              b.fields.push({
                content_source_object_id: e.csof_content_source_object_id,
                id: e.csof_id,
                isActive: e.csof_isActive,
                isFilterable: e.csof_isFilterable,
                isSearchable: e.csof_isSearchable,
                isSortable: e.csof_isSortable,
                label: e.csof_label,
                name: e.csof_name,
                type: e.csof_type,
                fragment_size: e.fragment_size,
                use: {}
              });
            });
            cb();
          }
        });
      },
      two: cb => {
        var key="SUR_relatedTilesContentObjects_"+platformId;
        commonFunctions.redisCache(key, relatedTilesContentObjects, platformId, (e, r) => {
            if (e)
              cb(e);
            else {
              cb(null, r);
            }
        });
      },
      field_mapping: cb => {
          var key="SUR_relatedTilesFieldsMapping_"+platformId;
          commonFunctions.redisCache(key, relatedTilesFieldsMapping, platformId, function(e, r){
            if (e) cb(e);
            else {
              r = r.map(en => {
                return {
                  content_source_object_field_id: en.scf_content_source_object_field_id,
                  id: en.scf_id,
                  priority: en.scf_priority,
                  search_clients_to_content_object_id: en.scf_search_clients_to_content_object_id,
                  use_as: en.scf_use_as,
                  exclude: en.scf_exclude
                };
              });
              cb(null, r);
            }
          });
      },
      metadata_mapping: cb => {
          var key="SUR_relatedTilesMetadataMapping_"+platformId;
          commonFunctions.redisCache(key, relatedTilesMetadataMapping, platformId, (e, r) => {
            if (e) cb(e);
            else {
              r = r.map(en => {
                return {
                  content_source_object_field_id: en.scmf_content_source_object_field_id,
                  id: en.scmf_id,
                  priority: en.scmf_priority,
                  search_clients_to_content_object_id: en.scmf_search_clients_to_content_object_id,
                  use_as: "Metadata"
                };
              });
              cb(null, r);
            }
          }); 
      },
      client: cb => {
        connection.query(`SELECT *,sc.id searchId,tt.id,sc.name scName,tt.name tname FROM search_clients sc LEFT JOIN template_types tt ON sc.template_type_id = tt.id WHERE sc.id=? LIMIT 1`, [platformId], (e, r, f) => {
          if (e) cb(e);
  
          else {
            r[0].id = r[0].searchId;
            r[0].name = r[0].scName
            cb(null, r);
          }
        });
      },
      deflection_formula: cb => {
        connection.query(`SELECT * FROM deflection_formula WHERE search_client_id=?`, [platformId], (e, r, f) => {
          if (e) cb(e);
          else cb(null, r);
        });
      },
      getAddonsStatus: cb => {
        commonFunctions.getAddonsStatus((error, result) => {
          if (result && result[7]) {
            cb(null, result[7].is_installed)
          } else {
            cb(null, 0)
          }
        });
      },
      three: ["one", "two", "field_mapping", "metadata_mapping", "deflection_formula", "client", "getAddonsStatus", (rs, cb) => {
        settings.sources.forEach(src => {
          src.objects.forEach(obj => {
            obj.enabled = rs.two.find(x => { return x.content_source_object_id == obj.id; }) ? true : false;
            if (obj.enabled) {
              obj.title_field_id = rs.two.find(x => { return x.content_source_object_id == obj.id; }).title_field_id;
              obj.base_href = rs.two.find(x => { return x.content_source_object_id == obj.id; }).base_href;
              obj.formula = rs.two.find(x => { return x.content_source_object_id == obj.id; }).formula;
              obj.icon = rs.two.find(x => { return x.content_source_object_id == obj.id; }).icon;
              obj.search_clients_to_content_object_id = rs.two.find(x => { return x.content_source_object_id == obj.id; }).id || null;
              obj.boosting_factor = rs.two.find(x => { return x.content_source_object_id == obj.id; }).boosting_factor,
                src.enabled = true;
            }
            obj.fields.forEach(f => {
              f.title = obj.title_field_id == f.id ? true : false
              f.use = rs.field_mapping.find(x => { return x.content_source_object_field_id == f.id; }) || {};
              f.metadata = rs.metadata_mapping.find(x => { return x.content_source_object_field_id == f.id; }) || {};
            });
          });
        });
        let caseDeflectionIndex = 0;
        
        settings.enabledObjects = rs.two;
        settings.client = rs.client[0];
        settings.field_mapping = rs.field_mapping;
        settings.deflection_formula = rs.deflection_formula[0] || {};
        cb(null, settings);
      }]
    }, (error, result) => {
      if (error)
        commonFunctions.errorlogger.error("error", error);
      else
        cb(null, settings);
    });
  };

  function getSearchClientSettingsFromUid(searchClientUid, callback){
    var sql = "SELECT * FROM `search_clients` WHERE uid ='"+ searchClientUid+"'";
    connection.query(sql, (e, r, f) => {
        callback(e,r);
    }); 
  }

  function get_kgr_knowledgegraph(searchClientId, callback){
    var sql = 'Select kgr.name, kgr.id, sc.name as search_client_name, sc.id as search_client_id, cs1.id as cs_source_id, cs1.name as cs_source_name, cs1.elasticIndexName as cs_source_elasticIndexName, cs2.elasticIndexName as cs_destination_elasticIndexName, cs2.name as cs_destination_name, cs2.id as cs_destination_id from knowledge_graph_relation kgr LEFT JOIN search_clients sc ON kgr.search_client_id = sc.id LEFT JOIN content_sources cs1 ON kgr.cs_source_id = cs1.id LEFT JOIN content_sources cs2 ON kgr.cs_destination_id = cs2.id where search_client_id = "' + searchClientId + '"';   
    connection.query(sql, (e, r, f) => {
        callback(e,r);
    });
  }

  function getViewHrefData(search_client_id, object_id, callback){
    var sql = "SELECT * from `search_clients_to_content_objects` where search_client_id =  ? AND content_source_object_id = ?";
    connection.query(sql, [search_client_id, object_id], (e, r, f) => {
        callback(e, r);
    });       
  }

  function getknowledgeGraphMetaFields(id, callback){
    var sql = "SELECT mdrf.meta_data_relation_id relation_id, mdrf.label display_label, csof.name, csof.label FROM `meta_data_relation_fields` mdrf JOIN content_source_object_fields csof ON mdrf.content_source_object_field_id = csof.id WHERE mdrf.meta_data_relation_id = ?";
    connection.query(sql, [id], (e, r, f) => {
        callback(e, r);
    });
  }

  function getSearcClientForKnowledgeGraph(callback){
      let sql = `SELECT
      cs.id    AS cs_id,
      cs.label AS cs_label,
      cs.elasticIndexName AS indexName,
      cs.content_source_type_id AS content_source_type_id,
      cs.sort_order AS sort_order,

      cso.base_url          AS cso_base_url,
      cso.content_source_id AS cso_content_source_id,
      cso.id                AS cso_id,
      cso.label             AS cso_label,
      cso.name              AS cso_name,
      cso.status            AS cso_status,
      cso.internal_access   AS cso_internal_access,
      cso.external_access   AS cso_external_access,
      csof.content_source_object_id AS csof_content_source_object_id,
      csof.id                       AS csof_id,
      csof.isActive                 AS csof_isActive,
      csof.isFilterable             AS csof_isFilterable,
      csof.isSearchable             AS csof_isSearchable,
      csof.isSortable               AS csof_isSortable,
      csof.label                    AS csof_label,
      csof.name                     AS csof_name,
      csof.type                     AS csof_type,
      csof.fragment_size            AS fragment_size
    FROM
      content_sources AS cs,
      content_source_objects AS cso,
      content_source_object_fields AS csof
    WHERE
      cs.id = cso.content_source_id
      AND cso.id = csof.content_source_object_id
    ORDER BY cs_label,csof_name ASC`;
    connection.query(sql, (e, r, f) => {
        callback(e, r);
    });
  }

  function relatedTilesContentObjects(platformId, callback){
    connection.query(`SELECT * from search_clients_to_content_objects where search_client_id=?`, [platformId], (e, r, f) => {
        callback(e,r);
    })
  }

  function relatedTilesFieldsMapping(platformId, callback){
    connection.query(`SELECT
    scf.content_source_object_field_id      AS scf_content_source_object_field_id,
    scf.id                                  AS scf_id,
    scf.priority                            AS scf_priority,
    scf.search_clients_to_content_object_id AS scf_search_clients_to_content_object_id,
    scf.use_as                              AS scf_use_as,
    scf.exclude                              AS scf_exclude
    FROM search_clients_to_content_objects AS sctco,
            search_clients_filters AS scf
    WHERE
        sctco.search_client_id = ?
        AND sctco.id = scf.search_clients_to_content_object_id`, [platformId], (e, r, f)=>{
            callback(e,r);
    });
  }

  function relatedTilesMetadataMapping(platformId, callback){
    connection.query(`SELECT
    scmf.field_id      AS scmf_content_source_object_field_id,
    scmf.id                                  AS scmf_id,
    scmf.priority                            AS scmf_priority,
    scmf.search_client_to_content_object_id AS scmf_search_clients_to_content_object_id
    FROM search_clients_to_content_objects AS sctco,
        search_client_metadata_fields AS scmf
    WHERE
        sctco.search_client_id = ?
        AND sctco.id = scmf.search_client_to_content_object_id`, [platformId], (e, r, f) =>{
            callback(e,r);
    });
  }

function getMetaGraphEntryData(searchClientId, callback) {
    var sql = 'SELECT mdr.id as id, mdr.name as name, mdr.search_client_id as search_client_id, sc.name as search_client_name, mdr.content_source_id content_source_id , cs.label content_source_label, cs.elasticIndexName content_source_elasticIndex, mdr.object_id, cso.label object_label, cso.name as object_name, mdr.field_id, csof1.name as field_name, csof1.label as field_label, mdr.title_id, csof2.name as title_name, csof2.label as title_label, mdr.subtitle_id, csof3.name as subtitle_name, csof3.label as subtitle_label, mdr.link_id, csof4.name as link_name, csof4.label as link_label, mdr.description_id, csof5.name as description_name, csof5.label as description_label, mdr.img_id, csof6.name as img_name, csof2.label as img_label, mdr.layout as layout '
        + 'from meta_data_relation as mdr '
        + 'LEFT JOIN search_clients sc ON sc.id = mdr.search_client_id '
        + 'LEFT JOIN content_sources as cs on mdr.content_source_id = cs.id '
        + 'LEFT JOIN content_source_objects cso ON cso.id = mdr.object_id '
        + 'LEFT JOIN content_source_object_fields csof1 ON csof1.id = mdr.field_id '
        + 'LEFT JOIN content_source_object_fields csof2 ON csof2.id = mdr.title_id '
        + 'LEFT JOIN content_source_object_fields csof3 ON csof3.id = mdr.subtitle_id '
        + 'LEFT JOIN content_source_object_fields csof4 ON csof4.id = mdr.link_id '
        + 'LEFT JOIN content_source_object_fields csof5 ON csof5.id = mdr.description_id '
        + 'LEFT JOIN content_source_object_fields csof6 ON csof6.id = mdr.img_id where search_client_id = ?'
    connection.query(sql, [searchClientId], (e, r, f) => {
        callback(e,r);
    });

}

module.exports = {
    router: router,
    metaGraph: metaGraph,
    relatedTiles: relatedTiles
}