var express = require('express');
var async = require('async');
var commonFunctions = require('./../../utils/commonFunctions');
var router = express.Router();
var contentTuningRouts = {};
var searchResultKafkaConfig = require('../admin/searchResultKafkaConfig');

router.get('/getAllContent', function (req, res, next) {
    /**
     * 1. fetch all idexes and
     * 2. fetch all types
     * 3. fetch all fields of all types
     */
    // connection[req.headers['tenant-id']].execute.query
    async.waterfall([
        cb => {
            connection[req.headers['tenant-id']].execute.query({
                sql: `SELECT 
                cs.id,cs.elasticIndexName,cs.label,cs.name,cs.url,
                cso.id,cso.boosting_factor,cso.label,cso.name,
                csof.id,csof.isActive,csof.isFilterable,csof.isSearchable,csof.isSortable,csof.label,csof.name,csof.single_multiple,csof.type
            FROM content_sources AS cs,
                content_source_objects AS cso,
                content_source_object_fields AS csof
            WHERE
                cs.id = cso.content_source_id
                AND cso.id = csof.content_source_object_id
            `, nestTables: true
            }, (e, r, f) => {
                if (e) cb(e);
                else {
                    let map = [];
                    r.forEach(e => {
                        let index = map.find(m => { return m.id == e.cs.id; });
                        if (!index) {
                            index = e.cs;
                            index.types = [];
                            map.push(index);
                        }
                        let type = index.types.find(t => { return t.id == e.cso.id; });
                        if (!type) {
                            type = e.cso;
                            type.fields = [];
                            index.types.push(type);
                        }
                        type.fields.push(e.csof);
                    });
                    cb(null, map);
                }
            });
        }
        /*cb=>{
            connection[req.headers['tenant-id']].execute.query(`select pci,csid, t6.name as name, index_name, content_source_table_index_field, content_type_to_sync
            from (select api_crawling_id pci, content_source_id csid from user_content_source_mapping where api_crawling_id!=0) as t5
                join (select name, index_name, id from api_crawling) as t6
                    on t5.pci=t6.id
                join  content_sources as cs
                    on t5.csid = cs.id
            union
            select pci,csid, t2.name as name, index_name, content_source_table_index_field, content_type_to_sync
            from (select public_crawler_id pci, content_source_id csid from user_content_source_mapping where public_crawler_id!=0) as t1
                join (select name,index_name, id from public_crawling) as t2
                    on t1.pci=t2.id
                join  content_sources as t3
                    on t1.csid = t3.id`,cb);
        },
        (indexes,useless,cb)=>{
            console.log(indexes.map(i=>`SELECT id, type,status,type_label,api_crawling_id,boosting_factor FROM ${i.content_type_to_sync} `).join(" UNION "));
            connection[req.headers['tenant-id']].execute.query(indexes.map(i=>`SELECT id, type,status,type_label,api_crawling_id,boosting_factor FROM ${i.content_type_to_sync} `).join(" UNION "), (error,rows)=>{
                if(error)
                    cb(error);
                var contentTypes = {};
                rows.forEach(r=>{
                    if(!contentTypes[r.api_crawling_id]) contentTypes[r.api_crawling_id]= [];
                    contentTypes[r.api_crawling_id].push(r);
                });
                indexes.forEach(idx=>{
                    idx.contentTypes = contentTypes[idx.pci] || [{
                        "type": "doc",
                        "status": 1,
                        "type_label": "Doc"
                    }];
                });
                cb(null,indexes);
            });
        },
        (indexes,cb)=>{
            connection[req.headers['tenant-id']].execute.query(indexes.map(i=>`SELECT id, field_name, field_label, field_weight,type, ${(i.csid==-1)?'public_crawler_id':'api_crawling_id'} as pci FROM ${i.content_source_table_index_field} WHERE field_type = 'string' `).join(" UNION "), (error, rows)=>{
                if(error)
                    cb(error);
                else{
                    var contentFields = {};
                    rows.forEach(r=>{
                        if(!contentFields[r.pci]) contentFields[r.pci] ={};
                        if(!contentFields[r.pci][r.type]) contentFields[r.pci][r.type] =[];
                        contentFields[r.pci][r.type].push(r);
                    });
                    indexes.forEach(idx=>{
                        idx.contentTypes.forEach(t=>{
                            try{
                                t.fields = contentFields[idx.pci][t.id];
                            }catch(ex){
                                console.log(idx)
                                console.log(t)
                            }
                        });
                    });
                    cb(null,indexes);
                }
            });
        }*/
    ], (error, result) => {
        if (error)
            commonFunctions.errorlogger.error("Error", error);
        else {
            res.send(result);
        }
    });
});

router.post('/changeTypeBoosting', function (req, res, next) {
    /**
     * POST /contentTuning/getAllContent -d'{"content_type_to_sync":"lithium_content_types_to_sync","id":5,"boosting_factor":1.23}'
     * Update boosting factor for type
     * required: content_type_to_sync, id, boosting_factor
     */
     connection[req.headers['tenant-id']].execute.query("UPDATE search_clients_to_content_objects SET `boosting_factor` = ? WHERE content_source_object_id = ? AND search_client_id=?", [req.body.boosting_factor, req.body.id, req.body.search_client_id], (error, result) => {
        if (error) {
            res.status(422).send(error);
        }
        else {
            let configObj = { "platformId": req.body.search_client_id }
            searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
                res.send(result);
            });
        }
    });
});

router.post('/changeFieldBoosting', function (req, res, next) {
    /**
     * POST /contentTuning/changeFieldBoosting -d'{"content_source_table_index_field":"content_source_lithium_index_fields","id":5,"field_weight":1.23}'
     * update field_weight
     * required [content_source_table_index_field, id]
     */
    async.series(req.body.map(f => {
        return cb => { connection[req.headers['tenant-id']].execute.query("UPDATE ?? SET `field_weight` = ? WHERE id = ?", [f.content_source_table_index_field, f.field_weight, f.id], cb); };
    }), (error, results) => {
        if (error) {
            res.status(422).send(error);
        }
        else {
            res.send(results);
        }
    });
});

// Enable Custom Boosting for a particular search client
router.get("/customBoostingCheck", function(req,res,next) {
    var search_client_id = req.query.search_client_id;
    connection[req.headers['tenant-id']].execute.query("Update `custom_boosting` set custom_boosting_enabled ='1' where search_client_id =?",[search_client_id], (error, results) => {
        if (error){
            console.error(error);
            res.send({"status":"Failed"});
        }
        else{
            let configObj = { "platformId": search_client_id };
            searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
                res.send({"status": "ok"});
            });
        }
    })
});

// Disable Custom Boosting for a particular search client
router.post("/customBoostingDisable", function(req, res, next) {
    var search_client_id = req.body.searchClientId;
    connection[req.headers['tenant-id']].execute.query("Update `custom_boosting` set custom_boosting_enabled ='0' where search_client_id =?",[search_client_id], (error, results) => {
        if (error){
            console.error(error);
            res.send(error);
        }
        else{ 
            let configObj = { "platformId": search_client_id }
            searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
                res.send({"status": "ok"});
            });
        }
    });
    
});

router.post("/saveCustomBoosting",function(req,res,next){
    var DataToSave = req.body.customBoostParam;
    var asyncTasks = [];
    var search_client_id = req.body.search_client_id;
    for (var i = 0; i < DataToSave.length; i++) {
        var custom_boosting_enabled = (DataToSave[i].custom_boosting_enabled ? 1 : 0);
        var title_boosting_factor = DataToSave[i].title_boosting_factor;
        var solved_boosting_factor = DataToSave[i].solved_boosting_factor;
        var click_boosting_base = DataToSave[i].click_boosting_base;
        var offset = DataToSave[i].offset;
        var scale = DataToSave[i].scale;
        var decay_rate = DataToSave[i].decay_rate;
        asyncTasks.push(saveCustomBoosting.bind(null,DataToSave[i], search_client_id,req));
    }
   
    async.series(asyncTasks, function (err,result) {
        if (err) {
          res.send({ "status": 401, "message": 'Save records failed' });
        }
        else {
            let configObj = { "platformId": search_client_id }
            searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
                res.send({ "status": 200, "message": 'Successfully saves records' });
            });
        }
      });
})

// function saveCustomBoosting(dataObject, search_client_id,cb){
//     connection[req.headers['tenant-id']].execute.query(`SELECT * FROM custom_boosting WHERE search_client_id =?`, [search_client_id], (err, response) => {
//         if (!err && (response.length == 0)) {
//            if (search_client_id) {
//                   var sqlInsert ="UPDATE `custom_boosting` SET `id`= VALUES(id),`custom_boosting_enabled`= VALUES(custom_boosting_enabled), `title_boosting_factor`= VALUES(title_boosting_factor), `solved_boosting_factor`= VALUES(solved_boosting_factor),`click_boosting_base`= VALUES(click_boosting_base),`offset`=VALUES(offset),`scale`=VALUES(scale),`decay_rate`=VALUES(decay_rate),`search_client_id`=VALUES(search_client_id)";
//                   connection[req.headers['tenant-id']].execute.query(sqlInsert,[dataObject.id,dataObject.custom_boosting_enabled,dataObject.title_boosting_factor,dataObject.solved_boosting_factor,dataObject.click_boosting_base,dataObject.offset,dataObject.scale,dataObject.decay_rate,search_client_id],function (errInsert, rowsInsert) {
//                     if (errInsert) {
//                       console.log("error while updating and inserting ", errInsert);
//                       cb(errInsert);
//                     } else {
//                       cb(null, rowsInsert);
//                     }
//                   });
//                 }
//                 else{ connection[req.headers['tenant-id']].execute.query("INSERT INTO `custom_boosting` (`id`,`custom_boosting_enabled`, `title_boosting_factor`, `solved_boosting_factor`, `click_boosting_base`,`offset`,`scale`,`decay_rate`,`search_client_id`)"+
//                 "VALUES (?,?, ?, ?, ?, ?, ?,?,?)",[dataObject.id,dataObject.custom_boosting_enabled,dataObject.title_boosting_factor,dataObject.solved_boosting_factor,dataObject.click_boosting_base,dataObject.offset,dataObject.scale,dataObject.decay_rate,search_client_id],function (errInsert, rowsInsert){
//                     if (errInsert) {
//                         console.log("error while updating and inserting ", errInsert);
//                         cb(errInsert);
//                       } else {
//                         cb(null, rowsInsert);
//                       }
//                  });
//                 }
               
//               }
//           })
// }
// function saveCustomBoosting(dataObject, search_client_id,cb){
//       connection[req.headers['tenant-id']].execute.query(`SELECT * FROM custom_boosting WHERE search_client_id =?`, [search_client_id], (err, response) => {
//         if (err)
//         console.error(err);
//         else{
//             var sqlInsert = ("INSERT INTO `custom_boosting` (`id`,`custom_boosting_enabled`, `title_boosting_factor`, `solved_boosting_factor`, `click_boosting_base`,`offset`,`scale`,`decay_rate`,`search_client_id`)"+
//             "VALUES (?,?, ?, ?, ?, ?, ?,?,?) ON DUPLICATE KEY UPDATE `id`= VALUES(id),`custom_boosting_enabled`=VALUES(custom_boosting_enabled), `title_boosting_factor`= VALUES(title_boosting_factor), `solved_boosting_factor`= VALUES(solved_boosting_factor),`click_boosting_base`= VALUES(click_boosting_base),`offset`=VALUES(offset),`scale`=VALUES(scale),`decay_rate`=VALUES(decay_rate),`search_client_id`=VALUES(search_client_id);")
//             connection[req.headers['tenant-id']].execute.query(sqlInsert,[dataObject.id,dataObject.custom_boosting_enabled,dataObject.title_boosting_factor,dataObject.solved_boosting_factor,dataObject.click_boosting_base,dataObject.offset,dataObject.scale,dataObject.decay_rate,search_client_id],function (errInsert, rowsInsert){
//                if (errInsert) {
//                    console.log("error while updating and inserting ", errInsert);
//                    cb(errInsert);
//                  } else {
//                    cb(null, rowsInsert);
//                  }
//             })
//             res.send(response);
//         }
//     })
// }
function saveCustomBoosting(dataObject, search_client_id,req,cb){
    connection[req.headers['tenant-id']].execute.query(`SELECT * FROM custom_boosting WHERE search_client_id =?`, [search_client_id], (err, response) => {
      if (err)
      console.error(err);
      else{
          var sqlInsert ="";
          if(response.length && dataObject.custom_boosting_enabled == 1){
            sqlInsert = "UPDATE `custom_boosting` SET `title_boosting_factor`= ?, `solved_boosting_factor`= ?, `click_boosting_enabled`= ?,`click_boosting_base`= ?,`date_boosting_enabled`= ?,`offset`= ?,`scale`= ?,`decay_rate`= ?,`attached_articles_enabled`= ?,`attached_articles_base`=?, `common_click_score_enabled`=? WHERE `search_client_id`= ?";
            connection[req.headers['tenant-id']].execute.query(sqlInsert,[dataObject.title_boosting_factor,dataObject.solved_boosting_factor,dataObject.click_boosting_enabled,dataObject.click_boosting_base,dataObject.date_boosting_enabled,dataObject.offset,dataObject.scale,dataObject.decay_rate,dataObject.attached_articles_enabled,dataObject.attached_articles_base,dataObject.common_click_score_enabled,search_client_id],function (errInsert, rowsInsert){
                if (errInsert) {
                    console.log("error while updating and inserting ", errInsert);
                    cb(errInsert,null);
                  } else {
                    cb(null, rowsInsert);
                  }
             })
          }else{
          sqlInsert = "INSERT INTO `custom_boosting` (`id`,`custom_boosting_enabled`, `title_boosting_factor`, `solved_boosting_factor`, `click_boosting_enabled`, `click_boosting_base`, `date_boosting_enabled`, `offset`,`scale`,`decay_rate`,`search_client_id`,`attached_articles_enabled`,`attached_articles_base`,`common_click_score_enabled`)"+
          "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            connection[req.headers['tenant-id']].execute.query(sqlInsert,[dataObject.id,dataObject.custom_boosting_enabled,dataObject.title_boosting_factor,dataObject.solved_boosting_factor,dataObject.click_boosting_enabled,dataObject.click_boosting_base,dataObject.date_boosting_enabled,dataObject.offset,dataObject.scale,dataObject.decay_rate,search_client_id,dataObject.attached_articles_enabled,dataObject.attached_articles_base, dataObject.common_click_score_enabled],function (errInsert, rowsInsert){
                if (errInsert) {
                    console.log("error while updating and inserting ", errInsert);
                    cb(errInsert,null);
                } else {
                    cb(null, rowsInsert);
                }
            })
          }
      }
  })
}
   
router.post("/getCustomBoosting", function (req, res) {

    var search_client_id = req.body.search_client_id;
    connection[req.headers['tenant-id']].execute.query("SELECT * FROM `custom_boosting` WHERE  search_client_id = ?", [search_client_id], (error, results, fields) => {
        if (error)
            console.error(error);
        else
            res.send(results);
    });
});


router.post("/getContentAndKeywordBoosting", function (req, res) {
    /**
     * This code is used to get the values of content and keyword boosting.
     */
    connection[req.headers['tenant-id']].execute.query("SELECT * FROM `boosting_custom`", (error, results, fields) => {
        if (error)
            console.error(error);
        else
            res.send(results);
    });
});
router.post("/updateBoosting", function (req, res) {
    /**
     * By: Vikash
     * This code is only for successfactor, But we need to include this in core product in future.
     */
    async.parallel(req.body.map(r => {
        return cb => {
            connection[req.headers['tenant-id']].execute.query("UPDATE `boosting_custom` SET `value` = ? WHERE `boosting_custom`.`id` = ?", [r.value, r.id], (error, results, fields) => {
                if (error)
                    cb(error);
                else
                    cb();
            });
        };
    }), (error, results) => {
        if (error)
            console.error(error);
        else
            res.send({ "updated": "true" });
    });
});

router.post("/updateCustomBoosting", function (req, res) {
    /**
     * By: Vikash
     * This code is only for successfactor, But we need to include this in core product in future.
     */
    async.parallel(req.body.map(r => {
        return cb => {
            connection[req.headers['tenant-id']].execute.query("UPDATE `boosting_custom` SET `value` = ? WHERE `boosting_custom`.`id` = ?", [r.value, r.id], (error, results, fields) => {
                if (error)
                    cb(error);
                else
                    cb();
            });
        };
    }), (error, results) => {
        if (error)
            console.error(error);
        else
            res.send({ "updated": "true" });
    });
});

router.post("/getFieldBoosting", function (req, res) {

    var search_client_id = req.body.search_client_id;
    connection[req.headers['tenant-id']].execute.query(`select
        fs.id,
        fs.name,
        fs.content_source_object_field_id,
        fs.boosting_factor,
        fs.search_client_id,
        fs.sort_value AS sortValue ,
        fs.is_sort AS isSort
    from
        field_boosting fs
    where
    fs.search_client_id = ?`,
        [search_client_id],
        (error, results, fields) => {
            if (error) {
                console.error(error);
            } else {
                connection[req.headers['tenant-id']].execute.query(
                    `select boosting_factor , content_source_object_id as id ,search_client_id , content_source_objects.name as name from search_clients_to_content_objects INNER JOIN content_source_objects ON search_clients_to_content_objects.content_source_object_id = content_source_objects.id WHERE search_client_id=?`,
                    [search_client_id],
                    (error, results1, fields) => {
                        if (error) {
                            console.error(error);
                        }else{
                            res.send({
                                result:results,
                                object:results1,
                            });
                        }
                    }
                );
            }
        }
    );
});

router.post("/deleteFieldBoosting", function (req, res) {

    var fieldBoostRow = req.body.fieldBoostRow;
    connection[req.headers['tenant-id']].execute.query(`delete from field_boosting where name = ? and search_client_id = ?;`, [fieldBoostRow.name, fieldBoostRow.search_client_id], (error, results, fields) => {
        if (error)
            console.error(error);
        else
            res.send(results);
    });
});


router.post("/updateFieldBoostingRow", function (req, res) {
    var fieldBoostRow = req.body.fieldBoostRow;
    connection[req.headers['tenant-id']].execute.query(`Insert into field_boosting (id,content_source_object_field_id,search_client_id, name, boosting_factor)
    VALUES(?,?,?,?,?) ON DUPLICATE KEY UPDATE id = VALUES(id) ,content_source_object_field_id = VALUES(content_source_object_field_id) , name=VALUES(name), boosting_factor = VALUES(boosting_factor)`, [fieldBoostRow.id|| null,fieldBoostRow.content_source_object_field_id,fieldBoostRow.search_client_id, fieldBoostRow.name, fieldBoostRow.boosting_factor], (error, result, fields) => {
        if (error)
            console.error(error);
        else{
            let configObj = { "platformId":fieldBoostRow.search_client_id }
            searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
                res.send({ 
                    id: result.insertId, 
                    content_source_object_field_id: fieldBoostRow.content_source_object_field_id 
                });
            });
        }  
    });
});

module.exports = router;
