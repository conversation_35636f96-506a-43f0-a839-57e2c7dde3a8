/*

created by <PERSON><PERSON><PERSON>

 */



var csv2json = require('csv2json');
var fs = require('fs');
const StreamArray = require('stream-json/streamers/StreamArray');
var elasticsearch = require('elasticsearch');



function readCsv(objectName,callback) {
  var csvFilePath= path.join(__dirname,"csvDir/"+objectName+".csv")
  fs.createReadStream(csvFilePath)
    .pipe(csv2json({separator: ','}))
    .pipe(fs.createWriteStream(path.join(__dirname,"jsonDir/",objectName+'.json')))
    .on('finish', () => callback(null,"done"))
  // Defaults to comma.
  // separator: ';'1``AWQS2

}



//convert json to multiple json files
function breakJsonToMultipleFiles(objectName,cb) {
  const jsonStream = StreamArray.withParser();

//You'll get json objects here
//Key is an array-index here

  var jsonData=[]
  var count=1
  jsonStream.on('data', ({key, value}) => {
    value = checkForArticleType(objectName, value)
      jsonData.push(value)
    if(jsonData.length==1000)
    {
      jsonStream.pause()
      createJsonFile(objectName,jsonData,count,function (cbOut) {
        jsonData=[]
        jsonStream.resume()
        count ++;
        console.log("file "+count)
      })
    }
  });

  jsonStream.on('end', () => {
    createJsonFile(objectName,jsonData,count,function (cbOut) {
      jsonData=[]
      console.log('All files creation done');
      cb(null)
    })

  });

  const filename = path.join(__dirname, 'jsonDir/'+objectName+'.json');
  fs.createReadStream(filename).pipe(jsonStream.input);
}

function createJsonFile(objectName,jsonData,fileNumber,cb) {
  const filename = path.join(__dirname, 'jsonDir/'+objectName+'_bulk_'+fileNumber+".json");
  fs.writeFile(filename,  JSON.stringify(jsonData, null, 4), 'utf8', cb);
}


/**
 * Created by manpreet on 16/12/17.
 */
var path = require('path');
environment = require('./../environment');
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, './../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
var async = require('async');
var request = require("request");
var fs = require('fs');
var jsforce = require('jsforce');
var commonFunctions = require('./../../utils/commonFunctions');
var connection_sql = require('./../../utils/connection');
const { spawn } = require('child_process');
var csv = require("fast-csv");
var striptags = require('striptags');
var clientNew = new elasticsearch.Client({
  host: config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port'),
  log: [{
    type: 'stdio',
    levels: ['error'] // change these options
  }]
});

process.on('uncaughtException', (err) => {
  console.error('whoops! there was an error', err.stack);
});
//SQL issue
// specific to run crawler seperately
var salesforceCrawl = {
  get runCrawlerSalesforce() {

    if (process.argv[2])
      connection_sql.handleDisconnect().then(function (result) {
        let pid = 0;
        //   process.argv[3]=126
        async.auto({
          start_crawler: function (cb) {
            if (process.argv[2] == "reindexing") {


              salesforceCrawl.SalesforceSpecificCrawl(process.argv[2], process.argv[3], function (err, result) {
                //console.log("crawling se bahar aa gya ", err + " updating process id")
                cb(null, "Refresh")
              })
            }
            else if (process.argv[2] == "update") {
              salesforceCrawl.fetchDataSalesforce(0, process.argv[3], 1, function (err, result) {
                // console.log("crawling se bahar aa gya ", err + " updating process id")
                cb(null, "Update")
              })
            }
            else if (process.argv[2] == "createMapping") {
              commonFunctions.createMapping(process.argv[3], 0, function (err, result) {
                //console.log("crawling se bahar aa gya ", err + " updating process id")
                cb(null, "Update")
              })
            }
            else
              cb("Parent Runnig", [])
          },
          get_content_source_data: function (cb) {
            commonFunctions.getContentSourceDataById(process.argv[3], function (err, contentSourceData) {
              if (!err)

                cb(null, contentSourceData)
              else
                cb(err, [])
            });
          },
          update_crawler_id: ['get_content_source_data', 'start_crawler', function (dataFromAbove, cb) {
            pid = dataFromAbove.get_content_source_data.contentSource.pid
            dataFromAbove.get_content_source_data.contentSource.pid = 0;
            dataFromAbove.get_content_source_data.contentSource.last_sync_date = new Date().toISOString()
            commonFunctions.insertUpdateContentSource(dataFromAbove.get_content_source_data.contentSource, cb)
          }],
          reindexing: ['get_content_source_data', 'start_crawler', function (dataFromAbove, cb) {
            if (process.argv[2] == "none") {
              var indexName = dataFromAbove.get_content_source_data.contentSource.elasticIndexName;
              var tempIndex = indexName + "_temp"
              commonFunctions.deleteMapping(indexName, function (err) {
                commonFunctions.createMapping(process.argv[3], 0, function (MappingErr, MappingRsult) {
                  commonFunctions.reIndex(indexName, tempIndex, function (reIndexErr, reIndexRes) {
                    if (reIndexErr)
                      console.error(reIndexErr)
                    cb(null)
                  })
                })
              })
            }
            else {
              cb(null)
            }

          }],

        }, function (err, result) {
          if (err)
            console.log(err);
          else {
            commonFunctions.uploadClickBoostingScoresAfterCrawling(result.get_content_source_data.contentSource.elasticIndexName, function (err, result) {
              if (err) {
                console.error(err);
              } else {
                console.log(result);

              }
              console.log("Done with PID" + pid);
              spawn('kill', ['-9', pid]);
            });
          }
        });
      });

  },
};





salesforceCrawl.fetchSalesforceData = function (status, connSF, contentSource, contentSourceObject, limit, offset, allRecords, AllCategories, allTopics, isCron, callback) {
  var objectName= contentSourceObject.name.toLowerCase()
  readCsvToJson(objectName,0,function (err,filesNameArray){

   var taskCsv = []

    //*************************
    //filesNameArray=[]
    //*************************

        for (var i = 0; i < filesNameArray.length; i++) {
      //    for (var i = 0; i < 1; i++) {
          taskCsv.push((function (i) {
            return function (cb) {
              console.log("reading file ",i)
              fs.readFile(path.join(__dirname, 'jsonDir/'+objectName+'_bulk_'+(i+1)+".json"), 'utf-8', (err, data) => {
                if (err) callback(err)
                else {
                  fs.unlinkSync(path.join(__dirname, 'jsonDir/'+objectName+'_bulk_'+(i+1)+".json"))
                  allRecords = JSON.parse(data)
                  intermediateFunction(status, connSF, contentSource, contentSourceObject, allRecords, AllCategories, allTopics, cb)
                  //cb(null)
                }
              })
            }
          })(i))
        }
        async.series(taskCsv, function (err, result) {
          console.log("done with main object data")

          taskCsv=[]
          async.series([
           // function for comment sync
            function (cbc) {
              if (contentSourceObject.name.slice(-5) != "__kav")
               setTimeout(function (cb) {
               readExtraFieldsCsv(contentSourceObject,contentSource,1,allTopics,AllCategories,cbc)
               },30)
              else cbc(null)
            },
            function (cbc) {
              if (contentSourceObject.name.slice(-5) == "__kav")
                setTimeout(function (cb) { // navigational topic sync in flat
                readExtraFieldsCsv(contentSourceObject,contentSource,4,allTopics,AllCategories,cbc)
                },30)
              else cbc(null)
            },
            function (cbc) {
              if (contentSourceObject.name.slice(-5) == "__kav")
                setTimeout(function (cb) { // category sync in flat
                readExtraFieldsCsv(contentSourceObject,contentSource,5,allTopics,AllCategories,cbc)
                },30)
              else cbc(null)
            },
            //for nevigationaltopic Sync
            function (cbc) {
              if (contentSourceObject.name.slice(-5) == "__kav")
                setTimeout(function (cb) { // navigational topic update in nested
                nestedTopics(contentSourceObject,contentSource,allTopics,AllCategories,cbc)
                },3000)
              else cbc(null)
            },
            //for nestedCategories Sync
            function (cbc) {
              if (contentSourceObject.name.slice(-5) == "__kav")
                setTimeout(function (cb) { // category update in nested
                nestedCategories(contentSourceObject,contentSource,allTopics,AllCategories,cbc)
                },3000)
              else cbc(null)
            }
          ],function (err,result) {
            callback(null,"done")
          })

        })
      })
}


function nestedTopics(contentSourceObject,contentSource,allTopics,AllCategories,cb)
{
  let nestedFields = contentSourceObject.fields.filter(f => f.name.slice(-11) == "_navigation")
  let dataCount=0
  let option ={
    index: contentSource.contentSource.elasticIndexName,
    scroll:'10s',
    size: 1000,
    type:contentSourceObject.name,
    body: {
      "query": {
        "match_all": {}
      }
     // ,"_source":nestedFields.map(x=>{return x.name+"_backup"}).concat(['_id','KnowledgeArticleId'])
    }
  }
  clientNew.search(option,function getMoreUntilDone(error, response) {
    // collect the title from each response
    if(!error && response.hits.hits.length)
    {
      dataCount+=response.hits.hits.length
      updateNestedTopics(response.hits.hits,nestedFields,contentSourceObject,contentSource,allTopics,AllCategories,function (responseData) {
        if (response.hits.total !== dataCount) {
          var scroll_id=response._scroll_id
          response=""
          // ask elasticsearch for the next set of hits from this search
          clientNew.scroll({
            scrollId: scroll_id,
            scroll: '30s'
          }, getMoreUntilDone);
        }
        else {
          cb(null)
        }
      })
    }

  });
}

function nestedCategories(contentSourceObject,contentSource,allTopics,AllCategories,cb)
{
  let nestedFields = contentSourceObject.fields.filter(f => f.name.slice(-7) == "_nested")
  let dataCount=0
  let option ={
    index: contentSource.contentSource.elasticIndexName,
    scroll:'10s',
    size: 1000,
    type:contentSourceObject.name,
    body: {
      "query": {
        "match_all": {}
      }
      // ,"_source":nestedFields.map(x=>{return x.name+"_backup"}).concat(['_id','KnowledgeArticleId'])
    }
  }
  clientNew.search(option,function getMoreUntilDone(error, response) {
    // collect the title from each response
    if(!error && response.hits.hits.length)
    {
      dataCount+=response.hits.hits.length
      updateNestedCategories(response.hits.hits,nestedFields,contentSourceObject,contentSource,allTopics,AllCategories,function (responseData) {
        if (response.hits.total !== dataCount) {
          var scroll_id=response._scroll_id
          response=""
          // ask elasticsearch for the next set of hits from this search
          clientNew.scroll({
            scrollId: scroll_id,
            scroll: '30s'
          }, getMoreUntilDone);
        }
        else {
          cb(null)
        }
      })
    }

  });
}

function updateNestedTopics(records,nestedFields,contentSourceObject,contentSource,allTopics,AllCategories,cb)
{
  //SELECT EntityId,Topic.Name,NetworkId FROM TopicAssignment where EntityId IN
  let finalString=""
  let deleteString=""
  let nestedFieldsBckup=nestedFields.map(x=>{return x.name+"_backup"})
  for (let rc = 0; rc < records.length; rc++) {
    let prefix = ""
    for (let f=0;f<nestedFieldsBckup.length;f++)
    {
      let topicsArray=records[rc]['_source'][nestedFieldsBckup[f]]?records[rc]['_source'][nestedFieldsBckup[f]].split(",").filter(x=>x):[]
      let expectedFieldName=nestedFieldsBckup[f].replace('_backup','')
      let topicGroup = allTopics.find(y => y.community.name == expectedFieldName)
      let jsonArray =[]
      topicsArray.forEach(y=>{
        jsonArray.push({ [prefix + expectedFieldName]: salesforceCrawl.createNestedObjectTopic(topicGroup.topics, prefix + topicGroup.community.name, y, { "count": 1 }, { "found": false }, '_') })
      })
      records[rc]['_source'][expectedFieldName] = salesforceCrawl.mergeArray(jsonArray, topicGroup.community.name, "") ? salesforceCrawl.mergeArray(jsonArray, topicGroup.community.name, "")[topicGroup.community.name] : []
      records[rc]['_source'][expectedFieldName.replace('_navigation','_flat')]=topicsArray
      delete records[rc]['_source'][nestedFieldsBckup[f]]
    }


    let insert = {
      "index": {
       // _id: records[rc]['_source']['KnowledgeArticleId'],
        _id: records[rc]['_id'],
        _index: contentSource.contentSource.elasticIndexName,
        _type: contentSourceObject.name
      }
    }
    let deleteIds={
      "delete": {
        _id: records[rc]['_id'],
        _index: contentSource.contentSource.elasticIndexName,
        _type: contentSourceObject.name
      }
    }

    finalString += JSON.stringify(insert) + "\n" + JSON.stringify(records[rc]['_source']) + "\n"
    deleteString+= JSON.stringify(deleteIds) + "\n"

  }

  if (records.length > 0) {
    commonFunctions.BulkUpload(finalString, function (err, data) {
     // commonFunctions.BulkUpload(deleteString, function (err, data) {
        cb(null, finalString);
      //})
    })
  }
  else {
    cb(null, finalString)
  }
}

function updateNestedCategories(records,nestedFields,contentSourceObject,contentSource,allTopics,AllCategories,cb)
{
  //SELECT EntityId,Topic.Name,NetworkId FROM TopicAssignment where EntityId IN
  let finalString=""
  let deleteString=""
  let nestedFieldsBckup=nestedFields.map(x=>{return x.name+"_backup"})
  for (let rc = 0; rc < records.length; rc++) {
    let prefix = ""
    for (let f=0;f<nestedFieldsBckup.length;f++)
    {
      let categoryArray=records[rc]['_source'][nestedFieldsBckup[f]]?records[rc]['_source'][nestedFieldsBckup[f]].split(",").filter(x=>x):[]
      let expectedFieldName=nestedFieldsBckup[f].replace('_backup','')
      let categoryGroup = AllCategories.find(y => y.name+'_category_nested' == expectedFieldName)
      let jsonArray =[]
      if(categoryGroup)
      categoryArray.forEach(y=>{
        jsonArray.push({ [prefix + expectedFieldName]: salesforceCrawl.createNestedObject(categoryGroup.topCategories, prefix + categoryGroup.name, y, { "count": 1 }, { "found": false }, '_category_nested_') })
      })
      if(categoryGroup)
      records[rc]['_source'][expectedFieldName] = salesforceCrawl.mergeArray(jsonArray, categoryGroup.name, "_category_nested") ? salesforceCrawl.mergeArray(jsonArray, categoryGroup.name, "_category_nested")[categoryGroup.name+"_category_nested"] : []
      records[rc]['_source'][expectedFieldName.replace('_nested','_flat')]=categoryArray
      delete records[rc]['_source'][nestedFieldsBckup[f]]
    }


    let insert = {
      "index": {
        _id: records[rc]['_source']['KnowledgeArticleId'],
        _index: contentSource.contentSource.elasticIndexName,
        _type: contentSourceObject.name
      }
    }
    let deleteIds={
      "delete": {
        _id: records[rc]['_id'],
        _index: contentSource.contentSource.elasticIndexName,
        _type: contentSourceObject.name
      }
    }

    finalString += JSON.stringify(insert) + "\n" + JSON.stringify(records[rc]['_source']) + "\n"
    deleteString+= JSON.stringify(deleteIds) + "\n"

  }

  if (records.length > 0) {
    commonFunctions.BulkUpload(finalString, function (err, data) {
      commonFunctions.BulkUpload(deleteString, function (err, data) {
        cb(null, finalString);
      })
    })
  }
  else {
    cb(null, finalString)
  }
}


function readCsvToJson(objectName,checkIfFileExist,callback)
{
  if(checkIfFileExist)
    callback(null,fs.readdirSync(path.join(__dirname, 'jsonDir')).filter(fn => fn.startsWith(objectName + '_bulk_')))
  else
  readCsv(objectName,function (dataJson) {

    breakJsonToMultipleFiles(objectName,function (result) {

      //read files one by one
      var filesNameArray = fs.readdirSync(path.join(__dirname, 'jsonDir')).filter(fn => fn.startsWith(objectName + '_bulk_'));
      callback(null,filesNameArray)
    })
  })
}



function readExtraFieldsCsv(contentSourceObject,contentSource,type,allTopics,AllCategories,callback)
{
  let objectName=contentSourceObject.name
  switch(type){
    case 1: objectName = objectName+"Comment"
          break;
    case 2: objectName = objectName+"Share"
          break;
    //SELECT EntityId,Topic.Name,NetworkId FROM TopicAssignment where entityType= object Name
    case 4:
      objectName = objectName+"_topicNavigational"
          break;
    case 5:
      objectName = objectName+"_nestedCategory"
      break;
    default: break;
  }

  readCsvToJson(objectName,0,function (err,filesNameArray){

    var taskCsv = []
    for (var i = 0; i < filesNameArray.length; i++) {
      taskCsv.push((function (i) {
        return function (cb) {
          console.log("reading file ",i)
          fs.readFile(path.join(__dirname, 'jsonDir/'+objectName+'_bulk_'+(i+1)+".json"), 'utf-8', (err, jsonData) => {
            if (err) callback(err)
            else {
              fs.unlinkSync(path.join(__dirname, 'jsonDir/'+objectName+'_bulk_'+(i+1)+".json"))
              jsonData = JSON.parse(jsonData)
             switch(type)
             {
               // for Comment sync important fields are commentBody, createdby.email, createdDate
               case 1: updateCommentWithParentId(jsonData,contentSource,contentSourceObject,type,(resultData)=>{
                 //taskCsv.splice(i)
                 cb(null)
               })
                  break;
               case 4:  updateNavigationalTopics(jsonData,contentSource,contentSourceObject,allTopics,(resultData)=>{
                 //taskCsv.splice(i)
                 cb(null)
               })
                 break;
               case 5: updateCategories(jsonData,contentSource,contentSourceObject,AllCategories,(resultData)=>{
                 //taskCsv.splice(i)
                 cb(null)
               })
                 break;
               default:break;

             }
            }
          })
        }
      })(i))
    }
    async.series(taskCsv, function (err, result) {
      taskCsv=[]
      console.log("done with comments")
      callback(null,"done")
    })
  })
}

function updateCommentWithParentId(records,contentSource,contentSourceObject,type,callback)
{
  let key=""
  switch(type)
  {
    case 1: key= "Description" //for  comments
          break;
    case 2: key = "UserOrGroupId" // for share object like caseShare,ideaShare
                    break;
    case 3: key= "Topics" //for  comments
      break;
    default: break;
  }

  let finalString=""
  for(var i=0;i<records.length;i++) {

    if ((contentSourceObject.fields.find(x => x.name == key) ) && (contentSourceObject.fields.find(x => x.name == key) ? !contentSourceObject.fields.find(x => x.name == key).isMerged : 0) && !key.includes("_nested") && !key.includes("_flat") && !key.includes("attachment_") && !key.toLowerCase().includes("accountid") && !key.toLowerCase().includes("contactid") && !key.includes("_navigation"))
      var newKey = contentSource.contentSource.elasticIndexName + "___" + contentSourceObject.name + "___" + key
    else
      newKey = key

    let update = {
      "update": {
        _id: records[i].ParentId || records[i].IdeaId || records[i].FeeditemId || records[i].CaseId || records[i].EntityId,
        _index: contentSource.contentSource.elasticIndexName,
        _type: contentSourceObject.name
      }
    }
    switch (type) {
      case 1:
      let script = {
          "script": {
            "source": "ctx._source." + newKey + "+=params.param",
            "lang": "painless",
            "params": {"param": " " + records[i].CommentBody}
          }
        }
        let scriptComment = {
          "script": {
            "source": "if (!ctx._source.caseComments.contains(params.param)){ctx._source.caseComments.add(params.param)}",
            "lang": "painless",
            "params": {
              "param": {
                "body": records[i].CommentBody,
                "createdDate": records[i].CreatedDate,
                "createdBy": records[i]["CreatedBy.Email"]
              }
            }
          }
        }
        finalString += JSON.stringify(update) + "\n" + JSON.stringify(script) + "\n" + JSON.stringify(update) + "\n" + JSON.stringify(scriptComment) + "\n"
    break;
      case 2:
        var scriptShare = {
          "script": {
            "source": "if (!ctx._source."+newKey+".contains(params.param)){ctx._source."+newKey+".add(params.param)}",
            "lang": "painless",
            "params": {
              "param": records[i].UserOrGroupId,

            }
          }
        }
        finalString += JSON.stringify(update) + "\n" + JSON.stringify(scriptShare) + "\n"
        break;

  case 3:
     var scriptShare = {
      "script": {
        "source": "if (!ctx._source."+newKey+".contains(params.param)){ctx._source."+newKey+".add(params.param)}",
        "lang": "painless",
        "params": {
          "param": records[i]["Topic.Name"],

        }
      }
    }
    finalString += JSON.stringify(update) + "\n" + JSON.stringify(scriptShare) + "\n"
    break;
  }
  }
  if (records.length > 0) {
    console.log("sending elastic hit to update")
    commonFunctions.BulkUpload(finalString, function (err, data) {
      callback(null, finalString);
    })
  }
  else {
    callback(null, finalString)
  }
}

function updateNavigationalTopics(records,contentSource,contentSourceObject,allTopics,callback) {

  //SELECT EntityId,Topic.Name,NetworkId FROM TopicAssignment where EntityId IN
  let finalString=""

  for (let rc = 0; rc < records.length; rc++) {
     let topicGroup = allTopics.find(y => y.community.id == records[rc].NetworkId)
    if (topicGroup)
      records[rc][topicGroup.community.name] = records[rc]["Topic.Name"]
    var key = topicGroup.community.name
    if ((contentSourceObject.fields.find(x => x.name == key) ) && (contentSourceObject.fields.find(x => x.name == key) ? !contentSourceObject.fields.find(x => x.name == key).isMerged : 0) && !key.includes("_nested") && !key.includes("_flat") && !key.includes("attachment_") && !key.toLowerCase().includes("accountid") && !key.toLowerCase().includes("contactid") && !key.includes("_navigation"))
      var newKey = contentSource.contentSource.elasticIndexName + "___" + contentSourceObject.name + "___" + key
    else
      newKey = key


    let update = {
      "update": {
        _id: records[rc].ParentId || records[rc].IdeaId || records[rc].FeeditemId || records[rc].CaseId || records[rc].EntityId,
        _index: contentSource.contentSource.elasticIndexName,
        _type: contentSourceObject.name
      }
    }
    newKey=newKey+"_backup"

    let scriptTopic = {
      "script": {
        "source": "if(ctx._source['"+newKey+"'] !=null ){ctx._source['"+newKey+"']+=(params.param)} else {ctx._source['"+newKey+"']=(params.param)}",
        "lang": "painless",
        "params": {
          "param": records[rc][topicGroup.community.name]+","
        }
      }
    }
    finalString += JSON.stringify(update) + "\n" + JSON.stringify(scriptTopic) + "\n"

     }

  if (records.length > 0) {
    commonFunctions.BulkUpload(finalString, function (err, data) {
      callback(null, finalString);
    })
  }
  else {
    callback(null, finalString)
  }
}

function updateCategories(records,contentSource,contentSourceObject,AllCategories,callback) {

  //SELECT EntityId,Topic.Name,NetworkId FROM TopicAssignment where EntityId IN
  let finalString=""

  for (let rc = 0; rc < records.length; rc++) {
    let categoryGroupName = records[rc].DataCategoryGroupName
      records[rc][categoryGroupName+"_category_nested"] = records[rc]["DataCategoryName"]
    var key = categoryGroupName+"_category_nested"
    if ((contentSourceObject.fields.find(x => x.name == key) ) && (contentSourceObject.fields.find(x => x.name == key) ? !contentSourceObject.fields.find(x => x.name == key).isMerged : 0) && !key.includes("_nested") && !key.includes("_flat") && !key.includes("attachment_") && !key.toLowerCase().includes("accountid") && !key.toLowerCase().includes("contactid") && !key.includes("_navigation"))
      var newKey = contentSource.contentSource.elasticIndexName + "___" + contentSourceObject.name + "___" + key
    else
      newKey = key


    let update = {
      "update": {
        _id: records[rc].ParentId || records[rc].IdeaId || records[rc].FeeditemId || records[rc].CaseId || records[rc].EntityId,
        _index: contentSource.contentSource.elasticIndexName,
        _type: contentSourceObject.name
      }
    }
    newKey=newKey+"_backup"

    let scriptTopic = {
      "script": {
        "source": "if(ctx._source['"+newKey+"'] !=null ){ctx._source['"+newKey+"']+=(params.param)} else {ctx._source['"+newKey+"']=(params.param)}",
        "lang": "painless",
        "params": {
          "param": records[rc].DataCategoryName+","
        }
      }
    }
    finalString += JSON.stringify(update) + "\n" + JSON.stringify(scriptTopic) + "\n"

  }

  if (records.length > 0) {
    commonFunctions.BulkUpload(finalString, function (err, data) {
      callback(null, finalString);
    })
  }
  else {
    callback(null, finalString)
  }
}

function intermediateFunction(status, connSF, contentSource, contentSourceObject, allRecords, AllCategories, allTopics, callback) {
  console.log("Record length: ", allRecords.length)
  let TaskRecordsBatch = []
  let allRecordIntermediate = []
  let batchSize = 500
  for (var r = 0; r < allRecords.length; r++) {
    TaskRecordsBatch.push((function (r) {
      return function (outerCB1) {
        //adding for Idea Visibility
        allRecordIntermediate.push(allRecords[r])
        if ((r + 1) % batchSize == 0 || (allRecords.length) - (r + 1) == 0) {
          salesforceCrawl.playWithSalesforceData(status, connSF, contentSource, contentSourceObject, allRecordIntermediate, AllCategories, allTopics, function (dataReturn) {
            allRecordIntermediate = []
            outerCB1(null)
          })
        }
        else
          outerCB1(null)
      }
    })(r))

  }
  async.series(TaskRecordsBatch, function (err, completedData) {
    TaskRecordsBatch=[]
    console.log("Done everysync")
    callback(null, completedData)
  })

}

checkForArticleType = function (contentSourceObjectName, record) {

  if (contentSourceObjectName.slice(-5) == "__kav") {
    record["Visibility"] = []
    if (checkVisibility(record["IsVisibleInPkb"])) {
      record["Visibility"].push("Public")
    }
    if (checkVisibility(record["IsVisibleInCsp"])) {
      record["Visibility"].push("Customer")
    }
    if (checkVisibility(record["IsVisibleInPrm"])) {
      record["Visibility"].push("Partner")
    }
    if (checkVisibility(record["IsVisibleInApp"])) {
      record["Visibility"].push("Internal App")
    }
  }
  record["Topics"] = []
  record["UserOrGroupId"] = []
  record["caseComments"] = []
  record["attachment_" + contentSourceObjectName] = []
  return record

}

salesforceCrawl.playWithSalesforceData = function (status, connSF, contentSource, contentSourceObject, allRecords, AllCategories, allTopics, callback) {

  var DataTosend = ""

  async.series([

  function(cb){
    cb(null)
  }
  ], function (err, result) {

    console.log("here in save 1")

    for (var r = 0; r < allRecords.length; r++) {
      salesforceCrawl.saveDataFormat(status, contentSource, contentSourceObject, allRecords[r], function (resultFormatted) {
        console.log("isme bhi aa gya" + r);
        DataTosend += resultFormatted + "\n"
      })
    }
    if (allRecords.length > 0) {
      commonFunctions.BulkUpload(DataTosend, function (err, data) {
        callback(null, DataTosend);
      })
    }
    else {
      callback(null, DataTosend)
    }

  })

}


salesforceCrawl.getObjectAttachment = function (connSF, record, callback) {

  var attachment = "SELECT Body,ContentType,Id,Name,ParentId FROM Attachment where ParentId IN ( " + record.join(",") + ") and IsPrivate=false";
  console.log("sqlAttachment===", attachment)
  connSF.query(attachment, function (err1, resultAttachment) {

    console.log("attachment" + err1)


    var commentParse = []
    for (var c = 0; c < resultAttachment.records.length; c++) {
      commentParse.push((function (c) {
        return function (cbComment) {
          console.log("attachment name", resultAttachment.records[c].Name)
          if (commonFunctions.extensionAttachment(resultAttachment.records[c].Name)) {

            salesforceCrawl.DownloadAttachment(connSF, resultAttachment.records[c].Body, function (err, attachmentData) {
              if (!err)
                resultAttachment.records[c].attachment = {
                  body: attachmentData,
                  name: resultAttachment.records[c].Name,
                  url: '/servlet/servlet.FileDownload?file=' + resultAttachment.records[c].Id
                }
              cbComment(null, attachmentData)
            })
          }
          else cbComment(null)
        }
      })(c))
    }
    async.series(commentParse, function (err, result) {
      callback(resultAttachment)
    })
  })

}

salesforceCrawl.getShareObject = function (connSF, record, objectName, callback) {

  let field = objectName + "Id"
  let object = objectName + "Share"
  if (objectName.includes("__c")) {
    field = "ParentId"
    object = objectName.substr(0, objectName.length - 1) + "Share"
  }
  if (objectName == 'feeditem_1') {
    objectName = 'feeditem'
  }
  var shareObject = "SELECT UserOrGroupId," + field + " FROM " + object + " where " + field + " IN ( " + record.join(",") + ")";
  console.log("sqlShare===", shareObject)
  connSF.query(shareObject, function (err1, resultShare) {
    console.log("data", JSON.stringify(resultShare))
    callback(resultShare);
  })
}

salesforceCrawl.getFeedGroupName=function (connSF, record, callback) {
  var names = "SELECT Name,Id FROM CollaborationGroup where id IN ( " + record.join(",") + ")"// AND NetworkId='" + config.get('networkId') + "'";
  console.log("topicName===", names)
  connSF.query(names, function (err1, resultNames) {

    callback(resultNames);
  })
}

salesforceCrawl.saveDataFormat = function (status, contentSource, contentSourceObject, fieldObject, callback) {

  var bulkString = "";
  var bulkStringIndex = {}
  bulkStringIndex["index"] = {}
  bulkStringIndex["index"]["_index"] = contentSource.contentSource.elasticIndexName + (status ? "_temp" : "");
  bulkStringIndex["index"]["_type"] = contentSourceObject.name

  // if (contentSourceObject.name.slice(-5) == "__kav")
  //   bulkStringIndex["index"]["_id"] = fieldObject["KnowledgeArticleId"]
  // else
    bulkStringIndex["index"]["_id"] = fieldObject["Id"]

  bulkStringIndex = JSON.stringify(bulkStringIndex)

  //console.log("here ----")

  var newObj = {};
  newObj['id'] = fieldObject["Id"];
  newObj["post_time"] = fieldObject["CreatedDate"] ? fieldObject["CreatedDate"] : new Date();

  if (config.get('mergeArticle')) {
    if (contentSourceObject.name.slice(-5) == "__kav")
      newObj["objType"] = "article"
    else
      newObj["objType"] = contentSourceObject.name
  }

  let taskParse = []
  for (key in fieldObject) {

    if (contentSourceObject.fields.find(x => x.name == key) ? (contentSourceObject.fields.find(x => x.name == key).isSearchable && !key.includes("attachment_")) : 0)
      fieldObject[key] = (commonFunctions.stripHtml(fieldObject[key]))
    if (contentSourceObject.fields.find(x => x.name == key) ? (contentSourceObject.fields.find(x => x.name == key).type == "multipicklist") : 0)
      fieldObject[key] = (fieldObject[key].split(";"))

    if (key == "Owner")
      newObj["OwnerName"] = fieldObject[key]["Name"]
    else if (key == "CreatedBy")
      newObj["Author"] = fieldObject[key]["Name"]
    //specail case handled
    else if (key == "UserOrGroupId") {
      fieldObject[key].push(fieldObject['OwnerId'])
      newObj["UserOrGroupId"] = fieldObject[key]
    }
    //specialCase Handled
    else if (key != "attributes") {

      if (contentSourceObject.fields.find(x => x.name == key) ? contentSourceObject.fields.find(x => x.name == key).type == "number" : "") {
        fieldObject[key] = parseInt(fieldObject[key])
      }

      if ((contentSourceObject.fields.find(x => x.name == key) || contentSourceObject.fields.find(x => "toLabel(" + x.name + ")" == key)) && (contentSourceObject.fields.find(x => x.name == key) ? !contentSourceObject.fields.find(x => x.name == key).isMerged : 0) && !key.includes("_nested") && !key.includes("_flat") && !key.includes("attachment_") && !key.toLowerCase().includes("accountid") && !key.toLowerCase().includes("contactid") && !key.includes("_navigation"))
        var newKey = contentSource.contentSource.elasticIndexName + "___" + contentSourceObject.name + "___" + key
      else
        newKey = key
      if (fieldObject[key]) {
        newObj[newKey] = fieldObject[key]
      }
    }
  }


  var bulkStringFieldsData = JSON.stringify(newObj)

  //bulkStringFieldsData = commonFunctions.stripHtml(bulkStringFieldsData)
  var finalString = bulkStringIndex + "\n" + bulkStringFieldsData//text.replace(/&quot;/g, '\"');
  callback(finalString)

  // })

}

salesforceCrawl.fetchDataSalesforce = function (status, contentSourceId, isCron, callback) {
  async.auto({

    get_content_source_data: function (cb) {
      commonFunctions.getContentSourceDataById(contentSourceId, function (err, contentSourceData) {
        if (!err)
          cb(null, contentSourceData)
        else
          cb(err, [])
      });
    },
    get_content_source_objects_and_fields: function (cb) {
      commonFunctions.getContentSourceObjectsAndFieldsById(contentSourceId, function (err, dataObjectFields) {
        if (!err)
          cb(null, commonFunctions.getFieldsApiName(dataObjectFields))
        else
          cb(err, [])
      })
    },
    initialize_salesforce_connection: function (cb) {
      salesforceCrawl.initalizeSalesforceConnection(contentSourceId, function (err, connSF) {
        cb(null, connSF);
      })
    },
    get_categories: ['initialize_salesforce_connection', function (getDataAbove, cb) {
      salesforceCrawl.getSalesforceCategoriesPost(getDataAbove.initialize_salesforce_connection, function (err, allCategories) {
        if (err)
          cb(err, [])
        else
          cb(null, allCategories)
      })
    }],
    get_navi_topics: ['initialize_salesforce_connection', function (getDataAbove, cb) {
      commonFunctions.getNavigationalTopics(getDataAbove.initialize_salesforce_connection, function (err, allCategories) {
        if (err)
          cb(err, [])
        else
          cb(null, allCategories)
      })
    }],
    crawlDataMethod: ['get_categories', 'get_content_source_objects_and_fields', 'get_navi_topics', function (getDataAbove, cb) {
      console.log("+++++++++++++++", getDataAbove.get_content_source_objects_and_fields)
      getDataAbove.get_content_source_objects_and_fields = getDataAbove.get_content_source_objects_and_fields.filter(x => { if (x.status) return x })


      var data = []
      getDataAbove.get_content_source_objects_and_fields.map(x => {
        if (x.name.toLowerCase().includes('__kav') && config.get('checkDraft')) {
          let cd = JSON.parse(JSON.stringify(x));
          cd.condition = "draft";
          data.push(cd);
        }
      });

      data.map(x => { getDataAbove.get_content_source_objects_and_fields.unshift(x) })



            var task = []

            for (var j = 0; j < getDataAbove.get_content_source_objects_and_fields.length; j++) {
              task.push((function (j) {

                return function (cbInternal) {

                  salesforceCrawl.fetchSalesforceData(status, getDataAbove.initialize_salesforce_connection, getDataAbove.get_content_source_data, getDataAbove.get_content_source_objects_and_fields[j], 100, 0, [], getDataAbove.get_categories, getDataAbove.get_navi_topics, isCron, function (err, result) {
                    if (!err)
                      cbInternal(null, "")
                    else
                      cbInternal("err in sync", [])
                  })
                }
              })(j))
            }
            async.series(task, function (err, resultInternal) {
              cb(null, "Month done")
            })


    }]
  }, function (err, result) {
    callback(null, result)
  })
}

salesforceCrawl.getSalesforceCategoriesPost = function (connSF, callback) {
  var options = {
    method: 'GET',
    url: connSF.instanceUrl + '/services/data/v38.0/support/dataCategoryGroups',
    qs:
      {
        sObjectName: 'KnowledgeArticleVersion',
        topCategoriesOnly: 'false'
      },
    headers:
      {
        authorization: 'Bearer ' + connSF.accessToken
      }
  };

  //connSF.sobject("contact").describe(function (err, meta) { //only to intialize data
  request(options, function (error, response, body) {
    if (error) {
      console.log(error);
      callback(error, [])
    }
    else {
      console.log(body);
      callback(null, JSON.parse(body)["categoryGroups"])
    }
  })
  // })
}

salesforceCrawl.SalesforceSpecificCrawl = function (status, contentSourceId, callback) {
  let isTemp = 0
  if (status == 'reindexing')
    isTemp = 0
  async.auto({
    create_mapping_salesforce: function (cb) {

      commonFunctions.createMapping(contentSourceId, isTemp, function (err, result) {
        if (!err)
          cb(null, result)
        else
          cb(result, [])
      })
      //cb(null,[])
    },
    fetch_data_salesforce: ['create_mapping_salesforce', function (getDataAbove, cb) {
      salesforceCrawl.fetchDataSalesforce(isTemp, contentSourceId, 0, function (err, result) {
        if (!err)
          cb(null, result)
      })
    }]
  }, function (err, result) {
    callback(null, result)
  })
}

salesforceCrawl.initalizeSalesforceConnection = function (contentSourceId, callback) {

  commonFunctions.initializeSalesforceConnection(contentSourceId, callback)

}
salesforceCrawl.requestTofetchSFObjectFields = function (connSF, objectName, callback) {

  connSF.sobject(objectName).describe(function (err, meta) {
    var res = {}
    if (!err) {
      res.flag = commonFunctions.constants.SUCCESS
      res.data = meta
      callback(null, res)
    }
    else {
      res.flag = commonFunctions.constants.SOMETHING_WRONG
      res.error = err
      callback(err, res)
    }
  });
}

const checkVisibility = function (ab) {
  return (typeof (ab) == "boolean") ? (ab) : ((ab) == "true" ? true : false)
}

salesforceCrawl.createNestedObject = function (topCategories, groupName, value, level, check, suffix) {
  var data = []
  var nodeName = groupName + suffix + level.count
  for (var tc = 0; tc < topCategories.length; tc++) {
    if (!check.found) {
      data = []
    }
    var obj = {}
    if (check.found) break;
    if (topCategories[tc].name == value) {
      obj["name"] = topCategories[tc].label
      obj[nodeName] = createNestedObjectCategory(topCategories[tc].childCategories,groupName ,{ "count": (level.count+1) },suffix)
      data.push(obj)
      check.found = true
      break;
    } else {
      level.count++;
      obj["name"] = topCategories[tc].label
      obj[nodeName] = salesforceCrawl.createNestedObject(topCategories[tc].childCategories, groupName, value, level, check, suffix)
      data.push(obj)
    }
  }
  level.count--;
  if(!check.found)
    data=[]
  return data
}

salesforceCrawl.createNestedObjectTopic = function (topCategories, groupName, value, level, check, suffix) {
  var data = []
  var nodeName = groupName + suffix + level.count
  for (var tc = 0; tc < topCategories.length; tc++) {
    if (!check.found) {
      data = []
    }
    var obj = {}
    if (check.found) break;
    if (topCategories[tc].topic.name.replace(/&amp;/g, '&') == value) {
      obj["name"] = value
      //obj[nodeName] = createNestedObject(topCategories[tc].children,groupName + suffix ,{ "count": (level.count+1) },'_') //special case in kronos
      data.push(obj)
      check.found = true
      break;
    } else {
      level.count++;
      obj["name"] = topCategories[tc].topic.name.replace(/&amp;/g, '&')
      obj[nodeName] = salesforceCrawl.createNestedObjectTopic(topCategories[tc].children, groupName, value, level, check, suffix)
      data.push(obj)
    }
  }
  level.count--;
  if(!check.found)
    data=[]
  return data
}

salesforceCrawl.mergeObject = function (obj1, obj2, groupName, level, check, suffix) {
  var object = groupName + suffix + (level ? "_" + level : "")
  var result = obj1[object] ? obj1[object] : obj2[object] ? obj2 : obj1
  if (!obj1[object] && result[object]) {
    obj1[object] = (result[object])
  }
  else if (!obj1[object] && !obj2[object])
    return result;

  for (var o1 = 0; o1 < obj1[object].length; o1++) {
    if (check.found) break;
    if (!obj2[object]) return obj1
    for (var o2 = 0; o2 < obj2[object].length; o2++) {
      if (check.found) break;
      if (obj1[object][o1].name == obj2[object][o2].name) {
        level++;
        salesforceCrawl.mergeObject(obj1[object][o1], obj2[object][o2], groupName, level, check, suffix)
      } else {
        if (!obj1[object].filter(x => x.name == obj2[object][o2].name).length){
          obj1[object]=obj1[object].concat(obj2[object])
          obj1[object]=obj1[object].filter((thing, index, self) =>
            index === self.findIndex((t) => (
              t.name === thing.name
            ))
          )
          check.found = true
          break;
        }
      }
    }
  }
  return obj1
}

salesforceCrawl.mergeArray = function (arr, groupName, suffix) {
  var obj = {}
  if (arr.length > 1) {
    for (var a = 0; a < arr.length; a++) {
      if (a == 0) obj = salesforceCrawl.mergeObject(arr[0], arr[a], groupName, 0, {
        "found": false
      }, suffix)
      else obj = salesforceCrawl.mergeObject(obj, arr[a], groupName, 0, {
        "found": false
      }, suffix)
    }
    return obj
  } else return arr[0]
}

salesforceCrawl.DownloadAttachment = function (connSF, url, callback) {
  var f = ""
  var options = {
    method: 'GET',
    url: connSF.instanceUrl + url,
    headers: {
      authorization: 'Bearer ' + connSF.accessToken
    }
  }
  request.get(options).pipe(request.put(config.get('tikaPath')))
    .on("error", err => {
      console.log("err", err)
      callback(err, "")
    })
    .on('data', da => {
      f += da.toString();
    })
    .on("end", () => {
      callback(null, f)
    })
}

function createNestedObject(topCategories, groupName, level,suffix) {
  var data = []
  var nodeName = groupName + suffix + level.count
  for (var tc = 0; tc < topCategories.length; tc++) {
    var obj = {}
    level.count++;
    obj["name"] = topCategories[tc].topic.name.replace(/&amp;/g,'&')
    obj[nodeName] = createNestedObject(topCategories[tc].children, groupName, level,suffix)
    data.push(obj)

  }
  level.count--;
  return data
}

function createNestedObjectCategory(topCategories, groupName, level,suffix) {
  var data = []
  var nodeName = groupName + suffix + level.count
  for (var tc = 0; tc < topCategories.length; tc++) {
    var obj = {}
    level.count++;
    obj["name"] = topCategories[tc].label.replace(/&amp;/g,'&')
    obj[nodeName] = createNestedObjectCategory(topCategories[tc].childCategories, groupName, level,suffix)
    data.push(obj)

  }
  level.count--;
  return data
}


salesforceCrawl.runCrawlerSalesforce

module.exports = salesforceCrawl

