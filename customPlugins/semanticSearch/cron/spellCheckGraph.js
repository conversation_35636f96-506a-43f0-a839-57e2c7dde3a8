var path = require('path');
var elastic = require('elasticsearch');
var fs = require('fs');
var async = require('async');
const pako = require('pako');
environment = require(path.join(__dirname, '../../../routes/environment'));
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../../config');
const { getTenantInfoFromTenantId } = require('auth-middleware');
const multiTenantSupport = config.get('multiTenantSupport');
process.env.NODE_CONFIG_DIR = configPath;
console.log(environment.configuration);
config = require('config');
const kafkaLib = require('../../../utils/kafka/kafka-lib');
const subscribeConsumer = async () =>{
    try{
        const spellCheckConsumer = await kafkaLib.getConsumer({
            groupId: `admin ${config.get("kafkaTopic.spellCorrectorTopic")}`,
            topic:config.get("kafkaTopic.spellCorrectorTopic"),
            fromBeginning: false
        })

        await spellCheckConsumer.run({
            eachMessage: async ({
                topic, partition, message, heartBeat
            }) => {
                try {
                    let spellCheckMessage = JSON.parse(message.value);
                    const tenantId = spellCheckMessage.tenantId
                    const tenantInfo = await getTenantInfoFromTenantId(tenantId);
                    const tpk = tenantInfo[0].tpk;
                    const spellCheckElasticIndex = multiTenantSupport ? `${tpk}_spellcheckindex` : `spellcheckindex_${config.get('poc_name')}`;
                    if(spellCheckMessage.compressed){
                        console.log('Recieved GZIP format');
                        spellCheckMessage.data = pako.ungzip(spellCheckMessage.data, { to: 'string' });
                    }
                    spellCheckMessage = JSON.parse(spellCheckMessage.data)['spellCorrector'];
                    const searchClients = Object.keys(spellCheckMessage);
                    try {
                        const completeSpellArray = await Promise.all(searchClients.map(async (client) => {
                            return await clustering(client, spellCheckMessage[client], spellCheckElasticIndex,tenantId);
                        }));
                        const nonEmptyArrays = completeSpellArray.filter(arr => Array.isArray(arr) && arr.length > 0);
                        kafkaLib.publishMessage({
                            topic: config.get("kafkaTopic.spellCorrectorEvent"),
                            messages: [{
                                value: JSON.stringify(nonEmptyArrays),
                                key: tenantId
                            }]
                        })
                    } catch (e) {
                        console.log("Error is ", e);
                    }
                } catch (e) {
                    console.log(e);
                }
            }
        });
    }catch(error){
        if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
            kafkaLib.createTopic(config.get("kafkaTopic.spellCorrectorTopic"));
        }
    }
};

async function clustering(uid, misSpelledWordsArray, spellCheckElasticIndex,tId) {
    console.log('clustering for UID  -', uid);
    try {
        // Read from dictionary
        const readFromDictionary = await new Promise((resolve, reject) => {
            fs.readFile(__dirname + '/dictionaryFiles/' + uid + '_dict.txt', (err, data) => {
                if (err) {

                    reject(err);
                } else {
                    let dictData = data.toString('utf8');
                    dictData = dictData.substring(0, dictData.length - 1);
                    const dictionary = dictData.split(",");
                    resolve(dictionary);
                }
            });
        });
        const spellCorrector = [];
        const resout = await Promise.all(
            misSpelledWordsArray.map((wordArr) => {
                const str_first = wordArr[0].toLowerCase();
                const str_second = wordArr[1].toLowerCase();
                if (readFromDictionary.includes(str_first) && readFromDictionary.includes(str_second)) {
                    const indexFirst = spellCorrector.findIndex(u => u.spell_term == str_first);
                    if (indexFirst == -1) {
                        spellCorrector.push({ indexName: spellCheckElasticIndex, spell_term: str_first, mis_spell_array: [] ,tenantId : tId})
                    }
                    const indexSecond = spellCorrector.findIndex(u => u.spell_term == str_second);
                    if (indexSecond == -1) {
                        spellCorrector.push({ indexName: spellCheckElasticIndex, spell_term: str_second, mis_spell_array: [] ,tenantId : tId })
                    }
                } else if (readFromDictionary.includes(str_first)) {
                    const index = spellCorrector.findIndex(u => u.spell_term == str_first);
                    if (index > -1) {
                        spellCorrector[index].mis_spell_array.push(str_second);
                    } else {
                        spellCorrector.push({ indexName: spellCheckElasticIndex, spell_term: str_first, mis_spell_array: [str_second] ,tenantId : tId })
                    }
                } else if (readFromDictionary.includes(str_second)) {
                    const index = spellCorrector.findIndex(u => u.spell_term == str_second);
                    if (index > -1) {
                        spellCorrector[index].mis_spell_array.push(str_first);
                    } else {
                        spellCorrector.push({ indexName: spellCheckElasticIndex, spell_term: str_second, mis_spell_array: [str_first] ,tenantId : tId})
                    }
                }
                return spellCorrector;
            }
            )
        )
        return resout[0];
    } catch (err) {
        return [];
    }
}


module.exports = {
    subscribeConsumer
}