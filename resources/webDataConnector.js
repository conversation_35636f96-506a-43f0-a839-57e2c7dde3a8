function myFunction() {
  var startDate = document.getElementById("start").value;
   console.log(startDate);
  var  endDate = document.getElementById("end").value;
   console.log(endDate);
   var uids = document.getElementById("searchClientNumber");
   var selectedUid = uids.options[uids.selectedIndex].value;

   var dateRange = {
     "startDate":startDate,
     "endDate" : endDate,
     "searchClient_Id":selectedUid
   };
   tableau.connectionData = JSON.stringify(dateRange);
 }
 (function () {
   'use strict';
 
   $(document).ready(function () {
     
     var accessToken = Cookies.get("accessToken");
     var hasAuth = accessToken && accessToken.length > 0;
     updateUIWithAuthState(hasAuth);
     if (!accessToken) {
       $("#connectbutton").click(function () {
         doAuthRedirect();
       });
 
     } else {
       $('#fetchSuAnalytics').click(function () {
         myFunction();
         tableau.connectionName = "web data connector";
         tableau.submit();
         
       });
     }
   });
 
   function doAuthRedirect() {
    config.clientId = document.getElementById("clientId").value;

    if (!config.clientId.trim().length){
      return;
    }
     var appId = config.clientId;
     if (tableau.authPurpose === tableau.authPurposeEnum.ephemerel) {
       appId = config.clientId; // This should be Desktop
     } else if (tableau.authPurpose === tableau.authPurposeEnum.enduring) {
       appId = config.clientId; // This should be the Tableau Server appID
     }
 
     var url = config.authUrl + '/authorise?response_type=code&client_id=' + config.clientId + '&redirect_uri=' + config.redirectUri;
     window.location.href = url;
   }
 
   function updateUIWithAuthState(hasAuth) {
     if (hasAuth) {
       $(".notsignedin").css('display', 'none');
       $(".signedin").css('display', 'block');
       $("#connectbutton").hide();
       $("#fetchSuAnalytics").show();
       $("#start").show();
       $("#end").show();
       $("#submitDateBtn").show();
     } else {
       $(".notsignedin").css('display', 'block');
       $(".signedin").css('display', 'none');
       $("#connectbutton").show();
       $("#fetchSuAnalytics").hide();
       $("#start").hide();
       $("#end").hide();
       $("#submitDateBtn").hide();
     }
   }
   var connector = tableau.makeConnector();
   /*
    * First phase of WDC
    * Interactive phase: Interact with the user --(1)
    */
   connector.init = function (initCallback) { // IP--(2)
     tableau.authType = tableau.authTypeEnum.custom;
     //If we are in the auth phase we only want to show the UI needed for auth
     if (tableau.phase == tableau.phaseEnum.authPhase) {
       $("#fetchSuAnalytics").css('display', 'none');
     }
 
     if (tableau.phase == tableau.phaseEnum.gatherDataPhase) {
       // If the API that WDC is using has an endpoint that checks
       // the validity of an access token, that could be used here.
       // Then the WDC can call tableau.abortForAuth if that access token is invalid.
 
       //tableau.abortForAuth(); ===> when a user, who has not been authenticated, opens an existing workbook and attempts to refresh data. 
       //In this case, the auth token is no longer available.
     }
 
     var accessToken = Cookies.get("accessToken");
     var hasAuth = accessToken && accessToken.length > 0;
     updateUIWithAuthState(hasAuth);
 
     initCallback(); // IP--(3)
 
     //after calling Connector waits for user i/p
     // If we are not in the data gathering phase, we want to store the token
     // This allows us to access the token in the data gathering phase
 
     if (tableau.phase == tableau.phaseEnum.interactivePhase || tableau.phase == tableau.phaseEnum.authPhase) {
       if (hasAuth) {
         tableau.password = accessToken;
 
         if (tableau.phase == tableau.phaseEnum.authPhase) {
           // Auto-submit here if we are in the auth phase
           tableau.submit()
         }
 
         return;
       }
     }
   }; //end of init phase
 
   /*
    *Gathering data phase (steps 1 to 3 same)
    */
   //GP --4
   connector.getSchema = function (schemaCallback) {
     var session_col = [{
       id: "sessionIdentifier",
       dataType: tableau.dataTypeEnum.string
     }, {
       id: "casesLogCount",
       alias: "cases Log Count",
       dataType: tableau.dataTypeEnum.int
     }, {
       id: "searchesCount",
       alias: "searches Count",
       dataType: tableau.dataTypeEnum.int
     }, {
       id: "pageViewsCount",
       alias: "page Views Count",
       dataType: tableau.dataTypeEnum.int
     }, {
       id: "startTime",
       alias: "start Time",
       dataType: tableau.dataTypeEnum.datetime
     }, {
       id: "endTime",
       alias: "end Time",
       dataType: tableau.dataTypeEnum.datetime
     }, {
       id: "support_visit",
       alias: "support visit",
       dataType: tableau.dataTypeEnum.bool
     }, {
       id: "case_created",
       alias: "case created",
       dataType: tableau.dataTypeEnum.bool
     }, {
       id: "clientId",
       alias: "clientId",
       dataType: tableau.dataTypeEnum.string
     }];
 
     var session_table = {
       id: "session",
       alias: "session table",
       columns: session_col
     }
 
     var search_col = [{
       id: "searchId",
       dataType: tableau.dataTypeEnum.string
     }, {
       id: "typeLabel",
       alias: "typeLabel",
       dataType: tableau.dataTypeEnum.string
     }, {
       id: "searchText",
       alias: "searchText",
       dataType: tableau.dataTypeEnum.string
     }, {
       id: "time",
       alias: "time",
       dataType: tableau.dataTypeEnum.datetime,
     }, {
       id: "sessionIdentifier",
       alias: "sessionIdentifier",
       dataType: tableau.dataTypeEnum.string,
       filterable: true
     }, {
      id: "clientId",
      alias: "clientId",
      dataType: tableau.dataTypeEnum.string,
      filterable: true
    }];
 
     var search_table = {
       id: "search",
       alias: "search table",
       columns: search_col,
       foreignKey: {
         "tableId": "session",
         "columnId": "sid"
       }
     }
 
     var conversion_col = [{
       id: "_id",
       alias: "_id",
       dataType: tableau.dataTypeEnum.string
     }, {
       id: "conversionTitle",
       alias: "conversionTitle",
       dataType: tableau.dataTypeEnum.string
     }, {
       id: "time",
       alias: "time",
       dataType: tableau.dataTypeEnum.datetime
     }, {
       id: "conversionLink",
       alias: "conversionLink",
       dataType: tableau.dataTypeEnum.string
     }, {
       id: "searchId",
       dataType: tableau.dataTypeEnum.string,
       filterable: true
     }, {
       id: "typeLabel",
       dataType: tableau.dataTypeEnum.string
     }, {
       id: "sessionIdentifier",
       alias: "sessionIdentifier",
       dataType: tableau.dataTypeEnum.string
     }, {
       id: "clientId",
       alias: "clientId",
       dataType: tableau.dataTypeEnum.string
     }];
 
     var conversion_table = {
       id: "conversion",
       alias: "conversion table",
       columns: conversion_col,
       foreignKey: {
         "tableId": "search",
         "columnId": "searchId"
       }
     };
     schemaCallback([session_table, search_table, conversion_table]); //GP --5
   }
 
   //GP --6
   /*
    * GetData is called once for each table defined in getSchema
    * Now refresh token will generate new access token 
    */
   
   connector.fetchData = async function (gth, table, connectionUri, currentPage, pageLimit, accessToken, doneCallback) {
    var tableData = [];
 
     var xhr = await $.ajax({
       url: connectionUri,
       contentType: 'application/json',
       headers: {
         authorization: "Bearer " + accessToken
       },
       success: function (data) {
        setTimeout(() => {
          tableau.log("Fetching Data");
          if (data.data && data.data.list) {
            var list = data.data.list;
            for (var i = 0; i < list.length; i++) {
              var session = list[i];
              var st = session.startTime;
              var newStartTime = new Date(st);
              var ed = session.endTime;
              var newEndTime = new Date(ed);

              if (table.tableInfo.id == "session"){ //id of the tbl
                tableData.push({
                  "startTime": newStartTime,
                  "endTime": newEndTime,
                  "support_visit": session.support_visit,
                  "case_created": session.case_created,
                  "clientId": session.searchclientUid,
                  "sessionIdentifier": session.searchIdentifier,
                  "casesLogCount": session.casesLogCount,
                  "searchesCount": session.searchesCount,
                  "pageViewsCount": session.pageViewsCount
                });
                }
              for (var j = 0; j < session.activityLog.length; j++) {
                var search = session.activityLog[j];
                var sTime = search.ts;
                var newTime = new Date(sTime);

                if (table.tableInfo.id == "search"){
                  if(search.type == "search"){
                    tableData.push({
                      "searchId": search.search_id, // to be checked
                      "typeLabel": search.type, 
                      "searchText": search.text_entered,
                      "time": newTime,
                      "sessionIdentifier": search.cookie,
                      "clientId": session.searchclientUid
                    });
                  } 
                }   
                var conversion = session.activityLog[j];
                var cTime = conversion.ts;
                var newConTime = new Date(cTime);

                if (table.tableInfo.id == "conversion"){
                if(conversion.type == "Conversion"){
                    tableData.push({
                      "searchId": search.search_id,
                      "_id": conversion.es_id, //to be checked
                      "conversionTitle": conversion.title,
                      "time": newConTime,
                      "conversionLink": conversion.url,
                      "typeLabel": conversion.type,
                      "sessionIdentifier": conversion.cookie,
                      "clientId": session.searchclientUid
                    });
                }
                }   
              }
            }
            table.appendRows(tableData); //GP --7
            let totalPages = Math.ceil(data.data.total/pageLimit);
            if (currentPage < totalPages) {
              tableau.reportProgress(`Total pages processed: ${currentPage}/${totalPages}`);
              currentPage += 1;
              connectionUri = `${config.authUrl}/api/v2/getSessionTrackingFormattedResult?from=${gth.startDate}&to=${gth.endDate}&export=0&uid=${gth.searchClient_Id}&keyword&count=${pageLimit}&startIndex=${currentPage}&orderby=endTime&order=desc&desc&internalUser=all`;
              connector.fetchData(gth, table, connectionUri, currentPage, pageLimit, accessToken, doneCallback);
            } else {
              doneCallback(); //GP --8
            }
          } else {
            tableau.abortWithError(data.err);
          }
        }, 300);
      },
      error: function (xhr, ajaxOptions, thrownError) {
        // WDC should do more granular error checking here
        // or on the server side.  This is just a sample of new API.
        tableau.abortForAuth(thrownError);
        doneCallback();
      }
    });
  }
   connector.getData = function (table, doneCallback) {
    var gth = JSON.parse(tableau.connectionData);
    let currentPage = 1;
    let pageLimit = 500;
    var connectionUri = `${config.authUrl}/api/v2/getSessionTrackingFormattedResult?from=${gth.startDate}&to=${gth.endDate}&export=0&uid=${gth.searchClient_Id}&keyword&count=${pageLimit}&startIndex=${currentPage}&orderby=endTime&order=desc&desc&internalUser=all`;
    var accessToken = tableau.password;

    connector.fetchData(gth, table, connectionUri, currentPage, pageLimit, accessToken, doneCallback);

  };
   // Register the tableau connector, call this last
   tableau.registerConnector(connector);
 })();
 
