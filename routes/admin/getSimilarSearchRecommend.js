/*  
Input Params - SearchKeyword , Cookie
Output - List of Recommendation | []
*/
var express = require('express');
var router = express.Router();
var request = require('request');
var natural = require('natural');
var TfIdf = natural.TfIdf;

var path = require('path');
var elasticsearch = require('elasticsearch');
var fs = require('fs');
var async = require('async');
environment = require(path.join(__dirname, '../environment.js'));
var commonFunctions = require('./../../utils/commonFunctions');
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../config');
process.env.NODE_CONFIG_DIR = configPath;
console.log(environment.configuration);
config = require('config');
// var similarSearchRecommender = "databricks_recommender1";
var similarSearchRecommender = "similarsearchrecommender"+"_"+config.get('poc_name');

const clientAnalytics = new elasticsearch.Client({
    host: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port')
});


router.post("/getRecommendations", function (req, res, next) {
    start(req, function (err, data) {
        if (err) {
            console.log(err);
            res.send({
                "similarRecommendations": []
            })
        }
        else {
            res.send({
                "similarRecommendations": data
            });
        }
    });
});


function getRecommendation(search_keyword, taid, uid, callback) {
    // uid = "e8a46fbb-1e09-11e9-b6f5-06d762ad9a62";
    var queryJson = {
        "size": 20,    //No. of Initial Clusters required 
        "query": {
            "bool": {
                "must": [
                    {
                        "range": {
                            "counter": {
                                "gte": 2
                            }
                        }
                    },
                    {
                        "range": {
                            "cluster_clicks": {
                                "gte": 2
                            }
                        }
                    },
                    {
                        "nested": {
                            "path": "cookie",
                            "query": {
                                "bool": {
                                    "must": [{
                                        "match": {
                                            "cookie.search_keyword": search_keyword
                                        }
                                    }
                                    ]
                                }
                            }
                        }
                    },
                    {
                        "match": {
                            "uid": uid
                        }
                    }
                ],
                "must_not": {
                    "nested": {
                        "path": "cookie",
                        "query": {
                            "bool": {
                                "must": [{
                                    "match": {
                                        "cookie.taid": taid
                                    }
                                }
                                ]
                            }
                        }
                    }
                }
            }
        }
    }
    console.log("Similar suggestion --->> "+JSON.stringify(queryJson));
    clientAnalytics.search({
        "index": similarSearchRecommender,
        "type": "recommend",
        "body": queryJson
    }, function (err, data) {
        if (err) {
            console.log(err);
            callback(err, null);
        }
        else {
            var clusterArray = [];
            data.hits.hits.forEach(x => {
                clusterArray.push({ "cValues": x._source.cookie });
            });
            clusterArray.map(x => {
                x.cValues.sort((a, b) => (a.search_date > b.search_date) ? 1 : ((b.search_date > a.search_date) ? -1 : 0));
            });

            /* Till now we have clusters with Sorted Saerches in them. */

            var asyncTask = [];
            for (var i = 0; i < clusterArray.length; i++) {
                asyncTask.push(function (i) {
                    return function (cb) {
                        /* Task needs to be done on every Cluster. */
                        var tfidf = new TfIdf();
                        clusterArray[i].cValues.forEach(item => {
                            tfidf.addDocument(item.clean_search_keyword);
                        });
                        clusterArray[i]['max'] = 0;
                        tfidf.tfidfs(search_keyword, function (j, measure) {
                            clusterArray[i].cValues[j]['tfIdf'] = measure;
                            clusterArray[i]['max'] = clusterArray[i]['max'] < measure ? measure : clusterArray[i]['max'];
                            // console.log(clusterArray[i].cValues[j].clean_search_keyword + "----" + clusterArray[i].cValues[j].tfIdf + "-----"+clusterArray[i].cValues[j].isClicked);
                        });
                        // console.log(clusterArray[i].max);
                        cb(null, clusterArray);
                    }
                }(i))
            }
            async.series(asyncTask, function (err, data) {
                if (data.length > 0) {
                    shrinkCluster(data[0], function (err, result) {
                        if (err) {
                            console.log(err);
                            callback(err, null);
                        }
                        else {
                            getCandidates(result, search_keyword, function (err, data) {
                                console.log("***** " + JSON.stringify(data));
                                callback(null, data);
                            });
                        }
                    });
                }
                else {
                    callback(null, []);
                }
            })
        }
    })
}

function shrinkCluster(clusterArray, cb) {
    for (var i = 0; i < clusterArray.length; i++) {

        // Finding Center in every Cluster 
        for (var j = 0; j < clusterArray[i].cValues.length; j++) {
            if (clusterArray[i].cValues[j].tfIdf == clusterArray[i].max) {
                clusterArray[i]['center'] = j;
                break;
            }
        }
        // Removing Duplicate Searches and also search Which have no clicks 
        // Except the Center Point
        for (var k = 0; k < clusterArray[i].cValues.length; k++) {
            if (k != clusterArray[i].center) {
                var isDuplicate = checkDulicate(clusterArray[i].cValues[k].clean_search_keyword, clusterArray[i].cValues[clusterArray[i]['center']].clean_search_keyword)
                if (isDuplicate || clusterArray[i].cValues[k].isClicked == false) {
                    clusterArray[i].cValues.splice(k, 1);
                    if (k < clusterArray[i].center) {
                        clusterArray[i]['center'] = clusterArray[i]['center'] - 1;
                    }
                    k = k - 1;
                }
            }
        }
    }
    cb(null, clusterArray);
}


function getTermMatchFreq(str1, str2) {
    var match = 0;
    arr1 = str1 ? str1.split(' ') : [];
    arr2 = str2 ? str2.split(' ') : [];
    for (var i = 0; i < arr1.length; i++) {
        for (var j = 0; j < arr2.length; j++) {
            if (arr1[i] == arr2[j]) {
                match = match + 1;
            }
        }
    }
    return match;
}

function checkRecommendationEnabled(uid,req, cb) {
    var sql = "Select similar_search from search_clients where uid=?"
    connection[req.headers['tenant-id']].execute.query(sql, [uid], function (err, results) {
        console.log(err);
        cb(null, results[0].similar_search ? true : false)
    });
}

function start(req, cb) {
    checkRecommendationEnabled(req.body.uid, req,function (err, result) {
        commonFunctions.errorlogger.info("********** check Recommendation enabled"+result);
        if (err) {
            console.log(err);
            cb(err, null);
        } else {
            if (result) {
                commonFunctions.errorlogger.info("*****"+req.body.searchString+"&&&&&&&&&&&&"+req.body.sid+"$$$$$$$"+req.body.uid);
                if(req.body.searchString  && req.body.sid  && req.body.uid){
                    var searchStringSize = req.body.searchString.split(" ").length;
                    if(req.body.searchString != "" && searchStringSize < 10){
                        getRecommendation(req.body.searchString, req.body.sid, req.body.uid, function (err, data) {
                            if (err) {
                                console.log(err);
                                cb(err, null);
                            }
                            else {
                                cb(null, data.length > 0 ? data : null);
                            }
                        });
                    }
                    else{
                        cb("length Limit of 10 exceed", null);
                    }
                }
                else{
                    cb("Paramter missing", null);
                }
            }
            else {
                cb('false Uid', null);
            }

        }
    });
}

function checkDulicate(str1, str2){
    var arr1 = str1 ? str1.split(" ") : [];
    var arr2 = str2 ? str2.split(" ") : [];
    arr1.sort();
    arr2.sort();
    if(arr1.length == arr2.length){
        if(arr1.join(" ") == arr2.join(" ")){
            return true;
        }
        else{
            return false;
        }
    }
    else{
        return false;
    }
}

function getCandidates(clusterArray, search_keyword, cb) {
    var allTerms = [];
    var candidatesList = [];
    var selectedNextList = [];
    var selectedPrevList = [];
    var res2 = [];

    var countMap = {};
    for (var i = 0; i < clusterArray.length; i++) {
        clusterArray[i].cValues.map(x => {
            if (!countMap[x.search_keyword]) {
                countMap[x.search_keyword] = 0;
            }
            if (x.search_keyword != search_keyword) {
                countMap[x.search_keyword]++;
            }
        })
    }

    for (var i = 0; i < clusterArray.length; i++) {
        clusterArray[i].cValues.map((x, index, arr) => {
            x['count_map_value'] = countMap[x.search_keyword];
            x['adjancyScore'] = 0;
            if (index == clusterArray[i].center + 1) {
                x['adjancyScore'] += 0.60
            }
            else if (index == clusterArray[i].center - 1) {
                x['adjancyScore'] += 0.40
            }
            x['termScore'] = 0;
            var match_score = getTermMatchFreq(x.clean_search_keyword, search_keyword);
            if(match_score == 0 ){
                x['termScore'] = 0;
            }
            else if(match_score == 1){
                x['termScore'] = 0.55
            }
            else{
                x['termScore'] = 0.63
            }
            x['finalScore'] = (x['count_map_value'] -1) + x['adjancyScore'] + x['termScore'];
        });
    }
    var finalObject = {};
    for (var i = 0; i < clusterArray.length; i++) {
        clusterArray[i].cValues.map(x=>{
            if(x.isClicked){
                if(!finalObject[x.search_keyword]){
                    finalObject[x.search_keyword] = x.finalScore;
                }
            }
        });
    }
    var finalArray = Object.keys(finalObject).map(x=>{
        return {"term":x, "finalScore":finalObject[x]};
    })

    finalArray.sort((a, b) => (a.finalScore < b.finalScore) ? 1 : ((b.finalScore < a.finalScore) ? -1 : 0));

    finalArray.map(x=>{
        if(x.term != search_keyword ) 
            res2.push(x.term)
    })
    console.log(finalArray);
    res2 = res2.splice(0, 10);
    cb(null, res2);
}


module.exports = {
    router: router,
    getRecommendation: getRecommendation,
    start: start
}