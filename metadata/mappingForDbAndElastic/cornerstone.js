module.exports = {
  "objects": [{
      "name": "trainings",
      "label": "Training Content",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": []

  }],
  "fields": [{
      "name": "ObjectId",
      "label": "ObjectId",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },{
      "name": "Author",
      "label": "Author",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1,
      "isMasked": 0
  },
  {
      "name": "Price",
      "label": "Price",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "TrainingType",
      "label": "Training Type",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "TrainingSubType",
      "label": "Training Sub Type",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "Provider",
      "label": "Provider",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "Title",
      "label": "Title",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "AvailableLanguages",
      "label": "Language",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "CreateDate",
      "label": "Created Date",
      "type": "datetime",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 0,
      "isActive": 1,
      "isMasked": 0
  },
  {
      "name": "ModifyDate",
      "label": "Modification Date",
      "type": "datetime",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 0,
      "isActive": 1,
      "isMasked": 0
  },
  {
      "name": "Description",
      "label": "Description",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "Duration",
      "label": "Duration",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "Subjects",
      "label": "Subjects",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "Version",
      "label": "Version",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "Availabilities",
      "label": "Availabilities",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "Skills",
      "label": "Skills",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "Location",
      "label": "Location",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "Instructors",
      "label": "Instructors",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
  },
  {
      "name": "view_href",
      "label": "URL",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1,
      "isMasked": 0
  }
]
}
