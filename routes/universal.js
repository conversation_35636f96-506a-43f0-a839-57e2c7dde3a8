/**
 * Created by man<PERSON><PERSON> on 29/8/16.
 */
var sanitizeHtml = require('sanitize-html');
var crontab = require('crontab')
var thisis = this;
var jsforce = require('jsforce');
var constants = require('./../constants/constants');
var appVariables=require('./../constants/appVariables');
var async = require('async');
var htmlparser = require("htmlparser2");
var request = require("request");
var spawn = require('child_process').spawn;
var xml2js = require('xml2js');
var fs = require('fs');
var path = require('path');
var url = require('url');
var async = require('async');
var CryptoJS = require("crypto-js");
var Promise = require('promise');
var Sharepoint = require('sharepoint-auth');
var constants = require('../constants/appVariables');
var commonFunctions = require('../utils/commonFunctions');
var transporter = require('nodemailer').createTransport({
  service: "Gmail",
  auth: {
    service: "Gmail",
    type: 'OAUTH2',
    user: constants.nodeMailer.user, // Your gmail address.
    // Not @developer.gserviceaccount.com
    clientId: constants.nodeMailer.clientId,
    clientSecret: constants.nodeMailer.clientSecret,
    refreshToken: constants.nodeMailer.refreshToken

  }
});
var transporterWebMail = require('nodemailer').createTransport({

  host: constants.transporterWebMail.host,
  port: 465,
  secure: true, // secure:true for port 465, secure:false for port 587
  auth: {
    user: constants.transporterWebMail.user,
    pass: constants.transporterWebMail.pass
  }

});

var lithiumBoards = require('./ApisForLithium/getBulkContent');


exports.checkArray = function (my_arr) {
  for (var i = 0; i < my_arr.length; i++) {
    if (my_arr[i] === "" || (!my_arr[i]))
      return false;
  }
  return true;
}

exports.getUserfromAccessToken = function (accTokn, req,callback) {
  var sql = "select u.id id,r.id roleId,u.user_email email,u.name name,r.role role from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where access_token= ? Limit 1";
  var q = connection[req.headers['tenant-id']].execute.query(sql, [accTokn], function (err, rows) {
    commonFunctions.errorlogger.debug(q.sql);
    if (!err) {
      if (rows.length > 0) {
        return callback(rows[0])
      }
      else {
        return callback(0)
      }
    }


  })
}

exports.getDataAuthJive = function (api_crawling_id,callback) {
  /**
   * 1. Get tokens from db
   * 2. Validate accessToken
   * 3. Reauthorize access token
   * 4. Send token to callback
   * 5. Save updated tokens in db
   */
  async.waterfall([
    cb => {
      var sql = "select * from content_source_jive where api_crawling_id=? Limit 1";
      connection.query(sql, [api_crawling_id], cb);
    },
    (tokens, fields, cb) => {
      tokens = tokens[0];
      var options = {
        method: 'GET',
        url: tokens.community_url + '/api/core/v3/people/@me',
        headers: {
          authorization: 'Bearer ' + tokens.auth_access_token
        },
        json: true
      };
      request(options, (e, r, d) => {
        if (e)
          cb(e);
        else if (d && d.id != -1) {
          cb(null, "updated", tokens)
        }
        else
          cb(null, "expired", tokens);
      });
    },
    (status, tokens, cb) => {
      if (status == "expired") {
        var options = {
          method: 'GET',
          url: tokens.community_url + '/oauth2/token',
          auth: {
            user: tokens.jive_client_id,
            pass: tokens.jive_client_secret
          },
          headers: {},
          formData: {
            refresh_token: tokens.auth_refresh_token,
            grant_type: 'refresh_token'
          },
          json: true
        };
        request(options, (e, r, d) => {
          if (e)
            cb(e);
          else {
            tokens.auth_access_token = d.access_token;
            cb(null, tokens);
            var sql = "UPDATE content_source_jive SET auth_access_token = '" + tokens.auth_access_token + "' WHERE auth_refresh_token='" + tokens.auth_refresh_token + "'";
            connection.query(sql, (e, r) => {
              commonFunctions.errorlogger.error(e);
              commonFunctions.errorlogger.info(r);
            });
          }
        })
      }
      else {
        cb(null, tokens);
      }
    },
    (tokens, cb) => {
      callback(tokens);
      cb();
    }
  ], (e, r) => {
    commonFunctions.errorlogger.error(e);
    commonFunctions.errorlogger.info(r);
  });
}



exports.searchTuning = function (indexName, type, searchString, callback) {
  var sql = "SELECT * FROM `boosting` where index_name=? and " +
    "type in (?) and is_boosted=1 and searchString= ?";
    connection.query(sql, [indexName, type, searchString], function (err, rows) {
    if (!err) {
      callback(rows);
    } else {
      callback(0);
    }
  });
}


exports.getIndexNames = function (generatedSearchClientId, req,callback) {
  var condition = ""
  if (generatedSearchClientId)
    condition = "where id=" + generatedSearchClientId;
  var ids = [];
  var sql = "select `user_content_source_mapping_id` from generate_search_client_history " + condition
  connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
    if (!err) {
      if (rows.length > 0) {
        rows.forEach(function (value) {
          var ab = value.user_content_source_mapping_id.split(',')
          ids = ids.concat(ab)
        })
      }

      //remove duplicate and empty
      ids = ids.filter((x, i, a) => a.indexOf(x) == i)
      ids = ids.filter(function (e) {
        return e
      });

      var sql = "SELECT cs.name name, case when ( ac.index_name!='NULL') then ac.index_name " +
        "else pc.index_name end as index_name FROM `content_sources` cs " +
        "left join api_crawling ac on cs.id=ac.content_source_id " +
        "left join public_crawling pc on cs.id=pc.content_source_id " +
        "join user_content_source_mapping ucsm on (ac.id=ucsm.api_crawling_id or pc.id=ucsm.public_crawler_id) " +
        "WHERE ucsm.id IN(" + ids + ")";
        connection[req.headers['tenant-id']].execute.query(sql, function (err, rows1) {

        var arr = [];
        for (var i = 0; i < rows1.length; i++) {
          if (rows1[i]["index_name"] != "")
            arr.push(rows1[i]["index_name"]);
        }
        callback(null, {index_name: arr, index_type_object: rows1});
      })

    }
  })
}



exports.sendEmailForRegister = function (email, forget_password_token) {
  var emailObject = {
    from: 'Searchunify<<EMAIL>>',
    to: email,
    // bcc: "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
    subject: "SearhUnify User Registration",
    html: registerMessage(email, forget_password_token)
  };
  // console.log("emailObject", emailObject);
  transporterWebMail.sendMail(emailObject, (error, response) => {
    commonFunctions.errorlogger.error("error", error);
    commonFunctions.errorlogger.info("response", response);
  });
}

exports.sendEmail = function (email, template, subject) {
  var emailObject = {
    from: 'Searchunify<<EMAIL>>',
    // from: config.get('fromEmail'),
    to: email,
    // bcc: "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
    subject: subject,
    html: template
  };
  // console.log("emailObject", emailObject);
  transporter.sendMail(emailObject, (error, response) => {
    commonFunctions.errorlogger.error("error", error);
    commonFunctions.errorlogger.info("response", response);
  });
}


exports.getDataAuthSharepoint = function (api_crawling_id, callback) {
  var sql = "select * from content_source_sharePoint where api_crawling_id=? Limit 1";
  connection.query(sql, [api_crawling_id], function (err, rows) {
    if (!err) {

      if (rows.length > 0) {
        return callback(1, rows);
      } else {
        return callback(0);
      }
    } else {
      return callback(0);
    }
  })
}
exports.getDataAuthConfluence = function (api_crawling_id, req,callback) {
  var sql = "SELECT * FROM `content_source_confluence` csl join api_crawling ac on csl.api_crawling_id=ac.id  where csl.api_crawling_id=? LIMIT 1";
  connection[req.headers['tenant-id']].execute.query(sql, [api_crawling_id], function (err, rows) {
    if (!err) {

      if (rows.length > 0) {
        return callback(1, rows);
      } else {
        return callback(0);
      }
    } else {
      return callback(0);
    }
  })
}

//Get sharepoint cookies to be sent in next calls
exports.SharepointCookiesAuth = function (cred, callback) {
  commonFunctions.errorlogger.info("cred", cred);
  if (cred.community_url.split('://').length > 1) {
    var options = {
      auth: {
        username: cred.username,
        password: cred.password
      },
      host: cred.community_url
    }
    Sharepoint(options, function (err, result) {
      if (err) {
        callback(err, null);
      }
      else {
        callback(null, result);
      }
    })
  }
  else {
    callback("Incorrect Community URL", null);
  }

}

exports.getSharepointFields = function (tableName, api_crawling_id, req,callback) {
  commonFunctions.errorlogger.info("api_crawling_id", api_crawling_id);
  /*select * from (SELECT content_source_id as contentSourceId,field_name,field_weight,field_type,id,is_activated,type as sharepoint_type,case when is_filter=1 then 'true' else 'false' end as status FROM `content_source_sharepoint_index_fields` where api_crawling_id=29) as a, (Select id as contentSourceType,type,type_label label,case when status=1 then 'true' else 'false' end as statusSources from `sharepoint_content_types_to_sync` where api_crawling_id=29 ) as b where a.sharepoint_type=b.contentSourceType order by sharepoint_type*/
  var sqlQuery = "select * from " +
    "(SELECT content_source_id as contentSourceId,field_name,field_weight,field_type,id,is_activated,type as sharepoint_type," +
    "case when is_filter=1 then 'true' else 'false' end as status FROM " +
    "`content_source_sharepoint_index_fields` where api_crawling_id=?) as a, " +
    "(Select id as contentSourceType,type,type_label label,case when status=1 then 'true' else 'false' end as statusSources from " +
    "`sharepoint_content_types_to_sync` where api_crawling_id=? ) as b where a.sharepoint_type=b.contentSourceType order by sharepoint_type  "

  var q = connection[req.headers['tenant-id']].execute.query(sqlQuery, [api_crawling_id, api_crawling_id], function (err, rows) {
    commonFunctions.errorlogger.debug(q.sql)
    if (!err) {
      //console.log("Rows==>",rows[0]);
      var idChangeFlag = rows[0].contentSourceType;
      var data = [];
      for (var i = 0; i < rows.length; i++) {

        var ObjToPass = {
          "id": rows[i].contentSourceType,
          "status": rows[i].statusSources,
          "type": rows[i].type,
          "fields": [],
          "label": rows[i].label
        }
        while (i < rows.length && idChangeFlag == rows[i].contentSourceType) {
          var FieldsObject = {};
          FieldsObject["field_name"] = rows[i].field_name;
          FieldsObject["field_weight"] = rows[i].field_weight;
          FieldsObject["id"] = rows[i].id;
          FieldsObject["field_type"] = rows[i].field_type;
          FieldsObject["is_activated"] = rows[i].is_activated;
          FieldsObject["sharepoint_type"] = rows[i].sharepoint_type;
          FieldsObject["status"] = rows[i].status;
          FieldsObject["type"] = rows[i].type;
          // FieldsObject["label"] = rows[i].label;
          ObjToPass["fields"].push(FieldsObject);

          i++;
        }
        if (i != rows.length) {
          idChangeFlag = rows[i].contentSourceType;
          i--;
        }
        data.push(ObjToPass);
      }
      callback(null, data);
    } else {
      callback(err);
    }
  })
}

exports.getSharepointActivatedObjects = function (mapping_id,callback) {

  var sql = "select scts.id,status,type name,type_label label,base_url baseUrl from sharepoint_content_types_to_sync scts JOIN user_content_source_mapping ucsm on scts.api_crawling_id=ucsm.api_crawling_id  Where status=1 and ucsm.id=?";
  connection.query(sql, [mapping_id], function (err, rows) {
    if (!err) {
      if (rows.length > 0) {

        return callback(rows)
      }
      else {
        return callback([])
      }
    }
  });

}

exports.getSharepointActivatedObjectFields = function (mappingId, callback) {
//group by name,objectName
  var sql = "select csif.id, cst.type objectName, csif.field_name name,csif.field_label label," +
    "csif.field_type type, csif.field_weight weight,csif.field_name display  " +
    "from content_source_sharepoint_index_fields csif " +
    "JOIN user_content_source_mapping ucsm on csif.api_crawling_id=ucsm.api_crawling_id " +
    "join sharepoint_content_types_to_sync cst on csif.type=cst.id " +
    "Where is_activated=1 and cst.status=1 and ucsm.id=?";
    connection.query(sql, [mappingId], function (err, rows) {
    if (!err) {
      if (rows.length > 0) {

        return callback(rows)
      }
      else {
        return callback(0)
      }
    }
  });

}


exports.getAutoprovisonToken = function (key, accessToken,req, callback) {

  var sql = ""
  sql = "select u.access_token from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where FIND_IN_SET(right(u.user_email,19),'<EMAIL>')!=0 and provisionKey='" + key + "'"
  if (accessToken)
    sql = "select u.access_token from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where FIND_IN_SET(right(u.user_email,19),'<EMAIL>')!=0 and u.access_token='" + accessToken + "'"

    connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
    if (!err) {
      if (rows.length)
        callback(rows[0])

      else
        callback(0)

    }
    else
      callback(0)
  })
}

// rajesh' code below ---------------
exports.getSuccessfactorConfig = function (url, req,callback) {
  var result = {
    elasticHost: config.get('elasticIndex.host'),
    elasticPort: config.get('elasticIndex.port')
  };
  var sql = "select pc.id, pc.url, pc.name, pc.index_name, pc.username, pc.password from " +
    "(SELECT * FROM `user_content_source_mapping` WHERE public_crawler_id > 0) ucsm " +
    "join (select * from public_crawling where custom_crawl_enable=1 and url like ?) pc on ucsm.public_crawler_id=pc.id";
    connection[req.headers['tenant-id']].execute.query(sql, [url + '%'], function (err, rows) {
    if (!err) {
      if (rows.length > 0) {
        commonFunctions.errorlogger.info(JSON.stringify(rows));
        result.contentsources = rows;
        return callback(result)
      }
      else {
        return callback([])
      }
    }
  });
};

exports.executeQuery = function (sql, params, req,callback) {
  connection[req.headers['tenant-id']].execute.query(sql, params, function (err, rows) {
    if (!err) {
      if (rows.length > 0) {
        return callback(rows)
      }
      else {
        return callback([])
      }
    } else {
      return callback([]);
    }
  });
}
// rajesh's code end ----------------
