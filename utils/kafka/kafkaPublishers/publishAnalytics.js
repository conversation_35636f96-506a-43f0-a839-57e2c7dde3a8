const kafkaLib = require("../kafka-lib");
const config = require('config');


const publishLogAnalytics = (req, body) => {
    kafkaLib.publishMessage({
        topic   :  config.get("kafkaTopic.publishAnalytics"), 
        messages: [{
            type: 'authFailure',
            value: JSON.stringify({
            generatedAnalytics: {
                Authentication : [
                    {
                        string:`Authentication failed for content source`,
                        key: 'Authentication failed',
                        field: `authFailure_${body.name}`,
                        visibility:true
                    }
                ]   
            },
            analyticsObject: { oldObj: body, newObj: {}},
            user: {name:req.headers.session.name, email: req.headers.session.email},
            info: `Authentication failed for content source <${body.name}> content source`,
            tenantId: req.headers['tenant-id'] ,
            ts: new Date().toISOString(),
            object: 'Content Source',
            eventType: 'ContentSourceAnalytics',
            isJson: true,
            event:'adminLog'
            }),
            key: req.headers['tenant-id']
        }]
    });
}

exports.publishLogAnalytics = publishLogAnalytics;