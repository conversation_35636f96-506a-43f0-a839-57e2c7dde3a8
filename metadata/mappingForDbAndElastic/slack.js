

module.exports = {
  "objects": [{
    "name": "message",
    "label": "message",
    "status": 1,
    "boosting_factor": 1,
    "base_url": "",
    "fields": []

  }],
  "fields": [
    {
      "name": "id",
      "label": "Id",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "title",
      "label": "Title",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "view_href",
      "label": "View Href",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1,
      "isMasked": 0
    },
    {
      "name": "type",
      "label": "Type",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "authorName",
      "label": "Author Name",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1,
      "isMasked": 0
    },
    {
      "name": "authorMail",
      "label": "Author Email",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1,
      "isMasked": 0
    },
    {
      "name": "post_time",
      "label": "Created Date",
      "type": "datetime",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isMerged":1,
      "isSortable": 1,
      "isSearchable": 0, //analyzer
      "isActive": 1,
      "isMasked": 0
    },
    {
      "name": "threadMessages",
      "label": "Thread Messages",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "threadCount",
      "label": "Thread Message Count",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "attachmentContent",
      "label": "Attachment Description",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "attachment",
      "label": "Attachment",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "channelId",
      "label": "Channel Id",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "channelName",
      "label": "Channel Name",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "parentMessageId",
      "label": "Parent Message Id",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": 'botReply',
      "label": 'Bot Reply',
      "type": 'string',
      "isFilterable": 1, // indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, // analyzer
      "isActive": 1
    },
    {
      "name": 'channelType',
      "label": 'Channel Type',
      "type": 'string',
      "isFilterable": 1, // indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, // analyzer
      "isActive": 1
    },
    {
      "name": "attachment_multiple",
      "label": "Attachment Multiple",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
  }


  ]
}
