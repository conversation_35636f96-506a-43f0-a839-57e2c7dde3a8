#!/bin/bash
echo "Going outside"
CURRENT=`pwd`
FILENAME=`basename "$CURRENT"`
cd ..
FILE="$FILENAME"-encrypted
if [ -d "$FILE" ]; then
    echo "$FILE exist"
else
    echo "Making new directory"
    mkdir "$FILE"
    echo "Copying Files."
    cp -r adminSearchUnify-cli/. adminSearchUnify-cli-encrypted
    echo "Moving encrypted build to codeBase"
    cp adminSearchUnify-cli/node adminSearchUnify-cli-encrypted
    echo "Going to code"
    cd adminSearchUnify-cli-encrypted/
    echo "running app.js"
    ./node app.js e
fi


