const config = require('config');
const kafkaLib = require('./kafka-lib');

// on startup
const kafkaStartup = () => {
  kafkaLib.initializekafka({
    clientId: 'admin',
    brokers: [config.get('statusPageService.kafka.host')],
    retryConfig: {
      maxRetryTime: config.get('statusPageService.kafka.maxRetryTime'), // default 30000
      initialRetryTime: config.get('statusPageService.kafka.initialRetryTime'), // default 300
      retries: config.get('statusPageService.kafka.retries') // default is 5
    }
  });
};

module.exports = {
    kafkaStartup
}