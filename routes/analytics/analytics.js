var express = require('express');
var router = express.Router();
var async = require('async');
var commonFunction = require('../../utils/commonFunctions');
var excelReportGenerator = require('./excelReportGenerator');
var request = require('request');
var moment = require('moment');
var elastic = require('elasticsearch');
var searchClient = require('../admin/searchClient');
var emailTemplates = require('../../routes/emailTemplates');
const searchunifyEmail = require('../../Lib/email');
var commonFunctions = require('./../../utils/commonFunctions');
const aes256 = require('nodejs-aes256');
var appVariables = require('../../constants/appVariables')
const notification = require('../../routes/admin/notifications');

const redis = require('redis');
const redisclient = redis.createClient(config.get("redis.redisPort"), config.get("redis.redisHost"));

var clientCS = new elastic.Client({
    host: 'http://' + config.get('elasticIndexCS.host') + ':' + config.get('elasticIndexCS.port')
});

router.post("/getActiveReportsInfo", function (req, res, next) {
    var uid = req.body.uid;
    var version = req.body.version;
    async.auto({
        getAddonsStatus: cb => {
            commonFunction.getAddonsStatus(req, (error, result) => {
                if (result && result.length) {
                    cb(null, result)
                } else {
                    cb(null, 0)
                }
            });
        },
        getAnalyticsReports: cb => {
            let sql;
            if (uid == "") {
                sql = `SELECT  analytics_reports.id,analytics_reports.name as label,analytics_reports.description, analytics_reports.id as analytics_report_id
                FROM  analytics_reports WHERE version = ?`;
            } else {
                sql = `SELECT  search_client_to_analytics_report.id,analytics_report_id,is_enabled, search_client_to_analytics_report.label 
                FROM search_clients, search_client_to_analytics_report, analytics_reports
                WHERE search_clients.id = search_client_to_analytics_report.search_client_id
                    AND search_client_to_analytics_report.analytics_report_id = analytics_reports.id
                    AND analytics_reports.version = ? AND search_clients.uid = ?`;
            }
            connection[req.headers['tenant-id']].execute.query(sql, [version, uid], (error, result) => {
                var data = [];

                if (uid == "") {
                    for (let i = 0; i < result.length; i++) {
                        result[i].is_enabled = 1;
                    }
                }
                if (!error) {
                    cb(null, result);
                }
            })
        },
        analyticsReport: ["getAnalyticsReports", "getAddonsStatus", (results, cb) => {
            let addOnResult = results.getAddonsStatus;
            let caseDeflection = addOnResult[7] ? addOnResult[7].is_installed : 0;
            let emailTracking = addOnResult[8] ? addOnResult[8].is_installed : 0;

            if (!caseDeflection) {
                results.getAnalyticsReports.map(x => { if (x.analytics_report_id == 14) { x.is_enabled = 0; } });
                results.getAnalyticsReports = { "reports": results.getAnalyticsReports, "emailTracking": emailTracking }
                cb(null, results.getAnalyticsReports);
            } else {
                results.getAnalyticsReports = { "reports": results.getAnalyticsReports, "emailTracking": emailTracking }
                cb(null, results.getAnalyticsReports)
            }

        }],
    }, (error, result) => {
        if (error)
            commonFunction.errorlogger.error("error", error);
        else
            res.send(result.analyticsReport)
    });
});

router.get("/getAllSearchPlatforms", function (req, res, next) {
    var sql = 'SELECT search_clients.id,search_clients.name,search_clients.ab_test_parent,search_clients.search_client_type_id,search_clients.sorting_methods,search_clients.uid,search_clients.client_href,search_clients.created_date ,search_clients.recommendations ,search_clients.auto_boosting,search_clients.knowledge_graph ,search_clients.facet_interpreter,search_clients.auto_spell_corrector,search_clients.featured_snippet,search_clients.kcsEnabled,search_clients.is_case_deflected_shown,search_clients.channel_id ,search_clients.recent_searches ,search_clients.recent_searches_count ,search_clients.similar_search ,search_clients.merged_facets,search_clients.did_you_mean ,search_clients.did_you_mean_dict_type,deflection_formula.external_user_enabled, deflection_formula.user_attribute_tracking_enabled, deflection_formula.user_attribute_label, deflection_formula.user_attribute_variable FROM search_clients left join deflection_formula on search_clients.id = deflection_formula.search_client_id';
    var q = connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
        if (!err) {
            rows = rows.filter(row => {
                if (req.query.clonnedAbTestSc) {
                  return true;
                } else {
                  return row.ab_test_parent === null;
                }
              });
            res.send(rows);
        }
    });
});

router.get("/getDirectViewEnabled", function (req, res, next) {
    var sql = 'SELECT search_clients.name,search_clients.uid,deflection_formula.directly_viewed_results FROM search_clients left join deflection_formula on search_clients.id = deflection_formula.search_client_id';
    connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
        if (!err) {
            res.send(rows);
        }
    });
});

router.post("/getContentAuthData", function (req, res, next) {
    const indexName = req.query.indexName;
    var sql = `SELECT csa.client_id, csa.client_secret, csa.instanceURL, csa.accessToken, csa.refreshToken, 
    cs.elasticIndexName FROM content_source_authorization csa 
    left join content_sources cs on cs.id = csa.content_source_id where cs.elasticIndexName = ?`;
    connection[req.headers['tenant-id']].execute.query(sql, [indexName], function (err, rows) {
        if (!err) {
            if (rows && rows.length > 0) {
                rows[0].client_secret = aes256.decrypt(appVariables.analytics.encryptionKey, rows[0].client_secret)
                rows[0].accessToken = aes256.decrypt(appVariables.analytics.encryptionKey, rows[0].accessToken)
                rows[0].refreshToken = aes256.decrypt(appVariables.analytics.encryptionKey, rows[0].refreshToken)
                res.send(rows);
            } else {
                res.send('No data available to be fetched').status(400);
            }
        } else {
            res.send({ "error": err }).status(400);
        }
    });
});

const setTypeStatistics = (payload, result, res) => {
    if (payload.csv) {
        let fields = ['Content Source Name', 'Count', 'Index size'];
        var myData = [];
        commonFunction.errorlogger.info(result);
        for (let i = 0; i < Object.keys(result).length; i++) {
            myData.push({ "Content Source Name": Object.keys(result)[i], "Count": result[Object.keys(result)[i]].count, "Index size": result[Object.keys(result)[i]].size });
        }
        excelReportGenerator.getExcelReport({ title: payload.label, from: "", to: "", data: myData, fields: fields, isNested: false }, wb => {
            wb.write('Searches-Count-Without-Result.xlsx', res);
        });
    }
    else if (payload.email) {
        let fields = ['Content Source Name', 'Count', 'Index size'];
        var myData = [];
        commonFunction.errorlogger.info(result);
        for (let i = 0; i < Object.keys(result).length; i++) {
            myData.push({ "Content Source Name": Object.keys(result)[i], "Count": result[Object.keys(result)[i]].count, "Index size": result[Object.keys(result)[i]].size });
        }
        excelReportGenerator.getExcelReport({ title: payload.label, from: "", to: "", data: myData, fields: fields, isNested: false }, wb => {
            var fileName = 'Search-index-by-content-source~' + new Date() + '.xlsx'
            var filePath = 'reports/reports/' + fileName;
            wb.write(filePath);
            // for local let fileurl = "http://localhost/backend/anreports/" + fileName
            let fileurl = config.get('adminURL') + '/anreports/' + fileName;
            let sessionInfo = {}
            sessionInfo.label = "Search index by content source";
            var body = emailTemplates.analyticsEmailTemplate(sessionInfo, payload.email, "", fileurl);
            searchunifyEmail.sendEmail(payload.email, payload.label, body, (response) => {
                if (!response) {
                    res.send({ message: 'File not sent via email' });
                }
                else {
                    res.send({ message: 'File will be sent via email' });
                }
            });
        })
    } else {
        res.send(result);
    }
};
  
router.post("/getTypesStatistics", function (req, res, next) {
    commonFunctions.errorlogger.info('get doc count and size of content source ', JSON.stringify(req.body));
    let headers = {
        "Content-Type": "application/json",
        "index-service-secret": config.get("indexService.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        "timeout" : 120000
    }
    let body = {
        label: req.body.label,
        csv: req.body.csv ? true : false
    }
    if (req.body.email) {
        body.email = req.body.email;
    }

    commonFunctions.httpRequest('POST', config.get("indexService.url") + '/index-service/analytics/getTypesStatistics', '', body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({ "error": error }).status(400);
        } else {
            if(result.status === false && result.code === 429 && result.message === "Api limit exceeded !!"){
                res.status(429).send(result);
            } else{
                setTypeStatistics(req.body, result, res);
            }
        }
    });
});

const setAddedContent = (payload, res, result) => {
    if(payload.csv || payload.email) {
        var fields = ["Content Source", "Quarter", "Count"];
        var myData = [];

        var keys = Object.keys(result);
        for (var j = 0; j < keys.length; j++) {
            var entry = {};
            entry[fields[0]] = {};
            entry[fields[0]].value = keys[j];
            entry[fields[0]][fields[1]] = [];
            var keys2 = Object.keys(result[keys[j]]);
            for (var i = 0; i < keys2.length; i++) {
                var val = {};
                val.value = keys2[i];
                val[fields[2]] = result[keys[j]][keys2[i]].doc_count
                entry[fields[0]][fields[1]].push(val);
            }
            myData.push(entry);
        }
        if (payload.value) {
            excelReportGenerator.getExcelReport({ title: payload.label, from: "", to: "", data: myData, fields: fields, isNested: true }, wb => {
                wb.write('Newly-added-content-sources.xlsx', res);
            });
        } else {
            let fileName = 'Newly-added-content-sources~' + new Date() + '.xlsx';
            var filePath = 'reports/reports/' + fileName;

            excelReportGenerator.getExcelReport({ title: payload.label, from: "", to: "", data: myData, fields: fields, isNested: true }, wb => {
                wb.write(filePath);

                if (payload.email) {
                    console.log('sending to ' + payload.email);
                    
                    let fileurl = '/anreports/' + fileName;
                    console.log('>>>fileurl', fileurl);
                    notification.sendAnalyticsTrackinReport(payload, { "from": "", "to": "" }, config.get('adminURL') + '/' + fileurl);
                    res.send({ message: 'File will be sent via email' });
                } else {
                    res.send({ message: 'File can not be sent via email' });
                }
            });
        }
    }
    else {
        res.send(result);
    };
};

router.post("/getAddedContent", function (req, res, next) {
    commonFunctions.errorlogger.info('get range of document count ', JSON.stringify(req.body));
    let headers = {
        "Content-Type": "application/json",
        "index-service-secret": config.get("indexService.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        "timeout" : 120000
    }

    let body = {
        label: req.body.label,
        csv: req.body.csv ? true : false
    }
    if (req.body.email) {
        body.email = req.body.email;
    }

    commonFunctions.httpRequest('POST', config.get("indexService.url") + '/index-service/analytics/getAddedContent', '', body, headers, async function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({ "error": error }).status(400);
        } else {
            if(result.status === false && result.code === 429 && result.message === "Api limit exceeded !!"){
                res.status(429).send(result);
            } else{
                setAddedContent(req.body, res, result);
            }
        }
    });
});

router.get("/getRecentSearch",(req,res) => {
    var options = {
        method: 'GET',
        url: config.get("searchService.url") + `/search/getRecentSearches?tenantId=${req.headers["tenant-id"]}&&tpk=${req.headers.session.tpk}`,
        headers: {
            'Content-Type': 'application/json',
            'search-secret': config.get('searchService.secret')
          },
        json: true,
        rejectUnauthorized: false
    };
    request(options, function (error, response, body) {
        if(error){
            res.send({"error":error});
        }else{
            res.send(body)
        }
    })
})
function getActiveContentSources(uid, req,  cb) {
    commonFunction.getDisplayMapping(uid, req, function (displayFields) {
        var contentSources = {};
        displayFields.map(r => {
            contentSources[r.contentSource] = r.contentSourceLabel;
            return r;
        });
        var indexes = Object.keys(contentSources).map(r => { return [r, contentSources[r]]; });
        // console.log(indexes);
        cb(null, indexes);
    });
}

function getDisplayMappingFromDisplayFields(displayFields) {
    var displayMapping = {};
    displayFields.map(function (displayField, idx) {
        if (!displayMapping[displayField.contentSource]) {
            displayMapping[displayField.contentSource] = {
                indexDisplayName: displayField.contentSourceLabel
            };
        }
        if (!displayMapping[displayField.contentSource][displayField.objectName])
            displayMapping[displayField.contentSource][displayField.objectName] = {};
        displayMapping[displayField.contentSource][displayField.objectName][displayField.fieldLabel] = displayField.fieldName;
    });
    return displayMapping;
}


function getSearchClientSettings(uid,req, callback) {
    connection[req.headers['tenant-id']].execute.query(`SELECT id FROM search_clients WHERE uid=?`, [uid], (e, r, f) => {
        if (e || !r.length) callback(e);
        else {
            searchClient.getSearchClient(r[0].id,req,callback)
        }
    });
}

function getTitleAndSummaryField(uid, displayMapping, indexName,req, cb) {
    getSearchClientSettings(uid,req, (err, data) => {
        //console.log(err, JSON.stringify(data));
        if (!err) {
            try {
                var fields = Object.keys(displayMapping).filter(k => { return k != 'indexDisplayName' });
                for (i = 0; i < fields.length; i++) {
                    displayMapping[fields[i]]._titleField_ = data.sources.filter(x => { return x.index == indexName }).map(y => { let o = y.objects.filter(z => z.name == fields[i]); return o }).reduce(commonFunction.reduceObject).map(z => {
                        return z.fields
                    }).reduce(commonFunction.reduceObject).filter(h => {
                        return h.title;
                    })[0].name;

                    displayMapping[fields[i]]._summaryField_ = data.sources.filter(x => { return x.index == indexName })
                        .map(y => y.objects.filter(z => z.name == fields[i]))
                        .reduce(commonFunction.reduceObject)
                        .map(y => y.fields.filter(z => {
                            if (z.use && z.use.use_as && z.use.use_as != 'Filter' && !z.title)
                                return true;
                            else
                                return false;
                        }))
                        .reduce(commonFunction.reduceObject).map(x=>{return x.name});
                }

            } catch (e) {

            }
        }
        cb(null, displayMapping);
    });
}

function generateLinkForArticles(uid, response,req ,cb) {
    getSearchClientSettings(uid,req, (err, data) => {
        if (!err) {
            for (var it = 0; it < response.length; it++) {
                try {
                    response[it]["view_href"] = data.sources.filter(x => x.enabled).map(y => { let o = y.objects; return o }).reduce(commonFunction.reduceObject).find(x => x.name == response[it]["_type"]).base_href

                    var testRegex = /{{.*?}}/g;
                    var str = response[it]["view_href"];
                    var m;

                    while ((m = testRegex.exec(str)) !== null) {
                        // This is necessary to avoid infinite loops with zero-width matches
                        if (m.index === testRegex.lastIndex) {
                            testRegex.lastIndex++;
                        }
                        m.forEach((match, groupIndex) => {
                            response[it]["view_href"] = response[it]["view_href"].replace(match, response[it]["_source"][match.replace("{{", "").replace("}}", "")])
                        });
                    }
                } catch (e) {

                }
                if (!response[it]["view_href"])
                    response[it]["view_href"] = response[it]["_id"] || response[it]["_source"]['id'];
            }
            cb(null, response);
        } else {
            cb({ "error": err });
        }
    });
}

function getDocumentsWithLargeContent(index, uid, req, cb) {
    try {
        commonFunction.getDisplayMapping(uid, req, function (displayFields) {
            displayMapping = getDisplayMappingFromDisplayFields(displayFields);
            if (displayMapping[index]) {
                getTitleAndSummaryField(uid, displayMapping[index], index, req, (err, newMapping) => {
                    var typeName = Object.keys(newMapping).filter(x => (x != 'indexDisplayName'));
                    if (typeName.length == 0) {
                        cb({ "error": "No TypeName is defined" });
                    }
                    else {
                        var summaryFieldScript = "";
                        for (var j = 0; j < typeName.length; j++) {
                            if (newMapping[typeName[j]]['_titleField_']) {
                                var titleField = newMapping[typeName[j]]['_titleField_'] + '.keyword';
                                summaryFieldScript += "doc[\"" + titleField + "\"].toString().length()"
                                if (newMapping[typeName[j]]['_summaryField_'] && newMapping[typeName[j]]['_summaryField_'].length) {
                                    summaryFieldScript += "+";
                                }
                            }

                            if (newMapping[typeName[j]]['_summaryField_']) {
                                for (var i = 0; i < newMapping[typeName[j]]['_summaryField_'].length; i++) {
                                    var summaryField = newMapping[typeName[j]]['_summaryField_'][i] + '.keyword';
                                    summaryFieldScript += "doc[\"" + summaryField + "\"].toString().length()"
                                    if (j != (typeName.length - 1) || i != newMapping[typeName[j]]['_summaryField_'].length - 1) {
                                        summaryFieldScript += "+";
                                    }
                                }
                            }

                        }
                        if (summaryFieldScript == "") {
                            cb({ "error": "No Summary Field is defined" });
                        }
                        else {
                            let options = {
                                method: "POST",
                                url: config.get("indexService.url") +"/index-service/open-search/query",
                                headers: {
                                    "Content-Type": "application/json",
                                    "tenant-id": req.headers['tenant-id'],
                                    "index-service-secret": config.get("indexService.sharedSecret"),
                                },
                                body: {
                                    index: index,
                                    type: Object.keys(newMapping).filter(x => (x != 'indexDisplayName')),
                                    queryData: {
                                        "from": 0,
                                        "size": 50,
                                        "sort": {
                                            "_script": {
                                                "type": "number",
                                                "script": {
                                                    "source": summaryFieldScript
                                                },
                                                "order": "desc"
                                            }
                                        }
                                    },
                                    tenantId: req.headers["tenant-id"],
                                },
                                json: true
                            };
                            request(options, (error, response, resBody) => {
                                if (resBody.error)
                                    cb({ "error": err });
                                else {
                                    try {
                                        let data = [];
                                        const types = Object.keys(newMapping).filter(x => (x != 'indexDisplayName'));

                                        for(const type of types) {
                                            data = [...data, ...response.body[type].hits.hits];
                                        }
                                        
                                        generateLinkForArticles(uid, data, req, (ein, din) => {
                                            if (ein) {
                                                cb(ein);
                                                return;
                                            }
                                            din = din.map((item) => {
                                                return {
                                                    //"articleTitle": item._source.title || item._source.subject || item._source.Title || item._source.Subject || item._source.Full_Name__c || item._source.name || item._source.Name,
                                                    "articleTitle": item._source[newMapping[item.type]._titleField_] || item._source.title || item._source.subject,
                                                    "articleLink": item._source[newMapping[item.type]['View Href']] || item._source.view_href || item._source.url || "",
                                                    "contentLength": (item.sort && item.sort[0]) ? item.sort[0] : "N/A"
                                                };
                                            });

                                            cb(null, din);
                                        });
                                    } catch (e) {
                                        console.log(e);
                                        cb({ "error": e });
                                    }
                                }
                            });
                        }
                    }
                });

            } else {
                cb({ "error": "No mapping present for index: " + index });
            }
        });
    } catch (e) {
        cb({ "error": e });
    }
}

router.get("/getDocumentsWithLargeContent", function (req, res, next) {
    // res.send({ 'error': "Get not supported" });
});

router.post("/getDocumentsWithLargeContent", function (req, res, next) {
    console.log('Calling function getDocumentsWithLargeContent');
    getActiveContentSources(req.body.uid, req, (error, indexes) => {
        if (!error) {
            // console.log(indexes);
            var indexId = req.body.indexId;
            indexId = indexId ? indexId : 0;
            indexId = indexId < 0 ? 0 : indexId;
            if (indexes.length > 0 && indexes[indexId]) {
                console.log('Calling function getDocumentsWithLargeContent');
                getDocumentsWithLargeContent(indexes[indexId][0], req.body.uid,req, (err, data) => {
                    if (!err) {
                        if (req.body.csv) {
                            var fields = ['Article Title', 'Article Link', 'contentLength'];
                            try {
                                var myData = [];
                                data.forEach(b => {
                                    myData.push({ "Article Title": b.articleTitle, "Article Link": b.articleLink, "contentLength": b.contentLength });
                                });
                                excelReportGenerator.getExcelReport({ title: 'Documents With Large Content', from: req.body.from, to: req.body.to, data: myData, fields: fields, isNested: false }, wb => {
                                    wb.write('documentsWithLargeContent.xlsx', res);
                                });
                                // var json2csvParser = new json2csv({ fields: fields });
                                // var result = json2csvParser.parse(myData);
                                // res.attachment('documentsWithLargeContent.csv');
                                // res.status(200).send(result);

                            } catch (err) {
                                // Errors are thrown for bad options, or if the data is empty and no fields are provided.
                                // Be sure to provide fields if it is possible that your data array will be empty.
                                console.error(err);
                            }
                        }else if (req.body.email) {
                            var fields = ['Article Title', 'Article Link', 'contentLength'];
                            try {
                                var myData = [];
                                data.forEach(b => {
                                    myData.push({ "Article Title": b.articleTitle, "Article Link": b.articleLink, "contentLength": b.contentLength });
                                });
                                excelReportGenerator.getExcelReport({ title: 'Documents With Large Content', from: new Date(), to: new Date(), data: myData, fields: fields, isNested: false }, wb => {
                                   // wb.write('documentsWithLargeContent.xlsx', res);
                                       var fileName = 'documentsWithLargeContent' + new Date() + '.xlsx'
                                        var filePath = 'reports/reports/' + fileName;
                                        wb.write(filePath);

                                        let fileurl = config.get('adminURL') + '/anreports/' + fileName;
                                            let sessionInfo = {}
                                            sessionInfo.label = "Documents-With-Large-Content";
                                            let range = {}
                                            range.from = new Date();
                                            var body = emailTemplates.analyticsEmailTemplate(sessionInfo, req.body.email,range , fileurl);
                                            searchunifyEmail.sendEmail(req.body.email, 'Documents-With-Large-Content', body, (response) => {
                                                if (!response) {
                                                    res.send({ message: 'File not sent via email' });
                                                }
                                                else {
                                                    res.send({ message: 'File will be sent via email' });
                                                }
                                            });


                                });
                                // var json2csvParser = new json2csv({ fields: fields });
                                // var result = json2csvParser.parse(myData);

                                // fs.writeFileSync('documentsWithLargeContent.csv', result, function(err) {
                                //     if (err) throw err;
                                //     console.log('file saved');
                                //   });
                                // res.attachment('documentsWithLargeContent.csv');
                                // res.status(200).send(result);

                            } catch (err) {
                                // Errors are thrown for bad options, or if the data is empty and no fields are provided.
                                // Be sure to provide fields if it is possible that your data array will be empty.
                                console.error(err);
                            }

                            // commonFunction.errorlogger.info(tree);
                            // for (let i = 0; i < Object.keys(tree).length; i++) {
                            //     myData.push({ "Content Source Name": Object.keys(tree)[i], "Count": tree[Object.keys(tree)[i]].count, "Index size": tree[Object.keys(tree)[i]].size });
                            // }
                            // excelReportGenerator.getExcelReport({ title: req.body.label, from: "", to: "", data: myData, fields: fields, isNested: false }, wb => {
                            //     var fileName = 'Search-index-by-content-source~' + new Date() + '.xlsx'
                            //     var filePath = 'reports/reports/' + fileName;
                            //     wb.write(filePath);
                            //     // for local let fileurl = "http://localhost/backend/anreports/" + fileName
                            //     let fileurl = config.get('adminURL') + '/anreports/' + fileName;
                            //     let sessionInfo = {}
                            //     sessionInfo.label = "Search index by content source";
                            //     var body = emailTemplates.analyticsEmailTemplate(sessionInfo, req.body.email, "", fileurl);
                            //     searchunifyEmail.sendEmail(req.body.email, 'Search-index-by-content-source', body, (response) => {
                            //         if (!response) {
                            //             res.send({ message: 'File not sent via email' });
                            //         }
                            //         else {
                            //             res.send({ message: 'File will be sent via email' });
                            //         }
                            //     });
    
                            // })
                         } else {
                            console.log('>>>>final response of data', data);
                            res.send(data);
                        }
                    } else {
                        res.send(err);
                    }
                });
            } else {
                // console.log('No Content Sources Available');
                res.send({ "error": "No Content Sources Available" });
            }
        } else {
            res.send({ "error": "Unable to retrieve content sources" });
        }
    });

});

router.post("/getContentSourceList", function (req, res, next) {
    if (req.body.uid) {
        getActiveContentSources(req.body.uid, req, (error, indexes) => {
            if (!error) {
                res.send(indexes);
            } else {
                res.send({ "error": "Unable to retrieve content sources" });
            }
        });
    } else {
        res.send({ "error": "Invalid parameters" });
    }
});

module.exports = {
    router
}
