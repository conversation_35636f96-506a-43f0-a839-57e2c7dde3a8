// remove cron process
var mysql = require('mysql');
var fs = require('fs');
var path = require('path');
var environment = require(path.join(__dirname, '..','..','..','..','routes','environment'));
process.env.NODE_ENV = environment.configuration;
var commonFunctions = require('../../../../utils/commonFunctions');

commonFunctions.errorlogger.warn('Initiating cron remove process...');
commonFunctions.errorlogger.warn('node environment configuraion reading');

var configPath = path.join(__dirname, '..','..','..','..','config');
process.env.NODE_CONFIG_DIR = configPath;
commonFunctions.errorlogger.info('Environment',environment.configuration);
config = require('config');
var cronUtility = require('../../cronUtility');
var crawlstatusfilepath = path.join(__dirname,'..','..','..','..','..','logs','crawlstatus.json');

commonFunctions.errorlogger.warn('removing cron');
cronUtility.deleteCron('test_helpdoc_cron_node', function(status){
  commonFunctions.errorlogger.info('cron status:' +status); 
});

// update status of crawling in database
var db_config = {
  host: config.get('databaseSettings.host'),
  user: config.get('databaseSettings.user'),
  password: config.get('databaseSettings.password'),
  database: config.get('databaseSettings.database'),
  port: config.get('databaseSettings.mysqlPORT'),
  // multipleStatements: true,
  // socketPath : '/var/run/mysqld/mysqld.sock'
  //config.get('databaseSettings.connectionLimit')
};
var connection = null;
var crawlstatus = {};
var contentSourceId = -1;
try{
  crawlstatus = fs.readFileSync(crawlstatusfilepath);
  crawlstatus = JSON.parse(crawlstatus);
  contentSourceId = crawlstatus.contentSourceId;
  crawlstatus.status=0;
  fs.writeFileSync(crawlstatusfilepath, JSON.stringify(crawlstatus));
}catch(e){

}

function handleDisconnect() {
  connection = mysql.createConnection(db_config); // Recreate the connection, since
  // the old one cannot be reused.

  commonFunctions.errorlogger.info('Db config',db_config);
  commonFunctions.errorlogger.warn("in the handleDisconnect");
  connection.connect(function(err) { // The server is either down
    if (err) { // or restarting (takes a while sometimes).
      commonFunctions.errorlogger.error('error when connecting to db:', err);
      setTimeout(handleDisconnect, 2000);  
    } else {
      commonFunctions.errorlogger.warn("connection variable created ");
      connection.query('update content_sources set pid=0 where id=?',[contentSourceId], function (err, rows) {
        if (!err) {
          if (rows.length > 0) {
            return callback(rows)
          }
          else {
            return callback([])
          }
        }else{
          return callback([]);
        }
      });
    }
  }); // process asynchronous requests in the meantime.
  //  If you're also serving http, display a 503 error.
  connection.on('error', function(err) {
    commonFunctions.errorlogger.error('db error', err);
    if (err.code === 'PROTOCOL_CONNECTION_LOST') { // Connection to the MySQL server is usually
      handleDisconnect(); // lost due to either server restart, or a
    } else if (err.code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
      restart();
    } else { // connnection idle timeout (the wait_timeout
      throw err; // server variable configures this)
    }
  });
}

if(crawlstatus.contentSourceId)
  handleDisconnect();
else
  commonFunctions.errorlogger.warn('No content source id present');