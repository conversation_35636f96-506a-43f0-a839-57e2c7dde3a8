var request = require("request");
var commonFunctions = require('../../utils/commonFunctions');
const aes256 = require('nodejs-aes256');
const md5 = require('md5')
let { getBlogByKey, getEventByKey, getLibraryByKey, getMessageThreadBy<PERSON>ey,delBy<PERSON>ey } = require('../../utils/higherLogicUtils');


const authenticateUser =  (resultData,isConnect) => {
    return new Promise((resolve,reject)=>{
        let options = {
            url :'https://api.connectedcommunity.org/api/v2.0/Authentication/Login',
            method: 'POST',
            headers:{
                HLIAMKey: resultData.authorization.client_secret,
            },
            body:{
                username:resultData.authorization.user_name,
                password:resultData.authorization.password
            },
            json: true,
        };

        request(options, async (error, response) =>{
            if (error) return reject(error)
            else {
                if (response.statusCode != 200) {
                    reject({status_code:response.statusCode,status_message:response.statusMessage});
                }
                else {
                    commonFunctions.errorlogger.info(response.body);
                    resultData.authorization.accessToken = response.body.Token;
                    resultData.authorization.username = resultData.authorization.user_name;
                    delete resultData.authorization.user_name;
                    if (isConnect) {
                        let communityIds;
                        try {
                            communityIds = await fetchAllCommunities(resultData.authorization.client_secret, response.body.Token, resultData.contentSource.id,resultData.projectIds);

                        } catch (error) {
                            commonFunctions.errorlogger.error("Error Inside fetchAllCommunities :: ", error);
                        }
                        resultData.spacesORboards = communityIds;
                    }
                    resolve(resultData);
                }
            }
        });

    });

}

const fetchAllCommunities = (HLIAMKey,HLAuthToken,contentSourceId,projectIds = [])=>{
    return new Promise((resolve,reject)=>{
        let options = {
            url :'https://api.connectedcommunity.org/api/v2.0/Communities/GetViewableCommunities?includeStatistics=true',
            method: 'GET',
            headers:{
                HLIAMKey: HLIAMKey,
                HLAuthToken: HLAuthToken
            },
            json: true,
        };

        request(options, (error, response) =>{
            if (error) return reject(error)
            else{
                if (response.statusCode != 200) {
                    resolve([]);
                }else{
                    let resToSend = response.body.map((com)=>{
                        return {
                                content_source_id: contentSourceId,
                                spaceName:com.CommunityName,
                                spaceUrl:com.CommunityName,
                                spaceId:`${com.CommunityKey},${com.DiscussionKey},${com.LibraryKey},${com.Statistics.DiscussionPostCount},${com.CommunitySize}`,
                                isSelected: projectIds.includes(`${com.CommunityKey},${com.DiscussionKey},${com.LibraryKey},${com.Statistics.DiscussionPostCount},${com.CommunitySize}`) ? 1 : 0,
                                spaceKey: "Community",
                                spaceType:"Subscribed"
                            }
                    })
                    resolve(resToSend);
                }

            }
        })

    });

} 

const reindexCommunities =  (csData, sort,req, callback) => {
  csData.authorization.user_name = csData.authorization.user_name || csData.authorization.username;
  authenticateUser(csData, true).then(communities => {
    commonFunctions.insertSpacesBoards(csData.authorization.content_source_id, communities.spacesORboards,req, function () {
        commonFunctions.getPlacesBySortParam(csData.authorization.content_source_id, sort,req, function (err, data) {
            callback(null, data);
        });
    });
  }).catch(error => {
    console.log("Error in reindexing communities");
    console.log(error);
    callback(null, []);
  });
  
} 

const refereshToken = (authData) =>{
    return new Promise( async (resolve,reject)=>{
        try {
            let result = await authenticateUser(authData,false);
            var param = [];
            param.push(aes256.encrypt(config.get("elasticIndex.encryptionKey"), result.authorization.accessToken));
            const sqlUpdate = "UPDATE content_source_authorization set accessToken = ? where content_source_id =" + authData.authorization.content_source_id;
            connection.query(sqlUpdate, param, (err, data)=> {

                if (err) {
                    reject(err);
                    commonFunctions.errorlogger.error("Access token is not updated for Higher Logic content_source_id "+authData.authorization.content_source_id +"err==>>"+err);
                } else {
                    resolve(result.authorization.accessToken)
                }
            });
            
        } catch (error) {
            reject(error);
        }


    });
    
}

const getDeleteEvents = async (req, res) => {
    try {
        req.params.csId = config.get('crawlerConfig.subscription.higherlogicCsId');
        req.params.tenantHash = md5(config.get('tenantId'));
        req.body    = { body: req.body, params: req.params };
            options = {
                  method: "POST",
                  rejectUnauthorized: false,
                  url   : config.get("crawler.crawlerUrl") + "/content-source/subscriptions/higherLogic",
                  headers: { 
                  "Content-Type": "application/json",
                  "su-crawler-secret" : config.get("crawler.sharedSecret"),
                  "tenant-id": config.get('tenantId') 
                  },
                  body : req.body,
                  json: true
            };
            res.sendStatus(200);
            request(options, function (error, response, body) {
                if (error) {
                    commonFunctions.errorlogger.error("error in delete thread crawler api:", error); 
                } else {
                    commonFunctions.errorlogger.info("success in delete thread crawler api:", body);
                }
            });
              
    } catch (error) {
        commonFunctions.errorlogger.error('Error inside getDeleteEvents :: ', error);
    }
}

const getInsertEvents = async (req, res) => {
    try {
        req.params.csId = config.get('crawlerConfig.subscription.higherlogicCsId');
        req.params.tenantHash = md5(config.get('tenantId'));
        req.body    = { body: req.body, params: req.params };
        options = {
            method: "POST",
            rejectUnauthorized: false,
            url   : config.get("crawler.crawlerUrl") + "/content-source/subscriptions/higherLogic",
            headers: { 
                "Content-Type": "application/json",
                "su-crawler-secret" : config.get("crawler.sharedSecret"),
                "tenant-id": config.get('tenantId') 
            },
            body : req.body,
            json: true
        };
        res.sendStatus(200);
        request(options, function (error, response, body) {
            if (error) {
                commonFunctions.errorlogger.error("error in delete thread crawler api:", error); 
            } else {
                commonFunctions.errorlogger.info("success in delete thread crawler api:", body);
            }
        });
              
    } catch (error) {
        commonFunctions.errorlogger.error('Error inside getInsertEvents :: ', error);
    }
}

module.exports = {
    authenticateUser: authenticateUser,
    refereshToken: refereshToken,
    getDeleteEvents: getDeleteEvents,
    getInsertEvents: getInsertEvents,
    reindexCommunities  : reindexCommunities
}
