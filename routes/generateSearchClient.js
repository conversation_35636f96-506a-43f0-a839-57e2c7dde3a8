var universal = require('./universal')
var fs = require('fs');
var commonFunctions = require('../utils/commonFunctions');
var async = require('async');


exports.getAutoProvisionToken = async function (req,res) {
  var provisionKey=req.body.tokenKey
  var uid = req.body.uid;

  async.auto({
    checkUid: function(cb){
      if (uid) 
        commonFunctions.getSearchClientIdUsingUid(uid,req, (err, result) => {
          if (err || !result.toString().length)
            cb('Invalid uid');
          else 
            cb(null, result);
        })
      else 
        cb(null, '');
    },
    getAutoprovisonToken: ["checkUid", function(data, cb){
      universal.getAutoprovisonToken(provisionKey,"",req,function (result) {
        commonFunctions.errorlogger.info('auto provision: ', result);
        if (result)
          cb(null, result.access_token);
        else 
          cb('No token');
      })
    }]
  }, (err, result) => {
    if (err)
      res.send({flag:404,searchToken:err})
    else
      res.send({
        flag:200,
        searchToken:result.getAutoprovisonToken,
        isLWCConsole: result.checkUid.type == 7 && result.checkUid.language == 'lwcSfConsole' ? 1 : 0})
  });
}

