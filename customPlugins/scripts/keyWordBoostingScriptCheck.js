var path = require("path");
var fs = require("fs");
var { spawn } = require('child_process');
environment = require(path.join(__dirname, '../../routes/environment'));
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../config');
process.env.NODE_CONFIG_DIR = configPath;
console.log(environment.configuration);
config = require('config');
const createRequest = require('../../auth/src/client');
const authUrl = config.get('authUrl');
var commonFunctions = require('./../../utils/commonFunctions');

const fetchAutomatedScriptData = async (req, cb) => {
    try {
        const options = {
        method: 'GET',
        url: `${authUrl}/tenant/getAutomatedScriptData`,
        body: {
            scriptName: 'keyWordBoosting'
        }
    };
        const response = await createRequest(options);
        // console.log(response);
        return response;
    } catch (error) {
        console.log(error);
        return error;
    }
}

const setAutomatedScriptData = async (req, cb) => {
    try {
        const options = {
        method: 'GET',
        url: `${authUrl}/tenant/setAutomatedScriptData`,
        body: {
            scriptName: 'keyWordBoosting',
            releaseName: 'm23',
            model: '',
            status: 1
        }
    };
        const response = await createRequest(options);
        // console.log(response);
        return response;
    } catch (error) {
        console.log(error);
        return error;
    }
}

async function keyWordBoostingSubprocess() {
    let flagData = await fetchAutomatedScriptData();
    if((flagData && flagData.data[0] && flagData.data[0].status && (flagData.data[0].status === '0' || flagData.data[0].status === 0)) || (!flagData || !flagData.data[0] || !flagData.data[0].status)) {
        let dir = path.join(__dirname, 'keyWordBoostingScriptLogs');
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir);
        }
        let outputFile = process.cwd() + '/customPlugins/scripts/keyWordBoostingScriptLogs/keyWordBoosting' + "_" + (new Date()).toISOString() + '.log'
        const out = fs.openSync(outputFile, 'a');
        const err = fs.openSync(outputFile, 'a');
        const filePath = path.resolve(path.resolve(process.cwd()) + '/customPlugins/scripts/keyWordBoostingScript.js');
        const subprocess = spawn('node', [`${filePath}`], {
            detached: true,
            stdio: ['ignore', out, err]
        });
        subprocess.unref();
        await setAutomatedScriptData();
    }
}

keyWordBoostingSubprocess();

