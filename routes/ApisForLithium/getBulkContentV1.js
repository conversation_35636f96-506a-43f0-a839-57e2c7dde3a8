/**
 * Created by harman on 19/10/16.
 */
var request = require('request');
var url = require('url');
var async = require("async");
var fs = require("fs");
var commonFunctions = require('./../../utils/commonFunctions');
var elasticsearch = require('elasticsearch');
var sessionLithium=require('./../sessionCapturing/getSessionUsingSelenium');
var clientNew = new elasticsearch.Client({
  host: config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port'),
  log: 'trace'
});
var path = require('path');
var universal=require('./../universal');

const fetchDataLithium_V1_open = function (contentSourceId,callback) {
var authObj={}
var threads=[]
  async.auto({
    get_content_source_data: function (cb) {
      commonFunctions.getContentSourceDataById(contentSourceId, function (err, contentSourceData) {
        if (!err)
          cb(null, contentSourceData)
        else
          cb(err, [])
      });
    },
    get_session_key: ['get_content_source_data', function (dataFromAbove, cb) {

      authObj.contentSourceId = dataFromAbove.get_content_source_data.contentSource.id
      authObj.communityUrl = dataFromAbove.get_content_source_data.contentSource.url;
      authObj.username = dataFromAbove.get_content_source_data.authorization.username;
      authObj.password = dataFromAbove.get_content_source_data.authorization.password;
      authObj.popup_username = dataFromAbove.get_content_source_data.authorization.htaccessUsername;
      authObj.popup_password = dataFromAbove.get_content_source_data.authorization.htaccessPassword;
      authObj.basicAuthEnabled = dataFromAbove.get_content_source_data.authorization.authorization_type.toLowerCase().includes("htaccess") ? 1 : 0;  //https enabled  //https enabled
      authObj.isOpen = dataFromAbove.get_content_source_data.authorization.authorization_type.includes("No Authentication") ? 1 : 0;
      authObj.type = dataFromAbove.get_content_source_data.authorization.authorization_type.includes("OAuth") ? "v2" : "v1";
      authObj.indexName = dataFromAbove.get_content_source_data.contentSource.elasticIndexName;
      authObj.accessToken = 'Bearer ' + dataFromAbove.get_content_source_data.authorization.accessToken;
      authObj.language = dataFromAbove.get_content_source_data.contentSource.language;
      authObj.contentSourceId = contentSourceId
      if( dataFromAbove.get_content_source_data.authorization.authorization_type.includes("Api user"))
      {
        authObj.username = dataFromAbove.get_content_source_data.authorization.publicKey;
        authObj.password = dataFromAbove.get_content_source_data.authorization.privateKey;
      }
      if (!authObj.isOpen)
        getSessionKey(authObj, function (sessionKeyResult) {
          cb(null, sessionKeyResult)
        })
      else
        cb(null, "")
    }],
    fetch_lithium_boards: ['get_session_key', function (getDataAbove, cb) {
      //dont fetch all boards only boards by db
      getboards(contentSourceId, authObj, getDataAbove.get_session_key, cb)

    }],
    crawl_data_via_boards: ['fetch_lithium_boards', function (dataFromAbove, cb) {
      crawlDataOnBoardBasis(authObj, dataFromAbove.get_session_key, dataFromAbove.fetch_lithium_boards, function (threadsReturn) {
        threads = threadsReturn;
        cb(null, threadsReturn)
      })
    }]
  }, function (err, result) {
    callback(null, result)
  })

}

function getboards(contentSourceId, authObj, session_key, callback) {
  var boards = []
  async.auto({
    // get_boards_from_db:function (cb) {
    //   commonFunctions.getActivePlacesBycontentSourceId(contentSourceId,function (err,data) {
    //     if(!err)
    //     cb(null,data)
    //     else
    //       cb(err)
    //   })
    // },
    crawl_all_open_boards: function (cb) {
      crawlallContentBoards(authObj, session_key, 100, 1, [], function (err, data) {
        if (!err) {
          boards = data
          cb(null, data)
        }
        else
          cb(err, data)
      })
    }
  }, function (err, result) {
    if (!err)
      callback(null, boards)
    else
      callback(err)
  })
}

//here data is boards array
function crawlDataOnBoardBasis(authObj, sessionKey, data, cb) {

  var threads = []
  var taskBoard = []
  for (var b = 0; b < data.length; b++) {
    taskBoard.push((function (b) {
      return function (cbInternal) {

        async.auto({
          //check if board is open group or not
          check_open_group: function (cbSI) {
            if (data[b].interaction_style['$'] == "group") {
              getGroupData(authObj, data[b].id['$'], sessionKey, function (privacy) {
                data[b].privacy = privacy
                cbSI(null, data[b])
              })
            }
            else {
              data[b].privacy = "open"
              cbSI(null, data[b])
            }
          },
          get_category: function (cbSI) {
            //get board category
            getCategory(authObj, data[b].id['$'], sessionKey, function (category) {

              data[b].categoryName = category.response.category.title['$']
              data[b].categoryId = category.response.category.id['$']

              cbSI(null, data[b])
            })
          },
          get_all_threads: ['check_open_group', 'get_category', function (dataFromabove, cbSI) {
            //get board threads
            getBoardThreads(authObj, sessionKey, data[b], [], 100, 1, function (thread) {
              cbSI(null, thread)
            })
          }]
        }, function (err, result) {
          cbInternal(null, result)
        })
      }
    })(b))
  }

  async.series(taskBoard, function (err, result) {
    cb(null, result)
  })
}

const getThreadsInfo = function (authObj, sessionKey, threads, cb) {
  var taskThreadMesaages = [];

  for (var m = 0; m < threads.length; m++) {
    taskThreadMesaages.push((function (m) {
      return function (cbOuter) {
        async.series([
          function (cbInternal1) {
            commonFunctions.errorlogger.info("thread is", threads[m].id["$"])

            getThreadData(authObj, sessionKey, threads[m], function (data) {
              threads[m] = data
              cbInternal1(null, 1)
            })
          }, function (cbInternal1) {

            getThreadTags(authObj, sessionKey, threads[m].id["$"], function (tags) {


              threads[m].tags = tags
              cbInternal1(null, 2)
            })
          }, function (cbInternal1) {

            if (threads[m].interaction_style['$'] == "group") {
              getGroupMembers(authObj, sessionKey, threads[m].boardId, function (groupMembers) {
                threads[m].groupUser = groupMembers
                cbInternal1(null, 3)
              })
            }
            else {
              threads[m].groupUser = []
              cbInternal1(null, 3)
            }
          }
          , function (cbInternal1) {
            if(config.get('checkForLithiumAttachment'))
            getAttachmentData(authObj, sessionKey, threads[m].id["$"], function (data) {
              if (data != 0) {
                threads[m]["attachment"] = 1
                threads[m]["file"] = data.join(" ").replace(/\s+/g, " ").trim()
                cbInternal1(null, 4)
              }
              else cbInternal1(null, 4)

            })
            else cbInternal1(null, 4)

          }
        ], function (err, result) {
          cbOuter(null, threads[m])
        })
      }
    })(m))
  }


  async.series(taskThreadMesaages, function (err, dataResponse) {

    //this function is used to get names of lithium content types

    var bulkDataNoImage = ""

    for (var s = 0; s < threads.length; s++) {

      saveDataFormatLithium(authObj, threads[s], threads[s].interaction_style['$'], function (resultBulkData) {
        bulkDataNoImage += resultBulkData + "\n"
      })

    }
    if (bulkDataNoImage)
      commonFunctions.BulkUpload(bulkDataNoImage, cb)
    else cb(null)
  })
}

function getGroupMembers(authObj, sessionKey, groupId, callback) {
  var options = {
    method: 'GET',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/groups/id/' + groupId + '/members',
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view',
      page_size: '100',
      page: '1'
    },
  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }

  request(options, function (error, response, body) {
    if (error) {
      commonFunctions.errorlogger.error(error)
      return;
    }

    body = JSON.parse(body)

    var userArr = body.response.users.user;
    var users = []

    for (var l = 0; l < userArr.length; l++) {
      users.push(userArr[l].id['$'])
    }
    callback(users)
  });
}

function getThreadTags(authObj, sessionKey, threadId, callback) {

  var options = {
    method: 'GET',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/messages/id/' + threadId + '/tagging/tags/all',
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view',
      page_size: '100',
      page: '1'
    },
  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }

  request(options, function (error, response, body) {
    if (error) {
      commonFunctions.errorlogger.error(error)
      return;
    }

    body = JSON.parse(body)

    var tagsArr = []
    if (body.response.tags)
      tagsArr = body.response.tags.tag;
    var tags = []

    for (var l = 0; l < tagsArr.length; l++) {
      tags.push(tagsArr[l].text['$'])
    }
    callback(tags)
  });

}


const crawlallContentBoards = function (authObj, sessionKey, page_size, page, initialarr, callback) {

  var options = {
    method: 'GET',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/boards/nested',
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view',
      page_size: page_size,
      page: page
    },

  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
  request(options, function (error, response, body) {
    if (error) callback(error, [])

    body = JSON.parse(body)
    for (var i = 0; i < body.response.boards.board.length; i++) {
      initialarr.push(body.response.boards.board[i]);
    }
    if (body.response.boards.board.length == 100) {
      page++;
      crawlallContentBoards(authObj, sessionKey, 100, page, initialarr, function (err, result) {
        callback(null, result)
      })
    }
    else {
      callback(null, initialarr)
    }
  });
}

function getBoardThreads(authObj, sessionKey, board, initialarr, page_size, page, callback) {
  var boardId = board.id['$'];
  var options = {
    method: 'GET',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/boards/id/' + boardId + '/threads',
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view',
      page_size: page_size,
      page: page
    },
  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }

  request(options, function (error, response, body) {
    if (error || JSON.parse(body).status=="error" )
      callback(0)

    else {
      body = JSON.parse(body)
      for (var i = 0; i < body.response.threads.thread.length; i++) {

        body.response.threads.thread[i].privacy = board.privacy
        body.response.threads.thread[i].categoryName = board.categoryName
        body.response.threads.thread[i].categoryId = board.categoryId
        body.response.threads.thread[i].boardName = board.title['$']
        body.response.threads.thread[i].boardId = boardId
        body.response.threads.thread[i].boardHref = board.view_href;

        initialarr.push(body.response.threads.thread[i]);
      }

      getThreadsInfo(authObj, sessionKey, initialarr, function (err, result) {

        if (body.response.threads.thread.length == 100) {
          page++;
          getBoardThreads(authObj, sessionKey, board, [], 100, page, function (result) {
            callback(result)
          })
        }
        else {
          callback(initialarr)
        }
      })
    }
  });
}


function getThreadData(authObj, sessionKey, thread, callback) {


  var id = thread.id["$"];
  var messagesArry = []
  var options = {
    method: 'GET',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/threads/id/' + id,
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view',
    },
  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }

  request(options, function (error, response, body) {
    if (error) callback(0);
    body = JSON.parse(body)
    var boards = []
    var messages = body.response.thread.messages.linear.message
    var taskThreadMessage = [];
    for (var m = 0; m < messages.length; m++) {
      taskThreadMessage.push((function (m) {
        return function (cb) {
          if (messages[m].id['$'] != id) {
            getMessageData(authObj, sessionKey, messages[m], function (message) {
              if (message != 0) {
                commonFunctions.parsehtmltoText('<body>' + message.response.message.body['$'] + '</body>', function (err, txt) {
                  messagesArry.push(txt)
                })
              }
              cb(null)
            })
          }
          else {
            commonFunctions.parsehtmltoText('<body>' + body.response.thread.messages.topic.body['$'] + '</body>', function (err, txt) {
              thread.body = txt;
              thread.html_body = "<div id='titlePreview'><strong>" + body.response.thread.messages.topic.subject['$'] + "</strong></div><hr>"
              thread.html_body = thread.html_body + "<div id='summaryPreview'>" + body.response.thread.messages.topic.body['$'] + "</div><hr>"
              thread.kudos = body.response.thread.messages.topic.kudos.count['$']
              thread.labels = []
              if (body.response.thread.messages.topic.labels) {
                for (var l = 0; l < body.response.thread.messages.topic.labels.label.length; l++) {
                  thread.labels.push(body.response.thread.messages.topic.labels.label[l].text['$'])
                }
              }
              thread.viewCount = body.response.thread.messages.topic.views.count['$']
              thread.authorHref = body.response.thread.messages.topic.author.view_href
              thread.author = body.response.thread.messages.topic.author.login['$']
              thread.post_time = body.response.thread.messages.topic.post_time['$']
              thread.subject = body.response.thread.messages.topic.subject['$']
              thread.view_href = body.response.thread.view_href
              cb(null)
            });
          }
        }
      })(m))
    }
    async.parallel(taskThreadMessage, function (err, result) {
      thread.comments = messagesArry;
      thread.html_body = thread.html_body + "<div id='commentsPreview'>";
      for (var m = 0; m < messagesArry.length; m++) {
        thread.html_body = thread.html_body.concat("<p>" + messagesArry[m] + "</p>")
      }
      thread.html_body = thread.html_body + "</div>";
      callback(thread);
    })
  });
}

getAtt = function (req, res) {
  res.send("Done")
  var id = req.query.id;
  var authObj = {};


  universal.getDataAuthLithium(function (errAuth, data) {
    if (errAuth == 0) {
      callback({ "error": "Lithium credentials are not saved" });
    } else {
      authObj.communityUrl = data[0].community_url;
      authObj.username = data[0].username;
      authObj.password = data[0].password;
      authObj.popup_username = data[0].popup_username;
      authObj.popup_password = data[0].popup_password;
      authObj.basicAuthEnabled = data[0].basicAuthEnabled;
      commonFunctions.errorlogger.info(JSON.stringify(authObj));
      var sessionKey = ""
      thisis.getSessionKey(authObj, function (sessionKeyResult) {
        sessionKey = sessionKeyResult;
        getAttachmentData(authObj, sessionKey, id, function (data) {
          commonFunctions.errorlogger.info(data)
        })
      })
    }
  })
}

getAttachmentData = function (authObj, sessionKey, messageId, callback) {
  var options = {
    method: 'GET',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/messages/id/' + messageId + '/uploads/attachments',
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view',
    }
  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
  request(options, function (error, response, body) {
    if (error) {
      commonFunctions.errorlogger.error(error)
      return;
    }

    body = JSON.parse(body)

    if (body.response.attachments.attachment.length > 0) {
      downloadAttachmentForLoop(authObj, sessionKey, body.response.attachments.attachment, function (attachmentEncodedArr) {
        callback(attachmentEncodedArr)
      })
    }
    else {
      callback(0)
    }

  });
}


function downloadAttachmentForLoop(authData, session, atthmentArr, callback) {
  var task = []
  for (var a = 0; a < atthmentArr.length; a++) {
    task.push((function (a) {
      return function (cb) {
        if (atthmentArr[a].content.format['$'] == 'application/pdf' || atthmentArr[a].content.format['$'] == 'application/pptx' || atthmentArr[a].content.format['$'] == 'application/ppt' || atthmentArr[a].content.format['$'] == 'application/xlsx' || atthmentArr[a].content.format['$'] == 'application/xls' || atthmentArr[a].content.format['$'] == 'application/doc' || atthmentArr[a].content.format['$'] == 'application/docx') {
          commonFunctions.errorlogger.info("current working directory",process.cwd())
          sessionLithium.getSessionCookies(authData.username,authData.password,config.get('lithium.sessionUrl'),path.resolve(process.cwd(), 'routes/sessionCapturing/testSession.txt'),0,function (err,result) {
            commonFunctions.errorlogger.info(result)
            authData.LithiumCookie=result?result.split('\n').find(x=>{if(x.startsWith("LiSESSIONID"))return x}):"LiSESSIONID=997DE03C948828F4076EF37101242166"
            DownloadAttachment(authData, atthmentArr[a].url['$'], function (text) { //""""""""""""""some Name
              cb(null, text)
            })
          })
        }
        else cb(null, 0)
      }
    })(a))
  }
  async.parallel(task, function (err, result) {
    //console.log("data is",result)
    callback(result)
  })
}

function DownloadAttachment(auth,uriImage,callback) {

  commonFunctions.errorlogger.info("url is ",uriImage)

  var headers = {
    'Cookie': auth.LithiumCookie,
  };

  commonFunctions.errorlogger.info("header is",headers)
  var options = {
    url: uriImage,
    headers: headers
  };
  var f=""
  request.get(options).pipe(request.put(config.get('tikaPath')))
    .on("error", err=>{
      commonFunctions.errorlogger.error("err",err)
      callback(err)
    })
    .on('data', da => {
      f += da.toString();
    })
    .on("end", () => {
      if(f.includes("Status 403") && auth.count<5)
        session.getSessionCookies(auth.username,auth.password,config.get('lithium.sessionUrl'),path.resolve(process.cwd(), 'routes/sessionCapturing/testSession.txt'),1,function (err,result) {
          commonFunctions.errorlogger.info(result)
          auth.count++;
          auth.LithiumCookie=result?result.split('\n').find(x=>{if(x.startsWith("LiSESSIONID"))return x}):"LiSESSIONID=997DE03C948828F4076EF37101242166"
          DownloadAttachment(auth,uriImage,callback)
        })
      else {
        auth.count=0
        callback(f.replace(/\s\s+/g, ' '))
      }
    })
}

function getGroupData(authObj, boardId, sessionKey, callback) {
  var options = {
    method: 'GET',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/groups/id/' + boardId,
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view',
    }
  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
  request(options, function (error, response, body) {
    if (error) {
      commonFunctions.errorlogger.error(error)
      return callback(err);
    }

    body = JSON.parse(body)
    return callback(body.response.group.privacy['$'])
  });
}


function getBoardRoles(authObj, BoardId, sessionKey, callback) {
  var options = {
    method: 'GET',
    url: config.get('lithium.baseUrl') + commonFunctions.appVariables.lithium.restPath + '/boards/id/' + BoardId + '/roles',
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view',
    }
  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
  request(options, function (error, response, body) {
    if (error) {
      commonFunctions.errorlogger.error(error)
      return;
    }

    body = JSON.parse(body)
    var roles = []
    var rolesTemp = body.response.roles.role
    for (var i = 0; i < rolesTemp.length; i++) {
      roles.push({ id: rolesTemp[i].id['$'], name: rolesTemp[i].name['$'] })
    }
    return callback(roles)
  });
}

function getCategory(authObj, boardId, sessionKey, callback) {
  commonFunctions.errorlogger.info("BoardId", boardId)

  var options = {
    method: 'GET',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/boards/id/' + boardId + '/category',
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view',
    }
  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
  request(options, function (error, response, body) {
    if (error) {
      commonFunctions.errorlogger.error(error)
      return;
    }

    body = JSON.parse(body)
    if(body.response.category.title) {
      commonFunctions.errorlogger.info("category name", body.response.category.title['$'])
      return callback(body)
    } else {
      body={"response":{"category":{"title":{"$":''},"id":{"$":''}}}}
      return callback(body)
    }
  });
}


function getMessageData(authObj, sessionKey, message, callback) {

  var id = message.id['$'];
  commonFunctions.errorlogger.info("id", id)
  var options = {
    method: 'GET',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/messages/id/' + id,
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view',
    }
  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
  request(options, function (error, response, body) {
    if (error) {
      // console.log("error tha yaahan pe")
      callback(0)
    }
    else {
      try {
        body = JSON.parse(body)
        callback(body)
      }
      catch (exception) {
        callback(0)
      }
    }

    // console.log(body);
  });


}


const saveDataFormatLithiumObject = function (auth, thread, Object, rows, callback) {


  rows.forEach(function (x) {
    if (Object == 'tkb')
      Object = 'Knowledge Base'
    if (Object == 'board')
      Object = 'Discussion'

    if (Object == x.type)
      Object = x.type_label

  })
  var bulkString = "";
  var bulkStringIndex = {}
  bulkStringIndex["index"] = {}
  bulkStringIndex["index"]["_index"] = auth.indexName;
  bulkStringIndex["index"]["_type"] = Object
  bulkStringIndex["index"]["_id"] = thread.id['$']
  bulkStringIndex = (bulkStringIndex)

  var newObj = {};
  newObj["language"] = commonFunctions.setLanguage(auth.language)
  for (var key in thread) {
    if (key == 'messages') {
      newObj["replyCount"] = thread[key].count['$'] - 1
    }
    else if (key == 'comments') {
      newObj[key] = thread[key].join(' ')
      //TODO: ADD HTML_BODY HERE
    }
    else if (key == 'board') {
      newObj['posted_in'] = thread['board']['href'].split('/')[3]
    }
    else if (key != 'id' && key != 'title' && key != 'solutions' && key != 'interaction_style')
      newObj[key] = thread[key]
  }

  var bulkStringFieldsData = (newObj)

  var finalString = { index: bulkStringIndex, field: bulkStringFieldsData }
  callback(finalString)

}

const saveDataFormatLithium = function (auth, thread, Object, callback) {

  if (Object == 'board')
    Object = 'forum'

  var bulkStringIndex = {}
  bulkStringIndex["index"] = {}
  bulkStringIndex["index"]["_index"] = auth.indexName;
  bulkStringIndex["index"]["_type"] = Object
  bulkStringIndex["index"]["_id"] = thread.id['$']
  bulkStringIndex = JSON.stringify(bulkStringIndex)

  var newObj = {};

  newObj["language"] = commonFunctions.setLanguage(auth.language)
  for (var key in thread) {
    if (key == 'messages') {
      newObj["replyCount"] = thread[key].count['$'] - 1
    }
    else if (key == 'comments') {
      newObj[key] = thread[key].join(' ')
    }
    else if (key == 'board') {
      newObj['posted_in'] = thread['board']['href'].split('/')[3]
    }

    else if (key != 'id' && key != 'title' && key != 'solutions' && key != 'interaction_style')
      newObj[key] = thread[key]
  }
  newObj['id'] = thread.id['$']

  var bulkStringFieldsData = JSON.stringify(newObj)

  var finalString = bulkStringIndex + "\n" + bulkStringFieldsData
  callback(finalString)

}

const getSessionKey = function (authObj, callback) {
  var options = {
    method: 'POST',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/authentication/sessions/login',
    qs: {
      'user.login': authObj.username,
      'user.password': authObj.password,
      'restapi.response_format': 'json',
    },
  };
  if (authObj.basicAuthEnabled) {

    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }


  request(options, function (error, response, body) {
    if (error) {
      commonFunctions.errorlogger.error(error)
      return;
    }

    body = JSON.parse(body)
    return callback((body).response.value['$']);
  });
}


const getUserDataSync = function (req, res) {
  var authObj = {};
  var fromDate = ""
  var toDate = ""
  if (req.query.fromDate)
    fromDate = new Date(req.query.fromDate).getTime();
  if (req.query.toDate)
    toDate = new Date(req.query.toDate).getTime();
  else {
    toDate = new Date(req.query.toDate ? req.query.toDate : new Date()).getTime();
  }

  var api_crawling_id = req.query.api_crawling_id

  universal.getDataAuthLithium(api_crawling_id, function (errAuth, data) {
    if (errAuth == 0) {
      callback({ "error": "Lithium credentials are not saved" });
    } else {
      authObj.communityUrl = data[0].community_url;
      authObj.username = data[0].username;
      authObj.password = data[0].password;
      authObj.popup_username = data[0].popup_username;
      authObj.popup_password = data[0].popup_password;
      authObj.basicAuthEnabled = data[0].basicAuthEnabled;
      authObj.indexName = data[0].indexName;
      commonFunctions.errorlogger.info(JSON.stringify(authObj));
      var sessionKey = ""

      getSessionKey(authObj, function (sessionKeyResult) {
        sessionKey = sessionKeyResult;


        async.series(
          [
            function (cb) {
              getAllUsers(authObj, sessionKeyResult, 10, 1, fromDate, toDate, [], function (data) {

                var taskUsers = []
                for (var t = 0; t < data.length; t++) {
                  taskUsers.push((function (t) {
                    return function (cbInternal) {

                      async.parallel([
                        function (internalCb) {
                          getUserRank(authObj, sessionKey, data[t], function (rank) {
                            internalCb(null, rank)
                          })
                        },
                        function (internalCb) {
                          getPostCounts(authObj, sessionKey, data[t], function (postCounts) {
                            internalCb(null, postCounts)
                          })
                        }
                      ], function (err, result) {

                        data[t].userRank = result[0]
                        data[t].postCount = result[1]
                        data[t].boardId = "1";  //to search user in elastic
                        cbInternal(null, "1")
                      })

                    }
                  })(t))
                }

                async.series(taskUsers, function (err, result) {

                  commonFunctions.errorlogger.info(data);
                  var bulkData = ""
                  for (var s = 0; s < data.length; s++) {
                    updateObject(data[s], function (result) {

                      thisis.saveDataFormatLithium(authObj, result, "user", function (resultBulkData) {
                        // console.log("Done--->>>---")
                        // res.send(authObj);
                        bulkData += resultBulkData + "\n"
                      })
                    })
                  }

                  commonFunctions.BulkUpload(bulkData)


                })
                cb(null, 2)

              });
            }], function (err, result) {

            })

        res.send(authObj);

      })
    }
  })
}


function updateObject(data, cb) {
  var user = {}
  user.id = {}
  user.id['$'] = data.id['$']
  user.userRank = data.userRank;
  user.view_href = data.view_href;
  user.boardId = data.boardId;
  user.registration_time = data.registration_time['$']
  user.postCount = data.postCount
  user.login = data.login['$']
  user.email = data.email['$']
  cb(user);
}

function getAllUsers(authObj, seesionKey, page_size, page, fromDate, toDate, initialarr, callback) {

  var url = authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/users'
  var qs =
    {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view',
      page_size: page_size,
      page: page,
    }
  if (fromDate) {
    url = authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/search/users'
    qs =
      {
        'restapi.response_format': 'json',
        'restapi.response_style': 'view',
        page_size: page_size,
        page: page,
        q: 'reg_date:[' + fromDate + ' TO ' + toDate + ']'
      }
  }

  if (!authObj.isOpen) {
    qs["restapi.session_key"] = sessionKey
  }
  var options = {
    method: 'GET',
    url: url,
    qs: qs
  };

  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }

  request(options, function (error, response, body) {
    if (error) callback(0)

    body = JSON.parse(body)
    for (var i = 0; i < body.response.users.user.length; i++) {
      initialarr.push(body.response.users.user[i]);
    }
    if (body.response.users.user.length == 10) {
      page++;
      getAllUsers(authObj, seesionKey, 10, page, fromDate, toDate, initialarr, function (result) {
        callback(result)
      })
    }
    else {
      callback(initialarr)
    }
  });

}


function getUserRank(authObj, sessionKey, user, callback) {
  var id = user.id['$'];
  var options = {
    method: 'GET',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/users/id/' + id + '/ranking/name',
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view'
    }
  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
  request(options, function (error, response, body) {
    if (error) callback(0)

    body = JSON.parse(body)
    callback(body.response.value["$"])
  });


}

function getPostCounts(authObj, sessionKey, user, callback) {
  var id = user.id['$'];
  var options = {
    method: 'GET',
    url: authObj.communityUrl + commonFunctions.appVariables.lithium.restPath + '/users/id/' + id + '/posts/count',
    qs: {
      'restapi.response_format': 'json',
      'restapi.response_style': 'view'
    }
  };
  if (!authObj.isOpen) {
    options.qs["restapi.session_key"] = sessionKey
  }
  if (authObj.basicAuthEnabled) {
    options.headers = {}
    options.headers = {
      authorization: 'Basic ' + new Buffer(authObj.popup_username + ":" + authObj.popup_password).toString("base64")
    }
  }
  request(options, function (error, response, body) {
    if (error) callback(0)

    body = JSON.parse(body)
    callback(body.response.value["$"])
  });
}

module.exports = {
  fetchDataLithium_V1_open: fetchDataLithium_V1_open,
  crawlallContentBoards: crawlallContentBoards,
  getSessionKey: getSessionKey
}
