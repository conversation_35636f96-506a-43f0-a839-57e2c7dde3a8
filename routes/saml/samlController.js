var http = require('http');
var express = require('express');
var fs = require('fs');
var passport = require('passport');
var saml = require('passport-saml');
var request = require("request");
var SamlStrategy = require('passport-saml').Strategy;
const util = require('util')
const path = require('path');
var xml2js = require('xml2js');
var https = require('https');
var promise = require("promise");
var parser = new xml2js.Parser();
const url = require('url');
var async = require('async');
var IdpEntityID;
const setCookie = require('set-cookie-parser');
const { getAccessTokenFromTenantId, fetchTenantInfoFromUid } = require('auth-middleware');
var jwtInit = require('../jwt');
const md5 = require('md5');

// var logoutProperties = {};
// var hostedSearchUser = require('../admin/hostedSearchUser');
var commonFunctions = require('./../../utils/commonFunctions');
const login = require('../login');

const urlAuth  = config.get('authUrl');

passport.serializeUser(function (user, done) {
    done(null, user);
});

passport.deserializeUser(function (email, done) {
    done(null, email);
});

ensure_authenticated = function ensureAuthenticated(req, res, next) {
    if (req.isAuthenticated())
        return next();
    else
        return res.redirect('/login');
}

function send_saml_request(req, res, next) {
    var fullUrl = `https://${req.hostname}`; //http://localhost/backend for local

    getSamlConfiguration(req, (err, result) => {
        req.query.uid = req.query.uid && req.query.uid !== 'undefined' ? req.query.uid : null;
        let idp = {};
        let logoutCallback = "";
        let loginCallback = "";
        relayState = req.query.relayState;
        idp = req.query && req.query.uid && req.query.uid!=='undefined' ? result.hostedConfiguration : result.adminConfiguration;
        logoutCallback = "/saml/logout";
        let tenantHashPlusScUid = req.query.uid ? md5(req.headers.session.tenantHash + req.query.uid) : req.headers.session.tenantHash;
        loginCallback = req.query.uid ? `/saml/auth-login/${tenantHashPlusScUid}` : `/saml/auth-login/${req.headers.session.tenantHash}`;
        if (err) throw err;
        if (!idp) {
            res.status(200).send({ message: "Idp not found" });
        } else {
            passport.use(tenantHashPlusScUid, new SamlStrategy({
                // URL that goes from the Identity Provider -> Service Provider
                //    callbackUrl: fullUrl + loginCallback,
                // URL that goes from the Service Provider -> Identity Provider
                entryPoint: idp.saml_sso_url,
                // Usually specified as `/shibboleth` from site root
                callbackUrl: fullUrl + `/saml/auth/${req.headers.session.tenantHash}`,
                signatureAlgorithm:'sha256',
                digestAlgorithm:'sha256',
                issuer: req.query.uid ? req.query.uid : fullUrl,
                identifierFormat: null,
                // Service Provider private key
                decryptionPvk: fs.readFileSync(__dirname + '/../../cert/key.pem', 'utf8'),
                // Service Provider Certificate
                privateCert: fs.readFileSync(__dirname + '/../../cert/key.pem', 'utf8'),
                // Identity Provider's public key
                cert: idp.certificate,
                validateInResponseTo: false,
                acceptedClockSkewMs: -1,
                disableRequestedAuthnContext: true,
                logoutUrl: idp.saml_logout_url,
                logoutCallbackUrl: fullUrl + logoutCallback,
                additionalParams: result.hostedConfiguration.sc_uid !== undefined ? { 
                    'RelayState': result.hostedConfiguration.sc_uid ? result.hostedConfiguration.sc_uid+`&hosted&redirectUri=${req.query.redirectUri}&queryParams=${JSON.stringify(req.query)}` : fullUrl,
                } : { 
                    'RelayState': relayState ? relayState : fullUrl 
                }
            }, function (profile, done) {
                return done(null, profile);
            }));
            res.redirect(fullUrl + loginCallback)
        }
    })
};



const adminLoginSaml = (req,cb) => {
    const cookie = req.headers.cookie;
    const body = {
        user: req.user
    }
    var options = {
        "method": "POST",
        "url": `${urlAuth}/saml/createSamlSession`,
        "headers": { 'content-type': 'application/json', 'Cookie': cookie,   },
        "body": JSON.stringify(body),
        "qs": req.query 

    };
    request(options, function (err, response, body) {
        req.headers['set-cookie'] = response.headers['set-cookie'];
        if (err) {
            console.log("ERROR :: " + err);
            cb(err, null);
        } else {
            cb(null,body)
        }
    })
}

async function receive_saml_reponse(req, res, next) {
    const urlFromConfig = config.get('adminURL');
    const splitUrl = urlFromConfig.split('//');
    const configDomain = splitUrl[1];
    if (!req.headers.session || 
        !req.headers.session.subdomain || 
        (!req.params['ssoKey'] && (req.hostname !== configDomain 
            || configDomain !== req.headers.session.subdomain
            || req.hostname !== req.headers.session.subdomain
    ))) {
        console.log('forbidden when ssoKey is missing');
        return res.send({
            status: 403,
            message: 'forbidden'
        });
    } else {
        const subdomainUrl = `https://${req.headers.session.subdomain}`;
        if (((req.body.RelayState && subdomainUrl !== req.body.RelayState) || (req.headers.session.subdomain !== req.hostname)) && req.body.RelayState.split('&')[1] != 'hosted') {
            console.log('session data')
            return res.send({
                status: 403,
                message: 'forbidden'
            });
        }
    }
    if (req.body.RelayState == 'testconfig') {
        res.send(req.user);
    }
    else {
        // call to auth server
        if(req.user) { 
            req.user['tenant-id'] = req.headers['tenant-id'];
        }
        
        let queryParams = req.body.RelayState && req.body.RelayState.split('&queryParams=')[1] || '';
        if (req.body.RelayState && req.body.RelayState.split('&')[1] == 'hosted') {
            const sc_uid = req.body.RelayState && req.body.RelayState.split('&')[0];
            req.query.redirect = `${urlFromConfig}/resources/search_clients_custom/${sc_uid}/download/index.html?${encodeURIComponent(queryParams)}`;
            req.body.email = req.user.nameID;
            const fetchedTenantId = await fetchTenantInfoFromUid(sc_uid);
            const result = await getAccessTokenFromTenantId(fetchedTenantId.tenantId);
            req.body.accessToken = result.accessToken;
            const finalAccessToken = await jwtInit.createSearchJwt(req, res);
            const googleTab = req.body && req.body.RelayState && req.body.RelayState.split('&') && req.body.RelayState.split('&')[2] && req.body.RelayState.split('&')[2].split('redirectUri=')[1] !== 'undefined' ? true : false;
            let userName, userEmail = '';
            try {
                // Function to convert all keys of an object to lowercase
                const convertKeysToLowerCase = (obj) => {
                    return Object.keys(obj).reduce((acc, key) => {
                        acc[key.toLowerCase()] = obj[key];
                        return acc;
                    }, {});
                };

                // Convert req.user keys to lowercase
                const userLowerCase = convertKeysToLowerCase(req.user);

                // Access properties using lowercase keys
                userName = `${userLowerCase['first name'] || ''} ${userLowerCase['last name'] || ''}`;
                userEmail = userLowerCase['primary email'] || '';
            } catch (error) {
                commonFunctions.errorlogger.error("error while receiving saml response username and email", error);
            }
            
            const userInfoToken = {
                token: finalAccessToken.message,
                name: userName,
                email: userEmail,
                googleTab: googleTab
            }
            res.cookie(`hscToken_${sc_uid}`, JSON.stringify(userInfoToken), { expire: new Date().getTime() + 180 * 60000 });
            res.setHeader('content-type', 'text/html; charset=UTF-8');
            return res.render(`${DIRNAME}/resources/loginAuthorize/oktaLogin.html`, {
                googleTab: googleTab,
                redirect: req.query.redirect,
                logOutUrl: urlFromConfig + '/saml/logout',
                csrf: '',
                addons: '',
                user: req.user
            });
        } else {
            req.user.appId = 1;
            adminLoginSaml(req, (error, data) => {
                const body = JSON.parse(data);
                if (body.flag == 105 || body.flag == 403) {
                    return res.send(body.message);
                }
                const splitCookie = setCookie.splitCookiesString(req.headers['set-cookie']);
                const cookies = setCookie.parse(splitCookie);
                cookies.forEach((item) => {
                  res.cookie(item.name, item.value, { maxAge: 1800000 , httpOnly: true });
                });
                res.setHeader('content-type', 'text/html; charset=UTF-8');
                res.render(`${DIRNAME}/resources/loginAuthorize/oktaLogin.html`, {
                  csrf: body.data.csrf,
                  addons: body.data.addons,
                  redirect: body.data.redirect,
                  user: body.data.user,
                  logOutUrl: urlFromConfig + '/saml/logout',
                  googleTab: false,
                });
            })
        }
    }
};

function adminLogin(req, res) {
    checkUserOrLogin(req.user.nameID,req, (err, data) => {
        if (data.status === 1) {
            redirectAdminLogin(data, req, res);
        }
        else {
            registerEmail(req.user.nameID, req.user.firstname,req, (err, data) => {
                checkUserOrLogin(req.user.nameID, (err, data) => {
                    redirectAdminLogin(data, req, res);
                })
            })
        }
    });
}

function send_logout_request(req, res) {
    if ((req.path.indexOf("logout") > -1 && req.headers.session.is_federated)) {
        var samlStrategy = passport._strategy(req.headers.session.tenantHash);
        if(!samlStrategy) {
            res.redirect(`${config.get('adminURL')}/saml/auth`);
        }
        let user = req.user;
        if(!req.user && req.body.user) {
            user = JSON.parse(req.body.user);
        }
        if (user && user.nameID) {
            req.user = { nameID: "", sessionIndex: "", nameIDFormat: "" }
            req.user.nameID = user.nameID;
            req.user.sessionIndex = user.sessionIndex;
            req.user.nameIDFormat = user.nameIDFormat; 
            samlStrategy.logout(req, function (err, requestUrl) {
                req.logOut();
                login.adminLogout(req, () => {
                    res.send({ "location": requestUrl })
                })
            })
        } else {
            login.adminLogout(req, (err) => {
                if (!err) {
                    res.clearCookie("CSRF-Token");
                    res.clearCookie("_csrf");
                    res.clearCookie("connect.admin_sid");
                    res.send({ "location": '' });
                }
                console.log("Error while logging out", err);
            });
        }
    } else {
        login.adminLogout(req, (err) => {
            if (!err) {
                res.clearCookie("CSRF-Token");
                res.clearCookie("_csrf");
                res.clearCookie("connect.admin_sid");
                res.send({ "location": '' });
            }
            console.log("Error while logging out", err);
        });
    }
};

function show_sp_metadata(req, res) {
    res.type('application/xml');
    var fullUrl = req.protocol + '://' + req.get('host');
    var samlStrategy = new SamlStrategy({
        callbackUrl: fullUrl,
        issuer: fullUrl,
        identifierFormat: null,
        decryptionPvk: fs.readFileSync(__dirname + '/../../cert/key.pem', 'utf8'),
        // Service Provider Certificate
        privateCert: fs.readFileSync(__dirname + '/../../cert/key.pem', 'utf8'),
        validateInResponseTo: false,
        disableRequestedAuthnContext: true,
        logoutCallbackUrl: fullUrl + "/saml/logout",
        signatureAlgorithm:'sha256',
        digestAlgorithm:'sha256'
    }, function (profile, done) {
        return done(null, profile);
    });
    res.status(200).send(samlStrategy.generateServiceProviderMetadata(fs.readFileSync(__dirname + '/../../cert/cert.pem', 'utf8')));
};

function fetch_metadata(req, response) {
    var promise = fetchMetadata();
    response.send("Done");
};

function fetchMetadata() {
    var data = '';
    return new promise(function (resolve, reject) {
        https.get('https://haka.funet.fi/metadata/haka_test_metadata_signed.xml', function (res) {
            if (res.statusCode >= 200 && res.statusCode < 400) {
                res.on('data', function (data_) { data += data_.toString(); });
                res.on('end', function () {
                    parser.parseString(data, function (err, result) {
                        var parent = result.EntitiesDescriptor.EntityDescriptor;
                        var idpCount = parent.length;
                        var idps = [];
                        //Populate IDPs
                        for (var i in parent) {
                            var idpItem = parent[i];
                            if (idpItem.IDPSSODescriptor) {
                                var entityID = idpItem.$.entityID;
                                var samlUrlArray = idpItem.IDPSSODescriptor[0].SingleSignOnService;
                                var samlUrl = samlUrlArray[0].$.Location;
                                for (var j in samlUrlArray) {
                                    if (samlUrlArray[j].$.Binding.toUpperCase() == 'HTTP-REDIRECT') {
                                        samlUrl = samlUrlArray[j].$.Location;
                                    }
                                }

                                var samlLogoutUrl;
                                var samlLogoutArray = idpItem.IDPSSODescriptor[0].SingleLogoutService;
                                if (samlLogoutArray) {
                                    samlLogoutUrl = samlLogoutArray[0].$.Location;
                                    for (var j in samlLogoutArray) {
                                        if (samlLogoutArray[j].$.Binding.toUpperCase() == 'HTTP-REDIRECT') {
                                            samlLogoutUrl = samlLogoutArray[j].$.Location;
                                        }
                                    }
                                }

                                var cert = idpItem.IDPSSODescriptor[0].KeyDescriptor[0]["ds:KeyInfo"][0]["ds:X509Data"][0]["ds:X509Certificate"][0];

                                idps.push({
                                    "entityID": entityID,
                                    "samlUrl": samlUrl,
                                    "samlLogoutUrl": samlLogoutUrl,
                                    "cert": cert,
                                });
                            }
                        }

                        resolve(idps);
                    });
                });
            }
        });
    });
}

function checkUserOrLogin(email,req, callback) {
    async.auto({
        adminLogin: cb => {
            var sql = "select u.id id,r.id roleId,u.user_email email,u.name name,r.role role, u.is_federated is_federated, u.is_active is_active, u.ques_ans ques_ans, u.selectedTabs selectedTabs, access_token access_token from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where u.user_email=? and u.is_federated=?"
            connection[req.headers['tenant-id']].execute.query(sql, [email, 1], function (err, rows) {
                commonFunctions.errorlogger.error("check err", err);
                var data = {
                    name: "",
                    email: "",
                    status: 0
                };
                if (rows.length > 0) {
                    data = {
                        name: rows[0].name,
                        email: rows[0].email,
                        roleName: rows[0].role,
                        roleId: rows[0].roleId,
                        status: 1,
                        id: rows[0].id,
                        is_federated: rows[0].is_federated,
                        selectedTabs: rows[0].selectedTabs,
                        is_active: rows[0].is_active
                    }
                }
                cb(null, data);
            })
        },
        addonsStatus: cb => {
            commonFunctions.getAddonsStatus((error, result) => {
                if (result && result.length) {
                    cb(null, result)
                } else {
                    cb(null, [])
                }
            });
        }
    }, (error, result) => {
        resultToSend = [];
        resultToSend.push(result.adminLogin);
        resultToSend[0].addons = [];
        resultToSend[0].addons = result ? result.addonsStatus : [];
        callback(null, resultToSend[0])
    })
}

function updateSamlConfiguration(data,req, cb) {
    let idp = data;

    let sql = `INSERT INTO saml_auth (id,idp_display_name,idp_identifier,idp_entity_id,saml_sso_url,certificate, saml_logout_url, idp_type, uid, isActivated, tenant_id,sso_type,certificate_uploaded)
  VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE id = VALUES(id),idp_display_name = VALUES(idp_display_name),idp_identifier = VALUES(idp_identifier),idp_entity_id=VALUES(idp_entity_id),
  saml_sso_url=VALUES(saml_sso_url),certificate=VALUES(certificate),saml_logout_url=VALUES(saml_logout_url),idp_type=VALUES(idp_type),uid=VALUES(uid),isActivated=VALUES(isActivated),tenant_id=VALUES(tenant_id),sso_type=VALUES(sso_type),certificate_uploaded=VALUES(certificate_uploaded)`
    let q = connection[req.headers['tenant-id']].execute.query(sql, [idp.id || null, idp.idp_display_name, idp.idp_identifier || 'okta', idp.idp_entity_id, idp.saml_sso_url, idp.certificate, idp.saml_logout_url, idp.idp_type, idp.uid, idp.isActivated, req.headers['tenant-id'], idp.sso_type, idp.certificate_uploaded], (error, rows) => {
        cb(null, []);
    })
}

function updateSSOCertificate(data,req, cb) {
    let idp = data;

    let sql = `UPDATE saml_auth SET certificate=?,certificate_uploaded=?,isActivated=? WHERE id=?`;
    let q = connection[req.headers['tenant-id']].execute.query(sql, [idp.certificate, idp.certificate_uploaded, 0, idp.id], (error, rows) => {
        cb(null, []);
    })
}

async function fetchAccessTokenFromTenantId (tenantId) {
    const data = await getAccessTokenFromTenantId(tenantId);
    if (data && data.accessToken){
        return data.accessToken;
    } else {
        return '';
    }
}

function getSamlConfiguration(req, callback) {
    async.auto({
        getSamlAuth: cb => {
            req.query.uid = (req.query && req.query.uid && req.query.uid !== 'undefined' ? req.query.uid : null);
            let sql;
            if(req.query.uid) {
                sql = `SELECT sso_config FROM search_client_sso_config WHERE sc_uid='${req.query.uid}'`;
            } else {
                sql = `SELECT *  FROM saml_auth`;
            }
            connection[req.headers['tenant-id']].execute.query(sql, (error, rows) => {
                let resultToSend = {};
                if (rows && rows.length) {
                    resultToSend = { 
                        "adminConfiguration": rows.filter(x => x.idp_type == 0)[0] || {}, 
                        "hostedConfiguration": typeof (rows[0] && rows[0].sso_config) == 'string' ? JSON.parse(rows[0].sso_config) : {} || {} 
                    };
                    cb(null, resultToSend);
                } else {
                    resultToSend = { "adminConfiguration": {}, "hostedConfiguration": {} };
                    cb(null, resultToSend);
                }
            });
        },
        getUid: cb => {
            let sql = `SELECT uid FROM search_clients WHERE uid='${req.query.uid}' LIMIT 1`;
            connection[req.headers['tenant-id']].execute.query(sql, (error, rows) => {
                if (rows && rows.length) {
                    cb(null, rows[0].uid);

                }else cb(null, '');
            });        
        }
    }, (error, result) => {
        let resultToSend = result.getSamlAuth;
        if(result.getUid)
            resultToSend["hostedConfiguration"].sc_uid = result.getUid;
        callback(null, resultToSend);
    })
}

function setSamlUid(callback) {
    let uid = '';
    async.auto({
        getUid: cb => {
            let sql = 'SELECT uid FROM search_clients WHERE search_client_type_id = 20';
            connection.query(sql, (error, rows) => {
                if (!error) {
                    uid = rows[0].uid;
                    cb(null, '');
                }
                else {
                    cb(error);
                }
            });
        },
        insertUid: ['getUid', (result, cb) => {
            let sql = 'UPDATE saml_auth SET uid = ? WHERE idp_type=1';
            connection.query(sql, uid, (error, rows) => {
                if (!error) {
                    getSamlConfiguration(callback);
                    cb(null, '');
                }
                else {
                    cb(error);
                }
            });
        }]
    }, (error, result) => {
        if (error) commonFunctions.errorlogger.error(error);
    });
}

function registerEmail(email, name,req, callback) {
    var sql = "Insert into user (user_email, name, is_federated, is_active) values (?,?,?,?)";
    connection[req.headers['tenant-id']].execute.query(sql, [email, name || email.split("@")[0], 1, 1], function (err, rows) {
        if (!err) {
            var sql = "Insert into user_roles (userId,roleId) values (?,?)";
            connection[req.headers['tenant-id']].execute.query(sql, [rows.insertId, 1], function (err, rows) {
                if (!err) {
                    return callback(1);
                }
            })
        }
    })
}

function redirectAdminLogin(data, req, res) {
    res.csrfToken = req.csrfToken();
    req.session.name = data.name;
    req.session.email = data.email;
    req.session.roleId = data.roleId;
    req.session.user_id = data.id;
    req.session.userId = data.id;
    req.session.is_federated = data.is_federated;
    req.session.selectedTabs = data.selectedTabs;
    req.session.is_active = data.is_active;
    let redirect = req.query.redirect || config.get('adminURL');
    res.setHeader("content-type", "text/html; charset=UTF-8");
    res.render(DIRNAME + '/resources/loginAuthorize/oktaLogin.html', {
        "csrf": req.csrfToken(),
        "addons": data.addons,
        "redirect": redirect,
        "user": req.user,
        googleTab: false,
    });
}
module.exports = {
    updateSamlConfiguration: updateSamlConfiguration,
    getSamlConfiguration: getSamlConfiguration,
    ensure_authenticated: ensure_authenticated,
    send_saml_request: send_saml_request,
    receive_saml_reponse: receive_saml_reponse,
    send_logout_request: send_logout_request,
    show_sp_metadata: show_sp_metadata,
    fetch_metadata: fetch_metadata,
    updateSSOCertificate: updateSSOCertificate
}
