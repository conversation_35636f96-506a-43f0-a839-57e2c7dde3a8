<svg id="SVG-Circus-7dff043e-2191-8d17-b037-712e22a62e2c" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet"><circle id="actor_3" cx="64" cy="50" r="5" opacity="1" fill="#6E99FA" fill-opacity="1" stroke="rgba(166,3,17,1)" stroke-width="0" stroke-opacity="1" stroke-dasharray=""></circle><circle id="actor_2" cx="36" cy="50" r="5" opacity="1" fill="#6E99FA" fill-opacity="1" stroke="rgba(166,3,17,1)" stroke-width="0" stroke-opacity="1" stroke-dasharray=""></circle><circle id="actor_1" cx="50" cy="50" r="5" opacity="1" fill="#6E99FA" fill-opacity="1" stroke="rgba(166,3,17,1)" stroke-width="0" stroke-opacity="1" stroke-dasharray=""></circle><script type="text/ecmascript"><![CDATA[(function(){var actors={};actors.actor_1={node:document.getElementById("SVG-Circus-7dff043e-2191-8d17-b037-712e22a62e2c").getElementById("actor_1"),type:"circle",cx:50,cy:50,dx:10,dy:6,opacity:1};actors.actor_2={node:document.getElementById("SVG-Circus-7dff043e-2191-8d17-b037-712e22a62e2c").getElementById("actor_2"),type:"circle",cx:36,cy:50,dx:10,dy:6,opacity:1};actors.actor_3={node:document.getElementById("SVG-Circus-7dff043e-2191-8d17-b037-712e22a62e2c").getElementById("actor_3"),type:"circle",cx:64,cy:50,dx:10,dy:6,opacity:1};var tricks={};tricks.trick_1=(function(_,t){t=(function(t){return.5*(1-Math.cos(Math.PI*t))})(t)%1,t=0>t?1+t:t;var i;i=0.5>=t?1+(0.8-1)/0.5*t:t>=0.5?0.8-(t-0.5)*((0.8-1)/(1-0.5)):0.8;var a=_._tMatrix,r=-_.cx*i+_.cx,x=-_.cy*i+_.cy,c=a[0]*i,n=a[1]*i,M=a[2]*i,f=a[3]*i,g=a[0]*r+a[2]*x+a[4],m=a[1]*r+a[3]*x+a[5];_._tMatrix[0]=c,_._tMatrix[1]=n,_._tMatrix[2]=M,_._tMatrix[3]=f,_._tMatrix[4]=g,_._tMatrix[5]=m});var scenarios={};scenarios.scenario_1={actors: ["actor_1","actor_2","actor_3"],tricks: [{trick: "trick_1",start:0,end:1}],startAfter:0,duration:700,actorDelay:230,repeat:0,repeatDelay:0};var _reqAnimFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.oRequestAnimationFrame,fnTick=function(t){var r,a,i,e,n,o,s,c,m,f,d,k,w;for(c in actors)actors[c]._tMatrix=[1,0,0,1,0,0];for(s in scenarios)for(o=scenarios[s],m=t-o.startAfter,r=0,a=o.actors.length;a>r;r++){if(i=actors[o.actors[r]],i&&i.node&&i._tMatrix)for(f=0,m>=0&&(d=o.duration+o.repeatDelay,o.repeat>0&&m>d*o.repeat&&(f=1),f+=m%d/o.duration),e=0,n=o.tricks.length;n>e;e++)k=o.tricks[e],w=(f-k.start)*(1/(k.end-k.start)),tricks[k.trick]&&tricks[k.trick](i,Math.max(0,Math.min(1,w)));m-=o.actorDelay}for(c in actors)i=actors[c],i&&i.node&&i._tMatrix&&i.node.setAttribute("transform","matrix("+i._tMatrix.join()+")");_reqAnimFrame(fnTick)};_reqAnimFrame(fnTick);})()]]></script></svg>
