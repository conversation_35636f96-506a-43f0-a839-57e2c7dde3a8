var express = require("express");
var router = express.Router();
var async = require("async");
var request = require("request");
var commonFunctions = require("./../../utils/commonFunctions");
config = require('config');
const { getUserInfo } = require("auth-middleware");

router.post("/llm-status", async(req,res,next)=>{

    const authUrl = config.get('authUrl');
    let instanceEnvironmentType = config.get('instanceType');
    instanceEnvironmentType = instanceEnvironmentType.toLowerCase();
    const method = 'POST';
    const url = `${authUrl}/utility/send-mail`;
    const headers = {
      "tenant-id": req.headers['tenant-id'],
      "auth-secret": "s"
    };

    let body = {
        selectedFields: {
            user: ["user_email"],
        },
        filter: {
            tenants: {
                tenant_id: req.headers['tenant-id'],
            },
            roles: {
                hierarchy: [1, 4],
            },
        },
    };
    const response = await getUserInfo(body);
    const email = response.data[0].user_email;

    const emailConfig = {
      data: {
        recipients: {
          to: {
            type: "filter",
            filters: {
              roles: {
                hierarchy: [1, 4]
              },
              user_roles: {
                app_id: [1]
              }
            }
          }
          ,
          cc: {
            type: "email",
            emails: ['development','qa'].includes(instanceEnvironmentType)? ["<EMAIL>"] : ["<EMAIL>"],
            filters: {
              roles: {
                hierarchy: [1]
              }
            }
          }
        },
        separateMails: "true",
        message: {
          subject: "Request for AWS-Claude integration received",
          text: "Request for AWS-Claude integration received",
          html:`<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
              <html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
              <head>
                  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
                  <title>SearchUnify LLM emailer</title>
                  <link type="text/css" rel="preconnect" href="https://fonts.googleapis.com" />
                  <link type="text/css" rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
                  <link type="text/css" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet" />
                  <style type="text/css">
                      *{
                          margin: 0;
                          padding: 0;
                      }
                      table{
                          border-collapse: collapse;
                      }
                  </style>
              </head>
              
              <body>
                  <!--Outer table start -->
                  <table width="840px" style="margin: auto;border-collapse: collapse; border-spacing: 0;" border="0" cellpadding="0" cellspacing="0">
                      <tbody>
                      <tr>
                          <td height="163px" bgcolor="#62ACFB" style="background-color: #62ACFB;"></td>
                          <td height="163px" bgcolor="#62ACFB" style="background-color: #62ACFB;">
                              <a href="https://searchunify.com" target="_blank">
                                <img id="stripe" src="https://daomcxh7n9l3r.cloudfront.net/27423c55c3b2beff62066dca8d5b71ed/Asset-Library/searchunify.png" border="0"/>
                              </a>
                          </td>
                          <td height="155px" bgcolor="#62ACFB" style="background-color: #62ACFB;"></td>
                      </tr>
                     
                      <tr>
                          <td width="114px" bgcolor="#F6F7FB" valign="top">
                              <img width="114px" height="164px" src="https://daomcxh7n9l3r.cloudfront.net/27423c55c3b2beff62066dca8d5b71ed/Asset-Library/side-stripe.png" alt="left-stripe"/>
                          </td>
              
                          <!--main table body start -->
                          <td width="612px" bgcolor="#ffffff"> 
                              <table width="612px" border="0" cellpadding="0" cellspacing="0" style="box-shadow: 0px 0px 24px 0px #1818181A;clip-path: inset(-24px -24px -24px -24px);">
                                  <tr>
                                      <td height="25px"></td>
                                  </tr>
                                  <tr>
                                      <td align="left" style="padding-left:25px;  font-weight: 500;font-size: 16px;line-height:19px;font-family:'Montserrat',sans-serif;">
                                          Hi <a href="#" style="color: #1155CC">__USEREMAIL__</a>
                                      </td>
                                  </tr>
                                  <tr><td height="30px" colspan="3"></td></tr>
                                  
                                  <tr>
                                      <td colspan="4" align="left" style="padding-left:25px;  font-weight:600;font-size: 20px;line-height:24px;font-family: 'Montserrat',sans-serif;color: #2A2A2A">
                                          Request for Claude integration
                                      </td>
                                  </tr>
              
                                  <tr>
                                      <td height="15px"></td>
                                  </tr>    
                                  
                                  <tr>
                                      <td colspan="4" align="left" style="padding:0px 25px 0px 25px; font-weight: 400;font-size: 14px;line-height:22px;font-family: 'Montserrat',sans-serif;letter-spacing:0px;color: #2A2A2A">
                                          We have received your request and will reach out to you shortly.
                                      </td>
                                  </tr> 
          
                                  <tr>
                                      <td height="30px"></td>
                                  </tr>
                  
                                  <tr>
                                      <td height="22px" colspan="3" align="center" bgcolor="#F6F7FB"></td>
                                  </tr>
              
                                  <tr>
                                      <td colspan="3" align="center" bgcolor="#F6F7FB">
                                          <p style="  font-weight: 500;font-size: 15px;line-height:30px;font-family: 'Montserrat',sans-serif;color: #222222">Feel free to reach out to SearchUnify's Support for any further assistance.</p>
                                      </td>
                                  </tr>
                                  <tr>
                                      <td colspan="3" align="center" bgcolor="#F6F7FB">
                                          <a href="https://community.searchunify.com/support" target="_blank" style=" font-weight: 400;font-size: 12px;line-height:30px;font-family: 'Montserrat',sans-serif;color: #6DA7FB;text-decoration: none;">https://community.searchunify.com/hc/en-us/p/support/</a>
                                      </td>
                                  </tr>   
                                  <tr>
                                      <td height="20px" colspan="3" align="center" bgcolor="#F6F7FB"></td>
                                  </tr>
              
                                  <tr>
                                      <td height="20px" colspan="3" align="center"></td>
                                  </tr>
                                  <tr>
                                      <td colspan="3" align="center">
                                          <p style=" font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #222222">© ${new Date().getFullYear()}  SearchUnify. All rights reserved</p>
                                      </td>
                                  </tr>
                                  <tr>
                                      <td height="8px" colspan="3" align="center"></td>
                                  </tr>
                                  <tr>
                                      <td colspan="3" align="center">
                                          <p style=" font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6">Email: <a href="mailto:<EMAIL>" style=" font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6;text-decoration: none;"><EMAIL></a>,<a href="mailto:<EMAIL>" style=" font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color: #A6A6A6;text-decoration: none;"><EMAIL></a></p>
                                      </td>
                                  </tr>
                                  <tr>
                                      <td height="20px" colspan="3"></td>
                                  </tr>
                              </table> 
                              <table width="612px" border="0" cellpadding="0" cellspacing="0">
                                  <tr>
                                      <td height="38px" colspan="3" bgcolor="#F6F7FB">
                                          <table width="612px" border="0" cellpadding="0" cellspacing="0">
                                              <tr>
                                                  <td height="20px" colspan="3"></td>
                                              </tr>
                                              <tr>
                                                  <td>
                                                      <a href="https://www.searchunify.com/contact-us/" title="Contact" style=" font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;">Contact | </a>
                                                      <a href="https://www.searchunify.com/privacy-policy/" title="Privacy Policy" style=" font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;">Privacy Policy | </a>
                                                      <a href="https://www.searchunify.com/terms-and-conditions/" title="Terms &amp; Conditions" style=" font-weight: 400;font-size: 12px;line-height:15px;font-family: 'Montserrat',sans-serif; color:#2B2B2B;text-decoration: none;"> Terms &amp; Conditions</a>
                                                  </td>
                                                  <td align="right">
                                                      <a href="https://www.facebook.com/SearchUnify/" title="Facebook" style="text-decoration: none;"><img src="https://daomcxh7n9l3r.cloudfront.net/27423c55c3b2beff62066dca8d5b71ed/Asset-Library/fb.png" alt="Facebook" /> </a>   
                                                      <a href="https://www.linkedin.com/showcase/searchunify/" title="linkedIn" style="text-decoration: none;"><img src="https://daomcxh7n9l3r.cloudfront.net/27423c55c3b2beff62066dca8d5b71ed/Asset-Library/linkedin.png" alt="linkedIn" /> </a>
                                                      <a href="https://twitter.com/SearchUnify" title="Twitter" style="text-decoration: none;"><img src="https://daomcxh7n9l3r.cloudfront.net/27423c55c3b2beff62066dca8d5b71ed/Asset-Library/twitter.png" alt="Twitter" /> </a>
                                                  </td>
                                              </tr>
                                               <tr>
                                                  <td height="20px" colspan="3"></td>
                                              </tr>
                                          </table>
                                      </td>
                                  </tr> 
                              </table> 
                          </td>
                          
                          <!--main table body end -->
                          <td width="114px" bgcolor="#F6F7FB" valign="top">
                              <img width="114px" height="164px" src="https://daomcxh7n9l3r.cloudfront.net/27423c55c3b2beff62066dca8d5b71ed/Asset-Library/side-stripe.png" alt="right-stripe" />
                          </td>
                      </tr>
                  </tbody>
                  </table>
                  <!--outer table end -->
              </body>
              </html>`
        }
      }
    };


  const emailConfigData = JSON.parse(JSON.stringify(emailConfig.data));

  commonFunctions.httpRequest(
    method,
    url,
    "",
    emailConfigData,
    headers,
    function (error, body) {
        if (error) {
            res.send({ status: "400", data: error || body });
        } else {
            res.send("mail sent")
        }   
    }
  );
})

module.exports.router= router;