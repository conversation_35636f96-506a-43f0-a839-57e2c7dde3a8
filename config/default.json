{"instanceType": "development", "PORT": 6009, "searchPORT": 7000, "base": "/", "addUserLimit": 10, "instanceName": "dev 2", "agentHelperURL": "http://agenthelper:8999", "aggregationLogic": false, "allowMFA": false, "adminURL": "https://dummyurl.searchunify.com", "checkSearchClientStatus": true, "databaseSettings": {"host": "mysql", "user": "root", "password": "dummymysqlpassword", "database": "<PERSON><PERSON><PERSON>", "mysqlPORT": "3306"}, "multiTenantAuthSecret": "XoNlzwIYhyxKHSNhnmZceaS99nUATqCp", "jwt": {"keys": {"private": "Lib/jwt-keys/development", "public": "Lib/jwt-keys/development.pub"}, "encryptionKey": "GrazSearch#271#unifyGrazti###SEA", "tenantId": "33850a62-19ac-477d-9cd7-837f3d716885", "domain": "http://test.searchunify.com"}, "github": {"communityUrl": "https://api.github.com"}, "index_enable": true, "isolateSynonymStopwords": false, "synonyms": {"HOST": "synonym", "PORT": "3500"}, "JWT_SECRET_KEY": "hello1", "statusPageJWTSecret": "statusPageJWTSecret@$123", "synonymUrl": "http://synonym:3500", "tenantId": "11850a62-19ac-477d-9cd7-837f3d716885", "tikaPath": "http://localhost:9998/tika", "obfuscation": false, "onPremises": false, "onPremisesUrl": "http://localhost:3001", "poc_name": "dummy<PERSON>l", "analyticsOld": [], "analyticsNew": [], "profiles": [], "authContentSource": [], "authentication": [], "trackingApi": [], "whiteListOnPremDomain": [], "tuning": [], "ticketsApi": [], "emailNotifications": [], "blockUserMinutes": 15, "restrictUserAccount": ["grazitti.com", "searchunify.com", "apiUser.com"], "salesEmail": ["<EMAIL>"], "solr": {"connection": "local"}, "searchAnalytics": {"supportUrl": ".*/[nN]ew[cC]ase", "detailUrl": ".*/s/publiccasedetail.*", "SUfieldonCase": "SU_Ltng__SearchUnifySessionId__c"}, "siteMapReplaceUrlContent": ["/help-center/home/<USER>/public_html/help-center", "https://help-center.vlocity.com"], "gmail": {"userName": "<EMAIL>", "password": "rzgvgyxursbycfjc"}, "searchApiEnds": [], "searchEndpoints": [], "statusPage": [], "chatBotUrl": "http://chatbot:3100/api", "checkDraft": false, "communityId": "0000bdettebhsu", "contentSourcesAndSearchClients": [], "debuglevel": 1, "maxSummaryLimit": "500", "linkExpiration": 24, "mergeArticle": false, "myAccountInstanceVariable": "production", "networkId": "0DB0U00000000EmWAI", "redis_enable": true, "redisCachingTime": 900, "redisCommandWaitTimeInMS": 100, "quarterlyReleaseMonths": {"02": "Q1", "05": "Q2", "08": "Q3", "11": "Q4"}, "release": "", "secret_code": "secret_passcode", "setSearchRedis": 1, "sso_enable": false, "discussionUrl": "*/feed/*", "jive": {"communityUrl": "https://marketo-ext.jiveon.com"}, "elasticIndex": {"port": 8035, "transportLayerPort": 9300, "host": "elastic-2.3.4", "jive": "http://localhost:8036/jive", "analytics": "analytics_colubridae20", "searchgraph": "search_graph", "sessionIndex": "analytics_colubridae20_sessions", "synonymIndex": "synonyms_colubridae20", "sessionProfile": "session_profile", "folder": "/opt/elasticsearch-2.3.4/", "encryptionKey": "Grazitti$271##", "md5salt": "@##@Lkvk@8a$*5c9#JOKxZwf64d@@", "oauthLogsIndex": "analytics_oauth_logs", "dailyAnalytics": "daily_analytics", "accronymIndex": "accronyms"}, "elasticIndexCS": {"port": 8045, "transportLayerPort": 9301, "host": "elastic-5.6.16", "folder": "/opt/elasticsearch-5.6.16/", "encryptionKey": "Grazitti$271##", "md5salt": "@##@Lkvk@8a$*5c9#JOKxZwf64d@@", "oauthLogsIndex": "analytics_oauth_logs", "accronymIndex": "accronyms", "synonymIndex": "synonyms", "clustedEnabled": false, "clusterUrls": ["http://elastic_config:3001"]}, "kafkaTopic": {"host": "kafka:9092", "suContentSource": "contentSource", "facetInterpreterTopic": "facetInterpreter", "contentSourceObjects": "contentSourceObjects", "contentSourceObjectFields": "contentSourceObjectFields", "contentSourceSpaces": "contentSourceSpaces", "tenantGlobals": "tenantGlobals", "crawlerConfig": "crawlerConfig", "emailSubscriptionPreferences": "emailSubscriptionPreferences", "contentSource": "contentSourceTopic", "searchResult": "searchResultConfigTopic", "boostingConfigTopic": "boostingConfigTopic", "authService": "authServiceTopic", "communityHelper": "communityBotAnalytics", "communityBotAnalytics": "communityBotFeedIDs", "customDictionary": "customDict", "synonymsDictionary": "synonymsDict", "dictionaryWords": "dictionaryWords", "boostingTopic": "autoBoosting", "similarSearchTopic": "similarSearches", "spellCorrectorTopic": "spell<PERSON><PERSON><PERSON><PERSON>", "csCrawlUpdates": "csCrawlUpdates", "userTopic": "userTopic", "knowledgeGraphTopic": "knowledgeGraphTopic", "searchClientTopic": "searchClient", "ecoSystemTopic": "ecoSystem", "synonymDictionaryTopicMultiPhrase": "synonymsDictMultiPhrase", "synonymDictionaryTopicSettings": "synonymDictionarySettings", "articleFeedbackToAdmins": "articleFeedbackToAdmins", "didYouMeanConfigTopic": "didYouMeanConfigTopic", "tenantUidTopic": "tenantUidAuthPublish", "publishIntentData": "publishIntentData", "publishTenantInfo": "publishTenantInfo", "agentHelperTrainingTopic": "agentHelperTraining", "cronManage": "cronManage", "enable": true, "maxRetryTime": 60000, "initialRetryTime": 1000, "retries": 500, "keywordBoostingTopic": "keywordBoostingTopic", "versionUpdateMapping": "CsVersionUpdateMapping", "processRequest": "processRequest", "publishCSTokensTopic": "publishCSTokensTopic", "publishAllCSInfo": "publishAllCSInfo", "spellCorrectorEvent": "spellCorrectorEvent", "adminAction": "adminAction", "agentHelperClient": "agentHelperClient", "slackConfigAh": "slackConfigAh", "dymTrainingStatusTopic": "dymTrainingStatusChange", "mergeField": "mergeField", "domainConfigurations": "domainConfigurations", "featureListTopic": "featureListTopic", "featureEngagementMigrationTopic": "featureEngagementMigration", "manageRateLimts": "manageRateLimts", "abTestTopic": "abTestTopic", "manageIndexes": "manageIndexes", "publishAnalytics": "publishAnalytics"}, "redis": {"redisHost": "redis", "redisPort": "6379"}, "epaddon": {"url": "http://ep-addon:8889", "secretKey": "KaeFE@h!ab34n290gsjhsuepkey"}, "crawler": {"crawlerUrl": "http://crawler:6500", "sharedSecret": "aaiquyhjn104!#nj1!nn"}, "suCrawler": {"suCrawlerUrl": "http://su-crawler:3333", "secret": "asdsgdjsgkagy34567absab"}, "searchClient": {"url": "http://search-client:6008"}, "analyticsService": {"url": "http://analytics:3000"}, "agentHelperService": {"url": "http://localhost:3001"}, "searchService": {"url": "http://search:7000", "formulaFieldSearch": false, "secret": "XoNlzwIYhyxKHSNhnmZceaS99nUATqCp"}, "MLService": {"url": "http://core-ml:5005"}, "agentHelper": {"charLimitRA": 5000}, "chatBotService": {"url": "http://chatbot:3200"}, "indexService": {"url": "http://index-service:2222", "sharedSecret": "very<PERSON><PERSON><PERSON>"}, "cronManager": {"cronMangerUrl": "http://cron-manager:4444", "secret": "qwertyuiop12345zxcvbnmasdf"}, "statusPageService": {"url": "https://status-sandbox.searchunify.com", "secretKey": "Grazitti$271##", "secretAuthToken": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoiS25vd2JsZXIyMyIsImlhdCI6MTY3NTA2MDc0MX0.AFlThPKTNyTnPSKjHRdJUHw5Om-NJd-zsvqXBg7Pci0", "kafka": {"host": "kafka:9092", "userTopic": "statusPage", "contentSourceTopic": "contentSource", "contentSourceObjectTopic": "contentSourceObjects", "searchClientTopic": "searchClient", "publishTenantInfo": "publishTenantInfoStatusPage", "enable": true, "maxRetryTime": 60000, "initialRetryTime": 1000, "retries": 500}}, "cache": {"adminAssestsMaxAge": 0}, "crawlerConfig": {"zoomin": {"japaneseContent": false}, "lithium": {"crawlObjects": {"duplicateObjectTkb": false, "fieldProductCategoryMapping": false}}, "subscription": {"lithiumCsId": 10, "higherlogicCsId": 11}}, "youtube": {"clientId": "183503824596-gu4oln1p45ot2ndlnjpvmn3db1v9qlfn.apps.googleusercontent.com", "appSecret": "svWe6Ze12kFi5bm2TfIUopHo"}, "stackoverflow": {"clientId": 22915, "clientSecret": "gU0KQFwxmOJg5oa7GBuC0g((", "key": "8x8ZDq8rZjgOfguQyed3UA(("}, "charFilterMapping": ["&=>\\u0020scampersand\\u0020", "@=>\\u0020scasperand\\u0020", ">=>\\u0020scgreaterthan\\u0020", "<=>\\u0020sclessthan\\u0020", "$=>\\u0020scdollar\\u0020", "%=>\\u0020scpercentage\\u0020", "^=>\\u0020sccaret\\u0020", "==>\\u0020scequalto\\u0020", ":=>\\u0020sccolon\\u0020", ".=>\\u0020scfullstop\\u0020", "/=>\\u0020scslash\\u0020 ", "-=>\\u0020schyphen\\u0020", "+=>\\u0020scplus\\u0020", "*=>\\u0020scasterisk\\u0020"], "intentDetection": {"url": "http://intent-detection:80"}, "chatBotConfig": {"url": "https://feature6.searchunify.com", "uid": "2c07bccf-ce8e-11eb-9297-0242ac120012"}, "lithium": {"accessTokenUrl": "https://api.lithium.com"}, "predictionIO": {"eventServerHost": "localhost:7070", "engineServerHost": "localhost:8000", "accessKey": "MFsv5XjuIYMWVClpeb9g6jkaD5FdTSRbc0VFE0gUqVVKoUPa9_FmMEqfD6v9xT9_"}, "marketplace": {"host": "http://marketplace:3333", "authorization": "1b218a63ee4a017844636cb5578c345a", "platform": "hbs", "localUrl": "http://backend:6009", "local": true, "localPath": "/home/<USER>/routes/marketplace/localMarketplace/"}, "didYouMean": {"url": "http://didyoumean:7001", "cronTimer": "0 0 * * 0", "enableCron": false}, "cloudStorageConfig": {"cloudProvider": "aws", "isCloudFrontSupported": false, "storageName": "su-client", "storageBaseURL": "https://suinternalazuretest.blob.core.windows.net", "cloudFrontURL": "https://su-client.azureedge.net", "searchClientPrefix": "search-clients", "assetLibPrefix": "asset-lib", "aws": {"accessId": "", "secret": "tDETFNkh/zlZcY46RIM7ZsBp6adutuq4p8SmanfQ", "distributionId": "E2V2SE5R8U3CNL", "region": "ap-south-1"}, "azure": {"accountName": "suinternalazuretest", "accountKey": ""}}, "authUrl": "http://authserver:4010", "allowStatusPageAdminLogin": true, "multiTenantSupport": true, "tenantMaxCsCount": 30, "clusterServiceUrl": 3000, "swagger": {"host": "localhost", "port": 4003}, "aes": {"encryptionkey": "bf3c199c2470cb477d907b1e0917c17b", "iv": "5183666c72eec9e4"}, "gchatUrls": {"migration": "https://chat.googleapis.com/v1/spaces/AAAAFzQ8w8Y/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=MgFyNxsq2Omu4JQQl_NEwIUfhhfbbsXI-3b3aHqIE6A%3D", "boostingAlert": "https://chat.googleapis.com/v1/spaces/AAAAYm-aRnw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=ZBFAb3z6xOHshWL72B9sjX1lO1rJ1ClrpaSGJDmMauo"}, "githubClientId": "5932df051a583a90d21b", "sharepointClientId": "f552c28d-89e2-4621-ae13-3cc5cdbbce07", "salesforceClientId": "3MVG9WtWSKUDG.x4.egfG0lQq64wyWUxqJ5ugxZ6vjPSGGBF276q4fkkY9M38Y.fkz.vKSTQlqTN_FFk8KPOn", "slackClientId": "************.************", "dropbox_client_id": "5108rv7fdg5wxgh", "vimeo_client_id": "794f298031fabd81e17c0c2c8c1fe16e95626930", "unrestrictedDomains": ["<EMAIL>"], "suDomains": ["grazitti.com", "searchunify.com"], "enableBoostingAlert": false, "analyticsSecretKey": "oQ(]PW)@HXe*Xf'-^u+X", "openSearch": {"osMigration": false, "osMigrationDays": 5}, "version": "25.07.00", "hscTokenExpireTime": 180, "searchTunning": {"maxKeywordLength": 500}, "feature": {"stemwords": {"whitelisted": ["3b132438-11d1-11ef-bd6b-3e3f7c426c8e"]}}, "synonymsLimit": 1000, "adminSecretKey": "********************************", "authMiddleware": {"routesToAuth": ["^\\/resendOTPMail", "^\\/resendEmail", "^\\/signUp", "^\\/registerEmail", "^\\/admin\\/userManagement\\/unblockEmail", "^\\/admin\\/userManagement\\/getSignupSettings", "^\\/admin\\/userManagement\\/getAllUsers", "^\\/admin\\/userManagement\\/getUserCount", "^\\/admin\\/userManagement\\/updateQuestion", "^\\/admin\\/userManagement\\/editUser", "^\\/admin\\/userManagement\\/deleteRegisteredUser", "^\\/admin\\/userManagement\\/usersConfigurations", "^\\/admin\\/userManagement\\/usersFeatureConfiguration", "^\\/admin\\/userManagement\\/userMetricsLastSelections", "^\\/forgetPassword", "^\\/logout", "^\\/oauth\\/token", "^\\/admin\\/userManagement\\/getSearchUnifySession", "^\\/admin\\/userManagement\\/changePassword", "^\\/admin\\/userManagement\\/getLastLogin", "^\\/oauthClients\\/", "^\\/getAutoProvisionToken", "^\\/user\\/continue-registration", "^\\/user\\/verify-temporary-user-token", "^\\/user\\/temporary-user-signup", "^\\/admin\\/userManagement\\/temporary-access-teams", "^\\/user\\/getSuperAdminUsersList", "^\\/token\\/"], "oAuthSecure": ["^\\/api", "^\\/api\\/analytics", "^\\/api\\/v2\\/", "^\\/api\\/v3\\/", "^\\/api\\/v2_search", "^\\/authorise", "^\\/agent<PERSON>elper\\/query-stage", "^\\/search\\/searchResultByPost"], "agentForceUrls": ["^\\/agentForceService\\/chat/"], "jwtSecure": ["^\\/admin\\/contentSources\\/byCaseUidAuth", "^\\/mlService\\/first-response", "^\\/mlService\\/case-summary", "^\\/mlService\\/case-timeline", "^\\/mlService\\/update-first-response", "^\\/mlService\\/get-tones", "^\\/mlService\\/user-tone", "^\\/mlService\\/case-sentiments", "^\\/mlService\\/get-esc-score", "^\\/mlService\\/translate-ra", "^\\/mlService\\/regenerate-ra"], "openUrls": ["^\\/addons\\/verifyJwt", "\\/checkToken", "\\/zendeskClient", "^\\/signup", "^\\/resources", "^\\/analytics\\/tableauConnector\\/authTableau", "^\\/analytics\\/tableauConnectorRawApi\\/authTableau", "^\\/assets", "^\\/ext_resources", "^\\/favicon.ico$", "^\\/html", "^\\/mainStaticResource", "^\\/node_modules", "^\\/readMain", "^\\/admin\\/searchClient\\/readAdHTML", "^\\/oauthApiRate[/\\W].*", "^\\/admin\\/contentSources\\/subscriptions", "^\\/knowbler", "^\\/kcs", "^\\/api-docs", "^\\/api-docs\\/analytics", "^\\/tenant-connections", "^\\/utility\\/credentials-cron", "^\\/admin\\/notifications\\/sendReport", "^\\/admin\\/notifications\\/sendNotifications", "^\\/agent<PERSON><PERSON><PERSON>\\/training-status", "\\/saml\\/logout", "^\\/admin\\/trial\\/marketo\\/requestDemoOpen", "\\/searchClientSettings", "^\\/rest\\/analytics\\/resources\\/reports", "\\/s3uploader", "\\/updateMigrations", "\\/admin-login", "\\/status-incoming", "\\/health", "\\/saml\\/auth", "\\/rest\\/analytics\\/llm-emails\\/send-subscription-email", "^\\/ext\\/auth-config", "^\\/ext\\/auth-callback[/\\W].*", "^\\/sc\\/searchClient\\/migration\\/accept-from-sbox", "\\/saml\\/refreshJwtToken", "^\\/publish\\/publish-content-sources-search-clients", "\\/crawler\\/jira\\/authorization-intermediate", "^\\/admin\\/searchClient\\/deleteExpiredAbTests", "^(/)?/scheduledCrons/publishtokentostatuspage$", "^(/)?/quotaManager/updateLimits$", "^\\/publish\\/kafka"], "samlUrls": ["\\/saml\\/login", "^\\/admin\\/userManagement\\/checkSsoEnable"], "uidUrls": ["^\\/chatbot\\/api\\/chat_client_conf", "^\\/chatbot\\/api\\/languages", "\\/getPageRatingData", "^\\/agent<PERSON>elper\\/query", "^\\/slackApis\\/", "^\\/agent<PERSON><PERSON><PERSON>\\/case-sentiments", "^\\/rest\\/analytics\\/track\\/checkResolutionStatus", "^\\/rest\\/analytics\\/content-health\\/get-article-health-scores", "^\\/rest\\/analytics\\/content-health\\/save-article-evaluation", "^\\/rest\\/analytics\\/autoGeneratedTitle\\/getSuggestedTitle", "^\\/rest\\/analytics\\/agent-request\\/related-case-articles", "^\\/rest\\/analytics\\/agent-request\\/generate-content-health", "^\\/category-section\\/get-zendesk-section-category", "^\\/mlService\\/first-response", "^\\/rest\\/analytics\\/duplicacy-checker\\/check-duplicate-articles", "^\\/rest\\/analytics\\/content-health\\/getReviewArticles", "^\\/rest\\/analytics\\/intelligent-insights\\/getFilteredKnowledgeBaseEffectivenessData", "^\\/rest\\/analytics\\/agent-auth\\/check-duplicate-articles", "^\\/rest\\/analytics\\/autoGeneratedTitle\\/compare-hashes", "^\\/rest\\/analytics\\/autoGeneratedTitle\\/get-article-meta", "^\\/rest\\/analytics\\/content-health\\/getArticlesStatus", "^\\/rest\\/analytics\\/content-health\\/evaluateArticle", "^\\/rest\\/analytics\\/autoGeneratedTitle\\/compare-hashes", "^\\/rest\\/analytics\\/autoGeneratedTitle\\/get-article-meta", "^\\/rest\\/analytics\\/autoGeneratedTitle\\/check-duplicate-articles"], "tenantUrls": ["\\/updateCronModel", "^\\/admin\\/cron\\/", "^\\/synonyms\\/dymTrainingStatus", "^\\/admin\\/searchClient\\/getSearchFieldSettings", "^\\/admin\\/searchClient\\/getContentSourceFields", "^\\/stopwords\\/read", "^\\/synonyms\\/suggestion", "^\\/searchClientAnalytics\\/getContentAuthData"], "hashUrls": ["\\/contentSources\\/subscriptions"], "accessTokenUrls": ["^\\/mlService\\/su-gpt", "^\\/mlService\\/summary"], "suUserUrls": ["^\\/admin\\/crawlerConfig", "^\\/admin\\/schedulerConfig"], "extJwtSecure": ["^\\/ext\\/config", "^\\/ext\\/case", "^\\/ext\\/article-list", "^\\/ext\\/article", "^\\/ext\\/generate-content", "^\\/ext\\/language-listing", "^\\/ext\\/article-health-score", "^\\/ext\\/article-evaluation", "^\\/ext\\/resolution-status", "^\\/ext\\/agent-email", "^\\/ext\\/attach-article", "^\\/ext\\/update-case", "^\\/ext\\/publish-article", "^\\/ext\\/generate-content-health", "^\\/ext\\/duplicate-articles", "^\\/ext\\/article-meta-data", "^\\/ext\\/compare-hashes", "^\\/ext\\/article-quality", "^\\/ext\\/email-sharing"]}, "dosConfig": {"requestThreshold": 5, "blockOriginIpTimeInSeconds": 60, "windowSizeInSeconds": 1, "endpoints": ["/api/v2_gpt"]}, "relevanceScore": {"dataRetention": 180}, "statusPageTokenOptions": {"key": "2CY9F8fwS0jSuH1LQsBvDGghrr6M8dg3", "expiry": "1d"}, "internalAdminSecret": "E7bHmBXmy81jjFTA8Cq6r7h7b945hpPU", "routeMappingForQuota": {"searchUnifyGPT": ["/api/v2_gpt"]}, "loginSessionTime": 30}