var express = require('express');
var router = express.Router();
var request = require("request");
var config = require("config");
function dynamicLoginPageFunc(req, res) {
  let staticContent = `
    <style>
    .logo img {
      padding-left: 30px;
      padding-top: 35px;
    }
    .left-pane-wrapper {
        width: 50%;
        height: 100%;
        position: relative;
    }
    .background {
      width: 100%;
      background-image: url("assets/img/Colubridae2020.svg");
      height: 100%;
      position: relative;
      background-repeat: no-repeat;
      background-position: left;
      background-size: cover;
    } 
    .main-body {
      padding: 0px 25px 25px;
      position: absolute;
      bottom: 0;
    }
    .headding-1 {
      font-size: 14px;
      font-weight: 500;
      padding-bottom: 5px;
      color: #63ADFC;
    }
    .headding-2 {
      font-size: 16px;
      font-weight: 500;
      padding-bottom: 8px;
      color: #63ADFC;
    }
    .headding-3 {
      font-size: 36px;
      font-weight: 600;
      color: #4A4A4A;
    }
    .headding-4 {
      font-size: 30px;
      font-weight: 500;
      padding-bottom: 15px;
      color: #4A4A4A;
      opacity: 0.8;
    }
    .Know-More-btn {
      width: 136px;
      height: 36px;
      background: transparent;
      border-radius: 2px;
      border: 1px solid #55C7FF;
      color: #55C7FF;
      cursor: pointer;
      z-index: 33;
      position: relative;
      font-weight: 500;
    }
    .static-login {
      height: 100%;
      position: absolute;
      top: 0;
      width: 100%;
      opacity: 1;
    }
    .opacity-0 {
      opacity: 0;
    }
    .opacity-1 {
      opacity: 1;
    }
    @media (min-width: 480px) and (max-width: 1024px) {
      #demo {
        height: 100%;
        width: 50%;
        float: left;
      }
      .left-pane-wrapper {
        width: 100%;
      }
      .background {
        display: flex;
        flex-direction: column;
      }
      .main-body {
        padding: 0px 20px 20px;
      }
      .headding-1 {
        font-size: 12px;
      }
      .headding-2 {
        font-size: 14px;
      }
      .headding-3 {
        font-size: 24px;
      }
      .headding-3 {
        font-size: 18px;
      }
    }
    @media (min-width: 1400px){
      .headding-1 {
        font-size: 18px;
      }
      .headding-2 {
        font-size: 20px;
      }
      .headding-3 {
        font-size: 40px;
      }
      .headding-3 {
        font-size: 34px;
      }
    }
    .fadeIn {
      -webkit-animation: fadeIn 1000ms linear both;
      animation: fadeIn 1000ms linear both;
    }
    
    @-webkit-keyframes fadeIn {
      from {
        opacity: 0;
      }
    
      to {
        opacity: 1;
      }
    }
    
    @keyframes fadeIn {
      from {
        opacity: 0;
      }
    
      to {
        opacity: 1;
      }
    }    
    </style>
    <div class="left-pane-wrapper">
      <div class="background fadeIn">
        <div class="logo">
          <img src="assets/img/su-logo-black.svg">
        </div>
        <div class="main-body">
          <div class="headding-1">Our Annual Spring Release</div>
          <div class="headding-2">Colubridae'20</div>
          <div class="headding-3">Elevate Your Enterprise</div>
          <div class="headding-4"> Search Experience</div>
          <div>
            <a target="_blank" href="https://docs.searchunify.com/Content/Release-Notes/Colubridae-20-Release-Notes.htm">
              <button class="Know-More-btn">Know More</button>
            </a>
          </div>
        </div>
      </div>
    </div>
    `
  if (config.has('dynamicScreen')) {
    let options = {
      method: "GET",
      url: config.get('dynamicScreen'),
      headers: { 'content-type': 'application/json' }
    };
    request(options, function (error, result) {
      if (error) {
        console.error("error", error);
      } else {
        if (result.body) {
          result.body = JSON.parse(result.body)
          res.send({ status: 'OK', data: `<style>${result.body.css}</style>` + result.body.html });
        } else {
          res.send({
            status: 'OK', data: staticContent
          })
        }
      }
    });
  } else {
    res.send({ status: 'OK', data: staticContent })
  }
}


module.exports = {
  router: router,
  dynamicLoginPageFunc: dynamicLoginPageFunc
}