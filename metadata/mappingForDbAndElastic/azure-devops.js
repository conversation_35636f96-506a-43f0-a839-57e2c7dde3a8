module.exports = {
  objects: [
    {
      name           : "workitems",
      label          : "work items",
      status         : 1,
      boosting_factor: 1,
      base_url       : "",
      fields         : [],
    },
  ],
  fields: [
    {
      name        : "id",
      label       : "id",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "AreaPath",
      label       : "AreaPath",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "TeamProject",
      label       : "TeamProject",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "IterationPath",
      label       : "IterationPath",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "WorkItemType",
      label       : "WorkItemType",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "State",
      label       : "State",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "Reason",
      label       : "Reason",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "AssignedTo__child__displayName",
      label       : "Assigned To",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "post_time",
      label       : "Created Date",
      type        : "datetime",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 0,
      isActive    : 1,
      isMerged : 1
    }, 
    {
      name        : "CreatedBy__child__displayName",
      label       : "Created By",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "ChangedDate",
      label       : "ChangedDate",
      type        : "datetime",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 0,
      isActive    : 1,
    },
    {
      name        : "ChangedBy__child__displayName",
      label       : "Changed By",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "CommentCount",
      label       : "CommentCount",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "Title",
      label       : "Title",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "Tags",
      label       : "Tags",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "StateChangeDate",
      label       : "StateChangeDate",
      type        : "datetime",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 0,
      isActive    : 1,
    },
    {
      name        : "ActivatedDate",
      label       : "ActivatedDate",
      type        : "datetime",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 0,
      isActive    : 1,
    },
    {
      name        : "ActivatedBy__child__displayName",
      label       : "ActivatedBy",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "Priority",
      label       : "Priority",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "Severity",
      label       : "Severity",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "ValueArea",
      label       : "ValueArea",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "SystemInfo",
      label       : "SystemInfo",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "ReproSteps",
      label       : "ReproSteps",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "Comments",
      label       : "Comments",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "ViewHref",
      label       : "ViewHref",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
    {
      name        : "Description",
      label       : "Description",
      type        : "string",
      isFilterable: 1,
      isSortable  : 1,
      isSearchable: 1,
      isActive    : 1,
    },
  ],
};
