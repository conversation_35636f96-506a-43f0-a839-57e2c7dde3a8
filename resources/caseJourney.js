var filters = document.getElementById('customScript').getAttribute('value').split(',');
console.log('searchunify script loaded');    
    var expanded = false;
    
    document.getElementById("all").checked = true
    for(let i = 0 ; i < filters.length ; i++){
        if(document.getElementById(filters[i]))
        document.getElementById(filters[i]).checked = true
    }
    function showCheckboxes() {
        var checkboxes = document.getElementById("checkboxes");
        if (!expanded) {
            checkboxes.style.display = "block";
            expanded = true;
        } else {
            checkboxes.style.display = "none";
            expanded = false;
        }
    }
    function checkUncheckAll(){
        var checkBoxAll = document.getElementById("all");
        if(checkBoxAll.checked == true){
            for(let i = 0 ; i < filters.length ; i++){
                if(document.getElementById(filters[i]))
                document.getElementById(filters[i]).checked = true
            }
          
        }else{
            for(let i = 0 ; i < filters.length ; i++){
                if(document.getElementById(filters[i]))
                document.getElementById(filters[i]).checked = false
            }
        }
        checkUncheck()
    }

    function checkUncheck() {
        let arr = [];
        var checkBoxAll = document.getElementById("all");
        for(let i = 0 ; i < filters.length ; i++){
            if(document.getElementById(filters[i])){
                if(document.getElementById(filters[i]).checked==true){
                var index = arr.indexOf(filters[i])
                if(index == -1)
                arr.push(filters[i])           
            }else{
                checkBoxAll.checked = false
                for(let j = 1 ; j < document.getElementsByClassName("suanalyticstype").length ; j++){
                        if(filters[i]==document.getElementsByClassName("suanalyticstype")[j].innerHTML){
                        document.getElementsByClassName("suanalyticstype")[j].parentElement.classList.add("hide")
                        }
                    }
                var index = arr.indexOf(filters[i]);
                if (index > -1) {
                arr.splice(index, 1); 
                }
            }
            }
            
        }
        let count = 0;
        for(let i = 0 ; i < filters.length ; i++){
            if(document.getElementById(filters[i])){
                if(document.getElementById(filters[i]).checked==true){
                count++;
                }
            }
            if(count == filters.length){
                checkBoxAll.checked = true
            }
        }
        

       for(let i = 0 ; i < arr.length ; i++){
           for(let j = 1 ; j < document.getElementsByClassName("suanalyticstype").length ; j++){
               if(arr[i]==document.getElementsByClassName("suanalyticstype")[j].innerHTML){
                document.getElementsByClassName("suanalyticstype")[j].parentElement.classList.remove("hide")
               }
           }
       }

    }
