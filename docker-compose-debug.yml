# docker compose for admin panel service
version: "3.0"

services:
  backend:
    container_name: backend
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ".:/home/<USER>"
      - "/home/<USER>/node_modules"
      # - "search_clients:/home/<USER>/resources/search_clients_custom"
      - "admin_generic:/home/<USER>/resources/search_clients_custom"
      - "synonyms:/home/<USER>/synonyms"
      - "backend_node_modules:/home/<USER>/node_modules"
      - "admin_crontab:/var/spool/cron/crontabs/"
      - "asset_library:/home/<USER>/resources/Asset-Library"
      - analytics_reports:/home/<USER>/reports
    networks:
      - shared_network
    deploy:
      resources:
        limits:
          memory: 2G
      
    command: >
      sh -c "crond -b -l 8
      && npm run debug"
    ports:
      - "6009:6009"
      - "2300:2300"
      
volumes:
# search_clients:
  admin_generic:
  synonyms:
  backend_node_modules:
  admin_crontab:
  asset_library:
  analytics_reports:

networks:
  shared_network:
    external:
      name: shared_network
