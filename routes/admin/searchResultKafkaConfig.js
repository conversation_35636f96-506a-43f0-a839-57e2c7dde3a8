var express = require("express");
var router = express.Router();
var async = require("async");
var config = require("config");
var commonFunctions = require("./../../utils/commonFunctions");
const { getTenantInfoFromTenantId } = require('auth-middleware');
const kafkaLib = require("../../utils/kafka/kafka-lib");
const kafkaStatusLib = require("../../utils/kafka-status-page/kafka-lib");

const { getBoostingDocs } = require('./service/searchResultKafkaConfigService');

const publishTenantUidToAuth = (uid,tenantId) => {
  let objData = {
    data: [{
      uid: uid,
      tenantId: tenantId
    }]
  };
  try {
    kafkaLib.publishMessage({
      topic: config.get("kafkaTopic.tenantUidTopic"),
      messages: [
        {
          value: JSON.stringify(objData),
          key: uid
        },
      ],
    });
    commonFunctions.errorlogger.info(
      "Kafka update"
    );
  } catch (e) {
    commonFunctions.errorlogger.error(
      " update kafka topic error",
      e
    );
  }
};

const publishDataToStatusPage = (topic, message) => {
  try {
    kafkaStatusLib.publishMessage({
      topic: topic,
      messages: [
        message,
      ],
    });
  } catch(e) {
    commonFunctions.errorlogger.info("Error while publishing data to status page ",e);
  }
}

const publishBoostingKafka = (boostingData) => {
  console.log("BOOSTING Kafka : ", JSON.stringify(boostingData));
  try {
    kafkaLib.publishMessage({
      topic: config.get("kafkaTopic.boostingConfigTopic"),
      messages: [
        {
          value: JSON.stringify(boostingData),
        },
      ],
    });
    commonFunctions.errorlogger.info("Boosting Kafka published");
  } catch (e) {
    commonFunctions.errorlogger.error("Error in publishing Boosting Kafka", e);
  }
};

const getSearchClientSettingsViaKafka = function (configuration ,req, cb) {
  let settings = {event: 'insert'};
  let keyUid = configuration.uid;
  let platformId = configuration.platformId;
  // remove it after QA
  commonFunctions.errorlogger.info("=======getSearchClientSettingsViaKafka=========keyUid===================platformId==========================>>>>>>>>>>>>>>>",keyUid , platformId);
  async.auto(
    {
      one: (cb) => {
        let sql = `SELECT
              cs.id    AS cs_id,
              cs.label AS cs_label,
              cs.elasticIndexName AS indexName,
              cs.content_source_type_id AS content_source_type_id,
              cs.sort_order AS sort_order,

              cso.base_url          AS cso_base_url,
              cso.content_source_id AS cso_content_source_id,
              cso.id                AS cso_id,
              cso.label             AS cso_label,
              cso.name              AS cso_name,
              cso.status            AS cso_status,
              cso.internal_access   AS cso_internal_access,
              cso.external_access   AS cso_external_access,
              csof.content_source_object_id AS csof_content_source_object_id,
              csof.id                       AS csof_id,
              csof.isActive                 AS csof_isActive,
              csof.isFilterable             AS csof_isFilterable,
              csof.isSearchable             AS csof_isSearchable,
              csof.isSortable               AS csof_isSortable,
              csof.label                    AS csof_label,
              csof.name                     AS csof_name,
              csof.type                     AS csof_type,
              csof.fragment_size            AS fragment_size,
              csof.merge_field_id           AS merge_field_id
            FROM
              content_sources AS cs,
              content_source_objects AS cso,
              content_source_object_fields AS csof
            WHERE
              cs.id = cso.content_source_id
              AND cso.id = csof.content_source_object_id
            ORDER BY cs_label,csof_name ASC`;
            connection[req.headers['tenant-id']].execute.query(sql, (e, r, f) => {
          if (e) cb(e);
          else {
            settings.sources = [];
            r.forEach((e) => {
              let a = settings.sources.find((x) => {
                return x.id == e.cs_id;
              });
              if (!a) {
                a = {
                  id: e.cs_id,
                  label: e.cs_label,
                  enabled: false,
                  objects: [],
                  index: e.indexName,
                  content_source_type_id: e.content_source_type_id,
                  sort_order: e.sort_order,
                };
                settings.sources.push(a);
              }
              let b = a.objects.find((x) => {
                return x.id == e.cso_id;
              });
              if (!b) {
                b = {
                  base_url: e.cso_base_url,
                  content_source_id: e.cso_content_source_id,
                  id: e.cso_id,
                  label: e.cso_label,
                  name: e.cso_name,
                  status: e.cso_status,
                  enabled: false,
                  internal_access: e.cso_internal_access,
                  external_access: e.cso_external_access,
                  fields: [],
                };
                a.objects.push(b);
              }
              b.fields.push({
                content_source_object_id: e.csof_content_source_object_id,
                id: e.csof_id,
                isActive: e.csof_isActive,
                isFilterable: e.csof_isFilterable,
                isSearchable: e.csof_isSearchable,
                isSortable: e.csof_isSortable,
                label: e.csof_label,
                name: e.csof_name,
                type: e.csof_type,
                fragment_size: e.fragment_size,
                use: {},
              });
            });
            cb();
          }
        });
      },
      two: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT * from search_clients_to_content_objects where search_client_id=?`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              cb(null, r);
            }
          }
        );
      },
      field_mapping: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT
              scf.content_source_object_field_id      AS scf_content_source_object_field_id,
              scf.id                                  AS scf_id,
              scf.priority                            AS scf_priority,
              scf.search_clients_to_content_object_id AS scf_search_clients_to_content_object_id,
              scf.use_as                              AS scf_use_as,
              scf.exclude                             AS scf_exclude,
              scf.search_priority                     AS scf_search_priority,
              scf.use_value                           AS scf_use_value,
              scf.sort_by                             AS scf_sort_by,
              scf.order_by                            AS scf_order_by,
              scf.summary_length                      AS scf_summary_length,
              scf.level_limit                         AS scf_level_limit,
              scf.extra_field                         AS scf_extra_field,
              scf.track_analytics                     AS scf_track_analytics,
              scf.auto_learning                       AS scf_auto_learning,
              scf.facet_type                           AS scf_facet_type,
              scf.facet_data_type                       AS scf_facet_data_type
          FROM search_clients_to_content_objects AS sctco,
              search_clients_filters AS scf
          WHERE
              sctco.search_client_id = ?
              AND sctco.id = scf.search_clients_to_content_object_id`, [platformId], (e, r, f) => {
                if (e) cb(e);
                else {
                    r = r.map(en => {
                        return {
                            content_source_object_field_id: en.scf_content_source_object_field_id,
                            id: en.scf_id,
                            priority: en.scf_priority,
                            search_clients_to_content_object_id: en.scf_search_clients_to_content_object_id,
                            use_as: en.scf_use_as,
                            exclude: en.scf_exclude,
                            search_priority: en.scf_search_priority,
                            use_value: en.scf_use_value,
                            sort_by: en.scf_sort_by,
                            order_by: en.scf_order_by,
                            summary_length: en.scf_summary_length,
                            level_limit: en.scf_level_limit,
                            extra_field: en.scf_extra_field,
                            track_analytics: en.scf_track_analytics,
                            auto_learning: en.scf_auto_learning,
                            facet_type: en.scf_facet_type,
                            facet_data_type: en.scf_facet_data_type
                        };
                    });
                    cb(null, r);
                }
            });
        },
        metadata_mapping: cb => {
            connection[req.headers['tenant-id']].execute.query(`SELECT
              scmf.field_id      AS scmf_content_source_object_field_id,
              scmf.id                                  AS scmf_id,
              scmf.priority                            AS scmf_priority,
              scmf.search_client_to_content_object_id AS scmf_search_clients_to_content_object_id,
              scmf.autosuggestField AS scmf_autosuggestField,
              scmf.metaData AS scmf_metaData
          FROM search_clients_to_content_objects AS sctco,
              search_client_metadata_fields AS scmf
          WHERE
              sctco.search_client_id = ?
              AND sctco.id = scmf.search_client_to_content_object_id`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              r = r.map((en) => {
                return {
                  content_source_object_field_id:
                    en.scmf_content_source_object_field_id,
                  id: en.scmf_id,
                  priority: en.scmf_priority,
                  search_clients_to_content_object_id:
                    en.scmf_search_clients_to_content_object_id,
                  autosuggestField: en.scmf_autosuggestField,
                  metaData: en.scmf_metaData,
                  use_as: en.scmf_metaData == 1 ? "Metadata" : "",
                };
              });
              cb(null, r);
            }
          }
        );
      },
      preview_mapping: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT
            scpf.field_id      AS scpf_content_source_object_field_id,
            scpf.id                                  AS scpf_id,
            scpf.search_client_to_content_object_id AS scpf_search_clients_to_content_object_id,
            scpf.sc_id AS scpf_sc_id,
            scpf.preview_order AS scpf_preview_order
        FROM search_clients_to_content_objects AS sctco,
            search_client_preview_fields AS scpf
        WHERE
            sctco.search_client_id = ?
            AND sctco.id = scpf.search_client_to_content_object_id`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              r = r.map((en) => {
                return {
                  content_source_object_field_id:
                    en.scpf_content_source_object_field_id,
                  id: en.scpf_id,
                  search_clients_to_content_object_id:
                    en.scpf_search_clients_to_content_object_id,
                  preview_order: en.scpf_preview_order,
                  sc_id: en.scpf_sc_id
                };
              });
              cb(null, r);
            }
          }
        );
      },
      client: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT *,sc.id searchId,tt.id,sc.name scName,tt.name tname, uf.contentSearchExp contentSearchExp, uf.searchExp searchExp, uf.conversionExp conversionExp FROM search_clients sc LEFT JOIN template_types tt ON sc.template_type_id = tt.id LEFT JOIN user_feedback uf ON sc.uid = uf.search_client_uid WHERE sc.id=? LIMIT 1`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              if (r.length) {
                // added pagination handling
                r[0].scrollPagination =
                  r[0].pagination === "scroll_pagination" ? 1 : 0;
                r[0].moreResultButton =
                  r[0].pagination === "more_result_button" ? 1 : 0;
                r[0].pageNoButton =
                  r[0].pagination === "page_no_button" ? 1 : 0;
                r[0].similarSearch = r[0].similar_search;
                r[0].specialSearch = r[0].special_search;
                try {
                  r[0].hiddenFacet = Array.isArray(r[0].hidden_facet)
                    ? r[0].hidden_facet
                    : JSON.parse(r[0].hidden_facet);
                } catch (error) {
                  commonFunctions.errorlogger.info("hidden_facet: ",error);
                }
                r[0].autoCompleteInstant = r[0].autocomplete_instant;
                r[0].autoComplete = r[0].autocomplete;
                r[0].redirectionUrl = r[0].redirection_url;
                r[0].mergeSources = r[0].mergeSources;
                r[0].languageManager = r[0].languageManager;
                r[0].SCsalesforceConsoleConfigurations =
                  r[0].SCsalesforceConsoleConfigurations;
                r[0].ViewedResults = r[0].ViewedResults;
                r[0].smartFacets = r[0].smart_facet;
                r[0].minFacetSelection = r[0].min_facet_selection;
                r[0].relevancy = r[0].relevancy;
                r[0].relevancyScoreBandsEnabled = r[0].relevancy && JSON.parse(r[0].relevancy) && JSON.parse(r[0].relevancy).relevancyScores && JSON.parse(r[0].relevancy).relevancyScores.enabled ? true : false;
                r[0].ner = r[0].ner;
                r[0].reRanking = r[0].re_ranking;
                r[0].reRankValue = r[0].re_rank_value ;
                delete r[0].pagination;
                delete r[0].similar_search;
                delete r[0].special_search;
                delete r[0].hidden_facet;
                delete r[0].autocomplete_instant;
                delete r[0].autocomplete;
                delete r[0].redirection_url;
                delete r[0].smart_facet;
                delete r[0].min_facet_selection;
                r[0].id = r[0].searchId;
                r[0].name = r[0].scName;
                r[0].preTag = r[0].pre_tag;
                r[0].postTag = r[0].post_tag;
                r[0].userFeedbackEnabled = {
                  contentSearchExp: (
                      r[0].contentSearchExp && Object.keys(JSON.parse(r[0].contentSearchExp || '{}')).length
                      ? JSON.parse(r[0].contentSearchExp).enabled || false : false
                    ),
                  searchExp: (
                      r[0].searchExp && Object.keys(JSON.parse(r[0].searchExp || '{}')).length
                      ? JSON.parse(r[0].searchExp).enabled || false : false
                    ),
                  conversionExp: (
                    r[0].conversionExp && Object.keys(JSON.parse(r[0].conversionExp || '{}')).length
                    ? JSON.parse(r[0].conversionExp).enabled || false : false
                  )
                };
                r[0].highlight = JSON.parse(r[0].highlight|| '{}');
                r[0].gpt = JSON.parse(r[0].gpt|| '{}');
                r[0].gptFeedback = r[0].gpt_feedback;
                r[0].vector_search_settings = JSON.parse(r[0].vector_search_settings|| '{}');
                r[0].gptRules = r[0].gptRules;
                r[0].domainPermission = JSON.parse(r[0].domainPermission || '{}');
                r[0].attachment_preview = r[0].attachment_preview;
                r[0].summarize = JSON.parse(r[0].summarize|| '{}');
                cb(null, r);
              } else {
                cb(null, []);
              }
            }
          }
        );
      },
      deflection_formula: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT * FROM deflection_formula WHERE search_client_id=?`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else cb(null, r);
          }
        );
      },
      getAddonsStatus: (cb) => {
        commonFunctions.getAddonsStatus(req, (error, result) => {
          if (result && result[7]) {
            cb(null, result[7].is_installed);
          } else {
            cb(null, 0);
          }
        });
      },
      analyticsReports: (cb) => {
        let sql = `SELECT * FROM analytics_reports LEFT JOIN (SELECT * FROM search_client_to_analytics_report WHERE search_client_to_analytics_report.search_client_id = ?) AS sctar ON analytics_reports.id = sctar.analytics_report_id WHERE (analytics_reports.is_dashboard=0)`;
        connection[req.headers['tenant-id']].execute.query(
          {
            sql: sql,
            nestTables: true,
          },
          [platformId],
          (error, rows) => {
            // console.log(rows);
            cb(error, rows);
          }
        );
      },
      boostField: (cb) => {
        connection[req.headers['tenant-id']].execute.query(`SELECT * FROM boosting_field`, (e, r, f) => {
          if (e) cb(e);
          else {
            cb(null, r);
          }
        });
      },
      getExcludedSuggestionsQuery: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `select * from suggestions_removed WHERE search_client_id = ?`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              cb(null, r);
            }
          }
        );
      },
      getBoostingDetails: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          "SELECT * FROM `keyword_boost` where search_client_id=? AND deindex=0",
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              cb(null, r);
            }
          }
        );
      },
      customBoosting: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          "select * from custom_boosting WHERE search_client_id  = ?",
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              cb(null, r);
            }
          }
        );
      },
      keywordBoosting: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          "SELECT * FROM keyword_boost INNER JOIN search_clients ON keyword_boost.search_client_id = search_clients.id WHERE search_clients.uid = ?",
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              r = r.map((item) => delete item.custom_css);
              cb(null, r);
            }
          }
        );
      },
      intentBoosting: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          "SELECT * FROM intent_boosting ib INNER JOIN search_clients sc ON ib.search_client_id = sc.id WHERE search_client_id = ?",
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              cb(null, r);
            }
          }
        );
      },
      fieldBoosting: cb => {
        connection[req.headers['tenant-id']].execute.query("select name, boosting_factor as boostingFactor, sort_value, is_sort from field_boosting WHERE search_client_id  = ?", [platformId], (e, r, f) => {
            if (e)
                cb(e);
            else {
                cb(null, r);
            }
        });
     },
     tenantInfo: async (cb) => {
      if(req.headers && req.headers.session && !req.headers.session.esClusterIp ){
        console.log('request url es cluster ip empty :',req.url);
        console.log('request header tenant id  es cluster ip empty :',req.headers['tenant-id']);
      }
      if(req.headers && req.headers.session && req.headers.session.esClusterIp){
        return {
          clusterInfo: { clusterId: req.headers.session.esClusterId, name:req.headers.session.esClusterName, host: req.headers.session.esClusterIp, tpk:req.headers.session.tpk },
          mlClusterInfo : { mlClusterId: req.headers.session.mlClusterId, name: req.headers.session.mlClusterName, host: req.headers.session.mlClusterIp }
        }
      }else{
        const data = await getTenantInfoFromTenantId(req.headers['tenant-id']);
        const tenantInfo = data.length ? data[0] : {}
        return {
          clusterInfo:{ clusterId: tenantInfo.es_cluster_id, name: tenantInfo.es_cluster_name, host:tenantInfo.es_cluster_ip, tpk: tenantInfo.tpk },
          mlClusterInfo: { mlClusterId: tenantInfo.ml_cluster_id, name: tenantInfo.ml_cluster_name, host: tenantInfo.ml_cluster_ip },
      }
      }
     },
      getDisplayFields: [
        "client",
        (dataFromAbove, cb) => {
          let uid = dataFromAbove.client[0].uid;
          commonFunctions.getDisplayFieldsInSearch(uid,req, (err, displayRows) => {
            let rowsDisplay = [];
            for (var iy = 0; iy < displayRows.length; iy++) {
              if (
                displayRows[iy]["elasticIndexName"] &&
                displayRows[iy]["elastic_object_name"]
              ) {
                rowsDisplay[iy] = {};
                rowsDisplay[iy]["elasticFieldName"] =
                  displayRows[iy]["elastic_field_name"] &&
                  displayRows[iy]["elastic_field_name"].includes(".")
                    ? displayRows[iy]["elastic_field_name"].replace(/\./g, "_")
                    : displayRows[iy]["elastic_field_name"];
                rowsDisplay[iy]["elasticIndexName"] =
                  displayRows[iy]["elasticIndexName"];
                rowsDisplay[iy]["indexLabel"] = displayRows[iy]["indexLabel"];
                rowsDisplay[iy]["elasticObjectName"] =
                  displayRows[iy]["elastic_object_name"];
                rowsDisplay[iy]["displayFieldName"] =
                  displayRows[iy]["display_field_name"];
                rowsDisplay[iy]["summaryDisplayName"] =
                  displayRows[iy]["summary_display_name"];
                rowsDisplay[iy]["searchPriority"] =
                  displayRows[iy]["search_priority"];
                rowsDisplay[iy]["summaryLength"] =
                  displayRows[iy]["summary_length"];
                rowsDisplay[iy]["extraField"] =
                  displayRows[iy]["extra_field"];
                rowsDisplay[iy]["trackAnalytics"] =
                  displayRows[iy]["track_analytics"]
                rowsDisplay[iy]["merged"] =
                  displayRows[iy]["merge_field_id"] !== 0
              }
            }
            cb(null, rowsDisplay);
          });
        },
      ],
      getMetadataFieldsInObject: [
        "client",
        (dataFromAbove, cb) => {
          let uid = dataFromAbove.client[0].uid;
          commonFunctions.getMetadataFieldsInObjects(uid, req,(err, metaFields) => {
            cb(null, metaFields);
          });
        },
      ],
      getPreviewFieldsInObject: [
        "client",
        (dataFromAbove, cb) => {
          let id = dataFromAbove.client[0].id;
          commonFunctions.getPreviewFieldsInObjects(id, req,(err, previewFields) => {
            cb(null, previewFields);
          });
        },
      ],
      slackChannels: [
        "client",
        (results, cb) => {
          if (
            results["client"] &&
            results["client"][0].search_client_type_id == 22
          ) {
            connection[req.headers['tenant-id']].execute.query(
              `SELECT channel_id FROM search_clients WHERE channel_id IS NOT NULL`,
              (e, r, f) => {
                if (e) cb(e);
                else {
                  cb(null, r);
                }
              }
            );
          } else cb(null, []);
        },
      ],
      three: [
        "one",
        "two",
        "field_mapping",
        "metadata_mapping",
        "preview_mapping",
        "deflection_formula",
        "client",
        "analyticsReports",
        "boostField",
        "getAddonsStatus",
        "slackChannels",
        "customBoosting",
        "keywordBoosting",
        "intentBoosting",
        "getExcludedSuggestionsQuery",
        "getBoostingDetails",
        "getMetadataFieldsInObject",
        "getPreviewFieldsInObject",
        "getDisplayFields",
        "fieldBoosting",
        "tenantInfo",
        (rs, cb) => {
          settings.sources.forEach((src) => {
            src.contentSourceId = src.id;
            src.objects.forEach((obj) => {
              obj.enabled = rs.two.find((x) => {
                return x.content_source_object_id == obj.id;
              })
                ? true
                : false;
              if (obj.enabled) {
                obj.indexName = src.index;
                obj.title_field_id = rs.two.find((x) => {
                  return x.content_source_object_id == obj.id;
                }).title_field_id;
                obj.base_href = rs.two.find((x) => {
                  return x.content_source_object_id == obj.id;
                }).base_href;
                obj.compose_title = rs.two.find((x) => {
                  return x.content_source_object_id == obj.id;
                }).compose_title;
                obj.icon = rs.two.find((x) => {
                  return x.content_source_object_id == obj.id;
                }).icon;
                obj.search_clients_to_content_object_id =
                  rs.two.find((x) => {
                    return x.content_source_object_id == obj.id;
                  }).id || null;
                (obj.boosting_factor =
                  rs.two.find((x) => {
                    return x.content_source_object_id == obj.id;
                  }).boosting_factor || 1),
                  (src.enabled = true);
                obj.permission_by_pass =
                  rs.two.find((x) => {
                    return x.content_source_object_id == obj.id;
                  }).permission_by_pass || 0;
                obj.isContentBoostingEnabled =
                  rs.two.find((x) => {
                    return x.content_source_object_id == obj.id;
                  }).isContentBoostingEnabled || 0;
                let mr =
                  rs.two.find((x) => {
                    return x.content_source_object_id == obj.id;
                  }).merge_results || "";
                obj.merge_results =
                  mr && JSON.parse(mr || "{}").enabled == true ? mr : "";
                
                obj.preview_type =
                  rs.two.find((x) => {
                    return x.content_source_object_id == obj.id;
                  }).preview_type || 1;
                obj.sourceOrder = (rs.client[0].horizontalTabEnabled && rs.client[0].horizontalTabFacet == '_type') || (rs.client[0].sourceSortBy == 'custom')
                  ? JSON.parse(rs.client[0].sourceOrder || "[]").indexOf(obj.name) > -1
                  ? JSON.parse(rs.client[0].sourceOrder || "[]").indexOf(obj.name) : null : null;
                obj.formula = rs.two.find((x) => x.content_source_object_id == obj.id).formula;
                  if(obj.formula) {
                    obj.formula = JSON.parse(obj.formula)
                    if(obj.formula.condition){
                      obj.formula.condition = obj.formula.condition.map((fr) => {
                          fr.byPass = obj.permission_by_pass;
                          return fr;
                      })
                    }
                    obj.formula = JSON.stringify(obj.formula);
                  }
              }
              obj.fields.forEach((f) => {
                f.title = obj.title_field_id == f.id ? true : false;
                f.use =
                  rs.field_mapping.find((x) => {
                    return x.content_source_object_field_id == f.id;
                  }) || {};
                f.metadata =
                  rs.metadata_mapping.find((x) => {
                    return x.content_source_object_field_id == f.id;
                  }) || {};
                if (rs.boostField) {
                  rs.boostField.map((boost) => {
                    if (f.id === boost.content_source_object_field_id) {
                      f.field_boosting_value = boost.boosting_value;
                      f.field_boosting_id = boost.id;
                    }
                  });
                }
                f.preview =
                rs.preview_mapping.find((x) => {
                  return x.content_source_object_field_id == f.id;
                }) || {};
              });
            });
            let byPassObject = src.objects.find((x) => {
              return src.id == x.content_source_id && x.permission_by_pass;
            });
            src.byPass = byPassObject ? byPassObject.permission_by_pass : 0;
            src.indexOrder = rs.client[0].horizontalTabEnabled && rs.client[0].horizontalTabFacet == '_index'
              ? JSON.parse(rs.client[0].horizontalTabOrder || "[]").indexOf(src.index) > -1
              ? JSON.parse(rs.client[0].horizontalTabOrder || "[]").indexOf(src.index) : null : null;
          });
          let caseDeflectionIndex = 0;
          rs.analyticsReports = rs.analyticsReports.map((report, index) => {
            if (report.sctar.id === null) {
              report.sctar.search_client_id = platformId;
              report.sctar.analytics_report_id = report.analytics_reports.id;
              report.sctar.is_enabled = false;
              report.sctar.label = report.analytics_reports.name;
            }
            report.sctar.name = report.analytics_reports.name;
            report.sctar.description = report.analytics_reports.description;
            report.sctar.version = report.analytics_reports.version;
            if (!rs.getAddonsStatus && report.sctar.analytics_report_id == 14) {
              caseDeflectionIndex = index;
            } else {
              return report.sctar;
            }
          });
          rs.analyticsReports = rs.analyticsReports.filter((x) => x);
          settings.enabledObjects = rs.two;

          settings.client = rs.client[0];
          settings.client.vectorSearchEnabled = rs.client[0].vector_search_enabled === 1 ? true : false;
          delete settings.client.vector_search_enabled;
          settings.client.horizontalTab = {
            enabled: rs.client[0].horizontalTabEnabled,
            facet: rs.client[0].horizontalTabFacet,
            order: rs.client[0].horizontalTabOrder
          };
          settings.client.sourceTab = {
            sortBy: rs.client[0].sourceSortBy,
            orderBy: rs.client[0].sourceOrderBy,
            priority: rs.client[0].sourcePriority
          };
          settings.client.summaryPref = {
            maxSummaryLength: rs.client[0].maxSummaryLength >= 0 ? rs.client[0].maxSummaryLength : 250,
            minSummaryLength: rs.client[0].minSummaryLength >= 0 ? rs.client[0].minSummaryLength : 100,
            collapsibleSummary: rs.client[0].collapsibleSummary || 0
          }
          try{
            settings.client.contentTag = JSON.parse(settings.client.contentTag);
          } catch(e){
            settings.client.contentTag =  "{}";
          }
          settings.client.hideAllContentSources = settings.client.hideAllContentSources;
          settings.client.versionResultsEnabled = settings.sources.some((x) =>
            x.objects.some((f) => {
              if (JSON.parse(f.merge_results || "{}").enabled) return true;
            })
          );
          settings.field_mapping = rs.field_mapping;
          settings.deflection_formula = rs.deflection_formula[0] || {};
          settings.analyticsReports = rs.analyticsReports;
          if (rs.slackChannels.length)
            settings.slackChannels = rs.slackChannels;
          settings.customBoosting = rs.customBoosting;
          settings.fieldBoosting = rs.fieldBoosting;
          settings.tenantInfo = rs.tenantInfo;
          settings.keywordBoosting = rs.keywordBoosting;
          settings.intentBoosting = rs.intentBoosting;
          settings.removedSuggestions = rs.getExcludedSuggestionsQuery;
          settings.getBoostingDetails = rs.getBoostingDetails;
          settings.metaDataFields = rs.getMetadataFieldsInObject;
          settings.previewFields = rs.getPreviewFieldsInObject;
          settings.displayFields = rs.getDisplayFields;
          console.log('--------------email:::::',req.headers && req.headers.session && req.headers.session.email);
          settings.neuralSearchEnabledInCurrentSession = configuration.neuralSearchEnabledInCurrentSession || false;
          settings.currentSessionEmail = (req.headers && req.headers.session && req.headers.session.email) || rs.client[0].email;
          settings.client.isIntentBoostingEnabled = rs.client && rs.client.re_ranking ? false : rs.intentBoosting && rs.intentBoosting.length ? !!rs.intentBoosting[0].isBoostingEnabled : false;
          settings.client.isKeywordBoostingEnabled = rs.client && rs.client.re_ranking ? false : rs.getBoostingDetails && rs.getBoostingDetails.length ? !!rs.getBoostingDetails[0].isBoostingEnabled : false;
          settings.client.userEntitlements =
            rs.deflection_formula[0] &&
            rs.deflection_formula[0].user_entitlements
              ? true
              : false;
          //getting content sourse all information
          if (settings.sources && settings.sources.length) {
            settings.sources = settings.sources.filter((x) => x.enabled);
            var taskArr = [];
            for (
              var contentSourseList = 0;
              contentSourseList < settings.sources.length;
              contentSourseList++
            ) {
              taskArr.push(
                commonFunctions.getContentSourceDataById.bind(
                  null,
                  settings.sources[contentSourseList].id,
                  req
                )
              );
            }
            settings.analyticsBoostingData = configuration.keywordBoostingDocsDetails;
            async.parallel(taskArr, (err, result) => {
              console.log("Kafka Settings: ", JSON.stringify(settings));
              if(settings.analyticsBoostingData && settings.analyticsBoostingData.length > 0){
                try {
                  kafkaLib.publishMessage({
                    topic: config.get("kafkaTopic.keywordBoostingTopic"),
                    messages: [
                      {
                        value: JSON.stringify(settings.analyticsBoostingData),
                        key: keyUid
                      },
                    ],
                  });
                  commonFunctions.errorlogger.info(
                    "Update send to analytics "
                  );
                } catch (e) {
                  commonFunctions.errorlogger.info(
                    "Error in update send to analytics ",
                    e
                  );
                }
              }
              settings.contentSourceAuthorization = result;
              // kafka producer
              delete settings.client.custom_css;
              //add tenantId while publishing to other services
              settings.tenantId = req.headers['tenant-id'];
              settings.clusterInfo = settings.tenantInfo.clusterInfo;
              settings.mlClusterInfo = settings.tenantInfo.mlClusterInfo;
              try {
                kafkaLib.publishMessage({
                  topic: config.get("kafkaTopic.searchResult"),
                  messages: [
                    {
                      value: JSON.stringify(settings),
                      key: keyUid
                    },
                  ],
                });
                commonFunctions.errorlogger.info(
                  "search client settings update kafka topic"
                );
              } catch (e) {
                commonFunctions.errorlogger.info(
                  "search client settings update kafka topic error",
                  e
                );
              }
              publishDataToStatusPage(config.get("kafkaTopic.searchResult"), {
                value: JSON.stringify(settings),
                key: keyUid
              });
              publishTenantUidToAuth(settings.client.uid, req.headers['tenant-id']);

              if (configuration.savedDocs) {
                const keywordBoostingDocs = getBoostingDocs(
                  rs.getBoostingDetails,
                  configuration.savedDocs.keyword,
                  configuration.savedDocs.savedIds,
                  ['search_string', 'record_id'],
                  ['auto_boosted', 'search_string', 'boosting_level', 'isBoostingEnabled', 'bypass_filter']
                );
                const intentBoostingDocs = getBoostingDocs(
                  rs.intentBoosting, 
                  configuration.savedDocs.intent, 
                  configuration.savedDocs.savedIntents,
                  ['document_id', 'intent'],
                  ['isBoostingEnabled', 'rank', 'intent', 'bypass_filter']
                );
                const boostingData = {
                  uid: settings.client.uid,
                  tenantId: req.headers['tenant-id'],
                  keywordBoosting: keywordBoostingDocs,
                  intentBoosting: intentBoostingDocs,
                  keywordBoostingEnabled: configuration.savedDocs.keywordBoostingEnabled,
                  intentBoostingEnabled: configuration.savedDocs.intentBoostingEnabled,
                };
                publishBoostingKafka(boostingData);
              }
              cb(null, settings);
            });
          }
        },
      ],
    },
    (error, result) => {
      if (error) {
        commonFunctions.errorlogger.error("error", error);
        cb(error, []);
      } else {
        commonFunctions.errorlogger.info("Updated Settings for search client id: ",platformId);
        cb(null, settings);
      }
    }
  );
};

const getContentSourceData = function (content_source_id, cb) {
  commonFunctions.getContentSourceDataById(
    content_source_id,req,
    function (err, resultData) {
      if (err) cb(err, []);
      else cb([], resultData);
    }
  );
};

module.exports.router = router;
module.exports.getSearchClientSettingsViaKafka =  getSearchClientSettingsViaKafka;
