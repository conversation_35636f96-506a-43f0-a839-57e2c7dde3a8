const path = require('path');
const configPath = path.join(__dirname, '../../config');
process.env.NODE_CONFIG_DIR = configPath;
const config = require("config");
const logger = require('tracer').console({
    level: 'info',
    format: "{{timestamp}} <{{title}}> {{message}} (in {{file}}:{{line}}) @crawlinglogs@@"
});

const crontab = require('crontab');
const fs = require('fs');
const filePath = path.resolve(__dirname, '../../synonyms/cron-backup.txt');
const alertUrl = config.get('gchatUrls.migration');
const alertGchatLib = require('../../Lib/alert-gchat');
fs.writeFileSync(filePath, '');

const createCron = (crontab) => {
    crontab.create(`find /home/<USER>/logs/ -mtime +5 -type f -name '*.log' -delete`, '@daily', 'versioningUpdateLogs_dummyurl');
    logger.info('cron has been created for log file deletion');
}

const backupAndDeleteCrons = () => {
    crontab.load((err, crontab) => {
        if (!err) {
            try {
                const jobs = crontab.jobs({});
                jobs.forEach(job => {
                    fs.appendFileSync(filePath, `${job.toString()}\r\n`);
                    crontab.remove(job);
                });
                logger.info('All jobs have been removed');
                createCron(crontab);
                crontab.save();
                alertGchatLib.alertGoogleChat({
                    url: alertUrl,
                    message: `*m23 crons deletion from crontab has been Successful - ${config.get('adminURL')}*`,
                    event: "Cron Job creation",
                });
            } catch (error) {
                logger.error('error removing crons', error);
                alertGchatLib.alertGoogleChat({
                    url: alertUrl,
                    message: `*m23 crons deletion from crontab has been Failed - ${config.get('adminURL')}*`,
                    event: "Cron Job creation",
                });
            }
        } else {
            logger.error('error removing crons', err);
            alertGchatLib.alertGoogleChat({
                url: alertUrl,
                message: `*m23 crons deletion from crontab has been Failed - ${config.get('adminURL')}*`,
                event: "Cron Job creation",
            });
        }
    })
}




if (process.argv && process.argv.length === 2) {
    backupAndDeleteCrons();
}
