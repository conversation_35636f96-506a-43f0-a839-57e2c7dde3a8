/**
 * Created by man<PERSON><PERSON> on 13/12/17.
 */

var express = require("express");
var router = express.Router();
var async = require("async");
var request = require("request");
var fs = require("fs");
const elastic = require("elasticsearch");
var commonFunctions = require("./../../utils/commonFunctions");
var searchResultKafkaConfig = require("./searchResultKafkaConfig");
const cron = require("./../../utils/Cron");
const kafkaLib = require("../../utils/kafka/kafka-lib");
const { spawn } = require("child_process");
var path = require("path");
const md5 = require('md5');


const { cloudFrontURL, isCloudFrontSupported, searchClientPrefix } =  require('config').get('cloudStorageConfig');


const JWT = require('jsonwebtoken');
const { getAccessTokenFromTenantId, fetchTenantInfoFromUid } = require('auth-middleware');
const { tenantSqlConnection  } = require('../../auth/sqlConnection');
var config = require("config");
const { jsErrors, SEARCH_TYPE } = require('./../../constants/constants');
const tuningService = require('../../routes/searchClient/service/tuning-service');
const kafkaStatusLib = require("./../../utils/kafka-status-page/kafka-lib");
const { getTenantsRateLimitInfo } = require("auth-middleware");
const { promisify } = require('util')
const requestPromise = promisify(request);

const Queue = require("bull");

const connectQueue = new Queue("first-queue", {
  redis: {
      port: config.get("redis.redisPort"),
      host: config.get("redis.redisHost"),
  }
});
const { osMigration, osMigrationDays } = config.get('openSearch');

connectQueue.process('__default__', 1, path.join(__dirname, '../../init-bootstrap/updateMappingScript.js'));

const publishDeleteSCEvent = (tenantId, scUid, updatedBy='registerList', regionStatus = true, isDisabled = false) => {
    let data = {
      task: "deletePlatform",
      updatedBy,
      searchClientRegionStatus: regionStatus,
      data: { 
        location: `resources/search_clients_custom/${scUid}`, 
        tenantId: tenantId,
        environment: config.get('myAccountInstanceVariable'),
      },
      tenantId : tenantId,
      isDisabled
    };
    try {
      kafkaLib.publishMessage({
        topic: config.get("kafkaTopic.searchClientTopic"),
        messages: [
          {
            value: JSON.stringify(data),
            key: scUid
          },
        ],
      });
      console.log(
        "delete instruction is sent to Search-client Service by Kafka "
      );
    } catch (e) {
      console.log(
        "Error while sending instruction to Search-client Service by Kafka => ",
        e
      );
    }
}



let totalOperations = 0;
let completedOperations = 0;

const deletePlatform = async (req, res) => {
  try {
    if (req.body.clonnedScLength && totalOperations === 0) {
      totalOperations = req.body.clonnedScLength;
    }

    const platformId = req.body.client.id;
    const uid = req.body.client.uid;
    
    // Validate access and permissions
    const scCreated = await getSCCreatedDate(platformId, req);
    const scOperations = await checkOSMigration(scCreated, req);
    if (scOperations) {
      return res.status(401).send({ flag: 401, message: "Unauthorized" });
    }
    let allowedFurther
    if(!req.query.abTestCron){
       allowedFurther = await accessCheck(
        req.headers.session.email,
        req.headers.session.roleId,
        platformId,
        req
      );
    }else{
      allowedFurther = true;
    }
    if (!allowedFurther) {
      return res.status(401).send({ flag: 401, message: "Unauthorized" });
    }

    // Execute main deletion operations
    const results = await new Promise((resolve, reject) => {
      async.auto(
        {
          get: (cb) => {
            connection[req.headers['tenant-id']].execute.query(
              `SELECT * FROM search_clients WHERE id=?`,
              [platformId],
              (e, r, f) => {
                if (e) cb(e);
                else if (r.length === 0) cb("Not found");
                else cb(null, r[0]);
              }
            );
          },
          deleteCronForBot: [
            "get",
            (result, cb) => {
              let sql = "SELECT id from `community_helper_bot` WHERE `search_client_id` = ?";
              connection[req.headers['tenant-id']].execute.query(sql, [platformId], function (error, rows) {
                if (rows[0] && rows[0].id) {
                  cron.deleteCron("bot_" + rows[0].id, function (result) {
                    commonFunctions.errorlogger.info(`Cron deleted for bot ${rows[0].id}`);
                    cb(null, "bot deleted");
                  });
                } else cb(null, "no bot assigned");
              });
            },
          ],
          deleteEntry: [
            "get",
            "deleteCronForBot",
            (result, cb) => {
            // First check for AB test association
            const checkABTestQuery = `
              SELECT * FROM ab_test 
              WHERE sc_uid = ?
            `;
        
            connection[req.headers["tenant-id"]].execute.query(checkABTestQuery, [result.get.uid], (error, rows) => {
              if (error) {
                commonFunctions.errorlogger.error("Error querying ab_test table:", error);
                return cb(error.message || "Failed to check AB Test association.");
              }
        
              // Handle AB test deletion if necessary
              const handleABTestDeletion = () => {
                if (rows.length > 0) {
                  // remove it after QA
                  commonFunctions.errorlogger.info("AB Test association found, deleting...");
                  return commonFunctions.deleteABTestData(req.headers["tenant-id"], result.get.uid, true)
                    .then(() => {
                      commonFunctions.errorlogger.info("AB Test data deleted successfully.");

                      return Promise.resolve();
                    })
                    .catch((err) => {
                      commonFunctions.errorlogger.error("Error deleting AB Test data", err);
                      throw new Error(err.message || "Failed to delete AB Test");
                    });
                }
                commonFunctions.errorlogger.info("No AB Test associated with the given UID.");
                return Promise.resolve();
              };
        
              // After AB test is handled, proceed with entry deletion
              handleABTestDeletion()
                .then(() => {
                  const deleteSQL = "DELETE FROM `search_clients` WHERE `search_clients`.`id` = ?";
                  connection[req.headers['tenant-id']].execute.query(deleteSQL, [platformId], (deleteError, deleteResult) => {
                    if (deleteError) {
                      commonFunctions.errorlogger.error("Error deleting search client:", deleteError);
                      return cb(deleteError.message || "Failed to delete search client");
                    }
                    cb(null, "Entry deleted successfully");
                  });
                })
                .catch((err) => {
                  cb(err);
                });
            });
          }
        ],
        deleteAnalytics: [
          "get",
          (result, cb) => {
            cb(null, "analytics not deleted");
          },
        ],
          deleteAnalyticslabels: [
            "get",
            (result, cb) => {
              let sql = "DELETE from analytics_cd_labels where uid = ?";
              connection[req.headers['tenant-id']].execute.query(sql, [result.get.uid], cb);
            },
          ],
        },
        (error, results) => {
          if (error) {
            reject(error);
          } else {
            if (!req.body.clonnedScLength) {
              // Original functionality
              let data = {
                event: "delete",
                client: req.body.client,
                tenantId: req.headers['tenant-id']
              }
        
              try {
                kafkaLib.publishMessage({
                  topic: config.get("kafkaTopic.searchResult"),
                  messages: [
                    {
                      value: JSON.stringify(data),
                      key: uid
                    },
                  ],
                });
                commonFunctions.errorlogger.info(
                  "delete instruction is sent to Analytics Service by Kafka "
                );
              } catch (e) {
                commonFunctions.errorlogger.error(
                  "Error while sending instruction to Analytics Service by Kafka => ",
                  e
                );
              }
        
              if (results.get.DownloadFilePath) {
                let data = {
                  task: "deletePlatform",
                  updatedBy: req.headers.session,
                  searchClientRegionStatus: results.get.s3_supported,
                  data: { 
                    location: results.get.DownloadFilePath, 
                    tenantId: req.headers['tenant-id'],
                    environment: config.get('myAccountInstanceVariable')
                  },
                  tenantId: req.headers['tenant-id']
                };
                try {
                  publishDeleteSCEvent(req.headers['tenant-id'], uid, req.headers.session, results.get.s3_supported);
                  kafkaLib.publishMessage({
                    topic: config.get("kafkaTopic.searchClientTopic"),
                    messages: [
                      {
                        value: JSON.stringify(data),
                        key: uid
                      },
                    ],
                  });
                  if(config.get('statusPageService.kafka.host') !== config.get('kafkaTopic.host')){
                    kafkaStatusLib.publishMessage({
                      topic: config.get("statusPageService.kafka.searchClientTopic"),
                      messages: [
                        {
                          value: JSON.stringify(data),
                          key: uid
                        },
                      ],
                    });
                  }
                  commonFunctions.errorlogger.info(
                    "delete instruction is sent to Search-client Service by Kafka "
                  );
        
                  let dataObj = {
                    data: {
                      uid
                    }
                  }
                  // Publishing uid to delete tenant-uid relation from auth service
                  kafkaLib.publishMessage({
                    topic: config.get("kafkaTopic.tenantUidTopic"),
                    messages: [
                      {
                        value: JSON.stringify(dataObj),
                        key: uid
                      },
                    ],
                  });
                  commonFunctions.errorlogger.info(
                    "KAFKA TOPIC PUBLISH >>> DELETE ", JSON.stringify(dataObj)
                  );
                } catch (e) {
                  commonFunctions.errorlogger.error(
                    "Error while sending instruction to Search-client Service by Kafka => ",
                    e
                  );
                }
              }
              resolve(results);
            } else {
              // Handle batch operations with Promise.all
              const data = {
                event: "delete",
                client: results.get,
                tenantId: req.headers['tenant-id']
              };
        
              Promise.all([
                kafkaLib.publishMessage({
                  topic: config.get("kafkaTopic.searchResult"),
                  messages: [{ value: JSON.stringify(data), key: uid }],
                })
                  .then(() => commonFunctions.errorlogger.info("delete instruction is sent to Analytics Service by Kafka"))
                  .catch(e => commonFunctions.errorlogger.error("Error while sending instruction to Analytics Service by Kafka =>", e)),
        
                results.get.DownloadFilePath ? (async () => {
                  const fileData = {
                    task: "deletePlatform",
                    updatedBy: req.headers.session,
                    searchClientRegionStatus: results.get.s3_supported,
                    data: {
                      location: results.get.DownloadFilePath,
                      tenantId: req.headers['tenant-id'],
                      environment: config.get('myAccountInstanceVariable')
                    },
                    tenantId: req.headers['tenant-id']
                  };
        
                  try {
                    await publishDeleteSCEvent(
                      req.headers['tenant-id'],
                      uid,
                      req.headers.session,
                      results.get.s3_supported
                    );
        
                    await kafkaLib.publishMessage({
                      topic: config.get("kafkaTopic.searchClientTopic"),
                      messages: [{ value: JSON.stringify(fileData), key: uid }],
                    });
        
                    if (config.get('statusPageService.kafka.host') !== config.get('kafkaTopic.host')) {
                      await kafkaStatusLib.publishMessage({
                        topic: config.get("statusPageService.kafka.searchClientTopic"),
                        messages: [{ value: JSON.stringify(fileData), key: uid }],
                      });
                    }
        
                    await kafkaLib.publishMessage({
                      topic: config.get("kafkaTopic.tenantUidTopic"),
                      messages: [{ value: JSON.stringify({ data: { uid } }), key: uid }],
                    });
                  } catch (e) {
                    commonFunctions.errorlogger.error("Error in file cleanup and Kafka messages:", e);
                  }
                })() : Promise.resolve()
              ])
                .finally(() => {
                  completedOperations++;
                  if (completedOperations === totalOperations) {
                    totalOperations = 0;
                    completedOperations = 0;
                    resolve({ flag: 200, message: "All Cloned Search clients deleted Successfully." });
                  } else {
                    resolve(results);
                  }
                });
            }
          }
        }
      );
    });

       // Send response if not part of batch operation
       if (!req.body.clonnedScLength) {
        res.send({ flag: 200, message: "Deleted" });
      }

  } catch (error) {
    commonFunctions.errorlogger.error('Delete platform error:', error);
    if (req.body.clonnedScLength) {
        // Reset counters even on error
        totalOperations = 0;
        completedOperations = 0;
    }
    res.status(400).send({
      flag: commonFunctions.constants.SOMETHING_WRONG,
      message: "Unexpected error occurred",
    });
  }
};

router.post("/deletePlatform", async (req, res, next) => {
  await deletePlatform(req, res);
});

router.get("/getSearchClient", async (req, res, next) => {
  try {
    const platformId = req.query.platformId;
    const result = await getSearchClientData(platformId, req);
    res.send(result);
  } catch (error) {
    next(error);
  }
});


async function getSearchClientData(platformId, req) {
  try {
    const scCreated = await getSCCreatedDate(platformId, req);
    const scOperations = await checkOSMigration(scCreated, req);
    if (scOperations) {
      return { flag: 401, message: "Unauthorized" };
    }

    const tempArr = [platformId];
    const isArrayValid = commonFunctions.checkArray(tempArr);
    if (!isArrayValid) {
      return {
        status: commonFunctions.constants.responseFlags.INVALID_ACCESS_TOKEN,
        message: "MISSING ARGUMENT",
      };
    }
    return new Promise((resolve, reject) => {
      getSearchClient(platformId, req, (error, result) => {
        if (error) {
          reject({ flag: 403, message: "Failed", error });
        } else if (result) {
          resolve(result);
        } else {
          resolve({ flag: 403, message: "Failed" });
        }
      });
    });
  } catch (error) {
    commonFunctions.errorlogger.error("getSearchClientData error:", error);
    return { flag: 500, message: "Internal Server Error", error };
  }
}

router.post("/updateSearchClient", async (req, res, next) => {
  let allowedFurther = await accessCheck(
    req.headers.session.email,
    req.headers.session.roleId,
    req.body.client.id,
    req
  );
  if (!allowedFurther) {
    res.send({ flag: 401, message: "Unauthorized" });
  } else {
    req.body.hasAbTestDataStatus = false; 
      let checkUID = await getUid(req);
      let abTestStatusForUid = await commonFunctions.getAbTestStatus(req, req.body.client.uid);
      const hasAbTest = abTestStatusForUid.data !== null;
      req.body.hasAbTestDataStatus = hasAbTest;
      if(checkUID) {
        updateSearchClient(req.body ,req, async function (error, result) {
          if (result) {
            if(req.body.updatedContentSource &&  config.get("searchService.formulaFieldSearch") === true) {
                const job = await connectQueue.add({ 
                  uid: req.body.client.uid, 
                  file: 'logs/updateMapping_' + req.body.client.uid.slice(0,6) + "_" + (new Date()).toISOString() + '.log' 
                });
                const result = await job.finished();
            }
          const checkAbTestSql = `
                  SELECT * 
                  FROM search_clients 
                  WHERE ab_test_parent = ?
                `; 

          
          connection[req.headers['tenant-id']].execute.query(
            checkAbTestSql,
            [req.body.client.uid],
            async (abTestError, abTestResults) => {
              if (abTestError) {
                commonFunctions.errorlogger.error("AB Test check error:", abTestError);
                return res.send({ flag: 500, message: "Internal Server Error" });
              }
              let platformIds = [];
              if (abTestResults && abTestResults.length > 0) {
                commonFunctions.errorlogger.info("Found AB test children:", abTestResults.length);
                commonFunctions.errorlogger.info("AB Test children details:", JSON.stringify(abTestResults, null, 2));
                platformIds = abTestResults.map(row => row.id);
                commonFunctions.errorlogger.info("Extracted platform IDs:", platformIds);
              } else {
                commonFunctions.errorlogger.info("No AB test children found");
              }
              
              try {
                let allSCData = [];
                if(platformIds && platformIds.length != 0){
                  commonFunctions.errorlogger.info("Processing data for", platformIds.length, "platform IDs");
                  for (const id of platformIds) {
                    req.query.platformId = id;
                    const scData = await getSearchClientData(id, req);
                    if (scData) {
                      allSCData.push(scData);
                    }
                  }
                  commonFunctions.errorlogger.info("Completed processing all platform IDs");
                } else {
                  commonFunctions.errorlogger.info("No platform IDs to process");
                }

                // commonFunctions.errorlogger.info("All SC Data collection complete. Items:", allSCData.length);
                // commonFunctions.errorlogger.info("AllSCData =====:", allSCData);
                // commonFunctions.errorlogger.info("AllSCData summary:", allSCData.map(sc => ({id: sc.id, uid: sc.uid})));
                
                // Process all AB test clients with Kafka before sending response
                if (config.get("kafkaTopic.enable") && allSCData && allSCData.length > 0) {
                  const kafkaPromises = [];
                  const kafkaResults = [];
                  
                  const getKafkaSettingsPromise = (configObj, req) => {
                    return new Promise((resolve, reject) => {
                      searchResultKafkaConfig.getSearchClientSettingsViaKafka(
                        configObj, req, (err, searchConfig) => {
                          if (!err && req.body.client.isDisabled) {
                            sendDeleteKafkaMessages( req, req.body.client.uid );
                          }
                          if (err) {
                            commonFunctions.errorlogger.error("Kafka error for platform ID", configObj.platformId, ":", err);
                            reject(err);
                          } else {
                            resolve(searchConfig);
                          }
                        }
                      );
                    });
                  };
                  
                  commonFunctions.errorlogger.info("Processing Kafka for each AB test client");
                  // Processing Kafka for each AB test client
                    for (const scData of allSCData) {
                      
                      // Extract ID and UID from the correct location in the object structure
                      const clientId = scData && scData.client && scData.client.id;
                      const clientUid = scData && scData.client && scData.client.uid;
                      
                      
                      if (clientId && clientUid) {
                        const configObj = {
                          platformId: clientId,
                          uid: clientUid,
                        };
                        
                        kafkaPromises.push(
                          getKafkaSettingsPromise(configObj, req)
                            .then(searchConfig => {
                              kafkaResults.push({
                                platformId: clientId,
                                kafkaConfig: searchConfig
                              });
                            })
                            .catch(err => {
                              kafkaResults.push({
                                platformId: clientId,
                                error: "Failed to get Kafka settings"
                              });
                            })
                        );
                      } else {
                        commonFunctions.errorlogger.info("Invalid search client data structure - missing id or uid in client object:", 
                          JSON.stringify({
                            hasClient: Boolean(scData && scData.client),
                            clientIdExists: Boolean(clientId),
                            clientUidExists: Boolean(clientUid)
                          })
                        );
                      }
                    }
                  
                  const mainConfigObj = {
                    platformId: req.body.client.id,
                    uid: req.body.client.uid,
                  };
                  
                  kafkaPromises.push(
                    getKafkaSettingsPromise(mainConfigObj, req)
                      .then(searchConfig => {
                        Promise.all(kafkaPromises).then(() => {
                          if (req.body.client.isDisabled) {
                            sendDeleteKafkaMessages( req, req.body.client.uid );
                          }
                          res.send({
                            searchClientSettings: result,
                            kafkaObject: searchConfig,
                            abTestData: allSCData,
                            abTestKafkaConfigs: kafkaResults
                          });
                        }).catch(err => {
                          commonFunctions.errorlogger.error("Error in Promise.all for Kafka promises:", err);
                          res.send({
                            searchClientSettings: result,
                            kafkaObject: searchConfig,
                            abTestData: allSCData,
                            abTestKafkaConfigs: kafkaResults,
                            promiseAllError: "Error resolving all Kafka promises"
                          });
                        });
                      })
                      .catch(err => {
                        commonFunctions.errorlogger.error("Main Kafka promise error:", err);
                        Promise.all(kafkaPromises).then(() => {
                          res.send({
                            searchClientSettings: result,
                            kafkaObject: null,
                            kafkaError: "Failed to get main Kafka settings",
                            abTestData: allSCData,
                            abTestKafkaConfigs: kafkaResults
                          });
                        }).catch(promiseErr => {
                          res.send({
                            searchClientSettings: result,
                            kafkaObject: null,
                            kafkaError: "Failed to get main Kafka settings",
                            promiseAllError: "Error resolving all Kafka promises",
                            abTestData: allSCData,
                            abTestKafkaConfigs: kafkaResults
                          });
                        });
                      })
                  );
                  
                } else if (config.get("kafkaTopic.enable") && req.body.client && req.body.client.uid && req.body.client.id) {
                  let configObj = {
                    platformId: req.body.client.id,
                    uid: req.body.client.uid,
                  };
                  
                  searchResultKafkaConfig.getSearchClientSettingsViaKafka(
                    configObj, req,
                    function (err, searchConfig) {
                      if (err) {
                        commonFunctions.errorlogger.error("Original Kafka flow error:", err);
                      } else {
                        commonFunctions.errorlogger.info("Original Kafka flow success");
                      }
                      if (req.body.client.isDisabled) {
                        sendDeleteKafkaMessages( req, req.body.client.uid );
                      }
                      res.send({
                        searchClientSettings: result,
                        kafkaObject: searchConfig,
                        abTestData: allSCData
                      });
                    }
                  );
                } else {
                  if (req.body.client.isDisabled) {
                    sendDeleteKafkaMessages( req, req.body.client.uid );
                  }
                  res.send({
                    ...result,
                    abTestData: allSCData
                  });
                }
              } catch (error) {
                commonFunctions.errorlogger.error("Error processing AB test data:", error);
                res.send({ flag: 500, message: "Error processing AB test data" });
              }
            }
          );
          
          commonFunctions.deleteGptRulesFromCache(req, req.body.client.uid);
        } else {
          res.send({ flag: 403, message: "Failed", error: error });
        }
      });
    } else {
      if (error && (error.sqlMessage || (error.stack && jsErrors.includes(error.stack.split(':')[0])))) {
        error = {
          message: 'Internal Server Error'
        }
      }
      res.send({ flag: 403, message: "Failed", error: error });
    }
  }
});

const getUid = function (req) {
  return new Promise((resolve, reject) => {
    connection[req.headers['tenant-id']].execute.query(`SELECT * from search_clients WHERE id=?`,[req.body.client.id], (error, result) => {
      if(error || (result && result.length === 0)) {
        commonFunctions.errorlogger.error(error);
        resolve(false);
      } else{
        req.body.client.uid = result[0].uid;
        req.body.client.DownloadFilePath = result[0].DownloadFilePath;
        resolve(true);
      }
    });
  });
}

// get saved sharing options for salesforce search client
router.post("/getResultActionsFromDb", function (req, res, next) {
  var sql =
    "Select result_action_id, search_client_type_id,name from result_actions where search_client_type_id = ?";
  connection[req.headers['tenant-id']].execute.query(sql, [7], function (err, response) {
    if (err) {
      console.log("error", err);
      res.send({ err: err });
    } else {
      var query =
        "Select  search_client_id, result_action_id, status, merge from search_client_actions where search_client_id = ? AND selected_object =? AND content_source_label =?";
      connection[req.headers['tenant-id']].execute.query(
        query,
        [
          req.body.searchClientId,
          req.body.selectedObjectName,
          req.body.contentSourceLabel,
        ],
        function (err, result) {
          if (err) {
            console.log("error", err);
            res.send({ err: err });
          } else {
            res.send({
              result_actions: response,
              search_client_actions: result,
            });
          }
        }
      );
    }
  });
});

router.post("/updateShareResults", function (req, res, next) {
  async.auto(
    {
      getResultAction: (cb) => {
        var query =
          "Select result_action_id, search_client_type_id,name from result_actions where search_client_type_id = ?";
        connection[req.headers['tenant-id']].execute.query(query, [7], function (err, records) {
          if (err) {
            cb(err, null);
          } else {
            cb(null, records);
          }
        });
      },
      getExistingEntries: [
        "getResultAction",
        (resultFromAbove, cb) => {
          var query =
            "Select  search_client_id, result_action_id, status, merge,content_source_label,selected_object from search_client_actions where search_client_id = ? ";
          connection[req.headers['tenant-id']].execute.query(
            query,
            [req.body.searchClientId],
            function (err, result) {
              if (err) {
                cb(err, null);
              } else {
                cb(null, result);
              }
            }
          );
        },
      ],
    },
    function (err, response) {
      let records = response.getResultAction;
      let result = response.getExistingEntries;
      for (let i = 0; i < req.body.contentSourceArray.length; i++) {
        for (
          let x = 0;
          x < req.body.contentSourceArray[i].objects.length;
          x++
        ) {
          let flag = true;
          for (let j = 0; j < result.length; j++) {
            if (
              req.body.contentSourceArray[i].contentSourceLabel ==
                result[j].content_source_label &&
              req.body.contentSourceArray[i].objects[x].name ==
                result[j].selected_object
            ) {
              flag = false;
            }
          }
          if (flag) {
            for (let k = 0; k < records.length; k++) {
              if (
                records[k].result_action_id == 3 &&
                req.body.contentSourceArray[i].objects[x].label.includes(
                  "knowledge"
                ) == false
              ) {
                var sqlQuery =
                  "Insert into search_client_actions( search_client_id, result_action_id, status, merge,selected_object,content_source_label) values(?,?,?,?,?,?) ";
                connection[req.headers['tenant-id']].execute.query(
                  sqlQuery,
                  [
                    req.body.searchClientId,
                    records[k].result_action_id,
                    0,
                    0,
                    req.body.contentSourceArray[i].objects[x].name,
                    req.body.contentSourceArray[i].contentSourceLabel,
                  ],
                  function (err, rows) {
                    if (err) {
                      res.send({ error: err });
                    }
                  }
                );
              } else {
                var sqlQuery1 =
                  "Insert into search_client_actions( search_client_id, result_action_id, status, merge,selected_object,content_source_label) values(?,?,?,?,?,?) ON DUPLICATE KEY UPDATE search_client_id = values(search_client_id),result_action_id = values(result_action_id),status = values(status),merge = values(merge)";
                connection[req.headers['tenant-id']].execute.query(
                  sqlQuery1,
                  [
                    req.body.searchClientId,
                    records[k].result_action_id,
                    1,
                    0,
                    req.body.contentSourceArray[i].objects[x].name,
                    req.body.contentSourceArray[i].contentSourceLabel,
                  ],
                  function (err, rows) {
                    if (err) {
                      res.send({ error: err });
                    }
                  }
                );
              }
            }
          }
        }
      }
      res.send({ status: "ok" });
    }
  );
});

// Save sharing options for salesforce search client
router.post("/saveShareResultOptions", function (req, res, next) {
  async.auto(
    {
      getExistingResultActions: (cb) => {
        var query =
          "Select result_action_id, search_client_type_id,name from result_actions where search_client_type_id = ?";
        connection[req.headers['tenant-id']].execute.query(query, [7], function (err, result) {
          if (err) {
            cb(err, null);
          } else {
            cb(null, result);
          }
        });
      },
      getExistingSharingOptions: [
        "getExistingResultActions",
        (dataFromAbove, cb) => {
          // console.log("dataFromAbove", dataFromAbove);
          var query =
            "Select  search_client_id, result_action_id, status, merge,selected_object from search_client_actions where search_client_id = ?";
          connection[req.headers['tenant-id']].execute.query(query, [req.body.uidData], function (err, result) {
            if (err) {
              cb(err, null);
            } else {
              cb(null, result);
            }
          });
        },
      ],
    },
    (error, results) => {
      if (error) {
        res.status(401).send(error);
      } else {
        // console.log("results", results);
        let records = results.getExistingResultActions;
        let existing_entries = results.getExistingSharingOptions;
        // console.log(error, results);
        let mergeValues = req.body.sharingOption.SingleClick;
        for (let i = 0; i < records.length; i++) {
          if (records[i].name == "Email") {
            records[i].value = req.body.sharingOption.email;
          }
          if (records[i].name == "Case comment") {
            records[i].value = req.body.sharingOption.caseComment;
          }
          if (records[i].name == "Attach article to case") {
            records[i].value = req.body.sharingOption.attachToCase;
          }

          let flag = true;
          for (let j = 0; j < existing_entries.length; j++) {
            if (
              req.body.uidData == existing_entries[j].search_client_id &&
              records[i].result_action_id ==
                existing_entries[j].result_action_id &&
              req.body.selectedObject == existing_entries[j].selected_object
            ) {
              var sql =
                "Update search_client_actions set status =?, merge=? where search_client_id =? AND result_action_id =? AND selected_object =? AND content_source_label =?";
              connection[req.headers['tenant-id']].execute.query(
                sql,
                [
                  records[i].value,
                  mergeValues,
                  req.body.uidData,
                  records[i].result_action_id,
                  req.body.selectedObject,
                  req.body.content_source_label,
                ],
                function (err, result) {
                  if (err) {
                    res.send({ error: err });
                  }
                }
              );
              flag = false;
              break;
            }
          }
          if (flag) {
            var sqlQuery =
              "Insert into search_client_actions( search_client_id, result_action_id, status, merge,selected_object,content_source_label) values(?,?,?,?,?,?) ON DUPLICATE KEY UPDATE search_client_id = values(search_client_id),result_action_id = values(result_action_id),status = values(status),merge = values(merge)";
            connection[req.headers['tenant-id']].execute.query(
              sqlQuery,
              [
                req.body.uidData,
                records[i].result_action_id,
                records[i].value,
                mergeValues,
                req.body.selectedObject,
                req.body.content_source_label,
              ],
              function (err, rows) {
                if (err) {
                  res.send({ error: err });
                }
              }
            );
          }
        }
        res.send({ result: "Success" });
      }
    }
  );
});

router.post("/addPlatform", async function (req, res, next) {
  let platform = req.body.platform;
  platform["email"] = req.headers.session.email;
  var validateHtmlrequest = checkobjectforhtml(platform);
  // Add dataRetention value before creating a new Search Client
  let relevancy = JSON.parse(platform.relevancy);
  relevancy['relevancyScores']['dataRetention'] = config.get('relevanceScore.dataRetention');
  platform.relevancy = JSON.stringify(relevancy);
  // Early exit in case search client same name check fails
  const sameSCName = await sameNameCheck(platform.name, req.headers['tenant-id']);
  if(sameSCName) {
    commonFunctions.errorlogger.info('Search client same name check failed!');
    return res.send({ flag: 403, message: "Search client same name check failed!" });
  }
  if (validateHtmlrequest == true) {
    platform.customUID = await checkCustomUID(platform.customUID, req);
    if(platform.customUID === 'UID already exists') {
      return res.send({ flag: 200, message: 'UID already exists' });
    }
    addSearchClient(platform, req, res, (error, result) => {
      commonFunctions.errorlogger.info(result);
      if (result) {
        analyticsKafka(result.uid, req);
        res.send({ flag: 200, uid : result.uid, message: "Done" });
      } else {
        res.send({ flag: 403, message: "Failed" });
      }
    });
  } else {
    res.send({ flag: 403, message: "Only string value is allowed" });
  }
});

const sendDeleteKafkaMessages = (req, uid) => {

  connection[req.headers['tenant-id']].execute.query(
    `SELECT * FROM search_clients WHERE uid = ?`,
    [uid],
    (error, results) => {
      if (error || results.length === 0) {
        console.log("Error fetching search client data for deletion:", error || "Not found");
        return;
      }

      let searchClientData = results[0];
      let data = {
        event: "delete",
        client: searchClientData,
        tenantId: req.headers['tenant-id']
      };

      try {
        kafkaLib.publishMessage({
          topic: config.get("kafkaTopic.searchResult"),
          messages: [{ value: JSON.stringify(data), key: uid }],
        });

        if (searchClientData.DownloadFilePath) {
          let dataForSC = {
            task: "deletePlatform",
            updatedBy: req.headers.session,
            searchClientRegionStatus: searchClientData.s3_supported,
            data: {
              location: searchClientData.DownloadFilePath,
              tenantId: req.headers['tenant-id'],
              environment: config.get('myAccountInstanceVariable')
            },
            tenantId: req.headers['tenant-id'],
            isDisabled: true
          };

          publishDeleteSCEvent(req.headers['tenant-id'], uid, req.headers.session, searchClientData.s3_supported, isDisabled=true);

          kafkaLib.publishMessage({
            topic: config.get("kafkaTopic.searchClientTopic"),
            messages: [{ value: JSON.stringify(dataForSC), key: uid }],
          });

          if (config.get('statusPageService.kafka.host') !== config.get('kafkaTopic.host')) {
            kafkaStatusLib.publishMessage({
              topic: config.get("statusPageService.kafka.searchClientTopic"),
              messages: [{ value: JSON.stringify(dataForSC), key: uid }],
            });
          }

          let authData = {
            data: { uid }
          };

          kafkaLib.publishMessage({
            topic: config.get("kafkaTopic.tenantUidTopic"),
            messages: [{ value: JSON.stringify(authData), key: uid }],
          });
        }
      } catch (e) {
        console.log("Error while sending delete Kafka messages:", e);
      }
    }
  );
};

const checkCustomUID = function (uid, req) {
  // UID => Valid / UID already exists , Invalid UID
  return new Promise((resolve, reject) => {

    // UID regex
    const uidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$/i

    // Continue 
    if(!uid) return resolve(false);

    // Continue with failure
    if(!uidRegex.test(uid)) return resolve('Invalid UID');

    connection[req.headers['tenant-id']].execute.query("SELECT uid from search_clients WHERE uid = ?", [uid], function (error, result) {
      if(result){
        return resolve(result.length ? 'UID already exists' : uid);
      }
      error && commonFunctions.errorlogger.error("error", error);
      return resolve(false);
    });
  });
}

function checkobjectforhtml(value) {
  for (var key in value) {
    var htmlRegex = new RegExp("</?[^>]+(>|$)");
    var check_htmlflag = true;
    if (htmlRegex.test(value[key]) == true) {
      check_htmlflag = false;
      break;
    } else {
      check_htmlflag = true;
    }
  }
  return check_htmlflag;
}

router.get("/migrated-sc-from-sbox", function (req, res) {
  getMigratedSCFromSandbox(req, (data) => {
    res.send(data);
  });
});

router.get("/backup-files/count", function (req, res) {
  var sql = `select COUNT(*) from backup_files where uid_prod = ?`;

  connection[req.headers['tenant-id']].execute.query(sql, [req.query.uid], (err, rows) => {
    if (!err) {
      res.send(rows);
    } else {
      res.send(err);;
    }
  });
});

router.get("/backup-files/uid", function (req, res) {
  var sql = `SELECT uid_prod from backup_files bf group by uid_prod`;

  connection[req.headers['tenant-id']].execute.query(sql, [req.query.uid], (err, rows) => {
    if (!err) {
      res.send(rows);
    } else {
      res.send(err);;
    }
  });
});

router.get('/sbox-prod-mapping/exists', function(req, res){
  let sql;
  const instanceUrl = config.get('adminURL');
  const instanceType = config.get('instanceType');
  const tenantId = req.headers['tenant-id'];
  
  sql = 'select COUNT(*) from suauth.sbox_to_prod_mapping where current_instance_url = ? and current_tenant_id = ?'

  connection[req.headers['tenant-id']].execute.query(sql, [instanceUrl, tenantId], (err, rows) => {
    if (!err) {
      res.send(rows);
    } else {
      res.send(err);
    }
  });
})

//on Production
const getMigratedSCFromSandbox = async (req, callback) => {
  var sql = `select * from migration_requests where is_migrated = 0`;

  connection[req.headers['tenant-id']].execute.query(sql, [req.query.contentSourceId], (err, rows) => {
    if (!err) {
     
      callback({
        flag: commonFunctions.constants.SUCCESS,
        message: rows,
      });
    } else {
      callback({
        flag: commonFunctions.constants.SOMETHING_WRONG,
        message: "Something went wrong",
      });
    }
  });
};

router.get("/getSearchClientByCsId", function (req, res) {
  getSearchClientByContentSourceTypeId(req, (data) => {
    res.send(data);
  });
});

const getSearchClientByContentSourceTypeId = async (req, callback) => {
  var sql = `
  select
    sc.id,
    sc.name,
    sc.search_client_type_id,
    sc.uid
  from
    search_clients sc
  left join search_clients_to_content_objects sctco
    on sc.id = sctco.search_client_id
  left join content_source_objects cso
    on cso.id = sctco.content_source_object_id
  left join content_sources cs 
    on cso.content_source_id = cs.id 
  WHERE 
    cs.content_source_type_id = ?
  GROUP BY
      sc.id, sc.name, sc.label, sc.search_client_type_id;`;

  connection[req.headers['tenant-id']].execute.query(sql, [req.query.contentSourceTypeId], (err, rows) => {
    if (!err) {
     
      callback({
        flag: commonFunctions.constants.SUCCESS,
        message: rows,
      });
    } else {
      callback({
        flag: commonFunctions.constants.SOMETHING_WRONG,
        message: "Something went wrong",
      });
    }
  });
};

router.get("/getPlatforms", function (req, res, next) {
  getPlatforms(req, (data) => {
    res.send(data);
  });
});

router.get("/getCsByTypeId", function (req, res) {
  getCsByTypeId(req, (data) => {
    res.send(data);
  });
});

const getCsByTypeId = async (req, callback) => {
  var sql = `
  select
	  cs.id,
    cs.name,
    cs.label,
    cs.elasticIndexName
  from
    search_clients sc
  left join search_clients_to_content_objects sctco
    on sc.id = sctco.search_client_id
  left join content_source_objects cso
    on cso.id = sctco.content_source_object_id
  left join content_sources cs 
    on cso.content_source_id = cs.id 
  WHERE 
    cs.content_source_type_id = ?
    and sc.uid = ?
  GROUP BY
      cs.id, cs.name, cs.label, cs.elasticIndexName`;

  connection[req.headers['tenant-id']].execute.query(sql, [req.query.contentSourceTypeId, req.query.uid], (err, rows) => {
    if (!err) {
     
      callback({
        flag: commonFunctions.constants.SUCCESS,
        message: rows,
      });
    } else {
      callback({
        flag: commonFunctions.constants.SOMETHING_WRONG,
        message: "Something went wrong",
      });
    }
  });
};

const getPlatforms = async (req,callback) =>{
  var sql = `select sc.id, sc.label as sc_label, sc.name,typ.name as scName,typ.img,sc.uid,sc.created_date,sc.search_client_type_id,sc.email,sc.sharedAccess,sc.s3_supported, sc.sc_creating_status, sc.created_by,sc.language,sc.last_updated_by,count(length(sctco.formula)>1) AS createdFormula, sc.vector_search_enabled AS vectorSearchEnabled, sc.vector_search_settings AS vectorSearchSettings, sc.ab_test_parent from search_clients AS sc LEFT join search_client_types AS typ ON sc.search_client_type_id=typ.id LEFT join search_clients_to_content_objects sctco ON sc.id=sctco.search_client_id GROUP BY sc.id ORDER BY sc.created_date DESC`;


  if (req.query.contentSourceId)
    sql = `select sc.id, sc.name, typ.img,sc.uid,sc.created_date,sc.search_client_type_id , sc.email, sc.sharedAccess, sc.s3_supported, sc.ab_test_parent
    from search_clients AS sc,
        search_client_types AS typ,
        search_clients_to_content_objects AS sctco,
        content_source_objects AS cso
    where sc.search_client_type_id = typ.id
        and sctco.search_client_id = sc.id
        and sctco.content_source_object_id = cso.id
        and cso.content_source_id = ?
    group by sc.id
    ORDER BY sc.created_date DESC `;

  
  if(req.query.platformId)
    sql = `select sc.id,sc.name,typ.name as scName,typ.img,sc.uid,sc.created_date,sc.search_client_type_id,sc.email,sc.sharedAccess,sc.s3_supported, sc.created_by, sc.sc_creating_status,
    sc.language,sc.last_updated_by,count(length(sctco.formula)>1) AS createdFormula, sc.ab_test_parent
    from search_clients AS sc LEFT join search_client_types AS typ ON 
    sc.search_client_type_id=typ.id LEFT join search_clients_to_content_objects sctco ON 
    sc.id=sctco.search_client_id LEFT join user AS usr ON usr.user_email=sc.email AND 
    COALESCE(usr.is_federated,0)=0 WHERE sc.search_client_type_id=${req.query.platformId} GROUP BY sc.id ORDER BY sc.created_date DESC;`

  // Check if the request is from cron job
  const isCronJob = req.query.abTestCron === true;

  // For cron jobs, we only need tenant-id and set userRole as 4
  if (isCronJob) {
    // Process with full admin access for cron
    connection[req.headers['tenant-id']].execute.query(sql, [req.query.contentSourceId], (err, rows) => {
      if (!err) {
        rows = rows.filter(row => {
          if (req.query.clonnedAbTestSc) {
            return true;
          } else {
            return row.ab_test_parent === null || row.ab_test_parent === '';
          }
        });

        // Set admin access for all rows in cron job
        rows.forEach(row => {
          row.editable = true;
          row.display = true;
          row.shareAccess = true;
          row.scOwnerName = 'SearchUnify Team';
          row.last_updated_by = 'SearchUnify Team';
          
          if(row.vectorSearchSettings && row.vectorSearchSettings.length) {
            row.hybridSearchEnabled = JSON.parse(row.vectorSearchSettings) && JSON.parse(row.vectorSearchSettings).hybridSearchEnabled;
          }
        });

        callback({
          flag: commonFunctions.constants.SUCCESS,
          message: rows,
        });
      } else {
        callback({
          flag: commonFunctions.constants.SOMETHING_WRONG,
          message: "Something went wrong",
        });
      }
    });
    return; // Exit early for cron jobs
  }

  // Regular request processing
  const userEmail = req.headers.session.email;
  const userRole = req.headers.session.roleId;
  const allowedSUuser = [/grazitti.com/, /searchunify.com/];
  const searchUnifyUser = allowedSUuser[0].test(userEmail) || allowedSUuser[1].test(userEmail);

  // Get Access control settings for regular requests
  const accessControlSettings = (
    await getDataFromDB(`select * from access_control_settings;`, req)
  )[0].searchClient;

  connection[req.headers['tenant-id']].execute.query(sql, [req.query.contentSourceId], (err, rows) => {
    if (!err) {
      allAccess = searchUnifyUser || userRole == 4;
      
      rows = rows.filter(row => {
        if (req.query.clonnedAbTestSc) {
          return true;
        } else {
          return row.ab_test_parent === null || row.ab_test_parent === '';
        }
      });

      for (var i = 0; i < rows.length; i++) {
        rows[i]["editable"] = true;
        rows[i]["display"] = true;
        rows[i]["shareAccess"] = accessControlSettings == 1 ? false : true;

        if(rows[i]['created_by']){
          try {
            let creator = JSON.parse(rows[i]['created_by']);
            rows[i]['scOwnerName'] = allowedSUuser[0].test(creator['email']) || allowedSUuser[1].test(creator['email'])? 'SearchUnify Team': creator.name;
          } catch (err) {
            rows[i]['scOwnerName'] = 'SearchUnify Team';
          }
        } else {
          rows[i]['scOwnerName'] = 'User not Found'
        }
        if(rows[i]['last_updated_by']){
          try {
            let lastUpdatedUser = JSON.parse(rows[i]['last_updated_by']) 
            rows[i]['last_updated_by'] =allowedSUuser[0].test(lastUpdatedUser.email) || allowedSUuser[1].test(lastUpdatedUser.email)? 'SearchUnify Team': lastUpdatedUser.name;
          } catch (err) {
            rows[i]['last_updated_by'] = 'SearchUnify Team';
          }
        }

        if (!allAccess && !(accessControlSettings == 1)) {
          try {
            const sharedAccess = JSON.parse(rows[i].sharedAccess || '[]');
            rows[i]["shareAccess"] = sharedAccess.includes(userEmail) || rows[i]["email"] == userEmail;
            rows[i]["editable"] = userEmail === rows[i]["email"] || rows[i]["shareAccess"];

            if (accessControlSettings == 3 && !rows[i]["editable"]) {
              rows[i]["display"] = false;
            }
          } catch (err) {
            // If there's any error parsing sharedAccess, default to no access
            rows[i]["shareAccess"] = false;
            rows[i]["editable"] = false;
            if (accessControlSettings == 3) {
              rows[i]["display"] = false;
            }
          }
        }

        if(rows[i].vectorSearchSettings && rows[i].vectorSearchSettings.length) {
          rows[i].hybridSearchEnabled = JSON.parse(rows[i].vectorSearchSettings) && JSON.parse(rows[i].vectorSearchSettings).hybridSearchEnabled;
        }
      }

      callback({
        flag: commonFunctions.constants.SUCCESS,
        message: rows,
      });
    } else {
      commonFunctions.errorlogger.error("Something went wrong getPlatforms function: ", err);
      callback({
        flag: commonFunctions.constants.SOMETHING_WRONG,
        message: "Something went wrong",
      });
    }
  });
};

router.get("/getContentTypes", function (req, res, next) {
  let sql = "SELECT * FROM search_client_types as sc ORDER BY sc.name ";
  connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
    if(err || !rows.length) return res.send({ status: 500, success: false, data : null});
    res.send(rows);
  });
});

router.get("/getObjects", (req, res, next) => {
  connection[req.headers['tenant-id']].execute.query(
    "select content_source_objects.name as name,content_source_objects.label as label from content_source_objects,content_sources where content_sources.id=content_source_objects.content_source_id and content_sources.elasticIndexName=?",
    [req.query.indexName],
    (error, results) => {
      if (error) {
        console.error(error);
        res.sendStatus(500);
      } else {
        res.send(results);
      }
    }
  );
});

router.post("/browse", (req, res, next) => {
  let client = new elastic.Client({
    host:
      "http://" +
      config.get("elasticIndexCS.host") +
      ":" +
      config.get("elasticIndexCS.port"),
  });
  client.search(
    {
      index: req.body.index,
      type: req.body.type,
      body: {
        query: {
          match: {
            title: req.body.searchText,
          },
        },
      },
    },
    (error, data) => {
      if (error) {
        console.error(error);
        res.send([]);
      } else {
        res.send(data.hits.hits);
      }
    }
  );
});

router.get("/getFiltersPriority", function (req, res, next) {
  var accessToken = req.query.uid;
  var tempArr = [];
  tempArr.push(accessToken);
  var check = commonFunctions.checkArray(tempArr);
  var response = {
    status: commonFunctions.constants.responseFlags.INVALID_ACCESS_TOKEN,
    message: "INVALID UID TOKEN",
  };
  if (!check) {
    res.send(response);
  } else {
    commonFunctions.getPriorityFieldsInFilter(req.query.uid,req, function (result) {
      let recordsToSend = result.filter((x) => {
        if (x.isFilterable) return x;
      });
      recordsToSend.sort(function (a, b) {
        return parseFloat(a.priority) - parseFloat(b.priority);
      });
      res.send(recordsToSend);
    });
  }
});

router.post('/saveFiltersPriority', function (req, res, next) {
  var accessToken = req.query.uid;
  var platformId = req.body.platformId;
  var tempArr = [];
  var deleteFilters = req.body.deleteFilters.map(
    (x) => x.field.use.search_clients_to_content_object_id
  );
  deleteFilters.filter((x, i, a) => a.indexOf(x) == i);
  tempArr.push(accessToken);
  var check = commonFunctions.checkArray(tempArr);
  var response = {
    status: commonFunctions.constants.responseFlags.INVALID_ACCESS_TOKEN,
    message: "INVALID UID TOKEN",
  };
  if (!check) {
    res.send(response);
  } else {
    async.series(
      [
        (cb) => {
          if (deleteFilters.length) {
            const sqlCS =
              "Delete from `search_clients_filters` where search_clients_to_content_object_id in(?) and id not in (?)";
            var q = connection[req.headers['tenant-id']].execute.query(
              sqlCS,
              [deleteFilters, req.body.filter.map((x) => x.id)],
              function (errAsync, rows) {
                if (errAsync) {
                  commonFunctions.errorlogger.error(
                    "Error inside filter",
                    errAsync
                  );
                  cb(errAsync, []);
                } else {
                  cb(null, []);
                }
              }
            );
          } else {
            cb(null, []);
          }
      },
      cb => {
        commonFunctions.insertFilters(req.body.filter,req, function (err, result) {
          cb(null, result)
        })
      },
      cb => {
        if (req.body.tabFilterPrefrence && Object.keys(req.body.tabFilterPrefrence).length && platformId) {
          let tabPref = req.body.tabFilterPrefrence;
          tabPref.sourceSortBy = tabPref.horizontalTabEnabled 
            && tabPref.horizontalTabFacet == '_type'
            ? 'custom' : tabPref.sourceSortBy;
          const sqlCS = `update search_clients SET
            hidden_facet = ?,
            hideAllContentSources = ?,
            horizontalTabEnabled = ?, 
            horizontalTabFacet = ?,
            horizontalTabOrder = ?,
            sourceSortBy = ?,
            sourceOrderBy = ?,
            sourceOrder = ?,
            sourcePriority = ?,
            collapsibleSummary = ?,
            minSummaryLength = ?,
            maxSummaryLength = ?,
            contentTag = ?,
            default_results_sorting = ?,
            minDocCount = ?
            where id = ? `;
          connection[req.headers['tenant-id']].execute.query(sqlCS, [JSON.stringify(tabPref.hidden_facet),tabPref.hideAllContentSources, tabPref.horizontalTabEnabled, tabPref.horizontalTabFacet, tabPref.horizontalTabOrder, tabPref.sourceSortBy, tabPref.sourceOrderBy, tabPref.sourceOrder, tabPref.sourcePriority, tabPref.collapsibleSummary, tabPref.minSummaryLength, tabPref.maxSummaryLength, tabPref.contentTag, JSON.stringify(req.body.sortingDetails), tabPref.minDocCount,platformId ], function (errAsync, rows) {
            if (errAsync) {
              commonFunctions.errorlogger.error("Error in updating filter preferences: ", errAsync);
              cb(errAsync, [])
            }
            else {
              cb(null, [])
            }
          })
        }
        else cb(null, []);
      },
      cb => {
        if (req.body.summaryPref && Object.keys(req.body.summaryPref).length) {
          var sqlCS = `INSERT into search_clients_filters (id, search_priority, summary_length) VALUES `
          req.body.summaryPref.forEach((r,i) => {
            sqlCS += `(${r.id}, ${r.search_priority}, ${r.summary_length || r.use.summary_length || 100})`;
            if (req.body.summaryPref.length - 1 > i) sqlCS += ','
          })
          sqlCS += `ON DUPLICATE KEY UPDATE search_priority=VALUES(search_priority),summary_length=VALUES(summary_length);`
          connection[req.headers['tenant-id']].execute.query(sqlCS, function (errAsync, rows) {
            if (errAsync) {
              commonFunctions.errorlogger.error("Error in updating filter preferences: ", errAsync);
              cb(errAsync, [])
            }
            else {
              cb(null, [])
            }
          })
        }
        else cb(null, []);
      }
    ], function (error, response) {
      if (error) {
        commonFunctions.errorlogger.error(error);
      } else {
        if (config.get("kafkaTopic.enable") && accessToken && platformId) {
          let configObj = { "platformId": platformId, "uid": accessToken }
          searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
            console.log('Sending info to Search service');
            res.send({ flag: 200, message: "DONE" })
          })
        }
      }
      commonFunctions.deleteGptRulesFromCache(req,req.query.uid)
    });
  }
});

router.post("/editClient", async (req, res, next) => {
  let scOperations = await checkOSMigration(req);
  if(scOperations) {
    res.send({ flag: 401, message: "Unauthorized" });
  }
  let platform = req.body.platform;
  platform.currentUser = JSON.stringify({email:req.headers.session.email, name:req.headers.session.name});
  let allowedFurther = await accessCheck(req.headers.session.email, req.headers.session.roleId, platform.id, req)
  if (!allowedFurther) {
    res.send({ flag: 401, message: "Unauthorized" });
  } else {
    var results = [];
    results.push({ readCss: platform.css, uid: platform.uid });
    var validatejsonforHTML = {
      client_href: platform.client_href,
      name: platform.name,
    };
    var validateHtmlrequest = checkobjectforhtml(validatejsonforHTML);
    if (validateHtmlrequest == true) {
      delete platform["js"];
      updateInsertSearchClient(
        results[0],
        platform,
        req,
        res,
        function (error, resultAfterCallback) {
          let platformId_kafka = platform.id;
          let uid_kafka = platform.uid;
          let neural_search_enabled_current_session = platform.neuralSearchEnabledInCurrentSession;
          if (resultAfterCallback) {
            if (
              config.get("kafkaTopic.enable") &&
              uid_kafka &&
              platformId_kafka
            ) {
              let configObj = {
                platformId: platformId_kafka,
                uid: uid_kafka,
                neuralSearchEnabledInCurrentSession: neural_search_enabled_current_session
              };
              console.log(':::::configObj::::::',configObj);
              searchResultKafkaConfig.getSearchClientSettingsViaKafka(
                configObj,req,
                function (err, searchConfig) {
                  if (!err && platform.isDisabled) {
                    sendDeleteKafkaMessages( req, platform.uid );
                  }
                  console.log("Sending info to Search service");
                }
              );
            }
            commonFunctions.deleteGptRulesFromCache(req,req.body.platform.uid)
            res.send({ flag: 200, message: "Done" });
          } else {
            commonFunctions.deleteGptRulesFromCache(req,req.body.platform.uid)
            res.send({ flag: 403, message: "Failed" });
          }
        }
      );
      
    } else {
      res.send({ flag: 403, message: "Only sting value is allowed" });
    }
  }
});

router.post('/updateHiglightQuery',(req,res)=>{
  const pre_tag = req.body.pre_tag;
  const post_tag = req.body.post_tag;
  const searchClientId = req.body.searchClientId
  const query = 'UPDATE search_clients SET pre_tag = ?, post_tag = ? WHERE id = ?';
  connection[req.headers['tenant-id']].execute.query(query,[pre_tag,post_tag,searchClientId],(err,response)=>{
    if (config.get("kafkaTopic.enable")) {
      let configObj = { "platformId": req.body.searchClientId, "uid": '' }
      searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
        res.send({});
      })
    }
  })
})


router.get('/getHighlightedQuery',(req,res)=>{
  const searchClientId = req.query.id
const query = `SELECT pre_tag,post_tag from search_clients WHERE id = ?`
  connection[req.headers['tenant-id']].execute.query(query,[searchClientId],(err,response)=>{
    if(err){
      res.send({err})
    }else{
      res.send({data:response[0]})
    }
  })
})


router.post("/cloneSearchClient", (req, res, next) => {
    cloneSearchClient(req, res, null);
});


let arrData = [];
async function cloneSearchClient(req, res, abTestPayload) {
 try {
  let allowedFurther = await accessCheck(
    req.headers.session.email,
    req.headers.session.roleId,
    req.body.platformId,
    req
  );
  if (!allowedFurther) {
    return res.send({ flag: 401, message: "Unauthorized" });
  }
    if(!req.body.restoreUid) {
      // Early exit in case search client same name check fails
      const sameSCName = await sameNameCheck(req.body.name, req.headers['tenant-id']);
      if(sameSCName) {
        commonFunctions.errorlogger.info('Search client same name check failed!');
        return res.send({ flag: 403, message: "Search client same name check failed!" });
      }
    }
    let platformId = req.body.platformId;
    async.auto({
      getSearchClient: cb => {
        getSearchClient(platformId,req, function (error, result) {
          cb(error, result);
        })
      },
      addSearchClient: ['getSearchClient', (results, cb) => {
        let client = [];
        client.push({ 
          currentUser : '',
          email: req.headers.session.email, 
          client_href: results.getSearchClient.client.client_href, 
          name: req.body.name, 
          search_client_type_id: req.body.search_client_type_id, 
          recommendations: req.body.recommendations, 
          clone: req.body.restoreSettings ? 0 : 1, 
          cloneUid: req.body.uid, 
          restoreUid: req.body.restoreUid ? 1 : 0, 
          language: req.body.language, 
          migrate: req.body.migrate ? 1 : 0 
        })
        let platform = req.body.restoreUid ? results.getSearchClient.client : client[0];
        if(req.body.restoreUid) {
          platform.clone = 0;
          platform.migrate = 0;
          platform.cloneUid = req.body.uid;
          platform.restoreUid = 1;
          platform.sc_creating_status = 1;
        }
        addSearchClient(platform, req, res, function (error, result) {
          cb(error, result);
        });
      }],
      getSetShareResultConfig: [ 'getSearchClient','addSearchClient', (results,  cb) => {
        if(req.body.restoreUid) {
          return cb(null, {});
        }
        //SC from where we are cloning
        const searchClientTypeID = results.getSearchClient.client.search_client_type_id;
        const searchClientTypeUID = results.getSearchClient.client.uid;
        const resultSearchClientTypeID = results.addSearchClient.getClientType[0][0].id;
        const scTypes = [7];

        if(scTypes.includes(searchClientTypeID)) {
          var sql = "Select id from search_clients where search_client_type_id = ? && uid = ?";
          connection[req.headers['tenant-id']].execute.query(sql, [searchClientTypeID, searchClientTypeUID], function (err, response) {
            if (err) {
              commonFunctions.errorlogger.error("error:: getSetShareResultConfig", err);
              cb(err, {});
            } else{
              const {id} = response[0];
              var query = "Select search_client_id, result_action_id, status, merge, selected_object, content_source_label from search_client_actions where search_client_id = ?";
              connection[req.headers['tenant-id']].execute.query(
                query,
                [id],
                function (err, result) {
                  if (err) {
                    cb(err, {});
                    commonFunctions.errorlogger.error("error:: getSetShareResultConfig", err);
                  } else {
                    if (result.length !== 0 && scTypes.includes(resultSearchClientTypeID)) {
                      // Update the OLD ID just with the new ID
                      var insertQuery = "INSERT INTO search_client_actions (search_client_id, result_action_id, status, merge, selected_object, content_source_label) VALUES (?, ?, ?, ?, ?, ?)";
                      let insertCount = 0;
                      result.forEach(function (row) {
                        connection[req.headers['tenant-id']].execute.query(
                          insertQuery,
                          [results.addSearchClient.insert.insertId, row.result_action_id, row.status, row.merge, row.selected_object, row.content_source_label],
                          function (insertErr, insertResult) {
                            if (insertErr) {
                              commonFunctions.errorlogger.error("error during insert:: search_client_actions", insertErr);
                              cb(insertErr, {});
                            } else {
                              insertCount++;
                            }
                            if (insertCount === result.length) {
                              cb(null, {});
                            }
                          }
                        );
                      });
                    } else {
                      cb(null, {});
                    }
                  }
                }
              );
            }
          });
        } else {
          cb(null, {});
        }
      }],
      getFeedbackData: [ 'getSearchClient', 'addSearchClient', 'getSetShareResultConfig', (results,  cb) => {
        if(req.body.restoreUid) {
          return cb(null, {});
        }
        const searchClientTypeID = results.getSearchClient.client.search_client_type_id;
        const searchClientTypeUID = results.getSearchClient.client.uid;
        const resultSearchClientTypeID = results.addSearchClient.getClientType[0][0].id;
        const SCTypes = [7, 16, 18];

        if(!SCTypes.includes(searchClientTypeID)) {
          var sql = "Select * from user_feedback where search_client_uid = ?";
          connection[req.headers['tenant-id']].execute.query(sql, [searchClientTypeUID], function (err, response) {
            if (err) {
              commonFunctions.errorlogger.error("error:: getFeedbackData", err);
              cb(err, {});
            } else{
              if (response.length === 0) {
                return cb(null, {});
              }
              if(!SCTypes.includes(resultSearchClientTypeID)){
                const sqlInsert = `INSERT INTO user_feedback (search_client_uid,pageRatingInstance,pageRatingCustomization,searchFeedback,contentSearchExp,searchExp,conversionExp,dropdowns) VALUES (?,?,?,?,?,?,?,? )`;
                connection[req.headers['tenant-id']].execute.query(sqlInsert, [results.addSearchClient.uid, response[0].pageRatingInstance, response[0].pageRatingCustomization, response[0].searchFeedback, response[0].contentSearchExp, response[0].searchExp, response[0].conversionExp, response[0].dropdowns], function (err, response) {
                  if (err) {
                    commonFunctions.errorlogger.error("error during insert:: user_feedback", err);
                    cb(err, {});
                  } else {
                    cb(null, {});
                  }
                }); 
              }
            }
          });
        } else {
          cb(null, {});
        }
      }],
      updateSSOConfig : [ 'getSearchClient','addSearchClient', 'getFeedbackData', (results,  cb) => {
        if(req.body.restoreUid) {
          return cb(null, {});
        }
        const searchClientTypeID = results.getSearchClient.client.search_client_type_id;
        const searchClientTypeUID = results.getSearchClient.client.uid;
        const resultSearchClientTypeID = results.addSearchClient.getClientType[0][0].id;

        if(searchClientTypeID == 31 && resultSearchClientTypeID === 31) {
          var sql = "Select * from search_client_sso_config where sc_uid = ?";
          connection[req.headers['tenant-id']].execute.query(sql, [searchClientTypeUID], function (err, response) {
            if (err) {
              commonFunctions.errorlogger.error("error:: updateSSOConfig", err);
              cb(err, {});
            } else{
              const sqlInsert = `UPDATE search_client_sso_config SET sso_config = ? WHERE sc_uid = ?`;
              connection[req.headers['tenant-id']].execute.query(sqlInsert, [response[0].sso_config, results.addSearchClient.uid], function (err, response) {
                if (err) {
                  commonFunctions.errorlogger.error("error during insert:: search_client_sso_config", err);
                  cb(err, {});
                } else {
                  cb(null, {});
                }
              }); 
            } 
          });
        } else {
          cb(null, {});
        }
      }],
      updateSearchClient: ['getSearchClient', 'addSearchClient', 'updateSSOConfig', (results, cb) => {
        if(req.body.restoreUid) {
          return cb(null, {});
        }
        let  parameters = {};
        results.getSearchClient.client.id = results.addSearchClient.insert.insertId;
        results.getSearchClient.client.uid = results.addSearchClient.getUUID;
        results.getSearchClient.client.DownloadFilePath = 'resources/search_clients_custom/' + results.getSearchClient.client.uid + '/';
        results.getSearchClient.client.created_date = null;
        results.getSearchClient.client.name = req.body.name;
        results.getSearchClient.client.language = req.body.language;
        results.getSearchClient.client.s3_supported = req.body.s3_supported || results.getSearchClient.client.s3_supported;
        results.getSearchClient.client.preview = req.body.preview || results.getSearchClient.client.preview;
        results.getSearchClient.client.attachment_preview = req.body.attachment_preview || results.getSearchClient.client.attachment_preview;
        results.getSearchClient.client.search_client_type_id = req.body.search_client_type_id;
        results.getSearchClient.deflection_formula.search_client_id = results.addSearchClient.insert.insertId;
        parameters.deflection_formula = results.getSearchClient.deflection_formula;
        parameters.client = results.getSearchClient.client;
        parameters.client.ab_test_parent = req.body.ab_test_status === 0 ? req.body.uid : null;
        parameters.client.name = req.body.ab_test_status === 0 ? `${parameters.client.name}_${results.addSearchClient.getUUID}`  : parameters.client.name;
        parameters.analyticsReports = results.getSearchClient.analyticsReports;
        parameters.mapping_objects = [];

        // remove it after QA
        // console.log("parameters =======parameters=================>>>>>>>>>>>>>>>>>>==========>>>>>>>>>>>>>>>>>>",parameters.client.name);
        // console.log("parameters=========parameters=================>>>>>>>>>>>>>>>>>>========>>>>>>>>>>>>>>>>>>",parameters.client.id);
        parameters.analyticsReports.map(x => { x.search_client_id = results.addSearchClient.insert.insertId; delete x.id; });
        for (let a = 0; a < results.getSearchClient.enabledObjects.length; a++) {

          let mapping = [];
          let metadata_mapping = [];
          let preview_mapping = [];

          let mapping_use = results.getSearchClient.sources.filter(x => x.enabled).map(y => y.objects).reduce(commonFunctions.reduceObject).filter(z => z.enabled).map(f => f.fields).reduce(commonFunctions.reduceObject).filter(x => x.use.search_clients_to_content_object_id === results.getSearchClient.enabledObjects[a].id);
          mapping_use.map(x => { mapping.push({ content_source_object_field_id: x.use.content_source_object_field_id, use_as: x.use.use_as, priority: x.use.priority, content_source_object_id: x.content_source_object_id, exclude: x.use.exclude, search_priority: x.search_priority, use_value: x.use.use_value, sort_by:x.use.sort_by, order_by:x.use.order_by, auto_learning:x.use.auto_learning, facet_type: x.use.facet_type, facet_data_type: x.use.facet_data_type}) })

          results.getSearchClient.sources.filter(x => x.enabled).map(y => y.objects).reduce(commonFunctions.reduceObject).filter(z => z.enabled).map(f => f.fields).reduce(commonFunctions.reduceObject).filter(x => x.metadata.search_clients_to_content_object_id === results.getSearchClient.enabledObjects[a].id).map(x => x.metadata).map(y => {
            metadata_mapping.push({ content_source_object_field_id: y.content_source_object_field_id, use_as: y.use_as, priority: y.priority, autosuggestField: y.autosuggestField, metaData: y.metaData })
          });

          results.getSearchClient.sources.filter(x => x.enabled).map(y => y.objects).reduce(commonFunctions.reduceObject).filter(z => z.enabled).map(f => f.fields).reduce(commonFunctions.reduceObject).filter(x => x.preview.search_clients_to_content_object_id === results.getSearchClient.enabledObjects[a].id).map(x => x.preview).map(y => {
            preview_mapping.push({ 
              content_source_object_field_id: y.content_source_object_field_id,
              preview_order: y.preview_order,
              sc_id: y.sc_id
            });
          });
          parameters.mapping_objects.push({
            base_href: results.getSearchClient.enabledObjects[a].base_href,
            compose_title: results.getSearchClient.enabledObjects[a].compose_title,
            merge_results: results.getSearchClient.enabledObjects[a].merge_results,
            content_source_object_id: results.getSearchClient.enabledObjects[a].content_source_object_id,
            mapping: mapping,
            metadata_mapping: metadata_mapping,
            preview_mapping,
            title_field_id: results.getSearchClient.enabledObjects[a].title_field_id,
            preview_type: results.getSearchClient.enabledObjects[a].preview_type,
            search_client_id: results.addSearchClient.insert.insertId,
            formula: results.getSearchClient.enabledObjects[a].formula,
            icon: results.getSearchClient.enabledObjects[a].icon,
            permission_by_pass: results.getSearchClient.enabledObjects[a].permission_by_pass
          })
        }
        parameters.mapping_objects.filter(x => x)
        const versioningEnabledObjects = results.getSearchClient.enabledObjects.filter(item => {
          try {
            const mergeResults = item.merge_results ? JSON.parse(item.merge_results) : null;
            return mergeResults && mergeResults.enabled === true;
          } catch (e) {
            return false;
          }
        });
       
        let kafkaMessageForVersioningObjects = {}
        let filteredSource;
        let type;
        for(let index= 0; index<versioningEnabledObjects.length;index++){
            let mergeFieldInfo  = JSON.parse(versioningEnabledObjects[index].merge_results);
            const primaryFieldName = (mergeFieldInfo.primaryField && mergeFieldInfo.primaryField.name) || null;
            filteredSource = results.getSearchClient.sources.filter(source => 
              source.objects.some(obj => obj.id === versioningEnabledObjects[index].content_source_object_id)
            );
            type = (filteredSource[0].objects.find(item => item.id === versioningEnabledObjects[index].content_source_object_id) || {}).name || null;
            kafkaMessageForVersioningObjects = {
              mergedResults:versioningEnabledObjects[index].merge_results,
              scriptParams:{
                primaryFieldUpdated:true,
                indexName:filteredSource[0].index,
                contentSourceId:filteredSource[0].id,
                type:type,
                primaryField:primaryFieldName,
                searchClientName:results.getSearchClient.client.name,
                email: results.getSearchClient.client.email,
                uid:results.getSearchClient.client.uid,
                toggleDisabled:false
              },
              appId: 1
            }
          kafkaLib.publishMessage({
            topic: config.get("kafkaTopic.processRequest"),
            messages: [{
              value: JSON.stringify({
                type: 'ADD',
                service: 'index-service',
                tenantId: req.headers['tenant-id'],
                comment: `merge_result_${filteredSource[0].id}_${results.getSearchClient.client.uid}_${req.headers['tenant-id']}`,
                information: {
                  eventType: 'merge-result-cron',
                  executionMode: 'kafka',
                  kafkaTopic: config.get('kafkaTopic.versionUpdateMapping'),
                  kafkaValue: {
                    tenantId: req.headers['tenant-id'],
                    data: kafkaMessageForVersioningObjects,
                  },
                },
              }),
              key: req.headers['tenant-id']
            }],
          });
        }
        req.body.client = {
          uid:parameters.client.uid
        }
        // setTimeout(function () {
            updateSearchClient(parameters,req, function (error, result) {
                if (result) {
                    if (
                        config.get('kafkaTopic.enable') &&
                        parameters.client &&
                        parameters.client.uid &&
                        parameters.client.id
                    ) {
                        let configObj = {
                            platformId: parameters.client.id,
                            uid: parameters.client.uid
                        }

                        // remove it after QA
                        // console.log("configObjconfigObjconfigObjconfigObjconfigObj========================>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",configObj.platformId);
                        // console.log("configObjconfigObjconfigObjconfigObjconfigObj========================>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",configObj.uid);
                        searchResultKafkaConfig.getSearchClientSettingsViaKafka(
                            configObj,req,
                            function (err, searchConfig) {
                              if(req.body.ab_test_status === 0){
                                const clientData = {
                                  uid: parameters.client.uid,
                                  name: parameters.client.name,
                                  abTestType: req.body.method,
                                  trafficSplit: req.body.trafficValue
                              };
                              arrData.push(clientData);

                              // remove it after QA
                              //console.log("arrDataarrDataarrDataarrDataarrDataarrDataarrData===============>>>>>>>>>>>>>>>>>>>>>>>",arrData);
                                if(arrData.length === req.body.searchMethodsCount){

                                  const rearrangeData = (data, referenceOrder) => {
                                    const methodOrder = Object.keys(referenceOrder);
                                    
                                    // Sort the data array based on the order of methods
                                    return data.sort((a, b) => {
                                        const indexA = methodOrder.indexOf(a.abTestType);
                                        const indexB = methodOrder.indexOf(b.abTestType);
                                        return indexA - indexB;
                                    });
                                };
                                  arrData = rearrangeData(arrData, abTestPayload.searchMethodTrafficSplit);

                                  
                                  // remove it after QA
                                 // console.log("abTestPayloadabTestPayload============uuidABTestuuidABTest-------->>>>>>>>>>>",abTestPayload.uuidABTest);
                                const insertQuery = `
                                  INSERT INTO ab_test (
                                    uid_ab_test, sc_id, name, sc_uid, tenant_id, test_duration, start_date, end_date, ab_test_status
                                  ) 
                                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                                `;

                                const values = [abTestPayload.uuidABTest,abTestPayload.sc_id, abTestPayload.name, abTestPayload.sc_uid, abTestPayload.tenant_id, abTestPayload.test_duration, abTestPayload.start_date || null, abTestPayload.end_date || null, abTestPayload.ab_test_status];



                                connection[req.headers['tenant-id']].execute.query(insertQuery, values, async(insertError, result) => {
                                  if (insertError) {
                                    commonFunctions.errorlogger.error("Database Error  inserting into ab_test:", insertError);
                                    if(req.body.ab_test_status === 0){
                                      commonFunctions.errorlogger.error("Error====================>>>>>>>>>>>>>>>>>>>>>>",error);
                                      try {
                                        // remove it after QA
                                        // console.log("Starting cleanup with updateABTest======>>>>>>>>>1111111111111111111");
                                        let searchClientsCleanUpData = await deleteScInCaseOfError(req, res, abTestPayload.sc_uid); // removing AB Test Search client if error occur 
                                        commonFunctions.errorlogger.info("Cleanup completed successfully",searchClientsCleanUpData);
                                        if(headerSentCleanUp === 0){
                                          headerSentCleanUp = 1;
                                          return res.status(200).send({ status: 4, err: "Error during search client creation/update" }); // sending status as 4 for AB Testing
                                        }
                                      } catch (cleanupError) {
                                        commonFunctions.errorlogger.error("Error during cleanup:", cleanupError);
                                      }
                                    }            
                                  }
                                  commonFunctions.errorlogger.info("Successfully inserted data into ab_test.");
                                    const ab_test_id = abTestPayload.uuidABTest;
                                    const tenant_id = req.headers['tenant-id'];
                                    const childInsertQuery = `
                                      INSERT INTO ab_test_children (ab_test_id, name, type, uid, tenant_id, traffic_split)
                                      VALUES (?, ?, ?, ?, ?, ?)
                                    `;

                                    for (const data of arrData) {
                                      const childInsertValues = [ab_test_id, data.name, data.abTestType, data.uid, tenant_id, data.trafficSplit];

                                      await new Promise((resolve, reject) => {
                                        connection[tenant_id].execute.query(childInsertQuery, childInsertValues, async (error, result) => {
                                          if (error) {
                                            commonFunctions.errorlogger.error("Error inserting into ab_test_children:", error);
                                            if(req.body.ab_test_status === 0){
                                              commonFunctions.errorlogger.error("Error====================>>>>>>>>>>>>>>>>>>>>>>",error);
                                              try {
                                                // remove it after QA
                                               // console.log("Starting cleanup with updateABTest======>>>>>>>>>22222222222222222222222222222");
                                                let searchClientsCleanUpData = await deleteScInCaseOfError(req, res, abTestPayload.sc_uid); // removing AB Test Search client if error occur 
                                                commonFunctions.errorlogger.info("Cleanup completed successfully",searchClientsCleanUpData);
                                                if(headerSentCleanUp === 0){
                                                  headerSentCleanUp = 1;
                                                  return res.status(200).send({ status: 4, err: "Error during search client creation/update" }); // sending status as 4 for AB Testing
                                                }
                                              } catch (cleanupError) {
                                                commonFunctions.errorlogger.error("Error during cleanup:", cleanupError);
                                              }
                                            }
                                          } else {
                                            commonFunctions.errorlogger.info("Successfully inserted data into ab_test_children");
                                            resolve(result);
                                          }
                                        });
                                      });
                                    }
                                    arrData = [];
                                    commonFunctions.fetchAbTestData(req.headers['tenant-id'], ab_test_id)
                                    .then((result) => {
                                      const { abTestName, searchClientName, ...filteredData } = result;
                                      const kafkaData = { ...filteredData, event: 'insert' };
                                      kafkaLib.publishMessage({
                                        topic: config.get("kafkaTopic.abTestTopic"),
                                        messages: [
                                          {
                                            value: JSON.stringify(kafkaData)
                                          },
                                        ],
                                      });
                                      commonFunctions.errorlogger.info("AB Test Kafka Details----->>>>>>>",kafkaData);
                                      return res.send(result);
                                    })
                                    .catch((error) => {
                                      commonFunctions.errorlogger.error("Error in /get-ab-test-data:", error.message);
                                      return res.status(500).send({ error: error.message });
                                    });
                                      arrData = [];
                                });
                                }
                              }else{
                                cb(null, {});
                              }
                            }
                        )
                    } else {
                      cb(null, {});
                    }
                } else {
                  if(req.body.ab_test_status === 0){
                    commonFunctions.errorlogger.error("Error====================>>>>>>>>>>>>>>>>>>>>>>",error);
                    (async () => {
                      try {
                        // remove it after QA
                        // console.log("Starting cleanup with updateABTest======>>>>>>>>>333333333333333333333");
                        let searchClientsCleanUpData = await deleteScInCaseOfError(req, res, abTestPayload.sc_uid); // removing AB Test Search client if error occur 
                        commonFunctions.errorlogger.info("Cleanup completed successfully",searchClientsCleanUpData);
                        if(headerSentCleanUp === 0){
                          headerSentCleanUp = 1;
                          return res.status(200).send({ status: 4, err: "Error during search client creation/update" }); // sending status as 4 for AB Testing
                        }
                      } catch (cleanupError) {
                        commonFunctions.errorlogger.error("Error during cleanup:", cleanupError);
                      }
                    })();
                  }else{
                    return res.send({
                      flag: 403,
                      message: 'Failed'
                  })
                  }
                }
            })
        // }, 1000);
      }]
    },
    (error, result) => {
      if (error) {
        commonFunctions.errorlogger.error("error in cloning", error);
        if(req.body.ab_test_status === 0){
          commonFunctions.errorlogger.error("Error====================>>>>>>>>>>>>>>>>>>>>>>",error);
          (async () => {
            try {
              // remove it after QA
              // console.log("Starting cleanup with updateABTest======>>>>>>>>>4444444444444444444444");
              let searchClientsCleanUpData = await deleteScInCaseOfError(req, res, abTestPayload.sc_uid);
              commonFunctions.errorlogger.info("Cleanup completed successfully",searchClientsCleanUpData);
              if(headerSentCleanUp === 0) {
                headerSentCleanUp = 1;
                return res.status(200).send({ status: 4, err: "Error during search client creation/update" }); // AB Testing status
              }
            } catch (cleanupError) {
              commonFunctions.errorlogger.error("Error during cleanup:", cleanupError);
            }
          })();
        }else{
          return res.send({
            flag: 403,
            message: 'Failed'
          });
        }

      } else { 
        return res.send({
          flag: 200,
          message: 'Success'
        });
      }
    });
 } catch (error) {
  if(req.body.ab_test_status === 0){
    commonFunctions.errorlogger.error("Error====================>>>>>>>>>>>>>>>>>>>>>>",error);
    (async () => {
      try {
        // remove it after QA
        // console.log("Starting cleanup with updateABTest======>>>>>>>>>4444444444444444444444");
        let searchClientsCleanUpData = await deleteScInCaseOfError(req, res, abTestPayload.sc_uid);
        commonFunctions.errorlogger.info("Cleanup completed successfully",searchClientsCleanUpData);
        if(headerSentCleanUp === 0) {
          headerSentCleanUp = 1;
          return res.status(200).send({ status: 4, err: "Error during search client creation/update" }); // AB Testing status
        }
      } catch (cleanupError) {
        commonFunctions.errorlogger.error("Error during cleanup:", cleanupError);
      }
    })();
  }else{
    return res.send({
      flag: 403,
      message: 'Failed'
    });
  }
 }
}

router.get("/getsearchclientSettings", function (req, res, next) {
  res.setHeader("Access-Control-Allow-Origin", "*");
  if (req.query.uid) {
    connection[req.headers['tenant-id']].execute.query(
      `SELECT id FROM search_clients WHERE uid=?`,
      [req.query.uid],
      (e, r, f) => {
        if (e || !r.length) {
          res.send({ flag: 404, data: {} });
        } else {
          getSearchClient(r[0].id, function (err, result) {
            connection[req.headers['tenant-id']].execute.query(
              `SELECT * FROM template_types WHERE id=?`,
              [result.client.template_type_id],
              (e1, r1, f1) => {
                if (e1 || !r1.length) {
                  res.send({ flag: 404, data: {} });
                } else {
                  res.send({
                    client: result.client,
                    template: r1[0],
                  });
                }
              }
            );
          });
        }
      }
    );
  } else {
    res.send({ flag: 404, data: {} });
  }
});

router.get("/readAdHTML/:uuid", async(req, res, next) => {
  const data = await fetchTenantInfoFromUid(req.params.uuid);
  tenantSqlConnection(data.tenantId, data.databaseName);
  const tenantId = data.tenantId ;
  async.auto(
    {
      getId: (cb) => {
        var sql_get_id = `SELECT * FROM search_clients WHERE uid = ?`;
        let q_get_id = connection[tenantId].execute.query(
          sql_get_id,
          [req.params.uuid],
          (e, r, f) => {
            if (e || !r.length) cb(e);
            else {
              if (r && r.length) {
                cb(null, { client_id: r[0].id });
              }
            }
          }
        );
      },
      getHtmlPath: [
        "getId",
        (results, cb) => {
          res.setHeader("Access-Control-Allow-Origin", "*");
          if (results.getId) {
            if (req.query.phrase && results.getId.client_id) {
              var sql = `SELECT *  FROM  advertisement_templates WHERE phrase = ? AND search_client_id = ?`;
              let q = connection[tenantId].execute.query(
                sql,
                [req.query.phrase, results.getId.client_id],
                (e, r, f) => {
                  if (e) cb(e);
                  else {
                    if (r && r.length) {
                      cb(null, {
                        htmlPath: r[0].advertisementHtml,
                      });
                    } else {
                      cb(null, { htmlPath: "" });
                    }
                  }
                }
              );
            } else {
              cb(null, { htmlPath: "" });
            }
          } else {
            cb(null, { htmlPath: "" });
          }
        },
      ],
    },
    (error, result) => {
      if (error) res.status(401).send({ error: "Invalid Session" });
      else res.send({ htmlString: result.getHtmlPath.htmlPath });
    }
  );
});

router.post("/saveMergedFilters", function (req, res, next) {
  connection[req.headers['tenant-id']].execute.query(
    `UPDATE search_clients SET merged_facets = ? WHERE uid = ?`,
    [req.body.mergedFilters, req.query.uid],
    (error, results) => {
      if (error) {
        console.error(error);
        res.sendStatus(500);
      } else {
        res.send(results);
      }
    }
  );
});

router.get("/getMergedFilters", (req, res, next) => {
  connection[req.headers['tenant-id']].execute.query(
    "select merged_facets from search_clients where uid = ?",
    [req.query.uid],
    (error, results) => {
      if (error) {
        console.error(error);
        res.sendStatus(500);
      } else {
        res.send(results[0] || "{}");
      }
    }
  );
});

const handleCustomBoosting = async (tenant_id,ab_test_id, search_client_id) => {
  return new Promise((resolve, reject) => {
    const selectQuery = `
      SELECT 
        custom_boosting_enabled,
        click_boosting_base,
        click_boosting_enabled,
        attached_articles_enabled,
        attached_articles_base,
        common_click_score_enabled
      FROM custom_boosting 
      WHERE search_client_id = ?
    `;

    connection[tenant_id].execute.query(
      selectQuery,
      [ab_test_id],
      (err, results) => {
        if (err) {
          commonFunctions.errorlogger.error('Error fetching custom boosting settings:', err);
          return reject(err);
        }

        // Default values for all fields
        const defaultValues = {
          custom_boosting_enabled: 0,
          title_boosting_factor: "1",
          solved_boosting_factor: "1",
          click_boosting_base: "10",
          offset: "30d",
          scale: "30d",
          decay_rate: "0.5",
          search_client_id: search_client_id,
          date_boosting_enabled: 0,
          click_boosting_enabled: 1,
          attached_articles_enabled: 1,
          attached_articles_base: "10",
          common_click_score_enabled: 1
        };

        if (results.length === 0) {
          commonFunctions.errorlogger.info('No Custom boosting found for the original search_client_id');
          return resolve({ message: 'No settings to clone' });
        }

        // If parent settings exist, override specific fields from parent
        if (results.length > 0) {
          const parentSettings = results[0];
          Object.assign(defaultValues, {
            custom_boosting_enabled: parentSettings.custom_boosting_enabled,
            click_boosting_base: parentSettings.click_boosting_base,
            click_boosting_enabled: parentSettings.click_boosting_enabled,
            attached_articles_enabled: parentSettings.attached_articles_enabled,
            attached_articles_base: parentSettings.attached_articles_base,
            common_click_score_enabled: parentSettings.common_click_score_enabled
          });
        }
        
        // Insert all fields, using combination of parent and default values
        const insertQuery = `
          INSERT INTO custom_boosting (
            custom_boosting_enabled,
            title_boosting_factor,
            solved_boosting_factor,
            click_boosting_base,
            offset,
            scale,
            decay_rate,
            search_client_id,
            date_boosting_enabled,
            click_boosting_enabled,
            attached_articles_enabled,
            attached_articles_base,
            common_click_score_enabled
          ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const insertValues = [
          defaultValues.custom_boosting_enabled,
          defaultValues.title_boosting_factor,
          defaultValues.solved_boosting_factor,
          defaultValues.click_boosting_base,
          defaultValues.offset,
          defaultValues.scale,
          defaultValues.decay_rate,
          defaultValues.search_client_id,
          defaultValues.date_boosting_enabled,
          defaultValues.click_boosting_enabled,
          defaultValues.attached_articles_enabled,
          defaultValues.attached_articles_base,
          defaultValues.common_click_score_enabled
        ];

        connection[tenant_id].execute.query(
          insertQuery,
          insertValues,
          (insertErr, insertResult) => {
            if (insertErr) {
              commonFunctions.errorlogger.error('Error inserting custom boosting settings:', insertErr);
              return reject(insertErr);
            }
            commonFunctions.errorlogger.info('Successfully created custom boosting settings with parent and default values');
            resolve(insertResult);
          }
        );
      }
    );
  });
};

const handleAutoTuningSettings = async (tenant_id, originalSearchClientId, newSearchClientId) => {
  return new Promise((resolve, reject) => {
    // Step 1: Retrieve existing auto_tuning_settings for the original search_client_id
    const retrieveQuery = `
      SELECT 
        auto_tuning_configuration_type,
        content_source_id,
        content_source_object_id,
        content_source_object_field_id,
        is_selected
      FROM auto_tuning_settings
      WHERE search_client_id = ?
    `;

    connection[tenant_id].execute.query(
      retrieveQuery,
      [originalSearchClientId],
      (err, results) => {
        if (err) {
          commonFunctions.errorlogger.error('Error fetching auto_tuning_settings:', err);
          return reject(err);
        }

        // If no settings exist for the original search_client_id, resolve immediately
        if (results.length === 0) {
          commonFunctions.errorlogger.info('No auto_tuning_settings found for the original search_client_id');
          return resolve({ message: 'No settings to clone' });
        }

        // Step 2: Prepare data for insertion into auto_tuning_settings for the new search_client_id
        const insertQuery = `
          INSERT INTO auto_tuning_settings (
            auto_tuning_configuration_type,
            content_source_id,
            content_source_object_id,
            content_source_object_field_id,
            search_client_id,
            is_selected
          ) VALUES ?
        `;

        // Map the retrieved data to the format required for the INSERT query
        const insertValues = results.map(setting => [
          setting.auto_tuning_configuration_type,
          setting.content_source_id,
          setting.content_source_object_id,
          setting.content_source_object_field_id,
          newSearchClientId, // Use the new search_client_id
          setting.is_selected
        ]);

        // Step 3: Insert the new settings for the new search_client_id
        connection[tenant_id].execute.query(
          insertQuery,
          [insertValues],
          (insertErr, insertResult) => {
            if (insertErr) {
              commonFunctions.errorlogger.error('Error inserting auto_tuning_settings:', insertErr);
              return reject(insertErr);
            }
            commonFunctions.errorlogger.info('Successfully cloned auto_tuning_settings for the new search_client_id');
            resolve(insertResult);
          }
        );
      }
    );
  });
};

const handleFieldBoosting = async (tenant_id, originalSearchClientId, newSearchClientId) => {
  return new Promise((resolve, reject) => {
    const retrieveQuery = `
      SELECT 
        name,
        content_source_object_field_id,
        sort_value,
        is_sort,
        boosting_factor
      FROM field_boosting
      WHERE search_client_id = ?
    `;

    connection[tenant_id].execute.query(
      retrieveQuery,
      [originalSearchClientId],
      (err, results) => {
        if (err) {
          commonFunctions.errorlogger.error('Error fetching field_boosting settings:', err);
          return reject(err);
        }

        if (results.length === 0) {
          commonFunctions.errorlogger.info('No field_boosting settings found for the original search_client_id');
          return resolve({ message: 'No settings to clone' });
        }

        const insertQuery = `
          INSERT INTO field_boosting (
            name,
            content_source_object_field_id,
            search_client_id,
            sort_value,
            is_sort,
            boosting_factor
          ) VALUES ?
        `;

        const insertValues = results.map(setting => [
          setting.name,
          setting.content_source_object_field_id,
          newSearchClientId, 
          setting.sort_value,
          setting.is_sort,
          setting.boosting_factor
        ]);


        connection[tenant_id].execute.query(
          insertQuery,
          [insertValues],
          (insertErr, insertResult) => {
            if (insertErr) {
              commonFunctions.errorlogger.error('Error inserting field_boosting settings:', insertErr);
              return reject(insertErr);
            }
            commonFunctions.errorlogger.info('Successfully cloned field_boosting settings for the new search_client_id');
            resolve(insertResult);
          }
        );
      }
    );
  });
};




let headerSentCleanUp = 0;
const updateInsertSearchClient = async function (results, platform, req, res, cb) {
  try {
    let clonnedScUid;

    if (req.body.ab_test_status === 0) {
      clonnedScUid = results.uid;
      const tenant_id = req.headers['tenant-id'];
     let childClonnedSearchData = await executeSearchClientInsert(platform, results, req, res, cb, clonnedScUid, tenant_id);
      await handleCustomBoosting(tenant_id,req.body.platformId ,childClonnedSearchData.insertId);
      await handleAutoTuningSettings(tenant_id,req.body.platformId,childClonnedSearchData.insertId);
      await handleFieldBoosting(tenant_id,req.body.platformId,childClonnedSearchData.insertId);
    } else {
      commonFunctions.errorlogger.info("ab_test_status is not 0, proceeding with normal insert");
      await executeSearchClientInsert(platform, results, req, res, cb, clonnedScUid, req.headers['tenant-id']);
    }
    
  } catch (error) {
    commonFunctions.errorlogger.error("Error details:", error);
    
    if (!res.headersSent) {
      try {
        // remove it after QA
        //  console.log("Starting cleanup with updateABTest======>>>>>>>>>555555555555555555555");
       await deleteScInCaseOfError(req, res, platform.cloneUid); // removing AB Test Search client if error occur 
       commonFunctions.errorlogger.info("Cleanup completed successfully");
        if(headerSentCleanUp === 0){
          headerSentCleanUp = 1;
          return res.status(200).send({ status: 4, err: "Error during search client creation/update" }); // sending status as 4 for AB Testing
        }
      } catch (cleanupError) {
        commonFunctions.errorlogger.error("Error during cleanup:", cleanupError);
      }
    }
    

  }
};

const deleteScInCaseOfError = async (req, res, uid) => {
  return new Promise((resolve, reject) => {
    req.query.clonnedAbTestSc = true;
    getPlatforms(req, (platforms, err) => {
      if (platforms && platforms.error) {
        commonFunctions.errorlogger.error("Error getting platforms:", platforms.error);
        reject({
          status: 500,
          message: "Failed to get search client data",
        });
        return;
      }
      const filteredPlatforms = platforms.message.filter(platform => platform.ab_test_parent === uid);
      if (filteredPlatforms.length === 0) {
        resolve({
          status: 200,
          message: "No matching platform found for the given ID"
        });
        return;
      }
        (async () => {
          try {
            const results = await Promise.all(
              filteredPlatforms.map(platform => {
                req.body.client = platform;
                req.body.clonnedScLength = filteredPlatforms.length;
                return deletePlatform(req, res, platform);
              })
            );
          }catch{
            commonFunctions.errorlogger.error("Error while deleting Search Client.");
            reject({
              status: 500,
              message: "Failed to get search client data",
            });
          }
        })();
      });
    });
  }

const executeSearchClientInsert = function (platform, results, req, res, cb, clonnedScUid, tenant_id) {
  return new Promise((resolve, reject) => {
    platform.created_by = JSON.stringify({ email: req.headers.session.email, name: req.headers.session.name });
    if (req.url.indexOf('editClient') == -1) {
      platform.label = isCloudFrontSupported ? 'CloudFront' : 'Docker';
    }

    let q = connection[tenant_id].execute.query(
      `INSERT INTO search_clients(id, label, name, search_client_type_id, uid, 
      DownloadFilePath, custom_css, client_href, recommendations,preview, knowledge_graph, 
      recent_searches, recent_searches_count,similar_search,special_search, did_you_mean, did_you_mean_dict_type,
      channel_id, jwt_href,jwt_expiry, pagination, autocomplete_instant, advertisements, language , redirection_url,autocomplete,languageManager,mergeSources, email,SCsalesforceConsoleConfigurations, ViewedResults,last_updated_by,relevancy, smart_facet, min_facet_selection, created_by, gpt, summarize, highlight,default_results_sorting, featured_snippet, gptRules, enableGPTConfigurations,gpt_feedback,sc_creating_status, scToggles, ab_test_parent, attachment_preview, isDisabled) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
      ON DUPLICATE KEY UPDATE id = VALUES(id), name = VALUES(name), client_href = VALUES(client_href),
       recommendations = VALUES(recommendations),preview=VALUES(preview), knowledge_graph = 
       VALUES(knowledge_graph), recent_searches = VALUES(recent_searches), recent_searches_count = 
       VALUES(recent_searches_count),similar_search = VALUES(similar_search),featured_snippet = VALUES(featured_snippet), special_search = VALUES(special_search), did_you_mean
        = VALUES(did_you_mean), did_you_mean_dict_type = VALUES(did_you_mean_dict_type),channel_id = 
        VALUES(channel_id),jwt_href = VALUES(jwt_href),jwt_expiry = VALUES(jwt_expiry),pagination = VALUES(pagination),autocomplete_instant = VALUES(autocomplete_instant),advertisements = VALUES(advertisements), language = VALUES(language), redirection_url = VALUES(redirection_url), autocomplete = VALUES(autocomplete), languageManager = VALUES(languageManager), mergeSources= VALUES(mergeSources), SCsalesforceConsoleConfigurations = VALUES(SCsalesforceConsoleConfigurations), ViewedResults = VALUES(ViewedResults), last_updated_by = VALUES(last_updated_by), relevancy = VALUES(relevancy), smart_facet = VALUES(smart_facet), min_facet_selection = VALUES(min_facet_selection), gpt = VALUES(gpt),summarize = VALUES(summarize),highlight=VALUES(highlight), default_results_sorting = VALUES(default_results_sorting), gptRules = VALUES(gptRules), enableGPTConfigurations = VALUES(enableGPTConfigurations), gpt_feedback = VALUES(gpt_feedback),sc_creating_status = VALUES(sc_creating_status),  scToggles = VALUES(scToggles) , ab_test_parent = VALUES(ab_test_parent), attachment_preview = VALUES(attachment_preview), isDisabled = VALUES(isDisabled)`,
      [
        platform.id, 
        platform.label, 
        req.body.ab_test_status === 0 ? platform.name + clonnedScUid : platform.name, 
        platform.search_client_type_id, 
        results.uid,
        "resources/search_clients_custom/" + results.uid + "/", 
        results.readCss ? results.readCss.toString() : '', 
        platform.client_href, 
        platform.recommendations, 
        platform.preview ? platform.preview : 0, 
        platform.knowledge_graph ? platform.knowledge_graph : 0, 
        platform.recent_searches ? platform.recent_searches : 0, 
        platform.recent_searches_count ? platform.recent_searches_count : 0, 
        platform.similar_search ? platform.similar_search : 0, 
        platform.special_search ? platform.special_search : 0, 
        platform.did_you_mean ? platform.did_you_mean : 0, 
        platform.did_you_mean_dict_type ? platform.did_you_mean_dict_type : 0, 
        platform.channel_id, 
        platform.jwt_href, 
        platform.jwt_expiry, 
        platform.pagination, 
        platform.autocomplete_instant, 
        platform.advertisements, 
        platform.language, 
        platform.redirection_url, 
        platform.autocomplete ? platform.autocomplete : '10', 
        platform.languageManager, 
        platform.mergeSources, 
        platform.email, 
        platform.SCsalesforceConsoleConfigurations ? platform.SCsalesforceConsoleConfigurations : '{"caseSelection":1,"caseNumberView":1,"searchResultsOpensNewBrowserTab":1,"isNewOrSameEmailThread":0}', 
        platform.ViewedResults ? platform.ViewedResults : 0, 
        platform.currentUser ? platform.currentUser : '', 
        platform.relevancy ? platform.relevancy : '{"searchOperator":"OR","advanceQuery":[]}', 
        platform.smart_facet ? platform.smart_facet : 0,
        platform.min_facet_selection ? platform.min_facet_selection : 10, 
        platform.created_by, 
        platform.gpt,
        platform.summarize, 
        platform.highlight, 
        platform.default_results_sorting, 
        platform.featured_snippet ? platform.featured_snippet : 0, 
        platform.gptRules, 
        platform.enableGPTConfigurations, 
        platform.gpt_feedback ? platform.gpt_feedback : 0, 
        platform.sc_creating_status,
        platform.scToggles ? platform.scToggles : '{"htAccess": 0}',
        req.body.ab_test_status === 0
        ? platform.cloneUid
        : platform.ab_test_parent
        ? platform.ab_test_parent
        : null,
        platform.attachment_preview ? platform.attachment_preview : 0 ,
        !!platform.isDisabled
      ],
      async (error, result) => {
        if (req.body.ab_test_status === 0) {
          if (error) {
            commonFunctions.errorlogger.error("Error in search client insert/update:", error);
            reject(error);
          } else {
            commonFunctions.errorlogger.info("Successfully inserted/updated search client");
            cb(null, result);
            resolve(result);
          }
        } else {
          if (error) {
            commonFunctions.errorlogger.error("Error in normal search client operation:", error);
            commonFunctions.errorlogger.error("error", error);
            cb(error, result);
            resolve(result);
          } else {
            commonFunctions.errorlogger.info("Successfully completed normal search client operation");

            // Step 1: Fetch child search clients
            const checkAbTestSql = `
              SELECT uid 
              FROM search_clients 
              WHERE ab_test_parent = ?
            `;

            connection[tenant_id].execute.query(
              checkAbTestSql,
              [platform.uid], // Use the parent search client ID
              (abTestError, abTestResults) => {
                if (abTestError) {
                  commonFunctions.errorlogger.error("AB Test check error:", abTestError);
                  cb(abTestError, null);
                  resolve(null);
                  return;
                }

                // Step 2: If child search clients exist, update them
                if (abTestResults && abTestResults.length > 0) {
                  let completedUpdates = 0;
                  let updateErrors = [];

                  abTestResults.forEach((child) => {
                    const updateChildSql = `
                      UPDATE search_clients 
                      SET 
                        client_href = ?, 
                        recommendations = ?,
                        rec_widget = ?,
                        rec_widget_regex = ?,
                        rec_widget_redirect_url = ?,
                        preview = ?, 
                        knowledge_graph = ?, 
                        recent_searches = ?, 
                        recent_searches_count = ?, 
                        similar_search = ?, 
                        featured_snippet = ?, 
                        did_you_mean = ?, 
                        did_you_mean_dict_type = ?, 
                        channel_id = ?, 
                        jwt_href = ?, 
                        jwt_expiry = ?, 
                        pagination = ?, 
                        autocomplete_instant = ?, 
                        advertisements = ?, 
                        language = ?, 
                        redirection_url = ?, 
                        autocomplete = ?, 
                        languageManager = ?, 
                        mergeSources = ?, 
                        SCsalesforceConsoleConfigurations = ?, 
                        ViewedResults = ?, 
                        last_updated_by = ?, 
                        smart_facet = ?, 
                        min_facet_selection = ?, 
                        gpt = ?, 
                        highlight = ?, 
                        default_results_sorting = ?, 
                        gptRules = ?, 
                        enableGPTConfigurations = ?, 
                        gpt_feedback = ?

                      WHERE uid = ?
                    `;

                    connection[tenant_id].execute.query(
                      updateChildSql,
                      [
                        platform.client_href, 
                        platform.recommendations, 
                        platform.rec_widget,
                        platform.rec_widget_regex,
                        platform.rec_widget_redirect_url,
                        platform.preview, 
                        platform.knowledge_graph, 
                        platform.recent_searches, 
                        platform.recent_searches_count, 
                        platform.similar_search, 
                        platform.featured_snippet, 
                        platform.did_you_mean, 
                        platform.did_you_mean_dict_type, 
                        platform.channel_id, 
                        platform.jwt_href, 
                        platform.jwt_expiry, 
                        platform.pagination, 
                        platform.autocomplete_instant, 
                        platform.advertisements, 
                        platform.language, 
                        platform.redirection_url, 
                        platform.autocomplete, 
                        platform.languageManager, 
                        platform.mergeSources, 
                        platform.SCsalesforceConsoleConfigurations, 
                        platform.ViewedResults, 
                        platform.currentUser, 
                        platform.smart_facet, 
                        platform.min_facet_selection, 
                        platform.gpt, 
                        platform.highlight, 
                        platform.default_results_sorting, 
                        platform.gptRules, 
                        platform.enableGPTConfigurations, 
                        platform.gpt_feedback, 
                        child.uid
                      ],
                      (updateError, updateResult) => {
                        if (updateError) {
                          updateErrors.push(updateError);
                        }
                        completedUpdates++;

                        // Check if all updates are done
                        if (completedUpdates === abTestResults.length) {
                          if (updateErrors.length > 0) {
                            commonFunctions.errorlogger.error("Errors updating child search clients:", updateErrors);
                            cb(updateErrors, null);
                            resolve(null);
                          } else {
                            commonFunctions.errorlogger.info("Successfully updated all child search clients");
                            cb(null, result);
                            resolve(result);
                          }
                        }
                      }
                    );
                  });
                } else {
                  // No child search clients found
                  cb(null, result);
                  resolve(result);
                }
              }
            );
          }
        }
      }
    );
  });
};

const getScId =  function (uid, req) {
  return new Promise((resolve, reject) => {
    connection[req.headers['tenant-id']].execute.query(
      `select id from search_clients where uid=?`,
      [uid],
      (error, result) => {
        if (error) {
          return reject(error);
        } else {
          return resolve(result);
        }
      }
    );
  });
}

const updateDeflectionFormulaTable = function (results, req, cb) {
  if (results) {
    connection[req.headers['tenant-id']].execute.query(
      `INSERT INTO deflection_formula(search_client_id) values(?)`,
      [results.insert.insertId],
      (error, result) => {
        if(error) {
          commonFunctions.errorlogger.error("error", error);
        }
        cb(error, result);
      }
    );
  }
};

const insertSCSSOConfig = function (results, platform, req, cb) {
  if(!req.body.restoreUid) {
    connection[req.headers['tenant-id']].execute.query(
      `INSERT INTO search_client_sso_config(search_client_id, sc_uid, sso_config) values(?, ?, ?)`,
      [results.insert.insertId, results.getUUID, platform.ssoConfig],
      (error, result) => {
        if(error) {
          commonFunctions.errorlogger.error("error", error);
        }

        getSCSSOConfig(results.getUUID, req, cb);
      }
    );
  } else {
    getSCSSOConfig(results.getUUID, req, cb);
  }
};


const getSCSSOConfig = function (uid, req, cb) {
  connection[req.headers['tenant-id']].execute.query('SELECT sso_config FROM search_client_sso_config WHERE sc_uid = ?', [uid], (selectError, selectResults) => {
    if (selectError) {
      commonFunctions.errorlogger.error("error", selectError)
      cb(selectError, '');
    }

    cb(null, selectResults[0] && selectResults[0].sso_config);
  });
};

const getSearchClient = function (platformId,req, cb) {
  let settings = {};
  async.auto(
    {
      one: (cb) => {
        let sql = `SELECT
            cs.id    AS cs_id,
            cs.label AS cs_label,
            cs.elasticIndexName AS indexName,
            cs.content_source_type_id AS content_source_type_id,
            cs.sort_order AS sort_order,

            cso.base_url          AS cso_base_url,
            cso.content_source_id AS cso_content_source_id,
            cso.id                AS cso_id,
            cso.label             AS cso_label,
            cso.name              AS cso_name,
            cso.status            AS cso_status,
            cso.internal_access   AS cso_internal_access,
            cso.external_access   AS cso_external_access,
            csof.content_source_object_id AS csof_content_source_object_id,
            csof.id                       AS csof_id,
            csof.isActive                 AS csof_isActive,
            csof.isFilterable             AS csof_isFilterable,
            csof.isSearchable             AS csof_isSearchable,
            csof.isSortable               AS csof_isSortable,
            csof.label                    AS csof_label,
            csof.name                     AS csof_name,
            csof.type                     AS csof_type,
            csof.fragment_size            AS fragment_size,
            csof.merge_field_id           AS mergeFieldId
          FROM
            content_sources AS cs,
            content_source_objects AS cso,
            content_source_object_fields AS csof
          WHERE
            cs.id = cso.content_source_id
            AND cso.id = csof.content_source_object_id
          ORDER BY cs_label,csof_name ASC`;
        connection[req.headers['tenant-id']].execute.query(sql, (e, r, f) => {
          if (e) cb(e);
          else {
            settings.sources = [];
            r.forEach((e) => {
              let a = settings.sources.find((x) => {
                return x.id == e.cs_id;
              });
              if (!a) {
                a = {
                  id: e.cs_id,
                  label: e.cs_label,
                  enabled: false,
                  objects: [],
                  index: e.indexName,
                  content_source_type_id: e.content_source_type_id,
                  sort_order: e.sort_order,
                };
                settings.sources.push(a);
              }
              let b = a.objects.find((x) => {
                return x.id == e.cso_id;
              });
              if (!b) {
                b = {
                  base_url: e.cso_base_url,
                  content_source_id: e.cso_content_source_id,
                  id: e.cso_id,
                  label: e.cso_label,
                  name: e.cso_name,
                  status: e.cso_status,
                  enabled: false,
                  internal_access: e.cso_internal_access,
                  external_access: e.cso_external_access,
                  fields: [],
                };
                a.objects.push(b);
              }
              b.fields.push({
                content_source_object_id: e.csof_content_source_object_id,
                id: e.csof_id,
                isActive: e.csof_isActive,
                isFilterable: e.csof_isFilterable,
                isSearchable: e.csof_isSearchable,
                isSortable: e.csof_isSortable,
                label: e.csof_label,
                name: e.csof_name,
                type: e.csof_type,
                fragment_size: e.fragment_size,
                mergeFieldId: e.mergeFieldId,
                use: {},
              });
            });
            cb();
          }
        });
      },
      two: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT * from search_clients_to_content_objects where search_client_id=?`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              cb(null, r);
            }
          }
        );
      },
      field_mapping: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT
            scf.content_source_object_field_id      AS scf_content_source_object_field_id,
            scf.id                                  AS scf_id,
            scf.priority                            AS scf_priority,
            scf.search_clients_to_content_object_id AS scf_search_clients_to_content_object_id,
            scf.use_as                              AS scf_use_as,
            scf.exclude                             AS scf_exclude,
            scf.search_priority                     AS scf_search_priority,
            scf.use_value                           AS scf_use_value,
            scf.sort_by                             AS scf_sort_by,
            scf.order_by                            AS scf_order_by,
            scf.summary_length                      AS scf_summary_length,
            scf.level_limit                         AS scf_level_limit,
            scf.extra_field                         AS scf_extra_field,
            scf.track_analytics                     AS scf_track_analytics,
            scf.auto_learning                       AS scf_auto_learning,
            scf.facet_type                           AS scf_facet_type,
            scf.facet_data_type                       AS scf_facet_data_type
        FROM search_clients_to_content_objects AS sctco,
            search_clients_filters AS scf
        WHERE
            sctco.search_client_id = ?
            AND sctco.id = scf.search_clients_to_content_object_id`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              r = r.map((en) => {
                return {
                  content_source_object_field_id:
                    en.scf_content_source_object_field_id,
                  id: en.scf_id,
                  priority: en.scf_priority,
                  search_clients_to_content_object_id:
                    en.scf_search_clients_to_content_object_id,
                  use_as: en.scf_use_as,
                  exclude: en.scf_exclude,
                  search_priority: en.scf_search_priority,
                  use_value: en.scf_use_value,
                  sort_by: en.scf_sort_by,
                  order_by: en.scf_order_by,
                  summary_length: en.scf_summary_length,
                  level_limit: en.scf_level_limit,
                  extra_field: en.scf_extra_field,
                  track_analytics: en.scf_track_analytics,
                  auto_learning:en.scf_auto_learning,
                  facet_type: en.scf_facet_type,
                  facet_data_type: en.scf_facet_data_type
                };
              });
              cb(null, r);
            }
          }
        );
      },
      metadata_mapping: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT
            scmf.field_id      AS scmf_content_source_object_field_id,
            scmf.id                                  AS scmf_id,
            scmf.priority                            AS scmf_priority,
            scmf.search_client_to_content_object_id AS scmf_search_clients_to_content_object_id,
            scmf.autosuggestField AS scmf_autosuggestField,
            scmf.metaData AS scmf_metaData
        FROM search_clients_to_content_objects AS sctco,
            search_client_metadata_fields AS scmf
        WHERE
            sctco.search_client_id = ?
            AND sctco.id = scmf.search_client_to_content_object_id`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              r = r.map((en) => {
                return {
                  content_source_object_field_id:
                    en.scmf_content_source_object_field_id,
                  id: en.scmf_id,
                  priority: en.scmf_priority,
                  search_clients_to_content_object_id:
                    en.scmf_search_clients_to_content_object_id,
                  autosuggestField: en.scmf_autosuggestField,
                  metaData: en.scmf_metaData,
                  use_as: en.scmf_metaData == 1 ? "Metadata" : "",
                };
              });
              cb(null, r);
            }
          }
        );
      },
      preview_mapping: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT
            scpf.field_id      AS scpf_content_source_object_field_id,
            scpf.id                                  AS scpf_id,
            scpf.search_client_to_content_object_id AS scpf_search_clients_to_content_object_id,
            scpf.sc_id AS scpf_sc_id,
            scpf.preview_order AS scpf_preview_order
        FROM search_clients_to_content_objects AS sctco,
            search_client_preview_fields AS scpf
        WHERE
            sctco.search_client_id = ?
            AND sctco.id = scpf.search_client_to_content_object_id`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              r = r.map((en) => {
                return {
                  content_source_object_field_id:
                    en.scpf_content_source_object_field_id,
                  id: en.scpf_id,
                  search_clients_to_content_object_id:
                    en.scpf_search_clients_to_content_object_id,
                  preview_order: en.scpf_preview_order,
                  sc_id: en.scpf_sc_id
                };
              });
              cb(null, r);
            }
          }
        );
      },
      client: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT *,sc.id searchId,tt.id,sc.name scName,tt.name tname FROM search_clients sc LEFT JOIN template_types tt ON sc.template_type_id = tt.id WHERE sc.id=? LIMIT 1`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else {
              if (r && r.length) {
                r[0].id = r[0].searchId;
                r[0].name = r[0].scName;
                r[0].maxSummaryLimit = parseInt(config.get("maxSummaryLimit") || 500);
                if (r[0].agentHelperSlackCreds) {
                  let slackCreds = JSON.parse(r[0].agentHelperSlackCreds || "{}");
                  if (slackCreds.clientId && slackCreds.clientId.length) 
                    slackCreds.clientId = commonFunctions.encryptDecryptCreds(slackCreds.clientId, 'decrypt');
                  else slackCreds.clientId = '';
                  slackCreds.clientSecret = '';
                  r[0].agentHelperSlackCreds = JSON.stringify(slackCreds);
                }
                cb(null, r);
              } else cb(null, r);
            }
          }
        );
      },
      deflection_formula: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT * FROM deflection_formula WHERE search_client_id=?`,
          [platformId],
          (e, r, f) => {
            if (e) cb(e);
            else cb(null, r);
          }
        );
      },
      getAddonsStatus: (cb) => {
        commonFunctions.getAddonsStatus(req, (error, result) => {
          if (result && result[7]) {
            cb(null, result[7].is_installed);
          } else {
            cb(null, 0);
          }
        });
      },
      analyticsReports: (cb) => {
        let sql = `SELECT * FROM analytics_reports LEFT JOIN (SELECT * FROM search_client_to_analytics_report WHERE search_client_to_analytics_report.search_client_id = ?) AS sctar ON analytics_reports.id = sctar.analytics_report_id WHERE (analytics_reports.is_dashboard=0)`;
        connection[req.headers['tenant-id']].execute.query(
          {
            sql: sql,
            nestTables: true,
          },
          [platformId],
          (error, rows) => {
            cb(error, rows);
          }
        );
      },
      boostField: (cb) => {
        getFieldBoosting(cb, req,(e, r) => {
          if (e) cb(e);
          else {
            cb(null, r);
          }
        });
      },
      getVectorProgress: ['client', (dataFromAbove, cb) => {

        if (dataFromAbove.client && dataFromAbove.client[0] && dataFromAbove.client[0].ab_test_parent) {
            req.body.uid = dataFromAbove.client[0].ab_test_parent;
        } else {
            req.body.uid = dataFromAbove.client[0].uid;
        }

        getMeanVectorProgress(req, (err, result) => {
          if (err) cb(null, {});
          else cb(null, result);
        });
      }],
      slackChannels: [
        "client",
        (results, cb) => {
          if (results["client"].length) {
            if (
              results["client"] &&
              results["client"][0].search_client_type_id == 22
            ) {
              connection[req.headers['tenant-id']].execute.query(
                `SELECT channel_id FROM search_clients WHERE channel_id IS NOT NULL`,
                (e, r, f) => {
                  if (e) cb(e);
                  else {
                    cb(null, r);
                  }
                }
              );
            } else cb(null, []);
          } else cb(null, []);
        },
      ],
      three: [
        "one",
        "two",
        "field_mapping",
        "metadata_mapping",
        "preview_mapping",
        "deflection_formula",
        "client",
        "analyticsReports",
        "boostField",
        "getAddonsStatus",
        "slackChannels",
        "getVectorProgress",
        (rs, cb) => {
          settings.sources.forEach((src) => {
            src.objects.forEach((obj) => {
              obj.enabled = rs.two.find((x) => {
                return x.content_source_object_id == obj.id;
              })
                ? true
                : false;
              if (obj.enabled) {
                obj.title_field_id = rs.two.find((x) => {
                  return x.content_source_object_id == obj.id;
                }).title_field_id;
                obj.base_href = rs.two.find((x) => {
                  return x.content_source_object_id == obj.id;
                }).base_href;
                obj.compose_title = rs.two.find((x) => {
                  return x.content_source_object_id == obj.id;
                }).compose_title;
                obj.merge_results = rs.two.find((x) => {
                  return x.content_source_object_id == obj.id;
                }).merge_results;
                obj.formula = rs.two.find((x) => {
                  return x.content_source_object_id == obj.id;
                }).formula;
                obj.icon = rs.two.find((x) => {
                  return x.content_source_object_id == obj.id;
                }).icon;
                obj.search_clients_to_content_object_id =
                  rs.two.find((x) => {
                    return x.content_source_object_id == obj.id;
                  }).id || null;
                (obj.boosting_factor =
                  rs.two.find((x) => {
                    return x.content_source_object_id == obj.id;
                  }).boosting_factor || 1),
                  (src.enabled = true);
                obj.permission_by_pass =
                  rs.two.find((x) => {
                    return x.content_source_object_id == obj.id;
                  }).permission_by_pass || 0;
                obj.merge_results =
                  rs.two.find((x) => {
                    return x.content_source_object_id == obj.id;
                  }).merge_results || "";
                  obj.preview_type =
                rs.two.find((x) => {
                  return x.content_source_object_id == obj.id;
                }).preview_type || 1;
              }
              obj.fields.forEach((f) => {
                f.title = obj.title_field_id == f.id ? true : false;
                f.use =
                  rs.field_mapping.find((x) => {
                    return x.content_source_object_field_id == f.id;
                  }) || {};
                f.metadata =
                  rs.metadata_mapping.find((x) => {
                    return x.content_source_object_field_id == f.id;
                  }) || {};
                f.preview =
                  rs.preview_mapping.find((x) => {
                    return x.content_source_object_field_id == f.id;
                  }) || {};
                if (rs.boostField) {
                  rs.boostField.map((boost) => {
                    if (f.id === boost.content_source_object_field_id) {
                      f.field_boosting_value = boost.boosting_value;
                      f.field_boosting_id = boost.id;
                    }
                  });
                }
              });
            });
          });
          let caseDeflectionIndex = 0;
          rs.analyticsReports = rs.analyticsReports.map((report, index) => {
            if (report.sctar.id === null) {
              report.sctar.search_client_id = platformId;
              report.sctar.analytics_report_id = report.analytics_reports.id;
              report.sctar.is_enabled = false;
              report.sctar.label = report.analytics_reports.name;
            }
            report.sctar.name = report.analytics_reports.name;
            report.sctar.description = report.analytics_reports.description;
            report.sctar.version = report.analytics_reports.version;
            report.sctar.sort_order = report.analytics_reports.sort_order;
            if (!rs.getAddonsStatus && report.sctar.analytics_report_id == 14) {
              caseDeflectionIndex = index;
            } else {
              return report.sctar;
            }
          });
          rs.analyticsReports = rs.analyticsReports.filter((x) => x);
          settings.enabledObjects = rs.two;
          settings.client = rs.client[0];
          settings.field_mapping = rs.field_mapping;
          settings.deflection_formula = rs.deflection_formula[0] || {};
          settings.analyticsReports = rs.analyticsReports;
          let updated_uid_mov = settings.client.uid;  
          if(settings.client.ab_test_parent ){
            updated_uid_mov = settings.client.ab_test_parent;
          }
          settings.vectorProgress = rs.getVectorProgress && rs.getVectorProgress.scProgress && rs.getVectorProgress.scProgress[updated_uid_mov] ? rs.getVectorProgress.scProgress[updated_uid_mov] : {} ;
          settings.tenantISConfig = (rs.getVectorProgress && rs.getVectorProgress.tenantISConfig) || {};
          // send value of dataRetention to frontend
          settings.dataRetention = config.get('relevanceScore.dataRetention');
          // Determine search type
          const vectorSearchSettings = settings.client.vector_search_settings ? JSON.parse(settings.client.vector_search_settings) : {};
          const searchType =
          vectorSearchSettings.hybridSearchEnabled
            ? SEARCH_TYPE.HYBRID
            : settings.client.vector_search_enabled
            ? SEARCH_TYPE.VECTOR
            : SEARCH_TYPE.KEYWORD;
        
          try {
          // Parse relevancy settings
          let relevancySettings = JSON.parse(settings.client.relevancy);
          // Check if relevancy score should be added and update if necessary
          if (!relevancySettings.relevancyScores) {
              relevancySettings.relevancyScores = {
                  enabled: 0,
                  dataRetention: settings.dataRetention,
                  searchType: searchType
              };
          }
          // Update relevancy settings
          settings.client.relevancy = JSON.stringify(relevancySettings)
          commonFunctions.errorlogger.info('parsing the relevancy settings')
          } catch (e) {
            commonFunctions.errorlogger.error('Error in relevancy settings ', e)
          }
          if (rs.slackChannels.length)
            settings.slackChannels = rs.slackChannels;
          cb(null, settings);
        },
      ],
    },
    (error, result) => {
      if (error){
          if(req.body.ab_test_status === 0){
            commonFunctions.errorlogger.error("Error====================>>>>>>>>>>>>>>>>>>>>>>",error);
            (async () => {
              try {
                // remove it after QA
                // console.log("Starting cleanup with updateABTest======>>>>>>>>>6666666666666666666666");
                let searchClientsCleanUpData = await deleteScInCaseOfError(req, res, req.body.uid);
                commonFunctions.errorlogger.info("Cleanup completed successfully",searchClientsCleanUpData);
                if(headerSentCleanUp === 0){
                  headerSentCleanUp = 1;
                  return res.status(200).send({ status: 4, err: "Error during search client creation/update" }); // sending status as 4 for AB Testing
                }
              } catch (cleanupError) {
                commonFunctions.errorlogger.error("Error during cleanup:", cleanupError);
              }
            })();
          }else{
            commonFunctions.errorlogger.error("error", error);
          }
      }else{
        cb(null, settings);
      }
    }
  );
};

router.get("/getSearchFieldSettings", (req, res, next) => {
  connection[req.headers['tenant-id']].execute.query(`select
      cs.elasticIndexName as index_name,
      cs.last_sync_date,
      cs.sync_start_date,
      cso.name as object_name,
      csof.name as field_name
    from
      search_clients_filters scf
    left join content_source_object_fields csof on
      csof.id = scf.content_source_object_field_id
    left join content_source_objects cso on
      cso.id = csof.content_source_object_id
    left join content_sources cs on
      cs.id = cso.content_source_id
    where
      scf.use_as in ('Search',
      'SearchSummary')
    and cs.elasticIndexName != ''
    group by
      csof.name,
      cso.name,
      cs.elasticIndexName ;`,
    [req.query.uid],
    (error, results) => {
      if (error) {
        console.error(error);
        res.sendStatus(500);
      } else {
        res.send(results || "{}");
      }
    }
  );
});

router.post('/getContentSourceFields',async (req, res, next) => { 
  try {
    if (!req.body.id || !req.body.cso_name) {
      throw "ContentSourceId Or cso_name is missing"
    }
    
  const data = await commonFunctions.getContentSourceFields(req.body.id, req.body.cso_name,req)
    res.send(data);
   } catch (err) { 
    next(err)
  }
})

/**
 * sameNameCheck - checks if SC with same name already exists
 * @param {object} name - SC name to add
 * @param {object} tenantId - tenant id (for mysql connection)
 * @returns {Promise} - promisified connection execution
 */
const sameNameCheck = (name, tenantId) => {
  return new Promise((resolve, reject)=>{
    connection[tenantId].execute.query("select * from search_clients where name=?",[name],(err, rows)=>{
      if(err || (rows && rows.length)){
        return resolve(true);
      }
      resolve(false);
    })
  })
}

const addSearchClient = function (platform, req, res, cb1) {
  async.auto(
    {
      getUUID: (cb) => {
        if(!platform.customUID) {
          connection[req.headers['tenant-id']].execute.query("SELECT UUID() AS uid", function (error, result) {
            let sendResult = platform.restoreUid
            ? platform.cloneUid
            : result[0].uid;
            cb(error, sendResult);
          });
        } else {
          cb(null, platform.customUID);
        }
      },
      insert: [
        "getUUID",
        (results, cb) => {
          results.uid = results.getUUID;
          if(!req.body.restoreUid) {
            updateInsertSearchClient(results, platform, req, res, cb);
          } else {
            const sql = `UPDATE search_clients SET sc_creating_status = 1 WHERE id = ? AND uid = ?;`;
            connection[req.headers['tenant-id']].execute.query(sql, [platform.id, results.uid], (error, result) => {
              if (error) {
                commonFunctions.errorlogger.error(`Failed to get search client created status`, error);
                cb(error, '');
              }
              cb(null, '');
            });
          }
        },
      ],
      deflection: [
        "insert",
        (results, cb) => {
          !req.body.restoreUid ? updateDeflectionFormulaTable(results, req, cb) : cb(null, '');
        },
      ],
      getClientType: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          `SELECT * from search_client_types WHERE id=?`,
          [platform.search_client_type_id],
          cb
        );
      },
      sso_config: [
        "insert",
        "getUUID",
        (results, cb) => {
          platform.search_client_type_id === 31 ? insertSCSSOConfig(results, platform, req, cb) : cb(null, '');
        },
      ],
      "accessToken": async (cb) => {
        try{
          const data = await getAccessTokenFromTenantId(req.headers['tenant-id']);
          if(data.accessToken === ''){
            throw new Error('Access token not found');
          }
          return data.accessToken; 
        }catch(error){
          throw new Error('Access token not found');
        }
      },
      copyFolder: [
        "getUUID",
        "insert",
        "accessToken",
        "getClientType",
        "sso_config",
        (results, cb) => {
          let options = {};
         let obj = {
            task: "addPlatform",
            updatedBy: req.headers.session,
            searchClientRegionStatus: true,
            data: {
              getHosted: results.getHosted,
              platform: platform,
              language: platform.language,
              uid: results.getUUID,
              accessToken: results.accessToken,
              clientInfo: results.getClientType,
              tenantId : req.headers['tenant-id'],
              environment: config.get('myAccountInstanceVariable'),
              migrate: platform.migrate ? 1 : 0,
              ssoConfig: results.sso_config,
              isABTestEnabled: false,
              resetSCOption: req.body.resetSCOption || {
                value: '1',
                sourceUid: ''
              },
              scSqlId: results.insert.insertId || platform.id || ''
            },
            tenantId : req.headers['tenant-id']
          };
          try {
            kafkaLib.publishMessage({
              topic: config.get("kafkaTopic.searchClientTopic"),
              messages: [
                {
                  value: JSON.stringify(obj),
                  key: results.getUUID
                },
              ],
            });
            if(config.get('statusPageService.kafka.host') !== config.get('kafkaTopic.host')){
              kafkaStatusLib.publishMessage({
                topic: config.get("statusPageService.kafka.searchClientTopic"),
                messages: [
                  {
                    value: JSON.stringify(obj),
                    key: results.getUUID
                  },
                ],
              });
            }
            console.log(
              "add instruction is sent to Search-client Service by Kafka  with data ",
              JSON.stringify(obj)
            );

            let objData = {
              data: [{
                uid: results.getUUID,
                tenantId: req.headers['tenant-id']
              }]
            };
            // Publishing uid and tenant to save uid-tenant relation in auth service
            kafkaLib.publishMessage({
              topic: config.get("kafkaTopic.tenantUidTopic"),
              messages: [
                {
                  value: JSON.stringify(objData),
                  key: results.getUUID
                },
              ],
            });
            console.log(
              "KAFKA TOPIC PUBLISH",
              JSON.stringify(objData)
            );
            
          } catch (e) {
            console.log(
              "Error while sending instruction to Search-client Service by Kafka => ",
              e
            );
          }
          cb();
        },
      ],
      insertCaseDeflectionLabels: [
        "getUUID",
        (results, cb) => {
          let query =
            "INSERT INTO analytics_cd_labels (uid ,report_title_name ,custom_title_name ,tooltip ) VALUES" +
            `(?,'Search Sessions (stage1)', 'Search Sessions', 'Number of sessions in which <br />users visited the search page.' ),` +
            `(?,'Click Sessions (stage1)', 'Click Sessions', 'Successful Searches Who didn\\\'t Visit Support' ),` +
            `(?,'No Clicks Sessions (stage1)', 'No Clicks Sessions', 'Unsuccessful Searches' ),` +
            `(?,'Exit (Stage1)', 'Exit', 'Same as Total Sessions.' ),` +
            `(?,'Total Sessions (Stage1)', 'Total Sessions', 'Total number<br />of unique sessions.' ),` +
            `(?,'No Search Sessions (Stage1)', 'No Search Sessions', 'Number of sessions in which <br/>users didn\\\'t visit the search page.' ),` +
            `(?,'Support Sessions (Stage1)', 'Support Sessions', 'Number of sessions in which users <br />visited the support page.' ),` +
            `(?,'Search Sessions (stage2)', 'Search Sessions', 'Number of sessions in which <br />users visited the search page.' ),` +
            `(?,'Support Sessions (Stage2)', 'Support Sessions', 'Number of sessions in which users <br />visited the support page.' ),` +
            `(?,'No Search Sessions (Stage2)', 'No Search Sessions', 'Number of sessions in which users <br />visited the support page <br />and did not search.' ),` +
            `(?,'Click Sessions (Stage2)', 'Click Sessions', 'Number of sessions in which users <br />searched and clicked on results.' ),` +
            `(?,'No Click Sessions (Stage2)', 'No Click Sessions', 'Number of sessions in which users <br />searched but did not click.' ),` +
            `(?,'Case Created Sessions (Stage2)', 'Case Created Sessions', 'Number of sessions in which users <br />created a case.' ),` +
            `(?,'Exit (Stage2)', 'Exit', 'Number of sessions who left the support <br />or have no activity on support' ),` +
            `(?,'Direct Pageview', 'Direct Pageview', 'Number of sessions who directly viewed the results' )`;

          let array = [];
          while (array.length < 15) {
            array.push(results.getUUID);
          }

          connection[req.headers['tenant-id']].execute.query(query, array, cb);
        },
      ]
    },
    
    (error, result) => { 
      if (error) {
        const scSqlId = result.insert && result.insert.insertId;
        const uid = result.uid;
        commonFunctions.errorlogger.error("error", error);
        if(req.body.ab_test_status === 0){
          (async () => {
            try {
             // remove it after QA
             // console.log("Starting cleanup with updateABTest=====================>>>77777777777777");
              let searchClientsCleanUpData = await deleteScInCaseOfError(req, res, req.body.uid);
              commonFunctions.errorlogger.info("Cleanup completed successfully",searchClientsCleanUpData);
              if(headerSentCleanUp === 0){
                headerSentCleanUp = 1;
                return res.status(200).send({ status: 4, err: "Error during search client creation/update" }); // sending status as 4 for AB Testing
              }
            } catch (cleanupError) {
              commonFunctions.errorlogger.error("Error during cleanup:", cleanupError);
            }
          })();
        }else{
          if(scSqlId && uid){
            const sql = `UPDATE search_clients SET sc_creating_status = 2 WHERE id = ? AND uid = ?;`;
            connection[req.headers['tenant-id']].execute.query(sql, [scSqlId, uid], (error, result) => {
              if (error) {
                commonFunctions.errorlogger.error(`Failed to update search client failed created status`, error);
              }
              commonFunctions.errorlogger.info(`-------------------- Failed to create search client --------------------`);
            });
          }
        }
        cb1(error, null);
      } else {
        cb1(null, result);
      }
    }
  );
};

const analyticsKafka = async function (uid, req) {
  getScId(uid, req).then(
    result => {
      if (result[0] && result[0].id && uid) {
        scId = result[0].id;
        let configObj = {
          platformId: scId,
          uid: uid
        };
        searchResultKafkaConfig.getSearchClientSettingsViaKafka(
          configObj, req,
          function (err, searchConfig) {
            console.log("Publish Kafka for analytics");
          }
        );
      }
    }, error => {
      console.log("Error: " + error);
    }
  );
};

const updateSearchClient = function (parameters ,req, cb) {
  let data = {};
  if(Array.isArray(parameters.client.hidden_facet)){
    parameters.client.hidden_facet = JSON.stringify(parameters.client.hidden_facet);
  }
  async.auto({
    "clientTable": cb => {
      data = { 
        task: 'updateSearchClient', 
        tenantId: req.headers['tenant-id'],
        updatedBy: req.headers.session,
        searchClientRegionStatus: parameters.client.s3_supported,
        // isS3Supported : isS3Supported,
        data: { 
          "tenantId": req.headers['tenant-id'], 
          'template_type_id': parameters.client.template_type_id, 
          'mainHtmlFilePath': parameters.client.DownloadFilePath + "/main.html", 
          'uid': parameters.client.uid, 
          'id': parameters.client.search_client_type_id, 
          'downloadFilePath': parameters.client.DownloadFilePath,
          'language': parameters.client.language
        },
        parameters: parameters,
        isABTestEnabled: parameters.hasAbTestDataStatus
      }

      delete parameters.client['label'];
      delete parameters.client['description'];
      delete parameters.client['folderPath'];
      delete parameters.client['searchId'];
      delete parameters.client['tname'];
      delete parameters.client['scName'];
      delete parameters.client['template_type'];
      // delete parameters.client['autocomplete'];
      delete parameters.client['js'];
      delete parameters.client['searchboxjs'];
      // delete parameters.client['language'];
      delete parameters.client['email'];
      delete parameters.client['last_updated_by']
      delete parameters.client['maxSummaryLimit']
      delete parameters.client['agentHelperSlackCreds']

      // remove it after QA
      // console.log("parameters=================>>>>>>>>>>>>>>>>>>",parameters.client.name);
      // console.log("parameters=================>>>>>>>>>>>>>>>>>>",parameters.client.id);

      if(req.body.ab_test_status === 0){
        if(parameters.client.name.includes('HybridSearch')){
          parameters.client.vector_search_enabled = 0;
          parameters.client.vector_search_settings = JSON.stringify({ minScoreThreshold: 0.4, hybridSearchEnabled : true });        
        }else if(parameters.client.name.includes('NeuralSearch')){
          parameters.client.vector_search_enabled = 1;
          parameters.client.vector_search_settings =  JSON.stringify({ minScoreThreshold: 0.4, hybridSearchEnabled : false });
        }else if(parameters.client.name.includes('KeywordSearch')){
          parameters.client.vector_search_enabled = 0
          parameters.client.vector_search_settings =  JSON.stringify({ minScoreThreshold: 0.4, hybridSearchEnabled : false });
        }
      }

      let q = connection[req.headers['tenant-id']].execute.query(`UPDATE search_clients SET ? WHERE id=?`, [parameters.client, parameters.client.id], (error, result) => {
        if(error) {
          commonFunctions.errorlogger.error(error);
        }
        cb();
      });
    },
    updateChildSearchClients: ["clientTable", (results, cb) => {
      // Step 2: Fetch child search clients
      const checkAbTestSql = `
          SELECT * 
          FROM search_clients 
          WHERE ab_test_parent = ?
      `;

      connection[req.headers['tenant-id']].execute.query(
          checkAbTestSql,
          [parameters.client.uid], // Use the parent search client UID
          (abTestError, abTestResults) => {
              if (abTestError) {
                commonFunctions.errorlogger.error("AB Test check error:", abTestError);
                  cb(abTestError, null);
                  return;
              }

              // Step 3: If child search clients exist, update them (excluding id, name, uid, DownloadFilePath)
              if (abTestResults && abTestResults.length > 0) {
                  let completedUpdates = 0;
                  let updateErrors = [];
                  data.abTestData =  [];
                  abTestResults.forEach((child) => {
                    commonFunctions.errorlogger.info("childchild===>>>>>>>",child);
                    data.abTestData.push({
                      uid : child.uid,
                      id: child.id,
                      relevancy: child.relevancy,
                      name: child.name,
                      downloadFilePath: `resources/search_clients_custom/${child.uid}/`,
                      mainHtmlFilePath: `resources/search_clients_custom/${child.uid}/main.html`,
                      ab_test_parent: child.ab_test_parent,
                      created_date: child.created_date
                    })
                      // Prepare the data for the child
                      const childData = { ...parameters.client }; 

                      // Remove fields that should not be updated for child clients
                      delete childData['id']; 
                      delete childData['name']; 
                      delete childData['uid']; 
                      delete childData['DownloadFilePath']; 
                      delete childData['ab_test_parent']; 
                      delete childData['relevancy']; 
                      delete childData['created_date']; 
                      delete childData['vector_search_settings']; 
                      delete childData['vector_search_enabled']; 
                      delete childData['special_search'];

                      connection[req.headers['tenant-id']].execute.query(
                          `UPDATE search_clients SET ? WHERE id = ?`,
                          [childData, child.id],
                          (updateError, updateResult) => {
                              if (updateError) {
                                  updateErrors.push(updateError);
                              }
                              completedUpdates++;

                              // Check if all updates are done
                              if (completedUpdates === abTestResults.length) {
                                  if (updateErrors.length > 0) {
                                    commonFunctions.errorlogger.error("Errors updating child search clients:", updateErrors);
                                      cb(updateErrors, null);
                                  } else {
                                    commonFunctions.errorlogger.info("Successfully updated all child search clients");
                                      cb(null, { message: "All updates completed successfully" });
                                  }
                              }
                          }
                      );
                  });
              } else {
                  // No child search clients found
                  cb(null, { message: "No child search clients to update" });
              }
          }
      );
  }],
      deflection_formula: (cb) => {
        /**
         * 1. if exist
         * 2. insert or update
         */
        let is_by_su_user;
        if (
          parameters.deflection_formula.caseDeflectionSetting ===
          "bySearchUsers"
        ) {
          is_by_su_user = true;
        } else {
          is_by_su_user = false;
        }
        connection[req.headers['tenant-id']].execute.query(
          "SELECT * FROM deflection_formula where search_client_id = ?",
          [parameters.client.id],
          (error, result) => {
            if (result.length === 0) {
              connection[req.headers['tenant-id']].execute.query(
                "INSERT INTO deflection_formula(is_by_su_user, search_client_id,session_url_regex,custom_event,support_url,session_idle_timeout,	email_tracking_enabled,external_user_enabled,tracking_only_logged_users_enabled,account_name,directly_viewed_results, viewed_results, cumulative, stage1, user_entitlements, kcs_analytics_enabled, kcs_url, kcs_service_desk_uid, true_deflection_enabled ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
                [
                  is_by_su_user,
                  parameters.client.id,
                  parameters.deflection_formula.session_url_regex,
                  parameters.deflection_formula.custom_event,
                  parameters.deflection_formula.support_url,
                  parameters.deflection_formula.session_idle_timeout,
                  parameters.deflection_formula.email_tracking_enabled,
                  parameters.deflection_formula.external_user_enabled,
                  parameters.deflection_formula.tracking_only_logged_users_enabled,
                  parameters.deflection_formula.account_name,
                  parameters.deflection_formula.directly_viewed_results,
                  parameters.deflection_formula.viewed_results,
                  parameters.deflection_formula.cumulative,
                  parameters.deflection_formula.stage1,
                  parameters.deflection_formula.user_entitlements,
                  parameters.deflection_formula.kcs_analytics_enabled,
                  parameters.deflection_formula.kcs_url,
                  parameters.deflection_formula.kcs_service_desk_uid,
                  parameters.deflection_formula.true_deflection_enabled
                ],
                (insertError) => {
                  if (insertError) return cb(insertError);
                  // Get child clients after parent insert
                  connection[req.headers['tenant-id']].execute.query(
                    "SELECT id FROM search_clients WHERE ab_test_parent = ?",
                    [parameters.client.uid],
                    (childError, childResults) => {
                      if (childError) return cb(childError);
                      if (!childResults || childResults.length === 0) return cb(null);
                      
                      let completed = 0;
                      childResults.forEach(childClient => {
                        connection[req.headers['tenant-id']].execute.query(
                          "INSERT INTO deflection_formula(is_by_su_user, search_client_id,session_url_regex,custom_event,support_url,session_idle_timeout,	email_tracking_enabled,external_user_enabled,tracking_only_logged_users_enabled,account_name,directly_viewed_results, viewed_results, cumulative, stage1, user_entitlements, kcs_analytics_enabled, kcs_url, kcs_service_desk_uid ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
                          [
                            is_by_su_user,
                            childClient.id,
                            parameters.deflection_formula.session_url_regex,
                            parameters.deflection_formula.custom_event,
                            parameters.deflection_formula.support_url,
                            parameters.deflection_formula.session_idle_timeout,
                            parameters.deflection_formula.email_tracking_enabled,
                            parameters.deflection_formula.external_user_enabled,
                            parameters.deflection_formula.tracking_only_logged_users_enabled,
                            parameters.deflection_formula.account_name,
                            parameters.deflection_formula.directly_viewed_results,
                            parameters.deflection_formula.viewed_results,
                            parameters.deflection_formula.cumulative,
                            parameters.deflection_formula.stage1,
                            parameters.deflection_formula.user_entitlements,
                            parameters.deflection_formula.kcs_analytics_enabled,
                            parameters.deflection_formula.kcs_url,
                            parameters.deflection_formula.kcs_service_desk_uid
                          ],
                          (err) => {
                            if (err) return cb(err);
                            completed++;
                            if (completed === childResults.length) cb(null);
                          }
                        );
                      });
                    }
                  );
                }
              );
            } else {
              connection[req.headers['tenant-id']].execute.query(
                "UPDATE deflection_formula SET is_by_su_user=?,custom_event=?,session_url_regex=?,support_url=?, session_idle_timeout =? , email_tracking_enabled =? , external_user_enabled=? , tracking_only_logged_users_enabled=? , account_name=?, directly_viewed_results=?, viewed_results=? , cumulative=?, stage1=?, user_entitlements=?, kcs_analytics_enabled=?, kcs_url=?, kcs_service_desk_uid=?, user_attribute_tracking_enabled=?, user_attribute_label=?, user_attribute_variable=?, true_deflection_enabled=? where id=? ",
                [
                  is_by_su_user,
                  parameters.deflection_formula.custom_event,
                  parameters.deflection_formula.session_url_regex,
                  parameters.deflection_formula.support_url,
                  parameters.deflection_formula.session_idle_timeout,
                  parameters.deflection_formula.email_tracking_enabled,
                  parameters.deflection_formula.external_user_enabled,
                  parameters.deflection_formula.tracking_only_logged_users_enabled,
                  parameters.deflection_formula.account_name,
                  parameters.deflection_formula.directly_viewed_results,
                  parameters.deflection_formula.viewed_results,
                  parameters.deflection_formula.cumulative,
                  parameters.deflection_formula.stage1,
                  parameters.deflection_formula.user_entitlements,
                  parameters.deflection_formula.kcs_analytics_enabled,
                  parameters.deflection_formula.kcs_url,
                  parameters.deflection_formula.kcs_service_desk_uid,
                  parameters.deflection_formula.user_attribute_tracking_enabled,
                  parameters.deflection_formula.user_attribute_label,
                  parameters.deflection_formula.user_attribute_variable,
                  parameters.deflection_formula.true_deflection_enabled,
                  result[0].id,
                ],
                (updateError) => {
                  if (updateError) return cb(updateError);
                  // Get child clients after parent update
                  connection[req.headers['tenant-id']].execute.query(
                    "SELECT id FROM search_clients WHERE ab_test_parent = ?",
                    [parameters.client.uid],
                    (childError, childResults) => {
                      if (childError) return cb(childError);
                      if (!childResults || childResults.length === 0) return cb(null);
      
                      let completed = 0;
                      childResults.forEach(childClient => {
                        connection[req.headers['tenant-id']].execute.query(
                          "SELECT * FROM deflection_formula where search_client_id = ?",
                          [childClient.id],
                          (selectError, childResult) => {
                            if (selectError) return cb(selectError);
      
                            if (childResult.length === 0) {
                              connection[req.headers['tenant-id']].execute.query(
                                "INSERT INTO deflection_formula(is_by_su_user, search_client_id,session_url_regex,custom_event,support_url,session_idle_timeout,	email_tracking_enabled,external_user_enabled,tracking_only_logged_users_enabled,account_name,directly_viewed_results, viewed_results, cumulative, stage1, user_entitlements, kcs_analytics_enabled, kcs_url, kcs_service_desk_uid ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
                                [
                                  is_by_su_user,
                                  childClient.id,
                                  parameters.deflection_formula.session_url_regex,
                                  parameters.deflection_formula.custom_event,
                                  parameters.deflection_formula.support_url,
                                  parameters.deflection_formula.session_idle_timeout,
                                  parameters.deflection_formula.email_tracking_enabled,
                                  parameters.deflection_formula.external_user_enabled,
                                  parameters.deflection_formula.tracking_only_logged_users_enabled,
                                  parameters.deflection_formula.account_name,
                                  parameters.deflection_formula.directly_viewed_results,
                                  parameters.deflection_formula.viewed_results,
                                  parameters.deflection_formula.cumulative,
                                  parameters.deflection_formula.stage1,
                                  parameters.deflection_formula.user_entitlements,
                                  parameters.deflection_formula.kcs_analytics_enabled,
                                  parameters.deflection_formula.kcs_url,
                                  parameters.deflection_formula.kcs_service_desk_uid
                                ],
                                (err) => {
                                  if (err) return cb(err);
                                  completed++;
                                  if (completed === childResults.length) cb(null);
                                }
                              );
                            } else {
                              connection[req.headers['tenant-id']].execute.query(
                                "UPDATE deflection_formula SET is_by_su_user=?,custom_event=?,session_url_regex=?,support_url=?, session_idle_timeout =? , email_tracking_enabled =? , external_user_enabled=? , tracking_only_logged_users_enabled=? , account_name=?, directly_viewed_results=?, viewed_results=? , cumulative=?, stage1=?, user_entitlements=?, kcs_analytics_enabled=?, kcs_url=?, kcs_service_desk_uid=?, user_attribute_tracking_enabled=?, user_attribute_label=?, user_attribute_variable=? where id=? ",
                                [
                                  is_by_su_user,
                                  parameters.deflection_formula.custom_event,
                                  parameters.deflection_formula.session_url_regex,
                                  parameters.deflection_formula.support_url,
                                  parameters.deflection_formula.session_idle_timeout,
                                  parameters.deflection_formula.email_tracking_enabled,
                                  parameters.deflection_formula.external_user_enabled,
                                  parameters.deflection_formula.tracking_only_logged_users_enabled,
                                  parameters.deflection_formula.account_name,
                                  parameters.deflection_formula.directly_viewed_results,
                                  parameters.deflection_formula.viewed_results,
                                  parameters.deflection_formula.cumulative,
                                  parameters.deflection_formula.stage1,
                                  parameters.deflection_formula.user_entitlements,
                                  parameters.deflection_formula.kcs_analytics_enabled,
                                  parameters.deflection_formula.kcs_url,
                                  parameters.deflection_formula.kcs_service_desk_uid,
                                  parameters.deflection_formula.user_attribute_tracking_enabled,
                                  parameters.deflection_formula.user_attribute_label,
                                  parameters.deflection_formula.user_attribute_variable,
                                  childResult[0].id,
                                ],
                                (err) => {
                                  if (err) return cb(err);
                                  completed++;
                                  if (completed === childResults.length) cb(null);
                                }
                              );
                            }
                          }
                        );
                      });
                    }
                  );
                }
              );
            }
          }
        );
      },
      mapping_object: (cb) => {
        async.parallel(
          parameters.mapping_objects.map((ma) => {
            if ([1,2].includes(ma.preview_type)) {
              ma.preview_mapping = [];
            }
            if (!parameters.client.preview) {
              ma.preview_type = 1;
              ma.preview_mapping = [];
            }
            return (cb) => {
              connection[req.headers['tenant-id']].execute.query(
                `INSERT INTO search_clients_to_content_objects(content_source_object_id,id,search_client_id,title_field_id, base_href, compose_title, merge_results, formula, icon, permission_by_pass, preview_type)
                 VALUES(?,?,?,?,?,?,?,?,?,?,?)
                 ON DUPLICATE KEY UPDATE content_source_object_id = VALUES(content_source_object_id),id = VALUES(id),search_client_id = VALUES(search_client_id),title_field_id = VALUES(title_field_id),base_href=VALUES(base_href),compose_title=VALUES(compose_title), merge_results=VALUES(merge_results) ,formula=VALUES(formula),icon=VALUES(icon),permission_by_pass=VALUES(permission_by_pass), preview_type=VALUES(preview_type)`,
                [
                  ma.content_source_object_id || null,
                  ma.id || null,
                  ma.search_client_id || null,
                  ma.title_field_id || null,
                  ma.base_href || null,
                  ma.compose_title || null,
                  ma.merge_results || null,
                  ma.formula || null,
                  ma.icon || null,
                  ma.permission_by_pass || 0,
                  ma.preview_type || 1
                ],
                (e, r, f) => {
                  if (e) cb(e);
                  else {
                    async.auto(
                      {
                        fetch_ab_test_data: function (cb) {
                          let retrieveQuery = `
                            SELECT isContentBoostingEnabled, boosting_factor 
                            FROM search_clients_to_content_objects 
                            WHERE search_client_id = ? AND content_source_object_id = ?
                          `;
                        
                          connection[req.headers["tenant-id"]].execute.query(
                            retrieveQuery,
                            [req.body.platformId , ma.content_source_object_id],
                            (err, data) => {
                              if (err) {
                                commonFunctions.errorlogger.error("Error fetching search_clients_to_content_objects settings:", err);
                                return cb(err);
                              }
                      
                        
                              if (data.length > 0) {
                        
                                let updateQuery = `
                                  UPDATE search_clients_to_content_objects 
                                  SET isContentBoostingEnabled = ?, boosting_factor = ?
                                  WHERE search_client_id = ? AND content_source_object_id = ?
                                `;

                                
                                connection[req.headers["tenant-id"]].execute.query(
                                  updateQuery,
                                  [
                                    data[0].isContentBoostingEnabled || 0, // Default to 0 if undefined
                                    data[0].boosting_factor || 1, // Default to 1 if undefined
                                    ma.search_client_id,
                                    ma.content_source_object_id,
                                  ],
                                  (updateErr) => {
                                    if (updateErr) {
                                      commonFunctions.errorlogger.error("Error updating search_clients_to_content_objects:", updateErr);
                                      return cb(updateErr);
                                    }
                        
                                    commonFunctions.errorlogger.info("Updated search_clients_to_content_objects successfully.");
                                    cb(null, data[0]); // Resolving with the fetched data
                                  }
                                );
                              } else {
                                commonFunctions.errorlogger.info("No search_clients_to_content_objects settings found for the original search_client_id");
                                cb(null, null);
                              }
                            }
                          );
                        },                        
                        use_as_summary_title: function (cb) {
                          async.parallel(
                            ma.mapping.map((m) => {
                              return (cb) => {
                                m.search_clients_to_content_object_id =
                                  r.insertId || ma.id;
                                var q = connection[req.headers['tenant-id']].execute.query(
                                  `INSERT INTO search_clients_filters( content_source_object_field_id, id, search_clients_to_content_object_id, use_as,priority,exclude,search_priority, use_value, sort_by, order_by, summary_length, level_limit, extra_field, track_analytics,auto_learning, facet_type, facet_data_type) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE content_source_object_field_id = VALUES(content_source_object_field_id), id = VALUES(id), search_clients_to_content_object_id = VALUES(search_clients_to_content_object_id), use_as = VALUES(use_as),priority = VALUES(priority),exclude = VALUES(exclude), search_priority = VALUES(search_priority), use_value = VALUES(use_value), sort_by = VALUES(sort_by), order_by = VALUES(order_by), summary_length= VALUES(summary_length), level_limit= VALUES(level_limit), extra_field = VALUES(extra_field), track_analytics = VALUES(track_analytics),auto_learning = VALUES(auto_learning),facet_type = VALUES(facet_type), facet_data_type=VALUES(facet_data_type)`,
                                  [
                                    m.content_source_object_field_id || null,
                                    m.id || null,
                                    m.search_clients_to_content_object_id ||
                                      null,
                                    m.use_as || null,
                                    m.priority || null,
                                    m.exclude || null,
                                    m.search_priority || null,
                                    m.use_value || 10,
                                    m.sort_by || 'count',
                                    m.order_by || 'desc',
                                    m.summary_length || 100,
                                    m.level_limit || 1,
                                    m.extra_field || 0,
                                    m.track_analytics || 0,
                                    m.auto_learning || 0,
                                    m.facet_type || 'default',
                                    m.facet_data_type || 'string'
                                  ],
                                  (e, r, f) => {
                                    cb(e, r.insertId || m.id);
                                  }
                                );
                              };
                            }),
                            (error, res) => {
                              const csId = r.insertId || ma.id;
                              let sql = `DELETE FROM search_clients_filters where search_clients_to_content_object_id=?`;
                              if (res.length > 0){
                                sql += ` AND id NOT IN (${res
                                  .map((t) => "?")
                                  .join(",")})`;
                              }
                              let q = connection[req.headers['tenant-id']].execute.query(
                                sql,
                                [csId, ...res],
                                (error, result) => {
                                  cb(error, r.insertId || ma.id);
                                }
                              );
                            }
                          );
                        },
                        use_as_metadata: function (cb) {
                          async.parallel(
                            ma.metadata_mapping.map((m) => {
                              return (cb) => {
                                m.search_client_to_content_object_id =
                                  r.insertId || ma.id;
                                connection[req.headers['tenant-id']].execute.query(
                                  `INSERT INTO search_client_metadata_fields( field_id, id, priority, search_client_to_content_object_id,autosuggestField,metaData) values(?,?,?,?,?,?) ON DUPLICATE KEY UPDATE field_id = VALUES(field_id), id = VALUES(id), priority = VALUES(priority), search_client_to_content_object_id = VALUES(search_client_to_content_object_id), autosuggestField = VALUES(autosuggestField), metaData = VALUES(metaData)`,
                                  [
                                    m.content_source_object_field_id || null,
                                    m.id || null,
                                    m.priority || null,
                                    m.search_client_to_content_object_id ||
                                      null,
                                    m.autosuggestField,
                                    m.metaData,
                                  ],
                                  (e, r, f) => {
                                    cb(e, r.insertId || m.id);
                                  }
                                );
                              };
                            }),
                            (error, res) => {
                              const csId = r.insertId || ma.id;
                              let sql = `DELETE FROM search_client_metadata_fields where search_client_to_content_object_id=?`;
                              if (res.length > 0){
                                sql += ` AND id NOT IN (${res
                                  .map((t) => "?")
                                  .join(",")})`;

                              }
                              let q = connection[req.headers['tenant-id']].execute.query(
                                sql,
                                [csId,...res],
                                (error, result) => {
                                  cb(error, r.insertId || ma.id);
                                }
                              );
                            }
                          );
                        },
                        use_as_preview: function (cb) {
                          async.parallel(
                            ma.preview_mapping.map((m) => {
                              return (cb) => {
                                m.search_client_to_content_object_id = r.insertId || ma.id;
                                connection[req.headers['tenant-id']].execute.query(
                                  `INSERT INTO search_client_preview_fields(
                                    field_id, 
                                    id, 
                                    search_client_to_content_object_id, 
                                    sc_id, 
                                    preview_order
                                  ) VALUES (?, ?, ?, ?, ?) 
                                  ON DUPLICATE KEY UPDATE 
                                    field_id = VALUES(field_id), 
                                    id = VALUES(id), 
                                    search_client_to_content_object_id = VALUES(search_client_to_content_object_id),
                                    sc_id = VALUES(sc_id),
                                    preview_order = VALUES(preview_order);`,
                                  [
                                    m.content_source_object_field_id || null,
                                    m.id || null,
                                    m.search_client_to_content_object_id || null,
                                    m.sc_id,
                                    m.preview_order && m.preview_order !== true ? m.preview_order : 10000
                                  ],
                                  (e, r, f) => {
                                    cb(e, r.insertId || m.id);
                                  }
                                );
                              };
                            }),
                            (error, res) => {
                              const csId = r.insertId || ma.id;

                              let sql = `DELETE FROM search_client_preview_fields where search_client_to_content_object_id=?`;
                              if (res.length > 0){
                                sql += ` AND id NOT IN (${res
                                  .map((t) => "?")
                                  .join(",")})`;
                              }
                              let q = connection[req.headers['tenant-id']].execute.query(
                                sql,
                                [csId,...res],
                                (error, result) => {
                                  cb(error, r.insertId || ma.id);
                                }
                              );
                            }
                          );
                        },
                      },
                      function (err, result) {
                        cb(err, result.use_as_summary_title);
                      }
                    );
                  }
                }
              );
            };
          }),
          (error, results) => {
            if (error) cb(error);
            else {
              let sql = `DELETE FROM search_clients_to_content_objects where search_client_id=?`;
              if (results.length > 0)
                sql += ` AND id NOT IN (${results.map((t) => "?").join(",")})`;
              let q = connection[req.headers['tenant-id']].execute.query(sql, [parameters.client.id,...results], (error, result) => {
                if(result){
                  let sql = `SELECT * FROM search_clients_to_content_objects where search_client_id=?`;
                  let q = connection[req.headers['tenant-id']].execute.query(sql, [parameters.client.id,...results], (error, result) => {
                    data.scObjects = result;
                    data.data.name = parameters.client.name;
                    data.tenantId = req.headers['tenant-id']
                    searchClientRunTimeVariables(data,req, function (error, result) {
                      try {
                        kafkaLib.publishMessage({
                          topic: config.get("kafkaTopic.searchClientTopic"),
                          messages: [{
                            value: JSON.stringify(result),
                            key: parameters.client.uid
                          }]
                        })
                        if(config.get('statusPageService.kafka.host') !== config.get('kafkaTopic.host')){
                          kafkaStatusLib.publishMessage({
                            topic: config.get("statusPageService.kafka.searchClientTopic"),
                            messages: [{
                              value: JSON.stringify(result),
                              key: parameters.client.uid
                            }]
                          })
                        }
                        commonFunctions.errorlogger.info('updateSearchClient instruction is sent to Search-client Service by Kafka ')
                      } catch (e) {
                        commonFunctions.errorlogger.error('Error while sending instruction to Search-client Service by Kafka => ', e)
                      }
                      
                    });
                    
                  });
                }
                cb(error, result);
              });
            }
          }
        );
      },
      analyticsReports: (cb) => {
        // Function to handle analytics report updates for a specific search client ID
        const updateAnalyticsForClient = (clientId, analyticsReports, callback) => {
          // Validate analytics reports data
          if (!analyticsReports || !Array.isArray(analyticsReports)) {
            commonFunctions.errorlogger.error("Analytics Reports data missing for client:", clientId);
            return callback(null);
          }
      
          let values = [];
          analyticsReports.forEach((filter) => {
            values.push(filter.id);
            values.push(clientId);
            values.push(filter.analytics_report_id);
            values.push(filter.is_enabled);
            values.push(filter.label || filter.name);
          });
      
          let updateSql = `INSERT INTO search_client_to_analytics_report (id, search_client_id, analytics_report_id, is_enabled, label)
            VALUES ${analyticsReports
              .map((filter) => "(?, ?, ?, ?, ?)")
              .join(", ")}
            ON DUPLICATE KEY UPDATE
              search_client_id    = values(search_client_id),
              analytics_report_id = values(analytics_report_id),
              is_enabled         = values(is_enabled),
              label              = values(label)`;
      
          connection[req.headers['tenant-id']].execute.query(updateSql, values, (error, result) => {
            if(error) {
              commonFunctions.errorlogger.error("Analytics Report Update Error:", error);
            }
            callback(error, result);
          });
        };
      
        // Check if we have an AB test result
        const checkAbTestSql = `
        SELECT id 
        FROM search_clients 
        WHERE ab_test_parent = ?`;
      
        connection[req.headers['tenant-id']].execute.query(
          checkAbTestSql, 
          [parameters.client.uid], 
          (abTestError, abTestResults) => {
            if (abTestError) {
              commonFunctions.errorlogger.error("AB Test check error:", abTestError);
              return cb(abTestError);
            }
      
            // If we have an AB test search client
            if (abTestResults && abTestResults.length > 0) {
              // Update analytics for both original and AB test search clients
              async.parallel([
                // Update analytics for original search client using frontend data
                (parallelCb) => updateAnalyticsForClient(parameters.client.id, parameters.analyticsReports, parallelCb),
                
                // Update analytics for all AB test child search clients
                ...abTestResults.map(result => 
                  (parallelCb) => {
                    // First get the specific search client data for this variant
                    getSearchClient(result.id, req, function(error, variantData) {
                      if (error) {
                        commonFunctions.errorlogger.error("Error getting variant data:", error);
                        return parallelCb(error);
                      }
                      const mergedAnalyticsReports = parameters.analyticsReports.map((report, index) => ({
                        id: variantData.analyticsReports[index].id || report.id, // Use id from variantData if available
                        analytics_report_id: report.analytics_report_id,
                        is_enabled: report.is_enabled,
                        label: report.label || report.name,
                    }));

                      
                      // Now update with the variant's specific analytics reports
                      updateAnalyticsForClient(result.id, mergedAnalyticsReports, parallelCb);
                    });
                  }
                )
              ], (parallelError, results) => {
                if (parallelError) {
                  commonFunctions.errorlogger.error("Error in parallel updates:", parallelError);
                }
                commonFunctions.errorlogger.info("Updated AB Test Clonned Search Clients Successfully.")
                cb(parallelError, results);
              });
            } else {
              // No AB test, just update the original search client
              updateAnalyticsForClient(parameters.client.id, parameters.analyticsReports, cb);
            }
          }
        );
      },
      addVersionFields: (cb) => {
        let versionResultsEnabled = parameters.mapping_objects.filter((x) => {
          if (JSON.parse(x.merge_results || "{}").enabled) return true;
        });
        if (parameters.client.uid && versionResultsEnabled.length) {
          kafkaLib.publishMessage({
            topic: config.get("kafkaTopic.processRequest"),
            messages: [{
              value: JSON.stringify({
                type: 'ADD',
                service: 'index-service',
                tenantId: req.headers['tenant-id'],
                comment: `merge_result_other_fields_${parameters.client.uid}_${req.headers['tenant-id']}`,
                information: {
                  eventType: 'merge-result-cron',
                  executionMode: 'kafka',
                  kafkaTopic: config.get('kafkaTopic.versionUpdateMapping'),
                  kafkaValue: {
                    tenantId: req.headers['tenant-id'],
                    type: kafkaLib.KAFKA_EVENT_TYPES.updateMergeResult,
                    data: req.body,
                  },
                },
              }),
              key: req.headers['tenant-id']
            }],
          });
        }
        cb(null, "");
      },
      reranking: (cb) => {
        let hybridSearchEnabled = false;

        if(parameters.client && parameters.client.vector_search_settings) {
          const vectorSearchSettings = JSON.parse(parameters.client.vector_search_settings);

          hybridSearchEnabled = vectorSearchSettings.hybridSearchEnabled;
        }

        if((parameters.client && parameters.client.vector_search_enabled) || hybridSearchEnabled) {
          req.body = {
            ...req.body,
            uid: parameters.client.uid,
            status: false,
            search_client_id: parameters.client.id,
            getKafka: false,
          };

          tuningService.autoTuning.reRankingEnable(req)
            .then(res => {
              cb(null, '');
            }).catch(err => {
              cb(err, null);
            });
        }
        else cb(null, "");
      },
      updateMigrationsDB : cb =>{
        const prod_uid = parameters.client.uid;
        const sandbox_uid = parameters.sandbox_uid;
        const isMigration = parameters.isMigration;
        if(!isMigration) return cb(null,"");
        const sql = 'update migration_requests set uid_prod=?, is_migrated=1 where uid=?';
        connection[req.headers['tenant-id']].execute.query( sql, [prod_uid, sandbox_uid], (error, result) => {
            cb(error, result);
        });
      }
  },
  (error, result) => {
      if (error){
        cb(error);
      }
      else{
        cb(null, result);
      }
    }
  );
};

const searchClientRunTimeVariables = function (data, req, cb1) {
  async.auto({
    "getClientType": (cb) => {
      connection[req.headers['tenant-id']].execute.query(`SELECT * from search_client_types WHERE id=?`,[data.data.id], cb);
    },
    "accessToken": async (cb) => {
      try{
        const data = await getAccessTokenFromTenantId(req.headers['tenant-id']);
        if(data.accessToken === ''){
          throw new Error('Access token not found');
        }
        return data.accessToken; 
      }catch(error){
        throw new Error('Access token not found');
      }
    },
    "hostedUrl": cb => {
      if (data.data.id !== 20) { cb(); }
      else {
        connection[req.headers['tenant-id']].execute.query(`SELECT uid FROM saml_auth WHERE idp_type=1`, (error, rows) => {
          if (error) {
            console.error(error);
            cb();
          }
          else {
            if (rows[0])
              cb(null, rows[0].uid || "");
            else
              cb(null, "");
          }
        });
      }
    },
    "addonEnabled": cb =>{
      if(data.data.id !== 7){ cb(); }
      else {
          connection[req.headers['tenant-id']].execute.query(`SELECT is_installed FROM addons, addons_status WHERE addons.id = addons_status.addon_id AND name='Agent Helper'`, (error, rows)=>{
              if(error){
                  console.error(error);
                  cb();
              }
              else{
                  cb(null, rows[0]&&rows[0].is_installed);
              }
          });
      }
    },
    "agentHelperConfiguration": cb => {
      if (data.data.id !== 7) { cb(); }
      else {
        let obj;
        connection[req.headers['tenant-id']].execute.query('SELECT * from agenthelper WHERE uid = ? AND tenant_id = ?', [data.data.uid, req.headers['tenant-id']], (error, doc) => {
          if(!error){
              obj = doc[0];
              if(obj && obj.configuration){
                  obj.configuration = JSON.parse(obj.configuration);
              }
              cb(null, obj);
          }else{
              cb(null, obj);
          }
        });
      }
    },
    "resultSharingOptions": cb => {
      var sql = "Select id from search_clients where uid =?";
      connection[req.headers['tenant-id']].execute.query(sql, [data.data.uid], function (err, result) {
        if (err) {
          console.log("error", err);
          cb(err, null);
        } else {
          var query = "Select  search_client_id, result_action_id, status, merge,selected_object,content_source_label from search_client_actions where search_client_id = ?";
          connection[req.headers['tenant-id']].execute.query(query, [result[0].id], function (err, result) {
            if (err) {
              console.log("error", err);
              cb(err, null);
            } else {
              cb(null, result);
            }
          });
        }
      });

    },
    "languageManager": cb => {
      var sql = "Select languageManager from search_clients where uid =?";
      connection[req.headers['tenant-id']].execute.query(sql, [data.data.uid], function (err, result) {
        if (err) {
          console.log("error", err);
          cb(err, null);
        } else {
          cb(null, result[0]);
        }
      });
    },
    // Getting Token url and expiry time for Higher Logic and AEM search Client 
    "jwtToken": cb => {
      if (data.data.id !== 23 && data.data.id !== 24) { cb(null, false); }
      else {
        connection[req.headers['tenant-id']].execute.query("SELECT jwt_href ,jwt_expiry FROM `search_clients` where uid =?", data.data.uid, (error, results) => {
          if (error) {
            console.error(error);
            cb();
          }
          else {
            if (results && results.length) {
              cb(null, results[0]);
            } else {
              cb(null, false);
            }
          }
        });
      }
    },
    "getSSOConfig": (cb) => {
      if (data.data.id !== 31) { cb(); }
      else {
        connection[req.headers['tenant-id']].execute.query('SELECT sso_config FROM search_client_sso_config WHERE sc_uid = ?', [data.data.uid], (selectError, selectResults) => {
          if (selectError) {
            commonFunctions.errorlogger.error("error", selectError)
          }
          cb(selectError, selectResults[0].sso_config);
        });
      }
    },
    
    "sendFile": ["agentHelperConfiguration", "addonEnabled", "hostedUrl", "resultSharingOptions", "languageManager", "jwtToken", "accessToken", "getClientType", "getSSOConfig", (results, cb) => {
      let agentHelperConfiguration = {
        enabled : results.agentHelperConfiguration && results.agentHelperConfiguration.is_enabled ? true : false,
        Input_Fields: null,
        search_uid_AH: results.agentHelperConfiguration && results.agentHelperConfiguration.search_uid_AH,
        search_uid_RA: results.agentHelperConfiguration && results.agentHelperConfiguration.search_uid_RA,
        slack_enabled: results.agentHelperConfiguration && results.agentHelperConfiguration.slack_enabled,
      };
      if ((!results.addonEnabled) || results.addonEnabled == "0")
        agentHelperConfiguration.enabled = false;
        if(results.agentHelperConfiguration && results.agentHelperConfiguration.is_enabled && results.agentHelperConfiguration.configuration && results.agentHelperConfiguration.configuration.Input_Fields.length){ 
          agentHelperConfiguration.Input_Fields = results.agentHelperConfiguration.configuration.Input_Fields.map(f=>{
            if (f.name.indexOf("___") > -1)
            return f.name.split("___")[2];
          else
            return f.name;
        });
      }
      results.agentHelperConfiguration = agentHelperConfiguration;
      results.languageManager = results.languageManager;
      results.accessToken = results.accessToken;
      cb();
    }]
  }, (error, result) => {
    if (error) commonFunctions.errorlogger.error("error", error);
    else {
      data.searchClientConfig = result;
      data.searchClientConfig.instanceName = config.get('instanceName');
      cb1(null, data)
    };
  });
};

function getFieldBoosting(cb, req) {
  connection[req.headers['tenant-id']].execute.query(`SELECT * FROM boosting_field`, (error, result) => {
    commonFunctions.errorlogger.info(result);
    cb(error, result);
  });
}

router.post("/updateFieldBoosting", (req, res, next) => {
  let parameters = req.body.field;
  let q = connection[req.headers['tenant-id']].execute.query(
    `INSERT INTO boosting_field(id, search_clients_to_content_object_id, content_source_object_field_id, boosting_value) values(?,?,?,?)
  ON DUPLICATE KEY UPDATE id = VALUES(id), search_clients_to_content_object_id = VALUES(search_clients_to_content_object_id), content_source_object_field_id = VALUES(content_source_object_field_id), boosting_value = VALUES(boosting_value)`,
    [
      parameters.id,
      parameters.search_clients_to_content_object_id,
      parameters.content_source_object_field_id,
      parameters.boosting_value,
    ],
    (error, result) => {
      if (result)
        res.send({
          id: result.insertId,
          content_source_object_field_id:
            parameters.content_source_object_field_id,
        });
    }
  );
});

function deleteAnalyticsByUid(uid, callback) {
  async.auto(
    {
      deleteFromAnalytics: (cb) => {
        var body = {
          query: {
            term: {
              uid: uid,
            },
          },
        };
        var options = {
          method: "DELETE",
          url:
            "http://" +
            config.get("elasticIndex.host") +
            ":" +
            config.get("elasticIndex.port") +
            "/" +
            config.get("elasticIndex.analytics") +
            "/_query",
          body: JSON.stringify(body),
        };
        request(options, function (error, response, result) {
          if (error || (response && response.statusCode != 200)) {
            cb(error, null);
          } else {
            cb(null, "success");
          }
        });
      },
      deleteFromSessionAnalytics: (cb) => {
        var body = {
          query: {
            term: {
              uid: uid,
            },
          },
        };
        var options = {
          method: "DELETE",
          url:
            "http://" +
            config.get("elasticIndex.host") +
            ":" +
            config.get("elasticIndex.port") +
            "/" +
            config.get("elasticIndex.sessionIndex") +
            "/_query",
          body: JSON.stringify(body),
        };
        request(options, function (error, response, result) {
          if (error || (response && response.statusCode != 200)) {
            cb(error, null);
          } else {
            cb(null, "success");
          }
        });
      },
    },
    function (err, data) {
      if (err) {
        callback(err, null);
      } else {
        callback(null, "analytics Deleted");
      }
    }
  );
}

router.get("/getLanguages", (req, res) => {
  var sql = `SELECT * FROM languages ORDER BY label ASC`;
  connection[req.headers['tenant-id']].execute.query(sql, (err, docs) => {
    res.send(JSON.stringify(docs));
  });
});

router.post("/caseDeflection", function (req, res, next) {
  var query = `UPDATE search_clients SET is_case_deflected_shown = "${req.body.caseDeflection}" WHERE uid ="${req.body.uid}"`;
  connection[req.headers['tenant-id']].execute.query(query, function (err, response) {
    if (err) {
      return res.send({ status: "401" });
    }

    const childQuery = `
      SELECT uid 
      FROM search_clients 
      WHERE ab_test_parent = ?`;
    
    connection[req.headers['tenant-id']].execute.query(
      childQuery, 
      [req.body.uid], 
      (childErr, childResults) => {
        if (childErr) {
          return res.send({ status: "401" });
        }

        if (!childResults || childResults.length === 0) {
          return res.send({ status: "200" });
        }

        const childUids = childResults.map(child => child.uid);
        const updateChildrenQuery = `
          UPDATE search_clients 
          SET is_case_deflected_shown = "${req.body.caseDeflection}" 
          WHERE uid IN (?)`;

        connection[req.headers['tenant-id']].execute.query(
          updateChildrenQuery,
          [childUids],
          (updateErr, updateResponse) => {
            if (updateErr) {
              res.send({ status: "401" });
            } else {
              res.send({ status: "200" });
            }
          }
        );
      }
    );
  });
});

router.post("/caseDeflectionStatus", function (req, res, next) {
  var query = `SELECT is_case_deflected_shown FROM search_clients WHERE uid ="${req.body.uid}"`;
  connection[req.headers['tenant-id']].execute.query(query, function (err, response) {
    if (err) {
      res.send({ status: "401", caseDeflectionStatus: response });
    } else {
      res.send({ status: "200", caseDeflectionStatus: response });
    }
  });
});

router.post("/caseDeflectionStatusEco", function (req, res, next) {
  var query = `SELECT stage2_deflection FROM ecosystems WHERE uid = ?`;
  connection[req.headers['tenant-id']].execute.query(query, [req.body.ecoId], function (err, response) {
    if (err) {
      res.send({ status: "401", caseDeflectionStatus: response });
    } else {
      res.send({ status: "200", caseDeflectionStatus: response });
    }
  });
});

router.post("/getSearchClientDeflectionFormula", function (req, res, next) {
  var query = `SELECT * FROM deflection_formula where search_client_id ="${req.body.id}"`;
  connection[req.headers['tenant-id']].execute.query(query, function (err, response) {
    if (err) {
      res.send({ status: "500", err: err });
    } else {
      res.send({ status: "200", data: response });
    }
  });
});

router.get("/getFormula", function (req, res, next) {
  var query = `SELECT formula FROM search_clients_to_content_objects`;
  connection[req.headers['tenant-id']].execute.query(query, function (err, response) {
    if (err) {
      res.send({ status: "402" });
    } else {
      let resToSend = response.map((x) => {
        let n = JSON.parse(x.formula);
        if (n && n.condition) return n.condition;
      });
      if (resToSend && resToSend.length) {
        resToSend = resToSend
          .filter((x) => x)
          .reduce(commonFunctions.reduceObject);
      }
      res.send({ status: "200", formula: resToSend });
    }
  });
});

router.get("/clearSearchPermissions", (req, res, next) => {
  commonFunctions.CleanSearchRedisCache();
  res.send({ flag: 200, message: "done" });
});

router.post("/casedeflectionlabels", function (req, res, next) {
  var query = `SELECT * FROM analytics_cd_labels where uid ="${req.body.uid}"`;
  connection[req.headers['tenant-id']].execute.query(query, function (err, response) {
    if (err) {
      res.send({ status: "500", err: err });
    } else {
      if (response.length > 0) {
        res.send({ status: "200", data: response });
      } else {
        let TemplateArray = [
          {
            uid: req.body.uid,
            report_title_name: "Search Sessions (stage1)",
            custom_title_name: "Search Sessions",
            tooltip:
              "Number of sessions in which <br />users visited the search page.",
          },
          {
            uid: req.body.uid,
            report_title_name: "Click Sessions (stage1)",
            custom_title_name: "Click Sessions",
            tooltip: "Successful Searches Who didn't Visit Support",
          },
          {
            uid: req.body.uid,
            report_title_name: "No Clicks Sessions (stage1)",
            custom_title_name: "No Clicks Sessions",
            tooltip: "Unsuccessful Searches",
          },
          {
            uid: req.body.uid,
            report_title_name: "Exit (Stage1)",
            custom_title_name: "Exit",
            tooltip: "Same as Total Sessions.",
          },
          {
            uid: req.body.uid,
            report_title_name: "Total Sessions (Stage1)",
            custom_title_name: "Total Sessions",
            tooltip: "Total number<br />of unique sessions.",
          },
          {
            uid: req.body.uid,
            report_title_name: "No Search Sessions (Stage1)",
            custom_title_name: "No Search Sessions",
            tooltip:
              "Number of sessions in which <br/>users didn't visit the search page.",
          },
          {
            uid: req.body.uid,
            report_title_name: "Support Sessions (Stage1)",
            custom_title_name: "Support Sessions",
            tooltip:
              "Number of sessions in which users <br />visited the support page.",
          },
          {
            uid: req.body.uid,
            report_title_name: "Search Sessions (stage2)",
            custom_title_name: "Search Sessions",
            tooltip:
              "Number of sessions in which <br />users visited the search page.",
          },
          {
            uid: req.body.uid,
            report_title_name: "Support Sessions (Stage2)",
            custom_title_name: "Support Sessions",
            tooltip:
              "Number of sessions in which users <br />visited the support page.",
          },
          {
            uid: req.body.uid,
            report_title_name: "No Search Sessions (Stage2)",
            custom_title_name: "No Search Sessions",
            tooltip:
              "Number of sessions in which users <br />visited the support page <br />and did not search.",
          },
          {
            uid: req.body.uid,
            report_title_name: "Click Sessions (Stage2)",
            custom_title_name: "Click Sessions",
            tooltip:
              "Number of sessions in which users <br />searched and clicked on results.",
          },
          {
            uid: req.body.uid,
            report_title_name: "No Click Sessions (Stage2)",
            custom_title_name: "No Click Sessions",
            tooltip:
              "Number of sessions in which users <br />searched but did not click.",
          },
          {
            uid: req.body.uid,
            report_title_name: "Case Created Sessions (Stage2)",
            custom_title_name: "Case Created Sessions",
            tooltip: "Number of sessions in which users <br />created a case.",
          },
          {
            uid: req.body.uid,
            report_title_name: "Exit (Stage2)",
            custom_title_name: "Exit",
            tooltip:
              "Number of sessions who left the support <br />or have no activity on support",
          },
          {
            uid: req.body.uid,
            report_title_name: "Direct Pageview",
            custom_title_name: "Direct Pageview",
            tooltip: "Number of sessions who directly viewed the results",
          },
        ];

        res.send({ status: "200", data: TemplateArray });
      }
    }
  });
});

// updatecasedeflectionlabels

router.post("/updatecasedeflectionlabels", function (req, res, next) {
  let data = req.body.data;

  async.auto(
    {
      DeleteEntries: (cb) => {
        connection[req.headers['tenant-id']].execute.query(
          "Delete from analytics_cd_labels where uid = ? ",
          [req.body.uid],
          function (error, result) {
            cb(error, result);
          }
        );
      },
      insert: [
        "DeleteEntries",
        (results, cb) => {
          let query =
            "INSERT INTO analytics_cd_labels (uid ,report_title_name ,custom_title_name ,tooltip ) VALUES" +
            `(?,'Search Sessions (stage1)', ?, ? ),` +
            `(?,'Click Sessions (stage1)', ?, ?),` +
            `(?,'No Clicks Sessions (stage1)', ?, ? ),` +
            `(?,'Exit (Stage1)', ?, ? ),` +
            `(?,'Total Sessions (Stage1)', ?, ? ),` +
            `(?,'No Search Sessions (Stage1)', ?, ? ),` +
            `(?,'Support Sessions (Stage1)', ?, ? ),` +
            `(?,'Search Sessions (stage2)', ?, ? ),` +
            `(?,'Support Sessions (Stage2)', ?, ? ),` +
            `(?,'No Search Sessions (Stage2)', ?, ? ),` +
            `(?,'Click Sessions (Stage2)', ?, ? ),` +
            `(?,'No Click Sessions (Stage2)', ?, ? ),` +
            `(?,'Case Created Sessions (Stage2)', ?, ? ),` +
            `(?,'Exit (Stage2)', ?, ? ),` +
            `(?,'Direct Pageview', ?, ? )`;

          let structuredArray = [];

          for (let p = 0; p < data.length; p++) {
            structuredArray.push(
              req.body.uid,
              data[p].custom_title_name,
              data[p].tooltip
            );
          }
          let qwe = connection[req.headers['tenant-id']].execute.query(
            query,
            structuredArray,
            function (error, result) {
              console.log(
                "============================== ERROR ===============",
                error
              );
              console.log(
                "query-------------------------------------------------------",
                qwe.sql
              );
              cb(error, result);
            }
          );
        },
      ],
    },
    (error, result) => {
      if (error) {
        res.send({ status: 400, error: error });
      } else {
        res.send({ status: 200, message: result });
      }
    }
  );
});

router.get("/getMergedResults", function (req, res, next) {
  var query = `SELECT merge_results FROM search_clients_to_content_objects where id = ?`;
  connection[req.headers['tenant-id']].execute.query(query, [req.query.id], function (err, response) {
    if (err) {
      res.send({ status: "402" });
    } else {
      let merge_results =
        response && response[0]
          ? JSON.parse(response[0].merge_results || "{}")
          : {};
      res.send({ status: "200", mergeResults: merge_results });
    }
  });
});

router.post("/saveMergedResults", function (req, res, next) {
  connection[req.headers['tenant-id']].execute.query(
    `UPDATE search_clients_to_content_objects SET merge_results = ? WHERE id = ?`,
    [req.body.mergedResults, req.body.objId],
    (error, results) => {
      if (error) {
        commonFunctions.errorlogger.error(error);
        res.sendStatus(500);
      } else {
          const scriptParams = req.body.scriptParams;
          if(scriptParams.toggleDisabled || scriptParams.primaryFieldUpdated) {
            
            kafkaLib.publishMessage({
              topic: config.get("kafkaTopic.processRequest"),
              messages: [{
                value: JSON.stringify({
                  type: 'ADD',
                  service: 'index-service',
                  tenantId: req.headers['tenant-id'],
                  comment: `merge_result_${scriptParams.contentSourceId}_${scriptParams.uid}_${req.headers['tenant-id']}`,
                  information: {
                    eventType: 'merge-result-cron',
                    executionMode: 'kafka',
                    kafkaTopic: config.get('kafkaTopic.versionUpdateMapping'),
                    kafkaValue: {
                      tenantId: req.headers['tenant-id'],
                      data: req.body,
                    },
                  },
                }),
                key: req.headers['tenant-id']
              }],
            });
        }
        res.send(results);
      }
    }
  );  
});
router.post("/shareAccessSettings", async (req, res) => {
  let allowedFurther = await accessCheck(
    req.headers.session.email,
    req.headers.session.roleId,
    req.body.id,
    req
  );
  if (!allowedFurther) {
    res.send({ flag: 401, message: "Unauthorized" });
  } else {
    if (req.body.task == "get") {
      let users = await commonFunctions.usersForAccessSettings(req);
      let accessDetails = await getDataFromDB(`SELECT email AS ownerEmail , sharedAccess FROM search_clients sc 
        WHERE id = '${req.body.id}'; `,req);

      let result = {};
      result.owner = [];
      result.admin = [];
      result.moderator = [];
      result.accessDetails = accessDetails[0];
      result.id = req.body.id;
      users.map((e) => {
        e["shareAccess"] = JSON.parse(accessDetails[0].sharedAccess).includes(
          e.email
        )
          ? true
          : accessDetails[0].ownerEmail.includes(e.email)
          ? true
          : false;
        if (e.email == accessDetails[0].ownerEmail) {
          result.owner.push(e);
        } else if (e.roleId == 1) {
          result.admin.push(e);
        } else if (e.roleId == 2) {
          result.moderator.push(e);
        }
      });
      res.send({ status: 200, data: result });
    } else if (req.body.task == "updateAray") {
      let q = `UPDATE search_clients SET sharedAccess = '${JSON.stringify(
        req.body.accessArray
      )}'
    WHERE id ='${req.body.id}';`;
      await getDataFromDB(q,req);
      res.send({ status: 200, data: `Access updated` });
    }
  }
});

const getDataFromDB = (queryString, req) => {
  return new Promise((resolve, reject) => {
    connection[req.headers['tenant-id']].execute.query(queryString, (err, rows) => {
      if (err) {
        console.log(err);
      }
      resolve(rows);
    });
  });
};

let accessCheck = async (email, roleId, platformId, req) => {
  let allowedSUuser = [/grazitti.com/, /searchunify.com/];
  /** All user with grazitti.com and searchunify.com are considered as Searchunify user */
  let searchUnifyUser =
    allowedSUuser[0].test(email) || allowedSUuser[1].test(email);
  /** Get Access control settings */
  let accessControlSettings = (
    await getDataFromDB(`select * from access_control_settings;`,req)
  )[0].searchClient;
  if (searchUnifyUser || roleId == 4 || accessControlSettings == 1) {
    return true;
  } else {
    let scDeatil = (
      await getDataFromDB(
        `SELECT * FROM search_clients WHERE id=${platformId};`,req
      )
    )[0];
    let userSCAccess = JSON.parse(scDeatil.sharedAccess);
    userSCAccess.push(scDeatil.email);
    return userSCAccess.includes(email);
  }
};

router.post("/updateSearchClientLastUpdated",(req, res) => {
  let currentUser = JSON.stringify({email:req.headers.session.email, name:req.headers.session.name});
  connection[req.headers['tenant-id']].execute.query(`UPDATE search_clients SET last_updated_by = ? WHERE uid = ?`,[currentUser, req.body.uid],(error, results) => {
      if (error) { console.error(error); res.sendStatus(500);
      } else { res.send({ status: 200, data: 'Last updated by information updated for search client' }); }
    }
  );
});

router.post("/saveAgentHelperSlackCreds",(req, res) => {
  if(!req.body.slackCreds) { return res.send({ status: 400, data: 'Insufficient data' }); }
  let clientId = req.body.slackCreds.clientId;
  let clientSecret = req.body.slackCreds.clientSecret;
  if (!(
    (!clientId.trim() && !clientSecret.trim()) 
    || (clientId.trim() && clientSecret.trim()))) return res.send({
      message: 'Insufficient data!'
    });
  clientId = clientId.trim().length ? commonFunctions.encryptDecryptCreds(clientId, 'encrypt') : clientId;
  clientSecret = clientSecret.trim().length ? commonFunctions.encryptDecryptCreds(clientSecret, 'encrypt') : clientSecret;
  const dataStringified = clientId ? JSON.stringify({clientId,clientSecret}) : null;
    connection[req.headers['tenant-id']].execute.query(`UPDATE search_clients SET agentHelperSlackCreds = ? WHERE uid = ?`,[dataStringified, req.body.uid],(error, results) => {
      if (error) { commonFunctions.errorlogger.error(error); res.sendStatus(500);
      } else { res.send({ status: 200, data: 'Updated Successfully' }); }
    }
  );
});

router.get("/isS3Supported",(req, res) => {
  if(isCloudFrontSupported) {
    res.send({ status: 200, data: cloudFrontURL });
  } else {
    res.send({ status: 200, data: false });
  }
});

router.post("/previewPath",(req, res) => {
  let s3PreviewPath =  `${cloudFrontURL}/${md5(req.headers['tenant-id'])}/${searchClientPrefix}/${req.body.uid}/${req.body.file ? 'download' : 'preview'}/index.html`;
  res.send({ status: 200, data: s3PreviewPath });
});

router.post("/iconsPreviewPath",(req, res) => {
  let s3PreviewPath =  isCloudFrontSupported ? `${cloudFrontURL}/${md5(req.headers['tenant-id'])}/Asset-Library/${req.body.file}` : `${config.get("adminURL")}/resources/Asset-Library/${md5(req.headers['tenant-id'])}/${req.body.file}`;
  res.send({ status: 200, data: s3PreviewPath });
  if(isCloudFrontSupported) {
    var body = {
      path: `${cloudFrontURL}/${md5(req.headers['tenant-id'])}/Asset-Library/${req.body.file}`,
      fileName: req.body.file
    };
    var options = {
      method: "POST",
      url: config.get("searchClient.url") + `/sc/searchClient/uploadIconToS3`,
      json: true,
      headers: { 'content-type': 'application/json', 'Cookie': req.headers.cookie, 'CSRF-Token' : req.headers['csrf-token']},
      rejectUnauthorized: false,
      body: body
    };
    request(options, function (error, response, result) {
      if (error || (response && response.statusCode != 200)) {
        console.log('error', error);
      } else {
        console.log('success', response.body);
      }
    });
  }
});

router.get("/getTenantKey",(req, res) => {
  let tenantKey =  `${md5(req.headers['tenant-id'])}`;
  res.send({ status: 200, data: tenantKey });
});

let getSCCreatedDate = async (scID, req) => {
  let scCreatedDate = (await getDataFromDB(`select created_date from search_clients WHERE id=${scID};`, req))[0];
  return scCreatedDate && scCreatedDate.created_date;
};

let checkOSMigration = async (scCreated, req) => {
  var scCreatedDate = new Date(scCreated);
  var currentDate = new Date();
  var calculateDate = new Date();
  calculateDate.setDate(currentDate.getDate() - osMigrationDays);
  if (osMigration && (scCreatedDate <= calculateDate) ) {
    return true; // sc was created 5 days ago or earlier
  }
};
const getMeanVectorProgress = async (req, cb) => {
  let obj = {
      tenantId: req.headers['tenant-id']
  }
  if (req.query.uid || req.body.uid) { obj.uid = req.query.uid || req.body.uid; }
  let options = {
      method: "GET",
      rejectUnauthorized: false,
      url: config.get("indexService.url") + "/index-service/getMeanVectorProgress",
      headers: {
          "Content-Type": "application/json",
          "tenant-id": req.headers['tenant-id'],
          "index-service-secret": config.get("indexService.sharedSecret"),
      },
      body: obj,
      json: true
  };
  request(options, async function (error, response, body) {
    commonFunctions.errorlogger.info('VectorProgress::::::::::::::', body);
      if (error) {
        commonFunctions.errorlogger.error('Error while fetching Vector Progress Status: ',error)
        cb(null);
      }
      else cb(null,body);
  })
}

router.get('/getMeanVectorProgress', async (req, res) => {
  getMeanVectorProgress(req, (err, result) => {
    if (err) res.send({});
    else res.send(result);
  });
});

router.post('/getChromeSSOConfig', async (req, res) => {
  if(req.body.action == 'get') {
    connection[req.headers['tenant-id']].execute.query('SELECT sso_config FROM search_client_sso_config WHERE search_client_id = ?', [req.body.id], (error, result) => {
      if (error) {
        commonFunctions.errorlogger.error("error", error);
        res.send({ flag: 403, error: error });
      }
  
      res.send({ flag: 200, data: (result && result[0].sso_config) || [] });
    });
  }

  if(req.body.action == 'save') {
    connection[req.headers['tenant-id']].execute.query('UPDATE search_client_sso_config SET sso_config = ? WHERE search_client_id = ?', [req.body.data, req.body.id], (error, result) => {
      if (error) {
        commonFunctions.errorlogger.error("error", error);
        res.send({ flag: 403, error: error });
      }
  
      res.send({ flag: 200, data: 'success' });
    });
  }
});
  
router.get("/getDefaultConfig", async (req, res) =>{
  try {
    const synonymsLimit = config.synonymsLimit !== undefined ? config.synonymsLimit : 1000;
    res.send({ synonymsLimit });
  } catch (error) {
    res.status(500).send({ error: "Failed to retrieve configuration." });
  }
});

router.get("/getSCCreatedStatus", async (req, res) =>{
  try {
    const sql = `select sc_creating_status from search_clients WHERE id = ? AND uid = ?;`;
    connection[req.headers['tenant-id']].execute.query(sql, [req.query.id, req.query.uid], (error, result) => {
      if (error) {
        commonFunctions.errorlogger.error(`Failed to get search client created status`, error);
        return res.status(500).send({ error: "Failed to retrieve configuration." });
      }
      res.send({ status: 200, data: result && result[0] });
    });
    
  } catch (error) {
    res.status(500).send({ status: 403, error: "Failed to retrieve configuration." });
  }
});


router.get('/getSCToggles', async (req, res) => {
  connection[req.headers['tenant-id']].execute.query('SELECT scToggles FROM search_clients WHERE uid = ?', [req.query.uid], (error, result) => {
    if (error) {
      commonFunctions.errorlogger.error("error", error);
      res.send({ flag: 403, error: error });
    }

    res.send({ flag: 200, data: (result && result[0]) || [] });
  });
});

router.post('/setSCToggles', async (req, res) => {
  const {uid, toggleColumnData } = req.body.data;
  connection[req.headers['tenant-id']].execute.query('UPDATE search_clients SET scToggles = ? WHERE uid = ?', [toggleColumnData, uid], (error, result) => {
    if (error) {
      commonFunctions.errorlogger.error("error", error);
      res.send({ flag: 403, error: error });
    }

    res.send({ flag: 200, data: (result && result[0]) || [] });
  });
});
module.exports.router= router;
module.exports.getPlatforms= getPlatforms;
module.exports.deletePlatform= deletePlatform;
module.exports.getSearchClient = getSearchClient;
module.exports.publishDeleteSCEvent = publishDeleteSCEvent;
module.exports.cloneSearchClient = cloneSearchClient;
