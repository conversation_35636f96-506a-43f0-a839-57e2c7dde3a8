class RateLimitInfo {
    constructor() {
      if (!RateLimitInfo.instance) {
        RateLimitInfo.instance = this;
        this.rateLimitBurst = {};
        this.rateLimitQuota = {};
      }
      return RateLimitInfo.instance;
    }

    set(key,data){
        console.log(`>>>>Updating RateLimit Info for ${key}`,data);
        switch (key) {
            case "BURST":
                this.rateLimitBurst = data;
              break; // Exit the switch block
            case "QUOTA":
                this.rateLimitQuota = data;
                break;
            default:
                console.log("InValid Key Passed!");
        }
        console.log("Updating RateLimit Info");
    }

    get(key){
        console.log(`>>>>RateLimit Info for ${key}`);
        switch (key) {
            case "BURST":
                return this.rateLimitBurst;
            case "QUOTA":
                return this.rateLimitQuota;
            default:
                console.log("InValid Key Passed!");
        }
    }
}

module.exports = new RateLimitInfo();