/**
 * <AUTHOR>
 * @version 1
 */
var path = require('path');
var elastic = require('elasticsearch');
var fs = require('fs');
var async = require('async');
var request = require('request');
var express = require('express');
environment = require(path.join(__dirname, '../../routes/environment'));
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../config');
process.env.NODE_CONFIG_DIR = configPath;
console.log(environment.configuration);
config = require('config');
var connection_sql = require('../../utils/connection');
var moment = require('moment');
var analyticsCommon = require('../../routes/analytics/common');
var commonFunction = require('../../utils/commonFunctions')


var client = new elastic.Client({
    host: 'http://' + config.get('elasticIndex.host') + ':' + config.get('elasticIndex.port')
});


var analytics_index = config.get('elasticIndex.dailyAnalytics') + "_" + config.get('poc_name');
console.log("INDEX NAME :: " + analytics_index);
var analytics_type = 'daily_analytics_type';

var currentDate = moment(new Date(new Date())).format('YYYY-MM-DD');
var startDate = '2018-01-01';
var endDate = moment(currentDate).subtract(1, 'days').format('YYYY-MM-DD');

connection_sql.handleDisconnect().then(function (result) {
    creatingIndex(function (err, data) {
        if (err) {
            console.log("Error in creating index");
        } else {
            // This function need's to be discussed to check about SQL Connection.
                    // SQL ISSUE
            fetchElasticData(data.isIndexExists, data.startDate, data.endDate);
        }
    })
});

let dailyAnalyticsMapping = { "mappings": { "daily_analytics_type": { "properties": { "case_count": { "type": "long" }, "clicks": { "type": "long" }, "date": { "type": "date", "format": "strict_date_optional_time||epoch_millis" }, "failed_searches": { "type": "long" }, "internal": { "type": "boolean" }, "no_click_searches": { "type": "long" }, "search_users": { "type": "long" }, "searches": { "type": "long" }, "uid": { "type": "string", "index": "not_analyzed" }, "unique_failed_searches": { "type": "long" }, "users_visited_support": { "type": "long" }, "users_with_failed_searches": { "type": "long" }, "visitor": { "type": "long" }, "with_result": { "type": "long" }, "without_result": { "type": "long" } } } } }

function creatingIndex(cb) {
    async.auto({
        "checkIndex": cb => {
            console.log('CHECK INDEX IF EXIST');
            client.indices.exists({
                index: analytics_index
            },
                function (err, res) {
                    if (err) {
                        cb(err, null);
                    }
                    else {
                        cb(null, res);
                    }
                });
        },
        "createIndex": ["checkIndex", function (dataFromAbove, cb) {
            let queryData = {};
            if (!dataFromAbove.checkIndex) {
                client.indices.create({
                    index: analytics_index,
                    body: dailyAnalyticsMapping
                },
                    function (err, result) {
                        if (err) {
                            cb(err, null);
                        }
                        else {
                            console.log('CREATING NEW INDEX');
                            queryData.isIndexExists = false;
                            queryData.startDate = startDate;
                            queryData.endDate = endDate;
                            cb(null, queryData);
                        }
                    });
            }
            else {
                console.log("INDEX ALREADY PRESENT");
                client.search({ index: analytics_index, type: analytics_type }, (err, resp) => {
                    if (resp.hits.total > 0) {
                        startDate = moment(currentDate).subtract(1, "days").format('YYYY-MM-DD');
                        queryData.isIndexExists = true;
                        queryData.startDate = startDate;
                        queryData.endDate = currentDate;
                        cb(null, queryData);
                    } else {
                        queryData.isIndexExists = false;
                        queryData.startDate = startDate;
                        queryData.endDate = endDate;
                        cb(null, queryData);
                    }
                });
            }
        }]
    },
        function (err, data) {
            if (err) {
                console.log(err);
                cb(err, null);
            }
            else {
                cb(null, data.createIndex);
            }
            console.log("NEW INDEX CREATED");
        })
}

function fetchElasticData(indexExists, startDate, endDate) {
    let splitAnalyticsIndex = '';
    let support_url = "";
    let newStartDate = startDate;
    newStartDate = newStartDate.split("-");
    let newDate = newStartDate[1] + "/" + newStartDate[2] + "/" + newStartDate[0];
    let finalStartDate = new Date(newDate).getTime();

    let newEndDate = endDate;
    newEndDate = currentDate;
    newEndDate = newEndDate.split("-");
    let newLastDate = newEndDate[1] + "/" + newEndDate[2] + "/" + newEndDate[0];
    let finalEndDate = new Date(newLastDate).getTime();

    let queryData = {
    }

    let finalArray = [];
    let clientIds = [];
    let dateArray = [];
    let array = [];
    var dsl = { "from": 0, "size": 0, "query": { "bool": { "should": [{ "range": { "ts": { "gte": "2019-04-01||/d", "lte": "2019-04-16||/d", "format": "yyyy-MM-dd" } } }, { "range": { "search_date": { "gte": "2019-04-01||/d", "lte": "2019-04-16||/d", "format": "yyyy-MM-dd" } } }], } }, "aggregations": { "uniqueVisitorsTsHistogram": { "date_histogram": { "field": "ts", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "unique_visitor": { "cardinality": { "field": "cookie" } } } }, "uniqueVisitorsSearchDateHistogram": { "date_histogram": { "field": "search_date", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "unique_visitor": { "cardinality": { "field": "cookie" } }, "query": { "filter": { "bool": { "minimum_should_match": 1 } } } } }, "uniqueVisitorsTsHistogram": { "date_histogram": { "field": "ts", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "unique_visitor": { "cardinality": { "field": "cookie" } } } }, "uniqueFailedSearchesHistogram": { "date_histogram": { "field": "search_date", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "query": { "filter": { "bool": { "must": [{ "term": { "isClicked": false } }] } } }, "unique_searches": { "cardinality": { "field": "text_entered" } } } }, "totalClicksHistogram": { "date_histogram": { "field": "search_date", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "conversions": { "sum": { "script": "if(_source.containsKey(\"conversion\")) return _source.conversion.values.size(); else return 0;" } }, } }, "noClickSearchesHistogram": { "date_histogram": { "field": "search_date", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "query": { "filter": { "bool": { "must": [{ "term": { "isClicked": false } }], "must_not": [{ "term": { "result_count": 0 } }] } } }, } }, "usersWithFailedSearchesHistogram": { "date_histogram": { "field": "search_date", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "query": { "filter": { "term": { "isClicked": false, } } }, "unique_search_users": { "cardinality": { "field": "cookie" } } } }, "searchWithoutResultsHistogram": { "date_histogram": { "field": "search_date", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "query": { "filter": { "bool": { "must": [{ "match": { "result_count": { "query": 0 } } }] } } } } }, "failedSearchHistogram": { "date_histogram": { "field": "search_date", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "query": { "filter": { "bool": { "must": [{ "match": { "result_count": { "query": 0 } } }] } } }, "query2": { "filter": { "bool": { "must": [{ "term": { "isClicked": false } }], "must_not": [{ "term": { "result_count": 0 } }] } } } } }, "casesLoggedhistogram": { "date_histogram": { "field": "ts", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "query": { "filter": { "term": { "event": "caseCreated" } } } } }, "searchWithResultHistogram": { "date_histogram": { "field": "search_date", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "query": { "filter": { "bool": { "must_not": { "match": { "result_count": { "query": 0 } } } } } } } }, "totalSearchHistogram": { "date_histogram": { "field": "search_date", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } } }, "uniqueSearchUsers": { "date_histogram": { "field": "search_date", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "unique_search_users": { "cardinality": { "field": "cookie" } } } }, "usersVisitedSupportHistogram": { "date_histogram": { "field": "ts", "interval": "day", "format": "yyyy-MM-dd", "min_doc_count": 0, "extended_bounds": { "min": finalStartDate, "max": finalEndDate } }, "aggs": { "query": { "filter": { "bool": { "should": [{ "regexp": { "url": support_url } }, { "regexp": { "targetUrl": support_url } }], "minimum_should_match": 1 } } }, "unique_sessions": { "cardinality": { "field": "cookie" } } } } } }

    async.waterfall([
        function (cb) {
            // fetch search clients
            let sql = 'SELECT * from search_clients';
            connection.query(sql, (err, searchClients) => {
                if (err) {
                    console.log("Error in fetching search clinets");
                    return cb(err);
                }
                else {
                    async.eachSeries(searchClients, (client, callback) => {
                        clientIds.push(client.uid);
                        callback();
                    }, () => {
                        cb(null, clientIds);
                    })
                }
            });
        },
        function (clients, cb) {
            let userType = [
                "All",
                "Internal User",
                "External User",
            ];
            cb(null, clients, userType);
        },
        function (clients, userType, cb) {
            // GET index
            analyticsCommon.get_split_indexes(startDate, endDate, (err, index) => {
                if (err) {
                    return cb(err);
                } else {
                    splitAnalyticsIndex = index; // 'analytics_colubridae19';
                    cb(null, clients, userType);
                }
            })
        },
        function (clients, userType, cb) {
            var dsl = {
                "from": 0,
                "size": 0,
                "query": {
                    "bool": {
                        "should": [{
                            "range": {
                                "search_date": {
                                    "gte": "2016-08-01||/M",
                                    "lte": "now",
                                    "format": "yyyy-MM-dd"
                                }
                            }
                        },
                    {
                        "range": {
                            "ts": {
                                "gte": "2016-08-01||/M",
                                "lte": "now",
                                "format": "yyyy-MM-dd"
                            }
                        }
                    }]
                    }
                },
                "aggregations": {
                    "uids": {
                        "terms": { "field": "uid", "size": 0 }
                    }
                }
            };

            var options = {
                method: 'POST',
                url: "http://" + config.get('elasticIndex.host') + ":" + config.get('elasticIndex.port') + "/" + splitAnalyticsIndex + '/_search',
                headers: { 'content-type': 'application/json' },
                body: dsl,
                json: true
            };
            request(options, (error, response, body) => {
                var newClientIds = [];
                if (body && body.aggregations && body.aggregations.uids && body.aggregations.uids.buckets) {
                    async.eachSeries(body.aggregations.uids.buckets, (uid, callback) => {
                        newClientIds.push(uid.key);
                        callback();
                    }, () => {
                        console.log("newclientIds", newClientIds);
                        cb(null, newClientIds, userType);
                    })
                }
                // cb(error, body.hits ? body.hits.total : 0);
            });

        },
        function (clients, userType, cb) {
            async.eachSeries(clients, (id, outerCallback) => {
                commonFunction.getSearchClientIdUsingUid(id, function (err, clientId) {
                    commonFunction.getSupportUrl(function (err, url) {
                        support_url = url;
                        if (url == null || url == 'null') {
                            support_url = "";
                        }
                        // cb(null, "");
                        queryData.uid = id;
                        dsl.aggregations.usersVisitedSupportHistogram.aggs.query.filter.bool.should[0].regexp.url = support_url;
                        dsl.aggregations.usersVisitedSupportHistogram.aggs.query.filter.bool.should[1].regexp.targetUrl = support_url;
                        async.eachSeries(userType, (user, outerInnerCallBack) => {
                            if (user == 'All') {
                                queryData.internalUser = 'all';
                                dateArray = [];
                                array = [];

                                if (startDate != '' && endDate != '') {
                                    dsl.query.bool.should[0].range.ts.gte = startDate + "||/d";
                                    dsl.query.bool.should[0].range.ts.lte = endDate + "||/d";
                                    dsl.query.bool.should[1].range.search_date.gte = startDate + "||/d";
                                    dsl.query.bool.should[1].range.search_date.lte = endDate + "||/d";
                                }
                                if (id != "") {
                                    if (!dsl.query.bool.must)
                                        dsl.query.bool.must = [];
                                    dsl.query.bool.must.push({ "term": { "uid": queryData.uid } });
                                }
                                var options = {
                                    method: 'POST',
                                    url: 'http://' + config.get("elasticIndex.host") + ':' + config.get("elasticIndex.port") + '/' + splitAnalyticsIndex + '/search_keyword,users,log,supportLog/_search',
                                    headers: {
                                        'content-type': 'application/json'
                                    },
                                    body: dsl,
                                    json: true
                                };
                                // fetchFinalElasticData(options, indexExists, queryData, finalArray, id, (err, data) => {
                                //     if (err) {
                                //         console.log("error", err);
                                //     } else {
                                //         outerInnerCallBack();
                                //     }
                                // });
                                outerInnerCallBack();
                            } else {
                                queryData.internalUser = (user == "Internal User") ? true : false;

                                dsl.query.bool.must = [];
                                dateArray = [];
                                array = [];

                                if (startDate != '' && endDate != '') {
                                    dsl.query.bool.should[0].range.ts.gte = startDate + "||/d";
                                    dsl.query.bool.should[0].range.ts.lte = endDate + "||/d";
                                    dsl.query.bool.should[1].range.search_date.gte = startDate + "||/d";
                                    dsl.query.bool.should[1].range.search_date.lte = endDate + "||/d";
                                }
                                if (id != "") {
                                    if (!dsl.query.bool.must)
                                        dsl.query.bool.must = [];
                                    dsl.query.bool.must.push({ "term": { "uid": queryData.uid } });
                                }
                                if (queryData.internalUser != undefined && queryData.internalUser != "all") {
                                    if (!dsl.query.bool.must)
                                        dsl.query.bool.must = [];
                                    if (queryData.internalUser == "true" || queryData.internalUser == true) {
                                        dsl.query.bool.must.push({ "term": { "internal": true } });
                                    } else {
                                        dsl.query.bool.must.push({ "term": { "internal": false } });
                                    }
                                }
                                var options = {
                                    method: 'POST',
                                    url: 'http://' + config.get("elasticIndex.host") + ':' + config.get("elasticIndex.port") + '/' + splitAnalyticsIndex + '/search_keyword,users,log,supportLog/_search',
                                    headers: {
                                        'content-type': 'application/json'
                                    },
                                    body: dsl,
                                    json: true
                                };
                                fetchFinalElasticData(options, indexExists, queryData, finalArray, id, (err, data) => {
                                    if (err) {
                                        console.log("error", err);
                                    } else {
                                        outerInnerCallBack();
                                    }
                                });
                            }
                        }, () => {
                            outerCallback();
                        })
                    }, clientId);
                });
            }, () => {
                var i;
                var j;
                var temp;
                var chunk = 100;
                var final = [];
                for (i = 0, j = finalArray.length; i < j; i += chunk) {
                    temp = finalArray.slice(i, i + chunk);
                    final.push(temp);
                    // bulk upload
                }
                async.eachSeries(final, (f, callback) => {
                    client.bulk({
                        body: f
                    }, function (err, res) {
                        if (err)
                            console.log(new Date() + ':' + 'Error while executing bulk queries: ' + JSON.stringify(err));
                        else
                            console.log(new Date() + ':' + 'after executing bulk queries: success');
                        callback();
                    });
                    console.log("DONE");
                }, () => {
                    cb();
                })
                // client.bulk({
                //     body: finalArray
                // }, function (err, res) {
                //     if (err)
                //         console.log(new Date() + ':' + 'Error while executing bulk queries: ' + JSON.stringify(err));
                //     else
                //         console.log(new Date() + ':' + 'after executing bulk queries: success');
                //     cb();
                // });
                // console.log("DONE");
            })
        }
    ], (err, data) => {

    })
}

function fetchFinalElasticData(options, indexExists, queryData, finalArray, id, getCallback) {
    request(options, (error, response, body) => {
        let dateArray = [];
        let result = {
            visitors: 0,
            search_users: 0,
            searches: 0,
            with_result: 0,
            clicks: 0,
            case_count: 0,
            failed_searches: 0,
            without_result: 0,
            unique_failed_searches: 0,
            users_with_failed_searches: 0,
            no_click_searches: 0,
            users_visited_support: 0
        };
        let array = [];
        let length = 0;

        if (indexExists == true || indexExists == 'true') {
            fetchNonAsyncData(dateArray, result, array, length, body, queryData, finalArray, id, (err, data) => {
                if (err) {
                    console.log("Non Async Error", err);
                } else {
                    getCallback(null, data);
                }
            });
        } else {
            fetchAsyncData(dateArray, result, array, length, body, queryData, finalArray, id, (err, data) => {
                if (err) {
                    console.log("Async Error", err);
                } else {
                    getCallback(null, data);
                }
            })
        }
    });
}


function fetchNonAsyncData(dateArray, result, array, length, body, queryData, finalArray, id, clbk) {
    async.waterfall([
        function (cb) {
            length = body.aggregations.uniqueVisitorsSearchDateHistogram.buckets.length;
            dateArray.push(body.aggregations.uniqueVisitorsSearchDateHistogram.buckets[length - 2].key_as_string);
            console.log("dateArray", dateArray);
            array.push({
                date: dateArray[0],
                uid: queryData.uid,
                internal: queryData.internalUser,
                visitor: 0, //body.aggregations.uniqueVisitorsSearchDateHistogram.buckets[length - 2].unique_visitor.value,
                search_users: 0,
                searches: 0,
                with_result: 0,
                clicks: 0,
                case_count: 0,
                failed_searches: 0,
                without_result: 0,
                unique_failed_searches: 0,
                users_with_failed_searches: 0,
                no_click_searches: 0,
                users_visited_support: 0
            });
            cb(null, {});
        },
        function (data, cb) {
            if (body.aggregations.uniqueVisitorsSearchDateHistogram.buckets && body.aggregations.uniqueVisitorsSearchDateHistogram.buckets.length) {
                length = body.aggregations.uniqueVisitorsSearchDateHistogram.buckets.length;
                let index = dateArray.indexOf(body.aggregations.uniqueVisitorsSearchDateHistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].visitor = body.aggregations.uniqueVisitorsSearchDateHistogram.buckets[length - 2].unique_visitor.value
                    result.visitors += body.aggregations.uniqueVisitorsSearchDateHistogram.buckets[length - 2].unique_visitor.value;
                }
                cb(null, data);
            } else {
                cb(null, data);
            }
        },
        function (data, cb) {
            if (body.aggregations.uniqueVisitorsTsHistogram.buckets && body.aggregations.uniqueVisitorsTsHistogram.buckets.length) {
                length = body.aggregations.uniqueVisitorsTsHistogram.buckets.length
                let index = dateArray.indexOf(body.aggregations.uniqueVisitorsTsHistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].visitor += body.aggregations.uniqueVisitorsTsHistogram.buckets[length - 2].unique_visitor.value;
                    result.visitors += body.aggregations.uniqueVisitorsTsHistogram.buckets[length - 2].unique_visitor.value;
                }
                cb(null, data);
            } else {
                cb(null, data);
            }
        },
        function (data, cb) {
            if (body.aggregations.uniqueFailedSearchesHistogram.buckets && body.aggregations.uniqueFailedSearchesHistogram.buckets.length) {
                length = body.aggregations.uniqueFailedSearchesHistogram.buckets.length;
                let index = dateArray.indexOf(body.aggregations.uniqueFailedSearchesHistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].unique_failed_searches += body.aggregations.uniqueFailedSearchesHistogram.buckets[length - 2].unique_searches.value;
                    result.unique_failed_searches += body.aggregations.uniqueFailedSearchesHistogram.buckets[length - 2].unique_searches.value;
                }
                cb(null, data);
            } else {
                cb(null, data);
            }
        },
        function (data, cb) {
            if (body.aggregations.totalClicksHistogram.buckets && body.aggregations.totalClicksHistogram.buckets.length) {
                length = body.aggregations.totalClicksHistogram.buckets.length;
                let index = dateArray.indexOf(body.aggregations.totalClicksHistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].clicks += body.aggregations.totalClicksHistogram.buckets[length - 2].conversions.value;
                    result.clicks += body.aggregations.totalClicksHistogram.buckets[length - 2].conversions.value;
                }
                cb(null, data);
            } else {
                cb(null, data);
            }
        },
        function (data, cb) {
            if (body.aggregations.noClickSearchesHistogram.buckets && body.aggregations.noClickSearchesHistogram.buckets.length) {
                length = body.aggregations.noClickSearchesHistogram.buckets.length;
                let index = dateArray.indexOf(body.aggregations.noClickSearchesHistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].no_click_searches += body.aggregations.noClickSearchesHistogram.buckets[length - 2].query.doc_count;
                    result.no_click_searches += body.aggregations.noClickSearchesHistogram.buckets[length - 2].query.doc_count;
                }
                cb(null, data);
            } else {
                cb(null, data);
            }
        },
        function (data, cb) {
            if (body.aggregations.usersWithFailedSearchesHistogram.buckets && body.aggregations.usersWithFailedSearchesHistogram.buckets.length) {
                length = body.aggregations.usersWithFailedSearchesHistogram.buckets.length;
                let index = dateArray.indexOf(body.aggregations.usersWithFailedSearchesHistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].users_with_failed_searches += body.aggregations.usersWithFailedSearchesHistogram.buckets[length - 2].unique_search_users.value;
                    result.users_with_failed_searches += body.aggregations.usersWithFailedSearchesHistogram.buckets[length - 2].unique_search_users.value;
                }
                cb(null, data);
            } else {
                cb(null, data);
            }
        },
        function (data, cb) {
            if (body.aggregations.searchWithoutResultsHistogram.buckets && body.aggregations.searchWithoutResultsHistogram.buckets.length) {
                length = body.aggregations.searchWithoutResultsHistogram.buckets.length
                let index = dateArray.indexOf(body.aggregations.searchWithoutResultsHistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].without_result += body.aggregations.searchWithoutResultsHistogram.buckets[length - 2].query.doc_count;
                    result.without_result += body.aggregations.searchWithoutResultsHistogram.buckets[length - 2].query.doc_count;
                }
                cb(null, data);
            } else {
                cb(null, data);
            }
        },
        function (data, cb) {
            if (body.aggregations.casesLoggedhistogram.buckets && body.aggregations.casesLoggedhistogram.buckets.length) {
                length = body.aggregations.casesLoggedhistogram.buckets.length
                let index = dateArray.indexOf(body.aggregations.casesLoggedhistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].case_count += body.aggregations.casesLoggedhistogram.buckets[length - 2].query.doc_count;
                    result.case_count += body.aggregations.casesLoggedhistogram.buckets[length - 2].query.doc_count;
                }
                cb(null, data);
            } else {
                cb(null, data);
            }

        },
        function (data, cb) {
            if (body.aggregations.searchWithResultHistogram.buckets && body.aggregations.searchWithResultHistogram.buckets.length) {
                length = body.aggregations.searchWithResultHistogram.buckets.length
                let index = dateArray.indexOf(body.aggregations.searchWithResultHistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].with_result += body.aggregations.searchWithResultHistogram.buckets[length - 2].query.doc_count;
                    result.with_result += body.aggregations.searchWithResultHistogram.buckets[length - 2].query.doc_count;
                }
                cb(null, data);
            } else {
                cb(null, data);
            }
        },
        function (data, cb) {
            if (body.aggregations.totalSearchHistogram.buckets && body.aggregations.totalSearchHistogram.buckets.length) {
                length = body.aggregations.totalSearchHistogram.buckets.length
                let index = dateArray.indexOf(body.aggregations.totalSearchHistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].searches += body.aggregations.totalSearchHistogram.buckets[length - 2].doc_count;
                    result.searches += body.aggregations.totalSearchHistogram.buckets[length - 2].doc_count;
                }
                cb(null, data);
            } else {
                cb(null, data);
            }

        },
        function (data, cb) {
            if (body.aggregations.uniqueSearchUsers.buckets && body.aggregations.uniqueSearchUsers.buckets.length) {
                length = body.aggregations.uniqueSearchUsers.buckets.length
                let index = dateArray.indexOf(body.aggregations.uniqueSearchUsers.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].search_users += body.aggregations.uniqueSearchUsers.buckets[length - 2].unique_search_users.value;
                    result.search_users += body.aggregations.uniqueSearchUsers.buckets[length - 2].unique_search_users.value;
                }
                cb(null, data);
            } else {
                cb(null, data);
            }

        },
        function (data, cb) {
            if (body.aggregations.failedSearchHistogram.buckets && body.aggregations.failedSearchHistogram.buckets.length) {
                length = body.aggregations.failedSearchHistogram.buckets.length
                let index = dateArray.indexOf(body.aggregations.failedSearchHistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].failed_searches += (parseInt(body.aggregations.failedSearchHistogram.buckets[length - 2].query.doc_count, 10) + parseInt(body.aggregations.failedSearchHistogram.buckets[length - 2].query2.doc_count, 10));
                    result.failed_searches += (parseInt(body.aggregations.failedSearchHistogram.buckets[length - 2].query.doc_count, 10) + parseInt(body.aggregations.failedSearchHistogram.buckets[length - 2].query2.doc_count, 10));
                }
                cb(null, data);
            } else {
                cb(null, data);
            }
        },
        function (data, cb) {
            if (body.aggregations.usersVisitedSupportHistogram.buckets && body.aggregations.usersVisitedSupportHistogram.buckets.length) {
                length = body.aggregations.usersVisitedSupportHistogram.buckets.length;
                let index = dateArray.indexOf(body.aggregations.usersVisitedSupportHistogram.buckets[length - 2].key_as_string.toString());
                if (array[index]) {
                    array[index].users_visited_support += body.aggregations.usersVisitedSupportHistogram.buckets[length - 2].query.doc_count;
                    result.users_visited_support += body.aggregations.usersVisitedSupportHistogram.buckets[length - 2].query.doc_count;
                }

                cb(null, data);
            } else {
                cb(null, data);
            }
        }
    ], (err, finalData) => {
        async.eachSeries(array, (element, callback) => {
            finalArray.push({ index: { _index: analytics_index, _type: analytics_type, _id: element.date.toString() + id + queryData.internalUser } });
            finalArray.push(element);
            callback();
        }, () => {
            clbk(null, finalArray);
        })
    })
}


function fetchAsyncData(dateArray, result, array, length, body, queryData, finalArray, id, clbk) {
    async.waterfall([
        function (cb) {
            async.eachSeries(body.aggregations.uniqueVisitorsSearchDateHistogram.buckets, (tsVisitor, callback) => {
                dateArray.push(tsVisitor.key_as_string);
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.uniqueVisitorsSearchDateHistogram.buckets, (visitor, callback) => {
                let index = dateArray.indexOf(visitor.key_as_string.toString());
                array.push({
                    date: dateArray[index],
                    uid: queryData.uid,
                    internal: queryData.internalUser,
                    visitor: visitor.unique_visitor.value,
                    search_users: 0,
                    searches: 0,
                    with_result: 0,
                    clicks: 0,
                    case_count: 0,
                    failed_searches: 0,
                    without_result: 0,
                    unique_failed_searches: 0,
                    users_with_failed_searches: 0,
                    no_click_searches: 0,
                    users_visited_support: 0
                });
                result.visitors += visitor.unique_visitor.value;
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.uniqueVisitorsTsHistogram.buckets, (visitor, callback) => {
                let index = dateArray.indexOf(visitor.key_as_string.toString());
                if (array[index]) {
                    array[index].visitor += visitor.unique_visitor.value;
                    result.visitors += visitor.unique_visitor.value;
                }
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.uniqueFailedSearchesHistogram.buckets, (uniqueFailed, callback) => {
                let index = dateArray.indexOf(uniqueFailed.key_as_string.toString());
                if (array[index]) {
                    array[index].unique_failed_searches += uniqueFailed.unique_searches.value;
                    result.unique_failed_searches += uniqueFailed.unique_searches.value;
                }
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.totalClicksHistogram.buckets, (totalClick, callback) => {
                let index = dateArray.indexOf(totalClick.key_as_string.toString());
                if (array[index]) {
                    array[index].clicks += totalClick.conversions.value;
                    result.clicks += totalClick.conversions.value;
                }

                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.noClickSearchesHistogram.buckets, (noClick, callback) => {
                let index = dateArray.indexOf(noClick.key_as_string.toString());
                if (array[index]) {
                    array[index].no_click_searches += noClick.query.doc_count;
                    result.no_click_searches += noClick.query.doc_count;
                }
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.usersWithFailedSearchesHistogram.buckets, (userWithFailed, callback) => {
                let index = dateArray.indexOf(userWithFailed.key_as_string.toString());
                if (array[index]) {
                    array[index].users_with_failed_searches += userWithFailed.unique_search_users.value;
                    result.users_with_failed_searches += userWithFailed.unique_search_users.value;
                }
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.searchWithoutResultsHistogram.buckets, (withoutResult, callback) => {
                let index = dateArray.indexOf(withoutResult.key_as_string.toString());
                if (array[index]) {
                    array[index].without_result += withoutResult.query.doc_count;
                    result.without_result += withoutResult.query.doc_count;
                }
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.casesLoggedhistogram.buckets, (cases, callback) => {
                let index = dateArray.indexOf(cases.key_as_string.toString());
                if (array[index]) {
                    array[index].case_count += cases.query.doc_count;
                    result.case_count += cases.query.doc_count;
                }
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.searchWithResultHistogram.buckets, (withResult, callback) => {
                let index = dateArray.indexOf(withResult.key_as_string.toString());
                if (array[index]) {
                    array[index].with_result += withResult.query.doc_count;
                    result.with_result += withResult.query.doc_count;
                }
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.totalSearchHistogram.buckets, (totalSearch, callback) => {
                let index = dateArray.indexOf(totalSearch.key_as_string.toString());
                if (array[index]) {
                    array[index].searches += totalSearch.doc_count;
                    result.searches += totalSearch.doc_count;
                }
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.uniqueSearchUsers.buckets, (uniqueSearchUser, callback) => {
                let index = dateArray.indexOf(uniqueSearchUser.key_as_string.toString());
                if (array[index]) {
                    array[index].search_users += uniqueSearchUser.unique_search_users.value;
                    result.search_users += uniqueSearchUser.unique_search_users.value;
                }
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.failedSearchHistogram.buckets, (failedSearch, callback) => {
                let index = dateArray.indexOf(failedSearch.key_as_string.toString());
                if (array[index]) {
                    array[index].failed_searches += (parseInt(failedSearch.query.doc_count, 10) + parseInt(failedSearch.query2.doc_count, 10));
                    result.failed_searches += (parseInt(failedSearch.query.doc_count, 10) + parseInt(failedSearch.query2.doc_count, 10));
                }
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        },
        function (data, cb) {
            async.eachSeries(body.aggregations.usersVisitedSupportHistogram.buckets, (supportUser, callback) => {
                let index = dateArray.indexOf(supportUser.key_as_string.toString());
                if (array[index]) {
                    array[index].users_visited_support += supportUser.query.doc_count;
                    result.users_visited_support += supportUser.query.doc_count;
                }
                callback();
            }, (err, data) => {
                cb(null, data);
            })
        }
    ], (err, finalData) => {
        // console.log("Array========", array);
        async.eachSeries(array, (element, callback) => {
            // console.log("element", element);
            finalArray.push({ index: { _index: analytics_index, _type: analytics_type, _id: element.date.toString() + id + queryData.internalUser } });
            finalArray.push(element);
            callback();
        }, () => {
            clbk(null, finalArray);
        })
    })
}