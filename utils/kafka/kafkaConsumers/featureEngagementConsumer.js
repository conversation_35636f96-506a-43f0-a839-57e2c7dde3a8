const config = require('config');
const kafkaLib = require("../kafka-lib");
const { processSCData } = require("../../../routes/admin/publishAllContent");
const { tenantGlobals } = require("../kafkaPublishers/tenantGlobals");

const subscribeConsumer = async () => {
    try {
        const consumer = await kafkaLib.getConsumer({
            groupId: `admin ${config.get("kafkaTopic.featureEngagementMigrationTopic")}`,
            topic: config.get("kafkaTopic.featureEngagementMigrationTopic"),
            otherproperties:{ fromBeginning: true },
        })
        await consumer.run({
            eachMessage: async ({
                message
            }) => {
                try {
                    let data = JSON.parse(message.value);

                    let sqlQuery = `SELECT id as searchClientId, search_client_type_id as scTypeId, uid FROM search_clients`;
                    const req = {
                        headers: {
                            'tenant-id': data.tenantId
                        }
                    };

                    await processSCData(sqlQuery, data.tenantId, req, null);
                    await tenantGlobals(data.tenantId);
                } catch (e) {
                    console.log(e)
                }
            }
        });
    } catch (error) {
        if (error.message === 'UNKNOWN_TOPIC_OR_PARTITION') {
            kafkaLib.createTopic(config.get("kafkaTopic.featureEngagementMigrationTopic"));
        }
    }
}

module.exports = {
    subscribeConsumer
}
