var express = require('express');
var router = express.Router();
var async = require('async');
var request = require("request");
var fs = require('fs');
const { spawn, exec } = require('child_process');
var path = require('path');
var multipart = require('connect-multiparty');
var multipartMiddleware = multipart();
var commonFunctions = require('./../../utils/commonFunctions');
var crawlerConfigCache = require('../../utils/crawler-config-cache');
const config = require('config');
const { getColumnsFromTables, getContentSourceData } = require('./publishAllContent');


router.get('/getCrawlerConfig',  function (req, res) {
    crawlerConfigCache.fetchCrawlerConfig({ tenantId: req.headers['tenant-id'], RETRY_COUNT: 30 }, function(response){
        res.send({success : true, data : {crawlerConfig: response}});
    });
});

router.get('/getAllDownloadedFiles', function(req, res){
    if (req.body.appId){
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret")
    }
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/crawler-config/get-downloaded-files', '', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({statusCode: 404, msg: 'Folder is empty'});
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);

            res.send(result);
        }
    })
});

router.get('/crawlerHealth', async (req, res, next) => {  
    let response = {};
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    let type = req.query.type;
    let healthPath = '/crawler-config/';
    if(type == "Backend DB") type = "mysql";
    else if(type == "Index DB") type = "index-db";
    else if(type == "stuckCrawlers") type = "stuck-crawlers";
    else if (type == "createMapping"){
        type = "/crawler-config/create-mapping";
        healthPath = '';
        delete req.query.type;
    }
    else if (type == "indexBackup"){
        type = "/crawler-config/backup-index";
        healthPath = '';
        delete req.query.type;
    } else if (type == "pollApiCheck") {
        type = "pollApiCheck";
        delete req.query.type;
    } else if (type == 'runningCrawlers') {
        type = "running-crawlers";
        delete req.query.type;
    } else if (type == 'redisCheck') {
        type = "cache-db";
        delete req.query.type;
    }

    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + healthPath + type, req.query, req.query, headers, function (error, result) {
        if (!result) {
            response = {
                status: 'false',
                message: 'some error occur while processing request',
                data: {}
            }
            result = response;
        }
        res.send(result);            
    });
});

router.get('/getCronTabJobs',  async (req, res, next) => {  
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        timeout: 15000
    }
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/crawler-config/get-cron-jobs', '', {}, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({error: true, msg: error});
        }
        else
            res.send(result);
    });
});

router.post('/debugContentSource', function(req, res){
    if (req.body.appId){
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/debug-content-source', '', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    })
});

router.get('/getCsConfiguration', function(req, res) {
    if (req.body.appId){
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/crawler-config/get-cs-configuration', '', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    })
});


router.post('/getConnectedUser', function(req, res) {
    commonFunctions.getContentSourceAuthorizationData(req.body.mysqlId, req.headers['tenant-id'], function(error, response) {
        if (req.body.appId){
            delete req.body.appId;
        }
        let headers = {
            "Content-Type": "application/json",
            "su-crawler-secret": config.get("crawler.sharedSecret"),
            "tenant-id": req.headers['tenant-id']
        };
        req.body.authorization = response;
        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/fetch-connected-cs-user', '', req.body, headers, function (error, result) {
            if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                res.send(error);
            }
            else {
                commonFunctions.errorlogger.info("result found: ", result.data);
                res.send(result);
            }
        });
    });
});

router.post('/register-webhook', function(req, res) {
    if (req.body.appId){
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        timeout: 60000
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/register', '', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    })
});
router.post('/allContentSource', function(req,res){
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/crawler-config/all-content-source`,'', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    })
});

router.post('/fetch-total-doc-count', function(req,res){
    if (req.body.appId){
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        timeout: 100000
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/crawler-config/fetch-total-doc-count-cs`,'', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    })
});


router.post('/websiteCsMigration', function(req, res){
    if (req.body.appId){
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        timeout: 100000
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/website-cs-migration','', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    })
});

router.post('/manageCronJobs', async (req, res, next) => {
    const body = {
        service: req.body.service
    }
    if (req.body.comment) {
        body.comment = req.body.comment;
    }
    if (req.body.condition) {
        body.condition = req.body.condition;
    }
    if (req.body.operation) {
        body.operation = req.body.operation;
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        timeout: 15000
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/crawler-config/manage-cron-job`,'',body, headers, function (error, result) {
        if (error || result.error) {
            const err = error || result.error;
            commonFunctions.errorlogger.error("Error found: ", err);
            res.send(err);
        }
        else
            res.send(result);
    });
});


router.get('/migrateCrons', async (req, res, next) => {  
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        timeout: 15000
    }
    console.log('headers');
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/crawler-config//migrate-cron-job', '', {}, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({error: true, msg: error});
        }
        else
            res.send(result);
    });
});

// router.post('/setCronJob',  async (req, res, next) => {
//     var setObj = {
//         addCronData: req.body.value
//     }
//     let headers = {
//         "Content-Type": "application/json",
//         "su-crawler-secret" : config.get("crawler.sharedSecret"),
//         "tenant-id": req.headers['tenant-id'] 
//     }
//     commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/setCronJob', '', setObj, headers, function (error, result) {
//         if (error) {
//             commonFunctions.errorlogger.error("Error found: ", error);
//             res.send(error);
//         }
//         else {
//             res.send(result);
//         }
//     });
// });

router.post('/resetCrawlerConfig', async function (req, res, next) {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    let reqBody = { config_key: req.body.config_key };
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/reset', '', reqBody, headers, ( err, result) =>{
        if(err){
            res.send(err);
            return ;
        }
        res.send(result.data);
    })
});

router.post('/updateCrawlerConfig', function (req, res, next) {
    let reqBody = {
        config_key: req.body.config_key,
        config_value: req.body.config_value,
        session: req.headers.session
    };
    crawlerConfigCache.updateCrawlerConfig(reqBody, req.headers['tenant-id'], (err, result) => {
        if (err) {
           res.send(err);
        } else {
           res.send(result.data);
        }
    });
});

router.post('/cs-delete-cron', async (req, res, next) => {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/cs-delete-cron', '', req.body.value, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.send(result);
        }
    });
});

router.post('/update-cs-crawl-status', async (req, res, next) => {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }

    const { contentSourceId, crawlStatusDetails } = req.body.value;
    
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/update-cs-crawl-status', '', req.body.value, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            if (result.status) {
                let updateCsSql = "UPDATE content_sources SET ? WHERE id=?"
                connection[req.headers['tenant-id']].execute.query(updateCsSql, [{
                    crawl_status: crawlStatusDetails.crawlStatus || 2,
                    pid: crawlStatusDetails.processId || 0
                }, contentSourceId], function (errAsync, rows) {
                    if (errAsync) {
                        commonFunctions.errorlogger.error('Error while updating content source data', errAsync);
                    }
                });
    
                let updateObjectSql = "UPDATE content_source_objects SET object_pid=? WHERE content_source_id=? and object_pid > 0";
                const objectPid = crawlStatusDetails.processId || 0;
                connection[req.headers['tenant-id']].execute.query(updateObjectSql, [objectPid , contentSourceId], function (errAsync, rows) {
                    if (errAsync) {
                      commonFunctions.errorlogger.error('Error while updating content object source data', errAsync);
                    }
                });
            }

            res.send(result);
        }
    });
});


router.post('/dumpClickBoostingFile', function(req, res){
    if (req.body.appId){
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret")
    }
    req.body.tenantId = req.headers['tenant-id'];
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/dump-click-boosting-file', '', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({statusCode: 404, mssg: 'Dump Boosting File Not '});
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);

            res.send(result.data);
        }
    })
});

router.post('/uploadClickBoostingScore', function(req, res, next){
   
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    req.body.tenantId = req.headers['tenant-id'];
    if(req.body.appId){
        delete req.body.appId;
    }
    
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/upload-click-boosting-score', '', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({statusCode: 404, mssg: 'Click Boosting File Not Uploaded'});
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result.data);
        }
    })
});

router.post('/uploadClickBoostingFile', multipartMiddleware, function(req, res, next){
    let reqBody = {
        uploadFile: fs.createReadStream(req.files.uploadFile.path),
        filename : req.files.uploadFile.name
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    
    request.post({ url: config.get("crawler.crawlerUrl") + '/crawler-config/upload-file', 
     formData : reqBody,
     headers 
    }, function (error, response, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({statusCode: 404, mssg: 'Dump Boosting File Not Uploaded'});
        }
        else {
            let resultData;
            if(typeof result === 'string'){
                resultData =  JSON.parse(result);
             if(!resultData.data.status) {
                  res.send({statusCode: 404, mssg: resultData.data.message || ''});
                }
            }
            commonFunctions.errorlogger.info("result found: ", result);
            res.send({statusCode: 200, mssg: resultData.data.message});
        }
    })
});


router.get('/crawlRequestConfig',  async (req, res, next) => {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/crawler-config/crawl-request-config/','', '', headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.send(result);
        }
    });
})

router.put('/crawlRequestConfig', async function (req, res, next) {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    const configObj = req.body;
    delete configObj.appId;
    commonFunctions.httpRequest('PUT', config.get("crawler.crawlerUrl") + '/crawler-config/crawl-request-config/', '', configObj, headers, ( err, result) =>{
        if(err){
            res.send(err);
            return ;
        }
        res.send(result);
    })
});


router.get('/crawlRequests',  async (req, res, next) => {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/crawler-config/crawl-requests/?status=${req.query.status}`,'', '', headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.send(result);
        }
    });
});

router.delete('/crawlRequests',  async (req, res, next) => {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    let url='/crawler-config/crawl-requests/';
    if(req.query.crawlRequestId){
        url +=`?crawlRequestId=${req.query.crawlRequestId}`
    }
    else if(req.query.status){
        url+=`?status=${req.query.status}`
    }
    commonFunctions.httpRequest('DELETE', config.get("crawler.crawlerUrl") + url,'', '', headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.send(result.data);
        }
    });
});

router.post('/updateFrequencyFields',  async (req, res, next) => {
    var updateObj = {
        content_source_type_id: req.body.content_source_type_id,
        setObj:  req.body.setObj
    };

    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/update-frequency-fields', '', updateObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({error: true, msg: error});
        }
        if(result.status) {
           res.send(result);
        }else {
            res.send({error: true, msg: result.data});
        }
    });
});

router.post('/sanitizeText', function(req, res){
    if (req.body.appId){
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/sanitize-text', '', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    })
});

router.get('/getClickBoostingData', function(req, res){
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/click-boosting/download?fileName=${req.query.fileName}`, '', '', headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            return res.send(error);
        }
        else {
            let response =  '';
            if(result && result.length) {
                response = result;
            }
           
            return res.send(response);
        }
    })
});

router.delete('/deleteAllAgendaRequests',  async (req, res, next) => {
    delete req.body.appId;
   
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }

    let csRes = await commonFunctions.resetCsFrequencyToNever(req.headers['tenant-id']);
    if (!csRes){
        commonFunctions.errorlogger.error("some error occurred");
        res.send('some error occurred');
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/crawler-config/reset-cs-frequency`,'', '', headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.send(result);
        }
    });
});


router.get('/undoMigrations',  async (req, res, next) => {
    const { filename } = req.query;
    let headers = {
        "mt-auth-secret": config.get("multiTenantAuthSecret"),
    }
    commonFunctions.httpRequest('GET', config.get("authUrl") + `/utility/manage-migrations?action=undo&filename=${filename}`,'', req.body, headers, function (error, result) { 
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.status(500).json({ error: error.message });
        }
        else {
            res.status(200).json(result)
        }
    });
 });
 
router.get('/manageMigrations', async (req, res, next) => {
    let headers = {
        "mt-auth-secret": config.get("multiTenantAuthSecret"),
    }
    commonFunctions.httpRequest('GET', config.get("authUrl") + `/utility/manage-migrations?action=undoAndPlay`, '', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.status(500).json({ error: error.message });
        } else {
            res.status(200).json(result);
        }
    });
});

router.get('/get-migration-running-status', async (req, res, next) => {
    let headers = {
        "mt-auth-secret": config.get("multiTenantAuthSecret"),
    }
    const { redisKey } = req.query;
    commonFunctions.httpRequest('GET', config.get("authUrl") + `/utility/get-migration-running-status?redisKey=${redisKey}`, '', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.status(500).json({ error: error.message });
        } else {
            res.status(200).json(result);
        }
    });
});

router.get('/delete-corrupt-tenants', async (req, res, next) => {
    let headers = {
        "mt-auth-secret": config.get("multiTenantAuthSecret"),
    }
    commonFunctions.httpRequest('GET', config.get("authUrl") + `/utility/delete-corrupt-tenants`, '', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found while deleting Corrupt DB: ", error);
            res.status(500).json({ error: error.message });
        } else {
            res.status(200).json(result);
        }
    });
});

const dropIndex = (indexName, tenantId) => {
    let headers = {
        "Content-Type": "application/json",
        "tenant-id": tenantId,
        "index-service-secret": config.get("indexService.sharedSecret"),
        "timeout": 60000
    }
    return new Promise((resolve, reject) => {
        commonFunctions.httpRequest('POST', config.get("indexService.url") + `/index-service/open-search/delete-index/${indexName}`,'','', headers, function (error, result) {
            if (error) {
                commonFunctions.errorlogger.error("Error while syncing crawled data: ", error);
                reject(error);
            } else {
                resolve(result);
            }
        });
    });
};

const syncCrawledData = async (req, res) => {
    const { csId } = req.body;
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("suCrawler.secret"),
        "tenant-id": req.headers['tenant-id'] 
    }

    commonFunctions.httpRequest('POST', config.get("suCrawler.suCrawlerUrl") + `/crawler-config/sync-crawled-data?csId=${csId}`,'','', headers, function (error, result) {
            if (error) {
                commonFunctions.errorlogger.error("Error while syncing crawled data: ", error);
                return res.status(500).json({ error: error.message });
            } else {
                return res.send(result);
            }
        }
    );
};

router.post('/sync-crawled-data',  async (req, res, next) => {
    if (req.body.appId){
        delete req.body.appId;
    }
    const { csId, deleteIndex } = req.body;
    const tenantId = req.headers['tenant-id'];

    if(deleteIndex) {
        const query = 'SELECT elasticIndexName FROM content_sources where id = ?';
        connection[tenantId].execute.query(query, [csId], async (error, doc) => {
            if(!error){
                const [contentSourceDetails] = doc;
                const response = await dropIndex(contentSourceDetails.elasticIndexName, req.headers['tenant-id']);

                if(response.status) {
                    const { csTypeId, csId } = req.body;
                    const result = await getColumnsFromTables(tenantId);
     
                    await getContentSourceData(result.data.tableColMapping, result.data.tenantInfo, csTypeId, csId);

                    return syncCrawledData(req, res);
                }
                return res.status(500).json({ error: 'Failed to Drop Index' });
            }
            else {
                return res.status(500).json({ error: error.message });
            };
        })
    }
    else {
        return syncCrawledData(req, res);
    };
});

module.exports = {
    router: router,
}

