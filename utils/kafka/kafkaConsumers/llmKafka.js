const kafkaLib = require("../kafka-lib");
const commonFunctions = require("../../commonFunctions");
const { tenantGlobals } = require("../kafkaPublishers/tenantGlobals");
const emailTemplates = require("../../../routes/emailTemplates");
const {informAdmins} = require("../../../routes/admin/mlService");
const searchunifyEmail = require("../../../Lib/email");
const { getUserInfo } = require("auth-middleware");


const subscribeConsumer = async () => {
  try{
      const tenantConsumer = await kafkaLib.getConsumer({
        groupId: `admin ${config.get("kafkaTopic.adminAction")}`,
        topic: config.get("kafkaTopic.adminAction"),
        fromBeginning: false
    })
  await tenantConsumer.run({
      eachMessage: async ({
        topic, parition, message, heartBeat
      }) => {
        try {
          let expireToggle = false;
          //Collecting the data coming up from the kafka
          const data = JSON.parse(message.value);
          const integrationsArray = data.globals.gpt.integrations;

          const integrations = integrationsArray.map((integration) => {
            if (integration.name === "claude") {
               integration.active = !(integration.trial_expired);
               expireToggle = integration.trial_expired;
            }
            return integration;
          });

          //Code which directs to switch off the toggle for claude, based upon action received
          if(data.action === "SuGptToggleOff" && expireToggle){
            const gpt = {integrations};
            gpt.active = !!integrations.find((v) => v.active);

            //updating the tenant_globals table and publishing the kafka
            const sql = "UPDATE tenant_globals set gpt=?";
            connection[data.tenantId].execute.query(
                sql,
                [JSON.stringify(gpt)],
                (err) => {
                    if (err) {
                        commonFunctions.errorlogger.error(
                            err || "no data found in tenant_globals"
                        );
                    }
                    tenantGlobals(data.tenantId, { gpt });
                }
            );

            // Now disabling the activation of claude for the search clients
            // const sqlSelect = "SELECT gpt,id FROM search_clients";
            // connection[data.tenantId].execute.query(sqlSelect, [], (err, rows) => {
            //     if (err || !rows || !rows.length) {
            //         commonFunctions.errorlogger.error(
            //             err || "no data found in search_clients"
            //         );
            //     }

            //     rows.map((row) => {
            //       const gptData = JSON.parse(row.gpt);
            //       if (gptData.name === "claude") {
            //           gptData.active = false;
            //           const sqlUpdate = "UPDATE search_clients set gpt=? WHERE id=?";
            //           connection[data.tenantId].execute.query(
            //               sqlUpdate,
            //               [JSON.stringify(gptData),row.id],
            //               (err) => {
            //                   if (err) {
            //                       commonFunctions.errorlogger.error(
            //                           err || "no data found in search_clients table"
            //                       );
            //                   }
            //               }
            //           );
            //       }
            //     });
            // });
          } 

          informAdmins(data.tenantId,data.action);
                    
          } catch (e) {
            commonFunctions.errorlogger.error(e);
          }
        }
    });
  }catch(error){
    if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
      kafkaLib.createTopic(config.get("kafkaTopic.adminAction"));
    }
  }
}

module.exports = {
    subscribeConsumer
}