#!/bin/bash

months=("jan" "feb" "mar" "apr" "may" "jun" "jul" "aug" "sep" "oct" "nov" "dec")
start_year='25'
selected=0
current_year=$(date +'%y')
current_month=$(date +'%h' | awk '{print tolower(substr($0, 1, 3))}')

# Find current month index
current_month_idx=-1
for i in "${!months[@]}"; do
    if [[ "${months[$i]}" == "$current_month" ]]; then
        current_month_idx=$i
        break
    fi
done

# Calculate next month and year
next_month_idx=$((current_month_idx + 1))
next_year=$current_year
if [ $next_month_idx -ge 12 ]; then
    next_month_idx=0
    next_year=$((current_year + 1))
fi
next_month="${months[$next_month_idx]}"

options=()

for ((year=start_year; year<=next_year; year++)); do
    if [ "$year" -eq "$start_year" ]; then
        start_month=2
    else
        start_month=0
    fi

    # Determine end month for this year
    if [ "$year" -eq "$next_year" ]; then
        end_month=$((next_month_idx + 1))
    else
        end_month=12
    fi

    for ((idx=start_month; idx<end_month; idx++)); do
        month="${months[$idx]}"
        options+=("$year-$month-dev")
        options+=("$year-$month-qa")
        options+=("$year-$month")
        if [ "$year-$month" == "24-feb" ]; then
            options+=("24-feb-opensearch")
        fi
    done
done


print_options() {
    clear
    local idx=0
    echo -e "WARNING !! This script will reset your branch to remote, please make sure you've saved local changes !!"
    echo -e "Current year is: $current_year\n"
    echo -e "Current month is: $current_month\n"
    echo -e "listing all branch options...\n";
    echo -e "Please select the branch to start to move forward process for commits\n"
    for option in "${options[@]}"; do
        if [[ $idx -eq $selected ]]; then
            printf "\e[7m%s\e[0m\n" "$option"
        else
            printf "%s\n" "$option"
        fi
        ((idx++))
    done
}

main() {
    local key
    while true; do
        print_options

        IFS=

        read -rsn1 key
        case "$key" in
            $'\e') # Escape sequence for arrow keys
                read -rsn2 key2
                case "$key2" in
                    $'[A') ((selected = (selected - 1 + ${#options[@]}) % ${#options[@]}));;
                    $'[B') ((selected = (selected + 1) % ${#options[@]}));;
                esac
            ;;
            $'')
                echo "enter press";
                break;;
        esac
    done
}

main
echo -e "\nYou selected ${options[$selected]}"
echo -e "\nStarting auto merge [ this process would exit on first failure /conflicts ]"


array_length="${#options[@]}";
current_branch=$(git rev-parse --abbrev-ref HEAD)
echo -e "\nResetting origin to origin/$current_branch for $current_branch"
git reset --hard origin/"$current_branch"
git pull

# Handle the new branching strategy: dev -> qa -> base
selected_branch="${options[$selected]}"

if [[ "$selected_branch" == *"-dev" ]]; then
    # Starting from dev branch - merge dev -> qa -> base
    base_name="${selected_branch%-dev}"
    qa_branch="${base_name}-qa"
    base_branch="$base_name"

    echo -e "\nDetected dev branch. Merging: $selected_branch -> $qa_branch -> $base_branch"

    # Merge dev to qa
    echo -e "\nMerging $selected_branch into $qa_branch"
    git checkout "$qa_branch"
    git pull
    git merge --no-ff --no-edit "origin/$selected_branch"
    if [ $? -ne 0 ]; then
        echo -e "Merge conflict detected while merging $selected_branch into $qa_branch. Exiting...\n"
        exit 1
    else
        git push
    fi

    # Merge qa to base
    echo -e "\nMerging $qa_branch into $base_branch"
    git checkout "$base_branch"
    git pull
    git merge --no-ff --no-edit "origin/$qa_branch"
    if [ $? -ne 0 ]; then
        echo -e "Merge conflict detected while merging $qa_branch into $base_branch. Exiting...\n"
        exit 1
    else
        git push
    fi

elif [[ "$selected_branch" == *"-qa" ]]; then
    # Starting from qa branch - merge qa -> base
    base_name="${selected_branch%-qa}"
    base_branch="$base_name"

    echo -e "\nDetected qa branch. Merging: $selected_branch -> $base_branch"

    # Merge qa to base
    echo -e "\nMerging $selected_branch into $base_branch"
    git checkout "$base_branch"
    git pull
    git merge --no-ff --no-edit "origin/$selected_branch"
    if [ $? -ne 0 ]; then
        echo -e "Merge conflict detected while merging $selected_branch into $base_branch. Exiting...\n"
        exit 1
    else
        git push
    fi

else
    # Base branch or special branch - continue with sequential merge logic
    for ((id=selected; id<array_length-1; id++)); do
        next=$((id+1))
        next_branch="${options[$next]}"

        # Skip if next branch doesn't follow the pattern (e.g., skip dev/qa branches when starting from base)
        if [[ "$selected_branch" != *"-dev" && "$selected_branch" != *"-qa" ]]; then
            if [[ "$next_branch" == *"-dev" || "$next_branch" == *"-qa" ]]; then
                continue
            fi
        fi

        echo "${options[$next]}"
        git checkout "${options[$next]}"
        git pull
        echo -e "\n merging ${options[$id]} into ${options[$next]}\n"
        git merge --no-ff --no-edit "origin/${options[$id]}"
        if [ $? -ne 0 ]; then
            echo -e "Merge conflict detected. Exiting...\n"
            echo -e "Please manually fix the conflicts and resume the script from the fixed branch"
            exit 1
        else
            git push
        fi
    done
fi
