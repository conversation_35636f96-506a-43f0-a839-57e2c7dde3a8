var request = require("request");
var commonFunctions = require('./../../utils/commonFunctions');
var appVariables = require('../../constants/appVariables');

let data = appVariables.vimeo.clientId + ':' + appVariables.vimeo.appSecret;
let buff = new Buffer(data);
let tokenbase64data = buff.toString('base64');


const getAccessToken = function (code, loginUrl, clientId, tokenUrl, redirect_Uri, callback) {
    
    var options = {
        method: 'POST',
        url: 'https://api.vimeo.com/oauth/access_token',
        headers:
        {
            'cache-control': 'no-cache',
            Accept: 'application/vnd.vimeo.*+json;version=3.4',
            'Content-Type': 'application/json',
            Authorization: 'basic ' + tokenbase64data
        },
        body:
        {
            grant_type: 'authorization_code',
            code: code,
            redirect_uri: redirect_Uri
        },
        json: true
    };

    request(options, function (error, response, body) {
        if (error) {
            callback(error, null);
        }
        else {
            if (response.statusCode == 200) {
                //body = JSON.parse(body);
                callback(null, body);
            }
            else {
                commonFunctions.errorlogger.error("####--Err in Oath--####");
                callback(response.statusMessage, null);
            }

        }
    });
}

module.exports = {
    getAccessToken: getAccessToken
}
