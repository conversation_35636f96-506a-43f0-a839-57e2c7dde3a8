const config = require("config");
const kafkaLib = require("../kafka-lib");
const commonFunctions = require("../../commonFunctions");
const { getTenantsRateLimitInfo } = require("auth-middleware");

const redisCacheModule = require('../../redis/redis-cache');
const redisClientProvider = require('../../redis-client-provider');

const publishAcknowledgement = async (status, processSettings) => {
  try {
    commonFunctions.errorlogger.info(">>>>publishAcknowledgement");
    /* status values - 2 for success and 5 for failure */
    const { processRequestId, tenantId } = processSettings;
    if (!processRequestId || !tenantId){
      commonFunctions.errorlogger.info("Missing processRequestId or tenantId");
      return;
    }
    const kafkaObject = {
      type: 'UPDATE',
      tenantId,
      processRequestId,
      setObj: { status },
    };

    await kafkaLib.publishMessage({
      topic: config.get("kafkaTopic.processRequest"),
      messages: [{ key: 'process_engine', value: JSON.stringify(kafkaObject) }],
    });
    commonFunctions.errorlogger.info("<<<<publishAcknowledgement", JSON.stringify(kafkaObject));
  } catch (error) {
    throw error;
  }
};
const safeJsonParse = (value) =>{
  let parsed;
  try{
    parsed = JSON.parse(value);
  }catch(err){
    parsed = {}
    commonFunctions.errorlogger.info("Error While parsing data",value);
  }
  return parsed;
};

const subscribeConsumer = async () => {
  try{
      const rateLimitCronConsumer = await kafkaLib.getConsumer({
        groupId: `admin ${config.get("kafkaTopic.manageRateLimts")}`,
        topic: config.get("kafkaTopic.manageRateLimts"),
        fromBeginning: false
    })
  await rateLimitCronConsumer.run({
      eachMessage: async ({
        topic, parition, message, heartBeat
      }) => {
         const settings = safeJsonParse(message.value);
        try {
          /* 
          1. Identify all tenants and reset their quota consumption in Redis. This action will prompt the middleware to retrieve the monthly quota consumption from analytics for subsequent requests.
          2. Update the Rate Limit Information in the singleton object to reflect allowed consumption for new tenants registered on Status Page. 
         */
        
          commonFunctions.errorlogger.info(">>>>Consumed Rate Limit Kafka", settings);
          const rateLimitInfo = await getTenantsRateLimitInfo().then((result) => {
            let tenantObj = {};
            for (i in result.data)
                if (Object.keys(result.data[i]).length)
                    tenantObj[i] = result.data[i];
            return tenantObj;
        }).catch(err=>{
            commonFunctions.errorlogger.error("Error While Fetching Tenants Information:",err)
        });
          const tenantIds = Object.keys(rateLimitInfo); 
          commonFunctions.errorlogger.info("tenantIds:",tenantIds);
          if(tenantIds.length>0){
            tenantIds.map(async (id)=>{
              const tenantUsedLimitsKey = `${id}_gpt_quota_remaining`;
              commonFunctions.errorlogger.info("Clearing Cache for ",tenantUsedLimitsKey);
              await redisCacheModule.deleteKey(redisClientProvider.getRedisClient(),tenantUsedLimitsKey);
            });
          }
          commonFunctions.errorlogger.error("Updating Allowed Consumption Object in Singleton Memory");
          await commonFunctions.fetchRateLimits();
          publishAcknowledgement(2, settings);
          commonFunctions.errorlogger.info("<<<<Consumed Rate Limit Kafka Completed");
          
        } catch (err) {
          publishAcknowledgement(5, settings);
          commonFunctions.errorlogger.info("Error Occured during kafka consumption for:",err);
        }
      }
    });
  }catch(error){
    if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
      kafkaLib.createTopic(config.get("kafkaTopic.manageRateLimts"));
    }
    commonFunctions.errorlogger.info("Error Occured while processing manage rate limits kafka",error);
  }
}

module.exports = {
    subscribeConsumer
}