let jwt = require("jsonwebtoken");
let fs = require("fs");
let crypto = require("crypto");
let config = require("config").get("jwt");
let tenantId = require("config").get("tenantId")
let path = require("path");
let commonFunctions = require('./../utils/commonFunctions');
const {fetchTenantInfoFromUid}=require('auth-middleware')
const base64url = require('base64url');

let baseDir = path.resolve(__dirname, "../");

let privateKey = fs.readFileSync(
  path.join(baseDir, config.keys.get("private")),
  "utf-8"
);

const publicKey = fs.readFileSync(
  path.join(baseDir, config.keys.get("public")),
  "utf-8"
);

function encryptData(payload) {
  let iv = crypto.randomBytes(16)
  let cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(config.encryptionKey), iv);
  encrypted = Buffer.concat([cipher.update(JSON.stringify(payload)), cipher.final()]);
  return iv.toString('hex') + ':' + encrypted.toString('hex');
}

function encode(password, cryptoStr) {
  try{
    let textBuffer = new Buffer(JSON.stringify(cryptoStr), 'utf-8');
    let iv = crypto.randomBytes(16);
    let cipher = crypto.createCipheriv('aes-256-cbc', password, iv);
    let encryptedBuffer = cipher.update(textBuffer);
    let encrypted = Buffer.concat([iv, encryptedBuffer, cipher.final()]).toString('base64');
    return encrypted;
  }
  catch {
    return {
      statusCode: 200,
      flag: 200,
      message: 'Token',
      hscToken: authToken
    };
  }
}

let createSearchJwt = async function (req, res, next) { 
  let accessToken = (typeof req.body.accessToken != 'undefined') ? req.body.accessToken : '';
  let d = new Date();
  let d2 = d.getTime() + require("config").get("hscTokenExpireTime") * 60000; //jwt is the time of expiry in minutes

  let payloadData = {
    "iat": d.getTime() / 1000,
    "exp": d2 / 1000,
    "email": req.body.email || '',
    "UserId": req.body.userId || '',
    "ProfileID": req.body.ProfileID || '',
    "UserType": req.body.UserType || '',
    "ContactId": req.body.ContactId || '',
    "AccountID": req.body.AccountID ||req.body.userId || '',
    "caseUid": req.body.caseUid || ''
  };

  let token = jwt.sign(payloadData, Buffer.from(accessToken + payloadData.UserId, 'base64'));

  let payloadToken = token.split('.')[1];
  let jwtToken = encode(accessToken, payloadData);
  let newToken = token.replace(payloadToken, jwtToken)
  let response = { flag: 200, message: newToken }
  return response;
};
exports.createSearchJwt = createSearchJwt;

let refreshJwtToken = async function (req, res, next) {
  let authToken = req.body.token;
  authToken = authToken.replace('bearer ', '')
  res.statusCode = 200;
  let payload = authToken.split('.')[1]
  const tenantInfo = await fetchTenantInfoFromUid(req.body.uid);
  req.body.accessToken = tenantInfo.accessToken;
  let UserInfo = await decode(tenantInfo.accessToken, payload)

  authToken = authToken.replace(payload, base64url(UserInfo))
  let id = JSON.parse(UserInfo).UserId
  try {
    jwt.verify(authToken, Buffer.from(tenantInfo.accessToken + id, 'base64'), async function (err, decoded) {
      res.header("Access-Control-Request-Headers", "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken");
      res.setHeader("Access-Control-Allow-Credentials", true);
      res.setHeader("Access-Control-Allow-Origin", req.headers.origin || ' ');
      res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken,Authorization")
      if (!err) {
        let response = {
          statusCode: 200,
          flag: 200,
          message: 'Token',
          hscToken: req.body.token
        };
        res.send((response));
        next();
      } else if (err.message == 'jwt expired') {
        req.body.email = JSON.parse(UserInfo).email || '';
        req.body.userId = JSON.parse(UserInfo).userId|| '';
        req.body.ProfileID = JSON.parse(UserInfo).ProfileID|| '';
        req.body.UserType = JSON.parse(UserInfo).UserType|| '';
        req.body.ContactId = JSON.parse(UserInfo).ContactId|| '';
        req.body.AccountID = JSON.parse(UserInfo).AccountID|| '';
        req.body.caseUid = JSON.parse(UserInfo).caseUid|| '';
        let finalAccessToken = await createSearchJwt(req, res, next);
        let response = {
          statusCode: 200,
          flag: 200,
          message: 'Token Renewed',
          hscToken: finalAccessToken.message
        };
        res.status = 200;
        res.send(response);
        next();
      } else {
        let response = { flag: 500, message: "Invalid Authentication" }
        res.send((response));
      }
    });
  } catch {
    let response = { flag: 500, message: "Invalid Authentication" }
    res.send((response));
  }
}

exports.refreshJwtToken = refreshJwtToken;

const decode = (password, cryptoStr) => {
  const buf = Buffer.from(cryptoStr, 'base64');
  const iv = buf.slice(0, 16);
  const crypt = buf.toString('base64', 16);

  const decipher = crypto.createDecipheriv('aes-256-cbc', password, iv);
  let dec = decipher.update(crypt, 'base64', 'utf-8');
  dec += decipher.final('utf-8');

  return dec;
};

exports.addonJwt = async (headers) => {
  let jwtToken = encryptData({
    'CSRF-Token': headers['csrf-token'],
    referer: headers.referer,
    Cookie: headers.cookie,
    instance: headers.instance,
    platform: 'admin',
    tenantId: headers['tenant-id']
  });
  let payloadData = {
    info: jwtToken,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 60 * 60 * 3
  };

  let token = jwt.sign(payloadData, privateKey, {
    algorithm: "RS256"
  });

  return token;
};

exports.verifyAddonJwt = async (authToken) => {
  const signatureOption = {
    algorithm: 'RS256'
  };
  await jwt.verify(authToken, publicKey, signatureOption);
  const buff = Buffer.from(authToken.split('.')[1], 'base64');
  const text = JSON.parse(buff.toString('utf-8'));
  const details = decrypt(text.info);
  return JSON.parse(details);
};

function decrypt(cryptoStr) {
    // API Gatway required to decript jwt: TODO (Remove Encrypted key and public key form sdk)
    let textParts = cryptoStr.split(':');
    let iv = Buffer.from(textParts.shift(), 'hex');
    let encryptedText = Buffer.from(textParts.join(':'), 'hex');
    let decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(config.encryptionKey), iv);
    decrypted = Buffer.concat([decipher.update(encryptedText), decipher.final()]);
    return decrypted.toString();
}

exports.createJwt = function (req, res, next) {
  var tenantInfo = encryptData({ tenantId: req.headers['tenant-id'], domain: config.domain });
    var payloadData = {
        info: tenantInfo,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 60 * 60 * 3
    };

    var token = jwt.sign(payloadData, privateKey, {
        algorithm: "RS256"
    });

    res.send({ statusCode: 200, data: { token: token } });
};
