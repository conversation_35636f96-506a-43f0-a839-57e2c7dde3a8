var path = require('path');
var jsonFile = require('jsonfile');
var fs = require('fs');
var copydir = require('copy-dir');
var async = require('async');
var express = require('express');
var router = express.Router();
var multipart = require('connect-multiparty');
var multipartMiddleware = multipart();
var commonFunctions = require('./../../utils/commonFunctions');


const createMetaFolder = function (contentSource, authorization, callback) {
  async.auto({
    check_Directory: function (cb) {
      var directory = "resources/on_premise/1_" + contentSource.id;
      fs.stat(directory, function (err, res) {
        if (err) {
          fs.mkdir(directory);
          cb(null, directory);
        } else {
          cb(null, directory)
        }
      });
    },
    copy_Directory: ['check_Directory', function (dir, cb) {
      copydir("resources/on_premise/jira", dir.check_Directory, function (err) {
        if (err) {
          commonFunctions.errorlogger.error(err);
          cb(err, []);
        } else {
          commonFunctions.errorlogger.warn("Directory Copied Successfully");
          cb(null, []);
        }
      });
    }],
    create_Metafile: ['copy_Directory', function (newDir, cb) {
      var metadata = {
        "projects": [], "objects": { "fields": [], "object": "issue" }, "authentication": {
          "label": contentSource.name ? contentSource.name : contentSource.label, 
          "indexName": "jira_on_premise",
          "url": contentSource.url,
          "elasticUrl": contentSource.searchunifyIndexUrl,
          "type": authorization.authorization_type,
          "username": authorization.username,
          "password": authorization.password,
          "access_token": authorization.accessToken,
          "refresh_token": authorization.refreshToken,
        }
      }
      fs.writeFile(newDir.check_Directory + "/download/metadata.json", JSON.stringify(metadata, null, 4), function (err) {
        if (err) {
          commonFunctions.errorlogger.error(err);
          cb(err, []);
        }
        else {
          commonFunctions.errorlogger.warn('Metadata creation complete');
          cb(null, []);
        }
      }
      );
    }]
  }, function (err, result) {
    callback(null, [])
  });
}

const spacesAndboards = function (content_source_id, ProjectsFetched,req, callback) {
  var contentSourceObjectArr = [];
  var contentObjectToInsert = {};
  for (var i = 0; i < ProjectsFetched.length; i++) {
    var contentObjectToInsert = {};
    contentObjectToInsert['content_source_id'] = content_source_id;
    contentObjectToInsert['spaceName'] = ProjectsFetched[i].spaceName.replace('\'', "").replace(',', "").replace("(", "").replace(")", "");
    contentObjectToInsert['spaceUrl'] = ProjectsFetched[i].spaceUrl;
    contentObjectToInsert['spaceId'] = ProjectsFetched[i].spaceId;
    contentObjectToInsert['isSelected'] = ProjectsFetched[i].isSelected;
    contentSourceObjectArr.push(contentObjectToInsert);
  }
  commonFunctions.insertSpacesBoards(content_source_id, contentSourceObjectArr,req, function () {
    commonFunctions.getPlacesBySortParam(content_source_id, "numeric",req, function (err, projects) {
      callback(null, projects);
    });
  });
}

router.post('/uploadfile', multipartMiddleware, function (req, res, next) {

  checkDirectory("resources/jiraonprem-Metadata/", function (error) {
    if (error) {
      res.send({ message: 'Error' });
    } else {
      fileUpload(req, function (err, data) {
        if (err)
          res.send({ message: 'Error' });
        else
          res.send({ message: 'saved', code: 200, contentSource: data, selectedIndex: 3 });
      })
    }
  });
});

function checkDirectory(directory, callback) {
  fs.stat(directory, function (err, stats) {
    if (err) {
      fs.mkdir(directory, callback);
    } else {
      callback(err)
    }
  });
}

function fileUpload(req, callback) {
  fs.writeFile(DIRNAME + "/resources/jiraonprem-Metadata/jiraOnPremMeta.json", fs.readFileSync(req.files.uploadFile.path), 'binary', function (err) {
    jsonFile.readFile(DIRNAME + "/resources/jiraonprem-Metadata/jiraOnPremMeta.json", function (err, jsonData) {
      if (err) throw err;
      var contentSource = {};
      var authorization = {};
      contentSource['url'] = jsonData.authentication.url;
      contentSource['label'] = jsonData.authentication.label;
      contentSource['searchunifyIndexUrl'] = jsonData.authentication.elasticUrl;
      //content_source_type_id for jira onprem
      contentSource['content_source_type_id'] = 27;
      authorization['instanceURL'] = jsonData.authentication.url;
      if (jsonData.authentication.authType == "Basic") {
        authorization['username'] = jsonData.authentication.username;
        authorization['password'] = jsonData.authentication.password;
      }
      else {
        authorization['accessToken'] = jsonData.authentication.access_token;
      }
      contentSource['id'] = req.body.content_source_id;
      async.auto({
        insert_content_source_id: function (cb) {
          commonFunctions.insertUpdateContentSource(contentSource, req, function (err, result) {
            if (err) throw err;
            else {
              cb(null, result);
            }
          });
        },
        insert_auth: ['insert_content_source_id', function (data, cb) {
          authorization["content_source_id"] = data.insert_content_source_id.id;
          commonFunctions.insertUpdateAuthorization(authorization, function (err, result) {
            if (err) throw err;
            else {
              cb(null, result);
            }
          });
        }],
        insert_spaces_boards: ['insert_auth', function (aboveResult, cb) {
          spacesAndboards(aboveResult.insert_content_source_id.id, jsonData.projects,req, function (err, result) {
            if (err) throw err;
            else {
              projects = result;
              cb(null, []);
            }
          });
        }],
        loadMapping: ['insert_spaces_boards', function (aboveResult, cb) {
          commonFunctions.metadata.loadMappingForDbAndElastic(contentSource['content_source_type_id'], (err, res) => {
            if(err){
              cb(err, null);
            } else {
              cb(null, res);
            }
          })
        }],
        insert_objects_fields: ['insert_spaces_boards', 'loadMapping', function (aboveResult, cb) {
          commonFunctions.getContentSourceObjectsAndFieldsById(contentSource.id,req, function (err, dataObjectFields) {
            if (err) {
              cb(err, [])
            }
            else {
              if (dataObjectFields.length > 0) {
                var id = dataObjectFields[0].id;
              }
              var jsonObject = {
                "id": id,
                "name": jsonData.objects.object,
                "label": jsonData.objects.object,
                "content_source_id": aboveResult.insert_content_source_id.id,
                "fields": jsonData.objects.fields
              }
              commonFunctions.insertObject(jsonObject, req,function (err, result) {
                if (err) throw err;
                else {
                  if(jsonObject.fields.length){
                    jsonObject.fields = aboveResult.loadMapping.fields;
                  }
                  commonFunctions.insertFields(aboveResult, contentSource['content_source_type_id'], jsonObject,req,function (errfields, datafields) {
                    if (errfields) throw errfields;
                    else {
                      cb(null, []);
                    }
                  })
                }
              });
            }
          })
        }]
      }, function (err, result) {
        if (err) throw new Error(err);
        else
        commonFunctions.errorlogger.info("Content Source added sucessfully");
        callback(null, result.insert_content_source_id);
      })
    });
  })
}

module.exports = {
  router: router,
  createMetaFolder: createMetaFolder
}
