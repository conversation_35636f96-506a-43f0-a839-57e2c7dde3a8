module.exports = {
  objects: [{
    name: 'blog',
    label: 'Blog',
    status: 1,
    boosting_factor: 1,
    base_url: '',
    fields: []
  },
  {
    name: 'event',
    label: 'Event',
    status: 1,
    boosting_factor: 1,
    base_url: '',
    fields: []
  },
  {
    name: 'page',
    label: 'Page',
    status: 1,
    boosting_factor: 1,
    base_url: '',
    fields: []
  }
  ],
  fields: [
    {
      name: 'id',
      label: 'Id',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'title',
      label: 'Title',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'content',
      label: 'Content',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'created_at',
      label: 'Created At',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'updated_at',
      label: 'Updated At',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    }
  ]
};