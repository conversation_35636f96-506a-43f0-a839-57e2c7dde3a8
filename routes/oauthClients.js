/**
 * All the routes of this file have been moved to multi-tenant-auth service
 */

var express = require('express');
var router = express.Router();
var async = require('async');
var request = require("request");
var url = require('url');
const commonFunctions = require('../utils/commonFunctions');

function generateRandom(a) {
    return a ? (a ^ ((Math.random() * 16) >> (a / 4))).toString(16) : ([1e10] + 1e10 + 1e9).replace(/[01]/g, generateRandom);
}

router.post('/saveOauthClients', function (req, res, next) {
    // generate random client id and client secret
    var validateHtmlrequest = commonFunctions.htmlvalidate(req.body);
    if(validateHtmlrequest == true){
    var client_id = "C" + generateRandom();
    var client_secret = "S" + generateRandom();

    var sql = 'INSERT INTO `oauth_clients`' +
        '(id ,`name`, `redirect_uri`,`client_id`,`client_secret`,`scope`, `grant_types`)' +
        ' VALUES (?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE id=values(id), name=values(name),redirect_uri=values(redirect_uri),client_id=values(client_id),client_secret=values(client_secret),scope=(scope),grant_types=(grant_types);'
    var q = connection[req.headers['tenant-id']].execute.query(sql, [req.body.id, req.body.name, req.body.redirect_uri, client_id, client_secret, req.body.scope, req.body.grant_types], function (error, result) {
        if (!error) {
            res.send(result);
        } else
        commonFunctions.errorlogger.error("error", error)
    });
    }else{
        res.send({
            flag: 403,
            message: "Only string values are allowed"
        });
    }
});

router.post('/deleteOauthClients', function (req, res, next) {
    var id = req.body.id;

    var sql = "DELETE FROM `oauth_clients` WHERE id = ?";
    var q = connection[req.headers['tenant-id']].execute.query(sql, [id], function (error, result) {
        commonFunctions.errorlogger.error(error);
        commonFunctions.errorlogger.info(result);
        if (!error) {
            res.send(result);
        } else res.sendStatus(403);
    });
});

router.get('/getOauthClients', function (req, res, next) {
    var sql = "Select * from `oauth_clients`";
    var q = connection[req.headers['tenant-id']].execute.query(sql, function (error, result) {
        if (!error) {
            res.send(result);
        } else res.sendStatus(403);
    });
});

router.get('/getOauthScopes', function (req, res, next) {
    var sql = "Select * from `oauth_scopes`";
    var q = connection[req.headers['tenant-id']].execute.query(sql, function (error, result) {
        if (!error) {
            res.send(result);
        } else commonFunctions.errorlogger.error(error);
    });
});

module.exports = router;
