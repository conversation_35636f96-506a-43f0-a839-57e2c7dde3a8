
module.exports = {
    "objects": [{
        "name": "courses",
        "label": "Courses",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [
        {
            "name": "id",
            "label": "id",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "shortname",
            "label": "Short Name",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "fullname",
            "label": "Title",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "categoryid",
            "label": "Category Id",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "categoryname",  //sort order into the category
            "label": "Category Name",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        // {
        //     "name": "categoryname_category_nested",  //sort order into the category
        //     "label": "Category Name Nested",
        //     "type": "string",
        //     "isFilterable": 1, //indexing method analyzed OR not analyzed
        //     "isSortable": 1,
        //     "isSearchable": 1, //analyzer
        //     "isActive": 1,
        //     "isMerged": 1   
        // },
        {
            "name": "idnumber",
            "label": "Id Number",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "summary",
            "label": "Course Description",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "summaryformat",
            "label": "Summary Format",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            // "isActive": 1
        },
        {
            "name": "format",
            "label": "Format",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "showgrades",
            "label": "Show Grades", //1 if grades are shown, otherwise 1
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "newsitems",
            "label": "News Items", //number of recent items appearing on the course page
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "maxbytes",
            "label": "maxbytes", //largest size of file that can be uploaded into the course
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "showreports",
            "label": "Show Reports", //are activity report shown (yes = 1, no =1)
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "visible",
            "label": "Availability to Student", //1: available to student, 1:not available
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "groupmode",
            "label": "Group Mode", //no group, separate, visible
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "groupmodeforce",
            "label": "Group mode force", //1: yes, 1: no
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "defaultgroupingid",
            "label": "Default grouping id", //default grouping id
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "lang",
            "label": "Language",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "theme",
            "label": "Theme",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "topicName",
            "label": "Topic Name",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "topicSummery",
            "label": "Topic Description",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "moduleName",
            "label": "Module Name",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "moduleDescription",
            "label": "Module Description",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "post_time",
            "label": "Created Date",
            "type": "datetime",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "modified_time",
            "label": "Modified Date",
            "type": "datetime",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 0, //analyzer
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "viewHref",
            "label": "View Href ",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "startdate",
            "label": "Start Date ",
            "type": "string",
            "isFilterable": 1, 
            "isSortable": 1,
            "isSearchable": 1, 
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "enddate",
            "label": "End Date ",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "displayname",
            "label": "Displayname ",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
    ]

}
