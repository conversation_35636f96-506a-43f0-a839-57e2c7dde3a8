<html>

<head>
    <meta name="viewport" content="width=device-width, user-scalable=no" />
    <link rel="stylesheet" href="loginAuthorize.css">
    <style>
    #OTP{
        display: none;
    }
   .otpcountdown{
        color: #58C0FE;
        font-size: 12px;
        font-weight: 500;
    }
    </style>
    <script>
        var resetTime;
        var timeLeft;
        var emailId;
        var passwordValue;
        var showTimer;
        var interval;
        var currentYear = new Date().getFullYear()
        document.getElementById("currentYear").textContent = currentYear;

        resetTime = 5 * 60 * 1000;
        timeLeft = resetTime;
        
        function login(email, password) {
                emailId = email;
                passwordValue = password;
                console.log("login method -----");
                // let userArr = JSON.parse(localStorage.getItem('accessibleUser') || "[]");
                let userArr = JSON.parse(localStorage.getItem('accessibleUser') || "[]");
                let user = userArr.find((o) => emailId == o.email);
                let token = user && user.access ? user.access : '';
                let allowFurther = false;
                var xhttp = new XMLHttpRequest();
                xhttp.onreadystatechange = function () {
                    if (this.readyState == 4 && this.status == 200) {
                        var result1 = JSON.parse(this.responseText)
                        if (result1.flag === 150 && result1.path != 'loggedIn') {
                            self.allowFurther = false;
                            document.getElementById("mainDiv").style.display = "none";
                            document.getElementById("OTP").style.display = "block";
                            startTimer();
                        }
                        else
                            window.location.href = getParameterByName("redirect");
                    }
                };
                var adminUrl = '<%=client_url%>/login';
                xhttp.open("POST", adminUrl, true);
                xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
                xhttp.send("email=" + encodeURIComponent(email) + "&password=" + encodeURIComponent(password) + "&token=" + encodeURIComponent(token) );

            }
        function getParameterByName(name, url) {
            if (!url) url = window.location.href;
            name = name.replace(/[\[\]]/g, "\\$&");
            var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, " "));
        }

        function validateUser() {
                var otpValue = document.getElementById("OPT").value;
                var xhttp2 = new XMLHttpRequest();
                xhttp2.onreadystatechange = function () {
                    if (this.readyState == 4 && this.status == 200) {
                        window.location.href = getParameterByName("redirect");
                    }
                };
                var adminUrl = '<%=client_url%>/validateUser';
                xhttp2.open("POST", adminUrl, true);
                xhttp2.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
                xhttp2.send("email=" + encodeURIComponent(emailId) + "&password=" + encodeURIComponent(passwordValue) + "&otp=" + otpValue);
            }
        
        function startTimer() {
                interval = setInterval(() => {
                    if (timeLeft > 0) {
                        let min = Math.floor(timeLeft / 60000);
                        let sec = ((timeLeft % 60000) / 1000).toFixed(0);
                        showTimer = min + ":" + (sec.length < 2 ? 0 : '') + sec;
                        if (showTimer > '0:01') {
                            document.getElementsByClassName("otpcountdown")[0].innerHTML = "Otp will expire in " + showTimer;
                        } else if (showTimer == '0:01') {
                            document.getElementsByClassName("otpcountdown")[0].innerHTML = "Otp expired";
                        }
                        timeLeft = timeLeft - 1000;
                    } else {
                        clearInterval(interval);
                    }
                }, 1000)
            }
    </script>
</head>

<body>
    <div class="splashScreen">
        <header class="searchunilogo searchunilogosm center-align">
            <img class="searchUnifyNewLogo" src="../Assets/whitelogo.png">
        </header>
        <div id="mainDiv" class="login-page login-pagesm center-align">
            <form role="form" method="post">
                <div class="form-content form-contentsm">
                    <div class="form-class">
                        <div >
                            <h3 style="text-align: center;"> Sign in to your account
                                <span class="headerSpan"> </span>
                            </h3>
                        </div>
                        <table class="table-responsive">
                            <tr>
                                <td>
                                    <label for="email"> Username or email </label>
                                </td>
                                <td class="password">
                                    <input type="text" name="email" required class="form-control" placeholder="Email" autocomplete="off" required>
                                </td>
                            </tr>
                            <tr>
                                <td class="password">
                                    <label for="password"> Password </label>
                                </td>
                                <td class="password">
                                    <input type="password" autocomplete="off" name="password" class="form-control" placeholder="Password" required>
                                </td>
                            </tr>
                        </table>
                        <div style="margin: 18px 0;">
                            <input type="button" onclick="login(email.value, password.value)" value="submit" class="btn rounded-btn" value="Log in">
                        </div>
                    </div>
                </div>
            </form>
            <div class="clear"></div>
        </div>
        <div id="OTP">
            <div class="login-page login-pagesm center-align">
                <div class="form-content form-contentsm">
                    <div class="form-class">
                        <table class="table-responsive">
                            <tr>
                                <td class="password">
                                    <label for="otp"> OTP </label>
                                </td>
                                <td class="password">
                                    <input id="OPT" type="text" autocomplete="off" name="opt" class="form-control"
                                        placeholder="0 0 0 0 0 0 0 0 0 0 0 0" required>
                                </td>
                                <td>
                                    <p class='otpcountdown'></p>
                                </td>
                            </tr>
                        </table>
                        <div style="margin: 18px 0;">
                            <button class="rounded-btn" onclick="validateUser()">Submit</button>
                        </div>
                    </div>
                </div>
                <div class="clear"></div>
            </div>
        </div>
        <footer class="footer footersm">
            <div class="footerAbove">
                <a href="https://www.searchunify.com/sitemap/" class="login-anchor" target="_blank">Sitemap . </a>
                <a href="https://www.searchunify.com/privacy-policy/" class="login-anchor" target="_blank">Privacy . </a>
                <a href="https://www.searchunify.com/terms-and-conditions/" class="login-anchor" target="_blank">Terms and Conditions</a>
            </div>
            <hr>
            <div class="footerBelow">
                © Copyright <span id="currentYear"></span> Grazitti Interactive. All rights reserved.
            </div>
        </footer>
    </div>
</body>

</html>