var express = require('express');
var async = require('async');
var router = express.Router();
var commonFunctions = require('../../utils/commonFunctions');

router.get("/get_advertisements", function (req, res, next) {
    get_advertisements(req.query,req, function (err, data) {
        if (err)
            res.send({ "status": "OK", "result": [] });
        else {
            res.send({ "status": "OK", "result": data });
        }
    });
});

function get_advertisements(data,req,callback) {
    async.auto({
        getId: cb => {
            connection[req.headers['tenant-id']].execute.query(`select * from search_clients where uid = ?`, [data.uid], (err, result) => {
                if (err) {
                    cb(err, []);
                }
                else {
                    cb(null, result);
                }
            });
        },
        advertisementsList: ["getId", (results, cb) => {
            if(results.getId.length){
                connection[req.headers['tenant-id']].execute.query(`select * from advertisement_templates WHERE search_client_id = ?`, [results.getId[0].id], (err, res) => {
                    if (err) {
                        cb(err, []);
                    }
                    else {
                        cb(null, res);
                    }
                });
            }
            else cb(null, []);
        }]
    }, (error, result) => {
        callback(null, result.advertisementsList);
    })
}

router.get("/delete_advertisements", function (req, res, next) {
    delete_advertisements(req.query, req, function (err, data) {
        if (err)
            res.send({ "result": [] });
        else {
            res.send({ "status": "oK", "result": data });
        }
    });

});
function delete_advertisements(data,req, callback) {

    async.auto({
        getId: cb => {
            const query = `SELECT * FROM search_clients WHERE uid = ?`;
            connection[req.headers['tenant-id']].execute.query(query, [data.uid], (err, result) => {
                if (err) {
                    cb(err, []);
                } else {
                    cb(null, result);
                }
            });
        },
        
        deleteAdvertisement: ["getId", (results, cb) => {
            const query = `DELETE FROM advertisement_templates WHERE search_client_id = ? AND phrase = ?`;
            const values = [results.getId[0].id, data["phrase"]]; // Access the id safely
            connection[req.headers['tenant-id']].execute.query(query, values, (err, res) => {
                if (err) {
                    cb(err, []);
                } else {
                    cb(null, res);
                }
            });
        }],
        deleteChildAdvertisements: ["getId", (results, cb) => {
            // First get child search clients
            const checkAbTestSql = `SELECT uid, id FROM search_clients WHERE ab_test_parent = ?`;
            
            connection[req.headers['tenant-id']].execute.query(
                checkAbTestSql,
                [data.uid],
                (abTestError, abTestResults) => {
                    if (abTestError) {
                        commonFunctions.errorlogger.error("Error while getting data from search_clients regarding AB Test.", abTestError);
                        cb(null, []);
                        return;
                    }

                    // If no child clients, just return
                    if (!abTestResults || abTestResults.length === 0) {
                        cb(null, []);
                        return;
                    }

                    // Delete advertisements for each child client
                    let completedDeletes = 0;
                    let deleteErrors = [];

                    abTestResults.forEach((child) => {
                        const deleteQuery = `DELETE FROM advertisement_templates WHERE search_client_id = ? AND phrase = ?`;
                        const deleteValues = [child.id, data["phrase"]];

                        connection[req.headers['tenant-id']].execute.query(
                            deleteQuery,
                            deleteValues,
                            (error, result) => {
                                if (error) {
                                    deleteErrors.push(error);
                                }
                                completedDeletes++;

                                // Check if all deletes are done
                                if (completedDeletes === abTestResults.length) {
                                    if (deleteErrors.length > 0) {
                                        commonFunctions.errorlogger.error("Errors deleting child advertisements", deleteErrors);
                                        cb(null, []); 
                                    } else {
                                        cb(null, { message: "Child deletes completed" });
                                    }
                                }
                            }
                        );
                    });
                }
            );
        }]
    }, (error, result) => {
        callback(null, result);
    });
}

router.post("/save_advertisements", function (req, res, next) {
    save_advertisements(req.body, req, function (err, data) {
        res.send({ "status": "ok" });
    });
});

function save_advertisements(body, req, callback) {
    async.auto({
        getId: cb => {
            var get_id = `SELECT * FROM search_clients WHERE uid = '${body.uid}'`;
            connection[req.headers['tenant-id']].execute.query(get_id, (error, result) => {
                if (error) {
                    cb(error, null);
                } else {
                    cb(null, { client_id: result[0].id, client_uid: result[0].uid });
                }
            });
        },
        UpdateHtmlPath: ["getId", (results, cb) => {
            if (results.getId.client_id) {
                var colums = [];
                var data = [];
                var body1 = [];
                delete body.appId;
                body1.push(body);

                for (var fi = 0; fi < body1.length; fi++) {
                    colums = []; // Initialize columns every time
                    const parameters = [];
                    for (var key in body1[fi]) {
                        if (body1[fi].hasOwnProperty(key)) {
                            if (key == "uid") {
                                colums.push("search_client_id");
                                parameters.push(results.getId.client_id);
                            } else {
                                colums.push(key);
                                parameters.push(body1[fi][key]);
                            }
                        }
                    }
                    data.push(parameters);
                }
                const sqlCS = "INSERT INTO `advertisement_templates`(" + colums +
                    ") VALUES " + data.map(x => { return "(" + x.map(y => { return "?" }) + ")" }).join(",") +
                    " ON DUPLICATE KEY UPDATE " + colums.map(x => {
                        return x + "=values(" + x + ")"
                    }).join(',');

                connection[req.headers['tenant-id']].execute.query(sqlCS, data.reduce((sum, x) => { return sum.concat(x); }, []), (error, response) => {
                    if (error) {
                        cb(error, null);
                    } else {
                        cb(null, { htmlPath: response.message });
                    }
                });
            } else {
                cb("Invalid client ID", null);
            }
        }],
        UpdateChildAdvertisements: ["UpdateHtmlPath", (results, cb) => {
            // Step 1: Fetch child search clients
            const checkAbTestSql = `
                SELECT uid, id 
                FROM search_clients 
                WHERE ab_test_parent = ?
            `;

            connection[req.headers['tenant-id']].execute.query(
                checkAbTestSql,
                [results.getId.client_uid], // Use the parent search client UID
                (abTestError, abTestResults) => {
                    if (abTestError) {
                        commonFunctions.errorlogger.error("Error while getting data from search_clients regarding AB Test.", abTestError);
                        cb(abTestError, null);
                        return;
                    }

                    // Step 2: If child search clients exist, update their advertisements
                    if (abTestResults && abTestResults.length > 0) {
                        let completedUpdates = 0;
                        let updateErrors = [];

                        abTestResults.forEach((child) => {
                            // Prepare the data for the child
                            const childData = { ...body }; // Copy the body
                            delete childData.search_client_id; // Remove if it exists
                            childData.uid = child.uid; // Set the child's uid
                        
                            const childColums = [];
                            const childParameters = [];
                            
                            for (var key in childData) {
                                if (childData.hasOwnProperty(key)) {
                                    if (key == "uid") {
                                        childColums.push("search_client_id");
                                        childParameters.push(child.id);
                                    } else {
                                        childColums.push(key);
                                        childParameters.push(childData[key]);
                                    }
                                }
                            }

                            const childSqlCS = "INSERT INTO `advertisement_templates`(" + childColums +
                            ") VALUES (" + childParameters.map(x => { return "?" }).join(",") + ")" + 
                            " ON DUPLICATE KEY UPDATE " + childColums.map(x => {
                                return x + "=values(" + x + ")"
                            }).join(',');

                            connection[req.headers['tenant-id']].execute.query(
                                childSqlCS,
                                childParameters,
                                (updateError, updateResult) => {
                                    if (updateError) {
                                        updateErrors.push(updateError);
                                    }
                                    completedUpdates++;

                                    // Check if all updates are done
                                    if (completedUpdates === abTestResults.length) {
                                        if (updateErrors.length > 0) {
                                            commonFunctions.errorlogger.error("Errors updating child advertisements:", updateErrors);
                                            cb(updateErrors, null);
                                        } else {
                                            commonFunctions.errorlogger.info("Successfully updated all child advertisements");
                                            cb(null, { message: "All updates completed successfully" });
                                        }
                                    }
                                }
                            );
                        });
                    } else {
                        // No child search clients found
                        cb(null, { message: "No child search clients to update" });
                    }
                }
            );
        }]
    }, (error, result) => {
        if (error) {
            commonFunctions.errorlogger.error("Error in save_advertisements:", error);
            callback(error, null);
        } else {
            callback(null, result);
        }
    });
}

module.exports = {
    router: router
}