var express = require('express');
var querystring = require('querystring');
var router = express.Router();
var async = require('async');
var request = require("request");
var fs = require('fs');
const { spawn, exec } = require('child_process');
var path = require('path');
var multipart = require('connect-multiparty');
var multipartMiddleware = multipart();
var commonFunctions = require('./../../utils/commonFunctions');
var crawlerConfigCache = require('../../utils/crawler-config-cache');
const config = require('config');


router.get('/fetchProcessRequestStatic', function (req, res) {
    if (req.body.appId) {
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "secretKey": config.get("cronManager.secret"),
    }
    commonFunctions.httpRequest('GET', config.get("cronManager.cronMangerUrl") + '/process-request/process-request-stats', '', {}, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({error: true, msg: error});
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    });
});

router.post('/addProcessConfig', async(req,res,next)=>{
    if (req.body.appId) {
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "secretKey": config.get("cronManager.secret"),
    }
    
    commonFunctions.httpRequest('POST', config.get("cronManager.cronMangerUrl") + `/process-request-config`,'', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.send(result);
        }
    });
});

router.get('/fetchProcessRequestConfig', function (req, res) {
    if (req.body.appId) {
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "secretKey": config.get("cronManager.secret"),
    }
    commonFunctions.httpRequest('GET', config.get("cronManager.cronMangerUrl") + '/process-request-config', '', {}, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({error: true, msg: error});
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    });
});

router.delete('/deleteProcessRequestConfig',  async (req, res, next) => {
    if (req.body.appId) {
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "secretKey": config.get("cronManager.secret"),
    }
    
    commonFunctions.httpRequest('DELETE', config.get("cronManager.cronMangerUrl") + `/process-request-config?_id=${req.query.id}&service=${req.query.service}`,'', '', headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.send(result);
        }
    });
});

router.delete('/deleteProcessRequest',  async (req, res, next) => {
    if (req.body.appId) {
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "secretKey": config.get("cronManager.secret"),
    }

    const deletePrBody = {
        _id: [req.query.id]
    }
    
    commonFunctions.httpRequest('DELETE', config.get("cronManager.cronMangerUrl") + `/process-request`,'', deletePrBody, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.send(result);
        }
    });
});


router.get('/fetchProcessRequest', function (req, res) {
    if (req.body.appId) {
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "secretKey": config.get("cronManager.secret"),
    }
   const queryString = querystring.stringify(req.query);
   
    commonFunctions.httpRequest('GET', config.get("cronManager.cronMangerUrl") + `/process-request?${queryString}`, '', {}, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({error: true, msg: error});
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    });
});

router.delete('/deleteAllFilteredProcessRequests', async (req, res, next) => {
    let headers = {
      "Content-Type": "application/json",
      "secretKey": config.get("cronManager.secret"),
    };
    const queryString = querystring.stringify(req.query);
    commonFunctions.httpRequest('DELETE', config.get("cronManager.cronMangerUrl") + `/process-request/delete-all?${queryString}`, '', {}, headers, function (error, result) {
      if (error) {
        commonFunctions.errorlogger.error("Error found: ", error);
        res.send(error);
      } else {
        res.send(result);
      }
    });
  });
  
module.exports = {
    router: router
}