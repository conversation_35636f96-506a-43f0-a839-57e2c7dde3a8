// contains producer as well as consumer
const { Kafka, logLevel, CompressionTypes } = require('kafkajs');
const consumers = require('./kafkaConsumers/index');
const config = require("config");
let kafka;
let producer;
let kafkaAdmin;

exports.KAFKA_EVENT_TYPES = {
  add    : "ADD",
  delete : "DELETE",
  update : "UPDATE",
  reload : "RELOAD",
  reset  : "RESET",
  updateMergeResult:'updateMergeResult',
  updateAuthStatus: 'updateAuthStatus',
  removeCSField: 'REMOVE_CS_FIELD'
}

exports.SU_CRAWLER_TOPIC = {
  contentSource : config.get("kafkaTopic.suContentSource"),
  contentSourceObjects: config.get("kafkaTopic.contentSourceObjects"),
  contentSourceObjectFields: config.get("kafkaTopic.contentSourceObjectFields"),
  contentSourceSpaces: config.get("kafkaTopic.contentSourceSpaces"),
  crawlerConfig: config.get("kafkaTopic.crawlerConfig"),
  emailSubscriptionPreferences : config.get("kafkaTopic.emailSubscriptionPreferences"),
  versionUpdateMapping: config.get("kafkaTopic.versionUpdateMapping")
}

const initializekafka = ({ clientId, brokers, retryConfig }) => {
  kafka = new Kafka({
    clientId,
    brokers,
    logLevel: logLevel.ERROR,
    retry: {
      maxRetryTime: retryConfig.maxRetryTime, // default 30000
      initialRetryTime: retryConfig.initialRetryTime, // default 300
      retries: retryConfig.retries, // default is 5
      restartOnFailure: async () => true // this is default
    }
  });

  producer = kafka.producer();
  kafkaAdmin = kafka.admin();
  consumers.subscribeConsumers();
};

const publishMessage = async ({ topic, messages }) => {
  console.log("Publishing message on topic",topic)
  const response = { success: false };
  if(!producer){
    await initializekafka({
      clientId: 'admin',
      brokers: [config.get('kafkaTopic.host')],
      retryConfig: {
        maxRetryTime: config.get('kafkaTopic.maxRetryTime'), // default 30000
        initialRetryTime: config.get('kafkaTopic.initialRetryTime'), // default 300
        retries: config.get('kafkaTopic.retries') // default is 5
      }
    });
  }
  try {
    await producer.connect();
    const publishResult = await producer.send({
      topic,
      messages,
      compression: CompressionTypes.GZIP
    });
    response.success = true;
    response.data = { result: publishResult };
  } catch (e) {
    console.log(e);
    response.error = e;
  }

  return response;
};

const getConsumer = async ({ groupId, topic, otherproperties }) => {
  try {
    const consumer = kafka.consumer({ groupId });
    await consumer.connect();
    await consumer.subscribe({ topic, ...otherproperties });

    return consumer;
  } catch (e) {
    console.log(`consumer error e : ${JSON.stringify(e)}`);
    throw new Error(e.type);
  }
};

const createTopic = async (topic) => {
  try{
    await kafkaAdmin.connect();
    await kafkaAdmin.createTopics({
      topics: [{ topic }],
      waitForLeaders: true,
    }) 
  }catch(e){
    console.log(e)
  }
}

exports.initializekafka = initializekafka;
exports.publishMessage = publishMessage;
exports.getConsumer = getConsumer;
exports.createTopic = createTopic;