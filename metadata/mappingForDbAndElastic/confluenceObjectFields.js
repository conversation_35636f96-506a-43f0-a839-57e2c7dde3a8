

module.exports = {
  "objects": [{
    "name": "page",
    "label": "Page",
    "status": 1,
    "boosting_factor": 1,
    "base_url": "",
    "fields": [{
      "name": "id",
      "label": "id",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "placeName",
      "label": "Space Name",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "placeId",
      "label": "Space Id",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "placeUrl",
      "label": "place Url",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "type",
      "label": "type",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "title",
      "label": "Title",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "Url",
      "label": "View Href",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "authorName",
      "label": "Author Name",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "authorUrl",
      "label": "Author Url",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "post_time",
      "label": "Created Date",
      "type": "datetime",
      "isFilterable": 1,
      "isMerged": 1,
      "isSortable": 1,
      "isSearchable": 0,
      "isActive": 1
    },
    {
      "name": "status",
      "label": "Status",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "permissions_nestedpermissions",
      "label": "Permissions",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1,
      "isMerged": 1
    },
    {
      "name": "description",
      "label": "Description",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "comments",
      "label": "Comments",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "commentCount",
      "label": "comment Count",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    }, {
      "name": "updatedAt",
      "label": "updated at",
      "type": "datetime",
      "isMerged": 1,
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "updatedBy",
      "label": "updated by",
      "type": "string",
      "isMerged": 1,
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    }]

  },
  {
    "name": "blogpost",
    "label": "Post",
    "status": 1,
    "boosting_factor": 1,
    "base_url": "",
    "fields": [{
      "name": "id",
      "label": "id",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "placeName",
      "label": "Space Name",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "placeId",
      "label": "Space Id",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "placeUrl",
      "label": "place Url",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "type",
      "label": "type",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "title",
      "label": "Title",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "Url",
      "label": "View Href",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "authorName",
      "label": "Author Name",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "authorUrl",
      "label": "Author Url",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "post_time",
      "label": "Created Date",
      "type": "datetime",
      "isFilterable": 1,
      "isMerged": 1,
      "isSortable": 1,
      "isSearchable": 0,
      "isActive": 1
    },
    {
      "name": "status",
      "label": "Status",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "permissions",
      "label": "Permissions",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1,
      "isMerged": 1
    },
    {
      "name": "description",
      "label": "Description",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "comments",
      "label": "Comments",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "commentCount",
      "label": "comment Count",
      "type": "string",
      "isFilterable": 1,
      "isSortable": 0,
      "isSearchable": 1,
      "isActive": 1
    }, {
      "name": "updatedAt",
      "label": "updated at",
      "type": "datetime",
      "isMerged": 1,
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    },
    {
      "name": "updatedBy",
      "label": "updated by",
      "type": "string",
      "isMerged": 1,
      "isFilterable": 1,
      "isSortable": 1,
      "isSearchable": 1,
      "isActive": 1
    }]

  }],
  
}
