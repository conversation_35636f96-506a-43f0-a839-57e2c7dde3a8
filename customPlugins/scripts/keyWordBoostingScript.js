var path = require('path');
environment = require(path.join(__dirname, '../../routes/environment'));
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../config');
process.env.NODE_CONFIG_DIR = configPath;
console.log(environment.configuration);
config = require('config');
const createRequest = require('../../auth/src/client');
const authUrl = config.get('authUrl');
var mysql = require('mysql');
const kafkaLib = require("../../utils/kafka/kafka-lib");

const keywordBoostingInsertOldData = async (req, cb) => {
    let oldData = [];
    let tenantListData = [];
    tenantListData = req.data;
    let tenantObjectList = [];
    for (let i = 0; i < tenantListData.length; i++ ) {
        tenantObjectList.push({tenantId: tenantListData[i].tenant_id, databaseName: tenantListData[i].database_name})
    }
    console.log(tenantObjectList);
    for (let i = 0; i < tenantObjectList.length; i++ ) {
        const connection_new= await mysql.createConnection({
            host: config.get("databaseSettings.host"),
            user: config.get("databaseSettings.user"),
            password: config.get("databaseSettings.password"),
            database: `${tenantObjectList[i].databaseName}`,
            port: config.get("databaseSettings.mysqlPORT")
        });
        console.log('-----------------------------------------------');
        let hasData = true;
        let offest = 0;
        let limit = 100;
        while(hasData){
            oldData = await fetchKBData(connection_new, offest, limit);
            offest = offest + limit;
            if(oldData && oldData.length){
                console.log("Length",oldData.length );
                await parseData(oldData, tenantObjectList[i].tenantId, tenantObjectList[i].databaseName);
            }
            else{
                console.log('For database : ============= '+tenantObjectList[i].databaseName);
                console.log('For tenantId : ============= '+tenantObjectList[i].tenantId);
                console.log("no more data");
                hasData = false;
                await connection_new.end();
            }
            
        }
    }
}

const fetchTenatDetailsFromAuth = async (req, cb) => {
    try {
        const options = {
        method: 'GET',
        url: `${authUrl}/tenant/getAllTenants`
    };
        const response = await createRequest(options);
        // console.log(response);
        return response;
    } catch (error) {
        console.log(error);
        return error;
    }
}

const start = async (req, cb) => {
    //create connectionn to suauth database
    let tenantListData = await fetchTenatDetailsFromAuth();
    await keywordBoostingInsertOldData(tenantListData);
}

function kafkaKeyWordBoosting(data){
    try{
        let key = data && data[0] && data[0].tenantId;
        if(!key){
            console.log(
                "Warning: UID is not available in the data object. The message will be sent without a specific key, Key Word Boosting Data sent to kafka"
            );
        }
        kafkaLib.publishMessage({
            topic: config.get("kafkaTopic.keywordBoostingTopic"),
            messages:[{
                value:JSON.stringify(data),
                key: key
            }]
        })
        console.log('Key Word Boosting Data sent to kafka');
        // console.log(data);
    }catch(err){
      console.log('Error while sending Key Word Boosting Data to Kafka=> ', err);
    }
}

function fetchKBData(connection_new, offset, limit) {
    return new Promise((resolve, reject) => {
        connection_new.query(`SELECT
        kb.search_string,
        kb.index_name,
        kb.index_type,
        kb.record_id,
        sc.uid,
        kb.title,
        kb.href
    FROM
        keyword_boost kb
    join search_clients sc on
        kb.search_client_id = sc.id
    LIMIT ${limit} OFFSET ${offset}`, (err, docs) => {
            if (!err) {
                let oldData = docs;
                resolve(oldData);
            } else {
                // console.log(err);
                // reject(err);
                resolve();
            }
        });
     });
}

async function parseData(oldData, tenant_id, databaseName){
    console.log('For database : ============= '+databaseName)
    console.log('For tenantId : ============= '+tenant_id)
        if (oldData !== undefined) {
            let data = oldData.map((data) => {
                return {
                  event: 'new',
                  searchString: data.search_string,
                  indexName: data.index_name,
                  indexType: data.index_type,
                  recordId: data.record_id,
                  uid: data.uid,
                  title: data.title,
                  url: data.href,
                  tenantId: tenant_id
                };
              });
            await kafkaKeyWordBoosting(data);
        }
}

start();
