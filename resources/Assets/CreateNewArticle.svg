<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" viewBox="0 0 24 24">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#55c7ff"/>
      <stop offset="1" stop-color="#7886f7"/>
    </linearGradient>
    <clipPath id="clip-Create_New_Article">
      <rect width="24" height="24"/>
    </clipPath>
  </defs>
  <g id="Create_New_Article" data-name="Create New Article" clip-path="url(#clip-Create_New_Article)">
    <g id="outline-description-24px_5_" data-name="outline-description-24px (5)">
      <path id="Path_1160" data-name="Path 1160" d="M0,0H24V24H0Z" fill="none"/>
      <path id="Path_1161" data-name="Path 1161" d="M8,16h8v2H8Zm0-4h8v2H8ZM14,2H6A2.006,2.006,0,0,0,4,4V20a2,2,0,0,0,1.99,2H18a2.006,2.006,0,0,0,2-2V8Zm4,18H6V4h7V9h5Z" fill="url(#linear-gradient)"/>
    </g>
  </g>
</svg>
