/*
    This program assumes the new index mapping is already created

*/
var elastic = require('elasticsearch');
var fs = require('fs');

var client;

function getMoreUntilDone(error, response, old_index, old_type, d, counter, index, type) {
    // 
    console.log('batch '+ counter+' data received...');
    if(!error && typeof response.hits!= 'undefined' && 
        response.hits.hits != 'undefined' && response.hits.hits.length > 0){
        try{
            console.log('writing batch '+counter+' data to index '+index);
            try{
                var json = response.hits;

                console.log('iterating over list of data. '+ json.hits.length);

                // bulk query code
                var bulkqueries = [];
                for(j=0;j<json.hits.length;j++){
                    var item = json.hits[j];
                    var id = item._id;
                    var source = item._source;
                    var idx = index;        // new index
                    var t = item._type;        // new type

                    bulkqueries.push({"index":{ "_index": idx, "_type": t, "_id": id }});
                    bulkqueries.push(source);
                }

                client.bulk({
                    body: bulkqueries
                }, function(err, res){
                    if(err)
                        console.log('Error while executing bulk queries: '+JSON.stringify(err));
                    else
                        console.log('after executing bulk queries: success');

                    console.log('batch '+counter+' data hase been written to index '+index);
                    counter++;
                    console.log('requesting for next batch of data');
                    client.scroll({
                        scrollId: response._scroll_id,
                        scroll: '30s'
                    }, function(err, data){
                        return getMoreUntilDone(err, data, old_index, old_type, d, counter, index, type);
                    });
                });
                
            }catch(e){
                console.log(e);
            }
               
        }catch(e){
            console.log('error while trying to write file.');
        }
    }else{
        console.log('response end for request no. '+counter);
    }
}

function reIndex(elasticClient, srcIndex, destIndex, callback){
    try{
        console.log('Update mapping process start. ---->');
        if(typeof client == 'undefined'){
            console.log('initialising client');
            client = elasticClient
        }
        var index = destIndex;
        var type = '';
        var old_index = srcIndex;
        var old_type = '';

        if(index!=null && typeof index != 'undefined'){
            var d = new Date();
            var counter = 1;
            console.log('Retrieving data for backup...');
            client.search({
                index: old_index,
                from: 0,
                size: 600,
                scroll: '30s'
            }, function(err, data){
                return getMoreUntilDone(err, data, old_index, old_type, d, counter, index, type);
            });

        }else{
            res.send('invalid');
        }
    }catch(e){
        console.log('Error while parsing file data Please check weather the data is in correct format: '+ e);
    }
}

exports.reIndex = reIndex;