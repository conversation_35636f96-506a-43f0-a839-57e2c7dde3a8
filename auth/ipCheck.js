const url = require("url");
var router = require('express').Router();
var ipRangeCheck = require("ip-range-check");
const fetch = require('node-fetch');
const urlAuth  = config.get('authUrl');
var commonFunctions = require('../utils/commonFunctions');


var apiGroupArray = {};

function loadApiGroups(){
    // repeating itself in 5 minutes to reload the permissions
    fetch(`${urlAuth}/admin/getApiGroups`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
    }).then((data) =>  data.json()).then((data) => {
        let apiGroupsNew = {};
        data.map(x => apiGroupsNew[x.url] = x.permission_id);
        apiGroupArray = apiGroupsNew;
        commonFunctions.errorlogger.warn('Permissions loaded');
    }).catch((e) => {
        commonFunctions.errorlogger.error(e);
    });
}
loadApiGroups();
setInterval(loadApiGroups, 1000*60*5);

router.use((req, res, next) => {
    let ip;
    let checkRange = false;

    if (!ip || ip == 1) try { ip = req.headers['x-client-ip']; } catch (e) { }
    if (!ip || ip == 1) try { ip = req.headers['x-forwarded-for']; } catch (e) { }
    if (!ip || ip == 1) try { ip = req.headers['cf-connecting-ip']; } catch (e) { }
    if (!ip || ip == 1) try { ip = req.headers['true-client-ip']; } catch (e) { }
    if (!ip || ip == 1) try { ip = req.headers['x-real-ip']; } catch (e) { }
    if (!ip || ip == 1) try { ip = req.headers['x-forwarded']; } catch (e) { }
    if (!ip || ip == 1) try { ip = req.headers['forwarded-for']; } catch (e) { }
    if (!ip || ip == 1) try { ip = req.headers.forwarded; } catch (e) { }
    if (!ip || ip == 1) try { ip = req.connection.remoteAddress; } catch (e) { }
    if (!ip || ip == 1) try { ip = req.connection.socket.remoteAddress; } catch (e) { }
    if (!ip || ip == 1) try { ip = req.socket.remoteAddress; } catch (e) { }
    if (!ip || ip == 1) try { ip = req.info.remoteAddress; } catch (e) { }

    if (ip && ip.indexOf(":") > -1) {
        ip = ip.split(":");
        ip = ip[ip.length - 1];
    }

    if(apiGroupArray[req.path]){
        var permissionId = apiGroupArray[req.path];
        try{
            if(permissionId && permissionId!=''){
                connection[req.headers['tenant-id']].execute.query("select * from security_ip_groups where ip = ?", [ip], (err, result)=>{
                    if(err){
                        next();
                    }
                    else{
                        if(result.length>0){
                            var permissionArray = result.map(x=>{return x.permission_id});
                            if(permissionArray.includes(permissionId)){
                                next();
                            }
                            else {
                                commonFunctions.errorlogger.error('2Un Authorised ip');
                                res.sendStatus(403);
                            }
                        }else{
                            next();
                        }
                    }
                });
            }else{
                commonFunctions.errorlogger.info('no rule set for the path ',req.path);
                next();
            }
        }
        catch(e){
            commonFunctions.errorlogger.error(e);
            next();            
        }
    }
    else{
        next();
    }

});

module.exports = router;
