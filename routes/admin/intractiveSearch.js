var express = require('express');
var router = express.Router();
var request = require('request');
var async = require('async');
const { Client } = require('@elastic/elasticsearch')
var commonFunctions = require('./../../utils/commonFunctions');
var searchQuery = require('./../../metadata/mappingForDbAndElastic');
const interactiveSearchIndexName = 'admin_interactive_search';

const componentTabs = ["home", "content-sources", "generate-search-client", "search-tuning", "analytics-v2", "account", "user-role-management", "manage-synonyms", "addons", "api", "notifications","security","knowledge-graph","oauthClients","marketplace","chat-bot","accountDetails"];

router.post("/hit", async function (req, res) {
    let esClient = new Client({
        nodes: req.headers.session.esClusterIp.split(','),
        nodeSelector: 'random'
    });
    const indexName = interactiveSearchIndexName;

    if (req.body.searchString.toLowerCase().indexOf('month') != -1) {
        req.body.searchString += 'last 30 days'
    }
    // For tab permissions
    if (req.body.selectedTabs && Array.isArray(req.body.selectedTabs) && req.body.selectedTabs.length > 0) {
        req.body.selectedTabs = req.body.selectedTabs;
        req.body.selectedTabs.push('home');//By default home is visible to everyone   
    }else if(req.body.selectedTabs  && typeof req.body.selectedTabs === "string"){
        req.body.selectedTabs = JSON.parse(req.body.selectedTabs);
        if(req.body.selectedTabs.length > 0){
            req.body.selectedTabs.push('home');//By default home is visible to everyone 
        }else{
            req.body.selectedTabs = componentTabs;
        }
    }else {
        req.body.selectedTabs = componentTabs;
    }
    try {
        let response = await esClient.search({
            index: indexName,
            body: {
                "highlight": {
                    "pre_tags": [
                        "<span class='highlight'>"
                    ],
                    "post_tags": [
                        "</span>"
                    ],
                    "fields": {
                        "search_query": {
                            "fragment_size": 250,
                            "order": "score"
                        }
                    }
                },
                "query": {
                    "bool": {
                        "should": [
                            {
                                "terms": {
                                    "components": req.body.selectedTabs
                                }
                            }
                        ],
                        "minimum_should_match": 1,
                        "must": {
                            "match": {
                                "search_query": req.body.searchString
                            }
                        }
                    }
                },
                "from": 0,
                "size": 4
            }
        });
        
        var categories = [];
        var newArr = [];
        var urlArr = [];
        var simpleArr = [];
        var mat_tab = [];
        if (response && response.body && response.body.hits && response.body.hits.hits) {
            var data = response.body.hits.hits;
            
            for (var i = 0; i < data.length; i++) {
                if (data[i].highlight && data[i].highlight.search_query && data[i].highlight.search_query[0]) {
                    data[i]._source.search_query1 = data[i]._source.search_query;
                    data[i]._source.search_query = data[i].highlight.search_query[0];
                }
            }
            var counter = 0;
            var today = req.body.searchString.toLowerCase().indexOf('today') != -1 ? true : false;
            var yesterday = req.body.searchString.toLowerCase().indexOf('yesterday') != -1 ? true : false;
            var day = req.body.searchString.toLowerCase().indexOf('day') != -1 ? true : false;
            var month = req.body.searchString.toLowerCase().indexOf('month') != -1 ? true : false;

            if (data.length > 0 && (req.body.searchString.toLowerCase().indexOf(data[0]["_source"]["search_query1"]) != -1 || month || day || yesterday || today) && (data[0]["_source"]["component_type"].startsWith("In Analytics") || data[0]["_source"]["component_type"] == "In Dashboard")) {
                var arr = [];
                if(data[0]["_source"]["component_type"] == "In Analytics Overview" || data[0]["_source"]["component_type"] == "In Analytics"){
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for today", "url": "/dashboard/" + data[0]["_source"]["components"] + "/overview/" + data[0]["_source"]["component_id"] , "date_range": "Today" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for yesterday", "url": "/dashboard/" + data[0]["_source"]["components"] + "/overview/" + data[0]["_source"]["component_id"] , "date_range": "Yesterday" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for last 7 days", "url": "/dashboard/" + data[0]["_source"]["components"] + "/overview/" + data[0]["_source"]["component_id"] , "date_range": "Last 7 Days" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for last 30 days", "url": "/dashboard/" + data[0]["_source"]["components"] + "/overview/" + data[0]["_source"]["component_id"] , "date_range": "Last 30 Days" });
                }else if(data[0]["_source"]["component_type"] == "In Analytics conversions"){
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for today", "url": "/dashboard/" + data[0]["_source"]["components"] + "/conversions/" + data[0]["_source"]["component_id"] , "date_range": "Today" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for yesterday", "url": "/dashboard/" + data[0]["_source"]["components"] + "/conversions/" + data[0]["_source"]["component_id"] , "date_range": "Yesterday" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for last 7 days", "url": "/dashboard/" + data[0]["_source"]["components"] + "/conversions/" + data[0]["_source"]["component_id"] , "date_range": "Last 7 Days" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for last 30 days", "url": "/dashboard/" + data[0]["_source"]["components"] + "/conversions/" + data[0]["_source"]["component_id"] , "date_range": "Last 30 Days" });
                }else if(data[0]["_source"]["component_type"] == "In Analytics content-gap-analysis"){
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for today", "url": "/dashboard/" + data[0]["_source"]["components"] + "/content-gap-analysis/" + data[0]["_source"]["component_id"] , "date_range": "Today" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for yesterday", "url": "/dashboard/" + data[0]["_source"]["components"] + "/content-gap-analysis/" + data[0]["_source"]["component_id"] , "date_range": "Yesterday" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for last 7 days", "url": "/dashboard/" + data[0]["_source"]["components"] + "/content-gap-analysis/" + data[0]["_source"]["component_id"] , "date_range": "Last 7 Days" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for last 30 days", "url": "/dashboard/" + data[0]["_source"]["components"] + "/content-gap-analysis/" + data[0]["_source"]["component_id"] , "date_range": "Last 30 Days" });
                }
                else{
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for today", "url": "/dashboard/" + data[0]["_source"]["components"] + "/" + data[0]["_source"]["component_id"], "date_range": "Today" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for yesterday", "url": "/dashboard/" + data[0]["_source"]["components"] + "/" + data[0]["_source"]["component_id"], "date_range": "Yesterday" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for last 7 days", "url": "/dashboard/" + data[0]["_source"]["components"] + "/" + data[0]["_source"]["component_id"], "date_range": "Last 7 Days" });
                    arr.push({ "search_query": data[0]["_source"]["search_query"] + " for last 30 days", "url": "/dashboard/" + data[0]["_source"]["components"] + "/" + data[0]["_source"]["component_id"], "date_range": "Last 30 Days" });
                }

                for (var i = 0; i < arr.length; i++) {
                    var obj = { "hits": arr[i], "counter": i };
                    urlArr.push(arr[0].url);
                    newArr.push(obj);
                }
                res.send({ message: 'saved fatched', code: 200, "Arr": urlArr, "searchdata": newArr, "mat_tab": [], "twoD": false });
            } else {
                for (var i = 0; i < data.length; i++) {
                    var flag = 0;
                    for (var j = 0; j < categories.length; j++) {
                        if (data[i]["_source"]["component_type"] == categories[j]["component_type"]) {
                            flag = 1;
                            categories[j]["values"].push(data[i]);
                        }
                    }
                    if (flag == 0) {
                        var values = [];
                        values.push(data[i]);
                        categories.push({ "component_type": data[i]["_source"]["component_type"], values: values });
                    }
                }

                //   data[i]["_source"]["counter"]=counter++;
                for (var i = 0; i < categories.length; i++) {
                    for (var j = 0; j < categories[i].values.length; j++) {
                        categories[i].values[j]["_source"]["counter"] = counter++;
                        mat_tab.push(categories[i].values[j]["_source"].mat_tab);

                        if(categories[i].component_type == "In Analytics Overview"  || categories[i].component_type == "In Analytics"){
                            simpleArr.push('/dashboard/' + categories[i].values[j]["_source"].components + "/overview/" + categories[i].values[j]["_source"].component_id)
                        }else if( categories[i].component_type == "In Analytics conversions"){
                            simpleArr.push('/dashboard/' + categories[i].values[j]["_source"].components + "/conversions/" + categories[i].values[j]["_source"].component_id)
                        }else if( categories[i].component_type == "In Analytics content-gap-analysis"){
                            simpleArr.push('/dashboard/' + categories[i].values[j]["_source"].components + "/content-gap-analysis/" + categories[i].values[j]["_source"].component_id)
                        }else if(categories[i].component_type == "In Api"){
                            simpleArr.push('/dashboard/api');
                        }else if(categories[i].component_type == "In Apps"){
                            simpleArr.push('/dashboard/apps');
                        }else {
                            simpleArr.push('/dashboard/' + categories[i].values[j]["_source"].components + "/" + categories[i].values[j]["_source"].component_id)
                        }
                    }
                }
                res.send({ message: 'saved fatched', code: 200, "searchdata": categories, "Arr": simpleArr, "mat_tab": mat_tab, "twoD": true });

            }
        } else 
            res.send({ message: 'saved fatched', code: 200, "searchdata": categories, "Arr": simpleArr, "mat_tab": mat_tab, "twoD": true });

    } catch(e) {
        console.log(e);
    }

    //options.body = JSON.stringify(options.quary_body);
});

module.exports = {
    router: router
};
