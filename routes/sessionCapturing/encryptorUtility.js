var child_process = require('child_process');
var fs= require('fs')
var path=require('path')
function cryptED(opcode, text, cb){
  var outfile = fs.createWriteStream(path.join(__dirname, "textEncryption.out"));
  var crawlProcess = child_process.spawn(path.join(__dirname,"encrypt.sh"), [opcode, text]);

  crawlProcess.stdout.on('data', function (data) {
   fs.appendFile(outfile.path, data,(err)=>{console.log(err)});
  });

  crawlProcess.stderr.on('data', function (data) {
   fs.appendFile(outfile.path, data,(err)=>{console.log(err)});
  });

  crawlProcess.on('exit', function (code) {
    console.log('child process exited with code ' + code);
    fs.readFile(path.join(__dirname,"encryption.txt"),'utf-8', function(e,d){
      cb(null,d);
    });
  });
}

exports.cryptED = cryptED;
