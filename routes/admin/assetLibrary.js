// var spawn = require('child_process').spawn;
var path = require('path');
var fs = require('fs');
var async = require('async');
var express = require('express');
var router = express.Router();
var request = require("request");
var url = require('url');
var xmlserializer = require('xmlserializer');
const createDOMPurify = require('dompurify');
const { JSDOM } = require('jsdom');
var multipart = require('connect-multiparty');
var multipartMiddleware = multipart();
var commonFunctions = require('./../../utils/commonFunctions');
const fileType = require('detect-file-type');
var redis = require("redis");
var client = redis.createClient(config.get("redis.redisPort"), config.get("redis.redisHost"));
const moment = require('moment');

const { readFile } = require('fs')
const { promisify } = require('util')
const asyncReadFile = promisify(readFile)

const returnSvg = async (path) => {
  const data = await asyncReadFile(path)

  // since fs.readFile returns a buffer, we should probably convert it to a string.
  return data.toString() 
}

router.get('/getIconList', function (req, res, next) {
  fs.readdir((`resources/Asset-Library`), function (err, result) {
    if (err) {
      commonFunctions.errorlogger.error(err);
      res.sendStatus(403);
      return;
    };
    const index = result.indexOf('README.md');
    if (index > -1) {
      result.splice(index, 1);
    }
    const chatbot = result.indexOf('chat-bot');
    if(chatbot > -1) {
      result.splice(index, 1);
    }
    res.send(result);
  });
});

router.post('/uploadClientIcon', multipartMiddleware, function (req, res, next) {
  let accepted_extensions = ['jpg', 'png', 'svg', 'jpeg'];
  let fileNameExt = req.files.uploadFile.path.split('.');
  client.mget(["file-upload-details-" + req.connection.remoteAddress, "file-upload-detailsCount-" + req.connection.remoteAddress], function (error, response) {
    var result = response.map(u => JSON.parse(u))
    var firstRun = false;
    if (result[0] == null) {
      firstRun = true;
      var ipDetails = { "ip": req.connection.remoteAddress };
      var ipDetailsCount = {
        counter: 1
      }
      if (result[1] != null) {
        client.del("file-upload-detailsCount-" + req.connection.remoteAddress);
        result[1].counter = 0;
      }
    } else {
      var ipDetails = { "ip": result[0].ip };
      var ipDetailsCount = {
        counter: result[1].counter
      }
    }
    if (firstRun || ipDetails.ip != req.connection.remoteAddress || (ipDetails.ip == req.connection.remoteAddress && ipDetailsCount.counter < 5)) {
      if ((result[0] && (ipDetails.ip == req.connection.remoteAddress))) {
        ipDetailsCount.counter++;
      }
      fs.readFile(req.files.uploadFile.path, function (err, data) {
        base64data = new Buffer(data);
        fileType.fromBuffer(base64data, function (err, result) {
          let mime = result;
          if (req.headers.referer.includes('/asset-library') && (!mime || !accepted_extensions.includes(mime.ext) || !(/^[A-Za-z0-9_\.\-\s]+$/.test(req.files.uploadFile.name)) || !accepted_extensions.includes(fileNameExt[1]))) {
            res.send({ code: 106, message: ' Invalid File',sameIp: false  });
          }
          else {
            var normPath = path.normalize(req.files.uploadFile.name).replace(/^(\.\.[\/\\])+/, '');
            fileUpload(req, function (err) {
              if (err) {
                checkDirectory(`resources/Asset-Library`, function (error) {
                  if (error) {
                    console.log(error);
                  } else {
                    fileUpload(req, function (err) { console.log(error) })
                  }
                });
              }
              if(mime.ext === 'svg') {
                returnSvg(path.join(`resources/Asset-Library`, req.files.uploadFile.name))
                  .then(data => {
                    // Sanitize the content for scripts
                    const window = new JSDOM('').window;
                    const DOMPurify = createDOMPurify(window);
                    const clean = DOMPurify.sanitize(data);
                    // Removal of external links inside SVG
                    const dom = new JSDOM(clean);
                    const suspectedContent = dom.window.document.querySelectorAll('script, image, a, animate, animateMotion, animateTransform, discard, feImage, linearGradient, mpath, pattern, radialGradient, set, textPath, use');
                    for (let i = 0; i < suspectedContent.length; i++) {
                      suspectedContent[i].removeAttribute('xlink:href');
                      suspectedContent[i].removeAttribute('href');
                    }
                    fs.writeFile(path.join(`resources/Asset-Library`, req.files.uploadFile.name), dom.window.document.body.innerHTML, (err) => {  
                      if (err) {
                        console.error(`failed to write svg file: ${err}`);
                        res.send({ message: 'Unable to upload SVG file', flag: 400 });
                      }
                    });
                  })
                  .catch(err => {
                    console.error(`failed to read svg file: ${err}`);
                    res.send({ message: 'Unable to upload SVG file', flag: 400 });
                  })
              }
              if (firstRun) {
                client.setex(`file-upload-details-${req.connection.remoteAddress}`, 60, JSON.stringify(ipDetails));
              }
              client.setex(`file-upload-detailsCount-${req.connection.remoteAddress}`, 2 * 60, JSON.stringify(ipDetailsCount));
              res.send({ message: 'saved', code: 200, sameIp: false });
            });
          }
        })
      });
    }
    else {
      res.send({ message: 'you are not allowed to upload icon', sameIp: true });
    }
  })
});

router.post('/deleteIcon', function (req, res, next) {
  let allowDelete = 1;
  let sql = `SELECT icon from search_clients_to_content_objects`;

  connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {

    rows.forEach(x => {
      if (x.icon === `resources/Asset-Library/` + req.body.fileName.name) {
        allowDelete = 0;
        return allowDelete;
      }
    })

    if (allowDelete === 1) {
      fs.unlink(`resources/Asset-Library/` + req.body.fileName.name, function (err) {
        if (err) {
          commonFunctions.errorlogger.error(err)
          return;
        };
        res.send({ message: 'deleted', flag: 200 });
      })
    }
    else {
      res.send({ message: 'Icon is currently being used in a search client', flag: 400 });
    }
  })
});


function checkDirectory(directory, callback) {
  fs.stat(directory, function (err, stats) {
    if (err) {
      fs.mkdir(directory, callback);
    } else {
      callback(err)
    }
  });
}

function fileUpload(req, callback) {
  fs.writeFile(path.join(`resources/Asset-Library`, req.files.uploadFile.name), fs.readFileSync(req.files.uploadFile.path), 'binary', function (err) {
    callback(err)
  })
}


module.exports =
{
  router: router
}
