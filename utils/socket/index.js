const crypto = require('crypto');
const socketio = require('socket.io');
const config = require('config');
var commonFunctions = require('../commonFunctions');

const ENC_KEY = config.get('aes.encryptionkey');
const IV = config.get('aes.iv');

let IO;

function listen(server) {
  // allowEIO3 added due to version mismatch with client
  const io = socketio(server, { path: '/socket.io', allowEIO3: true, cookie: {
    name: "io",
    secure: true,
    httpOnly: true,
    sameSite: commonFunctions.getSameSiteCookie()
  }});
  io.on('connection', (socket) => {
    IO = io;
    socket.on('disconnect', () => {
        commonFunctions.errorlogger.warn('User disconnected');
    });
  });

  return io;
}

const encryptTenantId = (text) => {
  const cipher = crypto.createCipheriv('aes-256-cbc', ENC_KEY, IV);
  let encrypted = cipher.update(text, 'utf8', 'base64');
  encrypted += cipher.final('base64');

  return encrypted;
};


const emitCompleteStatus = (tenantId, source) => {
  if (IO) {
    const encodedTenantId = encryptTenantId(tenantId);
    const url = `${source}_${encodedTenantId}`
    IO.sockets.emit(url);
  }
};

module.exports = {
  listen,
  emitCompleteStatus
};
