#!/bin/bash

# change working directory to crawler source directory
#pushd ./plugins/sapCrawler/crawler/helpdocs
pwd
pushd ./javaUtilities

#compile java file
javac -cp ".:./lib/apache-log4j-extras-1.2.17.jar:./lib/cglib-nodep-3.2.4.jar:./lib/client-combined-3.4.0-nodeps.jar:./lib/commons-cli-1.3.1.jar:./lib/commons-codec-1.10.jar:./lib/commons-exec-1.3.jar:./lib/commons-io-2.5.jar:./lib/commons-lang3-3.5.jar:./lib/commons-logging-1.2.jar:./lib/compiler-0.8.13.jar:./lib/compress-lzf-1.0.2.jar:./lib/cssparser-0.9.22.jar:./lib/elasticsearch-2.3.5.jar:./lib/gson-2.8.0.jar:./lib/guava-21.0.jar:./lib/hamcrest-core-1.3.jar:./lib/HdrHistogram-2.1.6.jar:./lib/hppc-0.7.1.jar:./lib/htmlunit-2.26.jar:./lib/htmlunit-core-js-2.26.jar:./lib/htmlunit-driver-2.26.jar:./lib/httpclient-4.5.3.jar:./lib/httpcore-4.4.6.jar:./lib/httpmime-4.5.3.jar:./lib/jackson-core-2.6.6.jar:./lib/jackson-dataformat-cbor-2.6.6.jar:./lib/jackson-dataformat-smile-2.6.6.jar:./lib/jackson-dataformat-yaml-2.6.6.jar:./lib/javax.servlet-api-3.1.0.jar:./lib/jetty-io-9.4.1.v20170120.jar:./lib/jetty-util-9.4.1.v20170120.jar:./lib/jna-4.1.0.jar:./lib/jna-platform-4.1.0.jar:./lib/joda-convert-1.2.jar:./lib/joda-time-2.9.4.jar:./lib/jsr166e-1.1.0.jar:./lib/jts-1.13.jar:./lib/junit-4.12.jar:./lib/log4j-1.2.17.jar:./lib/lucene-analyzers-common-5.5.0.jar:./lib/lucene-backward-codecs-5.5.0.jar:./lib/lucene-core-5.5.0.jar:./lib/lucene-grouping-5.5.0.jar:./lib/lucene-highlighter-5.5.0.jar:./lib/lucene-join-5.5.0.jar:./lib/lucene-memory-5.5.0.jar:./lib/lucene-misc-5.5.0.jar:./lib/lucene-queries-5.5.0.jar:./lib/lucene-queryparser-5.5.0.jar:./lib/lucene-sandbox-5.5.0.jar:./lib/lucene-spatial3d-5.5.0.jar:./lib/lucene-spatial-5.5.0.jar:./lib/lucene-suggest-5.5.0.jar:./lib/mysql-connector-java-3.0.10-stable-bin.jar:./lib/neko-htmlunit-2.25.jar:./lib/netty-3.10.5.Final.jar:./lib/phantomjsdriver-1.4.0.jar:./lib/sac-1.3.jar:./lib/securesm-1.0.jar:./lib/serializer-2.7.2.jar:./lib/snakeyaml-1.15.jar:./lib/spatial4j-0.5.jar:./lib/t-digest-3.0.jar:./lib/websocket-api-9.4.3.v20170317.jar:./lib/websocket-client-9.4.3.v20170317.jar:./lib/websocket-common-9.4.3.v20170317.jar:./lib/xalan-2.7.2.jar:./lib/xercesImpl-2.11.0.jar:./lib/xml-apis-1.4.01.jar" GeneralisedSessionCapturingCode.java

#execute java file for fetching helpdoc cookie
java -cp ".:./lib/apache-log4j-extras-1.2.17.jar:./lib/cglib-nodep-3.2.4.jar:./lib/client-combined-3.4.0-nodeps.jar:./lib/commons-cli-1.3.1.jar:./lib/commons-codec-1.10.jar:./lib/commons-exec-1.3.jar:./lib/commons-io-2.5.jar:./lib/commons-lang3-3.5.jar:./lib/commons-logging-1.2.jar:./lib/compiler-0.8.13.jar:./lib/compress-lzf-1.0.2.jar:./lib/cssparser-0.9.22.jar:./lib/elasticsearch-2.3.5.jar:./lib/gson-2.8.0.jar:./lib/guava-21.0.jar:./lib/hamcrest-core-1.3.jar:./lib/HdrHistogram-2.1.6.jar:./lib/hppc-0.7.1.jar:./lib/htmlunit-2.26.jar:./lib/htmlunit-core-js-2.26.jar:./lib/htmlunit-driver-2.26.jar:./lib/httpclient-4.5.3.jar:./lib/httpcore-4.4.6.jar:./lib/httpmime-4.5.3.jar:./lib/jackson-core-2.6.6.jar:./lib/jackson-dataformat-cbor-2.6.6.jar:./lib/jackson-dataformat-smile-2.6.6.jar:./lib/jackson-dataformat-yaml-2.6.6.jar:./lib/javax.servlet-api-3.1.0.jar:./lib/jetty-io-9.4.1.v20170120.jar:./lib/jetty-util-9.4.1.v20170120.jar:./lib/jna-4.1.0.jar:./lib/jna-platform-4.1.0.jar:./lib/joda-convert-1.2.jar:./lib/joda-time-2.9.4.jar:./lib/jsr166e-1.1.0.jar:./lib/jts-1.13.jar:./lib/junit-4.12.jar:./lib/log4j-1.2.17.jar:./lib/lucene-analyzers-common-5.5.0.jar:./lib/lucene-backward-codecs-5.5.0.jar:./lib/lucene-core-5.5.0.jar:./lib/lucene-grouping-5.5.0.jar:./lib/lucene-highlighter-5.5.0.jar:./lib/lucene-join-5.5.0.jar:./lib/lucene-memory-5.5.0.jar:./lib/lucene-misc-5.5.0.jar:./lib/lucene-queries-5.5.0.jar:./lib/lucene-queryparser-5.5.0.jar:./lib/lucene-sandbox-5.5.0.jar:./lib/lucene-spatial3d-5.5.0.jar:./lib/lucene-spatial-5.5.0.jar:./lib/lucene-suggest-5.5.0.jar:./lib/mysql-connector-java-3.0.10-stable-bin.jar:./lib/neko-htmlunit-2.25.jar:./lib/netty-3.10.5.Final.jar:./lib/phantomjsdriver-1.4.0.jar:./lib/sac-1.3.jar:./lib/securesm-1.0.jar:./lib/serializer-2.7.2.jar:./lib/snakeyaml-1.15.jar:./lib/spatial4j-0.5.jar:./lib/t-digest-3.0.jar:./lib/websocket-api-9.4.3.v20170317.jar:./lib/websocket-client-9.4.3.v20170317.jar:./lib/websocket-common-9.4.3.v20170317.jar:./lib/xalan-2.7.2.jar:./lib/xercesImpl-2.11.0.jar:./lib/xml-apis-1.4.01.jar" GeneralisedSessionCapturingCode $3 $5 $1 $6 $2 $7 $4

popd

