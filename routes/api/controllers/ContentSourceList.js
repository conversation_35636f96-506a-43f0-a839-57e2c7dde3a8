'use strict';

var url = require('url');

var contentSourceService = require('./ContentSourceListService');

module.exports.contentSourceListAll = function contentSourceListAll (req, res, next) {
    contentSourceService.getAllcs(req.swagger.params, res,req, next);
};

module.exports.contentSourceInfo = function contentSourceInfo (req, res, next) {
    contentSourceService.getcsInfo(req.swagger.params, res, req, next);
}

module.exports.contentSourceObjectDetails = function contentSourceObjectDetails (req, res, next) {
    contentSourceService.getcsObject(req.swagger.params,req, res, next);
}
module.exports.getContentSourceFields = function getContentSourceFields(req, res, next) {
    contentSourceService.getContentSourceFields(req.swagger.params, req, res)
}
module.exports.getContentSourceBulkData = function getContentSourceBulkData(req, res, next) {
    contentSourceService.getContentSourceBulkData(req.swagger.params,req, res,next)

}
module.exports.getScrollData = function getScrollData(req, res, next) {
    contentSourceService.getScrollData(req.swagger.params, req, res)
}

module.exports.contentSourceData = function contentSourceData(req, res, next){
    contentSourceService.getcsData(req.swagger.params, req, res, next);
}

module.exports.getDocumentData = function getDocumentData( req, res, next){
    contentSourceService.getElasticIndex(req.swagger.params, res,req, next);
}

module.exports.updateContentSourceDocument = function updateContentSourceDocument( req, res, next){
    contentSourceService.updateElasticIndex(req.swagger.params, res,req, next);
}

module.exports.deleteContentSourceDocument = function deleteContentSourceDocument(req, res, next){
    contentSourceService.deleteElasticDocument(req.swagger.params, res,req, next);
}

module.exports.documentBulkUpload = function documentBulkUpload( req, res, next){
    contentSourceService.bulkUploadElasticIndex(req.swagger.params, res,req, next);
}

module.exports.multiIndexBulkUpload = function multiIndexBulkUpload( req, res, next){
    contentSourceService.bulkUploadForMultipe(req.swagger.params, res,req, next);
}