var async = require('async');
var request = require("request");
const { OAuth2Client } = require('google-auth-library');
var scopes = ['https://www.googleapis.com/auth/userinfo.profile', 'https://www.googleapis.com/auth/youtube.force-ssl', 'https://www.googleapis.com/auth/youtube.readonly', 'https://www.googleapis.com/auth/userinfo.email'];
var gmailScopes = ['https://www.googleapis.com/auth/gmail.readonly', 'https://www.googleapis.com/auth/admin.directory.user.readonly', 'https://www.googleapis.com/auth/chat.spaces.readonly', 'https://www.googleapis.com/auth/chat.messages'];
var commonFunctions = require('./../../utils/commonFunctions');
var { youtube } = require('../../constants/appVariables');
const constants = require('./../../constants/constants');


const youtubeAuthorisationIntermediate = function(contentSource, authorization, callback){
    var clientSecret = authorization.client_secret || youtube.appSecret;
    var clientId = authorization.client_id || youtube.clientId;
    // var redirectUrl = config["adminURL"]+"/admin/authorization/oAuthCallback";
    var redirectUrl = "https://oauthsfdc.searchunify.com";    
    // const auth = new GoogleAuth();
    var oauth2Client = new OAuth2Client(clientId, clientSecret, redirectUrl);
    var authUrl = oauth2Client.generateAuthUrl({
        access_type: 'offline',
        prompt:'consent',        
        scope: contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.gmail ? gmailScopes : scopes,
        include_granted_scopes: true,
        enable_granular_consent: true
    });
    callback(null,{ "oauth": authUrl });
}

const getUser  = function(accessToken, contentSourceId,req, callback){
    var options = { 
        method: 'GET',
        url: 'https://www.googleapis.com/oauth2/v1/userinfo',
        qs: { 
            access_token: accessToken
        },
        headers: {
            'cache-control': 'no-cache' 
        } 
    };
    request(options, function (error, response, body) {
        if(error){
            callback(error, null);
        }
        else if( response.statusCode != 200){
            callback("bad status Code", null);
        }
        else{
            body = JSON.parse(body);
            userID = body.id;
            commonFunctions.getContentSourceDataById(contentSourceId, req, function(err,data){
                if(data.authorization.htaccessUsername)
                {
                    if(data.authorization.htaccessUsername !== userID){
                        commonFunctions.deleteDriveSpace(contentSourceId,req, function (err, result) {
                            if (!err) {
                                callback(null, userID);
                            }
                            else{
                                callback(err, null);
                            }
                        });                        
                    }
                    else{
                        callback(null, userID);                        
                    }
                }
                else{
                    callback(null, userID);
                }
            });
        }
    });
}

const requestTofetchObjectFields = function (authorization, objectName,req, callback) {
    var customFieldArray = [];
    var flag = 7;
    async.auto({
        get_default_fields: function (cb) {
            commonFunctions.getContentSourceObjectsAndFieldsById(authorization.content_source_id,req, function (err, res) {
                if (err) {
                    cb(err, null)
                }
                else {
                  var standardObj = commonFunctions.metadata.loadMappingForDbAndElastic("17").objects;
                    var fieldsArr = [];

                    for (var i = 0; i < standardObj.length; i++) {
                        if (objectName == standardObj[i].name) {
                            flag = 0;
                            fieldsArr = commonFunctions.metadata.loadMappingForDbAndElastic("17").fields.concat(standardObj[i].fields);
                            break;
                        }
                    }
                    var listToAdd = [];
                    var listInt = -1;
                    for (var type = 0; type < res.length; type++) {
                        if (objectName == res[type].name) {
                            listInt = type;
                        }
                    }
                    if (listInt != -1) {
                        type = listInt;
                        listToAdd = res[type].fields;
                        for (var mn = 0; mn < fieldsArr.length; mn++) {
                            var found = 0;
                            for (var field = 0; field < res[type].fields.length; field++) {
                                if (fieldsArr[mn].name == res[type].fields[field].name) {
                                    found = 1;
                                    break;
                                }
                            }
                            if (found == 0) {
                                listToAdd.push(fieldsArr[mn]);
                            }
                        }
                        for (var field = 0; field < listToAdd.length; field++) {

                            customFieldArray.push({
                                "name": listToAdd[field].name,
                                "label": listToAdd[field].label,
                                "type": listToAdd[field].type
                            })
                        }

                    } else {
                        for (var field = 0; field < fieldsArr.length; field++) {

                            customFieldArray.push({
                                "name": fieldsArr[field].name,
                                "label": fieldsArr[field].label,
                                "type": fieldsArr[field].type
                            })
                        }
                    }
                    cb(null, customFieldArray);
                }
            })
        }
    }, function (error, resp) {
        if (error) {
            callback(error, null)
        }
        else {
            callback(null, { data: customFieldArray, flag: flag });
        }
    })
}

const revokeAccess = function (authorization, callback) {
    try {
        
        let options = {
            method: 'POST',
            url: 'https://oauth2.googleapis.com/revoke',
            form:  {
                token: authorization.accessToken
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        };

        request(options, function (error, response, body) {
            if (error) {
                commonFunctions.errorlogger.error("Error in revoking access response", error);
                callback(error, null);
            } else if( response.statusCode === 400) {
                callback(null, 'Access already revoked');
            }
            else if(response.statusCode !== 200){
                callback(response.body, null);                    
            } 
            else {
                body = JSON.parse(body);
                    callback(null, 'Access revoked');;            
            }
        })


    } catch (error) {
        commonFunctions.errorlogger.error("Error in revoking access: ", error);
        callback(error, null);
    }
}

const getTokenInfo = (accessToken, callback) => {
    try {
        let options = {
            method: 'POST',
            url: `https://oauth2.googleapis.com/tokeninfo?access_token=${accessToken}`,
            headers: {
                'cache-control': 'no-cache' 
            }
        };

        request(options, function (error, response, body) {
            if (error) {
                commonFunctions.errorlogger.error("Error in getTokenInfo response: ", error);
                callback(error, null);
            }
            body = JSON.parse(body);

            callback(null, body);
        })

    } catch (error) {
        commonFunctions.errorlogger.error("Error in getTokenInfo: ", error);
        callback(error, null);
    }
}

module.exports = {
    youtubeAuthorisationIntermediate: youtubeAuthorisationIntermediate,
    requestTofetchObjectFields: requestTofetchObjectFields,
    getUser:getUser,
    revokeAccess,
    getTokenInfo
}
