module.exports = {
  "objects": [
    {
      "name": "posts",
      "label": "Posts",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "id",
          "label": "Id",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "team",
          "label": "Team",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "team_owner",
          "label": "Team Owner",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "channel_name",
          "label": "Channel Name",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "channel_description",
          "label": "Channel Description",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "channel_id",
          "label": "Channel ID",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "channel_url",
          "label": "Channel URL",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "channel_type",
          "label": "Channel Type",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "channel_created_date",
          "label": "Channel Created Date",
          "type": "datetime",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 0,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "post",
          "label": "Post",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "replies",
          "label": "Replies",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 0,
          "isActive": 1
        },
        {
          "name": "author_name",
          "label": "Author Name",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "author_email",
          "label": "Author Email",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "author_title",
          "label": "Author Title",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "conversation_start_date",
          "label": "Conversation Start Date",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "tags",
          "label": "Tags",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "ms_team_id",
          "label": "Team ID                                                                              ",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "view_href",
          "label": "Url",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        }
      ]

    },
    {
      "name": "files",
      "label": "Files",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "id",
          "label": "Id",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "file_name",
          "label": "File Name",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "folder_name",
          "label": "Folder Name",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "modified_date",
          "label": "Modified Date",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 0,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "modified_by",
          "label": "Modified By",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "file_url",
          "label": "File URL",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "document_type",
          "label": "Document Type",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "file_id",
          "label": "File ID",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "attachment",
          "label": "Attachment",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "ms_team_id",
          "label": "Team ID",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "view_href",
          "label": "View Href",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "attachment_multiple",
          "label": "Attachment Multiple",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        }
      ]
    },
    {
      "name": "wiki",
      "label": "Wiki",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name":"id",
          "label":"Id",
          "type":"string",
          "isFilterable":1,
          "isSortable":1,
          "isSearchable":1,
          "isActive":1,
          "isMerged": 1
        },
        {
          "name": "wiki_name",
          "label": "Wiki Name",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "wiki_link",
          "label": "Wiki Link",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "last_edited", // check this
          "label": "Last Edited",
          "type": "datetime",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 0,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "channel_name",
          "label": "Channel Name",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "channel_id",
          "label": "Channel Id ",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "ms_team_id",
          "label": "Team ID",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "view_href",
          "label": "View Href",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        }
      ]
    },
    {
      "name": "tasks",
      "label": "Tasks",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name":"id",
          "label":"Id",
          "type":"string",
          "isFilterable":1,
          "isSortable":1,
          "isSearchable":1,
          "isActive":1,
          "isMerged": 1
        },
        {   
          "name": "task_title",
          "label": "Task title",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "assigned_to",
          "label": "Assigned To",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "due_date",
          "label": "Due Date",
          "type": "datetime",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 0,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "bucket",
          "label": "Bucket",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "start_date",
          "label": "Start Date",
          "type": "datetime",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 0,
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "comments",
          "label": "Comments",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        }, {
          "name": "attachment",
          "label": "Attachment",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        }, {
          "name": "checklist",
          "label": "Checklist",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "plan_name",
          "label": "Plane Name",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "ms_team_id",
          "label": "Team ID",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1, 
          "isMerged": 1
        },
        {
          "name": "attachment_multiple",
          "label": "Attachment Multiple",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        }
      ]
    }

  ],
  "fields": [
  ]
}
