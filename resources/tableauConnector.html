<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-store" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js" type="text/javascript"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/js-cookie/2.0.2/js.cookie.min.js"
        type="text/javascript"></script>
    <script src="https://connectors.tableau.com/libs/tableauwdc-2.3.latest.js" type="text/javascript"></script>
    <script type="text/javascript">
            var config = {
                redirectUri: <%- JSON.stringify(redirect_uri) %>,
                authUrl:  <%- JSON.stringify(authUrl) %>,
                searchClientIds:  <%- JSON.stringify(searchClientIds) %>
            };
    </script>
    <script src="../../resources/tableau/webDataConnector.js" type="text/javascript"></script>
</head>

<body>
    <div style="text-align: center">
        <div>
            <div>
                <p class="signedin">PLEASE SELECT THE PERIOD TO FETCH DATA INTO TABLEAU</p>
                <div class="signedin" >
                    Select search client UID: <select id="searchClientNumber">
                </select>
                </div>
                <script>
                    var select = document.getElementById("searchClientNumber");
                    var options = config.searchClientIds;
                    for (var i = 0; i < options.length; i++) {
                        var opt = options[i];
                        var el = document.createElement("option");
                        el.textContent = opt;
                        el.value = opt;
                        select.appendChild(el);
                    }
                </script>
                <div class="notsignedin">
                    <p>PLEASE LOGIN TO YOUR SEARCHUNIFY ADMIN CONSOLE</p>
                    <p><label>Client Id  </label><input type="test" id="clientId" style="width: 28%; margin: 0 auto;"></p>
                </div>
                <button type="button" id="connectbutton" style="padding: 5px;width: 95px;border-radius: 2px;background-color: ffff;background-color: #ffff;border-color: #e2e2e2;">Proceed</button>
                <input type="date" id="start" value="2023-02-01" style="width: 25%; margin: 0 auto;">
                <input type="date" id="end" value="2023-03-01" style="width: 25%">
                <button type="button" id="fetchSuAnalytics" onclick="myFunction()" style="margin: 10px; padding: 5px;width: 95px;border-radius: 2px;background-color: ffff;background-color: #ffff;border-color: #e2e2e2;">Get Data</button>
            </div>
        </div>
    </div>
</body>
</html>