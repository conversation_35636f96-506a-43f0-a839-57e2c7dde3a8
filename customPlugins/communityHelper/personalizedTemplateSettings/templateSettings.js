const router = require('express').Router();
const request = require("request");
const async = require('async')
const commonFunctions = require('../../../utils/commonFunctions');
const stopwords = require('nltk-stopwords')
const english = stopwords.load('english')
const posTagger = require('wink-pos-tagger');
const tagger = posTagger();

router.post("/saveTemplate", (req, res) => {
    let { templateBody, id, templateFront, template_name } = req.body;
    let created_date = new Date();
    if (!templateBody || !template_name) {
        res.send({ "message": "Some Fields Empty!!" })
    } else {
        let sql = `INSERT INTO community_helper_template (id,template_body,response_type,template_front,template_name,created_date) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE id=VALUES(id),template_body=VALUES(template_body),template_front=VALUES(template_front),template_name=VALUES(template_name)`;
        let params = [(id) ? id : null, templateBody, 'su_results', templateFront, template_name, created_date]
        connection[req.headers['tenant-id']].execute.query(sql, params, (err, result) => {
            if (err) {
                commonFunctions.errorlogger.error(err);
            } else {
                res.send(result);
            }
        })
    }
})

const fetchAllTemplates = (botId, tenantId, cb) => {
    let sql = `SELECT * FROM community_helper_template cht JOIN community_helper_bot_responses chbr ON cht.id = chbr.community_helper_template_id WHERE chbr.community_helper_bot_id = ?`
    connection[tenantId].execute.query(sql, [botId], (err, results) => {
        if (err) {
            commonFunctions.errorlogger.error(err);
            cb(null, []);
        } else {
            cb(null, results);
        }
    })
}

router.get("/getTemplates", (req, res) => {
    const botId = req.query.bot_id;
    const tenantId = req.headers['tenant-id'];
    fetchAllTemplates(botId,tenantId, (error, result) => {
        res.send(result);
    })
})


router.get("/deleteTemplate", (req, res) => {
    let id = req.query.template_id;
    const sql = `DELETE FROM community_helper_template WHERE id=?`;
    connection[req.headers['tenant-id']].execute.query(sql, [id], (err, result) => {
        if (err) {
            commonFunctions.errorlogger.error(err);
        } else {
            res.send({ "message": "Deleted" });
        }
    })
})

router.post("/saveResponse", (req, res) => {
    console.log(req.body);
    let { bot_id, template_id, freq_type, freq_time } = req.body;

    let sql = `INSERT INTO community_helper_bot_responses (community_helper_bot_id,community_helper_template_id,response_freq_type,response_freq_time) VALUES (?,?,?,?)`;

    connection[req.headers['tenant-id']].execute.query(sql, [bot_id, template_id, freq_type, freq_time], (err, result) => {
        if (err) {
            commonFunctions.errorlogger.error(err);
        } else {
            res.send({ "message": "Response Saved" });
        }
    })
})

router.post("/saveResponseFrequency",(req,res)=>{
    let {bot_id,freq_type,freq_time} = req.body;
    
    // let sql = `UPDATE community_helper_bot_responses (community_helper_bot_id,community_helper_template_id,response_freq_type,response_freq_time) VALUES (?,?,?,?)`;
    let sql = `UPDATE community_helper_bot_responses SET response_freq_type=?, response_freq_time=? WHERE community_helper_bot_id=?;`
    connection[req.headers['tenant-id']].execute.query(sql,[freq_type,freq_time,bot_id],(err,result)=>{
        if(err){
            commonFunctions.errorlogger.error(err);
        } else {
            res.send({ "message": "Response Saved" });
        }
    })
})

const getBotResponses = (botId,req, cb)=>{
    let sql = `SELECT * from community_helper_bot_responses WHERE community_helper_bot_id=?`
    connection[req.headers['tenant-id']].execute.query(sql, [botId], (err, result) => {
        if (err) {
            commonFunctions.errorlogger.error(err);
            cb(null, []);
        } else {
            cb(null, result);
        }
    })
}

router.get("/getResponse", (req, res) => {
    let bot_id = req.query.bot_id;
    getBotResponses(bot_id,req, (error, result)=>{
        res.send(result);
    })
})


function getTokenizeText(text) {
    let tokens = new Set();
    let stopword_removed = stopwords.remove(text, english)
    stopword_removed = tagger.tagSentence(stopword_removed);

    stopword_removed.forEach(word => {
        if (word.tag != "punctuation" && word.pos != 'DT' && word.pos != 'VBP' && word.pos != 'PRP' && word.pos != 'RB') {
            tokens.add(word.value)
        }
    });
    let tokenArray = [...tokens];
    let tokenString = tokenArray.join(' ');
    return tokenString //converting set to array
}

const getTemplateWithValues = (templateBody, s, c, platformType = 3) => {
    let newTemplate = templateBody.replace(/\n/g, "\\n");
    newTemplate = JSON.parse(newTemplate);

    newTemplate.forEach(template => {
        s.forEach(element => {
            let rex = new RegExp("{\\$" + element.name + "\\$}", 'g');
            if (template.type == "Mention")
                template.id = template.id.replace(rex, element.value)
            else
                template.text = template.text.replace(rex, element.value)
        });

        c.forEach(element => {
            let rex = new RegExp("{\\#" + element.name + "\\#}", 'g');
            if (template.type == "Mention")                
                template.id = template.id.replace(rex, element.value)
            else
                if (element.name == 'author' && platformType == 2)
                    template.text = template.text.replace(rex, `<a href='${element.view_href}'>${element.value}</a>`);
                else
                    template.text = template.text.replace(rex, element.value)
        })
        if(template.text){
            template.text = template.text.replace(/&lt;/g,'<').replace(/&gt;/g,'>').replace(/&amp;/g,'&');
            template.text = commonFunctions.stripHtml(template.text);
        }
    });
    return newTemplate;
}

const getSearchFields = (template) => {
    var values = []
    var reg = /{\$([^}]+)\$}/g;
    var text;
    while (text = reg.exec(template)) {
        values.push(text[1]);
    }
    return values;
};

const getPlatformFields = (template) => {
    var values = []
    const re = /{#([^}]+)#}/g;
    var text;
    while (text = re.exec(template)) {
        values.push(text[1]);
    }
    return values;
}

module.exports = {
    router,
    fetchAllTemplates,
    getSearchFields,
    getPlatformFields,
    getTemplateWithValues,
    getTokenizeText,
    getBotResponses
};