// To use this migration place the file in admin directory and then hit 
// node spCharsMigractionScript.js 2>&1 | tee specialCharLogs.txt 

const fs = require("fs");
const path = require("path");
const async = require("async");
const configPath = path.join(__dirname, "../../config");
process.env.NODE_CONFIG_DIR = configPath;
const environment = require("../../routes/environment");
process.env.NODE_ENV = environment.configuration;
const config = require("config");
const mysql = require("mysql");
const connection = mysql.createConnection({
    host: config.get("databaseSettings.host"),
    user: config.get("databaseSettings.user"),
    password: config.get("databaseSettings.password"),
    database: config.get("databaseSettings.database"),
    port: config.get("databaseSettings.mysqlPORT"),
    multipleStatements: true
});
let request = require("request");

// For logging purposes
let sTime = new Date();
let eTime = new Date();
let totalDocuments = 0;

// Pass specific index names if required otherwise all will be picked from database
// Example let globalIndexes = ['micro_testcheck', 'micro_testcheck2'];
let globalIndexes = [];

// Pass specific index names to force them to reindex, MUST BE DONE AFTER YOU GET DOCUMENT MISMATCH ERROR
// Example let forceIndex = ['micro_testcheck', 'micro_testcheck2'];
let forceIndex = [];

// Array for the indexes whose document number doesn't match
let documentNumber = 0;
let haltLoop = false;
let isThereAnyIssue = false;

// A pretty function to make console output look better
const logPretty = (message, variable) => {
    console.log(message);
    console.log("           ", variable);
    console.log(`----------------------------------------------------------------`);
};

// Fetching all content sources present in the database
const getIndexNames = (cb) => {
    let elasticIndexNames = [];
    if (Array.isArray(globalIndexes) && globalIndexes.length > 0) {
        cb(globalIndexes);
    } else if (Array.isArray(forceIndex) && forceIndex.length > 0) {
        cb(forceIndex);
    } else {
        let sql = "SELECT elasticIndexName FROM `content_sources`";
        connection.query(sql, (err, sources) => {
            if (err) {
                logPretty("Error in fetching doc", JSON.stringify(err, null, 4));
            }
            if (sources && sources.length > 0) {
                for (const x of sources) {
                    elasticIndexNames.push(x.elasticIndexName);
                }
            }
            cb(elasticIndexNames);
        });
    }
};

// For deleting the temporary created index
const deleteIndex = (eI, cb) => {
    if(haltLoop === false || forceIndex.length > 0) {
        request({
            method: "DELETE",
            url: "http://" + config.get("elasticIndexCS.host") + ":" + config.get("elasticIndexCS.port") + "/" + eI + "/",
            headers: {
                'content-type': 'application/json'
            },
        }, (error, response, body) => {
            if (error) {
                logPretty("Unable to remove index", eI);
            } else {
                logPretty("Removed index", eI);
            }
            cb(error, body);
        });    
    } else {
        cb();
    }
};

// For getting total document count
const countDocuments = (eI, addTotal = false, check, cb) => {
    request({
        method: "GET",
        url: "http://" + config.get("elasticIndexCS.host") + ":" + config.get("elasticIndexCS.port") + "/" + eI + "/_count",
        headers: {
            "content-type": "application/json"
        },
        json: true
    }, (error, response, body) => {
        if (error) {
            logPretty(`Unable to get number of documents in `, eI);
        } else {
            if (addTotal) {
                totalDocuments += body.count ? parseInt(body.count) : 0;
            }
            if (check === true && eI.endsWith("_special_temp")) {
                    if (
                        documentNumber !== body.count
                            ? parseInt(body.count)
                            : 0
                    ) {
                        if(forceIndex.length > 0) {
                            console.log(`!!! BYPASSING TOTAL DOCUMENTS MISMATCH ON THE SELETCED INDEX ${eI.substring(0, eI.length - 13)}!!!`);
                        } else {
                            haltLoop = true;
                            console.log(`!!! Issue with indexes, document count doesn't match after reindexing !!!`);
                            console.log(`!!! Source Index ${eI.substring(0, eI.length - 13)} has ${documentNumber} and temporary index ${eI} has ${body.count ? parseInt(body.count) : 0} !!!`);
                            console.log(`!!! Index not removed, please check !!!`);
                            isThereAnyIssue = true;
                        }
                    }
                } else {
                    documentNumber = body.count ? parseInt(body.count) : 0;
                    haltLoop = false;
                    logPretty(`Number of documents in ${eI} are`, body.count ? parseInt(body.count) : 0);
                }
        }
        cb(error, body);
    });
};

// For reindexing a particular index
const reIndex = (eI, reverse, cb) => {
    if (haltLoop === false || reverse === "false" || forceIndex.length > 0) {
        const destinationIndex = reverse === 'true' ? eI : eI + "_special_temp";
        const sourceIndex = reverse === 'true' ? eI + "_special_temp" : eI;
        countDocuments(sourceIndex, true, false, () => {
            console.log("Re-Indexing......")
            request({
                method: "POST",
                url: "http://" + config.get("elasticIndexCS.host") + ":" + config.get("elasticIndexCS.port") + "/_reindex?refresh=wait_for",
                headers: {
                    "content-type": "application/json"
                },
                body: {
                    source: {
                        index: sourceIndex
                    },
                    dest: {
                        index: destinationIndex
                    }
                },
                json: true
            }, (error, response, body) => {
                if (error) {
                    logPretty("Unable to reIndex for", destinationIndex);
                    cb(error, body);
                } else {
                    countDocuments(destinationIndex, false, true, () => {
                        if (reverse === 'true') {
                            deleteIndex(sourceIndex, (err) => {
                                cb(err, body);
                            });
                        } else {
                            cb(error, body);
                        }
                    });
                }
            });
        });
    } else {
        cb();
    }
};

// For closing a particular index
const closeIndex = (eI, cb) => {
    if (haltLoop === false || forceIndex.length > 0) {
        request({
            method: "POST",
            url: "http://" + config.get("elasticIndexCS.host") + ":" + config.get("elasticIndexCS.port") + "/" + eI + "/_close",
        }, (error, response, body) => {
            if (error) {
                logPretty("Unable to close index", eI);
            } else {
                logPretty("Closing index", eI);
            }
            cb(error, body);
        });
    } else {
        cb();
    }
};

// For opening a particular index
const openIndex = (eI, cb) => {
    if(haltLoop === false || forceIndex.length > 0) {
        request({
            method: "POST",
            url: "http://" + config.get("elasticIndexCS.host") + ":" + config.get("elasticIndexCS.port") + "/" + eI + "/_open",
        }, (error, response, body) => {
            if (error) {
                logPretty("Unable to open index", eI);
            } else {
                logPretty("Opening index", eI);
            }
            cb(error, body);
        });
    } else {
        cb();
    }
};

// This functions backs up the current settings and then update the configuration with new settings
const backupAndUpdateSettings = (eI, cb) => {
    if(haltLoop === false || forceIndex.length > 0) {
        request({
            method: "GET",
            url: "http://" + config.get("elasticIndexCS.host") + ":" + config.get("elasticIndexCS.port") + "/" + eI + "/_settings",
            headers: {
                "content-type": 'application/json'
            },
        }, (error, response, body) => {
            if (error) {
                logPretty("Unable to get settings for", eI);
            }
            if (body) {
                fs.writeFile(path.join(__dirname, "/tempBckup/" + eI + "-old-settings.json"), body, (err) => {
                    if (err) {
                        logPretty("Unable to add temp settings file added for", eI);
                    } else {
                        logPretty("Temp settings file added for", eI);
                    }
                    let settings = {
                        analysis: {
                            char_filter: {
                                special_mapping: {
                                    type: "mapping",
                                    mappings: config.get("charFilterMapping")
                                }
                            },
                            analyzer: {
                                custom_special_analyzer: {
                                    filter: [
                                        "lowercase",
                                        "my_stop",
                                        "my_custom_stop",
                                        "custom_english_stemmer",
                                        "asciifolding"
                                    ],
                                    char_filter: [
                                        "special_mapping"
                                    ],
                                    tokenizer: "standard"
                                }
                            }
                        }
                    };
                    request({
                        method: "PUT",
                        url: "http://" + config.get("elasticIndexCS.host") + ":" + config.get("elasticIndexCS.port") + "/" + eI + "/_settings",
                        headers: {
                            "content-type": 'application/json'
                        },
                        body: JSON.stringify(settings)
                    }, (error, response, body) => {
                        if (error) {
                            logPretty("Unable to update settings", eI);
                        } else {
                            logPretty("Updated settings for", eI);
                        }
                        cb(error, body);
                    });
                });
            } else {
                cb(error, body);
            }
        });
    } else {
        cb();
    }
};

// Setting the mappings for all objects one by one
const setMapping = (eI, objectName, objectValue, cb) => {
    request({
        method: "PUT",
        url: "http://" + config.get("elasticIndexCS.host") + ":" + config.get("elasticIndexCS.port") + "/" + eI + "/_mapping/" + objectName,
        headers: {
            "content-type": 'application/json'
        },
        body: JSON.stringify({ "properties": objectValue })
    }, (error, response, body) => {
        if (error) {
            logPretty("Unable to update mapping", error);
        } else {
            logPretty("Updated mappings for", eI);
        }
        cb(error, body);
    });
}

// This functions backs up the current mappings and then update the configuration with new mappings
const backupAndUpdateMappings = (eI, cb) => {
    if(haltLoop === false || forceIndex.length > 0) {
        request({
            method: "GET",
            url: "http://" + config.get("elasticIndexCS.host") + ":" + config.get("elasticIndexCS.port") + "/" + eI + "/_mappings",
            headers: {
                "content-type": 'application/json'
            },
        }, (error, response, body) => {
            if (error) {
                logPretty("Unable to get mappings for", eI);
            }
            if (body) {
                fs.writeFile(path.join(__dirname, "/tempBckup/" + eI + "-old-mappings.json"), body, (err) => {
                    if (err) {
                        logPretty("Unable to add temp mapping file added for", eI);
                    } else {
                        logPretty("Temp mapping file added for", eI);
                    }
                    let specialFields = {};
                    body = JSON.parse(body);
                    const sourceType = body[eI] && body[eI].mappings ? Object.keys(body[eI].mappings) : [];
                    if (sourceType.length > 0) {
                        for (const x of sourceType) {
                            specialFields[x] = {};
                            let copyFields = Object.keys(body[eI].mappings[x].properties).filter((x) => x.includes("_copy"));
                            let specialFieldsTmp = [];
                            if (copyFields && copyFields.length > 0) {
                                for (const c in copyFields) {
                                    let tempAnalyzerFields = {
                                        name: '',
                                        property: {}
                                    };
                                    let fieldName = copyFields[c].replace("_copy", "_special");
                                    tempAnalyzerFields.name = fieldName;
                                    tempAnalyzerFields.property = body[eI].mappings[x].properties[copyFields[c]];
                                    const changeAnalyzer = Object.keys(tempAnalyzerFields.property);
                                    for (const z in changeAnalyzer) {
                                        if (typeof tempAnalyzerFields.property[changeAnalyzer[z]] === "object") {
                                            const spField = Object.keys(tempAnalyzerFields.property[changeAnalyzer[z]]);
                                            for (const n in spField) {
                                                if (("analyzer" in tempAnalyzerFields.property[changeAnalyzer[z]][spField[n]])) {
                                                    tempAnalyzerFields.property[changeAnalyzer[z]][spField[n]].analyzer = "custom_special_analyzer";
                                                }
                                            }
                                        }
                                    }
                                    specialFieldsTmp.push(tempAnalyzerFields);
                                }
                                for (const y of specialFieldsTmp) {
                                    specialFields[x][y.name] = y.property;
                                }
                            }
                        }
                    }
                    let actionsPerIndexObject = [];
                    const totalObjects = Object.keys(specialFields);
                    for (const t in totalObjects) {
                        actionsPerIndexObject.push(setMapping.bind(null, eI, totalObjects[t], specialFields[totalObjects[t]]));
                    }
                    async.series(actionsPerIndexObject, (err) => {
                        if (err) {
                            logPretty("Unable to complete mapping ", JSON.stringify(err));
                        } else {
                            cb(err, body);
                        }
                    });
                });
            } else {
                cb(error, body);
            }
        });
    } else {
        cb();
    }
};

// Fetches the index and writes the configuration in a file. Just to match the new configuration
const getNewIndex = (eI, cb) => {
    if(haltLoop === false || forceIndex.length > 0) {
        request({
            method: "GET",
            url: "http://" + config.get("elasticIndexCS.host") + ":" + config.get("elasticIndexCS.port") + "/" + eI,
            headers: {
                "content-type": 'application/json'
            },
        }, (error, response, body) => {
            if (error) {
                logPretty("Unable to get new mapping", error);
            } else {
                fs.writeFile(path.join(__dirname, "/tempBckup/" + eI + "-new-index.json"), body, (err) => {
                    if (err) {
                        logPretty("Unable to add new index file added for", eI);
                    } else {
                        logPretty("New Index file added for", eI);
                    }
                });
            }
            cb(error, body);
        });
    } else {
        cb();
    }
};

// Add copy_to fields in mapping
const processConfig = (mappings, settings, eI) => {
    if (mappings) {
        const objects = Object.keys(mappings);
        if (objects && objects.length > 0) {
            for (const x in objects) {
                const properties = mappings[objects[x]].properties;
                let mainFields = Object.keys(properties).filter((x) => !x.includes("_copy") && !x.includes("special"));
                for (const y in mainFields) {
                    if (mappings[objects[x]].properties[mainFields[y]].copy_to && Array.isArray(mappings[objects[x]].properties[mainFields[y]].copy_to)) {
                        const copyField = mappings[objects[x]].properties[mainFields[y]].copy_to[0];
                        mappings[objects[x]].properties[mainFields[y]].copy_to.push(copyField.replace("_copy", "_special"));
                    }
                }
            }
        }
    }
    delete settings.index.uuid;
    delete settings.index.provided_name;
    delete settings.index.version;
    delete settings.index.creation_date;
    return { mappings, settings };
}

// For creating a new index
const createNewIndex = (eI, cb) => {
    if (haltLoop === false || forceIndex.length > 0) {
        const filePath = path.join(__dirname, './tempBckup/' + eI + '-new-index.json');
        fs.readFile(filePath, 'utf8', (error, data) => {
            if (error) {
                throw error;
            }
            data = JSON.parse(data);
            if (data[eI] && data[eI].mappings) {
                let newMappings = processConfig(data[eI].mappings, data[eI].settings, eI);
                request({
                    method: "PUT",
                    url: "http://" + config.get("elasticIndexCS.host") + ":" + config.get("elasticIndexCS.port") + "/" + eI,
                    headers: {
                        "content-type": 'application/json'
                    },
                    body: JSON.stringify({
                        settings: newMappings.settings,
                        mappings: newMappings.mappings
                    })
                }, (error, response, body) => {
                    if (error) {
                        logPretty("Unable to create index ", eI);
                    } else {
                        logPretty("Created new index ", eI);
                    }
                    cb(error, body);
                });
            } else {
                cb(error, {});
            }
        });
    } else {
        cb();
    }
}

// Completion message
const sendCompletionMessage = (sTime, eTime, tIndexes, totalDocuments) => {
    console.log("----------------------------------------------------------------");
    if(isThereAnyIssue) {
        console.log("Migration script compiled with exceptions, please check the reported issues");
    } else {
        console.log("Migration script compiled successfully");
    }
    console.log("Task started at ", sTime);
    console.log("Task ended at ", eTime);
    console.log(
        "Task time taken ",
        parseInt((Number(eTime) - Number(sTime)) / 1000),
        "seconds"
    );
    console.log("Total indexes processed ", tIndexes);
    console.log("Total documents processed ", totalDocuments, "documents");
    if(isThereAnyIssue) {
        console.log("PLEASE USE FORCED `forceIndex` ARRAY TO OVERWRITE THE REMAINING INDEXES OR PROCESS MANUALLY, YOU CAN NOW TERMINATE THIS PROCESS");
    } else {
        console.log("YOU CAN NOW TERMINATE THE PROCESS");
    }
    console.log("----------------------------------------------------------------");
};

// Main envoke function that binds all other methods
const startProcess = () => {
    sTime = new Date();
    if (!fs.existsSync(path.join(__dirname, "/tempBckup"))) {
        fs.mkdirSync(path.join(__dirname, "/tempBckup"));
    }
    getIndexNames((elasticIndexes) => {
        let actionsPerIndex = [];
        if (Array.isArray(elasticIndexes) && elasticIndexes.length > 0) {
            console.log(`########## Special Character Script Logs ##########`);
            for (const indexName of elasticIndexes) {
                actionsPerIndex.push(reIndex.bind(null, indexName, "false"));
                actionsPerIndex.push(closeIndex.bind(null, indexName));
                actionsPerIndex.push(backupAndUpdateSettings.bind(null, indexName));
                actionsPerIndex.push(backupAndUpdateMappings.bind(null, indexName));
                actionsPerIndex.push(getNewIndex.bind(null, indexName));
                actionsPerIndex.push(openIndex.bind(null, indexName));
                actionsPerIndex.push(deleteIndex.bind(null, indexName));
                actionsPerIndex.push(createNewIndex.bind(null, indexName));
                actionsPerIndex.push(reIndex.bind(null, indexName, "true"));
            }
            async.series(actionsPerIndex, (err) => {
                if (err) {
                    logPretty("########## Unable to complete process ", JSON.stringify(err));
                } else {
                    eTime = new Date();
                    sendCompletionMessage(sTime, eTime, elasticIndexes.length, totalDocuments);
                }
            });
        } else {
            logPretty("########## No matching content source found", '0 records');
        }
    });
};

// Initialization
startProcess();

// Error handling for unhandled rejections
process.on("unhandledRejection", (err) => {
    console.error("########## Unhandled rejection", JSON.stringify(err));
    process.exit(1);
});
