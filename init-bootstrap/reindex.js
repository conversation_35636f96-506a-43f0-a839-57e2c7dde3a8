var request = require("request");
var fs = require("fs");
var indexes = ['']; 
const elasticPort = "8045";
const host = "elastic-5.6.16";

var settings = {
  "analysis": {
    "filter": {
      "custom_english_stemmer": {
        "name": "english",
        "type": "stemmer"
      },
      "synonym": {
        "type": "synonym_graph",
        "synonyms_path": "synonyms.txt"
      },
      "ja_stop": {
        "type": "ja_stop",
        "stopwords": [
          "_japanese_",
          "ストップ"
        ]
      },
      "arabic_stop": {
        "type": "stop",
        "stopwords": "_arabic_"
      },
      "my_stop": {
        "type": "stop",
        "stopwords": "_english_"
      },
      "my_custom_stop": {
        "type": "stop",
        "stopwords_path": "stopwords.txt"
      },
      "armenian_stop": {
        "type": "stop",
        "stopwords": "_armenian_"
      },
      "armenian_keywords": {
        "type": "keyword_marker",
        "keywords": ["օրինակ"]
      },
      "armenian_stemmer": {
        "type": "stemmer",
        "language": "armenian"
      },
      "basque_stop": {
        "type": "stop",
        "stopwords": "_basque_"
      },
      "basque_keywords": {
        "type": "keyword_marker",
        "keywords": ["Adibidez"]
      },
      "basque_stemmer": {
        "type": "stemmer",
        "language": "basque"
      },
      "brazilian_stop": {
        "type": "stop",
        "stopwords": "_brazilian_"
      },
      "brazilian_keywords": {
        "type": "keyword_marker",
        "keywords": ["exemplo"]
      },
      "brazilian_stemmer": {
        "type": "stemmer",
        "language": "brazilian"
      },
      "bulgarian_stop": {
        "type": "stop",
        "stopwords": "_bulgarian_"
      },
      "bulgarian_keywords": {
        "type": "keyword_marker",
        "keywords": ["пример"]
      },
      "bulgarian_stemmer": {
        "type": "stemmer",
        "language": "bulgarian"
      },
      "catalan_elision": {
        "type": "elision",
        "articles": ["d", "l", "m", "n", "s", "t"],
        "articles_case": true
      },
      "catalan_stop": {
        "type": "stop",
        "stopwords": "_catalan_"
      },
      "catalan_keywords": {
        "type": "keyword_marker",
        "keywords": ["exemple"]
      },
      "catalan_stemmer": {
        "type": "stemmer",
        "language": "catalan"
      },
      "danish_stop": {
        "type": "stop",
        "stopwords": "_danish_"
      },
      "danish_keywords": {
        "type": "keyword_marker",
        "keywords": ["eksempel"]
      },
      "danish_stemmer": {
        "type": "stemmer",
        "language": "danish"
      },
      "dutch_stop": {
        "type": "stop",
        "stopwords": "_dutch_"
      },
      "dutch_keywords": {
        "type": "keyword_marker",
        "keywords": ["voorbeeld"]
      },
      "dutch_stemmer": {
        "type": "stemmer",
        "language": "dutch"
      },
      "finnish_stop": {
        "type": "stop",
        "stopwords": "_finnish_"
      },
      "finnish_keywords": {
        "type": "keyword_marker",
        "keywords": ["esimerkki"]
      },
      "finnish_stemmer": {
        "type": "stemmer",
        "language": "finnish"
      },
      "french_elision": {
        "type": "elision",
        "articles_case": true,
        "articles": [
          "l", "m", "t", "qu", "n", "s",
          "j", "d", "c", "jusqu", "quoiqu",
          "lorsqu", "puisqu"
        ]
      },
      "french_stop": {
        "type": "stop",
        "stopwords": "_french_"
      },
      "french_keywords": {
        "type": "keyword_marker",
        "keywords": ["Exemple"]
      },
      "french_stemmer": {
        "type": "stemmer",
        "language": "light_french"
      },
      "galician_stop": {
        "type": "stop",
        "stopwords": "_galician_"
      },
      "galician_keywords": {
        "type": "keyword_marker",
        "keywords": ["exemplo"]
      },
      "galician_stemmer": {
        "type": "stemmer",
        "language": "galician"
      },
      "german_stop": {
        "type": "stop",
        "stopwords": "_german_"
      },
      "german_keywords": {
        "type": "keyword_marker",
        "keywords": ["Beispiel"]
      },
      "german_stemmer": {
        "type": "stemmer",
        "language": "light_german"
      },
      "greek_stop": {
        "type": "stop",
        "stopwords": "_greek_"
      },
      "greek_lowercase": {
        "type": "lowercase",
        "language": "greek"
      },
      "greek_keywords": {
        "type": "keyword_marker",
        "keywords": ["παράδειγμα"]
      },
      "greek_stemmer": {
        "type": "stemmer",
        "language": "greek"
      },
      "hindi_stop": {
        "type": "stop",
        "stopwords": "_hindi_"
      },
      "hindi_keywords": {
        "type": "keyword_marker",
        "keywords": ["उदाहरण"]
      },
      "hindi_stemmer": {
        "type": "stemmer",
        "language": "hindi"
      },
      "hungarian_stop": {
        "type": "stop",
        "stopwords": "_hungarian_"
      },
      "hungarian_keywords": {
        "type": "keyword_marker",
        "keywords": ["példa"]
      },
      "hungarian_stemmer": {
        "type": "stemmer",
        "language": "hungarian"
      },
      "indonesian_stop": {
        "type": "stop",
        "stopwords": "_indonesian_"
      },
      "indonesian_keywords": {
        "type": "keyword_marker",
        "keywords": ["contoh"]
      },
      "indonesian_stemmer": {
        "type": "stemmer",
        "language": "indonesian"
      },
      "irish_elision": {
        "type": "elision",
        "articles": ["d", "m", "b"],
        "articles_case": true
      },
      "irish_stop": {
        "type": "stop",
        "stopwords": "_irish_"
      },
      "irish_lowercase": {
        "type": "lowercase",
        "language": "irish"
      },
      "irish_keywords": {
        "type": "keyword_marker",
        "keywords": ["sampla"]
      },
      "irish_stemmer": {
        "type": "stemmer",
        "language": "irish"
      },
      "italian_elision": {
        "type": "elision",
        "articles": [
          "c", "l", "all", "dall", "dell",
          "nell", "sull", "coll", "pell",
          "gl", "agl", "dagl", "degl", "negl",
          "sugl", "un", "m", "t", "s", "v", "d"
        ],
        "articles_case": true
      },
      "italian_stop": {
        "type": "stop",
        "stopwords": "_italian_"
      },
      "italian_keywords": {
        "type": "keyword_marker",
        "keywords": ["esempio"]
      },
      "italian_stemmer": {
        "type": "stemmer",
        "language": "light_italian"
      },
      "norwegian_stop": {
        "type": "stop",
        "stopwords": "_norwegian_"
      },
      "norwegian_keywords": {
        "type": "keyword_marker",
        "keywords": ["eksempel"]
      },
      "norwegian_stemmer": {
        "type": "stemmer",
        "language": "norwegian"
      },
      "portuguese_stop": {
        "type": "stop",
        "stopwords": "_portuguese_"
      },
      "portuguese_keywords": {
        "type": "keyword_marker",
        "keywords": ["exemplo"]
      },
      "portuguese_stemmer": {
        "type": "stemmer",
        "language": "light_portuguese"
      },
      "romanian_stop": {
        "type": "stop",
        "stopwords": "_romanian_"
      },
      "romanian_keywords": {
        "type": "keyword_marker",
        "keywords": ["exemplu"]
      },
      "romanian_stemmer": {
        "type": "stemmer",
        "language": "romanian"
      },
      "russian_stop": {
        "type": "stop",
        "stopwords": "_russian_"
      },
      "russian_keywords": {
        "type": "keyword_marker",
        "keywords": ["пример"]
      },
      "russian_stemmer": {
        "type": "stemmer",
        "language": "russian"
      },
      "sorani_stop": {
        "type": "stop",
        "stopwords": "_sorani_"
      },
      "sorani_keywords": {
        "type": "keyword_marker",
        "keywords": ["mînak"]
      },
      "sorani_stemmer": {
        "type": "stemmer",
        "language": "sorani"
      },
      "spanish_stop": {
        "type": "stop",
        "stopwords": "_spanish_"
      },
      "spanish_keywords": {
        "type": "keyword_marker",
        "keywords": ["ejemplo"]
      },
      "spanish_stemmer": {
        "type": "stemmer",
        "language": "light_spanish"
      },
      "swedish_stop": {
        "type": "stop",
        "stopwords": "_swedish_"
      },
      "swedish_keywords": {
        "type": "keyword_marker",
        "keywords": ["exempel"]
      },
      "swedish_stemmer": {
        "type": "stemmer",
        "language": "swedish"
      },
      "turkish_stop": {
        "type": "stop",
        "stopwords": "_turkish_"
      },
      "turkish_lowercase": {
        "type": "lowercase",
        "language": "turkish"
      },
      "turkish_keywords": {
        "type": "keyword_marker",
        "keywords": ["örnek"]
      },
      "turkish_stemmer": {
        "type": "stemmer",
        "language": "turkish"
      },
      "thai_stop": {
        "type": "stop",
        "stopwords": "_thai_"
      },
      "czech_stop": {
        "type": "stop",
        "stopwords": "_czech_"
      },
      "czech_keywords": {
        "type": "keyword_marker",
        "keywords": ["příklad"]
      },
      "czech_stemmer": {
        "type": "stemmer",
        "language": "czech"
      },
      "my_katakana_stemmer": {
        "type": "kuromoji_stemmer",
        "minimum_length": "4"
      },
      "arabic_stemmer": {
        "type": "stemmer",
        "language": "arabic"
      },
      "ja_stop": {
        "type": "ja_stop",
        "stopwords": [
          "_japanese_",
          "ストップ"
        ]
      },
      "arabic_stop": {
        "type": "stop",
        "stopwords": "_arabic_"
      },
      "my_stop": {
        "type": "stop",
        "stopwords": "_english_"
      },
      "my_custom_stop": {
        "type": "stop",
        "stopwords_path": "stopwords.txt"
      },
      "armenian_stop": {
        "type": "stop",
        "stopwords": "_armenian_"
      },
      "armenian_keywords": {
        "type": "keyword_marker",
        "keywords": ["օրինակ"]
      },
      "armenian_stemmer": {
        "type": "stemmer",
        "language": "armenian"
      },
      "basque_stop": {
        "type": "stop",
        "stopwords": "_basque_"
      },
      "basque_keywords": {
        "type": "keyword_marker",
        "keywords": ["Adibidez"]
      },
      "basque_stemmer": {
        "type": "stemmer",
        "language": "basque"
      },
      "brazilian_stop": {
        "type": "stop",
        "stopwords": "_brazilian_"
      },
      "brazilian_keywords": {
        "type": "keyword_marker",
        "keywords": ["exemplo"]
      },
      "brazilian_stemmer": {
        "type": "stemmer",
        "language": "brazilian"
      },
      "bulgarian_stop": {
        "type": "stop",
        "stopwords": "_bulgarian_"
      },
      "bulgarian_keywords": {
        "type": "keyword_marker",
        "keywords": ["пример"]
      },
      "bulgarian_stemmer": {
        "type": "stemmer",
        "language": "bulgarian"
      },
      "catalan_elision": {
        "type": "elision",
        "articles": ["d", "l", "m", "n", "s", "t"],
        "articles_case": true
      },
      "catalan_stop": {
        "type": "stop",
        "stopwords": "_catalan_"
      },
      "catalan_keywords": {
        "type": "keyword_marker",
        "keywords": ["exemple"]
      },
      "catalan_stemmer": {
        "type": "stemmer",
        "language": "catalan"
      },
      "danish_stop": {
        "type": "stop",
        "stopwords": "_danish_"
      },
      "danish_keywords": {
        "type": "keyword_marker",
        "keywords": ["eksempel"]
      },
      "danish_stemmer": {
        "type": "stemmer",
        "language": "danish"
      },
      "dutch_stop": {
        "type": "stop",
        "stopwords": "_dutch_"
      },
      "dutch_keywords": {
        "type": "keyword_marker",
        "keywords": ["voorbeeld"]
      },
      "dutch_stemmer": {
        "type": "stemmer",
        "language": "dutch"
      },
      "finnish_stop": {
        "type": "stop",
        "stopwords": "_finnish_"
      },
      "finnish_keywords": {
        "type": "keyword_marker",
        "keywords": ["esimerkki"]
      },
      "finnish_stemmer": {
        "type": "stemmer",
        "language": "finnish"
      },
      "french_elision": {
        "type": "elision",
        "articles_case": true,
        "articles": [
          "l", "m", "t", "qu", "n", "s",
          "j", "d", "c", "jusqu", "quoiqu",
          "lorsqu", "puisqu"
        ]
      },
      "french_stop": {
        "type": "stop",
        "stopwords": "_french_"
      },
      "french_keywords": {
        "type": "keyword_marker",
        "keywords": ["Exemple"]
      },
      "french_stemmer": {
        "type": "stemmer",
        "language": "light_french"
      },
      "galician_stop": {
        "type": "stop",
        "stopwords": "_galician_"
      },
      "galician_keywords": {
        "type": "keyword_marker",
        "keywords": ["exemplo"]
      },
      "galician_stemmer": {
        "type": "stemmer",
        "language": "galician"
      },
      "german_stop": {
        "type": "stop",
        "stopwords": "_german_"
      },
      "german_keywords": {
        "type": "keyword_marker",
        "keywords": ["Beispiel"]
      },
      "german_stemmer": {
        "type": "stemmer",
        "language": "light_german"
      },
      "greek_stop": {
        "type": "stop",
        "stopwords": "_greek_"
      },
      "greek_lowercase": {
        "type": "lowercase",
        "language": "greek"
      },
      "greek_keywords": {
        "type": "keyword_marker",
        "keywords": ["παράδειγμα"]
      },
      "greek_stemmer": {
        "type": "stemmer",
        "language": "greek"
      },
      "hindi_stop": {
        "type": "stop",
        "stopwords": "_hindi_"
      },
      "hindi_keywords": {
        "type": "keyword_marker",
        "keywords": ["उदाहरण"]
      },
      "hindi_stemmer": {
        "type": "stemmer",
        "language": "hindi"
      },
      "hungarian_stop": {
        "type": "stop",
        "stopwords": "_hungarian_"
      },
      "hungarian_keywords": {
        "type": "keyword_marker",
        "keywords": ["példa"]
      },
      "hungarian_stemmer": {
        "type": "stemmer",
        "language": "hungarian"
      },
      "indonesian_stop": {
        "type": "stop",
        "stopwords": "_indonesian_"
      },
      "indonesian_keywords": {
        "type": "keyword_marker",
        "keywords": ["contoh"]
      },
      "indonesian_stemmer": {
        "type": "stemmer",
        "language": "indonesian"
      },
      "irish_elision": {
        "type": "elision",
        "articles": ["d", "m", "b"],
        "articles_case": true
      },
      "irish_stop": {
        "type": "stop",
        "stopwords": "_irish_"
      },
      "irish_lowercase": {
        "type": "lowercase",
        "language": "irish"
      },
      "irish_keywords": {
        "type": "keyword_marker",
        "keywords": ["sampla"]
      },
      "irish_stemmer": {
        "type": "stemmer",
        "language": "irish"
      },
      "italian_elision": {
        "type": "elision",
        "articles": [
          "c", "l", "all", "dall", "dell",
          "nell", "sull", "coll", "pell",
          "gl", "agl", "dagl", "degl", "negl",
          "sugl", "un", "m", "t", "s", "v", "d"
        ],
        "articles_case": true
      },
      "italian_stop": {
        "type": "stop",
        "stopwords": "_italian_"
      },
      "italian_keywords": {
        "type": "keyword_marker",
        "keywords": ["esempio"]
      },
      "italian_stemmer": {
        "type": "stemmer",
        "language": "light_italian"
      },
      "norwegian_stop": {
        "type": "stop",
        "stopwords": "_norwegian_"
      },
      "norwegian_keywords": {
        "type": "keyword_marker",
        "keywords": ["eksempel"]
      },
      "norwegian_stemmer": {
        "type": "stemmer",
        "language": "norwegian"
      },
      "portuguese_stop": {
        "type": "stop",
        "stopwords": "_portuguese_"
      },
      "portuguese_keywords": {
        "type": "keyword_marker",
        "keywords": ["exemplo"]
      },
      "portuguese_stemmer": {
        "type": "stemmer",
        "language": "light_portuguese"
      },
      "romanian_stop": {
        "type": "stop",
        "stopwords": "_romanian_"
      },
      "romanian_keywords": {
        "type": "keyword_marker",
        "keywords": ["exemplu"]
      },
      "romanian_stemmer": {
        "type": "stemmer",
        "language": "romanian"
      },
      "russian_stop": {
        "type": "stop",
        "stopwords": "_russian_"
      },
      "russian_keywords": {
        "type": "keyword_marker",
        "keywords": ["пример"]
      },
      "russian_stemmer": {
        "type": "stemmer",
        "language": "russian"
      },
      "sorani_stop": {
        "type": "stop",
        "stopwords": "_sorani_"
      },
      "sorani_keywords": {
        "type": "keyword_marker",
        "keywords": ["mînak"]
      },
      "sorani_stemmer": {
        "type": "stemmer",
        "language": "sorani"
      },
      "spanish_stop": {
        "type": "stop",
        "stopwords": "_spanish_"
      },
      "spanish_keywords": {
        "type": "keyword_marker",
        "keywords": ["ejemplo"]
      },
      "spanish_stemmer": {
        "type": "stemmer",
        "language": "light_spanish"
      },
      "swedish_stop": {
        "type": "stop",
        "stopwords": "_swedish_"
      },
      "swedish_keywords": {
        "type": "keyword_marker",
        "keywords": ["exempel"]
      },
      "swedish_stemmer": {
        "type": "stemmer",
        "language": "swedish"
      },
      "turkish_stop": {
        "type": "stop",
        "stopwords": "_turkish_"
      },
      "turkish_lowercase": {
        "type": "lowercase",
        "language": "turkish"
      },
      "turkish_keywords": {
        "type": "keyword_marker",
        "keywords": ["örnek"]
      },
      "turkish_stemmer": {
        "type": "stemmer",
        "language": "turkish"
      },
      "thai_stop": {
        "type": "stop",
        "stopwords": "_thai_"
      },
      "czech_stop": {
        "type": "stop",
        "stopwords": "_czech_"
      },
      "czech_keywords": {
        "type": "keyword_marker",
        "keywords": ["příklad"]
      },
      "czech_stemmer": {
        "type": "stemmer",
        "language": "czech"
      },
      "my_katakana_stemmer": {
        "type": "kuromoji_stemmer",
        "minimum_length": "4"
      },
      "arabic_stemmer": {
        "type": "stemmer",
        "language": "arabic"
      },
      "arabic_keywords": {
        "type": "keyword_marker",
        "keywords": ["مثال"]
      }
    },
    "analyzer": {
      "custom_lowercase_stemmed": {
        "filter": [
          "lowercase",
          "my_stop",
          "my_custom_stop",
          "custom_english_stemmer"
        ],
        "tokenizer": "standard"
      },
      "custom_lowercase_synonym": {
        "filter": [
          "lowercase",
          "my_stop",
          "my_custom_stop",
          "synonym",
          "custom_english_stemmer"
        ],
        "tokenizer": "standard"
      },
      "custom_ja_analyzer": {
        "filter": [
          "lowercase",
          "ja_stop",
          "my_custom_stop",
          "synonym",
          "my_katakana_stemmer"
        ],
        "type": "custom",
        "tokenizer": "kuromoji_user_dict"
      },
      "custom_ar_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "arabic_stop",
          "my_custom_stop",
          "synonym",
          "arabic_stemmer"
        ],
        "tokenizer": "standard"
      },
      "custom_hy_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "armenian_stop",
          "my_custom_stop",
          "armenian_keywords",
          "synonym",
          "armenian_stemmer"
        ]
      },
      "custom_eu_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "basque_stop",
          "my_custom_stop",
          "basque_keywords",
          "synonym",
          "basque_stemmer",
        ]
      },
      "custom_pt-br_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "brazilian_stop",
          "my_custom_stop",
          "brazilian_keywords",
          "synonym",
          "brazilian_stemmer",

        ]
      },
      "custom_bg_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "bulgarian_stop",
          "my_custom_stop",
          "bulgarian_keywords",
          "synonym",
          "bulgarian_stemmer"
        ]
      },
      "custom_ca_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "catalan_elision",
          "lowercase",
          "catalan_stop",
          "my_custom_stop",
          "catalan_keywords",
          "synonym",
          "catalan_stemmer",
        ]
      },
      "custom_da_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "danish_stop",
          "my_custom_stop",
          "danish_keywords",
          "synonym",
          "danish_stemmer",
        ]
      },
      "custom_nl_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "dutch_stop",
          "my_custom_stop",
          "dutch_keywords",
          "synonym",
          "dutch_stemmer",
        ]
      },
      "custom_fi_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "finnish_stop",
          "my_custom_stop",
          "finnish_keywords",
          "synonym",
          "finnish_stemmer",
        ]
      },
      "custom_fr_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "french_elision",
          "lowercase",
          "french_stop",
          "my_custom_stop",
          "french_keywords",
          "synonym",
          "french_stemmer",
        ]
      },
      "custom_gl_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "galician_stop",
          "my_custom_stop",
          "galician_keywords",
          "synonym",
          "galician_stemmer",
        ]
      },
      "custom_de_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "german_stop",
          "my_custom_stop",
          "german_keywords",
          "german_normalization",
          "synonym",
          "german_stemmer"
        ]
      },
      "custom_el_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "greek_lowercase",
          "greek_stop",
          "my_custom_stop",
          "greek_keywords",
          "synonym",
          "greek_stemmer"
        ]
      },
      "custom_hi_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "hindi_keywords",
          "hindi_stop",
          "my_custom_stop",
          "synonym",
          "hindi_stemmer"
        ]
      },
      "custom_hu_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "hungarian_stop",
          "my_custom_stop",
          "hungarian_keywords",
          "synonym",
          "hungarian_stemmer"
        ]
      },
      "custom_id_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "indonesian_stop",
          "my_custom_stop",
          "indonesian_keywords",
          "synonym",
          "indonesian_stemmer"
        ]
      },
      "custom_ga_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "irish_elision",
          "irish_lowercase",
          "irish_stop",
          "my_custom_stop",
          "irish_keywords",
          "synonym",
          "irish_stemmer"
        ]
      },
      "custom_it_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "italian_elision",
          "lowercase",
          "italian_stop",
          "my_custom_stop",
          "italian_keywords",
          "synonym",
          "italian_stemmer"
        ]
      },
      "custom_nn_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "norwegian_stop",
          "my_custom_stop",
          "norwegian_keywords",
          "synonym",
          "norwegian_stemmer"
        ]
      },
      "custom_pt_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "portuguese_stop",
          "my_custom_stop",
          "portuguese_keywords",
          "synonym",
          "portuguese_stemmer"
        ]
      },
      "custom_ro_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "romanian_stop",
          "my_custom_stop",
          "romanian_keywords",
          "synonym",
          "romanian_stemmer"
        ]
      },
      "custom_ru_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "russian_stop",
          "my_custom_stop",
          "russian_keywords",
          "russian_stemmer",
          "synonym"
        ]
      },
      "custom_ku_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "sorani_stop",
          "my_custom_stop",
          "sorani_keywords",
          "sorani_stemmer",
          "synonym"
        ]
      },
      "custom_es_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "spanish_stop",
          "my_custom_stop",
          "spanish_keywords",
          "synonym",
          "spanish_stemmer"
        ]
      },
      "custom_sv_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "swedish_stop",
          "my_custom_stop",
          "swedish_keywords",
          "synonym",
          "swedish_stemmer"
        ]
      },
      "custom_tr_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "apostrophe",
          "turkish_lowercase",
          "turkish_stop",
          "my_custom_stop",
          "turkish_keywords",
          "synonym",
          "turkish_stemmer"
        ]
      },
      "custom_th_analyzer": {
        "tokenizer": "thai",
        "filter": [
          "lowercase",
          "decimal_digit",
          "thai_stop",
          "my_custom_stop",
          "synonym"
        ]
      },
      "custom_cs_analyzer": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "czech_stop",
          "my_custom_stop",
          "czech_keywords",
          "synonym",
          "czech_stemmer"
        ]
      },
      "custom_ja_analyzer_stemmed": {
        "filter": [
          "lowercase",
          "ja_stop",
          "my_custom_stop",
          "my_katakana_stemmer"
        ],
        "type": "custom",
        "tokenizer": "kuromoji_user_dict"
      },
      "custom_ar_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "arabic_stop",
          "my_custom_stop",
          "arabic_keywords",
          "arabic_stemmer"
        ]
      },
      "custom_hy_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "armenian_stop",
          "my_custom_stop",
          "armenian_keywords",
          "armenian_stemmer"
        ]
      },
      "custom_eu_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "basque_stop",
          "my_custom_stop",
          "basque_keywords",
          "basque_stemmer"
        ]
      },
      "custom_pt-br_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "brazilian_stop",
          "my_custom_stop",
          "brazilian_keywords",
          "brazilian_stemmer"
        ]
      },
      "custom_bg_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "bulgarian_stop",
          "my_custom_stop",
          "bulgarian_keywords",
          "bulgarian_stemmer"
        ]
      },
      "custom_ca_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "catalan_elision",
          "lowercase",
          "catalan_stop",
          "my_custom_stop",
          "catalan_keywords",
          "catalan_stemmer"
        ]
      },
      "custom_da_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "danish_stop",
          "my_custom_stop",
          "danish_keywords",
          "danish_stemmer"
        ]
      },
      "custom_nl_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "dutch_stop",
          "my_custom_stop",
          "dutch_keywords",
          "dutch_stemmer"
        ]
      },
      "custom_fi_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "finnish_stop",
          "my_custom_stop",
          "finnish_keywords",
          "finnish_stemmer"
        ]
      },
      "custom_fr_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "french_elision",
          "lowercase",
          "french_stop",
          "my_custom_stop",
          "french_keywords",
          "french_stemmer"
        ]
      },
      "custom_gl_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "galician_stop",
          "my_custom_stop",
          "galician_keywords",
          "galician_stemmer"
        ]
      },
      "custom_de_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "german_stop",
          "my_custom_stop",
          "german_keywords",
          "german_normalization",
          "german_stemmer"
        ]
      },
      "custom_el_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "greek_lowercase",
          "greek_stop",
          "my_custom_stop",
          "greek_keywords",
          "greek_stemmer"
        ]
      },
      "custom_hi_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "hindi_keywords",
          "hindi_stop",
          "my_custom_stop",
          "hindi_stemmer"
        ]
      },
      "custom_hu_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "hungarian_stop",
          "my_custom_stop",
          "hungarian_keywords",
          "hungarian_stemmer"
        ]
      },
      "custom_id_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "indonesian_stop",
          "my_custom_stop",
          "indonesian_keywords",
          "indonesian_stemmer"
        ]
      },
      "custom_ga_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "irish_elision",
          "irish_lowercase",
          "irish_stop",
          "my_custom_stop",
          "irish_keywords",
          "irish_stemmer"
        ]
      },
      "custom_it_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "italian_elision",
          "lowercase",
          "italian_stop",
          "my_custom_stop",
          "italian_keywords",
          "italian_stemmer"
        ]
      },
      "custom_nn_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "norwegian_stop",
          "my_custom_stop",
          "norwegian_keywords",
          "norwegian_stemmer"
        ]
      },
      "custom_pt_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "portuguese_stop",
          "my_custom_stop",
          "portuguese_keywords",
          "portuguese_stemmer"
        ]
      },
      "custom_ro_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "romanian_stop",
          "my_custom_stop",
          "romanian_keywords",
          "romanian_stemmer"
        ]
      },
      "custom_ru_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "russian_stop",
          "my_custom_stop",
          "russian_keywords",
          "russian_stemmer"
        ]
      },
      "custom_ku_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "sorani_stop",
          "my_custom_stop",
          "sorani_keywords",
          "sorani_stemmer"
        ]
      },
      "custom_es_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "spanish_stop",
          "my_custom_stop",
          "spanish_keywords",
          "spanish_stemmer"
        ]
      },
      "custom_sv_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "swedish_stop",
          "my_custom_stop",
          "swedish_keywords",
          "swedish_stemmer"
        ]
      },
      "custom_tr_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "apostrophe",
          "turkish_lowercase",
          "turkish_stop",
          "my_custom_stop",
          "turkish_keywords",
          "turkish_stemmer"
        ]
      },
      "custom_th_analyzer_stemmed": {
        "tokenizer": "thai",
        "filter": [
          "lowercase",
          "decimal_digit",
          "thai_stop",
          "my_custom_stop"
        ]
      },
      "custom_cs_analyzer_stemmed": {
        "tokenizer": "standard",
        "filter": [
          "lowercase",
          "czech_stop",
          "my_custom_stop",
          "czech_keywords",
          "czech_stemmer"
        ]
      }
    },
    "tokenizer": {
      "kuromoji_user_dict": {
        "mode": "search",
        "type": "kuromoji_tokenizer",
        "discard_punctuation": "false"
      }
    }
  }
}



var mappingFile = fs.createWriteStream("reindex_mapping.bat");
mappingFile.write(`set -e\n`);
indexes.forEach(function (index) {
  var options = {
    url: `http://${host}:${elasticPort}/${index}`,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
    json: true
  };

  request(options, function (err, docs) {
    if (err) {
      console.error("Error elastic", err);
    } else {
      var newMapping = {};
      newMapping = docs.body[index];
      delete newMapping.aliases;
      for (var key in newMapping.mappings) {
        for (var j in newMapping.mappings[key].properties) {
          if (newMapping.mappings[key].properties[j].hasOwnProperty("analyzer")) {
            delete newMapping.mappings[key].properties[j].analyzer;
            delete newMapping.mappings[key].properties[j].search_analyzer;
            newMapping.mappings[key].properties[j]["fields"] = {
              "en": {
                "type": "text",
                "analyzer": "custom_lowercase_stemmed",
                "search_analyzer": "custom_lowercase_synonym"
              },
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          } else if (j.split("_").indexOf("copy") >= 0) {
            newMapping.mappings[key].properties[j]["fields"] = {
              "en": {
                "type": "text",
                "analyzer": "custom_lowercase_stemmed"
              },
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          }

        }

      }
      newMapping.settings = settings;
      mappingFile.write(`curl -XPUT ${host}:${elasticPort}/${index}_update -d'${JSON.stringify(newMapping)}'\n`)
      mappingFile.write(`curl -XPOST ${host}:${elasticPort}/_reindex -d'{"source":{"index":"${index}"},"dest":{"index":"${index}_update"}}'\n`)
      mappingFile.write(`curl -XDELETE  ${host}:${elasticPort}/${index}\n`);
      mappingFile.write(`curl -XPUT ${host}:${elasticPort}/${index} -d'${JSON.stringify(newMapping)}'\n`)
      mappingFile.write(`curl -XPOST ${host}:${elasticPort}/_reindex -d'{"source":{"index":"${index}_update"},"dest":{"index":"${index}"}}'\n`)
      mappingFile.write(`curl -XDELETE  ${host}:${elasticPort}/${index}_update\n`);
    }
  })
})
