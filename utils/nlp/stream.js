var fs = require("fs");
var through = require('through');
var split = require('split');
var commonFunctions = require('../commonFunctions');

const processStream = async (inputStream, outputPath, logMessage) => {
  try {
    await new Promise((resolve, reject) => {
      const writeStream = fs.createWriteStream(outputPath);

      inputStream
        .pipe(split())
        .pipe(
          through(function (buff) {
            this.queue(buff.toString().toLowerCase() + "\n");
          })
        )
        .pipe(writeStream)
        .on("finish", () => {
          commonFunctions.errorlogger.info(logMessage);
          inputStream.destroy();
          resolve();
        })
        .on("error", (err) => {
          commonFunctions.errorlogger.error(`Error processing ${logMessage}:`, err);
          reject(err);
        });
    });
  } catch (error) {
    commonFunctions.errorlogger.error(`Caught error in processStream: ${logMessage}`, error);
    throw error;
  }
};


module.exports = {
  processStream,
}