var express = require('express');
var router = express.Router();
var async = require("async");
var path = require("path");
const { tenantSqlConnection  } = require('../../auth/sqlConnection');

router.get('/authTableau', function (req, res) {

  async.auto({
    sqlConnection: async() => {
      if (req.headers['tenant-id'])
        await tenantSqlConnection(req.headers['tenant-id'], req.headers.databaseName);
      return [];
    },
    search_clients_uid: ['sqlConnection', (result, cb) => {
      if (req.headers['tenant-id']) {
        connection[req.headers['tenant-id']].execute.query(`SELECT uid FROM search_clients;`,(error,rows)=>{
          if(error) cb(error);
          else{
            var allUids=[]
            rows.map(u => allUids.push(u.uid))
            cb(null,allUids);
          }
        });
      } else {
        res.clearCookie('accessToken');
        res.clearCookie('email');
        cb(null, []);
      }
    }],
    process_request:['search_clients_uid', (result, cb)=>{
      if (req.query.code && res.locals) {
        res.cookie('accessToken', res.locals.accessToken, {
          expire: new Date(res.locals.accessTokenExpiresAt),
        });
        res.cookie('accessTokenExpiresAt', res.locals.accessTokenExpiresAt);
        res.cookie('refreshToken', res.locals.refreshToken);
        res.render(DIRNAME + '/resources/tableauConnector.html', {
          redirect_uri: `${config.get("adminURL")}/analytics/tableauConnector/authTableau`,
          // client_id:req.headers.client_id,
          authUrl : config.get("adminURL"),
          searchClientIds:result.search_clients_uid
        });
      } else {
        //* LOGIN + ACCESS TOKEN
        res.removeHeader("X-Frame-Options");
        res.render(DIRNAME + '/resources/tableauConnector.html', {
              // client_id:req.headers.client_id,
              redirect_uri: `${config.get("adminURL")}/analytics/tableauConnector/authTableau`,
              authUrl : config.get("adminURL"),
              searchClientIds:result.search_clients_uid
        });
      }
    }]
  }, (error, results)=>{
    if(error){
      console.error(new Error(error));
      res.status(500).send("Error occured");
    }
  });
});
module.exports = router;
