/* Created By <PERSON><PERSON>
    On 14/09/18
*/


var express = require('express');
var request = require('request');
var async = require('async');
const commonFunctions = require('./../../utils/commonFunctions');
var fs = require('fs');
var router = express.Router();

//************************ Start of zendesk Permissions ***************************'
router.get('/getContent', function (req, res, next) {
    var obj = {
        "contentSources": ["54"],
        "email": "<EMAIL>"
    }
    getPermissionObjects(obj,req, function (err, data) {
        res.send(data);
    })
});

const getPermissionObjects = function (contentSources,req, callback) {
    var asyncTasks = [];
    var contentSourceIds = contentSources['contentSources']
    var email = contentSources['email'];
    contentSourceIds.map(x => {
        asyncTasks.push(zendeskPermissions.bind(null, x, email,req));
    })
    async.series(asyncTasks, function (err, data) {
        if (err) {
            callback(err, null);
        }
        else
            callback(null, data);
    })
}

const zendeskPermissions = function (content_source_id, email,req, callback) {
    if (email.trim() == "") {
        return callback(null, { "id": content_source_id, "perms": [0] });
    }
    commonFunctions.getContentSourceDataById(content_source_id,req, function (err, resultData) {
        var username = email;
        var options = {
            method: 'GET',
            url: resultData.contentSource.url + '/api/v2/search?query=type:user%20"' + username + '"',
            headers:
            {
                'content-type': 'application/json'
            }
        };
        if (resultData.authorization.authorization_type == "OAuth") {
            options["headers"] = {
                "authorization": "Bearer " + resultData.authorization.accessToken
            }
        } else {
            options["headers"]["authorization"] = 'Basic ' + new Buffer(resultData.authorization.username + ":" + resultData.authorization.password).toString("base64");

        }
        request(options, function (error, response, body) {
            if (error || !JSON.parse(body)) {
                commonFunctions.errorlogger.error("Error", error);
                callback("Some Error", []);
            }
            else {
                if (commonFunctions.checkJSON(body)) {
                    var body = (JSON.parse(body));
                    var j = 0;
                    for (var i = 0; i < body.results.length; i++) {
                        if (body.results[i].role == "admin" || body.results[i].role == "agent") {
                            j = i;
                            break;
                        }
                    }
                    body = body.results[j];
                    if (!body) {
                        return callback(null, { "id": content_source_id, "perms": [0] });
                    }
                    var ObjToReturn = {
                        "id": content_source_id,
                        "perms": []
                    };
                    if (body.role == "agent" || body.role == "admin") {
                        return callback(null, { "id": content_source_id, "perms": [-1] });
                    }
                    getZendeskGroups(body.id, resultData, 0, 200, [], function (errGroups, dataGroups) {
                        dataGroups.push(0);
                        ObjToReturn["perms"] = dataGroups;
                        callback(errGroups, ObjToReturn);
                    });

                } else {
                    callback('Invalid Creds', []);
                }
            }
        });
    });
}

const getZendeskGroups = function (username, resultData, start, limit, initialArr, callback) {
    var options = {
        method: 'GET',
        url: resultData.contentSource.url + '/api/v2/users/' + username + '/group_memberships.json',
        headers:
        {
            'content-type': 'application/json'
        }
    };
    if (resultData.authorization.authorization_type == "OAuth") {
        options["headers"] = {
            "authorization": "Bearer " + resultData.authorization.accessToken
        }
    } else {
        options["headers"]["authorization"] = 'Basic ' + new Buffer(resultData.authorization.username + ":" + resultData.authorization.password).toString("base64");

    }
    request(options, function (error, response, body) {
        if (error) {
            commonFunctions.errorlogger.error("Error", error);
            callback(error, null);
        }
        else {
            if (commonFunctions.checkJSON(body)) {
                var body = JSON.parse(body);
                for (var i = 0; i < body.group_memberships.length; i++) {
                    initialArr.push(body.group_memberships[i].group_id);
                }
                if (body.next_page != null) {
                    start = start + limit;
                    getZendeskGroups(username, resultData, start, limit, initialArr, function (errGRP, grps) {
                        if (errGRP) {
                            callback(errGRP, null);
                        } else {
                            callback(null, grps);
                        }
                    });
                } else {
                    callback(null, initialArr);
                }

            } else {
                callback('Invalid Creds', null);
            }
        }
    });

}

//************************ End of zendesk Permissions ***************************

module.exports = {
    getPermissionObjects: getPermissionObjects,
    router: router
}
