module.exports = {
  "objects": [
    {
      "name": "posts",
      "label": "Posts",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": []

    },
    {
      "name": "articles",
      "label": "Articles",
      "status": 1,
      "boosting_factor": 1,
      
      "base_url": "",
      "fields": []

    },
    {
      "name": "tickets",
      "label": "Tickets",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "id",
          "label": "id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "url",
          "label": "View Href",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "external_id",
          "label": "External Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "type",
          "label": "Type",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "raw_subject",
          "label": "Title",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "description",
          "label": "Description",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "priority",
          "label": "Priority",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "open",
          "label": "Open",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "tags",
          "label": "Tags",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "recipient",
          "label": "Recipient",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "requester_name",
          "label": "Requester Name",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "requester_email",
          "label": "Requester E-mail",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "submitter_name",
          "label": "Submitter Name",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "submitter_email",
          "label": "Submitter E-mail",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "assigner_name",
          "label": "Assigner Name",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "assigner_email",
          "label": "Assigner E-mail",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "has_incidents",
          "label": "Has Incidents",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "due_at",
          "label": "Due Date",
          "type": "datetime",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 0, //analyzer
          "isActive": 1
        },
        {
          "name": "satisfaction_rating",
          "label": "Satisfaction Rating",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "is_public",
          "label": "Is Public",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "post_time",
          "label": "Created Date",
          "type": "datetime",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 0, //analyzer
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "modified_time",
          "label": "Modified Date",
          "type": "datetime",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 0, //analyzer
          "isActive": 1
        },
        {
          "name": "comments",
          "label": "Comments",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "attachments",
          "label": "Attachment Data",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "attachment_multiple",
          "label": "Attachments",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1,
          "isMerged": 1
        },
        {
          "name": "group_name",
          "label": "Group Name",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },  
        {
          "name": "group_url",
          "label": "Group Url",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "brand_url",
          "label": "Brand Url",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "brand_name",
          "label": "Brand Name",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "organization_url",
          "label": "Organization Url",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "organization_name",
          "label": "Organization Name",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "group_id",
          "label": "Group Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },

      ]

    }
  ],
  "fields": [
    {
      "name": "TopicName",
      "label": "Topic Name",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "TopicURL",
      "label": "Topic URL",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "author_name",
      "label": "Author Name",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "author_email",
      "label": "Author E-mail",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "closed",
      "label": "Closed",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "comment_count",
      "label": "Comment Count",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "details",
      "label": "Description",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "featured",
      "label": "Featured",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "follower_count",
      "label": "Follower Count",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "html_url",
      "label": "View href",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "id",
      "label": "id",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "pinned",
      "label": "Pinned",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "post_time",
      "label": "Created Date",
      "type": "datetime",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 0, //analyzer
      "isActive": 1,
      "isMerged": 1
    }, {
      "name": "status",
      "label": "Status",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "title",
      "label": "Title",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "topic_id",
      "label": "Topic Id",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "updated_at",
      "label": "Updated At",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 0, //analyzer
      "isActive": 1
    },
    {
      "name": "url",
      "label": "Url",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "vote_count",
      "label": "Vote Count",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "vote_sum",
      "label": "Vote Sum",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "comments",
      "label": "Comments",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "attachments",
      "label": "Attachment Content",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "attachment_multiple",
      "label": "Attachments",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1,
      "isMerged": 1
    },  
    {
      "name": "section_name",
      "label": "Section Name",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "category_name",
      "label": "Category Name",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "publication_status",
      "label": "Publication Status",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "locale",
      "label": "Locale",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    }, 
    {
      "name": "source_locale",
      "label": "Source Locale",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    },
    {
      "name": "labels",
      "label": "Label Names",
      "type": "string",
      "isFilterable": 1, //indexing method analyzed OR not analyzed
      "isSortable": 1,
      "isSearchable": 1, //analyzer
      "isActive": 1
    }
  ]
};


