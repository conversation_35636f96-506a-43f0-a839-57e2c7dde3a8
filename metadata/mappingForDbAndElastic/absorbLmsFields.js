module.exports = {
  objects: [{
    name: 'training',
    label: 'Training',
    status: 1,
    boosting_factor: 1,
    base_url: '',
    fields: []
  }],
  fields: [{
    name: 'courseType',
    label: 'Course Type',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'id',
    label: 'Id',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 0,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'name',
    label: 'Name',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1
  },
  {
    name: 'description',
    label: 'Description',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1
  },
  {
    name: 'notes',
    label: 'Notes',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1
  },
  {
    name: 'accessDate',
    label: 'Access Date',
    type: 'datetime',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 0,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'expireType',
    label: 'Expire Type',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 0,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'expiryDate',
    label: 'Expiry Date',
    type: 'datetime',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 0,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'activeStatus',
    label: 'Active Status',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'tagNames',
    label: 'Tags',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'resourceNames',
    label: 'Resources',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1
  },
  {
    name: 'editorIds',
    label: 'Editor Ids',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 0,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'departmentNames',
    label: 'Departments',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'prerequisiteCourseNames',
    label: 'Prerequisite Courses',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1,
    isMasked: 0
  },
  {
    name: 'categoryName',
    label: 'Category',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1,
    isMasked: 0
  },
  {
    name: 'certificateUrl',
    label: 'Certificate Url',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 0,
    isActive: 1,
    isMasked: 0
  },
  {
    name: 'currency',
    label: 'Currency',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1,
    isMasked: 0
  },
  {
    name: 'lessonNames',
    label: 'Lesson Names',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'lessonDescriptions',
    label: 'Lesson Descriptions',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1
  },
  {
    name: 'chapterNames',
    label: 'Chapter Names',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'slug',
    label: 'Slug',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'viewHref',
    label: 'View Href',
    type: 'string',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 1,
    isActive: 1,
    isMasked:0
  },
  {
    name: 'post_time',
    label: 'Created Date',
    type: 'datetime',
    isFilterable: 1,
    isSortable: 1,
    isSearchable: 0,
    isActive: 1,
    isMerged: 1,
    isMasked: 0
  }
  ]
};
