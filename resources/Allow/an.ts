if (window.Element && !Element.prototype.closest) {
  Element.prototype.closest =
  function(s) {
      var matches = (this.document || this.ownerDocument).querySelectorAll(s),
          i,
          el = this;
      do {
          i = matches.length;
          while (--i >= 0 && matches.item(i) !== el) {};
      } while ((i < 0) && (el = el.parentElement));
      return el;
  };
}
class CaptureEvent {
  endpoint: string;
  uid : string;
  private sid:string;
  private sessionTimeOut:string;
  private emailTrackingEnabled:any = false;
  private externalUserEnabled:any = false;
  private accountName: string;
  private heart:Beep;
  constructor(endpoint, uid, sessionTimeOut, emailTrackingEnabled?,externalUserEnabled?, accountName?){
    this.endpoint = endpoint;
    this.uid = uid;
    this.sessionTimeOut = sessionTimeOut;
    this.accountName = accountName;
    if(emailTrackingEnabled===true||emailTrackingEnabled==="true"||emailTrackingEnabled==="1"||emailTrackingEnabled===1)
      this.emailTrackingEnabled = true;
    if(externalUserEnabled===true||externalUserEnabled==="true"||externalUserEnabled==="1"||externalUserEnabled===1)
      this.externalUserEnabled = true;
    this.sid = this.getsid();
    this.gettaid();
  }
  startClickTracking(){
    this.utilDocReady(()=>{
      document.addEventListener("click", (e:any)=>{
        if (e.target.tagName === "A" || e.target.closest('a')) this.sendAnalytics("click", {
          "target": e.target.href || e.target.closest('a').href
        });
      });
      document.addEventListener("contextmenu", (e:any)=>{
        if (e.target.tagName === "A" || e.target.closest('a')) this.sendAnalytics("click", {
          "target": e.target.href || e.target.closest('a').href
        });
      });
    });
  }
  startTimeTracking(){
    this.heart = new Beep((event,params,cb?)=>{this.sendAnalytics(event,params,cb);});
    this.utilDocReady(()=>{
      document.addEventListener("mousemove",()=>{this.heart.beat();});
      document.addEventListener("mousedown",()=>{this.heart.beat();});
      document.addEventListener("mouseup"  ,()=>{this.heart.beat();});
      document.addEventListener("scroll"   ,()=>{this.heart.beat();});
      document.addEventListener("wheel"    ,()=>{this.heart.beat();});
      document.addEventListener("keyup"    ,()=>{this.heart.beat();});
    });
  }
  getsid(){
    // if(!this.sid)
    this.sid = this.getCookie("_gz_sid");
    if(!this.sid)
      this.sid = Date.now() + ("000" + Math.floor(Math.random() * 1000)).slice(-3);
    this.setCookie("_gz_sid",this.sid,this.sessionTimeOut);
    return this.sid;
  }
  gettaid(){
    let taid = this.getCookie("_gz_taid");
    if(!taid){
      taid = Date.now() + ("000" + Math.floor(Math.random() * 1000)).slice(-3);
      this.setCookie("_gz_taid",taid,365*24*60)
    }
    return taid;
  }
  sendAnalytics(event,params,cb?){
    if (!event) {
      event = "pageView";
    }
    if (!params) params = {};
    if (!params.url)      params.url      = window.location.href;
    if (!params.referrer) params.referrer = document.referrer;
    if (!params.e)        params.e        = event;
    if (!params.t)        params.t        = document.title;
    if (!params.uid)      params.uid      = this.uid;
    if (!params.r)        params.r        = Math.floor(Math.random() * 100000); // To disable caching
    if (!params.sid)      params.sid      = this.getsid();
    if (!params.taid)     params.taid     = this.gettaid();
    if (event === "conversion") {
      if (!(params.convUrl && params.convSub))
        console.info("incomplete object in gza function. missing convUrl or convSub");
      // params.analyticsID = this.getCookie("analyticsID");
    }
    let url = this.endpoint + "?";
    for (let p in params) {
      if (typeof params[p] === "object") {
        params[p] = JSON.stringify(params[p]);
      }
      url = url + encodeURIComponent(p) + "=" + encodeURIComponent(params[p]) + "&";
    }
    this.utilAjax(url,resText=>{
      if(cb)
        cb(resText);
    });
  }
  setUser(email){
    let obj:any = {};
    if (this.emailTrackingEnabled && email){
      obj.utm = email.split("").map(c=>c.charCodeAt() ^ 59).join(".");
    }
    if (this.externalUserEnabled && this.accountName)
      obj.internal = (email.split("@")[1] === this.accountName);
      this.setCookie("_gza_internal", obj.internal,this.sessionTimeOut);
    if(obj.utm || typeof obj.internal === "boolean")
      this.sendAnalytics("setUtm", obj);
  }
  utilDocReady(cb){
    if (document.readyState === 'complete') {
      cb();
    }
    else{
      document.onreadystatechange = ()=>{
        if (document.readyState === 'complete') {
          cb();
        }
      };
    }
  }
  utilAjax(url,cb){
    let ele = document.createElement("img");
    if(cb){
      ele.onload = cb;
      ele.onerror = cb;
    }
    ele.src = url;
    ele.width = 1;
    ele.height = 1;
  }
  getCookie(cname) {
    let name = cname + "=";
    let ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i = i + 1) {
      var c = ca[i];
      while (c.charAt(0) === ' ') {
        c = c.substring(1);
      }
      if (c.indexOf(name) === 0) {
        return c.substring(name.length, c.length);
      }
    }
    return "";
  }
  setCookie (cname, cvalue, exMins?) {
    let expires = '';
    if (exMins) {
      let d = new Date();
      d.setTime(d.getTime() + (exMins * 60 * 1000));
      expires = "expires=" + d.toUTCString();
    }
    document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
  }
}
class Beep{
  private startTime: number;
  private lastUsed : number;
  private gza : Function;
  private tmp : NodeJS.Timer;
  constructor(ping){
    this.gza = ping;
    this.startTime = Date.now();
    this.lastUsed = this.startTime;
  }
  beat(){
    let now = Date.now();
    if(document.hasFocus()){
      if((now-this.lastUsed >= 5*1000)){
        this.gza("beep",{alive:Math.round((now-this.lastUsed)/1000)});
        this.lastUsed = now;
      }
    }
    else{
      this.lastUsed = now;
    }
    this.checkActive();
  }
  checkActive(){
    if(this.tmp)
      clearTimeout(this.tmp);
    this.tmp = setTimeout(()=>{this.beat();},5*1000);
  }
}
class Analytics extends CaptureEvent {
  constructor() {
    super("<%= client_url %>/analytics/track.png", 
    "<%= uid %>",
    +"<%= sessionTimeOut %>",
    "<%= emailTrackingEnabled||false %>",
    "<%= externalUserEnabled||false %>",
    "<%= accountName||'' %>",
    );
    super.sendAnalytics("pageView", {});
    super.startClickTracking();
    super.startTimeTracking();
  }
}
var GzAnalytics: Analytics = GzAnalytics || new Analytics();
var gza:any = (event,params,cb?)=>{GzAnalytics.sendAnalytics(event,params,cb);};
var _gr_utility_functions:any = {};
_gr_utility_functions.getCookie = GzAnalytics.getCookie;
