var request = require("request");
var commonFunctions = require('./../../utils/commonFunctions');
var async = require('async');
const moment = require('moment');
const momentZone = require ('moment-timezone')
const { getAccessTokenFromTenantId } = require('auth-middleware');
const { authMiddlewareRoute } = require('../../auth/authMiddleware')
const config = require('config');
const { SUPPORTED_GPT_API_TYPES } =  require('../../constants/constants');
const { promisify } = require('util')
const requestPromise = promisify(request);
const { executeGPT } = require("../admin/mlService");
const getTzFromUserDb =(req) =>{
    
        return new Promise((resolve, reject)=>{
            let timezone = "UTC";
            const url = '/tenant/timezone'
            authMiddlewareRoute(req, url, (error, body)=>{
                if(body && body[0].timezone){
                    timezone = body[0].timezone
                }
            })
            resolve(timezone)
        })
}

const fetchTimezoneFromSession = async (req) => {
    let timezone = "UTC";

    if(req.body && req.body.timezone){
        timezone = req.body.timezone
    }else if(req.query && req.query.timezone){
        timezone = req.query.timezone;
    }else{
        // fetching timezone for analytics API's
        timezone = await getTzFromUserDb(req)
    }
    console.log(timezone);
    
    return timezone 
}


const updateTimeToUTC = (sD,eD,offset) => {
    let startOffset = momentZone(sD).tz(offset).format('Z')
    let endOffset = momentZone(sD).tz(offset).format('Z')
    let startDate = moment(sD).set({hours:0,minutes:0,seconds:0});
    let endDate = moment(eD).set({hours:23,minutes:59,seconds:59});
    let updatedStartDate = startDate.subtract(startOffset).format("YYYY-MM-DD HH:mm:ss")
    let updatedEndDate = endDate.subtract(endOffset).format("YYYY-MM-DD HH:mm:ss")

    return {updatedStartDate,updatedEndDate};
}

const changeDateAccordingToTimezone = async (req) => {
    const timezone = await fetchTimezoneFromSession(req);
    /**
     * Set time according to the POST and GET request for Analytics according time timezone
     */
    if (Object.keys(req.body).length != 0) {
        let { updatedStartDate, updatedEndDate } = updateTimeToUTC(req.body.startDate || req.body.from, req.body.endDate || req.body.to , timezone)
        if (req.body.startDate && req.body.endDate) {
            req.body.startDate = updatedStartDate;
            req.body.endDate = updatedEndDate;
        }
        if (req.body.from && req.body.to) {
            req.body.from = updatedStartDate;
            req.body.to = updatedEndDate;
        }
    }
    if (Object.keys(req.query).length != 0){
        let { updatedStartDate, updatedEndDate } = updateTimeToUTC(req.query.startDate || req.query.from , req.query.endDate || req.query.to, timezone)
        if (req.query.startDate && req.query.endDate) {
            req.query.startDate = updatedStartDate
            req.query.endDate = updatedEndDate
        }
        if (req.query.from && req.query.to) {
            req.query.from = updatedStartDate
            req.query.to = updatedEndDate
        }
    }
    let timeZoneOffset = momentZone(req.body.from).tz(timezone).format('Z')
    return timeZoneOffset;
}


let analyticsMiddleware = async function (req,res, cb) {
    commonFunctions.errorlogger.info("@admin service !! @analytics")
    var options;
    if (req.method == 'POST') {
        var bodyValue;
         if(Object.keys(req.body).length != 0){
            bodyValue = req.body;
            bodyValue['tenantId'] =  req.headers['tenant-id']
            if(req.url.includes('trackAdminAnalytics')) {
                // Set 'isTemporaryUser' to true if 'isTemporaryUser' or 'isCsmUser' exist in the session headers, otherwise set it to false.
                bodyValue['isTemporaryUser'] = req.headers.session['isTemporaryUser'] || req.headers.session['isCsmUser'] || false;

                // If 'isCsmUser' exists in the session headers, assign 'teamName' to a fixed value "csmUser".
                if (req.headers.session['isCsmUser']) {
                    bodyValue['teamName'] = "csmUser";
                } 
                // If 'teamName' exists in the session headers and 'isCsmUser' is not set, assign 'teamName' from session headers after some manipulation.
                else if (req.headers.session['teamName']) {
                    bodyValue['teamName'] = req.headers.session['teamName'];
                    const capitalizedA = bodyValue['teamName'].charAt(0).toUpperCase() + bodyValue['teamName'].slice(1);
                    bodyValue['teamName'] = capitalizedA + " Team";
                }
            }
         }else{
            bodyValue = req.query
            bodyValue['tenantId'] =  req.headers['tenant-id']
         }
         //console.log('I AM REQUEST FOR ANALYTICS', req.body, '&&&&&&&&', req.query);

        options = {
            method: req.method,
            rejectUnauthorized: false,
            url: config.get('analyticsService.url') + req.path,
            body:  bodyValue,
            headers: {},
            qs:req.query,
            json: true
        };
    } else{
        if(req.url.includes('/recentSearches')){
            req.url  = req.url +'?tenantId=' + req.headers['tenant-id']
        }else{
            //temporary change
            //req.url  = req.url +'&tenantId=' + req.headers['tenant-id']
            req.url  = req.url;
        }
        req.query.tenantId = req.headers['tenant-id'];
        options = {
            method: req.method,
            rejectUnauthorized: false,
            url: config.get('analyticsService.url') + req.url,
            headers: {},
            qs:req.query,
            json: true
        };

    }

    // if(req.url.includes('trackAdminAnalytics')){
        options.body.user = req.headers.session.email;
    // }
    if(req.url.includes('leadership')) {
        options.headers['analytics-secret'] = config.get('analyticsSecretKey');
    }
    //commonFunctions.errorlogger.info("|===== Option ======|", options);
    request(options, function (error, response, body) {
        if (error) {
            commonFunctions.errorlogger.error('i am error', error, JSON.stringify(options));
            cb(error, []);
        }
        else {
            if(req.url.includes('Download') || req.url.includes('download') ){
                //console.log(response.headers)
                res.setHeader('Content-disposition', 'attachment;');
                res.set( 'Content-Type', 'text/csv' );
            }
            if (body && !body.status) {
              cb(response, body);
            } else {
              cb(null, body);
            }
        }
    });
}

let analyticsApiMiddleware = async function (req, cb) {

    regex = new RegExp('^\/api\/v2');

    commonFunctions.errorlogger.info("@admin service !! @analytics api ")
    if (req.method == 'POST') {
        var bodyValue;
         if(Object.keys(req.body).length != 0){
            bodyValue = req.body;
            bodyValue['tenantId'] =  req.headers['tenant-id']
         }else{
            bodyValue = req.query
            bodyValue['tenantId'] =  req.headers['tenant-id']
         }

         let path = req.url


         if(req.url.includes('/searchSession/byCaseUid') ||
            req.url.includes('/searchConversion/DiscussionsReadyToBecomeArticles') ||
            req.url.includes('/searchQuery/missedQueryHistogram') ||
            req.url.includes('/searchQuery/kcsSupport') ||
            req.url.includes('/searchOverview/getTileData') ||
            req.url.includes('/searchOverview/getSearchSummaryChart') ||
            req.url.includes('/searchQuery/histogram') ||
            req.url.includes('searchSession/byCaseUid') ||
            req.url.includes('/searchSession/byCaseUidAuth')
            ){
            path = req.originalUrl
         }

        //console.log('path ================>>>>>>>>', path)
        //console.log('path url ---------------->>>>>>>', req.url)
        var options = {
            method: req.method,
            rejectUnauthorized: false,
            url: config.get('analyticsService.url') + path.split("?")[0],
            body: req.body,
            qs:req.query,
            json: true
        };
    } else if (req.method === 'DELETE') {
        var bodyValue = Object.keys(req.body) && Object.keys(req.body).length ? req.body : req.query;
        bodyValue['tenantId'] =  req.headers['tenant-id'] || config.get("tenantId");

        let path = req.url;

        var options = {
            method: req.method,
            rejectUnauthorized: false,
            url: config.get('analyticsService.url') + path.split("?")[0],
            body: bodyValue,
            json: true
        };
    } else {
        var path ;
        if(req.url.includes('/recentSearches')){
            path  = req.url +'?tenantId=' + req.headers['tenant-id']
        }else{
            path  = req.url;
        }
        req.query.tenantId = req.headers['tenant-id'];

        if(req.url.includes('/searchConversion/withFilters') || 
        req.url.includes('/searchQuery/all') || 
        req.url.includes('/searchQuery/withResults') ||
        req.url.includes('/searchQuery/withNoClicks') ||
        req.url.includes('/searchQuery/missedQueryHistogram') ||
        req.url.includes('/searchConversion/all') ||
        req.url.includes('/searchConversion/notOnFirstPage') ||
        req.url.includes('/searchConversion/bySessionsId/') ||
        req.url.includes('/searchQuery/withNoClicks') ||
        req.url.includes('/searchSession/all/searchQuery') ||
        req.url.includes('/searchSession/bySearchSessionId/') ||  
        req.url.includes('/session/log/all') ||
        req.url.includes('/getSessionTrackingFormattedResult') ||
        req.url.includes('/searchQuery/withoutResults') ||
        req.url.includes('/searchConversion/notOnFirstPage') ||
        req.url.includes('/searchConversion/bySessionId/') 
        ){
            if(Object.keys(req.query).length != 0){
                path = req.originalUrl;
            }else{
                path  = req.originalUrl;
            }
            

        }
        //let path = req.url;
        var options = {
            method: req.method,
            rejectUnauthorized: false,
            url: config.get('analyticsService.url') + path.split("?")[0],
            body: req.body,
            qs:req.query,
            json: true
        };
    }

    //commonFunctions.errorlogger.info("|===== Option ======|", options);
    request(options, function (error, response, body) {
        if (error) {
            cb(error, []);
        }
        else {
            cb(null, response);
        }
    });
}
let analyticsRawApiMiddleware = async function (req, cb) {

    commonFunctions.errorlogger.info("@admin service !! @analytics api ")
    if (req.method == 'POST') {
        var bodyValue;
         if(Object.keys(req.body).length){
            bodyValue = req.body;
         }
         if(Object.keys(req.query).length){
            bodyValue = {...bodyValue, ...req.query}
         }
         bodyValue['tenantId'] =  req.headers['tenant-id']
         let path = req.url

         if(req.url.includes('ds/get-data-sources') ||
            req.url.includes('ds/get-fields') ||
            req.url.includes('ds/get-data') ||
            req.url.includes('/stats') ||
            req.url.includes('/searches/summary-chart') ||
            req.url.includes('/searches/histogram') ||
            req.url.includes('/searches/no-results-histogram') ||
            req.url.includes('/sessions/activities/sorted') ||
            req.url.includes('/sessions/activities/grouped') ||
            req.url.includes('/conversions/DiscussionsReadyToBecomeArticles') ||
            req.url.includes('/cases/failed-to-deflect/conversions') ||
            req.url.includes('/cases/deflected/conversions') ||
            req.url.includes('/articles/attached') ||
            req.url.includes('/articles/attached/cases')
            ){
            path = req.originalUrl
         }

        var options = {
            method: req.method,
            rejectUnauthorized: false,
            url: config.get('analyticsService.url') + path.split("?")[0],
            body: bodyValue,
            qs:req.query,
            json: true
        };
        request(options, function (error, response, body) {
            if (error) {
                cb(error, []);
            }
            else {
                cb(null, response);
            }
        });
    } 
    if (req.method == 'GET') {
        var path ;
        path  = req.url +'&tenantId=' + req.headers['tenant-id'];
        req.query.tenantId = req.headers['tenant-id'];

         if(req.url.includes('/searches/all') ||
            req.url.includes('/searches/with-results') ||
            req.url.includes('/searches/no-clicks') ||
            req.url.includes('/searches/no-results') ||
            req.url.includes('/sessions/searches') ||
            req.url.includes('/sessions/searches/:sessionId') ||
            req.url.includes('/conversions/all') ||
            req.url.includes('/conversions/not-on-first-page') ||
            req.url.includes('/sessions/conversions') ||
            req.url.includes('/sessions/conversions/:sessionId') ||
            req.url.includes('/conversions/with-filters') ||
            req.url.includes('/session/log/all') ||
            req.url.includes('/getSessionTrackingFormattedResult')
            ){
                path = req.originalUrl +'&tenantId=' + req.headers['tenant-id']
         }

        var options = {
            method: req.method,
            rejectUnauthorized: false,
            url: config.get('analyticsService.url') + path.split("?")[0],
            body: req.body,
            qs: req.query,
            json: true
        };
        request(options, function (error, response, body) {
            if (error) {
                cb(error, []);
            }
            else {
                cb(null, response);
            }
        });
    }
    
}
let searchAdmin = function (req, cb) {

    async.auto({
        "getAccessToken": async (cb) => {
            try{
                const data = await getAccessTokenFromTenantId(req.headers['tenant-id']);
                if(data.accessToken === ''){
                    throw new Error('Access token not found.');
                }
                return data.accessToken; 
            }catch(error){
                throw new Error('Access token not found.');
            }
        },
        "getSearchResults": ["getAccessToken", (data, cb) => {
            if (data.getAccessToken) {
                req.body.accessToken = data.getAccessToken; 
                //commonFunctions.errorlogger.info("@admin service !! @admin panel search");
                var options = {
                    method: req.method,
                    rejectUnauthorized: false,
                    url: config.get('searchService.url') + "/search" + req.path,
                    body: req.body,
                    json: true
                };

                //commonFunctions.errorlogger.info("|===== Option ======|", options);
                request(options, function (error, response, body) {
                    if (error) {
                        cb(error, []);
                    }
                    else {
                        cb(null, body);
                    }
                });
            } else {
                cb(null, null);
            }
        }]
    }, (error, result) => {
        cb(error, result ? result.getSearchResults : result);
    });

}

let search = function (req, cb) {

    //commonFunctions.errorlogger.info("@admin service !! @admin panel search")
    var options = {
        method: req.method,
        rejectUnauthorized: false,
        url: config.get('searchService.url') + "/search" + req.path,
        body: req.body,
        json: true
    };

    //commonFunctions.errorlogger.info("|===== Option ======|", options);
    request(options, function (error, response, body) {
        if (error) {
            cb(error, []);
        }
        else {
            cb(null, body);
        }
    });
}


let Apisearch = async function (req, res, cb ,isRestApiSearch = false) {
    const data = await getAccessTokenFromTenantId(req.headers['tenant-id']);
    req.body.accessToken = data.accessToken || ''; 
    commonFunctions.errorlogger.info("@admin service !! @admin panel search");
    var options = {
        method: req.method,
        rejectUnauthorized: false,
        url: config.get('searchService.url') + "/search/searchResultByPost",
        body: req.body,
        json: true,
        headers: {
          'v2-search-origin': req.headers.origin
        }
    };
    if(isRestApiSearch){
        options.headers =  {'content-type': 'application/json' ,'searchType':"rest"};
    }

    commonFunctions.errorlogger.info("|===== Option ======|", options);
    request(options, function (error, response, body) {
        if (error) {
            cb(error, []);
        }
        else {
            cb(null, body);
        }
    });
}

const validateGPTRules = async (req) =>{
    const { searchString , uid } = req.body;
    const gptRules = await commonFunctions.getGptRulesFromCache(req, uid).then(data=>JSON.parse(data.gptRules)).catch(err=>commonFunctions.errorlogger.error("Could not fetch gptRules",err ));
    let words;
    if (searchString && searchString.length>0){
        words = searchString.trim().split(' ').filter(item => item.length); 
    }else{
        words="";
    }
    const gptRulesValidated = (gptRules&&gptRules.minWords && gptRules.maxWords) 
    && (parseInt(gptRules.minWords) <= words.length && words.length <= parseInt(gptRules.maxWords));
    return gptRulesValidated;
}
const validations = async (req) => {
    if(!req.body.RequestType || !Object.values(SUPPORTED_GPT_API_TYPES).includes(req.body.RequestType)){
        return false;
    }
    const gptRulesValid = await validateGPTRules(req);
    if (!gptRulesValid){
        return false;
    }
    commonFunctions.errorlogger.info("gptRulesValid:", gptRulesValid);
    const { uid, searchString} = req.body;
    if (req.body.RequestType == SUPPORTED_GPT_API_TYPES.GPT){
        const { context, uid , articles } = req.body;
        const isValid = context && uid && searchString && articles &&  articles.length>0;
        if(!isValid){
            return false;
        }
    }
    if (req.body.RequestType == SUPPORTED_GPT_API_TYPES.SEARCH_GPT){
    // important parameters
    const { from, sortby ,orderBy, pageNo , resultsPerPage , getScore } = req.body;
    const validRequest = uid&& !isNaN(from) && sortby && ["post_time", "_score"].includes(sortby) && orderBy && ["desc", "asc"].includes(orderBy) && !isNaN(pageNo) && resultsPerPage && !isNaN(getScore);
    if (!validRequest) return false;
    }
    return true;
};

const performSearch = async (req, isRestApiSearch)=>{
    commonFunctions.errorlogger.info("@admin service !! @admin panel search");
    var options = {
        method: req.method,
        rejectUnauthorized: false,
        url: config.get('searchService.url') + "/search/searchResultByPost",
        body: req.body,
        json: true
    };
    if(isRestApiSearch){
        options.headers =  {'content-type': 'application/json' ,'searchtype':"searchunify_gpt"};
    }
    try {
        const response = await requestPromise(options);
        return response.body;
    } catch (error) {
        commonFunctions.errorlogger.info("Error occurred while fetching search results:", error);
        throw error;
    }
}

const gptAPI = async function (req, res, cb ,isRestApiSearch=false) {
    try{
        commonFunctions.errorlogger.info(">>>>GPT API!!!!!!!!");
        const isValid = await validations(req);
        commonFunctions.errorlogger.info("isValid", isValid);
        if (!isValid) {
            return res.status(400).send({
                status: 400,
                success: false,
                message: "Invalid Request",
                data: null,
            });
        }
        commonFunctions.errorlogger.info("requestType", req.body.RequestType);
        const data = await getAccessTokenFromTenantId(req.headers['tenant-id']);
        req.body.accessToken = data.accessToken || ''; 
        req.body.app = "search";
        let context, articles, query;
        if(req.body.RequestType===SUPPORTED_GPT_API_TYPES.SEARCH_GPT){
            await performSearch(req, isRestApiSearch)
            .then(result => {
                if(!result.gptActive){
                    res.send({
                        status: 403,
                        success: false,
                        message: "GPT is not activated on the search client or tenant!",
                        data: null,
                    });
                    return;
                }
                context = result.searchClientSettings.gptConfig.gptContext;
                articles = result.searchClientSettings.gptConfig.gptLinks;
                query = req.body.searchString
            })
            .catch(err => {
                commonFunctions.errorlogger.error("Error while fetching Search Results:", JSON.stringify(err));
                throw err;
            });
        }
        if(req.body.RequestType===SUPPORTED_GPT_API_TYPES.GPT){
            context = req.body.context;
            articles = req.body.articles;
            query = req.body.searchString
        }
        if(context.length===0 || articles && articles.length===0){
            return res.status(200).send({
                status: 204,
                success: false,
                message: "Context for GPT is not available",
                data: null,
            });
        }
        const body = {
            description: context,
            llm:true,
            query,
            separator:"$___$__$_$",
            streaming:false,
            articles
        } 
        const gptRequest = {...req};
        gptRequest.body = body;
        gptRequest.headers["uid"] = req.body.uid;  
        gptRequest.headers["mlClusterIp"] = req.headers.session.mlClusterIp;
        executeGPT(gptRequest, res);
    }
    catch(err){
        commonFunctions.errorlogger.error("Error Occured while Fetching GPT Answer:", err);
        return res.status(500).send({
            status: 500,
            success: false,
            message: "Something went Wrong!!",
            data: null,
        });
    }
};

module.exports = {
    analyticsMiddleware: analyticsMiddleware,
    analyticsApiMiddleware: analyticsApiMiddleware,
    analyticsRawApiMiddleware: analyticsRawApiMiddleware,
    searchAdmin: searchAdmin,
    search: search,
    Apisearch: Apisearch,
    changeDateAccordingToTimezone,
    gptAPI
};
