/**
 * <AUTHOR>
 * @version 1
 */
var express = require('express');
var async = require('async');
var router = express.Router();
var request = require('request');
var md5 = require('md5');
var universal = require('./../universal')
var accessKey = config.get('predictionIO.accessKey');
var recommenderRoutes = {};
var elastic = require('elasticsearch');
var commonFunctions = require('../../utils/commonFunctions');
//const crawler = require('./../crawlers');
var client = new elastic.Client({
    host: 'http://' + config.get('elasticIndexCS.host') + ':' + config.get('elasticIndexCS.port')
});
var spell_check_index = 'spellcheckindex'+"_"+config.get('poc_name');
var spell_check_type = "cluster";

var initSearchTerm = "what is your index";


function processSearchTerm(t, cb){
    // t = decodeURIComponent(((t || "").toString() || "").trim().replace(/%/g, "%25").replace(/\\/g, "\\")) || '';
    var termArray = t.split(/[\s,.!?]+/);
    asyncTask = [];
    for(var i =0; i<termArray.length; i++){
        asyncTask.push(function(i){
            return function(cbx){
                getCorrectSpell(termArray[i], function(err, data){
                    if(!err){
                        termArray[i] = data;
                    }
                    cbx(null, "done");
                })
            }
        }(i));
    }
    async.parallel(asyncTask, function(err, data){
        if(err){
            commonFunctions.errorlogger.error("**Error**"+err);
            cb(null, t);
        }
        else{
            commonFunctions.errorlogger.warn("Completed Async task");
            var modifiedSearchTerm = termArray.join(" ");
            commonFunctions.errorlogger.info("modified Term is ",modifiedSearchTerm);
            cb(null, modifiedSearchTerm);            
        }
    })
}


function getCorrectSpell(term, cb){
    term = term.toLowerCase();
    client.search({
        index: spell_check_index,
        type: spell_check_type,
        body : {"query":{"match_phrase":{"mis_spell_array":term}}}
    }, function(err, data){
        if(err){
            commonFunctions.errorlogger.error("**ERROR**",err);
            cb(err, null);
        }
        else{
            if(data && data.hits && data.hits.hits && data.hits.hits.length){
                cb(null, data.hits.hits[0]._source.spell_term);  // We are Considering only First result for now
            }
            else{
                cb("no match", null);
            }
        }
    })
}

module.exports = {
    router: router,
    processSearchTerm:processSearchTerm
};