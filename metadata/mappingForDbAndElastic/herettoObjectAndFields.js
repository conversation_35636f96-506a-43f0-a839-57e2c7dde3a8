module.exports = {
    "objects": [
        {
            "name": "content",
            "label": "Content",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields": [
                {
                    "name": "url",
                    "label": "URL",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 0,
                    "isSearchable": 1,
                    "isActive": 1,
                    "isMasked": 0
                },
                {
                    "name": "topicId",
                    "label": "Topic Id",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "lastModified",
                    "label": "Last Modified",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1,
                    "isMerged": 1,
                    "isMasked": 0
                },
                {
                    "name": "title",
                    "label": "Title",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 0,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "body",
                    "label": "Body",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 0,
                    "isSearchable": 1,
                    "isActive": 1
                }
            ]
        }
    ]
}