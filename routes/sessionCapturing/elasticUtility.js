var elastic = require('elasticsearch');
//var indexerUtility = require('./reindex');
var client;

function setupConnection(result){
  client = new elastic.Client({
    host: 'http://'+result.elasticHost+':'+result.elasticPort
  });
}

function dropIndex(index, cb) {
  client.indices.delete({
    index: index,
  }, function(err, res){
    if(!err)
      cb(null, 1);
    else
      cb(null, 0);
  });
}

function indexExists(index, cb){
  client.indices.exists({
    index: index,
  }, function(err, res){
    if(!err)
      cb(null, 1);
    else
      cb(null, 0);
  });
}

function createIndex(index, body, cb){
  var obj = {};
  obj.index = index;
  if(body)
    obj.body = body;

  client.indices.create(obj, function(err, res){
    if(!err)
      cb(null, 1);
    else
      cb(null, 0);
  });
}

function reIndex(sourceIndex, destIndex, cb){
 // indexerUtility.reIndex(client, sourceIndex,destIndex, cb);
}

exports.createIndex = createIndex;
exports.indexExists = indexExists;
exports.dropIndex = dropIndex;
exports.setupConnection = setupConnection;
