let parent = [];
parent = [
    {
        parentName: 'contentSourcesAndSearchClients',
        children: ['/admin/contentSources/getAllSupportedContentSourceTypes',
            '/admin/contentSources/getAllSupportedContentSourceCategories',
            '/admin/contentSources/getAddedContentSources',
            '/admin/contentSources/getContentSourcesAuthData',
            '/admin/contentSources/getSearchResultForML',
            '/admin/contentSources/getFoldersId',
            '/admin/contentSources/addContentSource',
            '/admin/contentSources/deleteContentSource',
            '/admin/contentSources/deleteObject',
            '/admin/contentSources/deleteField',
            '/admin/contentSources/getPlaces',
            '/admin/contentSources/crawlData',
            '/admin/contentSources/stopCrawler',
            '/admin/contentSources/fetchObjects',
            '/admin/contentSources/getGitInfo',
            '/admin/authorization/oAuthCallback',
            '/admin/authorization/oAuthCallbackJira',
            '/admin/authorization/oAuthCallbackConfluence',
            '/admin/searchClient/deletePlatform',
            '/admin/searchClient/getSearchClient',
            '/admin/searchClient/updateSearchClient',
            '/admin/searchClient/addPlatform',
            '/admin/searchClient/getPlatforms',
            '/admin/searchClient/getContentTypes',
            '/admin/searchClient/getFiltersPriority',
            '/admin/searchClient/saveFiltersPriority',
            '/admin/searchClient/downloadPlatform',
            '/admin/searchClient/saveCss',
            '/admin/searchClient/editClient',
            '/admin/searchClient/cloneSearchClient',
            '/admin/searchClient/isS3Supported',
            '/admin/searchClient/previewPath',
            '/admin/aem/getChildFolders',
            '/admin/box/getChildFolders',
            '/admin/dropbox/getChildFolders'
        ]
    },
    {
        parentName: 'statusPage',
        children: ['/statusPage']
    },
    {
        parentName: 'authentication',
        children: ['/oauth/token',
            '/authorise',
            '/authorise_success',
            '/logout',
            '/signup',
            '/login',
            '/registerEmail',
            '/getAutoProvisionToken',
            '/validateUser',
            '/resendOTPMail',
        ]
    },
    {
        parentName: 'searchEndpoints',
        children: ['/search/searchResultByGet',
            '/search/searchResultByPost'
        ]
    },
    {
        parentName: 'trackingApi',
        children: ['/analytics/track.png']
    },
    {
        parentName: 'tuning',
        children: ['/tuning/contentTuning/getAllContent',
            '/tuning/contentTuning/changeTypeBoosting',
            '/tuning/contentTuning/changeFieldBoosting',
            '/tuning/contentTuning/custom-boosting',
            '/tuning/searchtuning/saveTuningData',
            '/tuning/searchtuning/getTuningData',
            '/tuning/searchtuning/deleteTuningData',
            '/tuning/searchtuning/getUnrankedSearchResults',
        ]
    },
    {
        parentName: 'emailNotifications',
        children: ['/admin/notifications/getAllNotificationPreferences',
            '/admin/notifications/saveNotificationPreferences',
            '/admin/notifications/deleteNotificationPreferences',
            '/admin/notifications/sendNotifications',
            '/admin/notifications/sendVersionMail',
            '/admin/notifications/sendReport',
        ]
    },
    {
        parentName: 'profiles',
        children: ['/statusPage',
            '/admin/userManagement/getAllUsers',
            '/admin/userManagement/editUser',
            '/admin/userManagement/manageAccount',
            '/admin/userManagement/changePassword',
            '/admin/userManagement/deleteRegisteredUser',
        ]
    },
    {
        parentName: 'ticketsApi',
        children: ['/admin/support/getAllTickets',
            '/admin/support/editTicket',
            '/admin/version/getVersions',
        ]
    },
    {
        parentName: 'authContentSource',
        children: ['/oauthClients/saveOauthClients',
            '/oauthClients/deleteOauthClients',
            '/oauthClients/getOauthClients',
            '/oauthClients/getOauthScopes',
        ]
    },
    {
        parentName: 'analyticsOld',
        children: ['/analytics/an/mytrail',
            '/analytics/missedQueryHistogram',
            '/analytics/missedQueryQueries',
            '/analytics/SearchHistogram',
            '/analytics/queriesCount',
            '/analytics/getConversions',
            '/searchClientAnalytics/getTypesStatistics',
            '/analytics/getGeoReport',
            '/analytics/getTileData',
            '/analytics/getRecentSearch',
            '/analytics/getLonelyDoc',
            '/searchClientAnalytics/getAddedContent',
            '/analytics/readyToBecomeHelpArticle',
            '/analytics/getFunnel',
            '/analytics/sa/getSessionReports',
            '/analytics/sa/trackSession',
            '/analytics/sa/getTopClickedResults',
            '/analytics/sa/getSessionsActivityDetails',
            '/analytics/sa/exploreSession',
            '/analytics/sa/exploreSearchText',
            '/analytics/sa/exploreSiteVisitSession',
            '/analytics/sa/getFilterBasedSearchChart',
            '/searchClientAnalytics/getAllSearchPlatforms',
            '/analytics/sa/getPlatformSearchChart',
            '/analytics/sa/getDomainSpecificVisitors',
            '/analytics/sa/getDomainSpecificSearchedText',
            '/analytics/sa/getConversionDetails',
            '/analytics/sa/getTopBackwardDocuments',
            '/analytics/sa/getTopSearchsWithNoClicks',
            '/analytics/sa/getDocumentsWithLargeContent',
            '/analytics/sa/getDocumentsWithLargeContent',
            '/analytics/sa/getContentSourceList',
            '/analytics/sa/getSessionChartDetails',
            '/analytics/sa/getSessionChartSearches',
            '/analytics/sa/getSessionChartPageViews',
            '/analytics/sa/getFiltersForQueries',
            '/searchClientAnalytics/getActiveReportsInfo',
            '/searchClientAnalytics/getContentAuthData',
        ]
    },
    {
        parentName: 'analyticsNew',
        children: ['/api/v2/searchQuery/all',
            '/api/v2/searchQuery/withResults',
            '/api/v2/searchQuery/withoutResults',
            '/api/v2/searchQuery/withNoClicks',
            '/api/v2/searchQuery/bySessionId',
            '/api/v2/searchConversion/all',
            '/api/v2/searchConversion/discussionsReadyToBecomeArticles',
            '/api/v2/searchConversion/notOnFirstPage',
            '/api/v2/searchConversion/bySessionId',
            '/api/v2/searchSession/all',
            '/api/v2/searchSession/all/searchQuery',
            '/api/v2/searchSession/all/searchConversion',
            '/api/v2/searchSession/bySearchSessionId',
            '/api/v2/searchSession/bySearchSessionId',
            '/api/v2/searchSession/bySearchSessionId',
            '/api/v2/searchSession/byCaseUid',
            '/api/v2/searchSession/byCaseUid/searches',
            '/api/v2/searchSession/byCaseUid/views'
        ]
    },{
        parentName: 'searchApiEnds',
        children: [
            '/api/v2/searchResults',
            '/api/v2/provisionToken']
    }
]
module.exports = parent;
