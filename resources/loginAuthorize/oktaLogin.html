<html>

<head>
    <meta name="viewport" content="width=device-width, user-scalable=no" />
</head>

<body>
    <div>Login successfull</div>
    <script>
        function getParameterByName(name, url) {
            if (!url) url = window.location.href;
            name = name.replace(/[\[\]]/g, "\\$&");
            var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, " "));
        }
        function getLocation(href) {
            var l = document.createElement("a");
            l.href = href;
            return l;
        };
        function setLocation() {
            var redirectPath = getLocation('<%=redirect%>');
            return redirectPath.pathname;
        }
        localStorage.setItem("user", '<%- JSON.stringify(user) %>' );
        localStorage.setItem("_csrf", '<%=csrf%>');
        localStorage.setItem("allAddons", '<%- JSON.stringify(addons)%>' );
        var path = setLocation();
        <% if(googleTab){ %> 
            window.close();
        <%}%>
        window.location.href = '<%=redirect%>';
    </script>
</body>

</html>