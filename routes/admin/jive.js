var express = require('express');
var router = express.Router();
var async = require('async');
var request = require("request");
var fs = require('fs');
var commonFunctions = require('./../../utils/commonFunctions');
var constants = require('../../constants/constants');
var appVariables = require('../../constants/appVariables');


const JVConnection = function (code, contentSource, callback) {
    var auth = "Basic " + new Buffer(contentSource.authorization.client_id + ":" + contentSource.authorization.client_secret).toString("base64");
    var headers = {
        "Authorization": auth,
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    var form = {}
    /** if (checkRefresh) {
       form = { grant_type: 'refresh_token', refresh_token: checkRefresh }
    }**/
    form = { 'grant_type': 'authorization_code', "client_id": contentSource.authorization.client_id, "code": code }

    // Configure the request
    var options = {
        url: contentSource.contentSource.url + '/oauth2/token',
        method: 'POST',
        headers: headers,
        form: form
    }

    // Start the request
    request(options, function (error, response, body) {
        if (!error && response.statusCode === 200) {
            commonFunctions.errorlogger.info(JSON.parse(body));
            callback(JSON.parse(body))
        }
        else {
            callback(0)
        }
    })

}

const getProjects = function (resultData, sort,req, callback) {
    getProjectsByAPI(resultData, [], 0, 100, function (errProjectFetched, ProjectsFetched) {
        var contentSourceObjectArr = [];
        var contentObjectToInsert = {};
        for (var i = 0; i < ProjectsFetched.length; i++) {
            if (ProjectsFetched[i].type != "blog") {
                var contentObjectToInsert = {};
                contentObjectToInsert['content_source_id'] = resultData.authorization.content_source_id;
                contentObjectToInsert['spaceName'] = ProjectsFetched[i].name.replace('\'', "").replace(',', "").replace("(", "").replace(")", "");
                contentObjectToInsert['spaceUrl'] = ProjectsFetched[i].resources.html.ref;
                contentObjectToInsert['spaceId'] = ProjectsFetched[i].placeID;
                contentObjectToInsert['isSelected'] = resultData.projectIds.includes(ProjectsFetched[i].placeID) ? 1 : 0;
                contentObjectToInsert['spaceKey'] = ProjectsFetched[i].resources.self.ref;
                contentSourceObjectArr.push(contentObjectToInsert);
            }
        }
        commonFunctions.insertSpacesBoards(resultData.authorization.content_source_id, contentSourceObjectArr,req, function () {
            commonFunctions.getPlacesBySortParam(resultData.authorization.content_source_id, sort,req, function (err, projects) {
                callback(null, projects);

            });
        });
    });
}


const getProjectsByAPI = function (resultData, initialArr, start, limit, callback) {
    var projects;
    var options = {
        method: 'GET',
        url: resultData.contentSource.url + '/api/core/v3/places?includeBlogs=false&count=' + limit + '&startIndex=' + start,
        headers: {
            'content-type': 'application/json'
        }
    };

    if (resultData.authorization.authorization_type == "Basic") {
        options.headers.authorization = 'Basic ' + new Buffer(resultData.authorization.username + ":" + resultData.authorization.password).toString("base64");
    }
    if (resultData.authorization.authorization_type == "OAuth") {
        options["headers"] = {
            "authorization": "Bearer " + resultData.authorization.accessToken
        }
    }
    request(options, function (error, response, body) {
        if (error) {
            commonFunctions.errorlogger.error(error);
            callback(error, null)
        }
        else {
            commonFunctions.errorlogger.info("Spaces found for: " + resultData.contentSource.url + '/api/core/v3/places?includeBlogs=false&count=' + limit + '&startIndex=' + start);
            body = body.replace("throw 'allowIllegalResourceCall is false.';", "");
            if (commonFunctions.checkJSON(body)) {
                // callback("Invalid Credentials", null)
                body = JSON.parse(body);
                projects = body.list;
                initialArr = initialArr.concat(projects);
                if (body.links && body.links.next) {
                    start = start + limit;
                    getProjectsByAPI(resultData, initialArr, start, limit, function (errProjectFetched, ProjectsFetched) {
                        if (errProjectFetched) {
                            callback(errProjectFetched, null);
                        } else {
                            callback(null, ProjectsFetched);
                        }
                    });
                } else {
                    callback(null, initialArr);
                }

            } else {
                callback('Invalid Creds', null);
            }
        }
    });



}

module.exports = {
    router: router,
    JVConnection: JVConnection,
    getProjects: getProjects,
    getProjectsByAPI: getProjectsByAPI
}


