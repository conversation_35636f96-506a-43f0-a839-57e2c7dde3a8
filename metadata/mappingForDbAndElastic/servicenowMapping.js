module.exports = {
  "objects": [
    {
      "name": "incident",
      "label": "Incident",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "id", //number
          "label": "Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "short_description", //short_description
          "label": "Short Description",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "description", //description
          "label": "Description",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "post_time", //sys_created_on
          "label": "Created Date",
          "type": "datetime",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 0, //analyzer
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "created_by",  //sys_created_by
          "label": "Created By",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "location", //if(location.link) location vale table ka data ane k bad if(full_name)
          "label": "Location",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "resolved_by",
          "label": "Resolved By",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1,
          "isMasked": 0
        },
        //  problem vale table ka data corresponding to an incident
        {
          "name": "problem_id", // problem_id
          "label": "Problem Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "problem_short_description", // short_description
          "label": "Problem Short Description",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "problem_description", // description
          "label": "Problem Description",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "view_href", // description
          "label": "View Href",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "additional_comments", // description
          "label": "Additional Comments",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "subcategory", //if(location.link) location vale table ka data ane k bad if(full_name)
          "label": "Sub Category",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "category", //if(location.link) location vale table ka data ane k bad if(full_name)
          "label": "Category",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
      ]
    },
    {
      "name": "knowledge_base_articles",
      "label": "Knowledge Base Articles",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "name",
          "label": "Name",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "description",
          "label": "Description",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "knowledge_base_id",
          "label": "Knowledge Base Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "post_time",
          "label": "Created Date",
          "type": "datetime",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 0, //analyzer
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "created_by",
          "label": "Created By",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "id",
          "label": "Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "knowledge_created_on",
          "label": "Knowledge Created On",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "knowledge_created_by",
          "label": "Knowledge Created By",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "knowledge_topics",
          "label": "Knowledge Topics",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "knowledge_short_description",
          "label": "Knowledge Short Description",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "knowledge_text",
          "label": "Knowledge Text",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "knowledge_full_category",
          "label": "Knowledge Full Category",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "knowledge_label",
          "label": "Knowledge Label",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "knowledge_feedback",
          "label": "Knowledge Feedback",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "view_href", // description
          "label": "View Href",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1,
          "isMasked": 0
        }
      ]

    },
    {
      "name": "knowledge_base_questions",
      "label": "Knowledge Base Questions",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "id",
          "label": "Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "question",
          "label": "Question",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "question_detail",
          "label": "Question Detail",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "post_time",
          "label": "Created Date",
          "type": "datetime",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 0, //analyzer
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "created_by",
          "label": "Created By",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1,
          "isMasked": 0
        },
        {
          "name": "votes",
          "label": "Votes",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "views",
          "label": "Views",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "knowledgebase_id",
          "label": "Knowledgebase Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "comments",
          "label": "Comments",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "accepted_answer",
          "label": "Accepted Answer",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "detailed_answers",
          "label": "Detailed Answers",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "knowledge_full_category",
          "label": "Knowledge Full Category",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "knowledge_label",
          "label": "Knowledge Label",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "top_answer",
          "label": "Top Answer",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "view_href", // description
          "label": "View Href",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1,
          "isMasked": 0
        }
      ]
    }
  ]
};



