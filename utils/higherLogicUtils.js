let request = require('request');
var commonFunctions = require('./commonFunctions');
var elastic = require('elasticsearch');
const aes256 = require('nodejs-aes256');

var client = new elastic.Client({
    host: 'http://' + config.get('elasticIndexCS.host') + ':' + config.get('elasticIndexCS.port')
  });



let { HLDiscussion,
    HLEvent,
    HLLibraryDocument,
    HLBlog,
    HLCommunity} = require('../routes/crawlers/elastic-mapper/higherLogic.mapper');




let baseUrl = 'https://api.connectedcommunity.org/api/v2.0';

let requestOptions = {
    headers: { HLIAMKey: '', HLAuthToken: '' },
    json: true
}

let userAuth = { authorization: {} };
let elasticIndexName = '';

const authenticateUser =  (resultData,isConnect) => {
    return new Promise((resolve,reject)=>{
        let options = {
            url :`${baseUrl}/Authentication/Login`,
            method: 'POST',
            headers:{
                HLIAMKey: resultData.authorization.client_secret,
            },
            body:{
                username:resultData.authorization.user_name,
                password:resultData.authorization.password
            },
            json: true,
        };

        request(options, async (error, response) =>{
            if (error) return reject(error)
            else {
                if (response.statusCode != 200) {
                    reject({status_code:response.statusCode,status_message:response.statusMessage});
                }
                else {
                    commonFunctions.errorlogger.info(response.body);
                    resultData.authorization.accessToken = response.body.Token;
                    resultData.authorization.username = resultData.authorization.user_name;
                    delete resultData.authorization.user_name;
                    if (isConnect) {
                        let communityIds;
                        try {
                            communityIds = await fetchAllCommunities(resultData.authorization.client_secret, response.body.Token, resultData.contentSource.id);

                        } catch (error) {
                            commonFunctions.errorlogger.error("Error Inside fetchAllCommunities :: ", error);
                        }
                        resultData.spacesORboards = communityIds;
                    }
                    resolve(resultData);
                }
            }
        });

    });

}

const fetchAllCommunities = (HLIAMKey,HLAuthToken,contentSourceId)=>{
    return new Promise((resolve,reject)=>{
        let options = {
            url :`${baseUrl}/Communities/GetViewableCommunities?includeStatistics=true`,
            method: 'GET',
            headers:{
                HLIAMKey: HLIAMKey,
                HLAuthToken: HLAuthToken
            },
            json: true,
        };

        request(options, (error, response) =>{
            if (error) return reject(error)
            else{
                if (response.statusCode != 200) {
                    resolve([]);
                }else{
                    let resToSend = response.body.map((com)=>{
                        return {
                                content_source_id: contentSourceId,
                                spaceName:com.CommunityName,
                                spaceUrl:com.CommunityName,
                                spaceId:`${com.CommunityKey},${com.DiscussionKey},${com.LibraryKey},${com.Statistics.DiscussionPostCount},${com.CommunitySize}`,
                                isSelected:0,
                                spaceKey: "Community",
                                spaceType:"Subscribed"
                            }
                    })
                    resolve(resToSend);
                }

            }
        })

    });

} 


const refereshToken = (authData, req) =>{
    return new Promise( async (resolve,reject)=>{
        try {
            let result = await authenticateUser(authData,false);
            var param = [];
            param.push(aes256.encrypt(config.get("elasticIndex.encryptionKey"), result.authorization.accessToken));
            const sqlUpdate = "UPDATE content_source_authorization set accessToken = ? where content_source_id =" + authData.authorization.content_source_id;
            connection[req.headers['tenant-id']].execute.query(sqlUpdate, param, (err, data)=> {

                if (err) {
                    reject(err);
                    commonFunctions.errorlogger.error("Access token is not updated for Higher Logic content_source_id "+authData.authorization.content_source_id +"err==>>"+err);
                } else {
                    resolve(result.authorization.accessToken)
                }
            });
            
        } catch (error) {
            reject(error);
        }


    });
    
}

const getAuthAccessSession = (contentSourceId) => {
    return new Promise((resolve,reject)=>{
        commonFunctions.getContentSourceDataById(contentSourceId, async (err, auth) => {
            if (err) commonFunctions.crawlerlogger.error("Error inside getContentSourceDataById :: ", err);
            else if(typeof auth === "object"){
                userAuth.authorization.user_name = auth.authorization.username;
                userAuth.authorization.password = auth.authorization.password;
                userAuth.authorization.client_secret = auth.authorization.client_secret;
                userAuth.authorization.content_source_id = contentSourceId;
                requestOptions.headers.HLIAMKey = auth.authorization.client_secret;
                requestOptions.headers.HLAuthToken = auth.authorization.accessToken;
                elasticIndexName = auth.contentSource.elasticIndexName;
                // content_source_data = auth.contentSource;
                resolve('done');
            }
            else{
                reject("Error in getAuthAccessToken");
            }

        });
    });
}

function getRequest(options,req) {
    return new Promise((resolve, reject) => {
        request(options, async (err, response) => {
            if (err) return reject(err);
            if (response.statusCode === 401) {
                try {
                    let accessToken = await refereshToken(userAuth, req);
                    requestOptions.headers.HLAuthToken = accessToken;
                    resolve("done");
                } catch (error) {
                    reject(error)
                }

            }
            if(response.statusCode === 504){
                resolve([]);
                return;
            }
            else if(response.body.length > 100){
                let [responseToSend,startIndex,endIndex] = [[],0,100];
                for(let i = 0 ; i < Math.ceil(response.body.length/100); i++){
                    responseToSend.push(response.body.slice(startIndex,endIndex));
                    startIndex += 100;
                    endIndex += 100;
                }
                resolve(responseToSend);
            }else resolve([response.body]);
        });
    });
}

function saveDataToElastic(data, indexType) {
    return new Promise((resolve, reject) => {
        commonFunctions.BulkUpload(data, (err, result) => {
            if (err) reject(err);
            else resolve(result);
        });
    });
}


/**
 * web hooks 
 */
function deleteDocumentFromElastic(indexName,type,docId){
    return new Promise(async (resolve,reject)=>{
        try {
            
            let response = await client.delete({index: indexName,type: type,id:docId});
            console.log(response);
            resolve(response);
        } catch (error) {
            reject(error);
            commonFunctions.crawlerlogger.error("Error Inside deleteDocumentFromElastic :: ", error);
        }
    });
}


const getBlogByKey = (contentSourceId, blogKey, attempt,req) => {
    return new Promise(async (resolve, reject) => {
        try {
            if(attempt > 1){
                console.error("refresh token atempt exceeded");
                return resolve("refresh token atempt exceeded");
            }
            await getAuthAccessSession(contentSourceId);
            const csObjsAndFields = await getCSObjsAndFields(contentSourceId,req);
            const objFileds = await getObjAndFieldsFormat(csObjsAndFields);
            const spacesAndBoards = await getSpacesAndBoardsByCSId(contentSourceId,req);

            requestOptions.url = `${baseUrl}/Blogs/GetBlog?blogKey=${blogKey}`;
            requestOptions.method = `GET`;
            let result = await getRequest(requestOptions, req);
            if(result == "done"){
                getBlogByKey(contentSourceId,blogKey, attempt++,req);
                return;
            }
            if (result.length > 0 && isBelongToSelectedCommunity(result[0].Community.CommunityKey,spacesAndBoards)) {
                let hLBlog = new HLBlog(result[0], elasticIndexName, objFileds['blog']);
                let hLBlogStr = hLBlog.getStringBlogs();
                commonFunctions.crawlerlogger.info(`Get Blog By Key :: ${blogKey}`);
                await saveDataToElastic(hLBlogStr, 'blog');
            }
            resolve("done");

        } catch (e) {
            reject(e);
            commonFunctions.crawlerlogger.error("Error Inside getBlogByKey :: ", e);
        }

    });
}


const getEventByKey = (contentSourceId,eventKey,attempt,req) =>{
    return new Promise(async(resolve,reject)=>{
        try {
            if(attempt > 1){
                console.error("refresh token atempt exceeded");
                return resolve("refresh token atempt exceeded");
            }
            await getAuthAccessSession(contentSourceId);
            const csObjsAndFields = await getCSObjsAndFields(contentSourceId,req);
            const objFileds = await getObjAndFieldsFormat(csObjsAndFields);
            const spacesAndBoards = await getSpacesAndBoardsByCSId(contentSourceId,req);

            requestOptions.url = `${baseUrl}/Events/GetEvent?eventKey=${eventKey}`;
            requestOptions.method = `GET`;
            let result = await getRequest(requestOptions, req);
            if(result == "done"){
                getEventByKey(contentSourceId,eventKey, attempt++ , req);
                return;
            }
            if (result.length > 0 && isBelongToSelectedCommunity(result[0].Community.CommunityKey,spacesAndBoards)) {
                let hLEvent = new HLEvent(result, elasticIndexName, objFileds['event']);
                let hLEventStr = hLEvent.getStringEvents();
                commonFunctions.crawlerlogger.info(`Get Event By Key :: ${eventKey}`);
                await saveDataToElastic(hLEventStr, 'event');
            }
            resolve("done");

        } catch (error) {
            reject(error);
            commonFunctions.crawlerlogger.error("Error Inside getEventByKey :: ", error);
        }
    });

}


const getLibraryByKey = (contentSourceId, libKey, comm, attempt,req) => {
    return new Promise(async (resolve, reject) => {
        try {
            if(attempt > 1){
                console.error("refresh token atempt exceeded");
                return resolve("refresh token atempt exceeded");
            }
            await getAuthAccessSession(contentSourceId);
            const csObjsAndFields = await getCSObjsAndFields(contentSourceId,req);
            const objFileds = await getObjAndFieldsFormat(csObjsAndFields);
            const spacesAndBoards = await getSpacesAndBoardsByCSId(contentSourceId,req);

            requestOptions.url = `${baseUrl}/ResourceLibrary/GetLibraryDocument?documentKey=${libKey}`;
            requestOptions.method = `GET`;
            let result = await getRequest(requestOptions, req);
            if(result == "done"){
                getLibraryByKey(contentSourceId,libKey, comm, attempt++);
                return;
            }
            if (result.length > 0 && isBelongToSelectedCommunity(comm.community_key,spacesAndBoards)) {
                let hLLibrary = new HLLibraryDocument(result, elasticIndexName, comm, objFileds['library']);
                let hLLibraryStr = hLLibrary.getStringLibraries();
                commonFunctions.crawlerlogger.info(`Get Library By Key :: ${libKey}`);
                await saveDataToElastic(hLLibraryStr, 'library');
            }
            resolve("done");

        } catch (e) {
            reject(e);
            commonFunctions.crawlerlogger.error("Error Inside getBlogByKey :: ", e);
        }

    });
}


const getMessageThreadByKey = (contentSourceId, threadKey,comm, attempt,req) => {
    return new Promise(async (resolve, reject) => {
        try {
            if(attempt > 1){
                console.error("refresh token atempt exceeded");
                return resolve("refresh token atempt exceeded");
            }
            await getAuthAccessSession(contentSourceId);
            const csObjsAndFields = await getCSObjsAndFields(contentSourceId,req);
            const objFileds = await getObjAndFieldsFormat(csObjsAndFields);
            const spacesAndBoards = await getSpacesAndBoardsByCSId(contentSourceId,req);

            requestOptions.url = `${baseUrl}/Discussions/GetDiscussionPosts?discussionThreadKey=${threadKey}`;
            requestOptions.method = `GET`;
            let result = await getRequest(requestOptions, req);
            if(result == "done"){
                getMessageThreadByKey(contentSourceId, threadKey, comm, attempt++ ,req);
                return;
            }
            if (result.length > 0 && isBelongToSelectedCommunity(comm.community_key,spacesAndBoards)) {
                let hLDiscussion = new HLDiscussion(result[0], comm, elasticIndexName,objFileds['discussion']);
                let hLDiscussionStr = hLDiscussion.getStringDiscussions();
                commonFunctions.crawlerlogger.info(`Get Discussion By Key :: ${threadKey}`);
                await saveDataToElastic(hLDiscussionStr, 'dicussion');
            }
            resolve("done");

        } catch (e) {
            reject(e);
            commonFunctions.crawlerlogger.error("Error Inside getBlogByKey :: ", e);
        }

    });
}


const delByKey = (contentSourceId,key,type) =>{
    return new Promise( async (resolve,reject)=>{
        try {
            await getAuthAccessSession(contentSourceId);
            await deleteDocumentFromElastic(elasticIndexName,type,key);
            resolve(type);
            
        } catch (error) {
            reject(error);
            commonFunctions.crawlerlogger.error(`Error Inside ${type} :: `, error);
        }
    });
}

/**
 * get objects and fields by content source id
 */

const getCSObjsAndFields = (contentSourceId,req) =>{
    return new Promise( async (resolve,reject)=>{
        try {
            commonFunctions.getContentSourceObjectsAndFieldsById(contentSourceId,req,(err,result)=>{
                if(err) return reject(err);
                resolve(result);
            });
        } catch (error) {
            reject(error);
        }

    });
}

const getObjAndFieldsFormat = (objsAndFields) =>{
    let objToReturn = {};
    objsAndFields.forEach((obj)=>{
        objToReturn[obj.name]={};
        obj.fields.forEach((field)=>{
            objToReturn[obj.name][field.name] = field.name;
        });
    });

    return objToReturn;
}

/**
 * 
 * @param {*} time 
 * delay request and wait for an interval
 */
const delay = (time)=>new Promise((resolve)=>setTimeout(resolve, time));

/**
 * 
 * @param {*} contentSourceId 
 * get all selected communities by user
 */
const getSpacesAndBoardsByCSId = (contentSourceId,req) =>{
    return new Promise((resolve,reject)=>{
        let [selectedCount,HLComm,HLAllComm] = [0,new HLCommunity(),new HLCommunity()];
        commonFunctions.getContentSourceSpaceBoardsById(contentSourceId,req, async (err, result) => {
            result.forEach((comm) => {
                let keys = comm.spaceId.split(',');
                let community = {
                    community_key: keys[0],
                    community_name: comm.spaceName,
                    discussion_key: keys[1],
                    library_key: keys[2],
                    discussion_post_count : keys[3],
                    community_size: keys[4]
                }
                if(comm.isSelected === 1){
                    HLComm.pushCommunity(community);
                    selectedCount++;
                }

                HLAllComm.pushCommunity(community);
            });
            if(selectedCount === 0){
                HLComm = HLAllComm;
            }
            resolve(HLComm);
        });
    })
} 

/**
 * 
 * @param {Community Key} communityKey 
 * @param {Array of selected communities} hlComm
 * 
 * True if selected community belogs else False 
 */
const isBelongToSelectedCommunity = (communityKey,hlComm) => hlComm.getCommunityList().some((comm)=>comm.community_key === communityKey);

module.exports = {
    getBlogByKey,
    getEventByKey,
    getLibraryByKey,
    getMessageThreadByKey,
    delByKey,
    authenticateUser,
    fetchAllCommunities,
    getCSObjsAndFields,
    getObjAndFieldsFormat,
    delay,
    getSpacesAndBoardsByCSId,
    isBelongToSelectedCommunity
}
