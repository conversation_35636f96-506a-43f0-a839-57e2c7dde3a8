if [ $# -eq 0 ]
  then
    echo "No arguments supplied"
    exit
fi
echo "Starting script to update mapping"
echo "Reindexing data to temporary index "$1"_temp"

curl -X POST "localhost:8035/_reindex" -H 'Content-Type: application/json' -d'
{
  "source": {
    "index": "'"$1"'"
  },
  "dest": {
    "index": "'"$1"'_temp"
  }
}'
echo "Reindexing to "$1"_temp completed."
curl -X POST localhost:8035/$1_temp/_refresh

echo "Deleting "$1" index."
curl -XDELETE localhost:8035/$1
echo "Deleted "$1

echo "Updating mapping for "$1
curl -XPUT localhost:8035/$1 -d '{"mappings":{"log":{"properties":{"alive":{"type":"long"},"articleStatus":{"type":"string","index":"not_analyzed"},"articleUrl":{"type":"string","index":"not_analyzed"},"author":{"type":"string","index":"not_analyzed"},"caseId":{"type":"string","index":"not_analyzed"},"caseNumber":{"type":"string"},"caseSubject":{"type":"string","index":"not_analyzed"},"cookie":{"type":"string","index":"not_analyzed"},"es_id":{"type":"string"},"event":{"type":"string","index":"not_analyzed"},"geo":{"type":"geo_point"},"internal":{"type":"boolean"},"referer":{"type":"string","index":"not_analyzed"},"size":{"type":"long"},"subject":{"type":"string"},"taid":{"type":"string","index":"not_analyzed"},"targetUrl":{"type":"string","index":"not_analyzed"},"title":{"type":"string","index":"not_analyzed"},"ts":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"uid":{"type":"string","index":"not_analyzed"},"url":{"type":"string","index":"not_analyzed"}}},"users":{"properties":{"cookie":{"type":"string","index":"not_analyzed"},"domain":{"type":"string","index":"not_analyzed"},"email":{"type":"string"},"emailMD5":{"type":"string","index":"not_analyzed"},"internal":{"type":"boolean"},"taid":{"type":"string","index":"not_analyzed"},"ts":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"uid":{"type":"string","index":"not_analyzed"}}},"adminLog":{"properties":{"info":{"type":"string"},"object":{"type":"string","index":"not_analyzed"},"ts":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"user":{"type":"string","index":"not_analyzed"}}},"search_keyword":{"properties":{"_words_":{"type":"string","analyzer":"custom_lowercase_stemmed","search_analyzer":"custom_lowercase_synonym"},"_suggest_":{"type":"completion","analyzer":"simple","payloads":false,"preserve_separators":true,"preserve_position_increments":true,"max_input_length":50,"context":{"uid":{"type":"category","path":"uid","default":[]}}},"auto_tune_conversion":{"properties":{"conversion_date":{"type":"long"},"es_id":{"type":"string"},"rank":{"type":"string"},"subject":{"type":"string"},"url":{"type":"string"}}},"conversion":{"type":"nested","properties":{"convSub":{"type":"string"},"convUrl":{"type":"string"},"conversion_date":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"cookie":{"type":"string"},"es_id":{"type":"string","index":"not_analyzed"},"id":{"type":"string"},"index":{"type":"string"},"internal":{"type":"boolean"},"rank":{"type":"short"},"search_id":{"type":"long"},"subject":{"type":"string","index":"not_analyzed"},"type":{"type":"string"},"uid":{"type":"string"},"url":{"type":"string","index":"not_analyzed"},"window_url":{"type":"string"}}},"cookie":{"type":"string","index":"not_analyzed"},"entry":{"type":"string"},"exactPhrase":{"type":"string"},"filters":{"type":"nested","properties":{"name":{"type":"string","index":"not_analyzed"},"selectedValues":{"type":"string","index":"not_analyzed"}}},"geo":{"type":"geo_point"},"internal":{"type":"boolean"},"isClicked":{"type":"boolean"},"isFreshConversion":{"type":"boolean"},"isFreshSearch":{"type":"boolean"},"page_no":{"type":"short"},"query":{"properties":{"match":{"properties":{"widow_url":{"type":"string"}}}}},"result_count":{"type":"short"},"search_date":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"search_type":{"type":"boolean"},"taid":{"type":"string","index":"not_analyzed"},"text_entered":{"type":"string","index":"not_analyzed","copy_to":["_words_","_suggest_"]},"uid":{"type":"string","index":"not_analyzed"},"window_url":{"type":"string","index":"not_analyzed"},"withOneOrMore":{"type":"string"},"withoutTheWords":{"type":"string"}}},"abTesting":{"properties":{"alive":{"type":"long"},"cookie":{"type":"string","index":"not_analyzed"},"esid":{"type":"string"},"event":{"type":"string","index":"not_analyzed"},"feedback":{"type":"string"},"helpful":{"type":"long"},"internal":{"type":"boolean"},"pageCategory":{"type":"string"},"referer":{"type":"string","index":"not_analyzed"},"taid":{"type":"string","index":"not_analyzed"},"title":{"type":"string","index":"not_analyzed"},"ts":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"uid":{"type":"string","index":"not_analyzed"},"url":{"type":"string","index":"not_analyzed"}}},"supportLog":{"properties":{"caseUid":{"type":"string","index":"not_analyzed"},"cookie":{"type":"string","index":"not_analyzed"},"event":{"type":"string","index":"not_analyzed"},"internal":{"type":"boolean"},"ts":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"uid":{"type":"string","index":"not_analyzed"}}}},"settings":{"index":{"analysis":{"filter":{"my_custom_stop":{"type":"stop","stopwords_path":"stopwords.txt"},"synonym":{"type":"synonym","synonyms_path":"synonyms.txt"},"my_stop":{"type":"stop","stopwords":"_english_"}},"analyzer":{"custom_lowercase_stemmed":{"filter":["lowercase","my_stop","my_custom_stop"],"tokenizer":"standard"},"custom_lowercase_synonym":{"filter":["lowercase","my_stop","my_custom_stop","synonym"],"tokenizer":"standard"}}}}}}'
echo "Mapping updated for "$1

echo "Reindexing back to original Index"
curl -X POST "localhost:8035/_reindex" -H 'Content-Type: application/json' -d'
{
  "source": {
    "index":  "'"$1"'_temp"
  },
  "dest": {
    "index": "'"$1"'"
  }
}'
curl -X POST localhost:8035/$1/_refresh
echo "Reindexing Completed"