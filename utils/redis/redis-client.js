const { promisify } = require('util');
const redis = require('redis');
const config = require('config')
  .get('redis');
const redisCommandWaitTimeInMS = require('config').get('redisCommandWaitTimeInMS');
var commonFunctions = require("../commonFunctions");
commonFunctions.errorlogger.log(process.env.NODE_ENV);

function wrapper(f) {
  return async function() {
      try {
        if(!this.closing){ 

          const timeoutWrap=  new Promise((resolve, reject) => {

            const timeoutId = setTimeout(() => {
              clearTimeout(timeoutId);
              reject(new Error('redis did not respond within 100ms'));
          }, redisCommandWaitTimeInMS);

          f.apply(this, arguments).then(res => {
            clearTimeout(timeoutId);
            resolve(res); })
          .catch(error=>{ 
            clearTimeout(timeoutId);
            reject(error);});

          });

          return await timeoutWrap; 
        }
        commonFunctions.errorlogger.error('redis client closed');
      } catch(e) {
        commonFunctions.errorlogger.error('error while running redis command',e);
      }
      return undefined;
  }
}

const connectionOptions = {
retry_strategy: (options) => {
  if (options.attempt > 1) {
    return undefined;
  }
  return Math.min(options.attempt * 100, 3000);
}
};

if (config.get('redisHost')) {
  connectionOptions.host = config.get('redisHost');
}

if (config.get('redisPort')) {
  connectionOptions.port = config.get('redisPort');
}

const createRedisClient = () =>{
const redisClient = redis.createClient({
  ...connectionOptions
});

redisClient.mset = wrapper(promisify(redisClient.mset).bind(redisClient));
redisClient.set = wrapper(promisify(redisClient.set).bind(redisClient));
redisClient.getAsync = promisify(redisClient.get).bind(redisClient);
redisClient.mgetAsync = promisify(redisClient.mget).bind(redisClient);
redisClient.keysAsync = promisify(redisClient.keys).bind(redisClient);
redisClient.delAsync = promisify(redisClient.del).bind(redisClient);
redisClient.pttl = promisify(redisClient.pttl).bind(redisClient);
redisClient.hget = promisify(redisClient.hget).bind(redisClient);
redisClient.exists = promisify(redisClient.exists).bind(redisClient);
redisClient.hincrbyAsync = promisify(redisClient.hincrby).bind(redisClient);
redisClient.hgetallAsync = promisify(redisClient.hgetall).bind(redisClient);
redisClient.hdelAsync = promisify(redisClient.hdel).bind(redisClient);
redisClient.hmgetAsync = promisify(redisClient.hmget).bind(redisClient);
redisClient.hsetAsync = promisify(redisClient.hset).bind(redisClient);
return redisClient;
};

const redisClient = createRedisClient();

module.exports = {  createRedisClient, redisClient };
