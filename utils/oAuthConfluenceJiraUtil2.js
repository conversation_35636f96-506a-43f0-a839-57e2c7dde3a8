"use strict";

const axios = require('axios');
const constants = require('../constants/constants');
const commonFunctions = require('../utils/commonFunctions');
const config = require('config');

// Core packages
const url = require('url');
const jiraConfluenceAuthIntermediate = function (contentSource, authorization, req, callback) {

  const contentSourceName = constants.CONTENT_SOURCE_TYPE_NAME[contentSource.content_source_type_id];


  const callback_url = ((config.get('adminURL').includes('localhost'))? 'http://localhost/backend': config.get('adminURL')) 
  + '/admin/authorization/oAuthCallback' + ((contentSource.content_source_type_id===6)? "Jira":"Confluence");

  const jiraScope = 'offline_access read:jira-work manage:jira-project manage:jira-configuration ' +
    'read:jira-user write:jira-work manage:jira-webhook manage:jira-data-provider';
  const confluenceScope = 'offline_access read:confluence-space.summary ' +
    'read:confluence-content.all read:confluence-content.summary search:confluence ' +
    'read:confluence-content.permission read:confluence-user readonly:content.attachment:confluence ' +
    'read:confluence-groups';
  const scope = contentSourceName==='jira'?jiraScope:confluenceScope;
  const clientId = authorization.client_id;
  const clientSecret = authorization.client_secret;

    const objectToPass = {
        oauth: {
            client_id: clientId,
            client_secret: clientSecret,
            scope: scope,
            callback_url
        }
    };
    getAuthorizeURL(objectToPass, function (error, oauthResult) {
    if(error) {
        commonFunctions.errorlogger.error("Error getAuthUrl: ",error);
        return callback({ "error": "Failed" });
    }
    authorization.client_id= objectToPass.oauth.client_id;
    authorization.client_secret= objectToPass.oauth.client_secret;
    authorization.callBackUrl= objectToPass.oauth.callback_url;
    authorization.content_source_id= contentSource.id;

      commonFunctions.insertUpdateAuthorization(authorization,req,function(error,result) {
        if(error) {
            callback({"error": "Failed"});
        } else {
            callback({"oauth": oauthResult});
        }
      });
  });
};

const getAuthorizeURL = function (config, callback) {
    const client_id = config.oauth.client_id;

    const authURL = url.format({
        protocol: 'https',
        hostname: "auth.atlassian.com",
        pathname: '/authorize',
        query: {
            audience: 'api.atlassian.com',
            client_id,
            redirect_uri: encodeURI(config.oauth.callback_url),
            scope: config.oauth.scope,
            response_type: 'code',
            prompt: 'consent'
        }
    });
    return callback(null,{url: authURL});
};

const getOAuthAccessToken = function (config) {
    const authUrl = url.format({
        protocol: 'https',
        hostname: 'auth.atlassian.com',
        pathname: '/oauth/token',
        query: {
            grant_type: "authorization_code",
            client_id: config.client_id,
            client_secret: config.client_secret,
            code: config.code,
            redirect_uri: config.callback_url
        }
    });

    return axios.post(authUrl);
};

const getRefreshToken = function(config) {
    const authUrl = url.format({
        protocol: 'https',
        hostname: 'auth.atlassian.com',
        pathname: '/oauth/token',
        query: {
            grant_type: "refresh_token",
            client_id: config.client_id,
            client_secret: config.client_secret,
            refresh_token: config.refresh_token,
            redirect_uri: config.callback_url
        }
    });
    return axios.post(authUrl);
};

const getCloudId = function(config) {
    return axios.get("https://api.atlassian.com/oauth/token/accessible-resources", {
        headers: {
            'Authorization': `Bearer ${config.access_token}`
        }
    });
};

exports.jiraConfluenceAuthIntermediate = jiraConfluenceAuthIntermediate;
exports.getAuthorizeURL = getAuthorizeURL;
exports.getOAuthAccessToken = getOAuthAccessToken;
exports.getRefreshToken = getRefreshToken;
exports.getCloudId = getCloudId;


