

module.exports = {
    "objects": [{
        "name": "items",
        "label": "Items",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [{
        "name": "id",
        "label": "id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "name",
        "label": "name",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "state",
        "label": "Item State",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "board_name",
        "label": "Board Name",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },{
        "name": "board_id",
        "label": "Board Id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "post_time",
        "label": "Created date",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1,
        "isMerged":1
    },
    {
        "name": "updated_at",
        "label": "Update date",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "updates_text_body",
        "label": "Update Body",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },{
        "name": "updates_replies_text_body",
        "label": "Update replies body",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },{
        "name": "updates_replies_text",
        "label": "Update replies text",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },{
        "name": "updates_replies_value",
        "label": "Update replies value",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },{
        "name": "board_permissions",
        "label": "Board permission",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },{
        "name": "board_workspace_id",
        "label": "WorkSpace Id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },{
        "name": "board_workspace_name",
        "label": "WorkSpace Name",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    }]
}