swagger: "2.0"
info:
  description: "This is a SearchUnify Analytics server.  You can find out more about\
    \ SearchUnify analytics api at [http://help.searchunify.com](http://help.searchunify.com)\
    \ or on [irc.freenode.net, #swagger](http://swagger.io/irc/).      For this sample,\
    \ you can use the api key `special-key` to test the authorization     filters."
  version: "1.0.0"
  title: "SearchUnify Analytics API"
  termsOfService: "http://swagger.io/terms/"
  contact:
    email: "<EMAIL>"
  license:
    name: "Apache 2.0"
    url: "http://www.apache.org/licenses/LICENSE-2.0.html"
host: "trial.searchunify.com"
tags:
- name: "/v2/searchQuery"
  description: "Everyting about the search query in your SearchUnify box"
- name: "/v2/searchConversion"
  description: "Access to all clicked search records"
- name: "/v2/searchSession"
  description: "Operations about Search User"
- name: "/v2/searchSessionForCase"
  description: "searchSession for Cases"
- name: "/v2/caseAnalytics"
  description: "case analytics corresponding to different sessions"
- name: "v2_search/searchResults"
  description: "Access your search results"
- name: "/v2_cs/contentSource"
  description: "get content Source Information"
- name: "/v2_cs/apiData"
  description: "Api Data saved in Content Sources"
- name: "/v2/session"
  description: "Get sessions details"
- name: "/v2/agentHelper"
  description: "Agent Helper Data saved in Search Sources"
schemes:
- "https"
paths:
  # /v2/searchQuery/all:
    # get:
    #   tags:
    #   - "v2/searchQuery"
    #   summary: "getAllSearchQueries"
    #   description: "Get all search queries on selected filters"
    #   operationId: "searchQueryAll"
    #   produces:
    #   - "application/xml"
    #   - "application/json"
    #   parameters:
    #   - name: "startDate"
    #     in: "query"
    #     description: "Status values that need to be considered for filter"
    #     required: true
    #     type: "string"
    #     collectionFormat: "multi"
    #   - name: "endDate"
    #     in: "query"
    #     description: "Status values that need to be considered for filter"
    #     required: true
    #     type: "string"
    #     collectionFormat: "multi"
    #   - name: "count"
    #     in: "query"
    #     description: "Status values that need to be considered for filter"
    #     required: true
    #     type: "string"
    #     collectionFormat: "multi"
    #   - name: "searchClientId"
    #     in: "query"
    #     description: "Status values that need to be considered for filter"
    #     required: false
    #     type: "string"
    #     collectionFormat: "multi"
    #   responses:
    #     200:
    #       description: "successful operation"
    #       schema:
    #         type: "array"
    #         items:
    #           $ref: "#/definitions/SearchQuery"
    #     400:
    #       description: "Invalid status value"
    #   security:
    #   - searchunify_auth:
    #     - "read:searchQuery"
    #   x-swagger-router-controller: "SearchQuery"
  # /v2/searchQuery/withResults:
  #   get:
  #     tags:
  #     - "v2/searchQuery"
  #     summary: "get All Search Queries which produced results"
  #     description: "Get all search queries which produced results on selected filters"
  #     operationId: "withResults"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchQuery"
  # /v2/searchQuery/withoutResults:
  #   get:
  #     tags:
  #     - "v2/searchQuery"
  #     summary: "get All Search Queries which did not produce results"
  #     description: "Get all search queries which did not produce results on selected\
  #       \ filters"
  #     operationId: "withoutResults"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchQuery"
  # /v2/searchQuery/withNoClicks:
  #   get:
  #     tags:
  #     - "v2/searchQuery"
  #     summary: "Get All Search Queries which produced results but no conversions"
  #     description: "Get All Search Queries which produced results but no conversions"
  #     operationId: "withNoClicks"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchQuery"
  # /v2/searchQuery/bySessionId/{searchSessionId}:
  #   get:
  #     tags:
  #     - "v2/searchQuery"
  #     summary: "Get All Search Queries which produced results but no conversions"
  #     description: "Get All Search Queries which produced results but no conversions"
  #     operationId: "searchQueryBySessionId"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchSessionId"
  #       in: "path"
  #       description: "sessionid of the user"
  #       required: true
  #       type: "string"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchQuery"
  # /v2/searchQuery/histogram:
  #   post:
  #     tags:
  #     - "v2/searchQuery"
  #     operationId: "searchHistogram"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #       200:
  #         description: "successful operation"
  #     x-swagger-router-controller: "SearchQuery"
  # /v2/searchQuery/missedQueryhistogram:
    # post:
    #   tags:
    #   - "v2/searchQuery"
    #   operationId: "missedQueryhistogram"
    #   consumes:
    #   - "application/json"
    #   - "application/octet-stream"
    #   produces:
    #   - "application/json"
    #   - "application/octet-stream"
    #   parameters:
    #   - in: "body"
    #     name: "body"
    #     description: "Status values that need to be considered for response"
    #     schema:
    #       type: "object"
    #       properties:
    #         startDate:
    #           type: "string"
    #           example:
    #             "1517288850000"
    #         endDate:
    #           type: "string"
    #           example:
    #             "1517318249999"
    #   responses:
    #     400:
    #       description: "Invalid status value"
    #     200:
    #       description: "successful operation"
    #   x-swagger-router-controller: "SearchQuery"
  /v2/recentSearchQuery/recent:
    get:
      tags:
      - "v2/searchQuery"
      summary: "getRecentSearchQueries"
      description: "Get Recent Search Queries"
      operationId: "searchQueryRecent"
      produces:
      - "application/json"
      parameters:
      - name: "searchClientUid"
        in: "query"
        description: "Search Client Uid"
        required: true
        type: "string"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
            properties:
              reponse:
                type: "array"
                items:
                  $ref: "#/definitions/RecentBulkItem"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth:
        - "read:searchQuery"
      x-swagger-router-controller: "SearchQuery"
  # /v2/searchQuery/kcsSupport:
  #   post:
  #     tags:
  #     - "v2/searchQuery"
  #     operationId: "kcsSupportArticles"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #       200:
  #         description: "successful operation"
  #     x-swagger-router-controller: "SearchQuery"
  /v2/searchQuery/updatedKcsSupportArticles:
    post:
      tags:
      - "v2/searchQuery"
      operationId: "updatedKcsSupportArticles"
      consumes:
      - "application/json"
      - "application/octet-stream"
      produces:
      - "application/json"
      - "application/octet-stream"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        schema:
          type: "object"
          properties:
            startDate:
              type: "string"
              example:
                "1517288850000"
            endDate:
              type: "string"
              example:
                "1517318249999"
      responses:
        400:
          description: "Invalid status value"
        200:
          description: "successful operation"
      x-swagger-router-controller: "SearchQuery"
  # /v2/searchConversion/all:
  #   get:
  #     tags:
  #     - "v2/searchConversion"
  #     summary: "getAllSearchQueries"
  #     description: "Get all search queries on selected filters"
  #     operationId: "searchConversionAll"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchConversion"
  # /v2/searchConversion/DiscussionsReadyToBecomeArticles:
  #   get:
  #     tags:
  #     - "v2/searchConversion"
  #     summary: "get All Search Queries which produced results"
  #     description: "Get all search queries which produced results on selected filters"
  #     operationId: "discussionsReadyToBecomeArticles"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchConversion"
  # /v2/searchConversion/notOnFirstPage:
  #   get:
  #     tags:
  #     - "v2/searchConversion"
  #     summary: "get All Search Queries which did not produce results"
  #     description: "Get all search queries which did not produce results on selected\
  #       \ filters"
  #     operationId: "notOnFirstPage"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchConversion"
  # /v2/searchConversion/bySessionId/{searchSessionId}:
  #   get:
  #     tags:
  #     - "v2/searchConversion"
  #     summary: "Get All Search Queries which produced results but no conversions"
  #     description: "Get All Search Queries which produced results but no conversions"
  #     operationId: "searchConversionBySessionId"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchSessionId"
  #       in: "path"
  #       description: "sessionid of the user"
  #       required: true
  #       type: "string"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchConversion"
  # /v2/searchConversion/withFilters:
  #   get:
  #     tags:
  #     - "v2/searchConversion"
  #     summary: "Get all conversions with filters"
  #     description: "Get all conversions with filters"
  #     operationId: "searchConversionWithFilters"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     - "text/csv"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchSessionId"
  #       in: "query"
  #       description: "sessionid of the user"
  #       required: false
  #       type: "string"
  #     - name: "csv"
  #       in: "query"
  #       description: "export value for report"
  #       required: false
  #       type: "string"
  #     - name: "internalUser"
  #       in: "query"
  #       description: "export value for report"
  #       required: false
  #       type: "string"
  #     responses:
  #       200:
  #         description: "successful operation"
  #       400:
  #         description: "Invalid status value"
  #     x-swagger-router-controller: "SearchConversion"
  # /v2/searchConversion/withFiltersCSV:
    # post:
    #   tags:
    #   - "v2/SearchConversion"
    #   operationId: "searchConversionWithFiltersCSV"
    #   consumes:
    #   - "application/json"
    #   - "application/octet-stream"
    #   produces:
    #   - "application/json"
    #   - "application/octet-stream"
    #   parameters:
    #   - in: "body"
    #     name: "body"
    #     description: "Status values that need to be considered for response"
    #     schema:
    #       type: "object"
    #       properties:
    #         startDate:
    #           type: "string"
    #           example:
    #             "1517288850000"
    #         endDate:
    #           type: "string"
    #           example:
    #             "1517318249999"
    #   responses:
    #     400:
    #       description: "Invalid status value"
    #     200:
    #       description: "successful operation"
    #   x-swagger-router-controller: "SearchConversion"
  # /v2/searchConversion/topSearchSessions:
  #   post:
  #     tags:
  #     - "v2/SearchConversion"
  #     operationId: "getTopSearchSessions"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #           uid:
  #             type: "string"
  #             example:
  #               "d405b5f1-5788-11e9-b795-1062e5515733"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       200:
  #         description: "successful operation"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchConversion"
  # /v2/searchConversion/topClickedResults:
  #   post:
  #     tags:
  #     - "v2/SearchConversion"
  #     operationId: "getTopClickedResults"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #           uid:
  #             type: "string"
  #             example:
  #               "d405b5f1-5788-11e9-b795-1062e5515733"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       200:
  #         description: "successful operation"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchConversion"
  # /v2/searchConversion/caseFormPageReport:
  #   post:
  #     tags:
  #     - "v2/SearchConversion"
  #     operationId: "getCaseFormPageReport"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #           uid:
  #             type: "string"
  #             example:
  #               "d405b5f1-5788-11e9-b795-1062e5515733"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       200:
  #         description: "successful operation"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchConversion"   
  # /v2/searchConversion/readyToBecomeHelpArticle:
  #   post:
  #     tags:
  #     - "v2/SearchConversion"
  #     operationId: "readyToBecomeHelpArticle"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #           uid:
  #             type: "string"
  #             example:
  #               "d405b5f1-5788-11e9-b795-1062e5515733"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       200:
  #         description: "successful operation"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchConversion"
  # /v2/searchOverview/getTileData:
  #   post:
  #     tags:
  #     - "v2/SearchOverview"
  #     operationId: "getTileData"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           from:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           to:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #           uid:
  #             type: "string"
  #             example:
  #               "d405b5f1-5788-11e9-b795-1062e5515733"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       200:
  #         description: "successful operation"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchOverview"
  # /v2/searchOverview/getSearchSummaryChart:
  #   post:
  #     tags:
  #     - "v2/SearchOverview"
  #     operationId: "getSearchSummaryChart"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           from:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           to:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #           uid:
  #             type: "string"
  #             example:
  #               "d405b5f1-5788-11e9-b795-1062e5515733"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       200:
  #         description: "successful operation"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchOverview"
  # /v2/searchOverview/getTopSearches:
  #   post:
  #     tags:
  #     - "v2/SearchOverview"
  #     operationId: "getTopSearches"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #           uid:
  #             type: "string"
  #             example:
  #               "d405b5f1-5788-11e9-b795-1062e5515733"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       200:
  #         description: "successful operation"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchOverview"
  # /v2/searchOverview/getTopSuccessfulSearches:
    # post:
    #   tags:
    #   - "v2/SearchOverview"
    #   operationId: "getTopSuccessfulSearches"
    #   consumes:
    #   - "application/json"
    #   - "application/octet-stream"
    #   produces:
    #   - "application/json"
    #   - "application/octet-stream"
    #   parameters:
    #   - in: "body"
    #     name: "body"
    #     description: "Status values that need to be considered for response"
    #     schema:
    #       type: "object"
    #       properties:
    #         startDate:
    #           type: "string"
    #           example:
    #             "1517288850000"
    #         endDate:
    #           type: "string"
    #           example:
    #             "1517318249999"
    #         uid:
    #           type: "string"
    #           example:
    #             "d405b5f1-5788-11e9-b795-1062e5515733"
    #   responses:
    #     400:
    #       description: "Invalid status value"
    #       schema:
    #         type: "array"
    #         items:
    #           $ref: "#/definitions/SearchQuery"
    #     200:
    #       description: "successful operation"
    #   security:
    #   - searchunify_auth: []
    #   x-swagger-router-controller: "SearchOverview"
  /v2/searchOverview/getTypesStatistics:
    post:
      tags:
      - "v2/SearchOverview"
      operationId: "getTypesStatistics"
      consumes:
      - "application/json"
      - "application/octet-stream"
      produces:
      - "application/json"
      - "application/octet-stream"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        schema:
          type: "object"
          properties:
            startDate:
              type: "string"
              example:
                "1517288850000"
            endDate:
              type: "string"
              example:
                "1517318249999"
            uid:
              type: "string"
              example:
                "d405b5f1-5788-11e9-b795-1062e5515733"
      responses:
        400:
          description: "Invalid status value"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/SearchQuery"
        200:
          description: "successful operation"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "SearchOverview"
  /v2/searchOverview/getAddedContent:
    post:
      tags:
      - "v2/SearchOverview"
      operationId: "getAddedContent"
      consumes:
      - "application/json"
      - "application/octet-stream"
      produces:
      - "application/json"
      - "application/octet-stream"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        schema:
          type: "object"
          properties:
            startDate:
              type: "string"
              example:
                "1517288850000"
            endDate:
              type: "string"
              example:
                "1517318249999"
            uid:
              type: "string"
              example:
                "d405b5f1-5788-11e9-b795-1062e5515733"
      responses:
        400:
          description: "Invalid status value"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/SearchQuery"
        200:
          description: "successful operation"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "SearchOverview"
  /v2/searchOverview/getTopUsefulFeaturedSnippet:
    post:
      tags:
      - "v2/SearchOverview"
      operationId: "getTopUsefulFeaturedSnippet"
      consumes:
      - "application/json"
      - "application/octet-stream"
      produces:
      - "application/json"
      - "application/octet-stream"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        schema:
          type: "object"
          properties:
            startDate:
              type: "string"
              example:
                "1517288850000"
            endDate:
              type: "string"
              example:
                "1517318249999"
            uid:
              type: "string"
              example:
                "d405b5f1-5788-11e9-b795-1062e5515733"
      responses:
        400:
          description: "Invalid status value"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/SearchQuery"
        200:
          description: "successful operation"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "SearchOverview"
  /v2/searchOverview/getTopKnowledgeGraphTitle:
    post:
      tags:
      - "v2/SearchOverview"
      operationId: "getTopKnowledgeGraphTitle"
      consumes:
      - "application/json"
      - "application/octet-stream"
      produces:
      - "application/json"
      - "application/octet-stream"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        schema:
          type: "object"
          properties:
            startDate:
              type: "string"
              example:
                "1517288850000"
            endDate:
              type: "string"
              example:
                "1517318249999"
            uid:
              type: "string"
              example:
                "d405b5f1-5788-11e9-b795-1062e5515733"
      responses:
        400:
          description: "Invalid status value"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/SearchQuery"
        200:
          description: "successful operation"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "SearchOverview"
  /v2/searchOverview/getPageRatingFeedback:
    post:
      tags:
      - "v2/SearchOverview"
      operationId: "getPageRatingFeedback"
      consumes:
      - "application/json"
      - "application/octet-stream"
      produces:
      - "application/json"
      - "application/octet-stream"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        schema:
          type: "object"
          properties:
            startDate:
              type: "string"
              example:
                "1517288850000"
            endDate:
              type: "string"
              example:
                "1517318249999"
            uid:
              type: "string"
              example:
                "d405b5f1-5788-11e9-b795-1062e5515733"
      responses:
        400:
          description: "Invalid status value"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/SearchQuery"
        200:
          description: "successful operation"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "SearchOverview"
  /v2/searchContentGapAnalysis/getUnsuccessfulSearchSummaryChart:
    post:
      tags:
      - "v2/SearchContentGapAnalysis"
      operationId: "getUnsuccessfulSearchSummaryChart"
      consumes:
      - "application/json"
      - "application/octet-stream"
      produces:
      - "application/json"
      - "application/octet-stream"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        schema:
          type: "object"
          properties:
            startDate:
              type: "string"
              example:
                "1517288850000"
            endDate:
              type: "string"
              example:
                "1517318249999"
            uid:
              type: "string"
              example:
                "d405b5f1-5788-11e9-b795-1062e5515733"
      responses:
        400:
          description: "Invalid status value"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/SearchQuery"
        200:
          description: "successful operation"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "SearchContentGapAnalysis"
  # /v2/searchContentGapAnalysis/getTopSearchesWithNoClick:
  #   post:
  #     tags:
  #     - "v2/SearchContentGapAnalysis"
  #     operationId: "getTopSearchesWithNoClick"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #           uid:
  #             type: "string"
  #             example:
  #               "d405b5f1-5788-11e9-b795-1062e5515733"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       200:
  #         description: "successful operation"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchContentGapAnalysis"
  # /v2/searchContentGapAnalysis/getTopSearchesWithNoResult:
  #   post:
  #     tags:
  #     - "v2/SearchContentGapAnalysis"
  #     operationId: "getTopSearchesWithNoResult"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #           uid:
  #             type: "string"
  #             example:
  #               "d405b5f1-5788-11e9-b795-1062e5515733"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       200:
  #         description: "successful operation"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchContentGapAnalysis"
  # /v2/searchContentGapAnalysis/getUnsuccessfulSearchSessionSummaryChart:
  #   post:
  #     tags:
  #     - "v2/SearchContentGapAnalysis"
  #     operationId: "getUnsuccessfulSearchSessionSummaryChart"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #           uid:
  #             type: "string"
  #             example:
  #               "d405b5f1-5788-11e9-b795-1062e5515733"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       200:
  #         description: "successful operation"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchContentGapAnalysis"
  # /v2/searchContentGapAnalysis/getTopBackwardDocuments:
    # post:
    #   tags:
    #   - "v2/SearchContentGapAnalysis"
    #   operationId: "getTopBackwardDocuments"
    #   consumes:
    #   - "application/json"
    #   - "application/octet-stream"
    #   produces:
    #   - "application/json"
    #   - "application/octet-stream"
    #   parameters:
    #   - in: "body"
    #     name: "body"
    #     description: "Status values that need to be considered for response"
    #     schema:
    #       type: "object"
    #       properties:
    #         startDate:
    #           type: "string"
    #           example:
    #             "1517288850000"
    #         endDate:
    #           type: "string"
    #           example:
    #             "1517318249999"
    #         uid:
    #           type: "string"
    #           example:
    #             "d405b5f1-5788-11e9-b795-1062e5515733"
    #   responses:
    #     400:
    #       description: "Invalid status value"
    #       schema:
    #         type: "array"
    #         items:
    #           $ref: "#/definitions/SearchQuery"
    #     200:
    #       description: "successful operation"
    #   security:
    #   - searchunify_auth: []
    #   x-swagger-router-controller: "SearchContentGapAnalysis"
  # /v2/searchContentGapAnalysis/getPageTime:
  #   get:
  #     tags:
  #     - "v2/SearchContentGapAnalysis"
  #     operationId: "getPageTime"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "internalUser"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "csv"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       200:
  #         description: "successful operation"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchContentGapAnalysis"
  /v2/searchContentGapAnalysis/kcsSupport:
    post:
      tags:
      - "v2/searchContentGapAnalysis"
      operationId: "kcsSupportArticles"
      consumes:
      - "application/json"
      - "application/octet-stream"
      produces:
      - "application/json"
      - "application/octet-stream"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        schema:
          type: "object"
          properties:
            startDate:
              type: "string"
              example:
                "1517288850000"
            endDate:
              type: "string"
              example:
                "1517318249999"
      responses:
        400:
          description: "Invalid status value"
        200:
          description: "successful operation"
      x-swagger-router-controller: "SearchContentGapAnalysis"
  /v2/searchContentGapAnalysis/getUpdatedArticlesOnCases:
    post:
      tags:
      - "v2/SearchContentGapAnalysis"
      operationId: "getUpdatedArticlesOnCases"
      consumes:
      - "application/json"
      - "application/octet-stream"
      produces:
      - "application/json"
      - "application/octet-stream"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        schema:
          type: "object"
          properties:
            startDate:
              type: "string"
              example:
                "1517288850000"
            endDate:
              type: "string"
              example:
                "1517318249999"
            uid:
              type: "string"
              example:
                "d405b5f1-5788-11e9-b795-1062e5515733"
      responses:
        400:
          description: "Invalid status value"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/SearchQuery"
        200:
          description: "successful operation"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "SearchContentGapAnalysis"
  # /v2/searchSession/all:
  #   get:
  #     tags:
  #     - "v2/searchSession"
  #     summary: "getAllSearchQueries"
  #     description: "Get all search queries on selected filters"
  #     operationId: "searchSessionAll"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "cookie"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchSession"
  # /v2/searchSession/pageTime:
  #   get:
  #     tags:
  #     - "v2/searchSession"
  #     summary: "getAllSearchQueries"
  #     description: "Get all search queries on selected filters"
  #     operationId: "searchSessionPageTime"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "internalUser"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "csv"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchSession"
  # /v2/searchSession/all/searchQuery:
  #   get:
  #     tags:
  #     - "v2/searchSession"
  #     summary: "get All Search Queries which produced results"
  #     description: "Get all search queries which produced results on selected filters"
  #     operationId: "searchSessionAllSearchQuery"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchSession"
  # /v2/searchSession/all/searchConversion:
  #   get:
  #     tags:
  #     - "v2/searchSession"
  #     summary: "get All Search Queries which did not produce results"
  #     description: "Get all search queries which did not produce results on selected\
  #       \ filters"
  #     operationId: "searchSessionAllSearchConversion"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchSession"
  # /v2/searchSession/bySearchSessionId/{searchSessionId}:
  #   get:
  #     tags:
  #     - "v2/searchSession"
  #     summary: "Get All Search Queries which produced results but no conversions"
  #     description: "Get All Search Queries which produced results but no conversions"
  #     operationId: "searchSessionBySearchSessionId"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchSessionId"
  #       in: "path"
  #       description: "sessionid of the user"
  #       required: true
  #       type: "string"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchSession"
  # /v2/searchSession/bySearchSessionId/{searchSessionId}/searchQuery:
  #   get:
  #     tags:
  #     - "v2/searchSession"
  #     summary: "get All Search Queries which produced results"
  #     description: "Get all search queries which produced results on selected filters"
  #     operationId: "searchSessionBySearchSessionIdSearchQuery"
  #     produces:
  #     - "application/xml"
  #     - "application/json"
  #     parameters:
  #     - name: "startDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "endDate"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "count"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: true
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchClientId"
  #       in: "query"
  #       description: "Status values that need to be considered for filter"
  #       required: false
  #       type: "string"
  #       collectionFormat: "multi"
  #     - name: "searchSessionId"
  #       in: "path"
  #       description: "sessionid of the user"
  #       required: true
  #       type: "string"
  #     responses:
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "array"
  #           items:
  #             $ref: "#/definitions/SearchQuery"
  #       400:
  #         description: "Invalid status value"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchSession"
  # /v2/searchSession/bySearchSessionId/{searchSessionId}/searchConversion:
    # get:
    #   tags:
    #   - "v2/searchSession"
    #   summary: "get All Search Queries which did not produce results"
    #   description: "Get all search queries which did not produce results on selected\
    #     \ filters"
    #   operationId: "searchSessionBySearchSessionIdSearchConversion"
    #   produces:
    #   - "application/xml"
    #   - "application/json"
    #   parameters:
    #   - name: "startDate"
    #     in: "query"
    #     description: "Status values that need to be considered for filter"
    #     required: true
    #     type: "string"
    #     collectionFormat: "multi"
    #   - name: "endDate"
    #     in: "query"
    #     description: "Status values that need to be considered for filter"
    #     required: true
    #     type: "string"
    #     collectionFormat: "multi"
    #   - name: "count"
    #     in: "query"
    #     description: "Status values that need to be considered for filter"
    #     required: true
    #     type: "string"
    #     collectionFormat: "multi"
    #   - name: "searchClientId"
    #     in: "query"
    #     description: "Status values that need to be considered for filter"
    #     required: false
    #     type: "string"
    #     collectionFormat: "multi"
    #   - name: "searchSessionId"
    #     in: "path"
    #     description: "sessionid of the user"
    #     required: true
    #     type: "string"
    #   responses:
    #     200:
    #       description: "successful operation"
    #       schema:
    #         type: "array"
    #         items:
    #           $ref: "#/definitions/SearchQuery"
    #     400:
    #       description: "Invalid status value"
    #   security:
    #   - searchunify_auth: []
    #   x-swagger-router-controller: "SearchSession"
  # /v2/searchSession/byCaseUid:
  #   post:
  #     tags:
  #     - "v2/searchSessionForCase"
  #     summary: "get All Search Queries which did not produce results"
  #     description: ""
  #     operationId: "searchSessionByCaseUid"
  #     consumes:
  #     - "application/json"
  #     produces:
  #     - "application/json"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       required: true
  #       schema:
  #         type: "object"
  #         required:
  #           - caseUid
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #           caseUid:
  #             type: "string"
  #             example: "1517288903382324"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #       200:
  #         description: "successful operation"
  #         schema:
  #           type: "object"
  #           properties:
  #             searchInterval:
  #               type: "array"
  #               items:
  #                 $ref: "#/definitions/GanttBlock"
  #             pageViewInterval:
  #               type: "array"
  #               items:
  #                 $ref: "#/definitions/GanttBlock"
  #             supportInterval:
  #               type: "array"
  #               items:
  #                 $ref: "#/definitions/GanttBlock"
  #     security:
  #     - searchunify_auth: []
  #     x-swagger-router-controller: "SearchSessionForCase"
  /v2/searchSession/byCaseUidAuth:
      post:
        tags:
        - "v2/searchSessionForCase"
        summary: "get All Search Queries which did not produce results"
        description: ""
        operationId: "searchSessionByCaseUidAuth"
        consumes:
        - "application/json"
        produces:
        - "application/json"
        parameters:
        - in: "body"
          name: "body"
          description: "Status values that need to be considered for response"
          required: true
          schema:
            type: "object"
            required:
              - caseUid
            properties:
              startDate:
                type: "string"
                example:
                  "1517288850000"
              endDate:
                type: "string"
                example:
                  "1517318249999"
              caseUid:
                type: "string"
                example: "1517288903382324"
        responses:
          400:
            description: "Invalid status value"
          200:
            description: "successful operation"
            schema:
              type: "object"
              properties:
                searchInterval:
                  type: "array"
                  items:
                    $ref: "#/definitions/GanttBlock"
                pageViewInterval:
                  type: "array"
                  items:
                    $ref: "#/definitions/GanttBlock"
                supportInterval:
                  type: "array"
                  items:
                    $ref: "#/definitions/GanttBlock"
        security:
        - searchunify_auth: []
        x-swagger-router-controller: "SearchSessionForCase"
  /v2/searchSession/byCaseUid/searches:
    post:
      tags:
      - "v2/searchSessionForCase"
      summary: "get All Search Queries which did not produce results"
      description: ""
      operationId: "searchSessionByCaseUidSearches"
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        required: true
        schema:
          type: "object"
          required:
            - caseUid
            - startDate
            - endDate
          properties:
            startDate:
              type: "string"
              example: "1517289600000"
            endDate:
              type: "string"
              example: "1517290049999"
            caseUid:
              type: "string"
              example: "1517288903382324"
      responses:
        400:
          description: "Invalid status value"
        200:
          description: "successful operation"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/GanttBlockSearch"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "SearchSessionForCase"
  /v2/searchSession/byCaseUid/views:
    post:
      tags:
      - "v2/searchSessionForCase"
      summary: "get All Search Queries which did not produce results"
      description: ""
      operationId: "searchSessionBySbyCaseUidViews"
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        required: true
        schema:
          type: "object"
          required:
            - caseUid
            - startDate
            - endDate
          properties:
            startDate:
              type: "string"
              example: "1517299800000"
            endDate:
              type: "string"
              example: "1517300399999"
            caseUid:
              type: "string"
              example: "1517288903382324"
      responses:
        400:
          description: "Invalid status value"
        200:
          description: "successful operation"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/GanttBlockViews"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "SearchSessionForCase"
  /v2/searchSession/byCookie:
    post:
      tags:
      - "v2/searchSessionForCase"
      summary: "get All Search Queries which did not produce results"
      description: ""
      operationId: "searchSessionByCookie"
      consumes:
      - "application/json"
      - "application/octet-stream"
      produces:
      - "application/json"
      - "application/octet-stream"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        required: true
        schema:
          type: "object"
          required:
            - cookie
          properties:
            startDate:
              type: "string"
              example:
                "1517288850000"
            endDate:
              type: "string"
              example:
                "1517318249999"
            cookie:
              type: "string"
              example: "1517288903382324"
      responses:
        400:
          description: "Invalid status value"
        200:
          description: "successful operation"
          schema:
            type: "object"
            properties:
              searchInterval:
                type: "array"
                items:
                  $ref: "#/definitions/GanttBlock"
              pageViewInterval:
                type: "array"
                items:
                  $ref: "#/definitions/GanttBlock"
              supportInterval:
                type: "array"
                items:
                  $ref: "#/definitions/GanttBlock"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "SearchSessionForCase"
  # /v2/caseAnalytics/caseSubjects:
  #   post:
  #     tags:
  #     - "v2/caseAnalytics"
  #     operationId: "caseSubjects"
  #     consumes:
  #     - "application/json"
  #     - "application/octet-stream"
  #     produces:
  #     - "application/json"
  #     - "application/octet-stream"
  #     parameters:
  #     - in: "body"
  #       name: "body"
  #       description: "Status values that need to be considered for response"
  #       schema:
  #         type: "object"
  #         properties:
  #           startDate:
  #             type: "string"
  #             example:
  #               "1517288850000"
  #           endDate:
  #             type: "string"
  #             example:
  #               "1517318249999"
  #     responses:
  #       400:
  #         description: "Invalid status value"
  #       200:
  #         description: "successful operation"
  #     x-swagger-router-controller: "caseAnalytics"
  # /v2/caseAnalytics/noCaseCreated:
    # post:
    #   tags:
    #   - "v2/caseAnalytics"
    #   operationId: "noCaseCreated"
    #   consumes:
    #   - "application/json"
    #   - "application/octet-stream"
    #   produces:
    #   - "application/json"
    #   - "application/octet-stream"
    #   parameters:
    #   - in: "body"
    #     name: "body"
    #     description: "Status values that need to be considered for response"
    #     schema:
    #       type: "object"
    #       properties:
    #         startDate:
    #           type: "string"
    #           example:
    #             "1517288850000"
    #         endDate:
    #           type: "string"
    #           example:
    #             "1517318249999"
    #   responses:
    #     400:
    #       description: "Invalid status value"
    #     200:
    #       description: "successful operation"
    #   x-swagger-router-controller: "caseAnalytics"
  /v2/session/log/all:
    get:
      tags:
      - "v2/session"
      summary: "getAllSearchQueries"
      description: "Get all search queries on selected filters"
      operationId: "sessionAll"
      produces:
      - "application/xml"
      - "application/json"
      parameters:
      - name: "startDate"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: true
        type: "string"
        collectionFormat: "multi"
      - name: "endDate"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: true
        type: "string"
        collectionFormat: "multi"
      - name: "uid"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: true
        type: "string"
        collectionFormat: "multi"
      - name: "export"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: "string"
        collectionFormat: "multi"
      - name: "keyword"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: "string"
        collectionFormat: "multi"
      - name: "count"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        minimum: 1
        default: 10
        type: number
        collectionFormat: "multi"
      - name: "startIndex"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        minimum: 0
        default: 0
        type: number
        collectionFormat: "multi"
      - name: "supportvisit"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: boolean
        collectionFormat: "multi"
      - name: "casecreated"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: boolean
        collectionFormat: "multi"
      - name: "orderby"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: "string"
        enum:
          - endTime
          - startTime
        default: "endTime"
      - name: "order"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: "string"
        enum:
          - asc
          - desc
        default: "desc"
      - name: "cookie"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: "string"
        collectionFormat: "multi"
      - name: "internalUser"
        in: "query"
        description: "export value for report"
        required: false
        type: "string"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/SearchQuery"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "Session"
  /v2/getSessionTrackingFormattedResult:
    get:
      tags:
      - "v2/session"
      summary: "getSessionTrackingFormattedResult"
      description: "Get all search queries on selected filters"
      operationId: "getSessionTrackingFormattedResult"
      produces:
      - "application/xml"
      - "application/json"
      parameters:
      - name: "startDate"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: true
        type: "string"
        collectionFormat: "multi"
      - name: "endDate"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: true
        type: "string"
        collectionFormat: "multi"
      - name: "uid"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: true
        type: "string"
        collectionFormat: "multi"
      - name: "export"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: "string"
        collectionFormat: "multi"
      - name: "keyword"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: "string"
        collectionFormat: "multi"
      - name: "count"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        minimum: 1
        default: 10
        type: number
        collectionFormat: "multi"
      - name: "startIndex"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        minimum: 0
        default: 0
        type: number
        collectionFormat: "multi"
      - name: "supportvisit"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: boolean
        collectionFormat: "multi"
      - name: "casecreated"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: boolean
        collectionFormat: "multi"
      - name: "orderby"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: "string"
        enum:
          - endTime
          - startTime
        default: "endTime"
      - name: "order"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: "string"
        enum:
          - asc
          - desc
        default: "desc"
      - name: "cookie"
        in: "query"
        description: "Status values that need to be considered for filter"
        required: false
        type: "string"
        collectionFormat: "multi"
      - name: "internalUser"
        in: "query"
        description: "export value for report"
        required: false
        type: "string"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "array"
            items:
              $ref: "#/definitions/SearchQuery"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "Session"
  /v2/session/log/byCookie:
    post:
      tags:
      - "v2/session"
      summary: "get All Search Queries which did not produce results"
      description: ""
      operationId: "sessionByCookie"
      consumes:
      - "application/json"
      - "application/octet-stream"
      produces:
      - "application/json"
      - "application/octet-stream"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        required: true
        schema:
          type: "object"
          required:
            - cookie
          properties:
            cookie:
              type: "string"
              example: "1517288903382324"
            searchClientId:
              type: "string"
              example: "1c5d8771-2b39-11e8-b12c-06083bffd1c6"
      responses:
        400:
          description: "Invalid status value"
        200:
          description: "successful operation"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "Session"
  /v2_search/searchResults:
    post:
      tags:
      - "v2_search/searchResults"
      summary: "getSearchResults"
      description: "Get all search results"
      operationId: "searchResults"
      produces:
      - "application/xml"
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        required: true
        schema:
          type: "object"
          required:
            - uid
          properties:
            uid:
              type: "string"
              example:
                "49291621-9ad7-11e8-a5ff-40b034e56204"
            aggregations:
              type: "array"
              items:
                  $ref: "#/definitions/Aggregations"
            email:
              type: "string"
              example:
                "<EMAIL>"
            searchString:
              type: "string"
              example:
                "<EMAIL>"
            sortby:
              type: "string"
              example:
                "Relevance"
            orderBy:
              type: "string"
              example:
                "_score"
            UserType:
              type: "string"
              example:
                "Standard"
            AccountID:
              type: "string"
              example:
                "007808900004xHfF9U"
            ProfileID:
              type: "string"
              example:
                "09898033004xHrt"
            ContactId:
              type: "string"
              example:
                "078W000005xcdre"
            UserId:
              type: "string"
              example:
                "0898W050005xcdre"
            boardsArr:
              type: "string"
              example:
                "board1,board2,board3"
            exactPhrase:
              type: "string"
              example:
                "phrase"
            withOneOrMore:
              type: "string"
              example:
                "phrase"
            withoutTheWords:
              type: "string"
              example:
                "phrase"
            from:
              type: "string"
              example:
                "10"
            resultsPerPage:
              type: "string"
              example:
                "20"
            pagingAggregation:
              type: "object"
              items:
                  $ref: "#/definitions/PagingAggregations"
            indexEnabled:
              type: "boolean"
              example:
                "true"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
            items:
              $ref: "#/definitions/SearchResult"
        400:
          description: "Invalid status value"
      x-swagger-router-controller: "SearchResultApi"
  /v2_cs/contentSource/all:
    get:
      tags:
      - "v2_cs/contentSource"
      summary: "getAllContentSourceList"
      description: "Get all Content Sources"
      operationId: "contentSourceListAll"
      produces:
      - "application/json"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
            properties:
              contentSources:
                type: "array"
                items:
                  $ref: "#/definitions/ContentSourceList"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
  /v2_cs/contentSource/{id}:
    get:
      tags:
      - "v2_cs/contentSource"
      summary: "getContentSourceInfo"
      description: "Get all Content Sources"
      operationId: "contentSourceInfo"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "path"
        description: "id of the Content Source"
        required: true
        type: "string"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
            properties:
              id:
                type: "string"
                example: '20'
              label:
                type: "string"
                example: 'Drive cs'
              name:
                type: "string"
                example: 'drive_cs'
              url:
                type: "string"
                example: 'http://drive.img1.com'
              size:
                type: "integer"
                example: 254
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
  /v2_cs/contentSource/{id}/objectAndFields:
    get:
      tags:
      - "v2_cs/contentSource"
      summary: "getContentSource Object Details"
      description: "Get Content Sources Object and field details"
      operationId: "contentSourceObjectDetails"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "path"
        description: "Id of the Content Source"
        required: true
        type: "string"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
            properties:
              items:
                type: "array"
                items:
                  $ref: "#/definitions/ContentSourceObject"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
     
  /v2_cs/contentSource/{id}/getContentSourceFields/{cso_name}:
    get:
      tags:
      - "v2_cs/contentSource"
      summary: "getContentSourceFields Field Details"
      description: "Get Content Sources fields Details"
      operationId: "getContentSourceFields"
      produces:
      - "application/json"
      parameters:
      - name: "id"
        in: "path"
        description: "Id of the Content Source"
        required: true
        type: "string"
      - name: "cso_name"
        in: "path"
        description: "name from the Content Source object"
        required: true
        type: "string"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
            properties:
              items:
                type: "array"
                items:
                  $ref: "#/definitions/ContentSourceObject"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
  /v2_cs/apiData/contentSource/{contentSourceId}/object/{objectId}/get:
    get:
      tags:
      - "v2_cs/apiData"
      summary: "getContentSource Data"
      description: "Get Content Sources data"
      operationId: "contentSourceData"
      produces:
      - "application/json"
      parameters:
      - name: "contentSourceId"
        in: "path"
        description: "Id of the Content Source"
        required: true
        type: "string"
      - name: "objectId"
        in: "path"
        description: "Id of the Object"
        required: true
        type: "string"
      - name: "from"
        in: "query"
        description: "from where we get content source data"
        required: false
        type: "integer"
      - name: "size"
        in: "query"
        description: "No of documents should be fetched from content source "
        required: false
        type: "integer"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
            properties:
              items:
                type: "array"
                items:
                  $ref: "#/definitions/ContentSourceData"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
  /v2_cs/apiData/contentSource/{contentSourceId}/object/{objectId}/document/{documentId}/get:
    get:
      tags:
      - "v2_cs/apiData"
      summary: "getDocumentData"
      description: "Get Content from Document with Id"
      operationId: "getDocumentData"
      produces:
      - "application/json"
      parameters:
      - name: "contentSourceId"
        in: "path"
        description: "Id of the Content Source"
        required: true
        type: "string"
      - name: "objectId"
        in: "path"
        description: "Id of the Object"
        required: true
        type: "string"
      - name: "documentId"
        in: "path"
        description: " Id of the indexed Document"
        required: true
        type: "string"
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
            properties:
              id:
                type: "string"
                example: '20'
              contentSource:
                type: "string"
                example: 'drive'
              object:
                type: "string"
                example: 'file'
              source:
                type: "object"
                example: {"created Date": '20-08-2018',"url": 'localhost:8025/drive.html'}
              found:
                type: "boolean"
                example: true
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
  /v2_cs/apiData/contentSource/{contentSourceId}/object/{objectId}/document/{documentId}/update:
    post:
      tags:
      - "v2_cs/apiData"
      summary: "updateContentSourceDocument"
      description: "Update Content Source Document"
      operationId: "updateContentSourceDocument"
      produces:
      - "application/json"
      parameters:
      - name: "contentSourceId"
        in: "path"
        description: "Id of the Content Source"
        required: true
        type: "string"
      - name: "objectId"
        in: "path"
        description: "Id of the Object"
        required: true
        type: "string"
      - name: "documentId"
        in: "path"
        description: "Id of the Document"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "Json Body"
        required: true
        schema:
          type: "object"
          properties:
            su__arrary_operations:
              type: "object"
              items:
                $ref: "#/definitions/suArrayOpertions"
      - name: "postTimeField"
        in: "query"
        description: "Define field which can be used as post_time"
        required: false
        type: "string"
      responses:
        200:
          description: "successful operation"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
  /v2_cs/apiData/contentSource/{contentSourceId}/object/{objectId}/document/{documentId}/delete:
    get:
      tags:
      - "v2_cs/apiData"
      summary: "deleteContentSourceDocument"
      description: "Delete Document form ant Content source"
      operationId: "deleteContentSourceDocument"
      produces:
      - "application/json"
      parameters:
      - name: "contentSourceId"
        in: "path"
        description: "id of the Content Source"
        required: true
        type: "string"
      - name: "objectId"
        in: "path"
        description: "Id of the Object"
        required: true
        type: "string"
      - name: "documentId"
        in: "path"
        description: "Id of the indexed Document"
        required: true
        type: "string"
      responses:
        200:
          description: "successful Deletion"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
  /v2_cs/apiData/contentSource/{contentSourceId}/object/{objectId}/bulkUpload:
    post:
      tags:
      - "v2_cs/apiData"
      summary: "documentBulkUpload"
      description: "Bulk upload to the Content Source"
      operationId: "documentBulkUpload"
      produces:
      - "application/json"
      parameters:
      - name: "contentSourceId"
        in: "path"
        description: "Id of the Content Source"
        required: true
        type: "string"
      - name: "objectId"
        in: "path"
        description: "Id of the Object"
        required: true
        type: "string"
      - name: "postTimeField"
        in: "query"
        description: "Define field which can be used as post_time"
        required: false
        type: "string"
      - in: "body"
        name: "body"
        description: "Json Body"
        required: true
        schema:
          type: "object"
          properties:
            bulkData:
              type: "array"
              items:
                $ref: "#/definitions/ApiDataBulkItem"
      responses:
        200:
          description: "successful operation"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
  /v2_cs/apiData/multiIndexBulkUpload:
    post:
      tags:
      - "v2_cs/apiData"
      summary: "multiIndexBulkUpload"
      description: "Bulk upload to Multiple Content source simultaneously"
      operationId: "multiIndexBulkUpload"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        description: "Json Body"
        required: true
        schema:
          type: "object"
          properties:
            bulkData:
              type: "array"
              items:
                $ref: "#/definitions/MultiDataBulkItem"
      responses:
        200:
          description: "successful operation"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
  /v2_cs/apiData/contentSource/{cs_name}/object/{cso_name}/getBulkData:
    post:
      tags:
      - "v2_cs/apiData"
      summary: "To get Content Source Data"
      description: "To get Content Source Data"
      operationId: "getContentSourceBulkData"
      produces:
      - "application/json"
      parameters:
      - name: "cs_name"
        in: "path"
        description: "Elastic Index Name of Content source"
        required: true
        type: "string"
      - name: "cso_name"
        in: "path"
        description: "Content Source Object Name"
        required: true
        type: "string"
      - in: body
        name: body
        description: Params info required to fetch content source Data.
        schema:
          type: object
          required:
            - fields
            - startDate
          properties:
            fields:
              type: array
              items:
                type: string
            startDate:
              type: string
            startDateField:
              type: string
              default: 'indexedDate'
            scroll:
              type: string
            offset:
              type: integer
              default: 0
              minimum: 0
            size:
              type: integer
              default: 10
              minimum: 1
              maximum: 1000
      responses:
        400:
          description: "Invalid status value"
        200:
          description: "successful operation"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
  /v2_cs/apiData/getDataWithScroll:
    get:
      tags:
      - "v2_cs/apiData"
      summary: "Get Content Source Data with Scroll Id"
      description: "Get Content Source Data with Scroll Id"
      operationId: "getScrollData"
      produces:
      - "application/json"
      parameters:
      - name: "scrollId"
        in: "query"
        description: "scrollId of the elasticSearch result"
        required: true
        type: "string"
      - name: "scroll"
        in: "query"
        description: "time for which scroll context should be open"
        required: false
        type: "string"
        default: "1h"
      responses:
        200:
          description: "successful operation"
        400:
          description: "Invalid status value"
      security:
      - searchunify_auth: []
      x-swagger-router-controller: "ContentSourceList"
  /v2/agentHelper/agentHelper:
    post:
      tags:
      - "v2/agentHelper"
      summary: "gets input fields and sends the output accordingly"
      description: "gets input fields and sends the output accordingly"
      operationId: "agentHelper"
      produces:
      - "application/json"
      parameters:
      - in: "body"
        name: "body"
        description: "Status values that need to be considered for response"
        required: true
        schema:
          type: "object"
          required:
            - uid
          properties:
            inputFields:
              type: "array"
              example:
                "[{'name':'mamba18_okta_salesforce___case___Subject','label':'Subject'},{'name':'mamba18_okta_salesforce___case___Description','label':'Description'}]"
            uid:
              type: "string"
              example:
                "abc21d09-cc4a-11e8-9938-80ce62985f6c"
      responses:
        400:
          description: "Invalid status value"
        200:
          description: "successful operation"
      x-swagger-router-controller: "agentHelper"
securityDefinitions:
  searchunify_auth:
    type: "oauth2"
    authorizationUrl: "http://petstore.swagger.io/oauth/dialog"
    flow: "implicit"
    scopes:
      read:searchQuery: "read your search queries"
  api_key:
    type: "apiKey"
    name: "api_key"
    in: "header"
definitions:
  SearchSession:
    type: "object"
    properties:
      id:
        type: "integer"
        format: "int64"
      email:
        type: "string"
      searchClientId:
        type: "integer"
        format: "int32"
        description: "Search Client which was used by the user for search"
      pageViews:
        type: "array"
        items:
          $ref: "#/definitions/Page"
      searchQueries:
        type: "array"
        items:
          $ref: "#/definitions/SearchQuery"
      SearchConversion:
        type: "array"
        items:
          $ref: "#/definitions/SearchConversion"
      SupportPageVisits:
        type: "array"
        items:
          $ref: "#/definitions/Page"
    xml:
      name: "SearchSession"
  SearchQuery:
    type: "object"
    properties:
      id:
        type: "integer"
        format: "int64"
      value:
        type: "string"
      count:
        type: "integer"
        format: "int64"
    xml:
      name: "SearchQuery"
  Page:
    type: "object"
    properties:
      id:
        type: "integer"
        format: "int64"
      value:
        type: "string"
      link:
        type: "string"
    xml:
      name: "Page"
  ContentSource:
    type: "object"
    properties:
      id:
        type: "integer"
        format: "int64"
      value:
        type: "string"
      name:
        type: "string"
      contentSourceType:
        type: "string"
    xml:
      name: "ContentSource"
  SearchConversion:
    type: "object"
    properties:
      id:
        type: "integer"
        format: "int64"
      contentSourceId:
        $ref: "#/definitions/ContentSource"
      url:
        type: "string"
        example: "http://www.searchunify.com"
      value:
        type: "string"
        example: "http://www.searchunify.com"
    xml:
      name: "Pet"
  ApiResponse:
    type: "object"
    properties:
      code:
        type: "integer"
        format: "int32"
      type:
        type: "string"
      message:
        type: "string"
  GanttBlock:
    type: "object"
    properties:
      start:
        type: "integer"
        example: 1517289600000
      end:
        type: "integer"
        example: 1517290049999
      count:
        type: "integer"
        example: 8
  GanttBlockSearch:
    type: "object"
    properties:
      text:
        type: "string"
        example: "new Search"
      ts:
        type: "integer"
        example: 1517304276810
  GanttBlockViews:
    type: "object"
    properties:
      title:
        type: "string"
        example: "testPagecommunity"
      ts:
        type: "integer"
        example: 1517304276810
      url:
        type: "string"
        example: "https://searchltng-developer-edition.ap5.force.com/s/testpagecommunity?searchString=data"
  SearchResult:
    type: "object"
    properties:
            uid:
              type: "string"
              example:
                "49291621-9ad7-11e8-a5ff-40b034e56204"
            aggregations:
              type: "array"
              items:
                  $ref: "#/definitions/Aggregations"
            email:
              type: "string"
              example:
                "<EMAIL>"
            searchString:
              type: "string"
              example:
                "<EMAIL>"
            sortby:
              type: "string"
              example:
                "Relevance"
            orderBy:
              type: "string"
              example:
                "_score"
            UserType:
              type: "string"
              example:
                "Standard"
            AccountID:
              type: "string"
              example:
                "007808900004xHfF9U"
            ProfileID:
              type: "string"
              example:
                "09898033004xHrt"
            ContactId:
              type: "string"
              example:
                "078W000005xcdre"
            UserId:
              type: "string"
              example:
                "0898W050005xcdre"
            boardsArr:
              type: "string"
              example:
                "board1,board2,board3"
            exactPhrase:
              type: "string"
              example:
                "phrase"
            withOneOrMore:
              type: "string"
              example:
                "phrase"
            withoutTheWords:
              type: "string"
              example:
                "phrase"
            from:
              type: "string"
              example:
                "10"
            resultsPerPage:
              type: "string"
              example:
                "20"
            pagingAggregation:
              type: "object"
              items:
                  $ref: "#/definitions/PagingAggregations"
            indexEnabled:
              type: "boolean"
              example:
                "true"
    xml:
      name: "SearchResult"
  Aggregations:
    type: "object"
    properties:
      filter:
        type: "array"
        items:
           type: "string"
      children:
          type: "object"
          items:
            $ref: "#/definitions/ChildrenAggregations"
      type:
        type: "string"
        example: '_type'
  ChildrenAggregations:
      type: "object"
      properties:
        childName:
          type: "string"
          example: 'AMA child1'
        level:
          type: "string"
          example: '3'
  PagingAggregations:
      type: "object"
      properties:
        key:
          type: "string"
          example: '_type'
        keyword:
          type: "string"
          example: 'discussion'
        offset:
          type: "string"
          example: '20'
  ContentSourceList:
      type: "object"
      properties:
        id:
          type: "string"
          example: '20'
        label:
          type: "string"
          example: 'Drive cs'
        name:
          type: "string"
          example: 'drive_cs'
        url:
          type: "string"
          example: 'http://drive.com'
  ContentSourceObject:
    type: "object"
    properties:
      object:
        type: "object"
        properties:
          name:
            type: "string"
            example: 'file'
          label:
            type: "string"
            example: "Drive Content"
          fields:
            type: "array"
            items:
              $ref: "#/definitions/ContentSourceFields"
  ContentSourceFields:
    type: "object"
    properties:
      name:
        type: "string"
        example: "id"
      label:
        type: "string"
        example: "Id"
      type:
        type: "string"
        example: "string"
      "isFilterable":
        type: "string"
        example: 0
      "isSearchable":
        type: "string"
        example: 1
  ContentSourceData:
    type: "object"
    properties:
      id:
        type: "string"
        example: '20'
      contentSource:
        type: "string"
        example: 'drive'
      object:
        type: "string"
        example: 'file'
      source:
        type: "object"
        example: {"created Date": '20-08-2018',"url": 'localhost:8025/drive.html'}
  apiDataIndex:
    type: "object"
    properties:
      id:
        type: "string"
        example: '20'
      contentSource:
        type: "string"
        example: 'drive'
      object:
        type: "string"
        example: 'file'
      source:
        type: "object"
        example: {"created Date": '20-08-2018',"url": 'localhost:8025/drive.html'}
      found:
        type: "boolean"
        example: true
  ApiDataBulkItem:
    type: "object"
    properties:
      id:
        type: "string"
        example: '123'
      content:
        type: "object"
        example: {"info":'abc'}
  MultiDataBulkItem:
    type: "object"
    properties:
      contentSourceId:
        type: "string"
        example: '123'
      obejctId:
        type: "string"
        example: '23'
      id:
        type: "string"
        example: '2333'
      postTimeField:
        type: "string"
        example: 'post_time'
      content:
        type: "object"
        example: {"info":'abc', "title":"Hello World"}
  RecentBulkItem:
    type: "object"
    properties:
      title:
        type: "string"
        example: 'Dummy Search'
  suArrayOpertions:
    type: "object"
    example: {"tag":'append', "channel_list":'update'}
externalDocs:
  description: "Find out more about Swagger"
  url: "http://swagger.io"
