/**
 * Created by man<PERSON><PERSON> on 7/10/16.
 */
var request=require('request');
var universal=require('./universal')
var constants = require('./constants');
var async = require('async');
var thisis=this;
var cron=require('./createCron')
var getBulkContent=require('./ApisForLithium/getBulkContent')
var parser = require('xml2json');
var litthiumOAuth=require('./lithiumOAuth')
var lithiumFetch=require('./ApisForLithium/getBulkContentV2');
var commonFunctions = require('../utils/commonFunctions');

exports.eventCreateUpdateThread=function (req,res) {
  var json = parser.toJson(req.body.message)
  //var json = parser.toJson('<message type="message" href="/messages/id/2169">\n  <seo_title type="string" null="true"/>\n  <seo_description type="string" null="true"/>\n  <kudos>\n    <count type="int">1</count>\n  </kudos>\n  <message_rating type="float">0.0</message_rating>\n  <last_edit_author type="user" href="/users/id/11">\n    <login type="string">daphne</login>\n  </last_edit_author>\n  <board_id type="int">3</board_id>\n  <root type="message" href="/messages/id/2169"/>\n  <canonical_url type="string" null="true"/>\n  <post_time type="date_time">2014-10-28T20:33:37+00:00</post_time>\n  <last_edit_time type="date_time">2014-10-28T20:33:37+00:00</last_edit_time>\n  <teaser type="string"></teaser>\n  <views>\n    <count type="int">1</count>\n  </views>\n  <parent type="message" null="true"/>\n  <body type="string">&lt;P&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;/P&gt;</body>\n  <thread type="thread" href="/threads/id/2169"/>\n  <board type="board" href="/boards/id/betaevaluators"/>\n  <subject type="string">another message here!</subject>\n  <id type="int">2169</id>\n  <read_only type="boolean">false</read_only>\n  <deleted type="boolean">false</deleted>\n  <author type="user" href="/users/id/11">\n    <login type="string">daphne</login>\n  </author>\n</message>\n');
  var dataFromHit=[JSON.parse(json).message]

  commonFunctions.errorlogger.info("is id",dataFromHit[0].id["$t"])
  lithiumFetch.getDataLithium("","",100,0,3,"",dataFromHit[0].id["$t"],function (data) {
    commonFunctions.errorlogger.warn("Done")
  })

      res.sendStatus(200);

}


exports.deleteThread=function (req,res) {
  var json = parser.toJson(req.body.message);
  // var json = parser.toJson('<message type="message" href="/messages/id/2169">\n  <seo_title type="string" null="true"/>\n  <seo_description type="string" null="true"/>\n  <kudos>\n    <count type="int">1</count>\n  </kudos>\n  <message_rating type="float">0.0</message_rating>\n  <last_edit_author type="user" href="/users/id/11">\n    <login type="string">daphne</login>\n  </last_edit_author>\n  <board_id type="int">3</board_id>\n  <root type="message" href="/messages/id/2169"/>\n  <canonical_url type="string" null="true"/>\n  <post_time type="date_time">2014-10-28T20:33:37+00:00</post_time>\n  <last_edit_time type="date_time">2014-10-28T20:33:37+00:00</last_edit_time>\n  <teaser type="string"></teaser>\n  <views>\n    <count type="int">1</count>\n  </views>\n  <parent type="message" null="true"/>\n  <body type="string">&lt;P&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;SPAN&gt;test&amp;nbsp;&lt;/SPAN&gt;&lt;/P&gt;</body>\n  <thread type="thread" href="/threads/id/2169"/>\n  <board type="board" href="/boards/id/betaevaluators"/>\n  <subject type="string">another message here!</subject>\n  <id type="int">2169</id>\n  <read_only type="boolean">false</read_only>\n  <deleted type="boolean">false</deleted>\n  <author type="user" href="/users/id/11">\n    <login type="string">daphne</login>\n  </author>\n</message>\n');

  json=JSON.parse(json).message
  commonFunctions.errorlogger.info("json is",json);
  var type=[]

  var sql="SELECT * FROM `lithium_content_types_to_sync` WHERE type!='user'"
  connection[req.headers['tenant-id']].execute.query(sql,function(err,row) {

    var task=[]
    for(var t=0;t<row.length;t++)
    {
      task.push((function (t) {
        return function (cb) {

          var optionsDel = { method: 'DELETE',
            url: "http://"+config.get('elasticIndexCS.host')+":"+config.get('elasticIndexCS.port')+"/"+'_all/'+row[t].type+"/"+json.thread.href.split('/')[3],
          };

          request(optionsDel, function (error, responseDel, bodyDel) {
             if (error) {
              commonFunctions.errorlogger.error( error)
              return;
            }

            commonFunctions.errorlogger.info(bodyDel);
            bodyDel=JSON.parse(bodyDel);

            cb(null)

          });
        }
      })(t))
    }

    async.parallel(task,function (err,result) {
      res.sendStatus(200)
    })

  })


}
