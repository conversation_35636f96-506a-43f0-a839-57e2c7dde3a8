/**
 * Author: <PERSON><PERSON>
 * Date: 19-09-2017
 * Description: Routes code for semantic search API
 */

var request = require("request");
var async = require('async');
var fs = require("fs");
// var howToRoutes = {};
var elastic = require('elasticsearch');
var thisis = this;
var router = require('express').Router();

/*exports.index = function(req, res, next) {
    try {
      req.method=req.method=="OPTIONS"?req.headers["access-control-request-method"]:req.method
      howToRoutes[req.params.path][req.method](req, res, next);
    } catch (error) {
        console.log("error", error);
        res.send(404);
    }
};*/

// howToRoutes["getHowToResult"] = {};
router.post("/getHowToResult", function(req, res, next){
    /* universal.getAutoprovisonToken("",req.body.accessToken,function (resultToken) {
        if(!resultToken) {
            res.send({flag: 404, status: "invalid accessToken"});
            return;
        }else{
            var userObj={};

            userObj.searchTuning=req.body.searchTuning;
            userObj.searchString =( req.body.searchString);
            userObj.exactPhrase =( req.body.exactPhrase);
            userObj.withoutTheWords=( req.body.withoutTheWords);
            userObj.withOneOrMore=( req.body.withOneOrMore);
            userObj.JiveSecurityGroups=req.body.JiveSecurityGroups || ""
            userObj.roles=req.body.roles|| "";
            userObj.userId=req.body.userId;
            userObj.noReply=req.body.noReply;
            userObj.generateSearchClientID=req.body.generateSearchClientID;
            userObj.platformId = req.body.platformId || 0; //set contentSouirtceId=0 manually
            userObj.fromPageNo = req.body.from; //offset
            userObj.pageNo = req.body.pageNo;
            userObj.cookie = req.body.cookie;
            if(req.body.entry)
            userObj.entry = req.body.entry;
            userObj.ip=req.body.userip;

            userObj.aggregations=req.body.aggregations;

            userObj.contentTypeForElastic=req.body.type;
            userObj.dateClient = (new Date()).getTime();
            //userObj.allSecurityGroupsOfUser=req.body.allSecurityGroupsOfUser;
            userObj.sortby=req.body.sortby;
            userObj.orderBy=req.body.orderBy;
            userObj.firstLoad=req.body.firstLoad;
            userObj.boardsArr=req.body.boardsArr || "";
            userObj.resultsPerPage=req.body.resultsPerPage;



            universal.getIndexNames(userObj.generateSearchClientID,function(err, indices){
                console.log("index names are",indices.index_name);
                try{
                    client.search(
                        {
                            index: config.get('elasticIndex.searchgraph'),
                            type: 'search_pattern',
                            body: {
                                "size": 10,
                                "query": {
                                    "function_score": {
                                        "query": {
                                            "match": {
                                                "text": {
                                                    "query": userObj.searchString,
                                                    "fuzziness": "auto",
                                                    "operator": "and"
                                                }
                                            }
                                        },
                                        "functions": [
                                            {
                                                "field_value_factor": {
                                                    "field": "convCount",
                                                    "factor": 0.2,
                                                    "modifier": "none",
                                                    "missing": 1
                                                }
                                            }
                                        ]
                                    }
                                }
                            }
                        }, function(err, data){
                            //console.log('search result query: ',err, JSON.stringify(data));
                            if(err)
                                res.send(err);
                            else{
                                var funcarray = [];
                                data.hits.hits.map(function(item){
                                    var urlarray = item._source.conversion;
                                    urlarray.map(function(urlobj){
                                        var url = urlobj.url;
                                        var obj = {};
                                        obj.index = url.split('/')[0];
                                        obj.type = url.split('/')[1];
                                        obj.id = url.split('/')[2];
                                        funcarray.push(function(cb){
                                            client.get(obj, function(errin, datain){
                                                if(!errin)
                                                    cb(null, datain);
                                                else{
                                                    cb(null, {"found":false});
                                                }
                                            });
                                        });
                                    });

                                    return item;
                                });
                                async.parallel(funcarray, function(errorp, resultsp){
                                    resultsp = resultsp.filter(function(source_item){
                                        return indices.index_name.indexOf(source_item._index)>=0?source_item.found:false;
                                    });
                                    res.send(resultsp);
                                });
                            }
                        }
                    );
                }catch(e){
                    //console.log(e);
                    res.send('error');
                }
            });
        }
    }); */

});
module.exports.index = router;