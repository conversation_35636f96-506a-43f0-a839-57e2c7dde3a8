/**
 * Created by <PERSON><PERSON><PERSON> on 14-05-2016.
 */
const url = require("url");
var oauthServer = require('oauth2-server');
var Request = oauthServer.Request;
var Response = oauthServer.Response;
var db = require('./sqldb');
var oauth = require('./oauth');
const OAuthError = require('oauth2-server/lib/errors/oauth-error');


module.exports = function(options){
  options = options || {};
  return function(req, res, next) {
    var request = new Request({
      headers: {authorization: req.headers.authorization},
      method: req.method,
      query: req.query,
      body: req.body
    });
    var response = new Response(res);

    oauth.authenticate(request, response,options)
      .then(function (token) {
        // Request is authorized.
            var urlObjecter = url.parse(req.url, true);

            var check_url = check_api_routes(urlObjecter.path,token.scope)
            if(check_url){
              req.user = token;
              next();
            }else{
              throw error = api_error();
            }
      })
      .catch(function (err) {
        // Request is not authorized.
        res.status(err.code || 500);
        next(err);
      });
  };
};

// Check routes according to the scope 
function check_api_routes(path,scope){
//      /^\/api\/v2_search/

  let apiroutes = {
    search:[
      /^\/api\/(\bv2_search\b)[^$]/
    ],
    analytics :[
      /^\/api\/(\bv2\b)[^$]/
    ],
    content:[
      /^\/api\/(\bv2_cs\b)[^$]/
    ]

  };

  if(scope == 'Search'){
    return apiroutes.search[0].test(path)
  }else if(scope == 'Analytics'){
    return apiroutes.analytics[0].test(path)
  }else if(scope == 'Content'){
    // return true;
    return apiroutes.content[0].test(path)
  }else if(scope == 'All'){
    return true;
  }else{
    return false;
  }

}

// set error if scope is not matched
function api_error(){
  const err = new OAuthError();
  err.status = 401;
  err.code = 401;
  err.statusCode = 401;
  err.message = 'You are not authorized';
  err.name = 'Scope Missmatch';
  return err;
}