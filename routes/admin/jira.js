var commonFunctions = require('./../../utils/commonFunctions');
var oauthUtil = require('./../../utils/oAuthConfluenceJiraUtil');

const jiraAuthorisationIntermediate = function (contentSource, authorization, req, callback) {
  authorization.privateKey = authorization.privateKey.replace('BEGIN RSA PRIVATE KEY', '');
  authorization.privateKey = authorization.privateKey.replace('END RSA PRIVATE KEY', '');
  authorization.privateKey = authorization.privateKey.replace(/ /g, '\n');
  authorization.privateKey = authorization.privateKey.replace(/-/g, '').replace(/\n/g, '');
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  // console.log('The auth key is: ', authorization.privateKey);
  var url = '';
  var objectToPass = {
    host: url,
    oauth: {
      consumer_key: authorization.publicKey,
      private_key: authorization.privateKey,
      signature_method: 'RSA-SHA1',
      callback_url: config.get('adminURL') + "/admin/authorization/oAuthCallbackJira"
    }
  };
  if (contentSource.url.indexOf('https://') != -1) {
    url = contentSource.url.replace('https://', '');
  } else {
    url = contentSource.url.replace('http://', '');
    objectToPass["protocol"] = 'http';
  }
  objectToPass["host"] = url;
  if (url.indexOf(":") != -1) {
    objectToPass["host"] = url.split(":")[0];
    objectToPass["port"] = url.split(":")[1];
  }
  oauthUtil.getAuthorizeURL(objectToPass, function (error, oauthResult) {
    if(error){
      commonFunctions.errorlogger.error("Error getAuthUrl: ",error);
      return callback({ "error": "Failed" });
    }
    authorization.refreshToken = oauthResult.token_secret;
    authorization.accessToken = oauthResult.token;
    if (oauthResult.token) {
      insertUpdateAuthorization(authorization,req, function (errAsync, rows) {
        if (errAsync) {
          commonFunctions.errorlogger.error("Error inside insert_content_source_authorization",errAsync);
          callback({ "error": "Failed" });
        }
        else {
          callback({ "oauth": oauthResult });
        }
      })
    }
    else {
      callback({ "error": "Failed" });
    }
  });
}


const insertUpdateAuthorization = function (authorization,req, callback) {
  const colums = []
  const parameters = []

  for (var key in authorization) {
    if (authorization.hasOwnProperty(key)) {
      colums.push(key)
      parameters.push(authorization[key])
    }
  }
  const sqlCS = "INSERT INTO `content_source_authorization`(" + colums +
    ") VALUES (" + colums.map(x => {
      return '?'
    }).join(',') + ") " +
    "ON DUPLICATE KEY UPDATE " + colums.map(x => {
      return x + "=values(" + x + ")"
    }).join(',');
  const q = connection[req.headers['tenant-id']].execute.query(sqlCS, parameters, function (errAsync, rows) {
    if (errAsync) {
      callback(errAsync, [])
    }
    else {
      callback(null, rows)
    }
  })
}

module.exports = {
  jiraAuthorisationIntermediate: jiraAuthorisationIntermediate
}
