var commonFunctions = require('./../../utils/commonFunctions');

const requestTofetchObjectFields = function (authorization, objectName,req, callback) {
    var fieldsArray = [];
    var flag = 7;
      
    var standardObj = commonFunctions.metadata.loadMappingForDbAndElastic("43").objects;
    var fieldsArr = [];

    for (var i = 0; i < standardObj.length; i++) {
        if (objectName == standardObj[i].name) {
            flag = 0;
            fieldsArr = commonFunctions.metadata.loadMappingForDbAndElastic("43").fields.concat(standardObj[i].fields);
            break;
        }
    }
    for (var field = 0; field < fieldsArr.length; field++) {
        fieldsArray.push({
            "name": fieldsArr[field].name,
            "label": fieldsArr[field].label,
            "type": fieldsArr[field].type
        })
    }

    callback(null, { data: fieldsArray, flag: flag });
}

module.exports = {
  requestTofetchObjectFields: requestTofetchObjectFields
}
