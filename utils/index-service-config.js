const commonFunctions = require('./commonFunctions');
const config = require('config');
const mysql = require('mysql');
const { getTenantInfoFromTenantId } = require('auth-middleware');
const { tenantSqlConnection  } = require('../auth/sqlConnection');
const { tenantGlobals } = require("./kafka/kafkaPublishers/tenantGlobals");
const { publishManageIndexesKafka } = require('../routes/admin/service/nlpKafkaService');

const setIndexingConfigToDefault = (reqBody, callback) => {
    let headers = {
        "Content-Type" : "application/json",
        "index-service-secret": config.get("indexService.sharedSecret"),
    }
    commonFunctions.httpRequest('POST', config.get("indexService.url") + '/index-service/is-config/resetIndexingConfiguration', {}, reqBody, headers, (error, result) => {
        if(error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            callback(error, null);
        }else {
            if (result.data.configValue && result.data.configValue.hasOwnProperty('enableMultilingual'))
              tenantGlobals(reqBody.tenantId, { enableMultilingual: result.data.configValue.enableMultilingual });
            callback(null, result);
        }
    })
}

const updateIndexingConfig = (updatedData, tenantId, callback) => {
    let headers = {
      "Content-Type": "application/json",
      "index-service-secret": config.get("indexService.sharedSecret"),
    }
    updatedData.tenantId = tenantId;
    commonFunctions.httpRequest('POST', config.get("indexService.url") + '/index-service/is-config/updateIndexingConfiguration', '', updatedData, headers, ( err, result) =>{
      if(!result.status){
        callback(result, null)
      } else{
        if (updatedData.configValue && updatedData.configValue.hasOwnProperty('enableMultilingual'))
          tenantGlobals(tenantId, { enableMultilingual: updatedData.configValue.enableMultilingual });
        callback(null, result);
      }
    });
  }

const refreshIndexes = async (tenantId) => {
  try {
    const data = await getTenantInfoFromTenantId(tenantId);
    const { tpk } = data[0];
    if (!tpk) throw new Error('tpk not found for tenant');
    await publishManageIndexesKafka({ OPERATION: "REFRESH", OPERATOR: "SYNONYMS", tpk, tenantId });
  } catch (error) {
    commonFunctions.errorlogger.error("Error in refreshIndexes method : ", error);
    throw error;
  }
};

module.exports = {
  updateIndexingConfig,
  setIndexingConfigToDefault,
  refreshIndexes,
}
