var router = require('express').Router();
var swaggerTools = require('swagger-tools');
var jsyaml = require('js-yaml');
var fs = require('fs'),
    path = require('path');
// var authenticate = require('../components/oauth/authenticate');

var options = {
    swaggerUi: path.join(__dirname, '/swagger.json'),
    controllers: path.join(__dirname, './controllers'),
    useStubs: process.env.NODE_ENV === 'development' // Conditionally turn on stubs (mock mode)
};

var spec = fs.readFileSync(path.join(__dirname, 'api/swagger.yaml'), 'utf8');
var swaggerDoc = jsyaml.safeLoad(spec);
// router.use(authenticate);
swaggerTools.initializeMiddleware(swaggerDoc, function (middleware) {

    // Interpret Swagger resources and attach metadata to request - must be first in swagger-tools middleware chain
    router.use(middleware.swaggerMetadata());

    // Validate Swagger requests
    router.use(middleware.swaggerValidator());

    // Route validated requests to appropriate controller
    router.use(middleware.swaggerRouter(options));

    // Serve the Swagger documents and Swagger UI
    router.use(middleware.swaggerUi());

});

module.exports = router;