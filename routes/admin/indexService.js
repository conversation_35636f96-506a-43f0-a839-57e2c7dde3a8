const express = require('express');
const router = express.Router();
const indexServiceConfig = require('../../utils/index-service-config');
const { fetchIndexingConfig } = require('../../utils/commonFunctions');



const getDataFromDB = (queryString,req) => {
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(queryString, (err, rows) => {
            if (err) {
                console.log(err)
            }
            resolve(rows)
        })
    })
};

let accessCheck = async (email, roleId, contentSourceId,req) => {
    let allowedSUuser = [/grazitti.com/, /searchunify.com/];
    /** All user with grazitti.com and searchunify.com are considered as Searchunify user */
    let searchUnifyUser = allowedSUuser[0].test(email) || allowedSUuser[1].test(email);
    /** Get Access control settings */
    let accessControlSettings = (await getDataFromDB(`select * from access_control_settings;`,req))[0].contentSource;
    if (searchUnifyUser || roleId == 4 || accessControlSettings == 1) { return true }
    else {
        // if contentSourceId is undefined --> its for creation of CS
        if(!contentSourceId){ return true }
        let csDeatil = (await getDataFromDB(`SELECT * FROM content_sources WHERE id=${contentSourceId};`,req))[0];
        let userCSAccess = JSON.parse(csDeatil.sharedAccess);
        userCSAccess.push(csDeatil.email);
        return userCSAccess.includes(email)
    }
}

const securityCheck = async (req,res,next) => {
    let contentSourceID = req.query.content_source_id || req.body.content_source_id || (req.body && req.body.contentSource && req.body.contentSource.id) || (req.body.contentSource && req.body.contentSource.content_source_id);
    let allowedFurther = await accessCheck(req.headers.session.email, req.headers.session.roleId,contentSourceID,req);
    if (!allowedFurther) {
        res.send({ flag: 401, message: 'Unauthorized' })
    } else {
        next();
    }
}

router.get('/getIndexingConfiguration', securityCheck, function (req, res) {
    fetchIndexingConfig({ tenantId: req.headers['tenant-id'], RETRY_COUNT: 30}, (response) => {
        res.send({success: true, data: { indexingConfig: response}});
    });
});

router.post('/resetIndexingConfiguration', securityCheck, function(req, res) {
    let reqBody = {
        tenantId: req.headers['tenant-id'],
        config_key: req.body.config_key
    }
    indexServiceConfig.setIndexingConfigToDefault(reqBody, (error, result) => {
        if(error) res.send(error);
        else res.send({ success: true, response: result});
    })
})

router.post('/updateIndexingConfiguration', securityCheck, function (req, res, next) {
    indexServiceConfig.updateIndexingConfig(req.body, req.headers['tenant-id'], (err, result) => {
        if (err) {
           res.send(err);
        } else {
           res.send(result);
        }
    });
});

router.post('/refreshIndexes', securityCheck, async function (req, res, next) {
    try {
        await indexServiceConfig.refreshIndexes(req.headers['tenant-id']);
        res.send({ success: true });
    } catch (error) {
        res.send(error);
    }
});
router.post('/maskExistingRecords', securityCheck, function (req, res, next) {
    indexServiceConfig.maskExistingRecords(req.body, req.headers['tenant-id'], (err, result) => {
        if (err) {
           res.send(err);
        } else {
           res.send(result);
        }
    });
});



module.exports = {
    router: router
}
