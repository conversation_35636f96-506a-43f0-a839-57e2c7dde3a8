var async = require('async');
var fs = require("fs");
var path = require('path');
var thisis = this;
var child_process = require('child_process');
var cronUtility = require('./cronUtility');
var elasticUtility = require('./elasticUtility');
var encryptorUtility = require('./encryptorUtility');

function getPreviosSession(outputFilePath, cbc){
  fs.readFile(outputFilePath,'utf-8', function(err, d){
    cbc(null,d);
  });
}

function getSessionCookies(username, password, url, outputFilePath,check, cbc){

  if(!check)
  {
    console.log("here--",outputFilePath)
    fs.readFile(outputFilePath,'utf-8', function(err, d){
      if(!err)
      cbc(null,d);
      else
        getSession(username, password, url, outputFilePath,check, cbc)
    });
  }
  else
    getSession(username, password, url, outputFilePath,check, cbc)

}



function getSessionCookiesUsingSelector(username, password, url,usernameselector,passwordselector,buttonselector, outputFilePath,check, cbc){
    getSession(username, password, url,usernameselector,passwordselector,buttonselector, outputFilePath,check, cbc)
}
function getSession(username, password, url,usernameselector,passwordselector,buttonselector,  outputFilePath,check, cbc) {
  var encryptedUsername = '';
  var encryptedPassword = '';
  async.series([
    cb=>{
      encryptorUtility.cryptED("encrypt", username, function(err, res){
        encryptedUsername = res.trim();
        cb(null, 1);
      });
    },
    cb=>{
      encryptorUtility.cryptED("encrypt", password, function(err, res){
        encryptedPassword = res.trim();
        cb(null, 1);
      });
    }
  ], function(error, response){
    var outfile = fs.createWriteStream(path.join(__dirname, "sessionCapturing.out"));
    console.log(usernameselector,passwordselector,buttonselector);
    if(!usernameselector && !passwordselector && !buttonselector)
    var crawlProcess = child_process.spawn(path.join(__dirname,"generateSession.sh"), [encryptedUsername, encryptedPassword, url , outputFilePath]);
    var crawlProcess = child_process.spawn(path.join(__dirname,"generateSessionWithSelectors.sh"), [encryptedUsername, encryptedPassword, url, outputFilePath ,usernameselector,passwordselector,buttonselector]);
    console.log(encryptedUsername, encryptedPassword, url, outputFilePath ,usernameselector,passwordselector,buttonselector);
    crawlProcess.stdout.on('data', function (data) {
      fs.appendFile(outfile.path, data,(err)=>{console.log(err)});
    });

    crawlProcess.stderr.on('data', function (data) {
      fs.appendFile(outfile.path, data,(err)=>{console.log(err)});
    });

    crawlProcess.on('exit', function (code) {
      console.log('child process exited with code ' + code);
      fs.readFile(outputFilePath,'utf-8', function(err, d){
        if(err)console.log(err);
        cbc(null,d);
      });

    });
  })
}
exports.getSessionCookies = getSessionCookies;
exports.getSessionCookiesUsingSelector = getSessionCookiesUsingSelector;