var elasticsearch = require('elasticsearch');
const request = require("request");
const path = require("path");
var configPath = path.join(__dirname, '../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');


let isolateSynonymStopwords = config?config.get('isolateSynonymStopwords'):false;
let pocName = config?config.get('poc_name'):'';
let elSynonymPath = (isolateSynonymStopwords && pocName)?('nlp/'+pocName+"_synonyms.txt"):"synonyms.txt";
let elStopwordsPath = (isolateSynonymStopwords && pocName)?('nlp/'+pocName+"_stopwords.txt"):"stopwords.txt";

client = new elasticsearch.Client({
    host: 'http://' + config.get('elasticIndexCS.host') + ':' + config.get('elasticIndexCS.port')
});

const indexName = "recent_searches_" + pocName;
const createMapping = () => {
    var options = {
        method: 'PUT',
        url: 'http://' + config.get('elasticIndexCS.host') + ':' + config.get('elasticIndexCS.port') + "/" + indexName + "/",
        body: {
            "settings": {
                "analysis": {
                    "filter": {
                        "my_custom_stop": {
                            "type": "stop",
                            "stopwords_path": elStopwordsPath
                        },
                        "synonym": {
                            "type": "synonym",
                            "synonyms_path": elSynonymPath
                        },
                        "my_stop": {
                            "type": "stop",
                            "stopwords": "_english_"
                        }
                    },
                    "analyzer": {
                        "custom_lowercase_stemmed": {
                            "filter": [
                                "lowercase",
                                "my_stop",
                                "my_custom_stop"
                            ],
                            "tokenizer": "standard"
                        },
                        "custom_lowercase_synonym": {
                            "filter": [
                                "lowercase",
                                "my_stop",
                                "my_custom_stop",
                                "synonym"
                            ],
                            "tokenizer": "standard"
                        }
                    }
                }
            },
            "mappings": {
                "recentsearch": {
                    "properties": {
                        "_words_": {
                            "type": "text",
                            "analyzer": "custom_lowercase_stemmed",
                            "search_analyzer": "custom_lowercase_synonym"
                        },
                        "_suggest_": {
                            "type": "completion",
                            "analyzer": "simple",
                            "preserve_separators": true,
                            "preserve_position_increments": true,
                            "max_input_length": 50,
                            "contexts": [
                              {
                                "name": "uid",
                                "type": "category",
                                "path": "uid"
                              }
                            ]
                        },
                        "searchString": {
                            "type": "text",
                            "analyzer": "custom_lowercase_stemmed",
                            "search_analyzer": "custom_lowercase_synonym",
                            "copy_to": [
                                "_words_",
                                "_suggest_"
                            ]
                        },
                        "uid": {
                            "type": "keyword"
                        },
                        "sid": {
                            "type": "keyword"
                        },
                        "timestamp": {
                            "type": "date"
                        }
                    }
                }
            }
        },
        json: true
    };

    request(options, function (error, response, body) {
        if (body && body.error) {
            deleteOlderRecords();
        } else {
            console.log("response", body);
        }
    })
}


const deleteOlderRecords = () => {
    var dsl = {
        "query": {
            "range": {
                "timestamp": {
                    "lte": "now-2d/d"
                }
            }
        }
    }

    var options = {
        method: 'POST',
        url: 'http://' + config.get('elasticIndexCS.host') + ':' + config.get('elasticIndexCS.port') + "/" + indexName + "/_delete_by_query",
        headers: { 'content-type': 'application/json' },
        body: dsl,
        json: true
    };

    request(options, function (error, response, body) { })
}

const checkIfIndexExist = () => {

    createMapping()

}

checkIfIndexExist();

const getRecentSearches = (cb) =>{
    let dsl = {
        "size": 100,
        "sort":{
            "timestamp":"desc"
        }
    };
    var options = {
        method: 'POST',
        url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + indexName + "/_search",
        headers: {
            'content-type': 'application/json',
        },
        body: dsl,
        json: true
    };
    request(options, function (error, response, body) {
        if(error){
            cb(error,[])
        }else{
            cb(null,response)
        }
    })
}

module.exports = {
    getRecentSearches
}