
var commonFunctions = require('./../../../utils/commonFunctions')
const kafkaLib = require('../../../utils/kafka/kafka-lib');
const config = require('config');

function startTraining(req,cb) {
    getContentSourceFields(req, async (error, data) => {
        if (error) {
            cb(error, null);
        }
        else {
            await kafkaLib.publishMessage({
                topic: config.get('kafkaTopic.didYouMeanConfigTopic'),
                messages: [{
                    value: JSON.stringify({
                        data,
                        tenantInfo: {
                            tenantId: req.headers['tenant-id'],
                            tpk: req.headers.session.tpk,
                            esClusterIp: req.headers.session.esClusterIp
                        }
                    }),
                    key: req.headers['tenant-id']
                }]
            })
            cb(null, data);
        }
    });
}

function getContentSourceFields(req, cb) {
    commonFunctions.errorlogger.info("FUNCTION:: getContentSourceFields");
    let sql = "SELECT "
        + " cs.id as content_source_id, cs.elasticIndexName as content_source_elastic_name,"
        + " cso.id as object_id , cso.name as object_name, "
        + " csof.id as field_id, csof.name as field_name "
        + " FROM content_source_object_fields as csof "
        + " LEFT JOIN content_source_objects as cso ON csof.content_source_object_id = cso.id "
        + " LEFT JOIN content_sources cs On cs.id = cso.content_source_id "
        + " WHERE csof.did_you_mean = true ";
        if(!req.query.mergedFields) {
            sql = sql.concat("and csof.merge_field_id = 0");
        }
        connection[req.headers['tenant-id']].execute.query(sql, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error(error);
            cb(error, null);
        }
        else if (result.length == 0) {
            commonFunctions.errorlogger.error('No Data');
            cb('Data 0 ', null);
        }
        else {
            jsonCsObj = {};
            for (var i = 0; i < result.length; i++) {
                if (!jsonCsObj[result[i].content_source_elastic_name]) {
                    jsonCsObj[result[i].content_source_elastic_name] = {};
                }
                if (!jsonCsObj[result[i].content_source_elastic_name][result[i].object_name]) {
                    jsonCsObj[result[i].content_source_elastic_name][result[i].object_name] = []
                }
                jsonCsObj[result[i].content_source_elastic_name][result[i].object_name].push(result[i].field_name);
            }
            commonFunctions.errorlogger.info(jsonCsObj);
            var flatCsObj = [];
            Object.keys(jsonCsObj).map(x => {
                Object.keys(jsonCsObj[x]).map(y => {
                    flatCsObj.push({
                        "index": x,
                        "type": y,
                        "fieldArr": jsonCsObj[x][y]
                    })
                });
            });
            cb(null, flatCsObj);
        }
    });
}

module.exports = {
    startTraining
}