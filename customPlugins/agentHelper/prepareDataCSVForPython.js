const fs         = require('fs');
const { Parser } = require('json2csv');
const argv       = require('yargs').argv;
const jsep       = require("jsep").addBinaryOp("AND", 10).addBinaryOp("OR", 10);
const env        = require('../../routes/environment');
const mainConfig = require('../../config/' + env.configuration);
const { getEsClient } = require('../../utils/elastic');

var host, port;
if(argv.createNow){
    host   = mainConfig.elasticIndexCS.host;
    port   = mainConfig.elasticIndexCS.port;
} else{
    host   = config.get("elasticIndexCS.host");
    port   = config.get("elasticIndexCS.port");
}

const fetchAll = async (uid, tenantInfo) => {
    const { tpk, esClusterIp } = tenantInfo.session;
    const { tenantId } = tenantId;
    const bulk = fs.createWriteStream(`${__dirname}/../../resources/Asset-Library/${tpk}_docs.csv`);
    const responseQueue = [];
    let agentConfig = JSON.parse(fs.readFileSync(`${__dirname}/../../resources/search_clients_custom/${tpk}_agentHelperConfig.json.ignore`));
    if(uid)
        agentConfig = agentConfig[uid];
    else
        for (let key in agentConfig) {
            agentConfig = agentConfig[key];
            break;
        }
    var index            = agentConfig.Source_object.parent;
    var type             = agentConfig.Source_object.child;
    let custom_condition = createConditions(agentConfig);
    if(custom_condition)
        custom_condition = {"query":{"bool":{"must":[custom_condition]}}};
    const parserWithHeaders = new Parser({
        fields : [
            {
                label:"id",
                value: row => `${encodeURIComponent(row._index)}/${encodeURIComponent(row._type)}/${encodeURIComponent(row._id)}`
            },
            // ...config.Input_Fields.map(f=>"_source."+f.name)
            ...agentConfig.Input_Fields.map(f=>{
                return {
                    label:f.name.split('___')[2],
                    value: row=>row._source[f.name] || "-null-"
                };
            })
        ]
    });

    const parserWithoutHeaders = new Parser({
        header:false,
        fields : [
            {
                value: row => `${encodeURIComponent(row._index)}/${encodeURIComponent(row._type)}/${encodeURIComponent(row._id)}`
            },
            // ...config.Input_Fields.map(f=>"_source."+f.name)
            ...agentConfig.Input_Fields.map(f=>{
                return {
                    value: row=>row._source[f.name] || "-null-"
                };
            })
        ]
    });
    const esClient = getEsClient(tenantId);
    const response = await esClient.search({
        index: index,
        type: type,
        size: 5000,
        scroll: "30s",
        body:custom_condition
    });
    responseQueue.push(response);
    let setHeaders = true;
    while (responseQueue.length) {
        const data = responseQueue.shift();
        if (data.hits.hits.length) {
            if(setHeaders){
                bulk.write(parserWithHeaders.parse(data.hits.hits));
            }else{
                bulk.write(parserWithoutHeaders.parse(data.hits.hits));
            }
            bulk.write(`\n`);
            responseQueue.push(
                await esClient.scroll({
                    scrollId: data._scroll_id,
                    scroll: "30s"
                })
            );
        setHeaders = false;    
        }
    }
    return 'Done';
};

const start = async (uid, tenantInfo) => {
    const { tpk } = tenantInfo.session;
    if(fs.existsSync(`${__dirname}/../../resources/Asset-Library/${tpk}_docs.csv`)){
        fs.unlinkSync(`${__dirname}/../../resources/Asset-Library/${tpk}_docs.csv`); //delete docs.csv file and create new
    }
    try{
        let status = await fetchAll(uid, tenantInfo);
        return {status};
    }catch(error){
        console.log(error);
        return {error};
    }
}

if(argv.createNow)
    if(argv.uid && typeof argv.uid === 'string')
        start(argv.uid); // Start directly if executed from commandline
    else
        console.error("uid missing");
function createConditions(conf){
    let formula = conf.Formula;
    let createDSLExpression = (jstree)=>{
        if(jstree.type === "BinaryExpression"){
            let dsl = {bool:{}};
            let arr = [
                createDSLExpression(jstree.left),
                createDSLExpression(jstree.right)
            ];
            if(jstree.operator==="AND")
                dsl.bool = {must:arr};
            else if(jstree.operator==="OR")
                dsl.bool = {should:arr};
            return dsl;
        }
        else if(jstree.type === "Literal"){
            let condition = formula.Condition[jstree.value-1];
            let dsl_condition = {};
            if(condition.Operator === ">" || condition.Operator === "greater than"){
                dsl_condition = {range: {[condition.Field] : {gt : condition.Value}}};
            }
            else if(condition.Operator === "<" || condition.Operator === "less than"){
                dsl_condition = {range: {[condition.Field] : {lt : condition.Value}}};
            }
            else if(condition.Operator === "=" || condition.Operator === "equals"){
                dsl_condition = {term: {[condition.Field] : condition.Value}};
            }
            else if(condition.Operator === "Contains"){
                dsl_condition = {match_phrase: {[condition.Field] : condition.Value}};
            }
            else if(condition.Operator === "!=" || condition.Operator === "not equal"){
                dsl_condition = {bool:{must_not:{term:{[condition.Field]:condition.Value}}}};
            }
            return dsl_condition;
        }
    };
    let query;
    if(formula.Expression){
        query = createDSLExpression(jsep(formula.Expression));
    }
    else{
        query = createDSLExpression(jsep(formula.Condition.map((e,i)=>i+1).join(" AND ")));
    }
    return query;
}

module.exports = {start};