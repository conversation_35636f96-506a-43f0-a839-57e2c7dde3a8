var commonFunctions = require("./commonFunctions");
const rateLimiterHelper = require('./rate-limit-helper');
commonFunctions.errorlogger.log(process.env.NODE_ENV);

module.exports = async (req, res, next) => {
    try{
        if(rateLimiterHelper.isEndpointSupported(req) &&  !await rateLimiterHelper.processRequest(req,res)){
            return ;
        }
    }catch(error){
        commonFunctions.errorlogger.error(error);
        commonFunctions.errorlogger.error('unable to record request count for origin ip');
    }

    next();
};