module.exports = {
  objects: [{
    name: 'message',
    label: 'Message',
    status: 1,
    boosting_factor: 1,
    base_url: '',
    fields: [
      {
        name: 'id',
        label: 'ID',
        type: 'string',
        isFilterable: 1,
        isSortable: 1,
        isSearchable: 1,
        isActive: 1
      },
      {
        name: 'title',
        label: 'Subject',
        type: 'string',
        isFilterable: 1,
        isSortable: 1,
        isSearchable: 1,
        isActive: 1
      },
      {
        name: 'view_href',
        label: 'View Href',
        type: 'string',
        isFilterable: 1,
        isSortable: 0,
        isSearchable: 0,
        isActive: 1
      },
      {
        name: 'type',
        label: 'Message Type',
        type: 'string',
        isFilterable: 1,
        isSortable: 1,
        isSearchable: 1,
        isActive: 1
      },
      {
        name: 'authorName',
        label: 'Author Name',
        type: 'string',
        isFilterable: 1,
        isSortable: 1,
        isSearchable: 1,
        isActive: 1
      },
      {
        name: 'authorMail',
        label: 'Author Email',
        type: 'string',
        isFilterable: 1,
        isSortable: 1,
        isSearchable: 1,
        isActive: 1
      },
      {
        name: 'post_time',
        label: 'Post Time',
        type: 'datetime',
        isFilterable: 1,
        isMerged: 1,
        isSortable: 1,
        isSearchable: 0,
        isActive: 1
      },
      {
        name: 'messageContent',
        label: 'Message Content',
        type: 'string',
        isFilterable: 0,
        isSortable: 0,
        isSearchable: 1,
        isActive: 1
      },
      {
        name: 'streamId',
        label: 'Stream ID',
        type: 'string',
        isFilterable: 1,
        isSortable: 1,
        isSearchable: 0,
        isActive: 1
      },
      {
        name: 'streamName',
        label: 'Stream Name',
        type: 'string',
        isFilterable: 1,
        isSortable: 1,
        isSearchable: 1,
        isActive: 1
      },
      {
        name: 'topic',
        label: 'Topic',
        type: 'string',
        isFilterable: 1,
        isSortable: 1,
        isSearchable: 1,
        isActive: 1
      },
      {
        name: 'flags',
        label: 'Message Flags',
        type: 'string',
        isFilterable: 1,
        isSortable: 0,
        isSearchable: 1,
        isActive: 1
      },
      {
        name: 'attachment',
        label: 'Attachment',
        type: 'string',
        isFilterable: 1,
        isSortable: 0,
        isSearchable: 1,
        isActive: 1
      }
    ]
  }]
};
