/**
 * Created by man<PERSON><PERSON> on 25/10/17.
 */

var request = require('request');
var jar = request.jar();
var https = require('https');
var http = require('http');
var url = require('url');
var htmlparser = require("htmlparser2");
var async=require("async");
var fs=require("fs");
var commonFunctions = require('./../../utils/commonFunctions');
const aes256 = require('nodejs-aes256');


var session=require('./../sessionCapturing/getSessionUsingSelenium')
var path= require('path')


const getallContentV2=function (authData,cb) {

  var fromDate=""
  var toDate=""
  if(authData.fromDate)
    fromDate=new Date(authData.fromDate).getTime();
  if(authData.toDate)
    toDate=new Date(authData.toDate).getTime();

  if(authData.isCrawl=="update")
  {
    toDate=new Date().getTime();
    fromDate=new Date(new Date().setHours(new Date().getHours() - 2)).getTime();
  }
  var limit= authData.limit || 100
  var offset=parseInt(authData.offset) || 0
  var contentSourceId = authData.contentSourceId
  var type = authData.typeofBoard || 0
  var id=authData.id || 0
  var boardId=authData.boardId || 0
  commonFunctions.errorlogger.info("offset limit is",offset+"---"+limit)
  getDataLithium(authData,fromDate,toDate,limit,offset,contentSourceId,type,id,boardId,(result)=>{
    commonFunctions.errorlogger.warn("done")
    cb(null,[])
  })
}

const getDataLithium=function (authData,fromDate,toDate,limit,offset,contentSourceId,type,id,boardId,callback) {

  async.auto({
    fetch_LithiumSession:function (cb) {
      commonFunctions.errorlogger.info("current working directory",process.cwd()+"userPassword"+JSON.stringify(authData))
      if(config.get('checkForLithiumAttachment'))
      session.getSessionCookies(authData.username,authData.password,config.get('lithium.sessionUrl'),path.resolve(process.cwd(), 'routes/sessionCapturing/testSession.txt'),0,function (err,result) {
        commonFunctions.errorlogger.info(result)
        authData.LithiumCookie=result?result.split('\n').find(x=>{if(x.startsWith("LiSESSIONID"))return x}):"LiSESSIONID=8A81547992D03D584DA67CECFCD1DBAE"
        cb(null,authData)
      })
      else
        cb(null,authData)
    },
    get_all_posts:['fetch_LithiumSession',function (dataAbove,cb) {
      getAllMessages(authData,contentSourceId, type, [],fromDate,toDate, limit,offset,id,boardId, function (err,result) {
        if(!err)
          cb(null,result)
        else
          cb("error in fetching"+type,err)
      })
    }]
  },function (err,result) {
    commonFunctions.errorlogger.error(err)
    commonFunctions.errorlogger.info("all-- ",type);
    callback(result)
  })

}

const getAllContentLithiumvLength=function (req,res) {

  var fromDate=""
  var toDate=""
  if(req.query.fromDate)
    fromDate=new Date(req.query.fromDate).getTime();
  if(req.query.toDate)
    toDate=new Date(req.query.toDate).getTime();

  var contentSourceId = req.query.contentSourceId
  var type = req.query.type

  var object=req.query.object || 'message'
  async.auto({


    fetch_oAuth:function (cb) {
      fetchOAuthData(contentSourceId,function (auth){
        if(auth)
        {
          auth.auth_access_token='Bearer '+auth.auth_access_token;
          cb(null,auth)
        }
        else
          cb("Error at auth",auth)
      })
    },
    get_all_posts:['fetch_oAuth',function (authData,cb) {
      getAllMessagesCount(authData.fetch_oAuth,object,contentSourceId, type, [],fromDate,toDate, 100,0, function (err,result) {
        if(!err)
          cb(null,"data count--"+result)
        else
          cb("error in fetching"+type,err)
      })
    }],
  },function (err,result) {
    commonFunctions.errorlogger.error(err);
    commonFunctions.errorlogger.info("all-- "+type+" synced-- ",JSON.stringify(result.get_all_posts))
    res.send("data"+result.get_all_posts)
  })
}

function processPosts(auth,contentSourceId,result,callback)
{

  var taskPosts=[]
  for(var i=0;i<result.length;i++)
  {
    taskPosts.push((function (i) {
      return function (cb) {

        processPostIndividually(auth,contentSourceId,result[i],function (err,elasticFormat) {
          if(!err)
            cb(null,elasticFormat)
          else
            cb(err)
        })
      }
    })(i))
  }

  async.parallelLimit(taskPosts,5,function (err,elasticFormatArray) {
    if(!err)
    {
      if(elasticFormatArray.length)
        commonFunctions.BulkUpload(elasticFormatArray.join('\n'),function (err,data) {
          callback(null,elasticFormatArray);
        })
      else
        callback(null)
    }
    else
      callback(err)
  })

  //res.send(result)
}

function processPostIndividually(auth,contentSourceId,post,callback) {

  var passData = post
  async.auto({
    pass_data: function (cb) {
      cb(null, passData)
    },
    get_replies: function (cb) {
      getReplies_Tags_Labels(auth, passData.replies.query, [], 10,0, function (resultReplies) {

        cb(null, resultReplies)
      })
    },
    get_labels: function (cb) {
      getReplies_Tags_Labels(auth, passData.labels.query, [], 10,0, function (resultReplies) {
        //console.log("out labels")
        cb(null, resultReplies)
      })
    },
    get_tags: function (cb) {
      getReplies_Tags_Labels(auth, passData.tags.query, [], 10,0, function (resultReplies) {
        //console.log("out tags")
        cb(null, resultReplies)
      })
    },
    get_custom_tags: function (cb) {
      getReplies_Tags_Labels(auth, passData.custom_tags.query, [], 10,0, function (resultReplies) {
        // console.log("out custom tags")
        cb(null, resultReplies)
      })
    },
    get_attachments:function (cb) {
      if(config.get('checkForLithiumAttachment'))
        getReplies_Tags_Labels(auth,passData.attachments.query,[],10,0,function (resultReplies) {
          //  console.log("out attchments")
          cb(null,resultReplies)
          //  cb(null,"2")
        })
      else
        cb(null,"")
    },
    downloadAttachment:['get_attachments',function (getDataAbove,cb) {
      if(config.get('checkForLithiumAttachment'))
        downloadAttachmentForLoop(auth,getDataAbove.get_attachments,function (resultAttachmentArray) {
          cb(null,resultAttachmentArray)
        })
      else
        cb(null,"")
    }],
    strip_body:function (cb) {
      // console.log("out strip body")
      passData.body=commonFunctions.stripHtml(passData.body)
      cb(null,passData.body)

    },
    strip_comment:['get_replies',function (repliesFromAbove,cb) {
      let replies=repliesFromAbove.get_replies.map(x => {
        return x.body
      }).join(" ");

      replies=commonFunctions.stripHtml(replies)
      cb(null,replies)

    }]
  }, function (err, getDataAbove) {

    var ExactData = {
      type:getDataAbove.pass_data.conversation.style,
      view_href: getDataAbove.pass_data.view_href.replace("http:","https:").replace(":80",""),
      replyCount: getDataAbove.get_replies.length,
      boardName: getDataAbove.pass_data.board.short_title,
      boardId: getDataAbove.pass_data.board.id,
      solved: getDataAbove.pass_data.conversation.solved?"Solved":"Unsolved",
      privacy: getDataAbove.pass_data.board.image_privacy,
      parentCategoryName: getDataAbove.pass_data.board.parent_category.short_title ? getDataAbove.pass_data.board.parent_category.short_title : "Other",
      parentCategoryId: getDataAbove.pass_data.board.parent_category.id ? getDataAbove.pass_data.board.parent_category.id : "Other",
      rootCategoryName: getDataAbove.pass_data.board.root_category.short_title ? getDataAbove.pass_data.board.root_category.short_title : "Other",
      rootCategoryId: getDataAbove.pass_data.board.root_category.id ? getDataAbove.pass_data.board.root_category.id : "Other",
      body: getDataAbove.strip_body,
      viewCount: getDataAbove.pass_data.metrics.views,
      kudos: getDataAbove.pass_data.kudos.sum.weight,
      label: getDataAbove.get_labels.map(x => {
        return x.text
      }),
      authorHref: getDataAbove.pass_data.author.view_href,
      author: getDataAbove.pass_data.author.login,
      post_time: getDataAbove.pass_data.post_time,
      subject: getDataAbove.pass_data.subject,
      comments:getDataAbove.strip_comment ,
      tags: getDataAbove.get_custom_tags.map(x => {
        return x.text
      }).concat(getDataAbove.get_tags.map(x => {
        return x.text
      })),
      id: getDataAbove.pass_data.id,
      metadata:[],
      file:'',
      last_update_time:getDataAbove.pass_data.current_revision.last_edit_time
    }

    if(config.get('checkForLithiumAttachment'))
      ExactData["file"]=getDataAbove.downloadAttachment.join(" ").replace(/\s+/g, " ").trim();
    if(commonFunctions.getByteLen(ExactData["file"])>32766)
      ExactData["file"]=ExactData["file"].substring(0,6553)
    if(getDataAbove.get_attachments.length)
      ExactData.metadata.push("Attachments")
    if(getDataAbove.pass_data.videos.count)
      ExactData.metadata.push("Videos")
    if(getDataAbove.pass_data.images.count)
      ExactData.metadata.push("Images")


    let bulkDataFormat= saveDataFormatLithiumObject(auth, ExactData, ExactData.type)
    callback(null,bulkDataFormat)


  })
}

function getAllMessages(auth,contentSourceId,type,messages,startDate,endDate,limit,offset,id,boardId,callback)
{
  var query='SELECT parent,current_revision.last_edit_time,view_href,attachments,videos.count(*),images.count(*),replies,kudos.sum(weight),labels,body,subject,board.parent_category.short_title,board.root_category.short_title,' +
    'board.root_category.id,board.parent_category.id,conversation.solved,conversation.style,tags,custom_tags,board.id,board.short_title,board.image_privacy,metrics.views,author.login,' +
    'author.view_href,post_time,id FROM messages WHERE '
  if(startDate && !id)
    query+= ' conversation.last_post_time>'+startDate+' and'
  if(endDate && !id)
    query+= ' conversation.last_post_time<'+endDate + ' and'
  if(!id)
    query+=' depth=0 and'
  else
    query+=' id=\''+id+'\' and'
  if(type)
    query+=' conversation.style=\''+type+'\' and'
  // if(id)
  //   query+=' and id=\''+id+'\' '
  if(boardId)
    query+=' board.id=\''+boardId+'\' and'


  query=query.substr(0,query.length-3)
  query+=' ORDER BY post_time ASC'
  query+=' limit '+limit+ ' offset '+offset


  commonFunctions.errorlogger.info("query is",query)

  let url=auth.communityUrl+'/api/2.0/search'

  let headers=
    {
      authorization: auth.accessToken,
      'client-id': auth.client_id,
      'content-type': 'application/json' }

  commonFunctions.httpRequest('GET',url,query,"",headers,function (error,body)
  {
    commonFunctions.errorlogger.info("body-->",body);
    if (error) {
      commonFunctions.errorlogger.error("err in response ",error)
      if(error.code == 'ETIMEDOUT') {
        setTimeout(function(){getAllMessages(auth, contentSourceId, type, [], startDate, endDate, limit, offset, id, boardId,callback)}, 10000);
      }
    }
    if(body.http_code==200)
    {
      messages=(body.data.items)

      if(messages.length==1 && messages[0].parent)
      {
        getAllMessages(auth, contentSourceId, type, [], startDate, endDate, limit, offset, messages[0].parent.id, boardId, callback)
      }
      else {
        processPosts(auth, contentSourceId, messages, function (err, dataProcessed) {
          if (!err) {
            if (body.data.items.length < parseInt(limit)) {
              callback(null, messages)
            }
            else {
              commonFunctions.errorlogger.info("offset is", offset)
              offset += parseInt(limit)

              getAllMessages(auth, contentSourceId, type, [], startDate, endDate, limit, offset, id, boardId,callback)
            }
          }
          else {
            commonFunctions.errorlogger.warn("something wrong in process")
            callback("something Wrong at process", err)
          }
        })
      }
    }
    else if(body.statusCode==504)
    {
      setTimeout(function(){getAllMessages(auth,contentSourceId,type,messages,startDate,endDate,limit,offset,id,boardId,callback)}, 10000);

    }
    else if(body.statusCode==401)
    {
      initializeLithiumConnection(auth.contentSourceId,function (result) {
        if(result)
        {
          auth.accessToken=result;
          commonFunctions.errorlogger.info("here is token",auth.accessToken)
          setTimeout(function(){getAllMessages(auth,contentSourceId,type,messages,startDate,endDate,limit,offset,id,boardId,callback)}, 10000);
        }
        else
        {
          callback("err",result)
        }
      })
    }
    else if(body.data.code==203)
    {
      initializeLithiumConnection(auth.contentSourceId,function (result) {
        if(result)
        {
          auth.accessToken=result;
          commonFunctions.errorlogger.info("here is token",auth.accessToken)
          setTimeout(function(){getAllMessages(auth,contentSourceId,type,messages,startDate,endDate,limit,offset,id,boardId,callback)}, 10000);
        }
        else
        {
          callback("err",result)
        }
      })
    }
    else
    {
      setTimeout(function(){getAllMessages(auth,contentSourceId,type,messages,startDate,endDate,limit,offset,id,boardId,callback)}, 10000);
    }

  });
}

function getAllMessagesCount(auth,object,contentSourceId,type,messages,startDate,endDate,limit,offset,callback)
{
  var query='SELECT count(*) FROM '+object+''
  if(type)
    query+=' WHERE depth=0 and conversation.style=\''+type+'\' '

  let url= auth.communityUrl+'/api/2.0/search'

  let headers={
    authorization: auth.accessToken,
    'client-id': auth.lithium_client_id,
    'content-type': 'application/json' }
  commonFunctions.httpRequest('GET',url,query,"",headers,function (error,body)
  {
    if (error)
      body=error;

    if(body.http_code==200)
    {

      messages=(body.data.items)


      callback(null,body.data.count)

    }
    else if(body.statusCode=401)
    {
      initializeLithiumConnection(auth.contentSourceId,function (result) {
        if(result)
        {
          auth.accessToken=result;
          getAllMessagesCount(auth,contentSourceId,type,messages,startDate,endDate,limit,offset,callback)
        }
        else
        {
          callback("err",result)
        }
      })
    }

  });
}

function getAccessTokenRefresh(auth,callback) {
  var options = { method: 'POST',
    url:auth.communityUrl+'/api/2.0/auth/refreshToken',
    qs: { client_id:auth.client_id },
    headers:
      {'content-type': 'application/json' },
    body:
      {
        client_id: auth.client_id,
        client_secret:auth.client_secret,
        refresh_token:auth.refreshToken,
        grant_type: 'refresh_token' },
    json: true };

    commonFunctions.errorlogger.info("options are----",options)
  request(options, function (error, response, body) {
    if (error)
      body=0;
    else {
      commonFunctions.errorlogger.info(body);
      if (body.status == 'Unauthorized') {
        body = 0

      }
      else {
        commonFunctions.errorlogger.info("header is",JSON.stringify(response.headers))
        var oldAccessToken=auth.accessToken;
        var newAccessToken=body.data.access_token;
        auth.accessToken='Bearer '+body.data.access_token

        newAccessToken = aes256.encrypt(commonFunctions.appVariables.analytics.encryptionKey, newAccessToken);
        connection.query("update content_source_authorization set accessToken=? where client_id=?",[newAccessToken,auth.client_id], function (err, rows) {})
        commonFunctions.errorlogger.info("new accessToken",auth.accessToken)
        callback(auth.accessToken);
      }
    }
  });
}

function getReplies_Tags_Labels(auth,query,data,limit,offset,callback)
{
  let method= 'GET'
  let url= auth.communityUrl+'/api/2.0/search'
  let qs= query+' limit '+limit+' offset '+offset

  let headers=
    {
      authorization: auth.accessToken,
      'client-id': auth.client_id,
      'content-type': 'application/json' }

  commonFunctions.httpRequest(method,url,qs,"",headers,function (error,body) {
    commonFunctions.errorlogger.info("body-tags->",body);
    if (error)
    {
      commonFunctions.errorlogger.error("eror is",error)
      body=error;
    }


    if(body && body.http_code==200)
    {

      data=data.concat(body.data.items)
      commonFunctions.errorlogger.warn("here in data 1")
      if(body.data.items.length<=limit)
      {
        commonFunctions.errorlogger.info("here in data 2",body.data.type)
        callback(data)
      }
      else
      {
        if(body.data.list_item_type=='attachment')
        {
          commonFunctions.errorlogger.warn("here in data attachment 4")
          callback(data)
        }
        else {
          commonFunctions.errorlogger.warn("here in data 3")
          offset += parseInt(limit)
          getReplies_Tags_Labels(auth, query, data, limit, offset,callback)
        }
      }
    }
    else if( body && body.code == 'ETIMEDOUT')
    {
      setTimeout(function(){getReplies_Tags_Labels(auth,query,data,limit,offset,callback) }, 10000);
    }
    else if(body && body.statusCode==504)
    {
      setTimeout(function(){getReplies_Tags_Labels(auth,query,data,limit,offset,callback) }, 10000);
    }
    else if(body && body.statusCode==401)
    {

      initializeLithiumConnection(auth.contentSourceId,function (result) {
        if(result)
        {
          auth.accessToken=result;
          commonFunctions.errorlogger.info("here is token",auth.accessToken)
          setTimeout(function(){getReplies_Tags_Labels(auth, query, data, limit, offset,callback)}, 10000);
        }
        else
        {
          commonFunctions.errorlogger.warn("****")
          callback("err",result)
        }
      })
    }
    else
    {
      commonFunctions.errorlogger.info(body)
      setTimeout(function(){getReplies_Tags_Labels(auth, query, data, limit, offset,callback)}, 10000);
    }
  });
}


function downloadAttachmentForLoop(auth,atthmentArr,callback) {
  var task=[]
  var limit=0
  auth.count=0
  for(var a=0;a<atthmentArr.length;a++)
  {
    task.push((function (a) {
      return function (cb) {
        if(atthmentArr[a].filename.split('.').pop()=='pdf' ||
          atthmentArr[a].filename.split('.').pop()=='doc' ||
          atthmentArr[a].filename.split('.').pop()=='docx' ||
          atthmentArr[a].filename.split('.').pop()=='ppt' ||
          atthmentArr[a].filename.split('.').pop()=='pptx' ||
          atthmentArr[a].filename.split('.').pop()=='potx' ||
          atthmentArr[a].filename.split('.').pop()=='csv' ||
          atthmentArr[a].filename.split('.').pop()=='xsl' )
          DownloadAttachment(auth,atthmentArr[a].url,function (text) { //""""""""""""""some Name
            cb(null,text)
          })
        else cb(null,"")
      }
    })(a))
  }
  async.parallel(task,function (err,result) {
    //console.log("data is",result)
    callback(result)
  })
}

function DownloadAttachment(auth,uriImage,callback) {

  commonFunctions.errorlogger.info("url is ",uriImage)

  var headers = {
    'Cookie': auth.LithiumCookie,
  };

  commonFunctions.errorlogger.info("header is",headers)
  var options = {
    url: uriImage,
    headers: headers
  };
  var f=""
  request.get(options).pipe(request.put(config.get('tikaPath')))
    .on("error", err=>{
      commonFunctions.errorlogger.error("err",err)
      callback(err)
    })
    .on('data', da => {
      f += da.toString();
    })
    .on("end", () => {
      if(f.includes("Status 403") && auth.count<5)
        session.getSessionCookies(auth.username,auth.password,config.get('lithium.sessionUrl'),path.resolve(process.cwd(), 'routes/sessionCapturing/testSession.txt'),1,function (err,result) {
          commonFunctions.errorlogger.info(result)
          auth.count++;
          auth.LithiumCookie=result?result.split('\n').find(x=>{if(x.startsWith("LiSESSIONID"))return x}):"LiSESSIONID=8A81547992D03D584DA67CECFCD1DBAE"
          DownloadAttachment(auth,uriImage,callback)
        })
      else {
        auth.count=0
        callback(f.replace(/\s\s+/g, ' '))
      }
    })
}

const saveDataFormatLithiumObject=function(auth,thread,Object) {

  var bulkString = "";
  var bulkStringIndex = {}
  bulkStringIndex["index"] = {}
  bulkStringIndex["index"]["_index"] = auth.indexName;
  bulkStringIndex["index"]["_type"] = Object
  bulkStringIndex["index"]["_id"] = thread.id
  bulkStringIndex = JSON.stringify(bulkStringIndex)

  var bulkStringFieldsData = JSON.stringify(thread)

  var finalString =bulkStringIndex+"\n"+ bulkStringFieldsData+"\n"
  return (finalString)
}

function initializeLithiumConnection(contentSourceId,callback) {

  fetchOAuthData(contentSourceId,function (result) {
    if (result != 0) {

      getAccessTokenRefresh(result,function (resultConnection) {
        callback(resultConnection)
      })
    }
    else {
      callback(0)
    }
  })
}

function fetchOAuthData(contentSourceId,callback)
{
  var sql = "select * from content_source_authorization where content_source_id=? limit 1"
  connection.query(sql,[contentSourceId], function (err, rows) {


    if (rows.length > 0) {
      commonFunctions.errorlogger.info(rows)
      if(rows[0].client_secret)    rows[0].client_secret    =  aes256.decrypt(commonFunctions.appVariables.analytics.encryptionKey, rows[0].client_secret);
      if(rows[0].password)         rows[0].password         =  aes256.decrypt(commonFunctions.appVariables.analytics.encryptionKey, rows[0].password);
      if(rows[0].htaccessPassword) rows[0].htaccessPassword =  aes256.decrypt(commonFunctions.appVariables.analytics.encryptionKey, rows[0].htaccessPassword);
      if(rows[0].accessToken)      rows[0].accessToken      =  aes256.decrypt(commonFunctions.appVariables.analytics.encryptionKey, rows[0].accessToken);
      if(rows[0].refreshToken)     rows[0].refreshToken     =  aes256.decrypt(commonFunctions.appVariables.analytics.encryptionKey, rows[0].refreshToken);
      if(rows[0].sessionId)        rows[0].sessionId        =  aes256.decrypt(commonFunctions.appVariables.analytics.encryptionKey, rows[0].sessionId);
      if(rows[0].privateKey)       rows[0].privateKey       =  aes256.decrypt(commonFunctions.appVariables.analytics.encryptionKey, rows[0].privateKey);
      if(rows[0].instanceURL)       rows[0].communityUrl       =   rows[0].instanceURL;
      return callback(rows[0])
    }
    else
      return callback(0)

  })
}


const syncUserV2 = function(req,res)
{
  var limit= parseInt(req.query.limit) || 100
  var offset=parseInt(req.query.offset) || 0
  var fromDate=""
  var toDate=""
  if(req.query.fromDate)
    fromDate=new Date(req.query.fromDate).getTime();
  if(req.query.toDate)
    toDate=new Date(req.query.toDate).getTime();
  else {
    toDate=new Date(req.query.toDate?req.query.toDate: new Date()).getTime();
  }
  var contentSourceId = req.query.contentSourceId
  var type = req.query.type
  res.send({"message":"Done Crawling"})

  async.auto({


    fetch_oAuth:function (cb) {
      fetchOAuthData(contentSourceId,function (auth){
        if(auth)
        {
          auth.auth_access_token='Bearer '+auth.auth_access_token;
          cb(null,auth)
        }
        else
          cb("Error at auth",auth)
      })
    },
    get_all_users:['fetch_oAuth',function (authData,cb) {
      getAllUsers(authData.fetch_oAuth,contentSourceId, [],fromDate,toDate, limit,offset, function (result) {
        if(result)
          cb(null,result)
        else
          cb("error in fetching"+type,result)
      })
    }]
  },function (err,result) {
    commonFunctions.errorlogger.error(err);
    commonFunctions.errorlogger.info("all-- "+type+" synced--");
  })
}



function getAllUsers(auth,contentSourceId,users,startDate,endDate,limit,offset,callback)
{
  var query='SELECT id,first_name,last_name,messages.count(*),kudos_received.sum(weight),avatar.profile,login,email,web_page_url,view_href,rank.name,' +
    'registration_data.registration_time,last_visit_time,location from users  '
  if(startDate)
    query+= 'and registration_data.registration_time>'+startDate
  if(endDate)
    query+= ' and registration_data.registration_time<'+endDate
  query+=' ORDER BY registration_data.registration_time ASC limit '+limit+ ' offset '+offset

  let headers=
    {
      authorization: auth.auth_access_token,
      'client-id': auth.lithium_client_id,
      'content-type': 'application/json' }
  let url=auth.communityUrl+'/api/2.0/search'
  commonFunctions.httpRequest('GET',url,query,"",headers,function (error,body) {
    if (error)
      body=error;

    if(body.http_code==200)
    {
      users=users.concat(body.data.items)
      processUsers(auth,contentSourceId,users,function (err,dataProcessed) {
        if(!err)
        {
          if(body.data.items.length<100)
          {
            callback(null,users)
          }
          else
          {
            commonFunctions.errorlogger.info("user length",offset)
            offset+=limit
            getAllUsers(auth,contentSourceId,[],startDate,endDate,limit,offset,function (err,result) {
              if(!err)
                callback(err,result)
              else
                callback(err)
            })
          }
        }
        else
          callback("something Wrong at process",err)
      })
    }
    else if(body.statusCode=401)
    {
      initializeLithiumConnection(auth.contentSourceId,function (result) {
        if(result)
        {
          auth.accessToken=result;
          getAllUsers(auth,contentSourceId,users,startDate,endDate,limit,offset,function (result) {
            callback(result)
          })
        }
        else
        {
          callback(result)
        }
      })
    }
  })
}



function processUsers(auth,contentSourceId,result,callback)
{
  var taskPosts=[]
  for(var i=0;i<result.length;i++)
  {
    taskPosts.push((function (i) {
      return function (cb) {
        processUsersIndividually(auth,contentSourceId,result[i],function (err,savedElastic) {
          if(!err)
            cb(null,savedElastic)
          else
            cb(err)
        })
      }
    })(i))
  }

  async.series(taskPosts,function (err,resultSeries) {
    if(!err)
      callback(null,resultSeries)
    else
      callback(err)
  })

  //res.send(result)
}

function processUsersIndividually(auth,contentSourceId,user,callback) {

  var pass_data = user
  var ExactData = {
    type:'User',
    view_href: pass_data.view_href,
    login:pass_data.login,
    email:pass_data.email,
    rank:pass_data.rank.name,
    avatar:pass_data.profile,
    registration_time:pass_data.registration_data.registration_time,
    post_time:pass_data.registration_data.registration_time,
    last_visit_time:pass_data.last_visit_time,
    id: pass_data.id,
    metadata:[],
    postCount:pass_data.messages.count,
    kudos:pass_data.kudos_received.sum.weight,
    name:pass_data.first_name+" "+pass_data.last_name
  }



  var sql = "select * from lithium_content_types_to_sync where contentSourceId=? ";
  connection.query(sql, [contentSourceId], function (err, rows) { // function is used to get names of lithium content types


    saveDataFormatLithiumObject(auth, ExactData, ExactData.type, rows, function (resultBulkData) {
      // console.log("Done--->>>---")
      // res.send(authObj);
      bulkUpload.singleUploadCallback(resultBulkData,function (cbRestur) {
        callback(null,"no error")
      })

    })

  })
}

const getAllboards=function (auth,contentSourceId,boards,limit,offset,id,callback) {

  var query='SELECT view_href,title,short_title,id,conversation_style FROM boards '

  if(id)
    query+=' where id=\''+id+'\' '

  query+=' limit '+limit+ ' offset '+offset

  commonFunctions.errorlogger.info("query is",query)

  let url=auth.communityUrl+'/api/2.0/search'

  let headers=
    {
      'client-id': auth.client_id,
      authorization: auth.accessToken,
      'content-type': 'application/json' }


  commonFunctions.httpRequest('GET',url,query,"",headers,function (error,body)
  {

    if (error)
      body=error;
    if(body.http_code==200)
    {
      boards=boards.concat(body.data.items)

      if(!error)
      {
        if(body.data.items.length<100)
          callback(null,boards)
        else {
          commonFunctions.errorlogger.info("ofset is",offset)
          offset+=100

          getAllboards(auth,contentSourceId,boards,limit,offset,id,function (err,result) {
            if(!err)
              callback(null,result)
            else
              callback(err)
          })
        }
      }
      else
        callback("something Wrong at process",error)
    }
    else
      if(body.statusCode=400)
      {
        callback(null,[])
      }
    else if(body.statusCode=401)
    {
      initializeLithiumConnection(contentSourceId,function (result) {
        if(result)
        {
          auth.accessToken=result;
          getAllboards(auth,contentSourceId,boards,limit,offset,id,function (err,result) {
            callback(null,result)
          })
        }
        else
        {
          callback("err",result)
        }
      })
    }

  });
}

module.exports={
  getAllboards:getAllboards,
  getallContentV2:getallContentV2
}
