const request = require('request');

/* Fetch default and custom Fields */

const fetchManagementSets = function  (resultData, callback) {
  let url = (new URL(resultData.contentSource.url)).hostname;
  var options = {
    method: 'GET',
    url:  'https://' + url + '/api/v2/guide/permission_groups.json'
  };
  if (resultData.authorization.authorization_type == "Basic") {
    options.auth = {
      'user': resultData.authorization.username,
      'pass': resultData.authorization.password
    }
  }
  else if (resultData.authorization.authorization_type == "OAuth") {
    options["headers"] = {
      "authorization": "Bearer " + resultData.authorization.accessToken
    }
  }
  request(options, function (error, response, body) {
    if (error){ 
      callback(error, []);
    }
    else {
      callback(null, JSON.parse(body).permission_groups);
    }
  });
}

const fetchUserSegments = function (resultData, callback) {
  let url = (new URL(resultData.contentSource.url)).hostname;
  var options = {
    method: 'GET',
    url:  'https://' + url + '/api/v2/help_center/user_segments',
    headers:
        {
          'Accept': 'application/json'
        }
  };
  if (resultData.authorization.authorization_type == "Basic") {
    options.auth = {
      'user': resultData.authorization.username,
      'pass': resultData.authorization.password
    }
  }
  else if (resultData.authorization.authorization_type == "OAuth") {
    options["headers"] = {
      'Accept': 'application/json',
      "authorization": "Bearer " + resultData.authorization.accessToken
    }
  }
  request(options, function (error, response, body) {
    if (error) {
      callback(error, []);
    }
    else {
      callback(null,JSON.parse(body).user_segments);
    }
  });
}


module.exports={
  fetchManagementSets: fetchManagementSets,
  fetchUserSegments: fetchUserSegments
}
