module.exports = {
    "objects": [{
        "name": "article",
        "label": "Knowledge Articles",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [
        {
            "name": "id",
            "label": "id",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "author",
            "label": "Author",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "category",
            "label": "Category",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1
        },
        {
            "name": "meta_description",
            "label": "Meta Description",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "current_version_title",
            "label": "Title",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "current_version_description",
            "label": "Description",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'summary',
            "label": 'Summary',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'searchTitle',
            "label": 'Search Title',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'name',
            "label": 'Name',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'inherited_roles',
            "label": 'Inherited Roles',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'reader_roles',
            "label": 'Reader Roles',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'root_category',
            "label": 'Root Category',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'status',
            "label": 'Status',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'tags',
            "label": 'Tags',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'url_hash',
            "label": 'URL',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "data_category_nested",
            "label": "Nested Category",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1,
            "isMerged": 1, 
            "isMasked": 0
        },
        {
            "name": "viewHref",
            "label": "href",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 0,
            "isSearchable": 1,
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "banner",
            "label": "Banner",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 0,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "thumbnail",
            "label": "Thumbnail",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 0,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "date_created",
            "label": "Created Date",
            "type": "datetime",
            "isFilterable": 1,
            "isMerged" : 1,
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "date_modified",
            "label": "Modified Date",
            "type": "datetime",
            "isFilterable": 1,
            "isMerged" : 1,
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1,
            "isMasked": 0
        }
    ]
}
