<#attempt>

<#assign boardsArr = '' />
<#list rest("/boards/nested").boards.board as board>
<#assign allowed=rest("/boards/id/${board.id}/messages/view/allowed?").value>
<#if allowed== "true">
<#assign boardsArr = boardsArr+board.id+','/>
 </#if>
<#if board?is_last> 
<#assign  lengthString=boardsArr?length/>
<#assign  boardsArr=boardsArr[0..*lengthString-1]/>
    </#if>
</#list>
<#assign boardsArr = boardsArr + ',0' />


<#assign rolesArr = '' />
<#list restadmin("/users/id/${user.id}/roles?").roles.role as role>
<#assign rolesArr = rolesArr+role.name+','/>
<#if role?is_last> 
<#assign  lengthString=rolesArr?length/>
<#assign  rolesArr=rolesArr[0..*lengthString-1]/>
    </#if>
</#list>

{"rolesArr": "${rolesArr}", "boardsArr": "${boardsArr}" }

<#recover>
<!-- Recovered -->
</#attempt>