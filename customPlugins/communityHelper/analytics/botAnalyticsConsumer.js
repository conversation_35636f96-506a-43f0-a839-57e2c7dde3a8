var express = require('express');
var router = express.Router();
const path = require('path');
environment = require('../../../routes/environment');
process.env.NODE_ENV = environment.configuration;
const configPath = path.join(__dirname, '../../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
const commonFunctions = require('../../../utils/commonFunctions');
// const connection_sql = require('../../../utils/connection');
const { spawn } = require('child_process');
const kafkaLib = require('../../../utils/kafka/kafka-lib');


const pitchAnalyticsForIds = (req, cb) => {
    const tenantId = req.headers['tenant-id'];
    commonFunctions.getAddonsStatus(req, async (error, result) => {
        if (result && result.length && result[13]) {
            try {
                const obj = {
                    getIds: true ,
                    tenantId
                }
                await kafkaLib.publishMessage({
                    topic: config.get("kafkaTopic.communityBotAnalytics"),
                    messages: [{
                        value: JSON.stringify(obj),
                    }]
                })
            } catch (e) {
                console.log(e);
            }
            cb(null, [])
        } else {
            commonFunctions.errorlogger.info("Bot not installed");
            cb(null, [])
        }
    });

}
const botAnalyticsConsumer = (req, cb) => {
    pitchAnalyticsForIds(req, (error, results) => {
        cb();
    })
};


module.exports = {
    botAnalyticsConsumer
}
