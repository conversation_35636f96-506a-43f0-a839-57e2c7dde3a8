
module.exports = {
    "objects": [
        {
            "name": "discussion",
            "label": "Discussion",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields": [
                {
                    "name": "DiscussionKey",
                    "label": "Discussion Key",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "DiscussionName",
                    "label": "Discussion Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "CommunityKey",
                    "label": "Community Key",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1,
                    "isMerged":1
                },
                {
                    "name": "CommunityName",
                    "label": "Community Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1,
                    "isMerged":1
                },
                {
                    "name": "DisplayName",
                    "label": "Author Display Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "EmailAddress",
                    "label": "Author Email",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "ContactStatusCode",
                    "label": "Author Contact Status Code",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "LinkToDiscussion",
                    "label": "Link To Discussion",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "LinkToMessageInContext",
                    "label": "Link To Message In Context",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "DiscussionUrl",
                    "label": "Discussion Url",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "MessageStatus",
                    "label": "Message Status",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "IsAnswer",
                    "label": "Is Answer",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "Subject",
                    "label": "Subject",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "post_time",
                    "label": "Created Date",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1,
                    "isMerged":1
                },
                {
                    "name": "BodyWithoutMarkup",
                    "label": "Discussion Body",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "Comments",
                    "label": "Discussion Commets",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "RecommendationCount",
                    "label": "Discussion Recommend Count",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": 'DiscussionTags',
                    "label": 'Topics',
                    "type": 'string',
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                  },
                  {
                    "name": "attachment_Attachments",
                    "label": 'Attachments',
                    "type": 'string',
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1,
                    "isMerged": 1,
                  }
                  
            ]
        },
        {
            "name": "event",
            "label": "Event",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields": [
            {
                "name": "EventKey",
                "label": "EventKey",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "Address1",
                "label": "Address1",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "Address2",
                "label": "Address2",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "Address3",
                "label": "Address3",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "AddressKey",
                "label": "AddressKey",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "CountryCode",
                "label": "Country Code",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "CountryName",
                "label": "Country Name",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "EventTitle",
                "label": "Event Title",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "EventTypeName",
                "label": "Event Type Name",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "EventTypeDescription",
                "label": "Event Type Description",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "CommunityKey",
                "label": "Community Key",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
                "isMerged":1
            },
            {
                "name": "CommunityName",
                "label": "Community Name",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
                "isMerged":1
            },
            {
                "name": "EventDescription",
                "label": "Event Description",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "LinkToEventDetails",
                "label": "Event Link",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "post_time",
                "label": "Created Date",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
                "isMerged":1
            },
            {
                "name": 'EventTags',
                "label": 'Topics',
                "type": 'string',
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                name: 'event_attachments',
                label: 'Event Attachments',
                type: 'string',
                isFilterable: 1,
                isSortable: 1,
                isSearchable: 1,
                isActive: 1
            }

            ]
        }, {
            "name": "library",
            "label": "Library",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields": [
                {
                    "name": "DocumentKey",
                    "label": "Document Key",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "DocumentTitle",
                    "label": "Document Title",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "Description",
                    "label": "Description",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "ContactKey",
                    "label": "Contact Key",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "DisplayName",
                    "label": "Display Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "ContactStatusCode",
                    "label": "Contact Status Code",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "CompanyName",
                    "label": "Company Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "CompanyTitle",
                    "label": "Company Title",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "LegacyContactKey",
                    "label": "Legacy Contact Key",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "LinkToLibraryDocument",
                    "label": "Link To Library Document",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "post_time",
                    "label": "Created Date",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1,
                    "isMerged":1
                },
                {
                    "name": "DownloadCount",
                    "label": "Download Count",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "AttachmentCount",
                    "label": "Attachment Count",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "CommunityName",
                    "label": "Community Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1,
                    "isMerged":1
                },
                {
                    "name": "CommunityKey",
                    "label": "Community Key",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1,
                    "isMerged":1
                },
                {
                  "name": 'LibraryTags',
                  "label": 'Topics',
                  "type": 'string',
                  "isFilterable": 1,
                  "isSortable": 1,
                  "isSearchable": 1,
                  "isActive": 1
                },
                {
                    "name": "attachment_Attachments",
                    "label": 'Attachments',
                    "type": 'string',
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1,
                    "isMerged": 1,
                }
                
            ]
        }, {
            "name": "blog",
            "label": "Blog",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields": [
            {
                "name": "DisplayName",
                "label": "Display Name",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "ContactKey",
                "label": "Contact Key",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "BlogKey",
                "label": "Blog Key",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "CommunityKey",
                "label": "Community Key",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
                "isMerged":1
            },
            {
                "name": "CommunityName",
                "label": "Community Name",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
                "isMerged":1
            },
            {
                "name": "CommunityTypeName",
                "label": "Community Type Name",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "BlogText",
                "label": "Blog Text",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "BlogTitle",
                "label": "Blog Title",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "post_time",
                "label": "Created Date",
                "type": "datetime",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1,
                "isMerged":1
            },
            {
                "name": "LinkToReadBlog",
                "label": "Link To Read Blog",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "ViewCount",
                "label": "Blog Views",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "RatingCount",
                "label": "Blog Rating",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
                {
                    "name": "Comments",
                    "label": "Blog Comments",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "CommentPermission",
                    "label": "Blog Comment Permission",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "ViewPermission",
                    "label": "Blog View Permission",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "BlogTags",
                    "label": "Blog Tags",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                }
            ]
        },
        {
            "name": "announcement",
            "label": "Announcement",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields": [
            {
                "name": "AnnouncementKey",
                "label": "Announcement Key",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "AnnouncementText",
                "label": "Announcement Text",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "AnnouncementTitle",
                "label": "Announcement Title",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "AnnouncementType",
                "label": "Announcement Type",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "CommunityKey",
                "label": "Community Key",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
                "isMerged": 1
            },
            {
                "name": "CommunityName",
                "label": "Community Name",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
                "isMerged": 1
            },
            {
                "name": "CreatedByContactKey",
                "label": "Created By ContactKey",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "ContactDisplayName",
                "label": "Contact Display Name",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "LinkToContactProfile",
                "label": "Link To Contact Profile",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "ContactLargePictureUrl",
                "label": "Contact Large Picture Url",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "ContactSmallPictureUrl",
                "label": "Contact Small Picture Url",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
            },
            {
                "name": "CreatedOn",
                "label": "CreatedOn",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
           },
            {
                "name": "MicrositeGroupTypeRouteDesignKey",
                "label": "Microsite Group Type Route Design Key",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "Tags",
                "label": "Tags",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },{
                "name": "UpdatedByContactKey",
                "label": "Updated By Contact Key",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
            },{
                "name": "UpdatedOn",
                "label": "UpdatedOn",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
            },{
                "name": "UserPermissions",
                "label": "User Permissions",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
            },
            {
                "name": "LinkToAnnouncements",
                "label": "LinkToAnnouncements",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
            }

            ]
        }
        //,
        // {
        //     "name": "member",
        //     "label": "Members",
        //     "status": 0,
        //     "boosting_factor": 1,
        //     "base_url": "",
        //     "fields": [
        //         {
        //         "name": "id",
        //         "label": "id",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1
        //     },
        //     {
        //         "name": "LinkToProfile",
        //         "label": "Link To Profile",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1
        //     },
        //     {
        //         "name": "PictureUrl",
        //         "label": "Link To Picture Profile",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1
        //     },
        //     {
        //         "name": "ContactKey",
        //         "label": "Contact Key",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1
        //     },
        //     {
        //         "name": "DisplayName",
        //         "label": "Display Name",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1
        //     }
        //     ,
        //     {
        //         "name": "EmailAddress",
        //         "label": "Email",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1
        //     }
        //     ,
        //     {
        //         "name": "CompanyName",
        //         "label": "Company Name",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1
        //     }
        //     ,
        //     {
        //         "name": "CompanyTitle",
        //         "label": "Company Title",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1
        //     }
        //     ,
        //     {
        //         "name": "Designation",
        //         "label": "Designation",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1
        //     }
        //     ,
        //     {
        //         "name": "LegacyContactKey",
        //         "label": "Legacy Contact Key",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1
        //     },
        //     {
        //         "name": "IsCompany",
        //         "label": "Is Company",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1
        //     },
        //     {
        //         "name": "HLCommunityKeys",
        //         "label": "Community Keys",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1,
        //         "isMerged":1
        //     },
        //     {
        //         "name": "HLCommunityNames",
        //         "label": "Community Names",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1,
        //         "isMerged":1
        //     },
        //     {
        //         "name": "post_time",
        //         "label": "Created Date",
        //         "type": "string",
        //         "isFilterable": 1,
        //         "isSortable": 1,
        //         "isSearchable": 1,
        //         "isActive": 1,
        //         "isMerged":1
        //     }
        //     ] 
        // }
    ]
};
