const crawlerCache = require('../../utils/crawler-config-cache');

const initKhorosAuroraConfig =  ( tenantId, cb ) => {

  let khorosAuroraObjectFields = {};
  crawlerCache.fetchCrawlerConfig({ tenantId, RETRY_COUNT: 1, configKey: 'khorosAurora' }, function(response){
    khorosAuroraObjectFields  = {
      objects: [
        {
          name: 'ka_idea',
          label: 'Idea',
          status: 1,
          boosting_factor: 1,
          base_url: '',
          fields: []
    
        },
        {
          name: 'ka_blog',
          label: 'Blog',
          status: 1,
          boosting_factor: 1,
          base_url: '',
          fields: []
        },
        {
          name: 'ka_forum',
          label: 'Discussion',
          status: 1,
          boosting_factor: 1,
          base_url: '',
          fields: []
        },
        {
          name: 'ka_tkb',
          label: 'Knowledge Base',
          status: 1,
          boosting_factor: 1,
          base_url: '',
          fields: []
        },
        {
          name: 'ka_group',
          label: 'Group',
          status: 1,
          boosting_factor: 1,
          base_url: '',
          fields: []
        },
        {
          name: 'ka_contest',
          label: 'Contest',
          status: 1,
          boosting_factor: 1,
          base_url: '',
          fields: []
        },
        {
          name: 'ka_qanda',
          label: 'QandA',
          status: 1,
          boosting_factor: 1,
          base_url: '',
          fields: []
        },
        {
          name: 'ka_review',
          label: 'Review',
          status: 1,
          boosting_factor: 1,
          base_url: '',
          fields: []
        },
        {
          name: 'ka_occasion',
          label: 'Occasion',
          status: 1,
          boosting_factor: 1,
          base_url: '',
          fields: [
            {
              name: 'occasionType',
              label: 'Occasion Type',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'subject',
              label: 'Title',
              type: 'string',
              isFilterable: 1, // indexing method analyzed OR not analyzed
              isSortable: 1,
              isSearchable: 1, // analyzer
              isActive: 1
            },
            {
              name: 'kudos',
              label: 'Kudos',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'body',
              label: 'Description',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'parentCategoryName',
              label: 'Parent Category',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'data_category_nested',
              label: 'Data Category Nested',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isMerged: 1,
              isActive: 1
            },
            {
              name: 'data_category_flat',
              label: 'Data Category Flat',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'products',
              label: 'Products',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'parentCategoryId',
              label: 'Parent Category Id',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'rootCategoryName',
              label: 'Root Category',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'rootCategoryId',
              label: 'Root Category Id',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'post_time',
              label: 'Created Date',
              type: 'datetime',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 0,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'boardName',
              label: 'Board',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'ka_boardId',
              label: 'Board Id',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'author',
              label: 'Author Name',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'authorHref',
              label: 'Author Url',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'type',
              label: 'Type',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'view_href',
              label: 'View Href',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'replyCount',
              label: 'Reply Count',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'solved',
              label: 'Solution',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'viewCount',
              label: 'Views',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'comments',
              label: 'Comments',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'metadata',
              label: 'Metadata',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'id',
              label: 'Id',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'file',
              label: 'file',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'replied',
              label: 'Replied',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'moderation_status',
              label: 'Moderation Status',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'grouphub_id',
              label: 'Grouphub id',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'boardNameTitle',
              label: 'Board Title',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'grouphub_short_title',
              label: 'Grouphub Short Title',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'grouphub_description',
              label: 'Grouphub Description',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'grouphub_view_href',
              label: 'Grouphub Link',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'grouphub_membership_type',
              label: 'Grouphub Membership Type',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1
            },
            {
              name: 'grouphub_title',
              label: 'Grouphub Title',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'last_update_time',
              label: 'Last Updated Date',
              type: 'datetime',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 0,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'state',
              label: 'state',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            },
            {
              name: 'attachment_multiple',
              label: 'Attachment Data',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isMerged: 1,
              isActive: 1
            },
            {
              name: 'tags',
              label: 'Tags',
              type: 'string',
              isFilterable: 1,
              isSortable: 1,
              isSearchable: 1,
              isActive: 1,
              isMerged: 1
            }
          ]
        }
      ],
      fields: [
        {
          name: 'subject',
          label: 'Title',
          type: 'string',
          isFilterable: 1, // indexing method analyzed OR not analyzed
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'kudos',
          label: 'Kudos',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'body',
          label: 'Description',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'parentCategoryName',
          label: 'Parent Category',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isMerged: 1,
          isActive: 1
        },
        {
          name: 'data_category_nested',
          label: 'Data Category Nested',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isMerged: 1,
          isActive: 1
        },
        {
          name: 'data_category_flat',
          label: 'Data Category Flat',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1,
          isMerged: 1
        },
        {
          name: 'products',
          label: 'Products',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1,
          isMerged: 1
        },
        {
          name: 'parentCategoryId',
          label: 'Parent Category Id',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'rootCategoryName',
          label: 'Root Category',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isMerged: 1,
          isActive: 1
        },
        {
          name: 'rootCategoryId',
          label: 'Root Category Id',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'post_time',
          label: 'Created Date',
          type: 'datetime',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 0,
          isActive: 1,
          isMerged: 1
        },
        {
          name: 'boardName',
          label: 'Board',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1,
          isMerged: 1
        },
        {
          name: 'ka_boardId',
          label: 'Board Id',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1,
          isMerged: 1
        },
        {
          name: 'author',
          label: 'Author Name',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1,
          isMerged: 1,
        },
        {
          name: 'authorHref',
          label: 'Author Url',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'type',
          label: 'Type',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isMerged: 1,
          isActive: 1
        },
        {
          name: 'view_href',
          label: 'View Href',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'replyCount',
          label: 'Reply Count',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isMerged: 1,
          isActive: 1
        },
        {
          name: 'solved',
          label: 'Solution',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isMerged: 1,
          isActive: 1
        },
        {
          name: 'viewCount',
          label: 'Views',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        // {
        //   name: 'label',
        //   label: 'Label',
        //   type: 'string',
        //   isFilterable: 1,
        //   isSortable: 1,
        //   isSearchable: 1,
        //   isActive: 1
        // },
        {
          name: 'comments',
          label: 'Comments',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'tags',
          label: 'Tags',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1,
          isMerged: 1
        },
        {
          name: 'metadata',
          label: 'Metadata',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'id',
          label: 'Id',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'file',
          label: 'file',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'replied',
          label: 'Replied',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1,
          isMerged: 1
        },
        {
          name: 'moderation_status',
          label: 'Moderation Status',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'grouphub_id',
          label: 'Grouphub id',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'boardNameTitle',
          label: 'Board Title',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1,
          isMerged: 1
        },
        {
          name: 'grouphub_short_title',
          label: 'Grouphub Short Title',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isMerged: 1,
          isActive: 1
        },
        {
          name: 'grouphub_description',
          label: 'Grouphub Description',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'grouphub_view_href',
          label: 'Grouphub Link',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'grouphub_membership_type',
          label: 'Grouphub Membership Type',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'grouphub_title',
          label: 'Grouphub Title',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isMerged: 1,
          isActive: 1
        },
        {
          name: 'last_update_time',
          label: 'Last Updated Date',
          type: 'datetime',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 0,
          isActive: 1,
          isMerged: 1
        },
        {
          name: 'state',
          label: 'state',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isMerged: 1,
          isActive: 1
        },
        {
          name: 'attachment_multiple',
          label: 'Attachment Data',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isMerged: 1,
          isActive: 1
        }
      ]
    };

    if(response.config_value.crawlObjects.duplicateObjectTkb){
      khorosAuroraObjectFields.objects.push(
        {
          "name":"ka_tkb1",
          "label":"Knowledge Base All",
          "status":1,
          "boosting_factor":1,
          "base_url":"",
          "fields":[]
        }
      );
    }

    if(response.config_value.crawlObjects.fieldProductCategoryMapping){
      khorosAuroraObjectFields.fields.push(
        {
          "name":"Product_category_flat",
          "label":"Product flat",
          "type":"string",
          "isFilterable":1,
          "isSortable":1,
          "isSearchable":1,
          "isActive":1
        }
      );
    }
    
    cb(null, khorosAuroraObjectFields)
  })
}


module.exports = {
  initKhorosAuroraConfig : initKhorosAuroraConfig
}
