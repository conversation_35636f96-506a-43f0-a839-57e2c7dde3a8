var async = require('async');
config = require('config');
var commonFunctions = require('../../utils/commonFunctions')
var searchClient = require('../../routes/admin/searchClient');
const request = require("request");


function updateKeyword(d,req,cb) {
    let querySql;
    querySql = `UPDATE keyword_boost SET object_label='${d.object_label}' ,source_label='${d.source_label}' WHERE record_id = '${d.record_id}'`;
    connection.query(querySql, (err, docs) => {
        cb(null,{})
    })
}


const runScript = (cb) => {
    var taskArray = [];
    var updateArray = [];
    connection.query(`SELECT * FROM search_clients`, (err, docs) => {
        let searchClients = docs
        searchClients.forEach((s) => {
            taskArray.push(getSearchClientSettingsFields.bind(null, s.id, s.uid,req))
        })
        async.series(taskArray, (err, docs) => {
            for (let i = 0; i < docs.length; i++) {
                docs[i].forEach((d) => {
                    updateArray.push(updateKeyword.bind(null, d,req));
                })
            }
            async.series(updateArray, (err, docs) => {
                console.log("Keyword Boosting Table Updated!!");
                cb(err, docs);
            })
        })
    });
}


const getSearchClientSettingsFields = (seachClientId, uid, req,callback) => {
    // Get fields
    let arrtypes = [];
    async.auto({
        get_search_client_settings: (cb) => {
            searchClient.getSearchClient(seachClientId,req,cb);
        },
        getDisplayFields: (cb) => {
            commonFunctions.getDisplayFields(uid,req, function (displayRows) {

                var rowsDisplay = displayRows
                for (var iy = 0; iy < rowsDisplay.length; iy++) {
                    if (rowsDisplay[iy]["display_field_name"] == "href") {
                        href = rowsDisplay[iy]["elastic_field_name"];
                    }
                }

                const unique = [...new Set(rowsDisplay.map(item => item.elastic_object_name))];
                unique.forEach(x => {
                    arrtypes.push(
                        {
                            "type": x,
                            "source_label":[...new Set (rowsDisplay.filter(z => z.elastic_object_name == x).map(y => y.indexLabel ))],
                            "object_label":[...new Set (rowsDisplay.filter(z => z.elastic_object_name == x).map(y => y.elastic_object_label ))],
                            "values": rowsDisplay.filter(z => z.elastic_object_name == x).map(y => { return ({ "fieldName": y.elastic_field_name, "Displaytype": y.display_field_name }) })
                        }
                    )
                })
                cb(null, {})
            })
        },
        getAllKeywordRelatedToId: cb => {
            const query = `SELECT * from keyword_boost WHERE search_client_id = ?`;
            connection.query(query, [seachClientId], (err, docs) => {
                cb(null, docs);
            });
        },
        fetchDataAndSave: ['get_search_client_settings', 'getAllKeywordRelatedToId', 'getDisplayFields', (results, cb) => {
            let boostDoc = results.getAllKeywordRelatedToId;
            async.parallel(boostDoc.map(r => {
                return cb => {
                    let options = {
                        method: 'GET',
                        url: `http://${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/${encodeURIComponent(r.index_name)}/${encodeURIComponent(r.index_type)}/${encodeURIComponent(r.record_id)}`,
                        headers: { 'content-type': 'application/json' },
                        json: true
                    };
                    request(options, (error, response, body) => {
                        if (body && body.found) {
                            var typeData = arrtypes.find(x => x.type == body["_type"])
                            if(typeData){
                            r.url = body._source.view_href;
                            var titleFieldName = typeData["values"].find(x => { if (x["Displaytype"] == "Title") return x }) ? typeData["values"].find(x => { if (x["Displaytype"] == "Title") return x })["fieldName"] : "_id"
                            r.subject = body._source[titleFieldName] ? body._source[titleFieldName] : body._source.title
                            //logic for href----------------**********************************
                            r.url = results.get_search_client_settings.sources.filter(x => x.enabled).map(y => { let o = y.objects.filter(z => z.enabled); return o }).reduce(commonFunctions.reduceObject).find(x => x.name == body["_type"]).base_href
                            r.source_label = typeData.source_label.length ? typeData.source_label[0]: '';
                            r.object_label = typeData.object_label.length ? typeData.object_label[0] : '';
                            var testRegex = /{{.*?}}/g;
                            var str = r.url;
                            var m;
                            while ((m = testRegex.exec(str)) !== null) {
                                // This is necessary to avoid infinite loops with zero-width matches
                                if (m.index === testRegex.lastIndex) {
                                    testRegex.lastIndex++;
                                }
                                m.forEach((match, groupIndex) => {
                                    r.url = r.url.replace(match, body["_source"][match.replace("{{", "").replace("}}", "")])
                                    commonFunctions.errorlogger.info(match);
                                });
                            }
                            r.deindex = 0;
                            if (!r.url)
                                r.url = body["_id"] || body["_source"]['id'];
                        }
                        }
                        else {
                            r.url = "";
                            r.recordId = r.record_id;
                            r.subject = "Document Not Found"
                            r.deindex = 1;
                        }
                        cb(null, r)
                    });
                }
            }), (error, results) => {
                callback(null, boostDoc);
            });
        }]
    })
}




module.exports = {
    runScript
}