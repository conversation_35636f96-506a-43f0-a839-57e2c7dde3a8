const path = require('path');
const configPath = path.join(__dirname, '../../config');
process.env.NODE_CONFIG_DIR = configPath;
const logger = require('tracer').console({
    level: 'info',
    format: "{{timestamp}} <{{title}}> {{message}} (in {{file}}:{{line}}) @crawlinglogs@@"
});

const request = require('request');

const alertGoogleChat = ({
    url,
    event,
    message
}) => {
    logger.info(`alerting google chat ${event}`);
    const options = {
        method: 'POST',
        url,
        body: { text: message },
        json: true
    };
    request(options, (error, response, body) => {
        if (error) {    
            logger.error('Alert Error', error);
        } else if (response || body) {
            logger.info('Response Body', body);
        }
    });
}

exports.alertGoogleChat = alertGoogleChat;