/**
 * <AUTHOR>
 * @version 1
 */
 var express = require('express');
 var router = express.Router();
var path = require('path');
var elastic = require('elasticsearch');
var fs = require('fs');
var async = require('async');
var request = require('request');
var express = require('express');
const kafkaLib = require("../../../utils/kafka/kafka-lib");
environment = require(path.join(__dirname, '../../../routes/environment'));
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../../config');
process.env.NODE_CONFIG_DIR = configPath;
console.log(environment.configuration);
config = require('config');
const multiTenantSupport = config.get('multiTenantSupport');
const { getOsClient } = require('../../../utils/opensearch');


var facet_index;

const faceInterpreter = async (req, cb) => {

    const  tenantId = req.headers['tenant-id'];
    const tpk = req.headers.session.tpk;
    const tenantInfo = { tenantId, tpk };
    repeatforSc(tenantInfo, cb);
}

async function repeatforSc (tenantInfo, cb){
    console.log("IN FUNCTION :: repeatforSc")
    var sql = "SELECT id, uid from search_clients";
    connection[tenantInfo.tenantId].execute.query(sql, function(sqlErr, sqlRes){
        if(sqlErr){
            console.log("SQL ERROR :: "+sqlErr);
        }
        else if(sqlRes && sqlRes.length <= 0){
            console.log("ERROR MESSAGE :: No Search Client Present");
        }
        else{
            console.log("TOTAL SEARCH cLIENT COUNT :: "+sqlRes.length);
            asyncTask = [];
            for(var i =0; i<sqlRes.length; i++){
                asyncTask.push(function(i){
                    return function(cb){
                        console.log("STARTED WITH ITERATION :: "+i +" searchClientUId "+ sqlRes[i].uid);
                        start(sqlRes[i].id, sqlRes[i].uid, {}, tenantInfo, function(err, data){
                            console.log("COMPLETED WITH ITERATION :: "+i+"\n");
                            cb(null, { message: `completed with iteration ${i}`})
                        });
                    }
                }(i));
            }
            async.series(asyncTask, function(err, data){
                console.log("DONE WITH ALL SEARCH CLIENTS");
                cb(null, {message: "FACET DATA SUCCESSFULLY PUBLISHED"});
            });
        }
    });
}

async function start(searchClientId, searchClientUId, facetJson, tenantInfo, cbx) {
    const osClient = await getOsClient(tenantInfo.tenantId);
    facetMap = {};
    async.auto({
        "fetch_cs_objects": (cb) => {
            console.log("IN ASYNC AUTO PART :fetch_cs_objects");
            var sql = "Select DISTINCT(content_source_object_id) from auto_tuning_settings where search_client_id = ? AND auto_tuning_configuration_type = ? AND is_selected = 1";
            connection[tenantInfo.tenantId].execute.query(sql, [searchClientId, 1], function (err, data) {
                if (err) {
                    console.log("ERROR :: " + err);
                }
                else {
                    facetJson[searchClientId] = {};
                    for (var i = 0; i < data.length; i++) {
                        facetJson[searchClientId][data[i].content_source_object_id] = {};
                    }
                    cb(null, facetJson[searchClientId]);
                }
            });
        },
        "fetch_cs_object_info": ['fetch_cs_objects', (dfa, cb) => {
            console.log("IN ASYNC AUTO PART :fetch_cs_object_info");
            asyncObjectTask = [];
            for (var i = 0; i < Object.keys(dfa.fetch_cs_objects).length; i++) {
                asyncObjectTask.push(function (i) {
                    return function (cbx) {
                        var sql = "SELECT * from content_source_objects WHERE id = ?";
                        connection[tenantInfo.tenantId].execute.query(sql, [Object.keys(dfa.fetch_cs_objects)[i]], function (err, data) {
                            if (err) {
                                console.log("ERROR :: "+err);
                                cbx(err, null);
                            }
                            else {
                                facetJson[searchClientId][Object.keys(dfa.fetch_cs_objects)[i]]['info'] = {};
                                facetJson[searchClientId][Object.keys(dfa.fetch_cs_objects)[i]]['info']['object_name'] = data[0].name;
                                var sql1 = "SELECT * from content_sources WHERE id = ?";
                                connection[tenantInfo.tenantId].execute.query(sql1, [data[0].content_source_id], function (err1, data1) {
                                    if (err1) {
                                        console.log("ERROR :: "+err1);
                                        cbx(err1, null);
                                    }
                                    else {
                                        facetJson[searchClientId][Object.keys(dfa.fetch_cs_objects)[i]]['info']['index_name'] = data1[0].elasticIndexName;
                                        facetJson[searchClientId][Object.keys(dfa.fetch_cs_objects)[i]]['info']['index_id'] = data1[0].id;
                                        cbx(null, data1);
                                    }
                                });
                            }
                        })
                    }
                }(i));
            }
            async.series(asyncObjectTask, function (asyncErr, asyncData) {
                if (asyncErr) {
                    cb(asyncErr, null);
                }
                else {
                    cb(null, asyncData);
                }
            })
        }],
        "fetch_cs_object_fields": ["fetch_cs_objects", (dfa, cb) => {
            console.log("IN ASYNC AUTO PART :fetch_cs_object_fields");
            asyncObjectTask = [];
            for (var i = 0; i < Object.keys(dfa.fetch_cs_objects).length; i++) {
                asyncObjectTask.push(function (i) {
                    return function (cbx) {
                        var sql = "SELECT csof.id as id, csof.label as label, csof.name as name, csof.type as type from auto_tuning_settings as ats "
                        +"LEFT JOIN content_source_object_fields as csof on ats.content_source_object_field_id = csof.id "
                        +"where ats.content_source_object_id = ? AND auto_tuning_configuration_type = ? AND ats.is_selected = 1";
                        connection[tenantInfo.tenantId].execute.query(sql, [Number([Object.keys(dfa.fetch_cs_objects)[i]]), 1], function (err, data) {
                            if (err) {
                                console.log(err);
                                cbx(err, null);
                            }
                            else {
                                facetJson[searchClientId][Object.keys(dfa.fetch_cs_objects)[i]]['fields'] = [];
                                for (var j = 0; j < data.length; j++) {
                                    if(data[j].name && data[j].name.includes('.'))
                                        data[j].name = data[j].name.replace(/\./g,'_');
                                    var tmp = {
                                        "id": data[j].id,
                                        "name": data[j].name,
                                        "label": data[j].label,
                                        "type": data[j].type
                                    }
                                    facetJson[searchClientId][Object.keys(dfa.fetch_cs_objects)[i]]['fields'].push(tmp);
                                }
                                cbx(null, "");
                            }
                        })
                    }
                }(i));
            }
            async.series(asyncObjectTask, function (asyncErr, asyncData) {
                if (asyncErr) {
                    cb(asyncErr, null);
                }
                else {
                    cb(null, facetJson);
                }
            });
        }],
        "create_elastic_array": ["fetch_cs_object_fields", "fetch_cs_object_info", (dfa, cb) => {

            console.log('FACET_JSON:::::::::', JSON.stringify(facetJson));
            console.log("IN ASYNC AUTO PART :create_elastic_array");
            var elasticObj = [];
            for (var i = 0; i < Object.keys(facetJson[searchClientId]).length; i++) {
                var indexName = facetJson[searchClientId][Object.keys(facetJson[searchClientId])[i]]['info']['index_name'];
                var indexId = facetJson[searchClientId][Object.keys(facetJson[searchClientId])[i]]['info']['index_id'];
                var typeId = Object.keys(facetJson[searchClientId])[i];
                var typeName = facetJson[searchClientId][Object.keys(facetJson[searchClientId])[i]]['info']['object_name'];
                for (var j = 0; j < facetJson[searchClientId][Object.keys(facetJson[searchClientId])[i]]['fields'].length; j++) {
                    var field = facetJson[searchClientId][Object.keys(facetJson[searchClientId])[i]]['fields'][j];
                    elasticObj.push({
                        "index_name": indexName + "__" + typeName,
                        "index_id": indexId,
                        "type_name": typeName,
                        "type_id": typeId,
                        "field_id": field.id,
                        "field_value": field.name + ".keyword"
                    });
                }
            }
            console.log("IN ASYNC AUTO PART COMPLETE :create_elastic_array");
            cb(null, elasticObj);
        }],
        "fetch_elastic_values": ["create_elastic_array", (dfa, cb) => {
            console.log("IN ASYNC AUTO PART :fetch_elastic_values");
            var asyncElasticTask = [];
            for (var i = 0; i < dfa.create_elastic_array.length; i++) {
                asyncElasticTask.push(function (i) {
                    return function (cbx) {
                        dfa.create_elastic_array[i]['values'] = [];
                        dfa.create_elastic_array[i]['keyword_fields_value'] = dfa.create_elastic_array[i].field_value;
                        const requestBody = {
                            "size": 0,
                            "aggs": {
                                "values_array": {
                                    "terms": {
                                        "field": dfa.create_elastic_array[i]['keyword_fields_value'],
                                        "size": 10000
                                    }
                                }
                            }
                        };
                        console.log(JSON.stringify(requestBody));
                        osClient.search({
                            index: dfa.create_elastic_array[i].index_name,
                            body: requestBody
                        },
                        function (err, data) {
                            if (err) {
                                console.log(err);
                                cbx(null, null);
                            }
                            else {
                                if(data.body && data.body.aggregations && data.body.aggregations.values_array && data.body.aggregations.values_array.buckets && data.body.aggregations.values_array.buckets.length){
                                    data.body.aggregations.values_array.buckets.map(x=>{
                                        if(!facetMap[x.key]){
                                            facetMap[x.key] = [];
                                        }
                                        facetMap[x.key].push({
                                            "o_id" : dfa.create_elastic_array[i].type_id,
                                            "f_id" : dfa.create_elastic_array[i].field_id,
                                            "o_name" : dfa.create_elastic_array[i].type_name,
                                            "f_name" : dfa.create_elastic_array[i].field_value
                                        });
                                    });
                                }
                                cbx(null, dfa.create_elastic_array);
                            }
                        });
                    }
                }(i));
            }
            async.series(asyncElasticTask, function (asyncErr, asyncData) {
                console.log("IN ASYNC AUTO PART COMPLETE :fetch_elastic_values");
                if (asyncErr) {
                    console.log(asyncErr);
                    cb(asyncErr, null);
                }
                else {
                    cb(null, facetJson);
                }
            });
        }],
        "create_json":["fetch_elastic_values",(dfa, cb)=>{
            console.log("IN ASYNC AUTO PART :create_json");
            var dataItems = '';
            console.log("Object.keys(facetMap).length"+Object.keys(facetMap).length);
            if(Object.keys(facetMap).length>0){
                var begin =0;
                end = Object.keys(facetMap).length < 100 ? Object.keys(facetMap).length : 100;
                var flag = Object.keys(facetMap).length < 100 ? 1 :  0;
                console.log("begin----->>>"+begin+"END ----->>>"+end);
                console.log("Flag--->>"+flag);

                batchRepreat(begin, end, Object.keys(facetMap).length, searchClientUId, flag, tenantInfo, function(err, data){
                    cb(null, "");
                })
            }
            else{
                cb(null, "");
            }

        }]
    }, function (err, data) {
        if (err) {
            console.log(err);
        }
        else {
            // console.log(facetMap);
        }
        console.log("XXXXXXXXXX :: END  :: XXXXXXXXXX");
        cbx(null, "Done");
    })
}

function batchRepreat(begin, end, total, searchClientUId, flag, tenantInfo, cb){
    var dataItems = [];
    for(var i =begin; i< end; i++){
        var obj = Object.keys(facetMap)[i];
        for(var j=0; j<facetMap[obj].length; j++){

            let dataItem = {
                "id": obj+"_"+facetMap[obj][j].o_id+"_"+facetMap[obj][j].f_id+"_"+searchClientUId,
                "title": obj,
                "titleExact":obj,
                "oId":facetMap[obj][j].o_id,
                "oName":facetMap[obj][j].o_name,
                "fId":facetMap[obj][j].f_id,
                "fName":facetMap[obj][j].f_name,
                "sUid":searchClientUId,
                "score":0,
                "searchScore":0,
                "searchs":[]
            }


            dataItems.push(dataItem);
        }
    }
    let dataItemsString = JSON.stringify(dataItems);
    publishKafka(dataItemsString, function(err, data){
        if(flag==1){
            console.log("Exit from flag 1");
            cb(null, "");
        }
        else{
            setTimeout(() => {
                if(total-end < 100){
                    begin=end;
                    end = total;
                    console.log("begin------->>>"+begin+"end-------->>>"+end);
                    batchRepreat(begin, end, total, searchClientUId, 1, tenantInfo, function(err, data){
                        cb(null, "done");
                    });
                }
                else{
                    begin = end;
                    end = end+100;
                    console.log("begin------->>>"+begin+"end-------->>>"+end);
                    batchRepreat(begin, end, total, searchClientUId, 0, tenantInfo, function(err, data){
                        cb(null, "done");
                    });
                }
            }, 5000);
        }
    });
}


async function publishKafka(facetInterpreterArray, cb){
    console.log("IN ASYNC AUTO PART :Publish Kafka");
    try {
        const newfacetInterpreterArray = JSON.parse(facetInterpreterArray);
        let key = newfacetInterpreterArray && newfacetInterpreterArray[0] && newfacetInterpreterArray[0].sUid;
        if (!key) {
            console.log(
                "Warning: UID is not available in the data object. The message will be sent without a specific key in facetInterpreter"
            );
        }
        kafkaLib.publishMessage({
            topic: config.get("kafkaTopic.facetInterpreterTopic"),
            messages: [{
                value: facetInterpreterArray,
                key: key
            }]
        }).then(response => {
            cb(null, response);
        });

    } catch(error) {
        return(error);
    };
}




module.exports = {
    faceInterpreter
};


/**
 * {"mappings":{"facetvalues":{"properties":{"f_id":{"type":"long","index":"not_analyzed"},"f_name":{"type":"string","index":"not_analyzed"},"id":{"type":"string"},"o_id":{"type":"string","index":"not_analyzed"},"o_name":{"type":"string","index":"not_analyzed"},"s_uid":{"type":"string","index":"not_analyzed"},"score":{"type":"long"},"search_score":{"type":"long"},"title":{"type":"string"},"title_exact":{"type":"string","index":"not_analyzed"}}}}}
 */
