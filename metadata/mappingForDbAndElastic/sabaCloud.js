
module.exports = {

    "objects": [{
        "name": "idea",
        "label": "Idea",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": [
            {
                "name": "id",
                "label": "id",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "name",
                "label": "Title",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "author_name",
                "label": "Author Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "description",
                "label": "Description",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "domain_name",
                "label": "Domain Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "domain_type",
                "label": "Domain Type",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "language",
                "label": "Language",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "owner_name",
                "label": "Owner Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "stateDisplayName",
                "label": "state Display Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "videoId",
                "label": "Video Id",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "type",
                "label": "Type",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "createdBy_name",
                "label": "CreatedBy Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "timestamp",
                "label": "Timestamp",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "state",
                "label": "State",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "tagList",
                "label": "Tag List",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "recommendationList",
                "label": "Recommendation List",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "categoryList",
                "label": "Category List",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "implementationLead_name",
                "label": "Name of Implementation Lead",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "permission",
                "label": "Permissions",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "tags",
                "label": "Tags",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "post_time",
                "label": "Created Date",
                "type": "datetime",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1
            },
            {
                "name": "modified_time",
                "label": "Modified Date",
                "type": "datetime",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 0, //analyzer
                "isActive": 1
            },
            {
                "name": "view_href",
                "label": "View Href ",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            }
        ]

    },
    {
        "name": "page",
        "label": "Page",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": [
            {
                "name": "id",
                "label": "id",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "title",
                "label": "Title",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "author_name",
                "label": "Author Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "description",
                "label": "Description",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "containerType",
                "label": "Container Type",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "containerName",
                "label": "Container Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "language",
                "label": "Language",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "owner_name",
                "label": "Owner Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "split_Name",
                "label": "Split Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "status",
                "label": "Status",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "uploadInfo",
                "label": "Upload Information",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "transValidity",
                "label": "TransValidity",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "uriReference",
                "label": "Uri Reference",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "versionSupported",
                "label": "Version Supported",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "tagList",
                "label": "Tag List",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "comment",
                "label": "Comments",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "text",
                "label": "Description",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "permission",
                "label": "Permissions",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "post_time",
                "label": "Created Date",
                "type": "datetime",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1
            },
            {
                "name": "modified_time",
                "label": "Updated Date",
                "type": "datetime",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 0, //analyzer
                "isActive": 1
            },
            {
                "name": "view_href",
                "label": "View Href ",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            }
        ]

    },
    {
        "name": "file",
        "label": "File",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": [
            {
                "name": "id",
                "label": "id",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "name",
                "label": "Title",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "author_name",
                "label": "Author Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "description",
                "label": "Description",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "domain_name",
                "label": "Domain Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "domain_type",
                "label": "Domain Type",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "language",
                "label": "Language",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "owner_name",
                "label": "Owner Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "videoId",
                "label": "Video Id",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "type",
                "label": "Type",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "createdBy_name",
                "label": "CreatedBy Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "timestamp",
                "label": "Timestamp",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "status",
                "label": "Status",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "tagList",
                "label": "Tag List",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "recommendable",
                "label": "Recommendable",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "attachments",
                "label": "Attachment Data",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "permission",
                "label": "Permissions",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "tags",
                "label": "Tags",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "post_time",
                "label": "Created Date",
                "type": "datetime",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1
            },
            {
                "name": "modified_time",
                "label": "Modified Date",
                "type": "datetime",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 0, //analyzer
                "isActive": 1
            },
            {
                "name": "view_href",
                "label": "View Href ",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            }
        ]

    }]

}
