const request = require("request");
const config  = require("config");
const commonFunctions = require("./../../utils/commonFunctions");

const getProducts = function (resultData, req, callback) {
    var options = {
        method: "GET",
        rejectUnauthorized: false,
        url: config.get("crawler.crawlerUrl") + "/crawler/aha/getProducts?cs_id=" +
            resultData.authorization.content_source_id,
        headers: { 
            "Content-Type": "application/json",
            "su-crawler-secret" : config.get("crawler.sharedSecret"),
            "tenant-id": req.headers['tenant-id'] 
        },
    };

    request(options, function (error, response, body) {
        if (error) {
            commonFunctions.errorlogger.error(error);
            callback(error, null);
        } else {
            var body = JSON.parse(body);
            commonFunctions.errorlogger.info("aha products Data: ", body);
            if(!body.success)
                callback(body.error);
            else {
                const products = body.data || [];
                let contentSourceObjectArr = [];

                for (let i = 0; i < products.length; i++) {
                    contentSourceObjectArr.push({
                        content_source_id: resultData.authorization.content_source_id,
                        spaceName: products[i].name,
                        spaceUrl: '',
                        spaceId: products[i].id,
                        spaceKey: 'Product',
                        isSelected: resultData.projectIds.includes(String(products[i].id)) ? 1 : 0
                    });
                }
                callback(null, contentSourceObjectArr);
            }
        }
    });
};


exports.getProducts = getProducts;