// To use this migration place the file in admin directory and then hit 
// node migration-agent-helperScipt.js 

const fs = require("fs");
const path = require("path");
const async = require("async");
const express = require('express');
const request = require('request');
const configPath = path.join(__dirname, "../../config");
process.env.NODE_CONFIG_DIR = configPath;
const environment = require("../../routes/environment");
process.env.NODE_ENV = environment.configuration;
const config = require("config");
const mysql = require("mysql");
const connection = mysql.createConnection({
    host: config.get("databaseSettings.host"),
    user: config.get("databaseSettings.user"),
    password: config.get("databaseSettings.password"),
    database: config.get("databaseSettings.database"),
    port: config.get("databaseSettings.mysqlPORT"),
    multipleStatements: true
    
});
const alertUrl = config.get('gchatUrls.migration');

const alertGoogleChat = async ({ url , event, message }) => {
	console.log(`alerting google chat ${event}`);
	const options = {
	  method: 'POST',
	  url,
	  body: { text: message },
	  json: true,
	};
	return new Promise((resolve, reject) => {
	  request(options, (error, response, body) => {
		if (error) {
		  return reject(error);
		}
		if (response || body) {
		  return resolve(body);
		}
	  });
	});
  };

const truncateTable = (tableName, cb) => {
	const sql = `TRUNCATE TABLE ${tableName}`;
	connection.query(sql, (err, res) => {
		// console.log('truncating here ', err, res);
		if (err) cb(err);
		else cb(null, res);
	})
};

// Main envoke function that binds all other methods
const startProcess = async (cb) => {
	let path = __dirname + '/../../resources/search_clients_custom/agentHelperConfig.json.ignore';
	if (fs.existsSync(path)) {
		fs.readFile(path, 'utf8', (error, data) => {
			data = JSON.parse(data || '{}');
			var uids = Object.keys(data);
			var taskArray = [];
			taskArray.push(truncateTable.bind(null, 'agenthelper'));
			uids.forEach((item) => {
				taskArray.push(agentHelperInsert.bind(null, item, data[item]))
			})
			async.series(taskArray, async (err, docs) => {
				console.log(err,docs);
				await alertGoogleChat({
					url: alertUrl,
                    message: `*m23 agent helper migration from admin has been Successful - ${config.get('adminURL')}*`,
                    event: "Cron Job creation",
                });
				process.exit();
			})
		});
	} else {
		console.log(`File ${path} does not exist. Exiting script.`);
		await alertGoogleChat({
			url: alertUrl,
			message: `*m23 agent helper migration from admin has been Successful - ${config.get('adminURL')}*`,
			event: "Agent Helper migration script",
		});
		process.exit();
	}
}
  
const agentHelperInsert = (uid, obj, cb)=> {
    let querySql;
    console.log('Running for uid: ',uid);
    if(!obj.Source_object || !obj.Input_Fields ){
      console.log("No Source_object and Input_Fields Found");
      return  cb(null,{});
    } 
    querySql =`SELECT id, name ,agentHelperSlackCreds FROM search_clients where uid='${uid}'`;
    connection.query(querySql, (err, docs) => {
		if(docs && docs.length){
			console.log("Search Client Found !!");
			obj.Source_object.sc_Id = docs[0].id;
			querySql = `INSERT INTO agenthelper (configuration, 
				search_client_name,
				slack_creds,
				tenant_id, 
				type, 
				is_enabled,
				last_trained,
				training_status,
				uid)
				VALUES('${JSON.stringify(obj)}',
				'${docs[0].name}',
				'${docs[0].agentHelperSlackCreds}',
				'${config.tenantId}',
				'Salesforce service console',
				${obj.agentHelperEnabled ? 1 :0},
				'${obj.lastTrained ? new Date(obj.lastTrained).toISOString().slice(0, 19).replace('T', ' ') : null }',
				3,
				'${uid}')`;
			connection.query(querySql, (err, docs) => {
				if(!err){
					console.log("Inserted Data for ", uid);
					cb(null,{});
				}
				else {
					console.log('Error while inserting data for ', uid);
					cb(err);
				}
			})
		}
		else {
			// console.log(uid,'No Entry found in search_clients table for ',uid);
			cb(null, `No Entry found in search_clients table for ${uid}`);
		}
    })
}
// Initialization
startProcess();

// Error handling for unhandled rejections
process.on("unhandledRejection", async (err) => {
    console.error("########## Unhandled rejection", JSON.stringify(err));
	await alertGoogleChat({
		url: alertUrl,
		message: `*m23 agent helper migration from admin has been Failed*`,
		event: "Agent Helper migration script",
	});
    process.exit();
});

