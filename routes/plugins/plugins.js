var express = require('express');
var async = require('async');
var router = express.Router();
var commonFunctions = require('./../../utils/commonFunctions');

router.post("/getAddonDetails", function (req, res, next) {
  var addonPlaceId = req.body.addonPlaceId;
  if (addonPlaceId && !isNaN(addonPlaceId)) {
    try {
      var sql = "select * from addon_places where id=?";
      connection[req.headers['tenant-id']].execute.query(sql, [addonPlaceId], (err, rows) => {
        if (!err && rows.length) {
          res.send({ status: 200, data: rows[0] });
        } else {
          res.send({ status: 403, message: 'Invalid addon Place id Or No plugin added at this place' });
        }
      });
    } catch (e) {
      res.send({ status: 403, message: 'Something went wrong please try again.' });
    }
  } else {
    res.send({ status: 403, message: 'Invalid addon Place id' });
  }
});

router.get('/getAddons', function (req, res, next) {
  var sqlCS;
  if (req.query.isInstalled)
    sqlCS = `SELECT * FROM addons LEFT JOIN addons_status ON addons_status.addon_id = addons.id WHERE addons_status.is_installed=true LEFT JOIN user on addons_status.installed_by=user.id LEFT JOIN addon_places on addons_status.id=addon_places.addon_id LEFT JOIN places on addon_places.place_id=places.id WHERE addons.id NOT IN (2,3,4,5,6) ORDER by addons.name`;
  else
    sqlCS = `SELECT *  
    FROM addons
    LEFT JOIN addons_status
      ON addons_status.addon_id = addons.id 
    LEFT JOIN user 
      on addons_status.installed_by=user.id 
    LEFT JOIN addon_places 
      on addons_status.id=addon_places.addon_id 
    LEFT JOIN places 
      on addon_places.place_id=places.id 
    WHERE addons.id NOT IN (2,3,4,5,6) ORDER by addons.name`;

    connection[req.headers['tenant-id']].execute.query({ sql: sqlCS, nestTables: true }, [], (error, result) => {
    res.send({ flag: 200, addons: result })
  });
});

router.post('/installAddon', async function (req, res, next) {
  let addon = {};
  addon.id = req.body.id;
  addon.addon_id = req.body.addon_id;
  addon.is_installed = req.body.is_installed;
  addon.installed_on = (new Date()).toISOString();
  addon.installed_by = req.headers.session.userId;
  let request_to_install = 0;
  let depricated = 0;

  const addonDetails = await getAddonDetails(addon.addon_id, req.headers['tenant-id']);
  const addonStatusDetails = await getAddonStatusDetails(addon.addon_id, req.headers['tenant-id']);
  if(addonDetails && addonDetails.addons && addonDetails.addons[0]){
    request_to_install = addonDetails.addons[0].request_to_install;
    depricated = addonDetails.addons[0].depricated;
    let sql;
      if (addon.is_installed) {
        if (!depricated && (addon.addon_id != 1 || (addon.addon_id == 1 && request_to_install))) {
          if (addonStatusDetails && addonStatusDetails.addons && addonStatusDetails.addons.length <= 0) {
            sql = 'INSERT INTO addons_status SET ? ON DUPLICATE KEY UPDATE id=values(id),installed_by=values(installed_by),is_installed=values(is_installed),addon_id=values(addon_id)'
            connection[req.headers['tenant-id']].execute.query(sql, addon, (error, result) => {
            res.send({ flag: 200, addons: result })
            });
          } else {
            res.send({flag: 403, message: 'This addon is already present and cannot be installed again'});
          }
        } else {
          res.send({flag: 403, message: 'This addon is depricated or not available for installation.'});
        }
      }
      else {
        async.auto({
          statusDelete: cb => {
            let sql = 'DELETE FROM addons_status WHERE id = ?';
            connection[req.headers['tenant-id']].execute.query(sql, addon.id, (error, result) => {
              cb(null, [])
            })
          },
          placeDelete: cb => {
            let sql = 'DELETE FROM addon_places WHERE addon_places.addon_id = ?';
            connection[req.headers['tenant-id']].execute.query(sql, addon.addon_id, (error, result) => {
              cb(null, [])
            })
          }
        }, (error, result) => {
         res.send({flag: 200, addons: result})
        })
  
      }
  }
  else{
    res.send({flag: 403, message: 'Invalid addon id or no plugin added at this place'});
  }

});

router.get('/getAddonsStatus', function (req, res, next) {
  res.setHeader("Access-Control-Allow-Origin", "*");
  commonFunctions.getAddonsStatus(req, (error, result) => {
    if (result) {
      if (req.query.type && req.query.type == 'json') {
        res.send({ "response": result });
      } else {
        res.send(result);
      }
    } else {
      res.send({ status: 403, message: 'Invalid addon id or no plugin added at this place' });
    }
  })
});

const getAddonDetails = async (addon_id, tenantId) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * FROM addons WHERE id=?`;
    connection[tenantId].execute.query(sql, addon_id, (error, result) => {
      if (error) {
        reject(error);
      } else {
        resolve({addons: result});
      }
    })
  })
}

const getAddonStatusDetails = async (addon_id, tenantId) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * FROM addons_status WHERE addon_id=?`;
    connection[tenantId].execute.query(sql, addon_id, (error, result) => {
      if (error) {
        reject(error);
      } else {
        resolve({addons: result});
      }
    })
  })
}


module.exports = router;
