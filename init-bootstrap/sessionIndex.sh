if [ $# -eq 0 ]
  then
    echo "No arguments supplied"
    exit
fi
echo "Starting script to update mapping"
echo "Reindexing data to temporary index "$1"_temp"

curl -X POST "localhost:8035/_reindex" -H 'Content-Type: application/json' -d'
{
  "source": {
    "index": "'"$1"'"
  },
  "dest": {
    "index": "'"$1"'_temp"
  }
}'
curl -X POST localhost:8035/$1_temp/_refresh
echo "Reindexing to "$1"_temp completed."

echo "Deleting "$1" index."
curl -XDELETE localhost:8035/$1
echo "Deleted "$1

echo "Updating mapping for "$1
curl -XPUT localhost:8035/$1 -d '{"mappings":{"session":{"properties":{"_words_":{"type":"string","analyzer":"custom_lowercase_stemmed","search_analyzer":"custom_lowercase_synonym"},"case_created":{"type":"boolean"},"endTime":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"searchclientUid":{"type":"string","index":"not_analyzed"},"startTime":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"support_visit":{"type":"boolean"},"casesLog":{"type":"nested","properties":{"caseUid":{"type":"string","index":"not_analyzed"},"cookie":{"type":"string","index":"not_analyzed"},"event":{"type":"string","index":"not_analyzed"},"subject":{"type":"string","index":"not_analyzed"},"ts":{"type":"date","format":"strict_date_optional_time||epoch_millis"}}},"searches":{"type":"nested","properties":{"conversion":{"properties":{"es_id":{"type":"string","index":"not_analyzed"},"rank":{"type":"string"},"subject":{"type":"string","index":"not_analyzed"},"conversion_date":{"type":"date"},"url":{"type":"string","index":"not_analyzed"}}},"cookie":{"type":"string","index":"not_analyzed"},"filters":{"type":"nested","properties":{"name":{"type":"string","index":"not_analyzed"},"selectedValues":{"type":"string","index":"not_analyzed"}}},"geo":{"type":"geo_point"},"isClicked":{"type":"boolean"},"keywords":{"type":"string"},"page_no":{"type":"short"},"result_count":{"type":"short"},"search_type":{"type":"boolean"},"text_entered":{"type":"string","index":"not_analyzed","copy_to":["_words_"]},"uid":{"type":"string","index":"not_analyzed"},"window_url":{"type":"string","index":"not_analyzed"}}},"conversions":{"type":"nested","properties":{"conversion_date":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"cookie":{"type":"string","index":"not_analyzed"},"es_id":{"type":"string","index":"not_analyzed"},"internal":{"type":"boolean"},"rank":{"type":"string"},"search_id":{"type":"long"},"subject":{"type":"string","index":"not_analyzed"},"uid":{"type":"string","index":"not_analyzed"},"url":{"type":"string","index":"not_analyzed"},"window_url":{"type":"string","index":"not_analyzed"}}},"pageViews":{"type":"nested","properties":{"cookie":{"type":"string","index":"not_analyzed"},"email":{"type":"string"},"event":{"type":"string","index":"not_analyzed"},"geo":{"type":"geo_point"},"ip":{"type":"string"},"referer":{"type":"string","index":"not_analyzed"},"size":{"type":"long"},"taid":{"type":"string","index":"not_analyzed"},"targetUrl":{"type":"string","index":"not_analyzed"},"title":{"type":"string","index":"not_analyzed"},"ts":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"url":{"type":"string","index":"not_analyzed"}}},"log":{"type":"nested","properties":{"cookie":{"type":"string","index":"not_analyzed"},"email":{"type":"string"},"event":{"type":"string","index":"not_analyzed"},"geo":{"type":"geo_point"},"ip":{"type":"string"},"referer":{"type":"string","index":"not_analyzed"},"size":{"type":"long"},"taid":{"type":"string","index":"not_analyzed"},"targetUrl":{"type":"string","index":"not_analyzed"},"title":{"type":"string","index":"not_analyzed"},"ts":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"uid":{"type":"string","index":"not_analyzed"},"url":{"type":"string","index":"not_analyzed"}}}}}},"settings":{"index":{"analysis":{"filter":{"my_custom_stop":{"type":"stop","stopwords_path":"stopwords.txt"},"synonym":{"type":"synonym","synonyms_path":"synonyms.txt"},"my_stop":{"type":"stop","stopwords":"_english_"}},"analyzer":{"custom_lowercase_stemmed":{"filter":["lowercase","my_stop","my_custom_stop"],"tokenizer":"standard"},"custom_lowercase_synonym":{"filter":["lowercase","my_stop","my_custom_stop","synonym"],"tokenizer":"standard"}}}}}}'
echo "Mapping updated for "$1

echo "Reindexing back to original Index"
curl -X POST "localhost:8035/_reindex" -H 'Content-Type: application/json' -d'
{
  "source": {
    "index":  "'"$1"'_temp"
  },
  "dest": {
    "index": "'"$1"'"
  }
}'
curl -X POST localhost:8035/$1/_refresh