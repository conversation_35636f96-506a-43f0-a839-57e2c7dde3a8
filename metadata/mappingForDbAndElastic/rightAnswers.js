const metaData = {
  objects: [{
    name: 'article',
    label: 'Article',
    status: 1,
    boosting_factor: 1,
    base_url: '',
    fields: []
  }],
  fields: [
    {
      name: 'article_id',
      label: 'Id',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'content',
      label: 'Content',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'keywords',
      label: 'Keywords',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'summary',
      label: 'Summary',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'taxonomy',
      label: 'Taxonomy',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'html_content_nested',
      label: 'HTML Content',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'comments',
      label: 'Comments',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'template',
      label: 'Template',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'title',
      label: 'Title',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'type',
      label: 'Type',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'author',
      label: 'Author',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'steps',
      label: 'Steps',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'approval',
      label: 'Approval',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'browse_path_2',
      label: 'Browse Path',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'complete',
      label: 'Complete',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'icon_code',
      label: 'Icon Code',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'quality_score',
      label: 'Quality Score',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'reading_ease_description',
      label: 'Reading Ease Description',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'reading_ease_level',
      label: 'Reading Ease Level',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'short_solution',
      label: 'Short Solution',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'solution_group',
      label: 'Collections',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'view_count',
      label: 'View Count',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'status',
      label: 'Status',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'post_time',
      label: 'Created Date',
      type: 'datetime',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1,
      isMerged: 1
    },
    {
      name: 'last_modified_date',
      label: 'Last Modified Date',
      type: 'datetime',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'action',
      label: 'Action',
      type: 'string',
      isFilterable: 0,
      isSortable: 0,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'categoryCode',
      label: 'Category Code',
      type: 'string',
      isFilterable: 0,
      isSortable: 0,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'solvedCount',
      label: 'Solved Count',
      type: 'string',
      isFilterable: 0,
      isSortable: 0,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'view_href',
      label: 'URL',
      type: 'string',
      isFilterable: 0,
      isSortable: 0,
      isSearchable: 1,
      isActive: 1
    }
  ]
  };
  module.exports = {
    metaData
  };
