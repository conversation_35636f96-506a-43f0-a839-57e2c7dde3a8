const router = require('express').Router();
const elastic = require("elasticsearch");
var Json2csvParser = require('json2csv').Parser;
const async = require('async');
var commonFunctions = require('../../../utils/commonFunctions');
// var common = require('../common.js');
var request = require('request');

router.use((req, res, next) => {
    const session_email = req.headers.session &&  req.headers.session.email ? req.headers.session.email : '';
    var objToSend = {
        method: 'POST',
        rejectUnauthorized: false,
        url: config.get('analyticsService.url') + `/track/trackAdminAnalytics`,
        json: true,
        body: { user: session_email,
            tenantId: req.headers['tenant-id'],
            isTemporaryUser: req.headers.session && req.headers.session['isTemporaryUser']? req.headers.session['isTemporaryUser']: false,
            teamName: req.headers.session && req.headers.session['teamName'],
            option:{} }
    };
    if (objToSend.body['teamName']) {
        const capitalizedA = objToSend.body['teamName'].charAt(0).toUpperCase() + objToSend.body['teamName'].slice(1);
        objToSend.body['teamName'] = capitalizedA +" Team";
    }
    if (req.path === "/admin/contentSources/addContentSource" && req.method === 'POST') {
        if (!req.body.contentSource.id) {
            objToSend.body.option = {
                    info: "Content Source > Added new " + req.body.contentSource.label +" content source",
                    object: "Content Source",
                    event: 'AdminLog'
            }
            console.log("OUTPUT::: ",objToSend);
            request(objToSend,  (error, response) => {
                console.log(error, response);
            })
        }
        else if (req.body.tabName && req.body.tabName == "Rules"){
            objToSend.body.option = {
                    info: "Content Source > Clicked Save on Edit " + req.body.contentSource.label + " configuration",
                    object: "Content Source",
                    event: 'AdminLog'
            }
            console.log("OUTPUT::: ",objToSend);
            setTimeout(function() {
                request(objToSend,  (error, response) => {
                    console.log(error, response);
                })
            },1000);
        }
    }
    // else if (req.path === "admin/contentSources/cloneContentSource") {
    //     AdminAnalytics.insert({
    //         user: session_email,
    //         info: `Content source ${req.body.contentSource.label} cloned`,
    //         object: "contentSource"
    //     });
    // }
    
    // else if (req.path === "/admin/contentSources/crawlData") {
    //     AdminAnalytics.insert({
    //         user: session_email,
    //         info: `Crawling requested for id ${req.query.content_source_id}`,
    //         object: "contentSource"
    //     });
    // }
    else if (req.path === "/admin/contentSources/deleteDocument") {
        objToSend.body.option = {
            info: "Content Source > Deleted Document from " + req.body.sourceLabel + " index",
            object: "Content Source",
            event: 'AdminLog'
        }
        console.log("OUTPUT::: ",objToSend);
        request(objToSend,  (error, response) => {
            console.log(error, response);
        })
    }
    else if (req.path === "/admin/searchClient/addPlatform") {
        objToSend.body.option = {
            info: "Search Client > Added new " + req.body.platform.name + " search client",
            object: "Search Client",
            event: 'AdminLog'
        }
        console.log("OUTPUT::: ",objToSend);
        request(objToSend,  (error, response) => {
            console.log(error, response);
        })
    }
    // else if (req.path === "/admin/searchClient/editClient") {
    //     AdminAnalytics.insert({
    //         user: session_email,
    //         info: `searchClient updated ${req.body.platform.name}`,
    //         object: "SearchClient"
    //     });
    // }
    // else if (req.path === "/admin/searchClient/editClient") {
    //     AdminAnalytics.insert({
    //         user: session_email,
    //         info: `searchClient updated ${req.body.platform.name}`,
    //         object: "SearchClient"
    //     });
    // }
    // else if (req.path === "searchClient/updateSearchClient") {
    //     AdminAnalytics.insert({
    //         user: session_email,
    //         info: `searchClient updated ${req.body.client.name}`,
    //         object: "SearchClient"
    //     });
    // }
    else if (req.path === "admin/searchClient/saveFiltersPriority") {
        AdminAnalytics.insert({
            user: session_email,
            info: `Filters priority Updated`,
            object: "SearchClient"
        });
    }
    // else if (req.path === "/admin/searchClient/cloneSearchClient") {
    //     AdminAnalytics.insert({
    //         user: session_email,
    //         info: `Search client ${req.body.name} cloned`,
    //         object: "SearchClient"
    //     });
    // }
    // else if (req.path === "/admin/searchClient/deletePlatform") {
    //     AdminAnalytics.insert({
    //         user: session_email,
    //         info: `deleted search client id ${req.body.platformId}`,
    //         object: "SearchClient"
    //     });
    // }
    else if (req.path === "/tuning/searchtuning/saveTuningData") {
        AdminAnalytics.insert({
            user: session_email,
            info: `Search tuning updated for keyword ${req.body.searchString}`,
            object: "boosting"
        });
    } else if (req.path === "/tuning/searchtuning/deleteTuningData") {
        AdminAnalytics.insert({
            user: session_email,
            info: `deleted search tuning for keyword ${req.body.id}`,
            object: "boosting"
        });
    } else if (req.path === "tuning/contentTuning/changeTypeBoosting") {
        AdminAnalytics.insert({
            user: session_email,
            info: `Updated type boosting`,
            object: "boosting"
        });
    } else if (req.path === "/synonyms/write") {
        AdminAnalytics.insert({
            user: session_email,
            info: `Synonyms updated`,
            object: "synonyms"
        });
    } else if (req.path === "/stopwords/write") {
        AdminAnalytics.insert({
            user: session_email,
            info: `Stopwords updated`,
            object: "Stopwords"
        });
    } else if (req.path === "/admin/assetLibrary/uploadClientIcon") {
        AdminAnalytics.insert({
            user: session_email,
            info: `uploded asset file`,
            object: "asset"
        });
    } else if (req.path === "/assetLibrary/deleteIcon") {
        AdminAnalytics.insert({
            user: session_email,
            info: `deleted asset file`,
            object: "asset"
        });
    } else if (req.path === "/admin/userManagement/editUser") {
        AdminAnalytics.insert({
            user: session_email,
            info: `User updated ${req.body.name}`,
            object: "User"
        });
    } else if (req.path === "/oauthClients/saveOauthClients") {
        AdminAnalytics.insert({
            user: session_email,
            info: `New app created ${req.body.name}`,
            object: "App"
        });
    } else if (req.path === "/oauthClients/deleteOauthClients") {
        AdminAnalytics.insert({
            user: session_email,
            info: `App deleted id ${req.body.id}`,
            object: "App"
        });
    } else if (req.path === "/admin/notifications/saveNotificationPreferences") {
        AdminAnalytics.insert({
            user: session_email,
            info: `Notification added for ${req.body.subscriberEmails}`,
            object: "subscription"
        });
    } else if (req.path === "/admin/notifications/saveNotificationPreferences") {
        AdminAnalytics.insert({
            user: session_email,
            info: `Notification added for ${req.body.notificationPreferencesData.subscriberEmails}`,
            object: "subscription"
        });
    } else if (req.path === "/notifications/deleteNotificationPreferences") {
        AdminAnalytics.insert({
            user: session_email,
            info: `Notification deleted for ${req.body.notificationPreferencesData.subscriberEmails}`,
            object: "subscription"
        });
    }
    next();
});

class AdminAnalytics {
    static insert(activity, cb) {
        let client = new elastic.Client({
            host: 'http://' + config.get('elasticIndex.host') + ':' + config.get('elasticIndex.port')
        });
        client.index({
            index: config.get('elasticIndex.analytics'),
            type: "adminLog",
            body: {
                ts: Date.now(),
                user: activity.user,
                info: activity.info,
                object: activity.object
            }
        }, AdminAnalytics.handler(cb));
    }
    static fetch(option, cb) {
        commonFunctions.getSplitIndexes(option.startDate, option.endDate, (err, splitAnalyticsIndex)=>{

            let client = new elastic.Client({
                host: 'http://' + config.get('elasticIndex.host') + ':' + config.get('elasticIndex.port')
            });
            let dsl = {
                from: option.offset || 0,
                size: option.count || 25,
                "sort": [{ "ts": { "order": "desc" } }]
            };
            if (option.user || option.object || option.startDate || option.endDate || option.activity) {
                dsl.query = {
                    bool: {
                        must: [],
                        //Disable Ghost analytics
                        must_not: { term: { user: "<EMAIL>" } }
                    }
                };
                if (option.user)
                    dsl.query.bool.must.push({
                        "term": {
                            "user": option.user
                        }
                    });
                if (option.object)
                    dsl.query.bool.must.push({
                        "term": {
                            "object": option.object
                        }
                    });
                if (option.startDate || option.endDate) {
                    let range = {
                        range: {
                            ts: {
                                format: "yyyy-MM-dd"
                            }
                        }
                    };
                    if (option.startDate) {
                        range.range.ts.gte = option.startDate + '||/d';
                    }
                    if (option.endDate) {
                        range.range.ts.lte = option.endDate + '||/d';
                    }

                    dsl.query.bool.must.push(range);
                }
                if (option.activity && option.activity != "") {
                    let activity = {
                        "multi_match": {
                            "query": option.activity,
                            "fields": ["info", "user"]
                        }
                    };
                    dsl.query.bool.must.push(activity);
                }
                if (option.userEmail && option.userEmail != "") {
                    let userEmail = {
                        "match": {
                            "info": option.userEmail
                        }
                    };
                    dsl.query.bool.must.push(userEmail);
                }
            }
            client.search({
                index: splitAnalyticsIndex,
                type: "adminLog",
                body: dsl
            }, (error, data) => {
                AdminAnalytics.handler(cb)(error, data);
            });
        });
    }
    static search(option, cb) {
        let client = new elastic.Client({
            host: 'http://' + config.get('elasticIndex.host') + ':' + config.get('elasticIndex.port')
        });
        let dsl = {
            from: option.offset || 0,
            size: option.count || 10
                // "sort": [{"ts": {"order": "desc"}}]
        };
        dsl.query = {
            term: {
                info: option.searchinfo
            }
        };
        if (option.user || option.object || option.startDate || option.endDate) {
            dsl.query.bool = {
                must: []
            };
            if (option.user)
                dsl.query.bool.must.push({
                    "term": {
                        "user": option.user
                    }
                });
            if (option.object)
                dsl.query.bool.must.push({
                    "term": {
                        "object": option.object
                    }
                });
            if (option.startDate || option.endDate) {
                let range = {
                    range: {
                        ts: {}
                    }
                };
                if (option.startDate)
                    range.range.ts.gte = option.startDate;
                if (option.endDate)
                    range.range.ts.lte = option.endDate;
                dsl.query.bool.must.push(range);
            }
        }
        client.search({
            index: config.get('elasticIndex.analytics'),
            type: "adminLog",
            body: dsl
        }, (error, data) => {
            AdminAnalytics.handler(cb)(error, data);
        });
    }
    static handler(cb) {
        return (error, result) => {
            if (error) {
                console.error(error);
            }
            if (cb)
                cb(error, result);
        };
    }
}

router.post('/getAdminAnalytics', (req, res, next) => {
    let option = {};
    option = req.body.option;
    // option.startDate = req.query.startDate;
    // option.endDate = req.query.endDate;
    AdminAnalytics.fetch(option, function(error, result) {
        if (error) {
            res.sendStatus(403);
        } else {
            res.send(result);
        }
    })
})

router.get('/downloadAdminAnalytics', (req, res, next) => {
    let option = {}
    option.endDate = req.query.endDate;
    option.startDate = req.query.startDate
    option.count = 10000;
    option.activity = req.body.activity;
    let fields = ['user', 'ts', 'info', 'object'];

    // option.startDate = req.query.startDate;
    // option.endDate = req.query.endDate;
    AdminAnalytics.fetch(option, function(error, result) {
        if (error) {
            res.sendStatus(403);
        } else {
            let data = result
            data.hits.hits.map(x => {
                x._source.ts = new Date(x._source.ts).toISOString().replace('Z', ' ').replace('T', ' ')
            });
            data = data.hits.hits.map(m => m._source)

            const json2csvParser = new Json2csvParser({ fields });
            const csvData = json2csvParser.parse(data);

            res.setHeader('Content-disposition', 'attachment; filename=analytics.csv');
            res.set('Content-Type', 'text/csv');
            res.status(200).send(csvData);
        }
    });
})

router.get('/getAdminReport', (req, res, next) => {
    let sql = '';
    if (req.query.uid) {
        sql = `SELECT *,ar.id ar_id FROM analytics_reports ar left join (SELECT *,arp.id report_preference_id FROM admin_reports_preferences arp WHERE arp.analytics_report_id IN
             (SELECT scar.analytics_report_id FROM search_client_to_analytics_report scar JOIN search_clients sc ON scar.search_client_id=sc.id 
                where sc.uid=? AND scar.is_enabled=1 ) OR arp.analytics_report_id In(18,19,20,21,49,50,51,52))
                 arp_d on ar.id=arp_d.analytics_report_id WHERE ar.version=2 AND ar.id NOT IN (5,9,14,15,22,27,29,28,35,47,32) and 
                 ar.id NOT IN (SELECT scar.analytics_report_id FROM search_client_to_analytics_report scar JOIN 
                    search_clients sc ON scar.search_client_id=sc.id where sc.uid=? 
                    AND scar.is_enabled=0)  ORDER BY arp_d.report_order ASC`
    } else {
        sql = `SELECT *,analytics_reports.id ar_id FROM analytics_reports LEFT JOIN (SELECT *,admin_reports_preferences.id report_preference_id FROM admin_reports_preferences) AS arp ON analytics_reports.id = arp.analytics_report_id WHERE arp.search_client_id IS NULL GROUP BY ar_id ORDER BY arp.report_order ASC`
    }
    let q = connection[req.headers['tenant-id']].execute.query(sql, [req.query.uid, req.query.uid], function(error, result) {
        if (error)
            commonFunctions.errorlogger.error(error)
        else {
            res.send(result);
        }
    })
})

router.post('/addAdminReportSettings', (req, res, next) => {
    addAdminReportSetting(req.body.parameters, req.body.uid, req.body.userId, req, (error, result) => {
        if (error)
            commonFunctions.errorlogger.error(error)
        else
            res.send({ flag: 200, message: 'Success' });
    });
});

// router.post('/addUpdateReadCsmMessages', (req, res, next) => {
//     const sqlCS = 'INSERT INTO csm(id, name, email, subject, message, messagesReadByUsers) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE id = VALUES(id), name = VALUES(name), email = VALUES(email), subject = VALUES(subject), message = VALUES(message), messagesReadByUsers = VALUES(messagesReadByUsers)';
//         var q = connection[req.headers['tenant-id']].execute.query(sqlCS, [req.body.parameters.id, req.body.parameters.user, req.body.parameters.email, req.body.parameters.subject, req.body.parameters.message, req.body.parameters.messagesReadByUsers], function(errAsync, rows) {
//             if (errAsync) {
//                 console.log("Error inside csm");
//                 console.log(errAsync);
//                 res.send({  message: 'error' });
//             } else {
//                 res.send({ flag: 200, message: 'Success' });
//             }
//         })
// });

router.post('/getCSM', (req, res, next) => {
    const sqlCS = `SELECT * FROM csm ORDER BY id DESC`;
        var q = connection[req.headers['tenant-id']].execute.query(sqlCS, function(errAsync, rows) {
            if (errAsync) {
                console.log("Error inside csm");
                console.log(errAsync);
                res.send({  message: 'error' });
            } else {
                res.send({ flag: 200, message: 'Success', result: rows });
            }
        })
});

router.post('/deleteCSMMessage', (req, res, next) => {
    const sqlCS = `DELETE FROM csm WHERE id = ?`;
        var q = connection[req.headers['tenant-id']].execute.query(sqlCS, [req.body.parameters.id], function(errAsync, rows) {
            if (errAsync) {
                console.log("Error inside csm");
                console.log(errAsync);
                res.send({  message: 'error' });
            } else {
                res.send({ flag: 200, message: 'Success', result: rows });
            }
        })
});

// router.post('/getRecommendations', (req, res, next) => {
//     const sqlCS = `SELECT * FROM recommendations ORDER BY id DESC`;
//         var q = connection[req.headers['tenant-id']].execute.query(sqlCS, function(errAsync, rows) {
//             if (errAsync) {
//                 console.log("Error inside recommendations");
//                 console.log(errAsync);
//                 res.send({  message: 'error' });
//             } else {
//                 res.send({ flag: 200, message: 'Success', result: rows });
//             }
//         })
// });

// router.post('/addUpdateRecommendations', (req, res, next) => {
//     const sqlCS = 'INSERT INTO recommendations(id, name, email, title, description ) VALUES (?,?,?,?,?) ON DUPLICATE KEY UPDATE id = VALUES(id), name = VALUES(name), email = VALUES(email), title = VALUES(title), description = VALUES(description)';
//         var q = connection[req.headers['tenant-id']].execute.query(sqlCS, [req.body.parameters.id, req.body.parameters.user, req.body.parameters.email, req.body.parameters.title, req.body.parameters.description ], function(errAsync, rows) {
//             if (errAsync) {
//                 console.log("Error inside recommendations");
//                 console.log(errAsync);
//                 res.send({  message: 'error' });
//             } else {
//                 res.send({ flag: 200, message: 'Success' });
//             }
//         })
// });

router.post('/deleteRecommendations', (req, res, next) => {
    const sqlCS = `DELETE FROM recommendations WHERE id = ?`;
        var q = connection[req.headers['tenant-id']].execute.query(sqlCS, [req.body.parameters.id], function(errAsync, rows) {
            if (errAsync) {
                console.log("Error inside recommendations");
                console.log(errAsync);
                res.send({  message: 'error' });
            } else {
                res.send({ flag: 200, message: 'Success', result: rows });
            }
        })
});

router.post('/updateAdminReportSettings', (req, res, next) => {
    let param = req.body.parameters;
    insertUpdateAdminReportSetting(req, param, (error, result) => {
        if (error)
            commonFunctions.errorlogger.error(error)
        else
            res.send({ flag: 200, message: 'Success' });
    });
});

router.post('/deleteAdminReportSettings', (req, res, next) => {
    let q = connection[req.headers['tenant-id']].execute.query(`DELETE FROM admin_reports_preferences WHERE admin_reports_preferences.analytics_report_id = ?`, [req.body.parameters], (error, result) => {
        if (error)
            res.send(error)
        else
            res.send({ flag: 200, message: "Success" })
    })
});

router.post('/trackAdminAnalytics', (req, res, next) => {
    AdminAnalytics.insert({
        user: req.headers.session.email,
        info: req.body.option.info,
        object: req.body.option.object
    }, (error, data) => {
        if (error) {
            commonFunctions.errorlogger.error(error);
        } else {
            commonFunctions.errorlogger.info(data);
            res.send({ "Insert": "done" });
        }
    });
});

const addAdminReportSetting = function(adminReportId, uid, userId, req, callback) {
    async.auto({
        fetchParams: cb => {
            const sqlCS = `SELECT report_order+1 as reportOrder from admin_reports_preferences ORDER BY admin_reports_preferences.id DESC Limit 1`;
            var q = connection[req.headers['tenant-id']].execute.query(sqlCS, function(errAsync, rows) {
                if (rows) {
                    cb(null, rows[0].reportOrder);
                }
            })
        },
        getClientId: cb => {
            const sqlCS = `SELECT id FROM search_clients WHERE uid =?`;
            var q = connection[req.headers['tenant-id']].execute.query(sqlCS, uid, function(errAsync, rows) {
                if (rows && rows.length) {
                    cb(null, rows[0].id);
                } else {
                    cb(null, null)
                }
            })
        },
        insertParams: ["fetchParams", "getClientId", (results, cb) => {
            const sqlCS = `INSERT INTO admin_reports_preferences(report_order, analytics_report_id, user_id, search_client_id) VALUES (?,?,?,?)`;
            var q = connection[req.headers['tenant-id']].execute.query(sqlCS, [results.fetchParams, adminReportId, req.session.userId, results.getClientId], function(errAsync, rows) {
                if (errAsync) {
                    commonFunctions.errorlogger.error("Error inside adminReportArr",errAsync);
                    cb(errAsync, [])
                } else {
                    cb(null, [])
                }
            })
        }]
    }, (error, result) => {
        if (error)
            commonFunctions.errorlogger.error("error", error);
        else
            callback(null, result);
    });
}

const insertUpdateAdminReportSetting = function(req, adminReportArr, callback) {
    var colums = []
    var data = []
    for (var fi = 0; fi < adminReportArr.length; fi++) {
        colums = []
        const parameters = []
        for (var key in adminReportArr[fi]) {
            if (adminReportArr[fi].hasOwnProperty(key)) {
                if ((key === 'report_preference_id') || (key === 'report_order') || (key === 'analytics_report_id') || (key === 'user_id')) {
                    if (key === 'report_preference_id')
                        colums.push('id')
                    else
                        colums.push(key)
                    parameters.push(adminReportArr[fi][key]);
                }
            }
        }
        data.push(parameters)
    }

    const sqlCS = "INSERT INTO `admin_reports_preferences`(" + colums +
        ") VALUES " + data.map(x => { return "(" + x.map(y => { return "?" }) + ")" }).join(",") +
        " ON DUPLICATE KEY UPDATE " + colums.map(x => {
            return x + "=values(" + x + ")"
        }).join(',');

    if (colums.length)
        var q = connection[req.headers['tenant-id']].execute.query(sqlCS, data.reduce((sum, x) => { return sum.concat(x); }, []), function(errAsync, rows) {
            if (errAsync) {
                commonFunctions.errorlogger.error("Error inside adminReportArr",errAsync);
                callback(errAsync, [])
            } else {
                callback(null, [])
            }
        })
    else {
        callback(null, [])
    }
}



// INSERT INTO `admin_reports_preferences` ( `report_order`, `analytics_report_id`, `user_id`) select `report_order`+1, 4,1 from admin_reports_preferences order by id desc Limit 1 ;



module.exports = {
    AdminAnalytics: AdminAnalytics,
    router: router
};