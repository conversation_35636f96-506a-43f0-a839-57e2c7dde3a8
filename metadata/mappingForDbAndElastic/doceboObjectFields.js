
module.exports = {
    "objects": [{
        "name": "courses",
        "label": "Courses",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [
        {
            "name": "course_id",
            "label": "Course Id",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "name",
            "label": "Title",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "code",
            "label": "Code",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "slug_name",
            "label": "Slug Name",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "description",
            "label": "Course Description",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "credits",
            "label": "Credits",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "lang_code",
            "label": "Language Code",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "lang_label",
            "label": "Language Label",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "type",
            "label": "Course Type",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "is_published",
            "label": "Published",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "rating_option", //disabled/always
            "label": "Rating Option",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "current_rating",
            "label": "Current Rating",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "course_date_start",
            "label": "Course Start Date",
            "type": "datetime",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 0, //analyzer
            "isActive": 1
        },
        {
            "name": "course_date_end",
            "label": "Course End Date",
            "type": "datetime",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 0, //analyzer
            "isActive": 1
        },
        {
            "name": "duration",
            "label": "Duration",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "price",
            "label": "Price",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "on_sale", //true/false
            "label": "Selling",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "available_seats", //
            "label": "Available Seats",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "categoryid",
            "label": "Category Id",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "categoryname",
            "label": "Category Name",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "enrollment_policy", //1/2/3
            "label": "Enrollment Policy",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "max_attempts",
            "label": "Max Attempts",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "view_href",
            "label": "View Href ",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "catalog_ids",
            "label": "Catalog Id",
            "type": "string",
            "isFilterable": 1, // indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, // analyzer
            "isActive": 1
          },
          {
            "name": "catalog_names",
            "label": "Catalog Name",
            "type": "string",
            "isFilterable": 1, // indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, // analyzer
            "isActive": 1
          },
          {
            "name": "catalog_descriptions",
            "label": "Catalog Description",
            "type": "string",
            "isFilterable": 1, // indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, // analyzer
            "isActive": 1
          },
          {
            "name": "deeplink",
           "label": "Deep Link ",
           "type": "string",
           "isFilterable": 1, //indexing method analyzed OR not analyzed
           "isSortable": 1,
           "isSearchable": 1, //analyzer
           "isActive": 1
         },
         {
           "name": "category_path",
           "label": "Category Path",
           "type": "string",
           "isFilterable": 1, //indexing method analyzed OR not analyzed
           "isSortable": 1,
           "isSearchable": 1, //analyzer
           "isActive": 1
       }
    ]

}
