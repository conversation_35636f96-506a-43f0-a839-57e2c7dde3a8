const kafkaLib = require('../../../utils/kafka/kafka-lib');
const commonFunctions = require("../../../utils/commonFunctions");
const config = require("config");

const publishKafka = async (topic, message) => {
  try {
    await kafkaLib
      .publishMessage({
        topic: topic,
        messages: [
          {
            value: JSON.stringify(message),
          },
        ],
      });
    commonFunctions.errorlogger.info(`Kafka published for topic : ${topic}`);
  } catch (error) {
    commonFunctions.errorlogger.error('Error in method publishKafka');
    return error;
  }
};

const publishManageIndexesKafka = async (message) => {
  return await publishKafka(config.get("kafkaTopic.manageIndexes"), message);
};

const publishSynonymsDictionaryKafka = async (message) => {
  return await publishKafka(config.get("kafkaTopic.synonymsDictionary"), message);
};

const publishSynonymsDictionaryMPKafka = async (message) => {
  return await publishKafka(config.get("kafkaTopic.synonymDictionaryTopicMultiPhrase"), message);
};

module.exports = {
  publishManageIndexesKafka,
  publishSynonymsDictionaryKafka,
  publishSynonymsDictionaryMPKafka,
}