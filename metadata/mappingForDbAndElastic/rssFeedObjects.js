module.exports={
  "objects":[
    {
      "name":"items",
      "label":"items",
      "status":1,
      "boosting_factor":1,
      "base_url":"",
      "fields":[
          {
            "name":"item_title",
            "label":"Item Title",
            "type":"string",
            "isFilterable":1, 
            "isSortable":1,
            "isSearchable":1, 
            "isActive":1
          },
          {
            "name":"item_description",
            "label":"Item Description",
            "type":"string",
            "isFilterable":1,
            "isSortable":1,
            "isSearchable":1,
            "isActive":1
          },
          {
            "name":"item_category",
            "label":"Item Category",
            "type":"string",
            "isFilterable":1,
            "isSortable":1,
            "isSearchable":1,
            "isActive":1
          },
          {
            "name": "item_comments",
            "label":"Item Comments",
            "type": "string",
            "isFilterable": 1, 
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
          },
          {
            "name": "item_link",
            "label": "Item Link",
            "type": "string",
            "isFilterable": 1, 
            "isSortable": 1,
            "isSearchable": 1, 
            "isActive": 1,
            "isMasked": 0
          },
          {
            "name":"item_pubDate",
            "label":"Item Published Date",
            "type":"datetime",
            "isFilterable":1,
            "isSortable":1,
            "isSearchable":0,
            "isActive":1,
            "isMasked": 0
          },
          {
            "name":"item_guid",
            "label":"Item GUID",
            "type":"string",
            "isFilterable":1,
            "isSortable":1,
            "isSearchable":1,
            "isActive":1
          },{
            "name":"channel_title",
            "label":"Channel Title",
            "type":"string",
            "isFilterable":1, 
            "isSortable":1,
            "isSearchable":1, 
            "isActive":1
          },
          {
            "name":"channel_description",
            "label":"Channel Description",
            "type":"string",
            "isFilterable":1,
            "isSortable":1,
            "isSearchable":1,
            "isActive":1
          },
          {
            "name":"channel_language",
            "label":"Channel Language",
            "type":"string",
            "isFilterable":1,
            "isSortable":1,
            "isSearchable":1,
            "isActive":1
          },
          {
            "name": "channel_lastBuildDate",
            "label": "Channel Last Build Date",
            "type": "datetime",
            "isFilterable": 1, 
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1,
            "isMasked": 0
          },
          {
            "name": "channel_link",
            "label": "Channel Link",
            "type": "string",
            "isFilterable": 1, 
            "isSortable": 1,
            "isSearchable": 1, 
            "isActive": 1,
            "isMasked": 0
          }
      ],
    },
  ],
    "fields":[
    ]
}
