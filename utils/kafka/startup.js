const config = require('config');
const kafkaLib = require('./kafka-lib');

// on startup
const kafkaStartup = () => {
  kafkaLib.initializekafka({
    clientId: 'admin',
    brokers: [config.get('kafkaTopic.host')],
    retryConfig: {
      maxRetryTime: config.get('kafkaTopic.maxRetryTime'), // default 30000
      initialRetryTime: config.get('kafkaTopic.initialRetryTime'), // default 300
      retries: config.get('kafkaTopic.retries') // default is 5
    }
  });
};

module.exports = {
    kafkaStartup
}