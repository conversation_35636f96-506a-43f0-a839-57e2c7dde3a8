import java.io.FileInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.openqa.selenium.By;
import org.openqa.selenium.Cookie;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.firefox.FirefoxBinary;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.firefox.FirefoxOptions;
import org.openqa.selenium.firefox.FirefoxProfile;
import org.openqa.selenium.htmlunit.HtmlUnitDriver;
import org.openqa.selenium.phantomjs.PhantomJSDriver;
import org.openqa.selenium.phantomjs.PhantomJSDriverService;
import org.openqa.selenium.remote.DesiredCapabilities;

import com.gargoylesoftware.htmlunit.BrowserVersion;

public class GeneralisedSessionCapturingCode {
	static String phantomDriverPath = "";
	static String browser = "";
	static String geckoDriverPath = "";
	//static String urlToLoad = "";
	static {
		Properties prop = new Properties();
		InputStream input = null;
		try {

			input = new FileInputStream("config.properties");

			// load a properties file
			prop.load(input);
			if (prop.containsKey("phantomDriverPath"))
				phantomDriverPath = prop.getProperty("phantomDriverPath");

			if (prop.containsKey("browser"))
				browser = prop.getProperty("browser");

			if (prop.containsKey("geckoDriverPath"))
				geckoDriverPath = prop.getProperty("geckoDriverPath");

		} catch (IOException io) {
			io.printStackTrace();
		} finally {
			if (input != null) {
				try {
					input.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}

		}
	}

	public static void main(String argv[]) {
		String loginUrl = "";
		String usernameInputSelector = "";
		String passwordInputSelector = "";
		String username = "";
		String password = "";
		String loginButtonSelector = "";
		String outputFile = "";
		int loginMethod = 1;
		// standard login method  - 1
		// stepped login method   - 2
		String nextButtonSelector = "";

		// fetching command line parameters
		if (argv.length >= 7) {
			loginUrl = argv[0];
			usernameInputSelector = argv[1];
			try {
				username = TextEncryptor.decrypt(argv[2]);
				password = TextEncryptor.decrypt(argv[4]);
			} catch (Exception e) {
				System.out.println("unable to decrypt username and password." + e.getMessage());
				e.printStackTrace();
				return;
			}
			passwordInputSelector = argv[3];
			loginButtonSelector = argv[5];
			outputFile = argv[6];
			try{
				File f = new File(outputFile);
				Files.deleteIfExists(f.toPath());
			}catch(Exception e){
				System.out.println("Error while removing previous cookie file");
			}

			if(argv.length == 9){
				if(argv[7].equals("stepped")){
					loginMethod = 2;
				}
				nextButtonSelector = argv[8];
			}
		} else {
			System.out.println("Insufficient arguments please pass following arguments in sequence:");
			System.out.println(
					"'login url', 'username input selector', 'username', 'password input selector', 'password', 'login button selector', 'outputFile'");
			return;
		}
		// capabilities configuration for phantom
		DesiredCapabilities capabilities = DesiredCapabilities.phantomjs();
		capabilities.setBrowserName("phantomjs");
		capabilities.setJavascriptEnabled(true);
		capabilities.setCapability(PhantomJSDriverService.PHANTOMJS_EXECUTABLE_PATH_PROPERTY, phantomDriverPath);

		//initialising web driver
		WebDriver driver = null;
		switch (browser) {
		case "firefox":
			FirefoxBinary firefoxBinary = new FirefoxBinary();
			firefoxBinary.addCommandLineOptions("--headless");
			FirefoxOptions options = new FirefoxOptions();
			System.setProperty("webdriver.gecko.driver", geckoDriverPath);
			options.setBinary(firefoxBinary);

			FirefoxProfile profile = new FirefoxProfile();

			//Set Location to store files after downloading.
			profile.setPreference("browser.download.dir", "/home/<USER>/Desktop/sitesnapshots/");
			profile.setPreference("browser.download.folderList", 2);

			//Set Preference to not show file download confirmation dialogue using MIME types Of different file extension types.
			profile.setPreference("browser.helperApps.neverAsk.saveToDisk",
					"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,"
							+ "application/pdf,application/doc, application/msword");

			profile.setPreference("permissions.default.stylesheet", 1);
			profile.setPreference("permissions.default.image", 1);
			profile.setPreference("network.http.connection-timeout", 15000);
			profile.setPreference("network.http.connection-retry-timeout", 15000);

			profile.setPreference("browser.download.manager.showWhenStarting", false);
			profile.setPreference("pdfjs.disabled", true);
			options.setProfile(profile);
			driver = new FirefoxDriver(options);
			break;
		case "phantom":
			driver = new PhantomJSDriver(capabilities);
			break;
		case "htmlunit":
			driver = new HtmlUnitDriver(BrowserVersion.FIREFOX_52, true);
			break;
		default:
			driver = new PhantomJSDriver(capabilities);
			break;
		}

		if (!browser.equals("firefox"))
			driver.manage().timeouts().pageLoadTimeout(150000, TimeUnit.SECONDS);

		try {
			System.out.println("Starting to load: " + loginUrl);
			driver.get(loginUrl);
			System.out.println("Waiting for page to load: " + driver.getCurrentUrl());
			Thread.sleep(15000);
			System.out.println("Assuming page is loaded: " + driver.getCurrentUrl());

			System.out.println("Authenticating user");
			WebElement user = null;
			try {
				user = driver.findElement(By.cssSelector(usernameInputSelector));
				user.sendKeys(username);
			} catch (Exception e) {
				System.out
						.println("Exiting Reason: Could not find username input place with selector: " + usernameInputSelector);
				return;
			}

			if(loginMethod == 2){
				WebElement nextButton = null;
				try{
					nextButton = driver.findElement(By.cssSelector(nextButtonSelector));
					nextButton.click();
					Thread.sleep(10000);
				}catch(Exception e){
					System.out.println("Exception in login intermediate step.");
					return;
				}
			}
			
			WebElement pass = null;
			try {
				pass = driver.findElement(By.cssSelector(passwordInputSelector));
				pass.sendKeys(password);
			} catch (Exception e) {
				System.out
						.println("Exiting Reason: Could not find password input place with selector: " + passwordInputSelector);
				return;
			}

			Thread.sleep(2000);
			WebElement loginButton = null;
			try {
				loginButton = driver.findElement(By.cssSelector(loginButtonSelector));
			} catch (Exception e) {
				System.out.println("Exiting Reason: Could not find login button with selector: " + loginButtonSelector);
				return;
			}
			loginButton.click();
			Thread.sleep(8000);
			System.out.println("User Authenticated");
			Set<Cookie> cookieSet = driver.manage().getCookies();
			System.out.println("Cookies start -------------------------");
			PrintWriter writer = new PrintWriter(outputFile, "UTF-8");
			for (Cookie cookie : cookieSet) {
				writer.println(cookie.getName() + "=" + cookie.getValue());
			}
			System.out.println("cookies end");

			writer.close();
			driver.close();
			driver.quit();
		} catch (Exception e) {
			System.out.println("Some Exception occurred, Please verify if parameters are correctly specified. Error message: "
					+ e.getMessage());
			System.out.println("Detailed stack flow: ");
			e.printStackTrace();
		}
		driver.quit();
	}
}