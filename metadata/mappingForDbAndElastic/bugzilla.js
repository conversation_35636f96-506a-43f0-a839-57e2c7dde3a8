module.exports = {
    "objects": [{
        "name": "bugs",
        "label": "Bugzilla Bugs",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [{
        "name": "id",
        "label": "ID",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "summary",
        "label": "Summary",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },

    {
        "name": "product",
        "label": "Product",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "component",
        "label": "Component",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "version",
        "label": "Version",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "priority",
        "label": "Priority",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "severity",
        "label": "Severity",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "comments",
        "label": "Comments",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "attachments",
        "label": "Attachments",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "status",
        "label": "Status",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "last_change_time",
        "label": "Last Change Time",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1,
        "isMasked": 0
    },
    {
        "name": "creator",
        "label": "Creator",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1,
        "isMasked": 0
    },
    {
        "name": "url",
        "label": "URL",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1,
        "isMasked": 0
    },
    {
        "name": "groups",
        "label": "Groups",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1,
        "isMerged": 1
    },
    {
        "name": "classification",
        "label": "Classification",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "cc",
        "label": "CC",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1,
        "isMerged": 1,
        "isMasked": 0
    },
    {
        "name": "assigned_to",
        "label": "Assigned To",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1,
        "isMerged": 1,
        "isMasked": 0
    },
    {
        "name": "creation_time",
        "label": "Creation Time",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1,
        "isMerged": 1,
        "isMasked": 0
    },
    {
        "name": "is_open",
        "label": "Is Open",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    }]
}
