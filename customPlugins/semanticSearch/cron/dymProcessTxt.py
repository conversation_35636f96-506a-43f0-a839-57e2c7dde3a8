from __future__ import print_function
# The following import is optional but convenient under Python 2.7
from __future__ import unicode_literals
import re
import os
import string
import sys
import logging
#from tokenizer import tokenize, TOK
import re
import polyglot
from polyglot.text import Text

counter = int(sys.argv[1])
# counter = 1
dymDictFileName = 'dymDictFile.dic.ignore'
dymRemovedFileName = 'dymRemovedFile.dic.ignore'
dymTextFileName = 'dymTxtFile'
frequency = {}

if not os.path.exists(os.getcwd()+'/customPlugins/semanticSearch/cron/'+dymDictFileName):
    fp = open(os.getcwd()+'/customPlugins/semanticSearch/cron/'+dymDictFileName, 'w+')

for fileCount in range(1,counter + 1):
    document_text = open(os.getcwd()+'/customPlugins/semanticSearch/cron/'+dymTextFileName+str(fileCount)+'.txt', 'r')
    print("\nFile Name-"+os.getcwd()+'/customPlugins/semanticSearch/cron/'+dymTextFileName+str(fileCount)+'.txt')
    text_string = document_text.read().lower()
    printable_str = ''.join(x for x in text_string if x.isprintable())
    print("Printable String done")
    text_string = Text(printable_str)
    print("tokens Generated")
    for token in text_string.words:
        regex = re.compile('[,\.\[\]@_!#$%^&*()<>?/\|}{~:;\"\'+=-]')
        if(regex.search(token) == None):
            count = frequency.get(token,0)
            frequency[token] = count + 1
    counter-=1
    os.remove(os.getcwd()+'/customPlugins/semanticSearch/cron/'+dymTextFileName+str(fileCount)+'.txt')


print("Read all files !!")

sorted_frequency_list = sorted(frequency.items(), key=lambda item: item[1], reverse=True)
sorted_frequency_dict = dict(sorted_frequency_list)
fOpen = open(os.getcwd()+'/customPlugins/semanticSearch/cron/'+dymDictFileName, 'w')
fOpen.close()

fRemovedOpen = open(os.getcwd()+'/customPlugins/semanticSearch/cron/'+dymRemovedFileName, 'w')
fRemovedOpen.close()

sorted_frequency_keys  = sorted_frequency_dict.keys()
cutoff = 20
f = open(os.getcwd()+'/customPlugins/semanticSearch/cron/'+dymDictFileName, "a")
fRemoved = open(os.getcwd()+'/customPlugins/semanticSearch/cron/'+dymRemovedFileName, "a")
count = 0
for words in sorted_frequency_keys:
    if(sorted_frequency_dict[words] > cutoff):
        count+=1
f.write(str(count)+'\n')
print("\nTotal uniquie Words"+str(count))
for words in sorted_frequency_keys:
    if(sorted_frequency_dict[words] > cutoff):
        wordToPrint = words
        f.write(wordToPrint + "\n")
    else:
        wordToPrint = words
        fRemoved.write(wordToPrint+' - ' +str(sorted_frequency_dict[words])+"\n")

print("\ndone")
sys.exit()