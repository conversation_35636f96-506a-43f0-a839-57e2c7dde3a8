const { publishCrons } = require('../kafkaConsumers/publishCrons');
const { getTenantsRateLimitInfo } = require("auth-middleware");
var commonFunctions = require('../../../utils/commonFunctions');

async function publishRateLimitCrons() {
    console.log(">>>>>>>>publishRateLimitCrons");
    const tenantInfo = await getTenantsRateLimitInfo().then((result) => {
        let tenantObj = {};
        for (i in result.data)
            if (Object.keys(result.data[i]).length)
                tenantObj[i] = result.data[i];
        return tenantObj;
    }).catch(err => {
        console.log("Error While Fetching Rate Limit Information for tenants:", err)
    });
    console.log("Tenant rateLimits Info: ", tenantInfo);
    const tenantIds = Object.keys(tenantInfo);
    if (tenantIds.length > 0) {
        tenantIds.map((tenantId) => {
            console.log("Publishing Rate Limit Cron for tenantId:", tenantId);
            publishCrons({
                type: 'ADD',
                service: 'admin',
                tenantId: tenantId,
                frequency: "0 0 * * *",
                cronId: `ratelimitfreq_${tenantId}`,
                processEngine: true,
                information: {
                    eventType: `ratelimitfreq_${tenantId}`,
                    executionMode: 'kafka',
                    kafkaTopic: config.get('kafkaTopic.manageRateLimts'),
                    kafkaValue: {
                        tenantId,
                        date: new Date()
                    },
                },
            });
        })
    }
    console.log("publishRateLimitCrons");
}
async function publishStatusPageTokenCrons() {
    console.log(">>>>>>>>publishStatusPageTokenCrons", config.get('internalAdminSecret'),config.get('adminURL') );

    const values = {
        type: 'ADD',
        service: 'admin',
        frequency: "0 0 * * *",
        tenantId: "DOMAIN_LEVEL_EVENT",
        cronId: `tokenFromSuTostatusPage`,
        processEngine: false,
        command: `curl --location --request GET '${config.get('adminURL')}/scheduledCrons/publishtokentostatuspage' --header 'admin-secret: ${config.get('internalAdminSecret')}'`
    }
    publishCrons(values);
    console.log("publishStatusPageTokenCrons");
}
async function publishAbTestDataCrons() {
    const values = {
        type: 'ADD',
        service: 'admin',
        frequency: "59 23 * * *",
        tenantId: "DOMAIN_LEVEL_EVENT",
        cronId: `abTestCronId`,
        processEngine: false,
        command: `curl --location --request DELETE '${config.get('adminURL')}/admin/ab-testing/deleteExpiredAbTests' --header 'admin-secret: ${config.get('internalAdminSecret')}'`    // need to change admin url in the default json
    }
    publishCrons(values);
    commonFunctions.errorlogger.info("publishAbTestDataCron==Values====>>>>>>>>>>>>>>>>>>>>>>",values);
}
module.exports = {
    publishRateLimitCrons,
    publishStatusPageTokenCrons,
    publishAbTestDataCrons
}