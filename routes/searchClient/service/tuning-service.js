const config = require('config');

var commonFunctions = require('./../../../utils/commonFunctions');
var searchResultKafkaConfig = require('../../admin/searchResultKafkaConfig');

const autoTuning = {

  updateAutoBoostingStatus: async (req) => {
    try {
      commonFunctions.errorlogger.info('Updating auto boosting status for auto based tuning');
      const result = await setAutoTuningParameter(req, "auto_boosting");
      return result;
    } catch (error) {
      commonFunctions.errorlogger.error("Error in updateAutoBoostingStatus function: ", error);
      throw error;
    }
  },

  updateAutoSpellCorrectorStatus: async (req) => {
    try {
      commonFunctions.errorlogger.info('Updating auto spell corrector status for auto based tuning');
      const result = await setAutoTuningParameter(req, "auto_spell_corrector");
      return result;
    } catch (error) {
      commonFunctions.errorlogger.error("Error in updateAutoSpellCorrectorStatus function: ", error);
      throw error;
    }
  },

  updateFacetInterpreterStatus: async (req) => {
    try {
      commonFunctions.errorlogger.info('Updating facet interpreter status for auto based tuning');
      const result = await setAutoTuningParameter(req, "facet_interpreter");
      return result;
    } catch (error) {
      commonFunctions.errorlogger.error("Error in updateFacetInterpreterStatus function: ", error);
      throw error;
    }
  },

  enableNER: async (req) => {
    try {
      commonFunctions.errorlogger.info('Updating NER status for auto based tuning');
      const result = await setAutoTuningParameter(req, "ner");
      return result;
    } catch (error) {
      commonFunctions.errorlogger.error("Error in enableNER function: ", error);
      throw error;
    }
  },

  reRankingEnable: async (req) => {
    try {
      commonFunctions.errorlogger.info('Updating reRanking status for auto based tuning');
      req.body.updateImpactedBoostings = req.body.updateImpactedBoostings === undefined ? true : req.body.updateAutoBoostingStatus;
      req.body.getKafka = req.body.getKafka === undefined ? true : req.body.getKafka;

      if (!req.body.uid && req.body.status === undefined) {
        throw new Error('UID or request status not found');
      }

      commonFunctions.errorlogger.warn('Inside reRanking status update call executing db update call');

      const sql = "update search_clients set re_ranking=? where uid=?";
      const values = [req.body.status ? 1 : 0, req.body.uid];

      await new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(sql, values, (error, resp) => {
          if (error) {
            commonFunctions.errorlogger.error("Error while executing db update status at method reRankingEnable : ", error);
            reject(error);
          } else {
            resolve(resp);
          }
        });
      });

      if (req.body.updateImpactedBoostings) {
        await updateKeywordBoosting(req.body.status, req.body.search_client_id, req);
        await updateIntentBoosting(req.body.status, req.body.search_client_id, req);
      }

      if (req.body.status) {
        let reqBody = {
          uid: req.body.uid,
          tenantId: req.headers['tenant-id']
        };
        let headers = {
          "Content-Type": "application/json",
          "index-service-secret": config.get("indexService.sharedSecret"),
        }
        await new Promise((resolve, reject) => {
          commonFunctions.httpRequest('POST', config.get("indexService.url") + '/index-service/is-config/reRankingTrigger', {}, reqBody, headers, (error, resp) => {
            if (error) {
              commonFunctions.errorlogger.error("Error while executing reRanking trigger at method reRankingEnable : ", error);
              reject(error);
            } else {
              resolve(resp);
            }
          })
        });
      }

      if (req.body.getKafka) {
        let configObj = { "platformId": req.body.search_client_id };
        await new Promise((resolve, reject) => {
          searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj, req, function (err, searchConfig) {
            if (err) {
              reject(err);
            } else {
              resolve(searchConfig);
            }
          });
        })
      }

      return { result: 1 };
    } catch (error) {
      commonFunctions.errorlogger.error("Error in reRankingEnable function: ", error);
      throw error;
    }
  }
}

const updateKeywordBoosting = async (status, id, req) => {
  const isEnable = status ? 0 : 1 ;
  try {
    if(status){
      await new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query("UPDATE keyword_boost SET isBoostingEnabled=? WHERE search_client_id=?",[isEnable,id],(err,response) => {
          if (err) {
            reject(err);
          } else {
            resolve(response);
          }
        });
      });
    } else {
      await new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query("Select last_state,search_client_id from keyword_boost where  search_client_id=?",[id],(err,response) => {
          if (err) {
            reject(err);
          } else if (response.length) {
            connection[req.headers['tenant-id']].execute.query("UPDATE keyword_boost SET isBoostingEnabled=? WHERE search_client_id=?",[response[0].last_state, id], (err, r) => {
              if (err) {
                reject(err);
              } else {
                resolve(response);
              }
            });
          } else {
            resolve(response);
          }
        });
      });
    }
  } catch (error) {
    commonFunctions.errorlogger.error("Error while updating keyword boosting in method updateKeywordBoosting : ", error);
    throw error;
  }
}

const updateIntentBoosting = async (status, id, req) => {
  const isEnable = status ? 0 : 1 ;
  try {
    if(status){
      await new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query("UPDATE intent_boosting SET isBoostingEnabled=? WHERE search_client_id=?",[isEnable,id],(err,response) => {
          if (err) {
            reject(err);
          } else {
            resolve(response);
          }
        });
      });
    } else {
      await new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query("Select last_state,search_client_id from intent_boosting where  search_client_id=?",[id],(err,response) => {
          if (err) {
            reject(err);
          } else if (response.length) {
            connection[req.headers['tenant-id']].execute.query("UPDATE intent_boosting SET isBoostingEnabled=? WHERE search_client_id=?",[response[0].last_state, id], (err, r) => {
              if (err) {
                reject(err);
              } else {
                resolve(response);
              }
            });
          } else {
            resolve(response);
          }
        });
      });
    }
  } catch (error) {
    commonFunctions.errorlogger.error("Error while updating intent boosting in method updateIntentBoosting : ", error);
    throw error;
  }
}

const setAutoTuningParameter = async (req, parameter) => {
  try {
    req.body.getKafka = req.body.getKafka === undefined ? true : req.body.getKafka; // Default value for getKafka
    
    if (!req.body.uid || req.body.status === undefined) {
      throw new Error('UID or request status not found');
    }

    commonFunctions.errorlogger.warn('Inside auto tuning status update call executing db update call');

    const sql = `UPDATE search_clients SET ${parameter} = ? WHERE uid = ?`;
    const values = [req.body.status ? 1 : 0, req.body.uid];

    await new Promise((resolve, reject) => {
      connection[req.headers['tenant-id']].execute.query(sql, values, (error, resp) => {
        if (error) {
          commonFunctions.errorlogger.error("Error while executing db update status at method setAutoTuningParameter : ", error);
          reject(error);
        } else {
          resolve(resp);
        }
      });
    });

    if (req.body.getKafka) {
      const configObj = { "platformId": req.body.search_client_id };
      await new Promise((resolve, reject) => {
        searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj, req, (err, searchConfig) => {
          if (err) {
            reject(err);
          } else {
            resolve(searchConfig);
          }
        });
      });
    }

    return { result: 1 };
  } catch (error) {
    commonFunctions.errorlogger.error("Error in setAutoTuningParameter function: ", error);
    throw error;
  }
};

const contentTuning = {

  changeStatus: async (req, params, content) => {
    commonFunctions.errorlogger.info('Changing status for content based tuning');
    try {
      await new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
          "UPDATE search_clients_to_content_objects SET isContentBoostingEnabled=? WHERE search_client_id=?",
          [content.enabled, params.searchClientId],
          (err, response) => {
            if (err) {
              reject(err);
            }else{
              resolve(response);
            }
          }
        );
      });
    } catch (error) {
      commonFunctions.errorlogger.error("Error in changeStatus method for content based tuning : ", error);
      throw error;
    }
  },

  deleteOld:  async (req, params, content) => {
    commonFunctions.errorlogger.info('Deleting old for content based tuning');
    try {
      if (content.enabled) {
        await new Promise((resolve, reject) => {
          connection[req.headers['tenant-id']].execute.query(
            `DELETE FROM field_boosting WHERE search_client_id = ?`,
            [params.searchClientId], (err, response) => {
              if (err) {
                reject(err);
              } else {
                resolve(response);
              }
            }
          );
        });
      }               
    } catch (error) {
      commonFunctions.errorlogger.error("Error in deleteOld method for content based tuning : ", error);
      throw error;
    }
  },

  insertNew:  async (req, content) => {
    commonFunctions.errorlogger.info('Inserting new for content based tunin');
    try {
      if (content.enabled) {
        const values = [];
        if (content.data && content.data.fieldBoostingRow && content.data.fieldBoostingRow.length > 0) {
          content.data.fieldBoostingRow.map((item) => {
            values.push([
              item.content_source_object_field_id,
              item.search_client_id,
              item.name,
              item.boosting_factor,
              item.sortValue,
              item.isSort
            ]);
          });
        }
        if (values.length > 0) {
          const sql = `Insert into field_boosting (
          content_source_object_field_id,
          search_client_id, 
          name, 
          boosting_factor, 
          sort_value, 
          is_sort
          ) VALUES ?`;
          await new Promise((resolve, reject) => {
            connection[req.headers['tenant-id']].execute.query(sql, [values], (err, response) => {
              if (err) {
                reject(err);
              } else {
                resolve(response);
              }
            });
          });
        } 
      }
    } catch (error) {
      commonFunctions.errorlogger.error("Error in insertNew method for content based tuning : ", error);
      throw error;
    }
  },

  insertObjectBoosting: async (req, content) => {
    commonFunctions.errorlogger.info('Inserting object boosting for content based tuning');
    try {
      const values = [];
      if (content.data && content.data.boostingFactor && content.data.boostingFactor.length > 0) {
        content.data.boostingFactor.map((item) => {
          values.push([
            item.boosting_factor || 1,
            item.id,
            item.search_client_id,
          ]);
        });
      }

      if (values.length > 0) {
        let sql = "";
        let valueTOSend = [];
        values.forEach((e) => {
          sql += `UPDATE search_clients_to_content_objects SET boosting_factor = ? WHERE content_source_object_id = ? AND search_client_id=?;`;
          valueTOSend = valueTOSend.concat(e)
        })
              
        await new Promise((resolve, reject) => {
          connection[req.headers['tenant-id']].execute.query(sql, valueTOSend, (err, response) => {
            if (err) {
              reject(err);
            }else{
              resolve(response);
            }
          });
        });
      }

    } catch (error) {
      commonFunctions.errorlogger.error("Error in insertObjectBoosting method for content based tuning : ", error);
      throw error;
    }
  }
}

const customTuning = {

  changeStatus: async (req, params, custom) => {
    commonFunctions.errorlogger.info('Changing status for custom based tuning');
    try {
      await new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
          "UPDATE custom_boosting SET custom_boosting_enabled=? WHERE search_client_id=?",
          [custom.data.isCustomBoostingEnabled ? '1' : '0', params.searchClientId],
          (err, response) => {
            if (err) {
              reject(err);
            }else{
              resolve(response);
            }
          }
        );
      });
    } catch (error) {
      commonFunctions.errorlogger.error("Error in changeStatus method for custom based tuning : ", error);
      throw error;
    }
  },

  deleteOld: async (req, params, custom) => {
    commonFunctions.errorlogger.info('Deleting old for custom based tuning');
    try {
      if (custom.data.isCustomBoostingEnabled) {
        await new Promise((resolve, reject) => {
          connection[req.headers['tenant-id']].execute.query(
            `DELETE FROM custom_boosting WHERE search_client_id = ?`,
            [params.searchClientId], (err, response) => {
              if (err) {
                reject(err);
              }else{
                resolve(response);
              }
            }
          );
        });
      }             
    } catch (error) {
      commonFunctions.errorlogger.error("Error in deleteOld method for custom based tuning : ", error);
      throw error;
    }
  },

  insertNew: async (req, params, custom) => {
    commonFunctions.errorlogger.info('Inserting new for custom based tuning');
    try {
      const jobs = [];

      if (custom.data && custom.data.isCustomBoostingEnabled && custom.data.customBoostParam && custom.data.customBoostParam.length > 0) {

        custom.data.customBoostParam.forEach(item => {
          jobs.push(saveCustomBoosting.bind(
            null,
            {
              ...item,
              custom_boosting_enabled: custom.data.isCustomBoostingEnabled ? 1 : 0,
              search_client_id: params.searchClientId
            },
            req.headers["tenant-id"]
          ));
        });
            
        for(const job of jobs) {
          await job();
        }
      }
    } catch (error) {
      commonFunctions.errorlogger.error("Error in insertNew method for custom based tuning : ", error);
      throw error;
    }
  }
}

async function saveCustomBoosting(dataObject, tenantId) {
  sqlInsert =
    `INSERT INTO custom_boosting(
      custom_boosting_enabled, 
      title_boosting_factor, 
      solved_boosting_factor, 
      click_boosting_enabled, 
      click_boosting_base, 
      date_boosting_enabled, 
      offset,
      scale,
      decay_rate,
      search_client_id,
      attached_articles_enabled,
      attached_articles_base,
      common_click_score_enabled
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    await new Promise((resolve, reject) => {
      connection[tenantId].execute.query(
      sqlInsert,
      [
        dataObject.custom_boosting_enabled,
        dataObject.title_boosting_factor,
        dataObject.solved_boosting_factor,
        dataObject.click_boosting_enabled,
        dataObject.click_boosting_base,
        dataObject.date_boosting_enabled,
        dataObject.offset,
        dataObject.scale,
        dataObject.decay_rate,
        dataObject.search_client_id,
        dataObject.attached_articles_enabled,
        dataObject.attached_articles_base,
        dataObject.common_click_score_enabled,
      ],
      (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      }
    );
  });
}

const intentTuning = {

  deleteOld: async (req, params, intent) => {
    commonFunctions.errorlogger.info('Deleting old for intent based tuning');
    try {
      let deletedIntents = [];

      if (intent.dData && intent.dData.length > 0) {
        intent.dData.map((item) => {
          deletedIntents.push(item.intent);
        });
      }

      if (intent.data && intent.data.length > 0) {
        intent.data.map((item) => {
          deletedIntents.push(item.intent);
        });
      }

      deletedIntents = deletedIntents.filter((v, i, a) => a.indexOf(v) === i);

      if (deletedIntents.length > 0) {
        var deleteData = "";
        deletedIntents.map((o, i) => {
          o = JSON.stringify(o);
          if (deletedIntents.length - 1 == i) {
            deleteData = deleteData.concat(o);
          } else {
            deleteData = deleteData.concat(o, ",");
          }
        });

        deleteData = "(" + deleteData + ")";
        let query = `DELETE FROM intent_boosting WHERE search_client_id = ? AND intent IN ` + deleteData;
              
        await new Promise((resolve, reject) => {
                
          connection[req.headers['tenant-id']].execute.query(
            query,
            [params.searchClientId], (err, response) => {
              if (err) {
                reject(err);
              } else {
                resolve(response);
              }
            }
          );
        }); 
      }

    } catch (error) {
      commonFunctions.errorlogger.error("Error in deleteOld method for custom based tuning : ", error);
      throw error;
    }
  },

  insertNew: async (req, params, intent) => {
    commonFunctions.errorlogger.info('Inserting new for intent based tuning');
    try {
      const values = [];
      if (intent.data && intent.data.length > 0) {
        intent.data.map((item) => {
          item.documents.map((document) => {
            values.push([
              document.intent,
              document.index_name || document.indexName,
              document.indexLabel,
              document.documentId,
              document.rank,
              item.search_client_id,
              document.title || '',
              document.href || '',
              document.object_name || document.objectName,
              document.objectLabel,
              intent.enabled ? 1 : 0,
              document.last_state,
              document.bypass_filter
            ]);
          });
        });
      }

      if (values.length > 0) {
        const sql = `Insert into intent_boosting (
          intent,
          index_name,
          index_label,
          document_id,
          rank,
          search_client_id,
          title,
          href,
          object_name,
          object_label,
          isBoostingEnabled,
          last_state,
          bypass_filter
        ) VALUES ?`;
            
        await new Promise((resolve, reject) => {
              
          connection[req.headers['tenant-id']].execute.query(sql, [values], (err, response) => {
            if (err) {
              reject(err);
            }else{
              resolve(response);
            }
          });
        });
      } 

      if (values && values.length == 0) {
        const sql = `UPDATE intent_boosting set isBoostingEnabled = ? WHERE search_client_id = ?`;

        await new Promise((resolve, reject) => {
          connection[req.headers['tenant-id']].execute.query(sql, [intent.enabled ? 1 : 0, params.searchClientId], (err, response) => {
            if (err) {
              reject(err);
            } else {
              resolve(response);
            }
          });
        });
      } 
    } catch (error) {
      commonFunctions.errorlogger.error("Error in insertNew method for custom based tuning : ", error);
      throw error;
    }
  }
}

const keywordTuning = {

  changeStatus: async (req, params, keyword) => {
    commonFunctions.errorlogger.info('Changing status for keyword based tuning');
    try {
      await new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(
          "UPDATE search_clients SET synonymKeywordBoosting=? WHERE id=?",
          [keyword.synonymKeywordBoostToggle ? 1 : 0, params.searchClientId],
          (err, response) => {
            if (err) {
              reject(err);
            } else {
              resolve(response);
            }
          }
        );
      });
    } catch (error) {
      commonFunctions.errorlogger.error("Error in changeStatus method for keyword based tuning : ", error);
      throw error;
    }
  },

  deleteOld: async (req, params, keyword) => {
    commonFunctions.errorlogger.info('Deleting old for keyword based tuning');
    try {
      const deletedIds = [];
      if (keyword.dData && keyword.dData.length > 0) {
        keyword.dData.map((item) => {
          deletedIds.push(item.recordId);
        });
      }

      if (keyword.data && keyword.data.length > 0) {
        keyword.data.map((item) => {
          deletedIds.push(item.recordId);
        });
      }
            
      if (deletedIds.length > 0) {

        var deleteData = "";
        deletedIds.map((o, i) => {
          o = JSON.stringify(o);
          if (deletedIds.length - 1 == i) {
            deleteData = deleteData.concat(o);
          } else {
            deleteData = deleteData.concat(o, ",");
          }
        });

        deleteData = "(" + deleteData + ")";
        let query = `DELETE FROM keyword_boost WHERE search_client_id = ? AND record_id IN ` + deleteData;
              
        await new Promise((resolve, reject) => {
          connection[req.headers['tenant-id']].execute.query(
            query,
            [params.searchClientId], (err, response) => {
              if (err) {
                reject(err);
              } else {
                resolve(response);
              }
            }
          );
        });
      }
    } catch (error) {
      commonFunctions.errorlogger.error("Error in deleteOld method for keyword based tuning : ", error);
      throw error;
    }
  },

  insertNew: async (req, params, keyword) => {
    commonFunctions.errorlogger.info('Inserting new for keyword based tuning');
    try {
      docsDetailsForAnalytics = [];

      if (keyword.data && keyword.data.length > 0) {
        keyword.data.map((u) => {
          u.keywordValues.map((v) => {
            if (v.event) {
              docsDetailsForAnalytics.push({
                event: v.event,
                searchString: v.searchString,
                indexName: u.index,
                indexType: u.type,
                recordId: u.recordId.replace(u.index + "_" + u.type + "_", ""),
                uid: params.uid,
                title: u.title,
                url: u.href,
                tenantId: req.headers["tenant-id"],
              });
              delete v.event;
            }
          });
        });
      }

      if (keyword.dDataAnalytics && keyword.dDataAnalytics.length > 0) {
        keyword.dDataAnalytics.map(v => {
          docsDetailsForAnalytics.push({
            event: v.event,
            searchString: v.searchString,
            indexName: v.indexName,
            indexType: v.indexType,
            recordId: v.recordId.replace(v.indexName + "_" + v.indexType + "_", ""),
            uid: params.uid,
            title: v.title,
            url: v.url,
            tenantId: req.headers["tenant-id"],
          })
        })
      }

      const values = [];
      if (keyword.data && keyword.data.length > 0) {
        keyword.data.map((item) => {
          item.keywordValues.map((document) => {
            values.push([
              document.searchString,
              item.index,
              item.type,
              item.recordId,
              document.rank,
              params.searchClientId,
              keyword.enabled ? 1 : 0,
              item.last_state ? 1 : 0,
              item.title,
              item.href,
              item.object_label,
              item.source_label,
              document.bypass_filter,
            ]);
          });
        });
      }

      if (values.length > 0) {
        const sql = `Insert into keyword_boost (
          search_string, 
          index_name, 
          index_type, 
          record_id,
          boosting_level,
          search_client_id,
          isBoostingEnabled,
          last_state,
          title,
          href,
          object_label,
          source_label,
          bypass_filter
        ) VALUES ?`;
              
        await new Promise((resolve, reject) => {

          connection[req.headers['tenant-id']].execute.query(sql, [values], (err, response) => {
            if (err) {
              reject(err);
            } else {
              resolve(response);
            }
          });
        });
      }  
              
      if (keyword.data && keyword.data.length == 0) {

        const sql = `UPDATE keyword_boost set isBoostingEnabled = ? WHERE search_client_id = ?`;

        await new Promise((resolve, reject) => {
          connection[req.headers['tenant-id']].execute.query(sql, [keyword.enabled ? 1 : 0, params.searchClientId], (err, response) => {
            if (err) {
              reject(err);
            } else {
              resolve(response);
            }
          });
        });
      }
    } catch (error) {
      commonFunctions.errorlogger.error("Error in insertNew method for keyword based tuning : ", error);
      throw error;
    }
  }
}

const boostingDocs = {

  saveDocs: async (req, params, intent, keyword) => {
    commonFunctions.errorlogger.info('Saving Deleted Docs for keyword and intent based tuning');
    const savedDocs = {
      keyword : [],
      intent: [],
      savedIntents: [],
      savedIds: []
    }
    try {
      const savedIds = [];
      if (keyword.dData && keyword.dData.length > 0) {
        keyword.dData.map((item) => {
          savedIds.push(item.recordId);
        });
      }

      if (keyword.data && keyword.data.length > 0) {
        keyword.data.map((item) => {
          savedIds.push(item.recordId);
        });
      }
            
      let keywordDocs = [];
      if (savedIds.length > 0) {
        var savedData = "";
        savedIds.map((o, i) => {
          o = JSON.stringify(o);
          if (savedIds.length - 1 == i) {
            savedData = savedData.concat(o);
          } else {
            savedData = savedData.concat(o, ",");
          }
        });

        savedData = "(" + savedData + ")";
        let sql = `SELECT * FROM keyword_boost WHERE search_client_id = ? AND record_id IN ` + savedData;
        keywordDocs = await new Promise((resolve, reject) => {
          connection[req.headers['tenant-id']].execute.query(sql, [params.searchClientId], (e, r, f) => {
            if (e) reject(e);
            else {
              resolve(r);
            }
          });
        });
      } 

      savedDocs.keyword = keywordDocs;
      savedDocs.savedIds = savedIds;

      let savedIntents = [];

      if (intent.dData && intent.dData.length > 0) {
        intent.dData.map((item) => {
          savedIntents.push(item.intent);
        });
      }

      if (intent.data && intent.data.length > 0) {
        intent.data.map((item) => {
          savedIntents.push(item.intent);
        });
      }

      savedIntents = savedIntents.filter((v, i, a) => a.indexOf(v) === i);

      let intentDocs = []; 
      if (savedIntents.length > 0) {
        var savedData = "";
        savedIntents.map((o, i) => {
          o = JSON.stringify(o);
          if (savedIntents.length - 1 == i) {
            savedData = savedData.concat(o);
          } else {
            savedData = savedData.concat(o, ",");
          }
        });

        savedData = "(" + savedData + ")";
        const sql = `
          SELECT * 
          FROM intent_boosting ib 
          INNER JOIN search_clients sc ON ib.search_client_id = sc.id 
          WHERE ib.search_client_id = ? 
          AND ib.intent IN 
        ` + savedData;

        intentDocs = await new Promise((resolve, reject) => {
          connection[req.headers['tenant-id']].execute.query(sql, [params.searchClientId], (e, r, f) => {
            if (e) reject(e);
            else {
              resolve(r);
            }
          });
        });
      }
      savedDocs.intent = intentDocs;
      savedDocs.savedIntents = savedIntents;
      
      return savedDocs;
    } catch (error) {
      commonFunctions.errorlogger.error("Error in saveDocs method for boostingDocs : ", error);
      throw error;
    }
  }
}

module.exports = {
  contentTuning : contentTuning,
  customTuning : customTuning,
  intentTuning : intentTuning,
  keywordTuning : keywordTuning,
  autoTuning: autoTuning,
  boostingDocs: boostingDocs,
}
