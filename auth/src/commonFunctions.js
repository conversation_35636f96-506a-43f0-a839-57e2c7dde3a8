const setTenantToHeaders = (req, data, cb) => {
    req.headers['tenant-id'] = data.tenantId || data.tenant_id;
    req.headers.databaseName = data.databaseName || data.database_name;
    req.headers.session = {};
    req.headers.session.userId = data.userId;
    req.headers.session.roleId = data.roleId;
    req.headers.session.email = data.email;
    req.headers.session.accessToken = data.accessToken;
    req.headers.session.is_federated = data.is_federated;
    req.headers.session.esClusterDns = data.esClusterDns   || data.es_cluster_dns  || '';
    req.headers.session.esClusterId = data.esClusterId     || '';
    req.headers.session.esClusterIp = data.esClusterIp     ||  data.es_cluster_ip  || '';
    req.headers.session.esClusterName = data.esClusterName || data.es_cluster_name || '';
    req.headers.session.mlClusterDns  = data.mlClusterDns  || data.ml_cluster_dns  || '';
    req.headers.session.mlClusterId = data.mlClusterId     || '';
    req.headers.session.mlClusterIp = data.mlClusterIp     || data.ml_cluster_ip   || '';
    req.headers.session.mlClusterName = data.mlClusterName || data.ml_cluster_name || '';
    req.headers.session.tpk = data.tpk || '';
    req.headers.session.config = data.config ? JSON.parse(data.config) : '' ;
    req.headers.session.apps = data.apps;
    req.headers.session.name = data.name;

    cb(null, data);
}

const checkIfTenantIdExists = (req) => {
    const tenantId = req.headers['tenant-id'] || req.query['tenant-id'] || req.body['tenant-id'];
    if(tenantId){
        return true;
    }
    return false;
}

module.exports = {
    setTenantToHeaders,
    checkIfTenantIdExists
}