/**
 * Created by <PERSON><PERSON><PERSON> on 14-05-2016.
 */

/** https://github.com/dsquier/oauth2-server-php-mysql **/
// var config = require('./../../../config')
var Sequelize = require('sequelize');

var db = {
  sequelize: new Sequelize(
    config.get("databaseSettings.database"),
    config.get("databaseSettings.user"),
    config.get("databaseSettings.password"),
    {
      host: config.get("databaseSettings.host"),
      database: config.get("databaseSettings.database"),
      username: config.get("databaseSettings.user"),
      password: config.get("databaseSettings.password"),
      dialect: 'mysql', // PostgreSQL, MySQL, MariaDB, SQLite and MSSQL See more: http://docs.sequelizejs.com/en/latest/
      logging: false,
      dialectOptions: {
       // socketPath: '/var/run/mysqld/mysqld.sock'
      }
      // timezone: '+05:30'
    }
  )
};

db.User                   = db.sequelize.import('./User');
db.OAuthClient            = db.sequelize.import('./OAuthClient');
db.OAuthAccessToken       = db.sequelize.import('./OAuthAccessToken');
db.OAuthAuthorizationCode = db.sequelize.import('./OAuthAuthorizationCode');
db.OAuthRefreshToken      = db.sequelize.import('./OAuthRefreshToken');
db.OAuthScope             = db.sequelize.import('./OAuthScope');

Object.keys(db).forEach(function(modelName) {
  if ('associate' in db[modelName]) {
    db[modelName].associate(db);
  }
  db[modelName].sync();
});

module.exports = db;