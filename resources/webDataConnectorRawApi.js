function commonModal(modalName, closeName) {
  var modal = document.getElementById(`${modalName}`);

    // Get the button that opens the modal
    var btn = document.getElementById("fetchSuAnalytics");
    
    // Get the <span> element that closes the modal
    var span = document.getElementsByClassName(`${closeName}`)[0];
    
    // When the user clicks the button, open the modal 
        modal.style.display = "block";
    
    // When the user clicks on <span> (x), close the modal
    span.onclick = function() {
      modal.style.display = "none";
    }
    
    // When the user clicks anywhere outside of the modal, close it
    window.onclick = function(event) {
      if (event.target == modal) {
        modal.style.display = "none";
      }
    }
}
function getoldDate() {
  var d = new Date();
  d.setMonth(d.getMonth()-12);
  let dyear = d.getFullYear();
  let dmonth = d.getMonth()+1;
  let dday = d.getDate();
  if (dday < 10) {
    dday = '0' + dday;
  }
  if (dmonth < 10) {
    dmonth = '0' + dmonth;
  }
  const oldDate = dyear+'-'+dmonth+'-'+dday;

  return oldDate;
}
function modalLogic() {
  var startDate = document.getElementById("start").value;
  var endDate = document.getElementById("end").value;
  const oldDate = getoldDate();
  if (startDate < oldDate || endDate < oldDate) {
    commonModal("myModalRange","closeRange");
  } else if (startDate > endDate) {
    commonModal("myModal","close");
  } else {
    myFunction();
  }
}
function myFunction() {
    var startDate = document.getElementById("start").value;
    console.log(startDate);
    var endDate = document.getElementById("end").value;
    console.log(endDate);
    var uids = document.getElementById("searchClientNumber");
    var selectedUid = uids.options[uids.selectedIndex].value;
  
    var dateRange = {
      "startDate": startDate,
      "endDate": endDate,
      "searchClient_Id": selectedUid
    };
    tableau.connectionData = JSON.stringify(dateRange);
  }
  (function () {
    'use strict';
  
    $(document).ready(function () {
  
      var accessToken = Cookies.get("accessToken");
      var hasAuth = accessToken && accessToken.length > 0;
      updateUIWithAuthState(hasAuth);
      if (!accessToken) {
        $("#connectbutton").click(function () {
          doAuthRedirect();
        });
  
      } else {
        $('#fetchSuAnalytics').click(function () {
          var startDate = document.getElementById("start").value;
          var endDate = document.getElementById("end").value;
          const oldDate = getoldDate();
          if (startDate < oldDate || endDate < oldDate) {
            commonModal("myModalRange","closeRange");
          } else if (startDate > endDate) {
            commonModal("myModal","close");
          } else {
            myFunction();
            tableau.connectionName = "web data connector";
            tableau.submit();
          }
        });
      }
    });
  
    function doAuthRedirect() {
      config.clientId = document.getElementById("clientId").value;

      if (!config.clientId.trim().length){
        return;
      }
      var appId = config.clientId;
      if (tableau.authPurpose === tableau.authPurposeEnum.ephemerel) {
        appId = config.clientId; // This should be Desktop
      } else if (tableau.authPurpose === tableau.authPurposeEnum.enduring) {
        appId = config.clientId; // This should be the Tableau Server appID
      }
  
      var url = config.authUrl + '/authorise?response_type=code&client_id=' + config.clientId + '&redirect_uri=' + config.redirectUri;
      window.location.href = url;
    }
    
  
    function updateUIWithAuthState(hasAuth) {
      if (hasAuth) {
        $(".notsignedin").css('display', 'none');
        $(".signedin").css('display', 'block');
        $("#connectbutton").hide();
        $("#fetchSuAnalytics").show();
        $("#start").show();
        $("#end").show();
        $("#submitDateBtn").show();
      } else {
        $(".notsignedin").css('display', 'block');
        $(".signedin").css('display', 'none');
        $("#connectbutton").show();
        $("#fetchSuAnalytics").hide();
        $("#start").hide();
        $("#end").hide();
        $("#submitDateBtn").hide();
      }
    }
    var connector = tableau.makeConnector();
    /*
     * First phase of WDC
     * Interactive phase: Interact with the user --(1)
     */
    connector.init = function (initCallback) { // IP--(2)
      tableau.authType = tableau.authTypeEnum.custom;
      //If we are in the auth phase we only want to show the UI needed for auth
      if (tableau.phase == tableau.phaseEnum.authPhase) {
        $("#fetchSuAnalytics").css('display', 'none');
      }
  
      if (tableau.phase == tableau.phaseEnum.gatherDataPhase) {
        // If the API that WDC is using has an endpoint that checks
        // the validity of an access token, that could be used here.
        // Then the WDC can call tableau.abortForAuth if that access token is invalid.
  
        //tableau.abortForAuth(); ===> when a user, who has not been authenticated, opens an existing workbook and attempts to refresh data. 
        //In this case, the auth token is no longer available.
      }
  
      var accessToken = Cookies.get("accessToken");
      var hasAuth = accessToken && accessToken.length > 0;
      updateUIWithAuthState(hasAuth);
  
      initCallback(); // IP--(3)
  
      //after calling Connector waits for user i/p
      // If we are not in the data gathering phase, we want to store the token
      // This allows us to access the token in the data gathering phase
  
      if (tableau.phase == tableau.phaseEnum.interactivePhase || tableau.phase == tableau.phaseEnum.authPhase) {
        if (hasAuth) {
          tableau.password = accessToken;
  
          if (tableau.phase == tableau.phaseEnum.authPhase) {
            // Auto-submit here if we are in the auth phase
            tableau.submit()
          }
  
          return;
        }
      }
    }; //end of init phase
  
    /*
     *Gathering data phase (steps 1 to 3 same)
     */
    //GP --4
    connector.getSchema = async function (schemaCallback) {
      var getDataSourceUrl = config.authUrl + "/api/v3/ds/get-data-sources";
      var dataSources = [];
      let schema_table_final = [];
      var accessToken = tableau.password;
      tableau.log('Connection URL ----------------' + getDataSourceUrl + '------Access token' + accessToken);
  
      var xhr = await $.ajax({
        url: getDataSourceUrl,
        contentType: 'application/json',
        method: "POST",
        headers: {
          authorization: "Bearer " + accessToken
        },
        success: function (data) {
          if (data != 'null' && data != 'undefined' && data != '') {
            dataSources = data.data;
          } else {
            tableau.abortWithError(data.err);
          }
        },
        error: function (xhr, ajaxOptions, thrownError) {
          // WDC should do more granular error checking here
          // or on the server side.  This is just a sample of new API.
          tableau.abortForAuth(thrownError);
        }
      });
  
      for (let i = 0; i < dataSources.length; i++) {
        var getFieldsUrl = config.authUrl + "/api/v3/ds/get-fields?dataTableName=" + dataSources[i];
        var fields = [];
        let schema_col_array = [];
  
        var xhr_fields = await $.ajax({
          url: getFieldsUrl,
          contentType: 'application/json',
          method: "POST",
          headers: {
            authorization: "Bearer " + accessToken
          },
          success: function (data) {
            //Changes for unsupported data type for tableau while using on app
            if (data != 'null' && data != 'undefined' && data != '') {
              fields = data.data;
              for (let j = 0; j < fields.length; j++) {
                if(fields[j].data_type === "text" || fields[j].data_type === "jsonb" || fields[j].data_type === "uuid") {
                  fields[j].data_type = "string";
                } else if (fields[j].data_type === "timestamp without time zone") {
                  fields[j].data_type = "datetime";
                } else if (fields[j].data_type === "integer" || fields[j].data_type === "smallint") {
                  fields[j].data_type = "int";
                } else if (fields[j].data_type === "boolean") {
                  fields[j].data_type = "bool";
                }
                schema_col_array.push({
                  id: fields[j].column_name,
                  dataType: fields[j].data_type
                });
              }
              let schema_table = {
                id: dataSources[i],
                alias: `${dataSources[i]}`,
                columns: schema_col_array
              };
              schema_table_final.push(schema_table);
            } else {
              tableau.abortWithError(data.err);
            }
          },
          error: function (xhr_fields, ajaxOptions, thrownError) {
            // WDC should do more granular error checking here
            // or on the server side.  This is just a sample of new API.
            tableau.abortForAuth(thrownError);
          }
        });
      }
      schemaCallback(schema_table_final);
    }
  
    connector.getData = async function (table, doneCallback) {
      var gth = JSON.parse(tableau.connectionData);
      let col_arr = [];
      for (let i = 0; i <= table.tableInfo.columns.length; i++) {
        if (table.tableInfo.columns[i] && table.tableInfo.columns[i].id) {
          col_arr.push(table.tableInfo.columns[i].id);
        }
      }
      let status = await getDataUntilDone(col_arr, gth, 10000, 1, table);
      if(status){
        doneCallback();
      }else{
        tableau.abortForAuth(thrownError);
      }
  
    };
  
    async function getDataUntilDone(col_arr, gth, limit, offset, table){
      let loopAgain = true;
      let success = true;
      var getDataUrl = config.authUrl + "/api/v3/ds/get-data";
      var accessToken = tableau.password;
  
      while(loopAgain){
        var xhr = await $.ajax({
          url: getDataUrl,
          data: JSON.stringify({
            fieldNames: col_arr,
            dataTableName: table.tableInfo.id,
            from: gth.startDate,
            to: gth.endDate,
            uid: gth.searchClient_Id,
            limit:limit,
            offset:offset
          }),
          contentType: 'application/json',
          method: 'POST',
          headers: {
            authorization: "Bearer " + accessToken
          },
          success: function (data) {
              if (data && data.data && data.data.rawApiData && data.data.rawApiData.length) {
                if (table.tableInfo.id === 'searches') {
                  for (let i = 0; i < parseInt(data.data.rawApiDataCount, 10); i += 1) {
                    data.data.rawApiData[`${i}`].geo = JSON.stringify(data.data.rawApiData[`${i}`].geo);
                    data.data.rawApiData[`${i}`].filters = JSON.stringify(data.data.rawApiData[`${i}`].filters);
                  }
                }
                let totalPages = Math.floor(((data.data.rawApiDataCount)/limit));
                if (Number.isInteger(((data.data.rawApiDataCount)/limit))) {
                  totalPages = totalPages;
                } else {
                  totalPages = totalPages + 1;
                }
                tableau.reportProgress("Getting Batch "+(offset)+" out of "+totalPages+" of "+ data.data.rawApiDataCount+" results");
                table.appendRows(JSON.parse(JSON.stringify(data.data.rawApiData)));
                tableau.log(table);
                if((offset*limit) <= data.data.rawApiDataCount){
                  offset++;
                }
                else{
                  loopAgain = false;
                  tableau.reportProgress("No More Data");
                }
              } else {
                loopAgain = false;
                tableau.reportProgress("No More Data");
              }
          },
          error: function (xhr, ajaxOptions, thrownError) {
            // WDC should do more granular error checking here
            // or on the server side.  This is just a sample of new API.
            success = false;
            loopAgain = false;
          }
        });
      }
      return success;
    }
    // Register the tableau connector, call this last
    tableau.registerConnector(connector);
  })();
  
