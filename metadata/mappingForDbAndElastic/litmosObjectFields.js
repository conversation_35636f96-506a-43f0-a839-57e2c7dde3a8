module.exports = {
    "objects": [
        {
        "name": "courses",
        "label": "Courses",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": [
            {
                "name": "id",
                "label": "id",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "code",
                "label": "Reference Code",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "name",
                "label": "Title",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "name_formula",
                "label": "Name For Formula",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "active",
                "label": "Active",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "forsale",
                "label": "For Sale",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "originalid",
                "label": "Course Id",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "description",
                "label": "Description",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "ecommerceShortDescription",
                "label": "Ecommerce Short Description",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "ecommerceLongDescription",
                "label": "Ecommerce Long Description",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "courseCodeForBulkImport",
                "label": "Course Code For Bulk Import",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "price",
                "label": "Price",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "accessTillDate",
                "label": "Access Till Date",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 0, //analyzer
                "isActive": 1
            },
            {
                "name": "accessTillDays",
                "label": "Access Till Days",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "courseTeamLibrary",
                "label": "Course Team Library",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "module_name",
                "label": "Module Name",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "module_description",
                "label": "Module Description",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "view_href",
                "label": "View Href",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "post_time",
                "label": "Created Date",
                "type": "datetime",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1,
                "isMerged": 1
            },
            {
                "name": "view_href_type",
                "label": "View Href Type",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            }
        ]

    },
    {
        "name": "learning_path",
        "label": "Learning Path",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": [
            {
                "name": "id",
                "label": "id",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "name",
                "label": "Title",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "description",
                "label": "Description",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "courses",
                "label": "Courses",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "ecommerceShortDescription",
                "label": "Ecommerce Short Description",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "ecommerceLongDescription",
                "label": "Ecommerce Long Description",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "price",
                "label": "Price",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "accessTillDate",
                "label": "Access Till Date",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 0, //analyzer
                "isActive": 1
            },
            {
                "name": "accessTillDays",
                "label": "Access Till Days",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "learningPathTeamLibrary",
                "label": "Learning Path Team Library",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "active",
                "label": "Active",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "forsale",
                "label": "For Sale",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "originalid",
                "label": "Course Id",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "isEquivalency",
                "label": "Is Equivalency",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "seqId",
                "label": "Seq ID",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "learningPathImageURL",
                "label": "Learning Path Image URL",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "view_href",
                "label": "View Href",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            },
            {
                "name": "post_time",
                "label": "Created Date",
                "type": "datetime",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1,
                "isMerged": 1
            },
            {
                "name": "view_href_type",
                "label": "View Href Type",
                "type": "string",
                "isFilterable": 1, //indexing method analyzed OR not analyzed
                "isSortable": 1,
                "isSearchable": 1, //analyzer
                "isActive": 1
            }
        ]

    }],
    "fields": []


}
