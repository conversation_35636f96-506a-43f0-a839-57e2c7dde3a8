const kafkaLib = require("../../../utils/kafka/kafka-lib");

const alterPostInfo = (posts, sf, tenantId) => {
    let { postData, botData } = posts;
    let postToSend = {
        update: false,
        modified_date: new Date(),
        post_title: botData.searchString,
        feed_comment_id: postData.id,
        bot_id: botData.botId,
        bot_name: botData.getUidForBot.name,
        uid: botData.getUidForBot.uid,
        platform_id: botData.getUidForBot.platform_id
    };

    if (sf) {
        postToSend = {
            ...postToSend,
            created_date: postData.createdDate,
            post_answer: postData.body.text,
            feed_id: postData.feedElement.id,
            user_id: botData.userId
        };
    } else {
        postData = postData.data;
        postToSend = {
            ...postToSend,
            created_date: postData.post_time,
            post_answer: postData.body,
            feed_id: postData.conversation.id,
            user_id: postData.author.id,
            feed_comment_id: postData.id,
        };
    }
    postToSend.tenantId = tenantId;
    return postToSend ;
}


const publishHelpfulContent = async (posts, sf, tenantId, callback) => {
    let dataToSend = []
    posts.map(x => {
        if (sf) {
            dataToSend.push({
                modified_date: x.LastModifiedDate,
                feed_comment_id: x.BestCommentId,
                is_helpful: true
            })
        } else {
            dataToSend.push({
                modified_date: x.conversation.last_post_time,
                feed_comment_id: x.id,
                is_helpful: true
            })
        }
    })
    let postToSend = {
        update: true,
        data: dataToSend,
        tenantId
    }
    try{
        await kafkaLib.publishMessage({
            topic:config.get("kafkaTopic.communityHelper"),
            messages:[{
                value:JSON.stringify(postToSend),
            }]
        })
    }catch(e){
        commonFunctions.errorlogger.info('error is', e);
    }
    callback(null, "");
}


module.exports = {
    alterPostInfo,
    publishHelpfulContent
}
