var express = require('express');
var router = express.Router();
var path = require('path');
var fs = require('fs');
var async = require('async');
environment = require(path.join(__dirname, '../../../routes/environment'));
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../../config');
process.env.NODE_CONFIG_DIR = configPath;
console.log(environment.configuration);
config = require('config');
var connection_sql = require('../../../utils/connection');
var commonFunctions = require('../../../utils/commonFunctions')
var searchClient = require('../../../routes/admin/searchClient');
const request = require("request");
const { getEsClient } = require('../../../utils/elastic');
var searchResultKafkaConfig = require('../../../routes/admin/searchResultKafkaConfig');
//  run => node keywordBoostingCron.js updateBoosting ===> for updating all fields
//  run => node keywordBoostingCron.js ===> for updating only deindex field


function updateKeyword(d,req,cb) {
    let querySql;
    querySql = `UPDATE keyword_boost SET deindex=${d.deindex} WHERE index_name ='${d.index_name}' AND record_id = '${d.record_id}'`;
    connection[req.headers['tenant-id']].execute.query(querySql, (err, docs) => {
        cb(null,{})
    })
}

const keywordBoostingCron = async (req, cb) => {
    if(process.argv[3] == "keywordBoostingCsvImport"){
        req = {
            headers : {
                "tenant-id" : process.argv[4]
            }
        }
    }
    var taskArray = [];
    var updateArray = [];
    console.log("I m starting");
    const esClient = await getEsClient(req.headers['tenant-id']);
    connection[req.headers['tenant-id']].execute.query(`SELECT * FROM search_clients`, (err, docs) => {
        let searchClients = docs
        searchClients.forEach((s) => {
            taskArray.push(getSearchClientSettingsFields.bind(null, s.id, s.uid, req, esClient))
        })
        async.series(taskArray, (err, docs) => {
            for (let i = 0; i < docs.length; i++) {
                docs[i].forEach((d) => {
                    updateArray.push(updateKeyword.bind(null,d, req));
                })
            }
            async.series(updateArray, (err, docs) => {
                cb() ;
            })
        })
    });
}

if(process.argv[3] == "keywordBoostingCsvImport"){
    console.log("Import export case");
    connection_sql.handleDisconnect().then(async function (result) {
      keywordBoostingCron(process.argv[4], (err, res) => {
        if (!err) {
          console.log("error");
        }
      });
    })
}




const getSearchClientSettingsFields = (seachClientId, uid,req, esClient, callback) => {
    // Get fields
    let arrtypes = [];
    async.auto({
        get_search_client_settings: (cb) => {
            searchClient.getSearchClient(seachClientId,req, cb);
        },
        getDisplayFields: (cb) => {
            commonFunctions.getDisplayFields(uid,req, function (displayRows) {
                commonFunctions.errorlogger.warn("here in get_display_fields ", displayRows)

                var rowsDisplay = displayRows
                for (var iy = 0; iy < rowsDisplay.length; iy++) {
                    if (rowsDisplay[iy]["display_field_name"] == "href") {
                        href = rowsDisplay[iy]["elastic_field_name"];
                    }
                }

                const unique = [...new Set(rowsDisplay.map(item => item.elastic_object_name))];
                unique.forEach(x => {
                    arrtypes.push(
                        {
                            "type": x,
                            "values": rowsDisplay.filter(z => z.elastic_object_name == x).map(y => { return ({ "fieldName": y.elastic_field_name, "Displaytype": y.display_field_name }) })
                        }
                    )
                })
                cb(null, {})
            })
        },
        getAllKeywordRelatedToId: cb => {
            const query = `SELECT * from keyword_boost WHERE search_client_id = ?`;
            connection[req.headers['tenant-id']].execute.query(query, [seachClientId], (err, docs) => {
                cb(null, docs);
            });
        },
        fetchDataAndSave: ['get_search_client_settings', 'getAllKeywordRelatedToId', 'getDisplayFields', (results, cb) => {
            let boostDoc = results.getAllKeywordRelatedToId;
            matchUrlsElasticBatchwise(results,boostDoc, arrtypes,esClient, (error, res) => {
                if (error) {
                  cb(error);
                } else {
                    cb(null,boostDoc);
                }
              });
        }]
        }, function (err, result) {
            if (err)
            callback(err, "error")
            else{
                // if(!err){
                //     let configObj = { "platformId": seachClientId };
                //     searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
                //         callback(null, result.fetchDataAndSave)
                //     });
                // }
                callback(null, result.fetchDataAndSave)
            }
        })
}

function matchUrlsElasticBatchwise(results, docsUrls, arrtypes,esClient, callback) {
    const batches = createDataBatches(docsUrls, 50);
    let asyncTasks = [];
    for (let i = 0; i < batches.data.batches.length; i++) {
        asyncTasks.push(getUrlElastic.bind(null, results, batches.data.batches[i], arrtypes ,esClient));
    }
    async.series(asyncTasks, function (err, data) {
        if (err) {
            return callback(err);
        }
        return callback(null, []);
    });
}

function getUrlElastic(results, boostDoc, arrtypes,esClient, callback) {
    console.log("Boosted Doc length - -", boostDoc.length);
    async.parallel(boostDoc.map(r => {
                return (cb) => {
                    esClient.get({
                        index: r.index_name + "__"+r.index_type,
                        id: r.record_id
                    },(error, body) => {
                        body = body.body
                if (body && body.found) {
                    var typeData = arrtypes.find(x => x.type == body._source["objectName"])
                    if (typeData) {
                        r.url = body._source.view_href;
                        var titleFieldName = typeData["values"].find(x => { if (x["Displaytype"] == "Title") return x }) ? typeData["values"].find(x => { if (x["Displaytype"] == "Title") return x })["fieldName"] : "_id"
                        r.subject = body._source[titleFieldName] ? body._source[titleFieldName] : body._source.title

                        //logic for href----------------**********************************
                        r.url = results.get_search_client_settings.sources.filter(x => x.enabled).map(y => { let o = y.objects.filter(z => z.enabled); return o }).reduce(commonFunctions.reduceObject).find(x => x.name == body._source["objectName"]).base_href
                        var testRegex = /{{.*?}}/g;
                        var str = r.url;
                        var m;
                        while ((m = testRegex.exec(str)) !== null) {
                            // This is necessary to avoid infinite loops with zero-width matches
                            if (m.index === testRegex.lastIndex) {
                                testRegex.lastIndex++;
                            }
                            m.forEach((match, groupIndex) => {
                                r.url = r.url.replace(match, body["_source"][match.replace("{{", "").replace("}}", "")])
                                    commonFunctions.errorlogger.info(match);
                            });
                        }
                        r.deindex = 0;
                        if (!r.url)
                            r.url = body["_id"] || body["_source"]['id'];
                    } else {
                        r.deindex = 1;
                    }
                   // console.log("docs found");
                } else {
                    r.url = "";
                    r.recordId = r.record_id;
                    r.subject = "Document Not Found"
                    r.deindex = 1;
                    console.log("= = = Docs not found = = =",r);
                }
                cb(null, r)
            });
        }
    }), (error, results) => {
        callback(null, results);
    });
}

const createDataBatches = (data = [], batchSize = 1) => {
    let response = { success: false };
    let batches = [];
    let batchCount = 0;
    try {
        for (let i = 0; i < data.length; i = i + batchSize) {
            batches.push(data.slice(i, i + batchSize));
            batchCount++;
        }
        response.success = true;
        response.data = { batchCount, batches };
    } catch (e) {
        response.error = e;
    }
    return response;
};
module.exports = {
    keywordBoostingCron
}