module.exports = {
    objects: [
        {
            name: "issues",
            label: "Issue",
            status: 1,
            boosting_factor: 1,
            base_url: "",
            fields: [
                {
                    name: "id",
                    label: "Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "iid",
                    label: "Issue Number",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "title",
                    label: "Title",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "project_id",
                    label: "Repository ID",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "milestone_title",
                    label: "Milestone",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "labels",
                    label: "Label",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "created_at",
                    label: "Created Date",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "updated_at",
                    label: "Updated Date",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "state",
                    label: "Issue State",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "assignee_name",
                    label: "Assignee",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "assignees",
                    label: "Assignees",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "user_notes_count",
                    label: "Comments",
                    type: "integer",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "web_url",
                    label: "View Href",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "description",
                    label: "Issue Description",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "confidential",
                    label: "Private",
                    type: "boolean",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "upvotes",
                    label: "Upvotes",
                    type: "integer",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "downvotes",
                    label: "Downvotes",
                    type: "integer",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "due_date",
                    label: "Due Date",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "merge_requests_count",
                    label: "Linked Merge Requests",
                    type: "integer",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "task_status",
                    label: "Task Status",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
            ],
        },
        {
            name: "projects",
            label: "Projects",
            status: 1,
            boosting_factor: 1,
            base_url: "",
            fields: [
                {
                    name: "id",
                    label: "Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "name",
                    label: "Name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "description",
                    label: "Description",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "description_html",
                    label: "Description (HTML)",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "default_branch",
                    label: "Default Branch",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "visibility",
                    label: "Visibility",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "ssh_url_to_repo",
                    label: "SSH URL",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "http_url_to_repo",
                    label: "HTTP URL",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "web_url",
                    label: "Web URL",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "readme_url",
                    label: "Readme URL",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "topics",
                    label: "Topics",
                    type: "array",
                    isFilterable: 1,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "owner_id",
                    label: "Owner Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "owner_name",
                    label: "Owner Name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "created_at",
                    label: "Created At",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "updated_at",
                    label: "Updated At",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "last_activity_at",
                    label: "Last Activity At",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "open_issues_count",
                    label: "Open Issues Count",
                    type: "number",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "forks_count",
                    label: "Forks Count",
                    type: "number",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "star_count",
                    label: "Star Count",
                    type: "number",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "license_name",
                    label: "License Name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "license_html_url",
                    label: "License HTML URL",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "namespace_name",
                    label: "Namespace Name",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "namespace_path",
                    label: "Namespace Path",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
            ],
        },
        {
            name: "merge_requests",
            label: "Merge Requests",
            status: 1,
            boosting_factor: 1,
            base_url: "",
            fields: [
                {
                    name: "id",
                    label: "Id",
                    type: "number",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "iid",
                    label: "Internal Id",
                    type: "number",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "project_id",
                    label: "Project Id",
                    type: "number",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "title",
                    label: "Title",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "description",
                    label: "Description",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "state",
                    label: "State",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "imported",
                    label: "Imported",
                    type: "boolean",
                    isFilterable: 1,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "imported_from",
                    label: "Imported From",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "merged_at",
                    label: "Merged At",
                    type: "date",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "merge_after",
                    label: "Merge After",
                    type: "date",
                    isFilterable: 0,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "prepared_at",
                    label: "Prepared At",
                    type: "date",
                    isFilterable: 0,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "closed_at",
                    label: "Closed At",
                    type: "date",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "created_at",
                    label: "Created At",
                    type: "date",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "updated_at",
                    label: "Updated At",
                    type: "date",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "target_branch",
                    label: "Target Branch",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "source_branch",
                    label: "Source Branch",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "upvotes",
                    label: "Upvotes",
                    type: "number",
                    isFilterable: 0,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "downvotes",
                    label: "Downvotes",
                    type: "number",
                    isFilterable: 0,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "source_project_id",
                    label: "Source Project Id",
                    type: "number",
                    isFilterable: 1,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "target_project_id",
                    label: "Target Project Id",
                    type: "number",
                    isFilterable: 1,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "draft",
                    label: "Draft",
                    type: "boolean",
                    isFilterable: 1,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "merge_when_pipeline_succeeds",
                    label: "Merge When Pipeline Succeeds",
                    type: "boolean",
                    isFilterable: 1,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "merge_status",
                    label: "Merge Status",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "detailed_merge_status",
                    label: "Detailed Merge Status",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "sha",
                    label: "SHA",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "merge_commit_sha",
                    label: "Merge Commit SHA",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "squash_commit_sha",
                    label: "Squash Commit SHA",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "user_notes_count",
                    label: "User Notes Count",
                    type: "number",
                    isFilterable: 0,
                    isSortable: 1,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "should_remove_source_branch",
                    label: "Should Remove Source Branch",
                    type: "boolean",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "force_remove_source_branch",
                    label: "Force Remove Source Branch",
                    type: "boolean",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "allow_collaboration",
                    label: "Allow Collaboration",
                    type: "boolean",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "allow_maintainer_to_push",
                    label: "Allow Maintainer to Push",
                    type: "boolean",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
                {
                    name: "web_url",
                    label: "Web URL",
                    type: "string",
                    isFilterable: 0,
                    isSortable: 0,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "squash",
                    label: "Squash",
                    type: "boolean",
                    isFilterable: 1,
                    isSortable: 0,
                    isSearchable: 0,
                    isActive: 1,
                },
            ],
        },
    ],
};
