var mysql = require('mysql');

const tenantSqlConnection = (tenantId, dbName) => {
    return new Promise((resolve, reject)=>{
        if(tenantId && dbName){
            if (!connection[tenantId]) {
                var db_config = {
                    host: config.get('databaseSettings.host'),
                    user: config.get('databaseSettings.user'),
                    password: config.get('databaseSettings.password'),
                    database: dbName,
                    port: config.get('databaseSettings.mysqlPORT'),
                    connectionLimit: 3,
                    multipleStatements: true,
                };
                connection[tenantId] = {};
                connection[tenantId].execute = mysql.createPool(db_config);
                connection[tenantId].lastConnection = new Date();
                connection[tenantId].execute.getConnection(function (err, connection) {
                    if (err) {
                        console.log("error while connection");
                        setTimeout(tenantSqlConnection, 2000, tenantId, dbName);
                    }
                    resolve();
                })
            }else{
                connection[tenantId].lastConnection = new Date();
                resolve();
            }
        }
        resolve();
    });
}


module.exports = {
    tenantSqlConnection
}
