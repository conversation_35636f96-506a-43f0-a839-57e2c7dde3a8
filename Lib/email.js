const constants = require('../constants/appVariables');

exports.sendEmail = function (email, subject, content,callback) {
    const mailOptions = {
        from: 'SearchUnify<<EMAIL>>', // sender address
        to: email, // list of receivers
        subject: subject, // Subject line
        html: content
    };
    transportMail(mailOptions,false,function(msg){
        callback(msg);
    })
   
}

exports.sendEmailArticleFeedback = function (email, subject, content, callback) {
    const mailOptions = {
        from: 'SearchUnify<<EMAIL>>', // sender address
        //  to: email, // list of receivers
        to : email, // list of receivers
        subject: subject, // Subject line
        html: content
    };
    transportMail(mailOptions,true,function(msg){
        callback(msg);
    })
   
}
exports.sendEmailWithAttachments = function(email, subject, content,attachments,callback){
    const mailOptions = {
        from: 'SearchUnify<<EMAIL>>', // sender address
        to: email, // list of receivers
        subject: subject, // Subject line
        html: content,
        attachments:attachments
    };
    transportMail(mailOptions, false, function(msg){
        callback(msg);
    })
}

// optional attachment
exports.sendEmailWithCcWithAttachments = function(email, subject, content, callback){
    const mailOptions = {
        from: 'SearchUnify<<EMAIL>>', // sender address
        to: email.to, // list of receivers
        cc: email.cc,
        subject: subject, // Subject line
        html: content
    };
    if(Object.keys(email.attachments).length) {
        mailOptions['attachments'] = email.attachments;
    }
    transportMail(mailOptions,false , function(msg){
        callback(msg);
    })
}


function transportMail(mailOptions,forArticleFeedback , cb){
    console.log(constants.transporterWebMail.user);
    // if(forArticleFeedback){
    //     var authorization ={
    //         user: constants.gmailForArticleFeedback.userName,
    //         pass: constants.gmailForArticleFeedback.password
    //        }
    // }else{
        var authorization ={
            user: constants.gmail.userName,
            pass: constants.gmail.password
           }
    // }
    var transporterWebMail = require('nodemailer').createTransport({
        service: 'gmail',
        auth: authorization
       });

    transporterWebMail.sendMail(mailOptions, function (error, info) {
        if (error) {
            console.log(error);
            cb(null)
        }
        else {
            console.log('Message sent: ' + info.response);
            cb(info.response);
        }
    });
}