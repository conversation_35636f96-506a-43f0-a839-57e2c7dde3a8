const config = require('config');
const axios = require('axios');
const setCookie = require('set-cookie-parser');
const urlAuth = config.get('authUrl');

const socketAuth = async (socket, next) => {
  try {
    let csrfToken;
    const splitCookie = setCookie.splitCookiesString(socket.handshake && socket.handshake.headers.cookie);
    const cookies = setCookie.parse(splitCookie);
    cookies.forEach((item) => {
      if (item._csrf) {
        csrfToken = item._csrf;
      } 
    });

    if (csrfToken) {
      const options = {
        method: 'GET',
        url: `${urlAuth}/verification/getTenant`,
        headers: { 
          'content-type': 'application/json',
          Cookie: socket.handshake.headers.cookie,
          'CSRF-Token': csrfToken
        },
        timeout: 10000
      };
      await axios(options);
      next();
    } else {
      next('failed connection');
    }
  } catch (error) {
    next(error);
  }
};
module.exports = {
  socketAuth
};
