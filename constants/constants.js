const reverseObject = (obj) => {
  try{
    let rev = {};
    if(obj == null || !obj instanceof Object){
      return undefined;
    }
    for (const [key, value] of Object.entries(obj)) {
      rev[value] = key;
    }

    return rev;
  }catch(e){
    console.log(`ERROR while reversing obj ${JSON.stringify(e)}`);
  }
};
/**
 * The node-module to hold the constants for the server
 */


function define(obj, name, value) {
    Object.defineProperty(obj, name, {
        value:        value,
        enumerable:   true,
        writable:     false,
        configurable: false
    });
}

define(exports, "CASH_DISABLE_TIME",    3);// in min

define(exports, "NUM_DRIVER_REQUESTS_BATCH",    5);
define(exports, "NUM_ADHOC_FIRST_BATCH",        5);
define(exports, "NUM_DEDICATED_FIRST_BATCH",    0);
define(exports, "NUM_DEDICATED_REQUESTS_BATCH", 2);
define(exports, "PICKUP_RADIUS",                2500);               // in meters
define(exports, "PICKUP_RADIUS_MANUAL",         12000);               // in meters
define(exports, "DEDICATED_PICKUP_RADIUS",      12000);               // in meters
define(exports, "NUM_TRIES_ALLOCATE_DRIVER",    25);
define(exports, "TRIES_TIMEOUT",                60000);              // in milliseconds
define(exports, "SCHEDULER_ALARM_TIME",         30);                 // in minutes
define(exports, "MANUAL_NUM_TRIES",             2);
define(exports, "PROXIMITY_RADIUS",             12000);
define(exports, "EMPTY_ACCURACY",              -999);
define(exports, "SERVICE_QUALITY_WINDOW",       60);                // in minutes
define(exports, "SCHEDULE_CANCEL_WINDOW",       40);                // in minutes
define(exports, "SCHEDULING_LIMIT",             60);                // in minutes
define(exports, "SCHEDULE_NOTIFICATION_TIME",   60);                // in minutes
define(exports, "SCHEDULE_PROCESS_INTERVAL",    4);                 // in minutes

define(exports, "GCM_INTENT_FLAG",              1);
define(exports, "NUKKAD_ENABLE_FLAG",           0);
define(exports, "NUKKAD_BUTTON_ICON",           '');            // icon URL
define(exports, "NUKKAD_BUTTON_ICON_IOS",       '');            // icon URL

exports.jiveContentType=["Document","Discussion"];

// enums
exports.batchType = {};
define(exports.batchType, "NEAREST",          0);   // Drivers in 400 meters
define(exports.batchType, "ADHOC",            1);   // Drivers in 2500 meters
define(exports.batchType, "DEDICATED",        2);   // Drivers in 12000 meters
define(exports.batchType, "MANUAL",           3);   // Drivers in 12000 meters manually

exports.userCurrentStatus = {};
define(exports.userCurrentStatus, "OFFLINE",            0);
define(exports.userCurrentStatus, "DRIVER_ONLINE",      1);
define(exports.userCurrentStatus, "CUSTOMER_ONLINE",    2);

exports.userFreeStatus = {};
define(exports.userFreeStatus, "FREE",    0);
define(exports.userFreeStatus, "BUSY",    1);

exports.userVerificationStatus = {};
define(exports.userVerificationStatus, "NotVerified",    0);
define(exports.userVerificationStatus, "Verified",       1);
define(exports.userVerificationStatus, "Blocked",        1);

exports.userRegistrationStatus = {};
define(exports.userRegistrationStatus, "CUSTOMER",          0);
define(exports.userRegistrationStatus, "DRIVER",            1);

exports.engagementStatus = {};
define(exports.engagementStatus, "REQUESTED",                   0 );    // request has been sent
define(exports.engagementStatus, "ACCEPTED",                    1 );    // request has been accepted by the driver
define(exports.engagementStatus, "STARTED",                     2 );    // ride has started
define(exports.engagementStatus, "ENDED",                       3 );    // ride has ended
define(exports.engagementStatus, "REJECTED_BY_DRIVER",          4 );    // request rejected by driver
define(exports.engagementStatus, "CANCELLED_BY_CUSTOMER",       5 );    // request cancelled by customer
define(exports.engagementStatus, "TIMEOUT",                     6 );    // request timed out
define(exports.engagementStatus, "ACCEPTED_BY_OTHER_DRIVER",    7 );    // request was accepted by another driver
define(exports.engagementStatus, "ACCEPTED_THEN_REJECTED",      8 );    // request was accepted and then rejected
define(exports.engagementStatus, "CLOSED",                      9 );    // request was closed when the driver accepted other request
define(exports.engagementStatus, "CANCELLED_ACCEPTED_REQUEST",  10);    // request was cancelled after it was accepted by a driver

exports.deviceType = {};
define(exports.deviceType, "ANDROID",   0);
define(exports.deviceType, "iOS",       1);

exports.sessionStatus = {};
define(exports.sessionStatus, "INACTIVE",   0);
define(exports.sessionStatus, "ACTIVE",     1);
define(exports.sessionStatus, "TIMED_OUT",  2);

exports.notificationFlags = {};
define(exports.notificationFlags, "REQUEST",                        0 );  //driver
define(exports.notificationFlags, "REQUEST_TIMEOUT",                1 );  //driver
define(exports.notificationFlags, "REQUEST_CANCELLED",              2 );  //driver
define(exports.notificationFlags, "RIDE_STARTED",                   3 );  //customer
define(exports.notificationFlags, "RIDE_ENDED",                     4 );  //customer
define(exports.notificationFlags, "RIDE_ACCEPTED",                  5 );  //customer
define(exports.notificationFlags, "RIDE_ACCEPTED_BY_OTHER_DRIVER",  6 );  //driver
define(exports.notificationFlags, "RIDE_REJECTED_BY_DRIVER",        7 );  //customer
define(exports.notificationFlags, "NO_DRIVERS_AVAILABLE",           8 );  //customer
define(exports.notificationFlags, "WAITING_STARTED",                9 );  //customer
define(exports.notificationFlags, "WAITING_ENDED",                  10);  //customer
define(exports.notificationFlags, "DRIVER_ARRIVED",                 1001 );  //customer
define(exports.notificationFlags, "RIDE_REJECTED_BY_CUSTOMER",       2 );  //customer

define(exports.notificationFlags, "SPLIT_FARE_REQUEST",             11);
define(exports.notificationFlags, "HIDE_CASH_PAYMENT",              12);
define(exports.notificationFlags, "CHANGE_STATE",                   20);
define(exports.notificationFlags, "DISPLAY_MESSAGE",                21);
define(exports.notificationFlags, "TOGGLE_LOCATION_UPDATES",        22);
define(exports.notificationFlags, "MANUAL_ENGAGEMENT",              23);
define(exports.notificationFlags, "HEARTBEAT",                      40);
define(exports.notificationFlags, "STATION_CHANGED",                50);
define(exports.notificationFlags, "PAYMENT_INFO_TO_DRIVER",         51);
define(exports.notificationFlags, "PAYMENT_INFO_TO_CUSTOMER",       52);

exports.responseFlags = {};
define(exports.responseFlags, "PARAMETER_MISSING",              100);
define(exports.responseFlags, "INVALID_ACCESS_TOKEN",           101);
define(exports.responseFlags, "ERROR_IN_EXECUTION",             102);
define(exports.responseFlags, "SHOW_ERROR_MESSAGE",             103);
define(exports.responseFlags, "SHOW_MESSAGE",                   104);
define(exports.responseFlags, "ASSIGNING_DRIVERS",              105);
define(exports.responseFlags, "NO_EMAIL_AVAILABLE",             106);
define(exports.responseFlags, "RIDE_ACCEPTED",                  107);        // Duplicate payload in case of data loss
define(exports.responseFlags, "RIDE_ACCEPTED_BY_OTHER_DRIVER",  108);
define(exports.responseFlags, "RIDE_CANCELLED_BY_DRIVER",       109);
define(exports.responseFlags, "REQUEST_REJECTED",               110);
define(exports.responseFlags, "REQUEST_TIMEOUT",                111);
define(exports.responseFlags, "REQUEST_CANCELLED",              112);
define(exports.responseFlags, "SESSION_TIMEOUT",                113);
define(exports.responseFlags, "RIDE_STARTED",                   114);
define(exports.responseFlags, "RIDE_ENDED",                     115);
define(exports.responseFlags, "WAITING",                        116);
define(exports.responseFlags, "USER_OFFLINE",                   130);
define(exports.responseFlags, "NO_ACTIVE_SESSION",              131);
define(exports.responseFlags, "ENGAGEMENT_DATA",                132);
define(exports.responseFlags, "ACTIVE_REQUESTS",                133);
define(exports.responseFlags, "COUPONS",                        140);
define(exports.responseFlags, "SCHEDULED_PICKUPS",              141);
define(exports.responseFlags, "ACK_RECEIVED",                   142);
define(exports.responseFlags, "ACTION_COMPLETE",                143);
define(exports.responseFlags, "ACTION_FAILED",                  144);
define(exports.responseFlags, "LOGIN_SUCCESSFUL",               150);
define(exports.responseFlags, "INCORRECT_USERNAME_PASSWORD",    151);
define(exports.responseFlags, "CUSTOMER_LOGGING_IN",            152);
define(exports.responseFlags, "LOGOUT_SUCCESSFUL",              153);
define(exports.responseFlags, "LOGOUT_FAILURE",                 154);
define(exports.responseFlags, "NO_SUCH_USER",                   155);
define(exports.responseFlags, "STATION_ASSIGNED",               170);
define(exports.responseFlags, "NO_STATION_ASSIGNED",            171);
define(exports.responseFlags, "NO_STATION_AVAILABLE",           172);
define(exports.responseFlags, "DRIVER_LOGGING_IN",              173);       // When driver tries to login to customer app
define(exports.responseFlags, "DUPLICATE_CARD",                 174);
define(exports.responseFlags, "DRIVER_END_RIDE_SCREEN",         175);
define(exports.responseFlags, "CORP_ACCOUNT_ADDED",             176);
define(exports.responseFlags, "DRIVER_DETAILS_ADDED",           400);
define(exports.responseFlags, "ADMIN_APPROVED",                 401);
define(exports.responseFlags, "PROMO_CODE_ADDED",               450);
define(exports.responseFlags, "OK",                             200);
define(exports.responseFlags, "SMS_SENT_SOS_PHONE",             201);
define(exports.responseFlags, "REGISTRATION_DONE",              202);
define(exports.responseFlags, "INVALID_DATE",                   301);


exports.rideAcceptanceFlag = {};
define(exports.rideAcceptanceFlag, "NOT_YET_ACCEPTED",          0);
define(exports.rideAcceptanceFlag, "ACCEPTED",                  1);
define(exports.rideAcceptanceFlag, "ACCEPTED_THEN_REJECTED",    2);

exports.couponType = {};
define(exports.couponType, "FREE_RIDE",      1);

exports.couponStatus = {};
define(exports.couponStatus, "EXPIRED",      0);
define(exports.couponStatus, "ACTIVE",       1);
define(exports.couponStatus, "REDEEMED",     2);

exports.promoType = {};
define(exports.promoType, "REGISTRATION",    0);
define(exports.promoType, "IN_APP",          1);
define(exports.promoType, "ALL_TIME",        2);

exports.infoSection = {};
define(exports.infoSection, "ABOUT",         0);
define(exports.infoSection, "FAQs",          1);
define(exports.infoSection, "PRIVACY",       2);
define(exports.infoSection, "TERMS",         3);
define(exports.infoSection, "FARE",          4);
define(exports.infoSection, "SCHEDULES_TNC", 5);

exports.blockingReasons = {};
define(exports.blockingReasons, "SUSPECTED_NUMBER",     0);
define(exports.blockingReasons, "INVALID_EMAIL",        1);
define(exports.blockingReasons, "TERMS_VIOLATION",      2);

define(exports, "SCHEDULE_CURRENT_TIME_DIFF",  60);     // in minutes   //60 earlier
define(exports, "SCHEDULE_DAYS_LIMIT",          1);     // in days
define(exports, "SCHEDULE_CANCEL_LIMIT",       60);     // in minutes    // 60 earlier
define(exports, "REFFERAL_DAYS_LIMIT",         15);     // in days   // 15 earlier

exports.scheduleStatus = {};
define(exports.scheduleStatus, "IN_QUEUE",    0);      // the ride will be scheduled at the appropriate time
define(exports.scheduleStatus, "IN_PROCESS",  1);      // the schedule has been picked up for processing
define(exports.scheduleStatus, "PROCESSED",   2);      // the schedule has already been processed, and is in past the current time
define(exports.scheduleStatus, "COULD_NOT_PROCESS",   3);      // the schedule has already been processed, and is in past the current time


exports.FILE_TYPE = {};
define(exports.FILE_TYPE, "RIDE_PATH",         0);      // the ride will be scheduled at the appropriate time

exports.splitFareRequestStatus = {};
define(exports.splitFareRequestStatus, "SPLITFARE_REQUEST_SENDING_SUCCESSFUL",              0);
define(exports.splitFareRequestStatus, "SPLITFARE_REQUEST_ACCEPTED",                        1); // Accepted and in ride will be same
define(exports.splitFareRequestStatus, "SPLITFARE_REQUEST_REJECTED",                        2);
define(exports.splitFareRequestStatus, "SPLITFARE_REQUEST_TIMEOUT",                         3);


exports.UserResponseToSplitFareRequest = {};
define(exports.UserResponseToSplitFareRequest, "ACCEPTED",              0);
define(exports.UserResponseToSplitFareRequest, "REJECTED",              1);
define(exports.UserResponseToSplitFareRequest, "TIMEOUT",               2);


exports.SplitFareRequestDisplayMessage = {};
define(exports.SplitFareRequestDisplayMessage, "ACCEPTED",              'You have accepted the split fare request successfully');
define(exports.SplitFareRequestDisplayMessage, "REJECTED",              'You have rejected the split fare request successfully');
define(exports.SplitFareRequestDisplayMessage, "TIMEOUT",               'Request timeout');
define(exports.SplitFareRequestDisplayMessage, "RIDEALREADYENDED",      'Ride has been already completed');
define(exports.SplitFareRequestDisplayMessage, "REQUESTALREADYACCEPTED",'Request has been already accepted');


exports.splitFareRequestResponse = {};
define(exports.splitFareRequestResponse, "SPLITFARE_REQUEST_SENT",            'Request already sent');
define(exports.splitFareRequestResponse, "USER_NOT_REGISTERED",               'No customer registered with this phone number');
define(exports.splitFareRequestResponse, "USER_NOT_LOGGED_IN",                'User not logged in');
define(exports.splitFareRequestResponse, "BLOCKED_USER",                      'User has been blocked');
define(exports.splitFareRequestResponse, "USER_ALREADY_IN_RIDE",              'User is in ride');
define(exports.splitFareRequestResponse, "SPLITFARE_REQUEST_SENDING_SUCCESSFUL",'Request sent successfully');
define(exports.splitFareRequestResponse, "SENDING_REQUEST_TO_OWN",              'You cannot send request to yourself');


exports.splitFareRequestTimer = {};
define(exports.splitFareRequestTimer, "TIMEINSECONDS",              60);

exports.PaymentStatus = {};
define(exports.PaymentStatus, "UNSUCCESSFUL",      0);
define(exports.PaymentStatus, "SUCCESSFUL",        1);

exports.PaymentMethod = {};
define(exports.PaymentMethod, "CASH",      0);
define(exports.PaymentMethod, "CARD",      1);



exports.userBlockingStatus = {};
define(exports.userBlockingStatus, "BLOCKED",      1);

//chat constants

define(exports, "BY_DRIVER", 1);
define(exports, "BY_ADMIN", 2);

define(exports,"SUCCESS", 0);
define(exports, "CHAT_MSG_NOT_SENT", 30);
define(exports, "SOMETHING_WRONG", 7);
define(exports, "NEW_MESSAGE_RECEIVED", 14);

exports.CONNECTION_FAILURE = 500;

exports.CONTENT_SOURCE_TYPE = {
  jive               : 1,
  lithium            : 2,
  salesforce         : 3,
  confluence         : 4,
  sharepoint         : 5,
  jira               : 6,
  zendesk            : 7,
  slack              : 8,
  website            : 9,
  madcap             : 10,
  mindtouch          : 11,
  drive              : 12,
  box                : 13,
  helpScout          : 14,
  github             : 15,
  sap                : 16,
  youtube            : 17,
  stackOverflow      : 18,
  amazonS3           : 19,
  litmos             : 20,
  solr               : 21,
  customContentSource: 22,
  moodle             : 23,
  dropbox            : 24,
  sabaCloud          : 25,
  servicenow         : 26,
  jiraOnprem         : 27,
  marketo            : 28,
  dynamics           : 29,
  receptive          : 30,
  zoomin             : 31,
  docebo             : 32,
  vimeo              : 33,
  azureDevops        : 34,
  seismic            : 37,
  cornerstone        : 38,
  skillJar           : 39,
  higherLogic        : 41,
  aha                : 42,
  discourse          : 43,
  thoughtIndustries  : 44,
  rssFeed            : 45,
  microsoftTeams     : 46,
  jsWeb              : 47,
  vidyard            : 48,
  contentful         : 49,
  wistia             : 50,
  getguru            : 52,
  knowledgeOwl       : 53,
  document360	       : 51,
  insided            : 55,
  learnupon          : 58,
  vanilla            : 66,
  monday             : 60,
  aem                : 61,
  brightspace        : 67,
  rightAnswers       : 70,
  khorosAurora       : 71,
  freshDesk          : 72,
  freshService       : 73,
  uservoice          : 56,
  file               : 69,
  heretto            : 59,
  bugzilla           : 68,
  gmail              : 81,
  simpplr            : 82,
  airtable           : 75,
  gitlab             : 84,
  zulip              : 86,
  absorbLms          : 76,
  workday            : 87
};
exports.CONTENT_SOURCE_TYPE_NAME = reverseObject(module.exports.CONTENT_SOURCE_TYPE);

exports.FREQ_DAY_OF_WEEK_MAPPING = {
  "Sunday"              : 0,
  "Monday"              : 1,
  "Tuesday"             : 2,
  "Wednesday"           : 3,
  "Thursday"            : 4,
  "Friday"              : 5,
  "Saturday"            : 6,
};

exports.CONTENT_SOURCES_WITH_UNIQUE_FIELD_NAMES = [
  this.CONTENT_SOURCE_TYPE.salesforce,
  this.CONTENT_SOURCE_TYPE.azureDevops,
  this.CONTENT_SOURCE_TYPE.higherLogic,
  this.CONTENT_SOURCE_TYPE.cornerstone,
  this.CONTENT_SOURCE_TYPE.zendesk,
  this.CONTENT_SOURCE_TYPE.aha,
  this.CONTENT_SOURCE_TYPE.discourse,
  this.CONTENT_SOURCE_TYPE.helpScout,
  this.CONTENT_SOURCE_TYPE.thoughtIndustries,
  this.CONTENT_SOURCE_TYPE.rssFeed,
  this.CONTENT_SOURCE_TYPE.seismic,
  this.CONTENT_SOURCE_TYPE.microsoftTeams,
  this.CONTENT_SOURCE_TYPE.jsWeb,
  this.CONTENT_SOURCE_TYPE.stackOverflow,
  this.CONTENT_SOURCE_TYPE.github,
  this.CONTENT_SOURCE_TYPE.youtube,
  this.CONTENT_SOURCE_TYPE.jira,
  this.CONTENT_SOURCE_TYPE.confluence,
  this.CONTENT_SOURCE_TYPE.slack,
  this.CONTENT_SOURCE_TYPE.customContentSource,
  this.CONTENT_SOURCE_TYPE.zendesk,
  this.CONTENT_SOURCE_TYPE.vimeo,
  this.CONTENT_SOURCE_TYPE.servicenow,
  this.CONTENT_SOURCE_TYPE.vidyard,
  this.CONTENT_SOURCE_TYPE.contentful,
  this.CONTENT_SOURCE_TYPE.wistia,
  this.CONTENT_SOURCE_TYPE.lithium,
  this.CONTENT_SOURCE_TYPE.box,
  this.CONTENT_SOURCE_TYPE.drive,
  this.CONTENT_SOURCE_TYPE.mindtouch,
  this.CONTENT_SOURCE_TYPE.dropbox,
  this.CONTENT_SOURCE_TYPE.getguru,
  this.CONTENT_SOURCE_TYPE.monday,
  this.CONTENT_SOURCE_TYPE.knowledgeOwl,
  this.CONTENT_SOURCE_TYPE.sharepoint,
  this.CONTENT_SOURCE_TYPE.marketo,
  this.CONTENT_SOURCE_TYPE.moodle,
  this.CONTENT_SOURCE_TYPE.docebo,
  this.CONTENT_SOURCE_TYPE.document360,
  this.CONTENT_SOURCE_TYPE.litmos,
  this.CONTENT_SOURCE_TYPE.vanilla,
  this.CONTENT_SOURCE_TYPE.insided,
  this.CONTENT_SOURCE_TYPE.skillJar,
  this.CONTENT_SOURCE_TYPE.brightspace,
  this.CONTENT_SOURCE_TYPE.aem,
  this.CONTENT_SOURCE_TYPE.rightAnswers,
  this.CONTENT_SOURCE_TYPE.khorosAurora,
  this.CONTENT_SOURCE_TYPE.uservoice,
  this.CONTENT_SOURCE_TYPE.file,
  this.CONTENT_SOURCE_TYPE.heretto,
  this.CONTENT_SOURCE_TYPE.bugzilla,
  this.CONTENT_SOURCE_TYPE.learnupon,
  this.CONTENT_SOURCE_TYPE.airtable,
  this.CONTENT_SOURCE_TYPE.freshDesk,
  this.CONTENT_SOURCE_TYPE.freshService
];

exports.TEMP_MAPPING_CS = [
  this.CONTENT_SOURCE_TYPE.higherLogic,
  this.CONTENT_SOURCE_TYPE.dropbox,
  this.CONTENT_SOURCE_TYPE.zoomin,
  this.CONTENT_SOURCE_TYPE.vidyard,
  this.CONTENT_SOURCE_TYPE.jsWeb
];
exports.CS_AUTH_COMPONENT_FIELDS= [
  'content_source_type_id',
  'hasPlaces',
  'hasClientCreds',
  'hasTenant',
  'hasClientUrl',
  'clientUrl',
  'spaceLabel',
  'allowedAuths',
  'defaultAuth',
  'additionalFields',
  'dropDownLabel',
  'dropDownOptions',
  'dropDownKey',
];

exports.TEMP_MAPPING_EXCLUSIONS = [
  this.CONTENT_SOURCE_TYPE.website,
  this.CONTENT_SOURCE_TYPE.sabaCloud
];

exports.OBJECT_CRAWL = [
  this.CONTENT_SOURCE_TYPE.salesforce,
  this.CONTENT_SOURCE_TYPE.contentful,
  this.CONTENT_SOURCE_TYPE.zendesk
];

exports.USER_ROLES = {
  ADMIN: 1,
  MODERATOR: 2,
  API_USER: 3,
  SUPER_ADMIN: 4,
};

exports.jsErrors = [
  'SyntaxError',
  'ReferenceError',
  'TypeError',
  'EvalError',
  'RangeError',
  'URIError',
  'InternalError'
];
exports.BUILD_INFO_FILE_PATH = 'build-info';
exports.BUILD_INFO_FILE_PATH_FRONTEND = 'dist/build-info-frontend';

exports.IDP_TYPES = {
  ADMIN_SSO: 0,
  HOSTED_SSO: 1
}

exports.instanceTypes = {
  production: 'production',
  sandbox: 'sandbox',
  development: 'development'
}
exports.PROTECTED_AUTH_KEYS = ['accessToken', 'refreshToken', 'privateKey', 'htaccessPassword', 'sessionId', 'instanceURL', 'client_secret', 'password'];

exports.CS_MERGE_STATUS = {
  inProgress: 1,
  complete: 2,
  fail: 3,
  mongoComplete: 4
};

exports.SEARCH_TYPE = {
  HYBRID: 3,
  VECTOR: 2,
  KEYWORD: 1,
};

exports.SUPPORTED_GPT_API_TYPES = {
  "SEARCH_GPT":"SEARCH_GPT",
  "GPT":"GPT"
}

exports.DEFAULT_CONSUMPTION_LIMTS = {
    "usages": {
      "searchApiLimit": {
        "minute": 60,
        "hourly": 3500,
        "monthly": 300000
      },
      "contentApiLimit": {
        "minute": 60,
        "hourly": 2500,
        "monthly": 150000
      },
      "analyticsApiLimit": {
        "minute": 50,
        "hourly": 2500,
        "monthly": 150000
      },
      "chatbotApiLimit": {
        "minute": 0,
        "hourly": 0,
        "monthly": 0
      },
      "gptApiLimit": {
        "minute": 60,
        "hourly": 3500,
        "monthly": 100000
      },
      "adminLicenseLimit": 10,
      "kcsLicenseLimit": 5,
      "chatbotLicenseLimit": 5,
      "contentSourceLimit": 30,
      "indexSizeLimit": 40,
      "documentsLimit": 4000000,
      "searchClientsLimit": 10,
      "searchesLimit": 300000,
      "apiUsagePerSecond": 5
    }
}

exports.CS_WITH_MAPPING_FOR_DB_AND_ES = [module.exports.CONTENT_SOURCE_TYPE.lithium, module.exports.CONTENT_SOURCE_TYPE.zoomin, module.exports.CONTENT_SOURCE_TYPE.khorosAurora];
exports.TUNING_LABELS = {
  UNSAVED_VS_LIVE: 'Tuned vs Live Search',
  UNSAVED_VS_BASIC: 'Tuned vs Vanilla Search',
  LIVE_VS_BASIC: 'Live vs Vanilla Search',
  LIVE_SEARCH: 'Live Search',
  BASIC_SEARCH: 'Vanilla Search',
  UNSAVED_SEARCH: 'Tuned Search',
  UNSAVED_SEARCH_TOOLTIP: 'This search contains tuning changes',
  LIVE_SEARCH_TOOLTIP: 'Current live search results',
  TEMP_LIVE_SCORE_MSG: 'Rank in Live Search',
  TEMP_BASIC_SCORE_MSG: 'Rank in Vanilla Search',
  TEMP_BASIC_SEARCH_TOOLTIP: 'Search Result without any boosting'
}

exports.IP_REGEX = /^(\d{1,3}\.){3}\d{1,3}$/;
exports.IS_PRIVATE_IP = /(?:10|127|172\.(?:1[6-9]|2[0-9]|3[01])|192\.168)\./;

exports.YOUTUBE_REQ_SCOPES = {
  readAccess: 'https://www.googleapis.com/auth/youtube.readonly',
  fullAccess: 'https://www.googleapis.com/auth/youtube.force-ssl'
};

