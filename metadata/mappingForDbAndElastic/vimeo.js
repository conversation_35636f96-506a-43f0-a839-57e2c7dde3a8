

module.exports = {
    "objects": [{
        "name": "vimeovideos",
        "label": "Vimeo Content",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [{
        "name": "id",
        "label": "id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "title",
        "label": "Title",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "post_time",
        "label": "Created Date",
        "type": "datetime",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 0,
        "isActive": 1,
        "isMerged": 1,
        "isMasked": 0
    },
    {
        "name": "body",
        "label": "Description",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "subtitle",
        "label": "Sub Title",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "view_href",
        "label": "View Href",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1,
        "isMasked": 0
    },
    {
        "name": "tags",
        "label": "Tags",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "channel_id",
        "label": "Channel Id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "channel_name",
        "label": "Channel Name",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "user_id",
        "label": "User Id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "comments",
        "label": "comments",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "thumbnail",
        "label": "Thumbnail",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1,
        "isMerged": 1
    },
    {
        "name": "channel_type",
        "label": "Channel Type",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "privacy_status",
        "label": "Privacy Status",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1,
        "isMerged": 1
    },
    {
            "name": 'data_category_nested',
            "label": 'Nested Folder',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1,
            "isMerged": 1,
            "isMasked": 0
          
    }, {
        "name": "subtitle__body__s",
        "label": "Subtitle Body",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    }]
}
