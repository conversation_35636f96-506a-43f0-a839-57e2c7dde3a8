const constants = require('./../../constants/constants');

const loadMappingForDbAndElastic = ( requiredMapping, tenantId, cb ) => {
  let mappingObject;

  switch(Number(requiredMapping)) {
    case constants.CONTENT_SOURCE_TYPE.confluence: //4
      mappingObject = require('./confluenceObjectFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.lithium: //2
      mappingObject = require('./lithiumObjectFields').initLithiumConfig(tenantId, cb);
      break;

    case constants.CONTENT_SOURCE_TYPE.zendesk://"7":
      mappingObject = require('./zendeskObjectFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.sharepoint://"5"
      mappingObject = require('./sharepointObjectFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.jive:// "1"
      mappingObject = require('./jiveObjectFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.drive: //"12"
      mappingObject = require('./drive');
      break;

    case constants.CONTENT_SOURCE_TYPE.helpScout: //"14"
      mappingObject = require('./helpScout');
      break;

    case constants.CONTENT_SOURCE_TYPE.box: //"13"
      mappingObject = require('./box');
      break;

    case constants.CONTENT_SOURCE_TYPE.github: //"15"
      mappingObject = require('./githubObjectFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.sap: //"16"
      mappingObject = require('./sap');
      break;

    case constants.CONTENT_SOURCE_TYPE.youtube: //"17"
      mappingObject = require('./youtube');
      break;

    case constants.CONTENT_SOURCE_TYPE.stackOverflow: //"18"
      mappingObject = require('./stackoverflow');
      break;

    case constants.CONTENT_SOURCE_TYPE.jira: //"6"
      mappingObject = require('./jiraObjectFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.slack://"8"
      mappingObject = require('./slack');
      break;

    case constants.CONTENT_SOURCE_TYPE.litmos: //"20"
      mappingObject = require('./litmosObjectFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.moodle: //"23"
      mappingObject = require('./moodle');
      break;

    case constants.CONTENT_SOURCE_TYPE.dropbox: //"24"
      mappingObject = require('./dropbox');
      break;

    case constants.CONTENT_SOURCE_TYPE.sabaCloud: //"25"
      mappingObject = require('./sabaCloud');
      break;

    case constants.CONTENT_SOURCE_TYPE.servicenow: //"26"
      mappingObject = require('./servicenowMapping');
      break;

    case constants.CONTENT_SOURCE_TYPE.marketo: //"28"
      mappingObject = require('./marketo');
      break;

    case constants.CONTENT_SOURCE_TYPE.mindtouch: //"11"
      mappingObject = require('./mindtouch');
      break;

    case constants.CONTENT_SOURCE_TYPE.dynamics: //"29"
      mappingObject = require('./dynamicsObjectAndFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.receptive: //"30"
      mappingObject = require('./receptiveObjectFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.docebo: //"32"
      mappingObject = require('./doceboObjectFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.vimeo: //"33"
      mappingObject = require('./vimeo');
      break;

    case constants.CONTENT_SOURCE_TYPE.zoomin: //"31"
      mappingObject = require('./zoomin').initZoominConfig(tenantId, cb);
      break;

    case constants.CONTENT_SOURCE_TYPE.skillJar: //"39"
      mappingObject = require('./skilljarObjectsAndFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.higherLogic: //"41"
      mappingObject = require('./higherLogic');
      break;

    case constants.CONTENT_SOURCE_TYPE.azureDevops: //"34"
      mappingObject = require('./azure-devops');
      break;

    case constants.CONTENT_SOURCE_TYPE.cornerstone: //"38"
      mappingObject = require('./cornerstone');
      break;

    case constants.CONTENT_SOURCE_TYPE.jiraOnprem: //"27"
      mappingObject = require('./jiraonprem.json')
      mappingObject = { objects : mappingObject.objects, fields: mappingObject.fields };
      break;

    case constants.CONTENT_SOURCE_TYPE.aha: //"42"
      mappingObject = require('./ahaObjectFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.discourse: //"43"
      mappingObject = require('./discourse');
      break;
  
    case constants.CONTENT_SOURCE_TYPE.thoughtIndustries: //"44"
      mappingObject = require('./thought-industries');
      break;

    case constants.CONTENT_SOURCE_TYPE.rssFeed: //"45"
      mappingObject = require('./rssFeedObjects');
      break;
      
    case constants.CONTENT_SOURCE_TYPE.seismic: //37
      mappingObject = require('./seismic');
      break;

    case constants.CONTENT_SOURCE_TYPE.microsoftTeams: //46
      mappingObject = require('./microsoftTeamsObjectFields');
      break;
    case constants.CONTENT_SOURCE_TYPE.getguru: //52
      mappingObject = require('./getGuru');
      break;

    case constants.CONTENT_SOURCE_TYPE.vidyard: //"48"
      mappingObject = require('./vidyard');
      break;

    case constants.CONTENT_SOURCE_TYPE.wistia: // 50
      mappingObject = require('./wistiaObjectFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.knowledgeOwl: //53
      mappingObject = require('./knowledgeOwl');
      break;
    
    case constants.CONTENT_SOURCE_TYPE.monday: //60
      mappingObject = require('./mondaymapping');
      break;

    case constants.CONTENT_SOURCE_TYPE.document360: //51
      mappingObject = require('./document360ObjectsFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.vanilla:
      mappingObject=require('./vanilla');  
      break;
    
    case constants.CONTENT_SOURCE_TYPE.insided: //55
      mappingObject = require('./insidedObjectsFields');
      break;
    
    case constants.CONTENT_SOURCE_TYPE.rightAnswers: //70
      const mappingTempObjects = require('./rightAnswers');
      mappingObject = mappingTempObjects.metaData;
      
    case constants.CONTENT_SOURCE_TYPE.uservoice: //56
      mappingObject = require('./uservoiceObjectAndFields');
      break;
      
    case constants.CONTENT_SOURCE_TYPE.uservoice: //56
      mappingObject = require('./uservoiceObjectAndFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.brightspace: //67
      mappingObject = require('./brightspaceObjectsAndFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.aem: //61
      mappingObject = require('./aem');
      break;
      
      case constants.CONTENT_SOURCE_TYPE.bugzilla: //68
      mappingObject = require('./bugzilla');
      break;

    case constants.CONTENT_SOURCE_TYPE.khorosAurora: //71
    mappingObject = require('./khorosAuroraObjectFields').initKhorosAuroraConfig(tenantId, cb);;
    break;

    case constants.CONTENT_SOURCE_TYPE.freshDesk: //72
      mappingObject = require('./freshDesk');
      break;

    case constants.CONTENT_SOURCE_TYPE.freshService: //73
      mappingObject = require('./freshService');
      break;

    
    case constants.CONTENT_SOURCE_TYPE.heretto: //59
      mappingObject = require('./herettoObjectAndFields');
      break;
      
    case constants.CONTENT_SOURCE_TYPE.gitlab: //84
      mappingObject = require('./gitlabObjectAndFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.gmail: //81
      mappingObject = require('./gmail');
      break;
      
    case constants.CONTENT_SOURCE_TYPE.simpplr: //82
      mappingObject = require('./simpplr');
      break;

    case constants.CONTENT_SOURCE_TYPE.airtable: //75
      mappingObject = require('./airtableObjects');

      break;
    
    case constants.CONTENT_SOURCE_TYPE.absorbLms: //76
      mappingObject = require('./absorbLmsFields');
      break;

    case constants.CONTENT_SOURCE_TYPE.zulip: //86
      mappingObject = require('./zulip');
      break;

    case constants.CONTENT_SOURCE_TYPE.workday: //87
      mappingObject = require('./workday');
      break;

    default:
      mappingObject = [];
      break;
  }

  if(mappingObject !== undefined) {
    return mappingObject;
  }

}

module.exports = {
  loadMappingForDbAndElastic : loadMappingForDbAndElastic,
  elasticSetting: require('./elasticSettings'),
  confluenceMapping: require('./confluenceElasticMapping'),
  intractiveSearchQuery: require('./intractiveSearch'),
  oauthLogs: require('./oauthLogs'),
  // kcsSupport:require('./kcsSupportMapping'),
};
