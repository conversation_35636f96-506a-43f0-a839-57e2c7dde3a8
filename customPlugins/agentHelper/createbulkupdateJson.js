const csv=require('csvtojson');
const fs = require("fs");
let out = fs.createWriteStream('bulkUpdate.json.ignore');
fs.createReadStream('clustered_cases.csv')
    .pipe(
        csv({
            noheader:true, 
            output:"csv"
        })
        .subscribe(row=>{
            row[0] = row[0].split("/").map(e=>decodeURIComponent(e));
            out.write(JSON.stringify({ "update" : {"_index" : row[0][0], "_type" : row[0][1], "_id" : row[0][2]} })+"\n");
            out.write(JSON.stringify({ "doc" : {"cluster":row[1]}}) +"\n");
        })
    );

