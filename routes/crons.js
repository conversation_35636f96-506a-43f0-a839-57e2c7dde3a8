const express = require('express');
const router = express.Router();
const async = require('async');
const request = require("request");
const url = require('url');
const commonFunctions = require('../utils/commonFunctions');
const appVariables = require('../constants/appVariables');
const jwt = require('jsonwebtoken');
const config = require('config');
const kafkaLib = require("../utils/kafka-status-page/kafka-lib");
const kafkaStatusLib = require("../utils/kafka-status-page/kafka-lib");

const allFunctions = {
  ...require('../customPlugins/semanticSearch/cron/createDictionary'),
  ...require('../customPlugins/semanticSearch/cron/facetInterpreter')
}

const mlCoreURL = appVariables.ml.coreURL

const path = require("path");
const fs = require("fs");
const {
  spawn,
  exec
} = require('child_process');
const {
  da
} = require('stopword');


router.get('/getAdminCrons', function (req, res, next) {
  let sql = "Select * from `crons`";
  let q = connection[req.headers['tenant-id']].execute.query(sql, function (error, result) {
    if (!error) {
      res.send({
        "status": 200,
        "data": result
      });
    } else res.sendStatus(403);
  });
});

router.post("/startCrons", function (req, res) {
  let inputData = req.body;
  let array = JSON.parse(inputData.execution_array);
  let ml_url = req.headers.session['mlClusterIp']
  if (array.length == 1) {
    array.map(item => {
      let dir = path.join(__dirname, '../cron-logs');
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir);
      }
      let fileName = path.join(__dirname, '../../../cron-logs/') + (inputData.name).split(" ").join("") + "_" + (new Date()).toISOString() + '.log';
      if (item.execution_environment == 'admin') {
      console.log(array[0].function_name);
      const cronFunction = allFunctions[array[0].function_name].bind(null);
      let currDate = new Date();
        let sql = "UPDATE crons SET is_enable = 1, last_train_date = ?, pid = ?, logFile = ? WHERE id = ? and name = ?;"
          connection[req.headers['tenant-id']].execute.query(sql, [currDate, 10, '', inputData.id, inputData.name], function (error, data) {
          if (error) {
            commonFunctions.errorlogger.error(error);
            res.send({
              "status": 400,
              "res": "Not Updated"
            })
          } else {
            commonFunctions.errorlogger.info(data);
            res.send({
              "status": 200,
              "res": "Crons data updated successfully"
            })
          }
        });
      cronFunction(req, () => {
          commonFunctions.errorlogger.info("Cron completed ");
          updatePidStatus(0, inputData, req, function (error, result) {
            if (error) {
              commonFunctions.errorlogger.error(error);
            } else {
              commonFunctions.errorlogger.info(result);
            }
          });
        })
      }
      else if(item.execution_environment == 'Annotation ml-service'){
          ml_url = mlCoreURL
          var options = {
            method: "GET",
            rejectUnauthorized: false,
            url: ml_url + "/annotation-batch",
            json: true,
            body: {}
          };
          options.body.tenantId = req.headers['tenant-id'];
          request(options, function (error, response, body) {
            if (error) {
              commonFunctions.errorlogger.error('i am error', error);
              res.send([]);
            }
            else {
              res.send(body);
              commonFunctions.errorlogger.info("body", body);
              let currDate = new Date();
              let sql = "UPDATE crons " +
                " SET is_enable = 1, last_train_date = ?, logFile = ? WHERE id = ? and name = ?;"
                connection[req.headers['tenant-id']].execute.query(sql, [currDate, fileName, inputData.id, inputData.name], function (error, data) {
                if (error) {
                  commonFunctions.errorlogger.error(error);
                } else {
                  commonFunctions.errorlogger.info(data);
                }
              });
            }
          });
      }
      else if (item.execution_environment == 'Snippets ml-service'){
        ml_url = mlCoreURL
          var options = {
            method: "GET",
            rejectUnauthorized: false,
            url: ml_url + "/snippets-batch",
            json: true,
            body: {}
          };
        options.body.tenantId = req.headers['tenant-id'];
        if ('index_name' in inputData){
          options.body.index_name = inputData.index_name
          request(options, function (error, response, body) {
            if (error) {
              commonFunctions.errorlogger.error('i am error', error);
              res.send([]);
            }
            else {
              res.send(body);
              let currDate = new Date()
              let sql = "UPDATE crons " +
              " SET is_enable = 1, last_train_date = ?, logFile = ? WHERE id = ? and name = ?;"
              connection[req.headers['tenant-id']].execute.query(sql, [currDate, fileName, inputData.id, inputData.name], function (error, data) {
                if (error) {
                commonFunctions.errorlogger.error(error);
              } else {
                commonFunctions.errorlogger.info(data);
              }
            });
            }
          });
        }else{
          request(options, function (error, response, body) {
            if (error) {
              commonFunctions.errorlogger.error('i am error', error);
              res.send([]);
            }
            else {
              res.send(body);
              let currDate = new Date();
              let sql = "UPDATE crons " +
                " SET is_enable = 1, last_train_date = ?, logFile = ? WHERE id = ? and name = ?;"
                connection[req.headers['tenant-id']].execute.query(sql, [currDate, fileName, inputData.id, inputData.name], function (error, data) {
                if (error) {
                  commonFunctions.errorlogger.error(error);
                } else {
                  commonFunctions.errorlogger.info(data);
                }
              });
            }
          });
        }

      }
      else if (item.execution_environment == 'Reranking ml-service') {
        var options = {
          method: "GET",
          rejectUnauthorized: false,
          url: ml_url + "/re-ranking-batch",
          json: true,
          body: {}
        };
        options.body.tenantId = req.headers['tenant-id'];
        if ('search_client_id' in inputData){
          options.body.uid = inputData.search_client_id
          request(options, function (error, response, body) {
            if (error) {
              commonFunctions.errorlogger.error('Error in Re ranking job', error);
              res.send([]);
            }
            else {
              res.send(body);
              let currDate = new Date()
              let sql = "UPDATE crons " +
              " SET is_enable = 1, last_train_date = ?, logFile = ? WHERE id = ? and name = ?;"
              connection[req.headers['tenant-id']].execute.query(sql, [currDate, fileName, inputData.id, inputData.name], function (error, data) {
                if (error) {
                commonFunctions.errorlogger.error(error);
              } else {
                commonFunctions.errorlogger.info(data);
              }
            });
            }
          });
        }
      }
      else {
        let fileName = path.join(__dirname, '../../../cron-logs/') + (inputData.name).split(" ").join("") + "_" + (new Date()).toISOString() + '.log';
        let query = {
          log_file: fileName,
          execute_by: item.execute_by,
          name: inputData.name,
          tenantId: req.headers['tenant-id']
        }
        let options = {
          method: 'GET',
          rejectUnauthorized: false,
          url: config.get('analyticsService.url') + `/adminCrons/trainCron`,
          qs: query
        };
        request(options, function (error, response, body) {
          let result = JSON.parse(response.body);
          if (error) {
            commonFunctions.errorlogger.error(error)
            return;
          } else {
            commonFunctions.errorlogger.info("body", body);
            let currDate = new Date();
            let sql = "UPDATE crons " +
              " SET is_enable = 1, last_train_date = ?, pid = ?, logFile = ? WHERE id = ? and name = ?;"
              connection[req.headers['tenant-id']].execute.query(sql, [currDate, result.data, fileName, inputData.id, inputData.name], function (error, data) {
                if (error) {
                commonFunctions.errorlogger.error(error);
                res.send({
                  "status": 400,
                  "res": "Not Updated"
                })
              } else {
                commonFunctions.errorlogger.info(data);
                res.send({
                  "status": 200,
                  "res": "Crons data updated successfully"
                })
              }
            });
          }
        })
      }
    })
  } else {
    let dir = path.join(__dirname, '../cron-logs');
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir);
      }
    let fileName = path.join(__dirname, '../../../cron-logs/') + (inputData.name).split(" ").join("") + "_" + (new Date()).toISOString() + '.log';
    async.series([
      callback => {
        const cronFunction = allFunctions[array[0].function_name].bind(null);
        let currDate = new Date();
        let sql = "UPDATE crons SET is_enable = 1, last_train_date = ?, pid = ?, logFile = ? WHERE id = ? and name = ?;"
          connection[req.headers['tenant-id']].execute.query(sql, [currDate, 10, fileName, inputData.id, inputData.name], function (error, result) {
            if (error) {
              commonFunctions.errorlogger.error(error);
            } else {
              commonFunctions.errorlogger.info(result);
            }
        });
        cronFunction(req, ()=>{
        let query = {
          log_file: fileName,
          execute_by: array[1].execute_by,
          name: inputData.name,
          tenantId: req.headers['tenant-id']
        }
        let options = {
          method: 'GET',
          rejectUnauthorized: false,
          url: config.get('analyticsService.url') + `/adminCrons/trainCron`,
          qs: query
        };
        request(options, function (error, response, body) {
          let result = JSON.parse(response.body);
          if (error) {
            commonFunctions.errorlogger.error(error)
            return;
          } else {
            commonFunctions.errorlogger.info("body", body);
            let currDate = new Date();
            let sql = "UPDATE crons " +
              " SET is_enable = 1, last_train_date = ?, pid = ?, logFile = ? WHERE id = ? and name = ?;"
              connection[req.headers['tenant-id']].execute.query(sql, [currDate, result.data, fileName, inputData.id, inputData.name], function (error, data) {
              if (error) {
                commonFunctions.errorlogger.error(error);
                callback(null);

              } else {
                commonFunctions.errorlogger.info(data);
                callback(null);
              }
            });
          }
        })
        })
      }
    ], (err, result) => {
      if (err) {
        res.send({
          "status": 400,
          "res": "Not Updated"
        })
      } else {
        res.send({
          "status": 200,
          "res": "Crons data updated successfully"
        })
      }
    })
  }
});

function updatePidStatus(pid, data, req, cb) {
  let sql = "UPDATE crons SET pid = ? where  id = ? and name = ?;"
  connection[req.headers['tenant-id']].execute.query(sql, [pid, data.id, data.name], function (error, data) {
    if (error) {
      commonFunctions.errorlogger.error(error);
      cb(null, null);
    } else {
      commonFunctions.errorlogger.info("PID Updated in DB with pid - " + pid);
      cb(null, null);
    }
  })
}

router.post("/updateCronModel", function (req, res) {
  let input = req.query;
  let sql = "UPDATE crons SET pid = ? where name = ?;"
  connection[req.headers['tenant-id']].execute.query(sql, [input.codeId, input.cronName], function (error, data) {
    if (error) {
      commonFunctions.errorlogger.error(error);
      res.send({
        "status": 400,
        "res": "Some Error in Updating the cron status"
      });
    } else {
      res.send({
        "status": 200,
        "cronsData": data
      });
    }
  });
});


router.post("/getCronLogs", function (req, res) {
  let inputData = req.body;
  let array = JSON.parse(inputData.execution_array);
  if (array.length == 1) {
    array.map(item => {
      let file = req.body.logFile;
      if (item.execution_environment == 'admin') {
        let onlyCronlogs;
        let filepath = path.join(process.cwd(), file);
        let command = 'tail -100 ' + filepath;
        return new Promise((resolve, reject) => {
          exec(command, (error, stdout, stderr) => {
            if (error) {
              commonFunctions.errorlogger.error(error);
            }
            else{
              let source = {};
              source.logLines = stdout;
              onlyCronlogs = source.logLines;
              resolve(onlyCronlogs ? onlyCronlogs : '');
              if (error) {
                commonFunctions.errorlogger.error(error)
                res.send({
                  "status": 400,
                  "res": "Not Updated"
                })
              } else {
                commonFunctions.errorlogger.info("cron logs for admin cron :- " + inputData.name);
                res.send({
                  "status": 200,
                  "res": onlyCronlogs
                })
              }
            }
          })
        })
      } else {
        let query = {
          log_file: file,
          execute_by: item.execute_by,
          name: inputData.name,
          tenantId: req.headers['tenant-id']
        }
        let options = {
          method: 'GET',
          rejectUnauthorized: false,
          url: config.get('analyticsService.url') + `/adminCrons/cronLog`,
          qs: query
        };
        request(options, function (error, response, body) {
          let result = JSON.parse(response.body);
          if (error) {
            commonFunctions.errorlogger.error(error)
            res.send({
              "status": 400,
              "res": "Not Updated"
            })
          } else {
            commonFunctions.errorlogger.info("cron logs for analytics cron :- " + inputData.name);
            let res1 = result.data ? result.data : '';
            res.send({
              "status": 200,
              "res": res1
            })
          }
        })
      }
    })
  } else {
    let file = req.body.logFile;
    async.waterfall([
      function (callback) {
        let filepath = path.join(process.cwd(), file);
        let command = 'tail -100 ' + filepath;
        exec(command, (error, stdout, stderr) => {
          if (error) {
            commonFunctions.errorlogger.error(error);
            callback(null, '');
          } else {
            commonFunctions.errorlogger.info("cron logs for admin cron :- " + inputData.name);
            let source = {};
            source.logLines = stdout;
            onlyCronlogs = source.logLines;
            callback(null, onlyCronlogs ? onlyCronlogs : '');
          }
        })
      },
      function (data, callback) {
        let responseData = '';
        if(data != ''){
          responseData += 'Admin \n\n';
          responseData += data;
        }
        let query = {
                log_file: file,
                execute_by: inputData.execution_array[1].execute_by,
                name: inputData.name,
                tenantId: req.headers['tenant-id']
              }
              let options = {
                method: 'GET',
                rejectUnauthorized: false,
                url: config.get('analyticsService.url') + `/adminCrons/cronLog`,
                qs: query
              };
              request(options, function (error, response, body) {
                let result = JSON.parse(response.body);
                if (error) {
                  commonFunctions.errorlogger.error(error)
                  callback(null, null)
                } else {
                  commonFunctions.errorlogger.info("cron logs for analytics cron :- " + inputData.name);
                  if(result.data != ''){
                    responseData += '\n\n Analytics \n\n';
                    responseData += result.data;
                  }
                  callback(null, responseData)
                }
              })
      }
     ], function (err, result) {
      if (err) {
        res.send({
                "status": 400,
                "res": "Not Updated"
              })
            } else {
              res.send({
                "status": 200,
                "res": result
              })
            }
     });
  }
});

router.post('/mlConversion', function (req, res, next) {
  let sql = "select uid from search_clients";
  const mlUrl = mlCoreURL
  connection[req.headers['tenant-id']].execute.query(sql, function (error, result) {
    if (!error) {
      const UIDs = [];
      result.map(item => {
        UIDs.push(item.uid);
      })
      const options = {
        method: 'POST',
        url: mlUrl + `/profiles`,
        body: JSON.stringify({
          tenant_id: req.headers['tenant-id'],
          uid: UIDs
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      };
      request(options, function (error, response) {
        if (error || response.statusCode !== 200) {
          commonFunctions.errorlogger.error(error)
          res.send({
            "status": 400,
            "res": "Unable to start conversion"
          });
        } else {
          commonFunctions.errorlogger.info("ML Conversion started");
          sql = "UPDATE crons SET is_enable = 1, last_train_date = ?, pid = ? WHERE id = ? and name = ?;"
          connection[req.headers['tenant-id']].execute.query(sql, [new Date(), 1, 3, 'ML Conversions'], function (error, result) {
            if (error) {
              commonFunctions.errorlogger.error(error);
            } else {
              res.send({
                "status": 200,
                "res": result
              })
            }
          });
        }
      });
    } else res.sendStatus(403);
  });
});

module.exports = router;
