module.exports = {
    "objects": [{
        "name": "document",
        "label": "Document",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []
  
    }],
    "fields": [{
        "name": "id",
        "label": "ID",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "name",
        "label": "Title",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "version",
        "label": "Version",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 0,
        "isActive": 1
    },
    {
        "name": "modifiedAt",
        "label": "Modified Date",
        "type": "datetime",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 0,
        "isActive": 1
    },
    {
        "name": "latestLibraryContentVersionId",
        "label": "Latest Library Content Version Id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "latestLibraryContentVersionSize",
        "label": "Latest Library Content Version Size",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "libraryUrl",
        "label": "Library Url",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "docCenterUrl",
        "label": "DocCenter Url",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "format",
        "label": "Format",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 0,
        "isActive": 1
    },
    {
        "name": "teamsiteId",
        "label": "Teamsite Id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "isDeleted",
        "label": "Is Deleted",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 0,
        "isActive": 1
    },
    {
        "name": "ownerId",
        "label": "Owner Id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "isPublished",
        "label": "Is Published",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "post_time",
        "label": "Created Date",
        "type": "datetime",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 0,
        "isActive": 1,
        "isMerged": 1
    },
    {
        "name": "viewHref",
        "label": "View Href",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    }]
}  