var express = require("express");
var async = require("async");
var path = require("path");
var moment = require("moment");
environment = require("./routes/environment");
var http = require("http");
var fs = require("fs");
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, "config");
process.env.NODE_CONFIG_DIR = configPath;
config = require("config");
var cookieParser = require("cookie-parser");
var bodyParser = require("body-parser");
// var multipart = require('connect-multiparty');
const compression = require("compression");
// var oauthClients = require('./routes/oauthClients'); // moved to multi-tenant-auth
var crons = require("./routes/crons");
var { throttle } = require("./routes/throttleApi/throttle");
const { authenticate } = require("auth-middleware");
var throttleOptions = require("./routes/admin/rateLimit");
var commonFunctions = require("./utils/commonFunctions");
commonFunctions.errorlogger.log(environment.configuration);
const dir = `${__dirname}/reports/`;
const io = require("./utils/socket/index");
const { socketAuth } = require('./auth/socket.auth');
const cors = require('cors');
const kafka = require("./utils/kafka/startup");
kafka.kafkaStartup();
const {
    tenantGlobals,
} = require("./utils/kafka/kafkaPublishers/tenantGlobals");
const { featurePublisher } = require("./utils/kafka/kafkaPublishers/featurePublisher");
var scheduledCrons = require('./routes/scheduledCrons');

// const versionInfo = require('./config/version');
// var initScript = require('./init-bootstrap/contentSourceTypes');

DIRNAME = __dirname;
connSF = ""; //salesforceConnection Variable
//------------Requiring files-----------------an.
var dynamicLoginPage = require("./routes/dynamicLoginPage");
var connection_sql = require("./utils/connection");
var crawlerConfigCache = require("./utils/crawler-config-cache");
var constants = require("./constants/appVariables");
var login = require("./routes/login");
var jwtInit = require("./routes/jwt");
var admin = require("./routes/admin");
// var crawler = require('./routes/crawlers');
// var analytics = require('./routes/analytics');
var searchClientAnalytics = require("./routes/analytics/analytics").router;
var tableauConnector = require("./routes/analytics/tableauConnector");
var tableauConnectorRawApi = require("./routes/analytics/tableauConnectorRawApi");
var analyticsMiddleware = require("./routes/analytics/serviceMiddleware");
var pluginRoutes = require("./routes/plugins/plugins.js");
var synonyms = require("./routes/synonyms");
var tuning = require("./routes/searchClient/tuning");
var personalisedTuning = require("./routes/searchClient/personalisedTuning");
// var generateSearchClient = require('./routes/generateSearchClient');
var oauth = require("./routes/components/oauth/express.js");
var recommender = require("./routes/recommender/recommendation");
var stopwords = require("./routes/stopwords");
var stemwords = require("./routes/stemwords")
var saveAdminAnalytics = require("./routes/analytics/adminAnalytics/saveAdminAnalytics");
// var bot = require("./routes/chatBot");
var knowledgeGraph = require("./routes/knowledgeGraph");
// var kcsConfiguration = require("./routes/kcsConfiguration");

var security = require("./routes/security/security");
var advertisements = require("./routes/advertisements/advertisements");
var pageRating = require("./routes/pageRating/pageRating");
var communityHelper = require("./customPlugins/communityHelper");
var autoSpellCorrect = require("./routes/autoTuning/getSpellCorrect");
var automatedScript = require("./customPlugins/scripts/keyWordBoostingScriptCheck");
var updateAnalyticsUserConfig = require("./customPlugins/scripts/updateAnalyticsUserConfigCheck");
const sessionPreference = require("./customPlugins/semanticSearch/cron/updateSessionPreferences");
const spellCorrector = require("./customPlugins/semanticSearch/cron/spellCheckGraph");
const similarSearch = require("./routes/admin/similarSearchRecommend");
const taxonomyAnnotation = require("./routes/admin/taxonomyAnnotation");
const mlService = require("./routes/admin/mlService");
const statusPage = require("./routes/admin/statusPage");
const publisher = require("./routes/admin/publishAllContent");
const agentForceService = require("./routes/admin/agentForceService.js");
const { createProxyMiddleware,fixRequestBody } = require('http-proxy-middleware');
var searchunifySupport = require("./routes/searchunifySupport");
var nlpIntent = require("./routes/nlpIntent/");
// var getAutoFacet = require('./routes/autoTuning/getAutoFacet');
var saveFilteredSearches = require("./routes/autoTuning/saveFilteredSearches");
// SSO SIGN IN
var ssoSaml = require("./routes/saml/sso-saml");
var passport = require("passport");
// var splitIndex = require('./routes/analytics/splitAnalyticsIndex');
const marketplace = require("./routes/marketplace/marketplace.controller");
const slackIntegrationApis = require("./routes/slackIntegrationApis");
const agentHelper = require("./customPlugins/agentHelper");
const { accessControlReqUrlsForSuperAdmin,
    accessControlOnReqUrlForAdminAndSuperAdmin,
    accessControlForSecurityTabsModerator,
    accessControlForNotification,
    accessControlForAnalytics,
    accessControlForNlpManager,
    accessControlForMarketplace ,
    accessControlForApps,
    accessControlForLlmIntegrations,
    accessControlForAHApp} = require('./auth/urlAuthorization');
const mergeFields = require('./routes/merge-fields');
//set port
var layer = require("./auth/layer");

var layerSql = require("./auth/sqlConnectionMiddleware");
config = require("config");
const aes256 = require('nodejs-aes256');

process.env.PORT = config.get("PORT");
var searchCallPermissions = require("./routes/searchCallPermissions");
const { BUILD_INFO_FILE_PATH, BUILD_INFO_FILE_PATH_FRONTEND } = require("./constants/constants");
const { rateLimitandQuota, manageTenantRateLimitInfo} = require("./routes/throttleApi/rate-limits-quota-middleware.js");
const rateLimitDosMiddleware = require("./utils/rate-limit-dos-middleware.js");
const RateLimitInfoStorage = require('./utils/singleton-storage/rate-limit-storage.js');
const { publishRateLimitCrons, publishStatusPageTokenCrons, publishAbTestDataCrons } = require('./utils/kafka/kafkaPublishers/crons');
const { accessCheck } = require('./utils/commonFunctions');
var app = express();

// Allow all origins and set 'credentials' to 'true' for all URLs
app.use(
    cors({
      origin: true, // Allow all origins
      credentials: true, // Enable credentials for all URLs
    })
);
  
app.startComplete = (cb) => {
    if (app._working) cb();
    else app._startComplete = cb;
};

// Custom middleware to set secure and httpOnly flags for cookies
app.use(commonFunctions.setSecureHttpOnlyFlagsForCookies);

//----------------------------------------------
function serveProd() {
    /*    if (environment.configuration == "production") {
            var options = {
                key: fs.readFileSync('/usr/local/ssl/certificates/connectors-grazitti-com.key'),
                cert: fs.readFileSync('/usr/local/ssl/certificates/connectors-grazitti-com.crt'),
                ca: [fs.readFileSync('/usr/local/ssl/certificates/intermediate.crt')]
            };
        }
       */
    var root = path.resolve(process.cwd(), "dist");
    app.disable("view cache");
    // var sessionStore = new connection_sql.MySQLStore({}/* session store options */, connection.auth);
    app.use((req, res, next) => {
        if (req.url.indexOf("iframe.html") == -1) {
            res.setHeader("X-Frame-Options", "DENY");
        }
        if (
            req.url.indexOf(".png") != -1 ||
            req.url.indexOf(".svg") != -1 ||
            req.url.indexOf(".gif") != -1
        ) {
            res.setHeader(
                "Cache-Control",
                `public, max-age=${config.get("cache.adminAssestsMaxAge")}`
            );
        } else {
            res.setHeader("Cache-Control", "no-cache, no-store");
            res.setHeader("pragma", "no-cache");
            res.setHeader("Expires", "0");
        }
        // res.setHeader("X-XSS-Protection", "1;");
        res.setHeader("Access-Control-Allow-Methods", "POST, GET");
        res.setHeader("X-Content-Type-Options", "nosniff");
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload'); // HSTS
        // res.setHeader("Content-Security-Policy", "default-src 'self'; script-src 'self' ; img-src *");
        res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin'); // Controls Referer header
        res.setHeader('Permissions-Policy', 'camera=(), microphone=()'); // Re

        //res.setHeader("Content-Security-Policy", "default-src 'self'; script-src 'self' ; img-src *");
        next();
    });
    app.use(compression());
    app.set("view engine", "html");
    app.use(bodyParser.urlencoded({ extended: true }));
    app.use(bodyParser.json({ limit: "5mb" }), (err, req, res, next) => {
        if (err) {
            commonFunctions.errorlogger.log("Invalid Request data");
            commonFunctions.errorlogger.log("Headers", req.headers);
            commonFunctions.errorlogger.error(err);
            res.status(406).send("Invalid Request data");
        } else {
            next();
        }
    });

    connection_sql.sessionSet.value = {
        secret: "Vvvbmhppra",
        resave: true,
        saveUninitialized: false,
        // store: sessionStore,
        rolling: true,
        name: "connect.admin_sid",
        cookie: {
            maxAge: 1800 * 1000,
            secure: false,
            httpOnly: true,
            // sameSite: 'none'
        },
    };
    if (
        environment.configuration == "trial" ||
        environment.configuration == "production"
    ) {
        connection_sql.sessionSet.value.cookie.secure = true;
        connection_sql.sessionSet.value.cookie.sameSite = "none";
    }
    // app.use(connection_sql.session(connection_sql.sessionSet.value));
    app.use(cookieParser());

    app.use(function (req, res, next) {
        var mem = process.memoryUsage();
        var logObject = {};
        logObject.Time = Date();
        logObject.url = req.url;
        logObject.method = req.method;
        logObject.referer = req.headers.referer || '';
        regex = new RegExp("^/api/analytics");
        const allowedOrigin =
            environment.configuration == "production"
                ? config.get("adminURL")
                : req.headers.origin;
        if (req.body) logObject.body = req.body;

        //removing unneccessary logs
        if (logObject.url
           && logObject.url.indexOf("/search/socket") == -1
           && logObject.url.indexOf("/index-service/socket") == -1
           && logObject.referer.indexOf("/ngsw-worker.js") == -1) {
            // commonFunctions.errorlogger.info(
            //     "================ current memory uses:",
            //     Math.round(mem.rss) + " B"
            // );

            if (logObject.url && logObject.url.indexOf("/login")) {
                delete logObject.body;
            }
            commonFunctions.errorlogger.info("requestLog", logObject);
        }
        //  res.setHeader("Access-Control-Allow-Origin", "*");

        // res.header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type");
        // // res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key");
        if (
            "OPTIONS" === req.method &&
            (req.path == "/search/searchResultByPost" ||
                req.path == "/ai/getRecommendedResult" ||
                req.path == "/pageRating/getPageRatingData" ||
                req.path == "/pageRating/getPageRatingDataInstance")
        ) {
            res.setHeader("Access-Control-Allow-Origin", "*");
            res.setHeader("Access-Control-Allow-Methods", "POST, GET");
            res.header(
                "Access-Control-Allow-Headers",
                "X-Requested-With, Content-Type"
            );
            //res.header("Origin, Content-Type, Accept, Key, X-UserToken");
            res.send(200);
        } else if (
            "OPTIONS" === req.method &&
            (req.path == "/admin/contentSources/byCaseUidAuth" ||
                req.path == "/search/SUSearchResults" ||
                req.path == "/agentHelper/query" ||
                req.path == "/ai/authSURecommendation" ||
                req.path.includes("/slackApis") ||
                req.path == "/agentHelper/case-sentiments")
        ) {
            res.header(
                "Access-Control-Request-Headers",
                "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken"
            );
            res.setHeader("Access-Control-Allow-Credentials", true);
            res.setHeader(
                "Access-Control-Allow-Origin",
                req.headers.origin || ""
            );
            res.header(
                "Access-Control-Allow-Headers",
                "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken,Authorization"
            );
            res.sendStatus(200);
        } else if (
            req.path.includes("/resources/search_clients_custom") ||
            req.path.includes("/slackApis")
        ) {
            res.header(
                "Access-Control-Request-Headers",
                "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken"
            );
            res.setHeader("Access-Control-Allow-Credentials", true);
            res.setHeader(
                "Access-Control-Allow-Origin",
                req.headers.origin || ""
            );
            res.header(
                "Access-Control-Allow-Headers",
                "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken,Authorization"
            );
            //   res.sendStatus(200);
            next();
        } else if (
            /* Ep addon apis routes */
            req.path.includes("/api/v2_cs") ||
            req.path.includes("/escalationPrediction") ||
            req.path.includes("/admin/contentSources") ||
            req.path.includes("/oauth/token")
        ) {
            res.header(
                "Access-Control-Request-Headers",
                "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken"
            );
            res.setHeader("Access-Control-Allow-Credentials", true);
            res.setHeader(
                "Access-Control-Allow-Origin",
                req.headers.origin || ""
            );
            res.header(
                "Access-Control-Allow-Headers",
                "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken,Authorization"
            );
            next();
        } else if ("OPTIONS" === req.method && regex.test(req.path)) {
            res.setHeader(
                "Access-Control-Allow-Origin",
                req.headers.origin || ""
            );
            // res.setHeader("Access-Control-Allow-Credentials", false);
            res.header(
                "Access-Control-Allow-Headers",
                "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken"
            );
            res.setHeader("Content-Type", "application/json");
            console.log("i am reponse ***********", res._headers);
            res.sendStatus(200);
            // next();
        } else if ("OPTIONS" === req.method || "HEAD" === req.method) {
            res.header(
                "Access-Control-Request-Headers",
                "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken"
            );
            res.header(
                "Access-Control-Allow-Headers",
                "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken,Authorization"
            );
            res.sendStatus(401);
        } else if (req.path.includes("/admin/assetLibrary/uploadClientIcon")) {
            if (req.headers.referer.includes(allowedOrigin)) {
                next();
            } else {
                res.sendStatus(401);
            }
        } else if (
            "POST" === req.method &&
            (req.path == "/agentHelper/query" ||
                req.path == "/agentHelper/case-sentiments")
        ) {
            res.setHeader(
                "Access-Control-Allow-Origin",
                req.headers.origin || ""
            );
            next();
        } else {
            next();
        }
    });

    //public accessible path
    app.use("/node_modules", express.static("node_modules"));
    // app.use(express.static(__dirname + "/dist"));
    // Watch for changes in the dist folder and update middleware
    let staticMiddleware = express.static(root);

    app.use((req, res, next) => {
        staticMiddleware(req, res, next);
    });

    // Hybrid file watching approach for shared AWS volumes
    let lastModified = {};
    let pollingInterval;
    let watcherFallback = null;
    let lastRefreshTime = Date.now();
    let refreshCount = 0;

    // Configuration - optimized for Angular builds
    const POLLING_INTERVAL = process.env.DIST_POLLING_INTERVAL || 5000; // 5 seconds default
    const MAX_REFRESH_PER_MINUTE = 12; // Prevent excessive refreshes
    const ENABLE_FILE_COUNT_CHECK = process.env.ENABLE_FILE_COUNT_CHECK !== 'false'; // Default: true
    const ENABLE_DEEP_SCAN = process.env.ENABLE_DEEP_SCAN === 'true'; // Default: false (for performance)

    // Function to get directory modification times - optimized for Angular builds
    function getDirectoryStats(dirPath) {
        const stats = {};
        try {
            if (fs.existsSync(dirPath)) {
                // 1. Directory modification time (fastest check)
                const dirStat = fs.statSync(dirPath);
                stats[dirPath] = dirStat.mtime.getTime();

                // 2. Always check index.html (entry point, always present)
                const indexPath = path.join(dirPath, 'index.html');
                if (fs.existsSync(indexPath)) {
                    const indexStat = fs.statSync(indexPath);
                    stats[indexPath] = indexStat.mtime.getTime();
                }

                // 3. Check for Angular-specific indicator files (lightweight check)
                const angularIndicators = [
                    'runtime.js',           // Angular runtime (small, changes with every build)
                    'polyfills.js',         // Polyfills bundle
                    'main.js',              // Main bundle (if not hashed)
                    'styles.css'            // Styles bundle (if not hashed)
                ];

                angularIndicators.forEach(file => {
                    const filePath = path.join(dirPath, file);
                    if (fs.existsSync(filePath)) {
                        const fileStat = fs.statSync(filePath);
                        stats[filePath] = fileStat.mtime.getTime();
                    }
                });

                // 4. Smart detection for hashed files (only check a few key patterns)
                const files = fs.readdirSync(dirPath);
                const hashPatterns = [
                    /^runtime\.[a-f0-9]+\.js$/,      // runtime.{hash}.js
                    /^polyfills\.[a-f0-9]+\.js$/,   // polyfills.{hash}.js
                    /^main\.[a-f0-9]+\.js$/,        // main.{hash}.js
                    /^styles\.[a-f0-9]+\.css$/      // styles.{hash}.css
                ];

                // Only check first match of each pattern to avoid scanning all files
                hashPatterns.forEach(pattern => {
                    const matchedFile = files.find(file => pattern.test(file));
                    if (matchedFile) {
                        const filePath = path.join(dirPath, matchedFile);
                        const fileStat = fs.statSync(filePath);
                        stats[filePath] = fileStat.mtime.getTime();
                    }
                });

                // 5. Optional: Check file count as an additional indicator (lightweight)
                if (ENABLE_FILE_COUNT_CHECK) {
                    stats['__fileCount__'] = files.length;
                }

                // 6. Deep scan mode (only if explicitly enabled - for debugging)
                if (ENABLE_DEEP_SCAN) {
                    commonFunctions.errorlogger.info('Deep scan mode enabled - checking all files');
                    files.forEach(file => {
                        if (file.match(/\.(js|css|html)$/)) {
                            const filePath = path.join(dirPath, file);
                            const fileStat = fs.statSync(filePath);
                            stats[filePath] = fileStat.mtime.getTime();
                        }
                    });
                }
            }
        } catch (error) {
            commonFunctions.errorlogger.error('Error checking directory stats:', error);
        }
        return stats;
    }

    // Rate-limited refresh function
    function refreshStaticMiddleware(source = 'unknown') {
        const now = Date.now();
        const timeSinceLastRefresh = now - lastRefreshTime;

        // Reset counter if more than a minute has passed
        if (timeSinceLastRefresh > 60000) {
            refreshCount = 0;
        }

        // Check rate limit
        if (refreshCount >= MAX_REFRESH_PER_MINUTE && timeSinceLastRefresh < 60000) {
            commonFunctions.errorlogger.warn(`Rate limit exceeded for static middleware refresh (source: ${source})`);
            return false;
        }

        try {
            commonFunctions.errorlogger.info(`Refreshing static middleware (source: ${source}, count: ${refreshCount + 1})`);
            staticMiddleware = express.static(root);
            lastModified = getDirectoryStats(root);
            lastRefreshTime = now;
            refreshCount++;
            return true;
        } catch (error) {
            commonFunctions.errorlogger.error('Error refreshing static middleware:', error);
            return false;
        }
    }

    // Function to check for changes
    function checkForChanges() {
        const currentStats = getDirectoryStats(root);
        let hasChanges = false;
        let changedFiles = [];

        // Check if any files have been modified
        for (const [filePath, mtime] of Object.entries(currentStats)) {
            if (!lastModified[filePath] || lastModified[filePath] !== mtime) {
                hasChanges = true;
                changedFiles.push(filePath);
            }
        }

        // Check if any files have been deleted
        for (const filePath of Object.keys(lastModified)) {
            if (!currentStats[filePath]) {
                hasChanges = true;
                changedFiles.push(`${filePath} (deleted)`);
            }
        }

        if (hasChanges) {
            commonFunctions.errorlogger.info(`File changes detected: ${changedFiles.join(', ')}`);
            refreshStaticMiddleware('polling');
        }
    }

    // Initialize with current state
    lastModified = getDirectoryStats(root);

    // Start polling with configurable interval
    const pollingIntervalMs = parseInt(process.env.DIST_POLLING_INTERVAL) || 5000;
    pollingInterval = setInterval(checkForChanges, pollingIntervalMs);

    commonFunctions.errorlogger.info(`Started dist folder polling with ${pollingIntervalMs}ms interval`);

    // Try to set up chokidar as a fallback (may not work on shared volumes)
    try {
        const chokidar = require('chokidar');
        watcherFallback = chokidar.watch(root, {
            persistent: true,
            ignoreInitial: true,
            usePolling: false, // Try native first
            awaitWriteFinish: {
                stabilityThreshold: 1000,
                pollInterval: 100
            }
        });

        watcherFallback.on('all', (event, filePath) => {
            commonFunctions.errorlogger.info(`Chokidar detected ${event} on ${filePath}`);
            refreshStaticMiddleware('chokidar');
        });

        watcherFallback.on('error', (error) => {
            commonFunctions.errorlogger.warn('Chokidar watcher error (falling back to polling only):', error.message);
            if (watcherFallback) {
                watcherFallback.close();
                watcherFallback = null;
            }
        });

        commonFunctions.errorlogger.info('Chokidar fallback watcher initialized');
    } catch (error) {
        commonFunctions.errorlogger.info('Chokidar not available, using polling only:', error.message);
    }

    // Cleanup on process exit
    process.on('SIGTERM', () => {
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }
        if (watcherFallback) {
            watcherFallback.close();
        }
    });

    process.on('SIGINT', () => {
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }
        if (watcherFallback) {
            watcherFallback.close();
        }
    });

    app.use("/anreports", express.static("reports/reports"));
    app.use("/epreports", express.static("reports/reports"));
    /** Create Folder dynamically */
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir);
    } else {
        console.log("Directory already exist");
    }
    // app.set('view engine', 'ejs');
    app.engine("html", require("ejs").renderFile);
    app.engine("js", require("ejs").renderFile);
    app.engine("css", require("ejs").renderFile);
    //check for auth
    // oauth(app);

    // if (environment.configuration != "localDev" && environment.configuration != "development")
    // app.use(layer);
    const appInfo = {
        name: "admin",
        id: 1,
    };
    //Poll API for admin Service
    app.get('/pollApiCheck', (req, res) => {
        const distFolder = path.resolve(process.cwd(), 'dist');
        if (!fs.existsSync(distFolder)) {
            console.log('Dist folder does not exist not exists');
            return res.send({ statusCode: 404, message: 'Dist folder does not exist', data: '' });
        }
        if(fs.existsSync(distFolder))
        {
            const files = fs.readdirSync(distFolder);
            if (files.length === 0) {
                console.log('Dist folder exists but is empty');
                return res.send({ statusCode: 204, message: 'Dist folder is empty', data: '' });
            }
        }

        res.send({ statusCode: 200, message: 'Poll API is working fine!', version: config.get('version')});
    })

    // Manual refresh endpoint for static middleware
    app.post('/admin/refresh-static', (req, res) => {
        try {
            const distFolder = path.resolve(process.cwd(), 'dist');
            commonFunctions.errorlogger.info('Manual static middleware refresh requested');

            // Use the rate-limited refresh function
            const refreshed = refreshStaticMiddleware('manual');

            // Get current dist folder info for response
            const stats = {
                exists: fs.existsSync(distFolder),
                fileCount: 0,
                lastModified: null,
                refreshed: refreshed,
                refreshCount: refreshCount,
                timeSinceLastRefresh: Date.now() - lastRefreshTime
            };

            if (stats.exists) {
                const files = fs.readdirSync(distFolder);
                stats.fileCount = files.length;
                if (files.length > 0) {
                    const distStat = fs.statSync(distFolder);
                    stats.lastModified = distStat.mtime;
                }
            }

            const message = refreshed ?
                'Static middleware refreshed successfully' :
                'Refresh skipped due to rate limiting or error';

            commonFunctions.errorlogger.info(message, stats);
            res.send({
                statusCode: 200,
                message: message,
                data: stats
            });
        } catch (error) {
            commonFunctions.errorlogger.error('Error refreshing static middleware:', error);
            res.status(500).send({
                statusCode: 500,
                message: 'Error refreshing static middleware',
                error: error.message
            });
        }
    })

    // Debug endpoint to check current dist folder status
    app.get('/admin/dist-status', (req, res) => {
        try {
            const distFolder = path.resolve(process.cwd(), 'dist');
            const stats = getDirectoryStats(distFolder);

            const response = {
                distPath: distFolder,
                exists: fs.existsSync(distFolder),
                trackedFiles: Object.keys(stats).length,
                fileStats: stats,
                pollingActive: !!pollingInterval,
                chokidarActive: !!watcherFallback,
                pollingInterval: pollingIntervalMs,
                refreshCount: refreshCount,
                lastRefreshTime: new Date(lastRefreshTime).toISOString(),
                timeSinceLastRefresh: Date.now() - lastRefreshTime
            };

            if (response.exists) {
                const files = fs.readdirSync(distFolder);
                response.totalFiles = files.length;
                response.fileList = files.slice(0, 10); // First 10 files

                // Get directory modification time
                const dirStat = fs.statSync(distFolder);
                response.directoryModified = dirStat.mtime.toISOString();
            }

            res.send({ statusCode: 200, data: response });
        } catch (error) {
            commonFunctions.errorlogger.error('Error checking dist status:', error);
            res.status(500).send({
                statusCode: 500,
                message: 'Error checking dist status',
                error: error.message
            });
        }
    })
    // build Info API
    app.get('/build-info', (req, res) => {
    console.log('Fetching build info data for admin backend :');
    const buildInfoPath = path.resolve(process.cwd(), BUILD_INFO_FILE_PATH);
    if (!fs.existsSync(buildInfoPath)) {
        console.log('Build info file does not exists');
        return res.send({ statusCode: 200, message: 'Build info does not exist', data: '' });
    }
    const readFile = fs.readFileSync(buildInfoPath, 'utf8');
    const sendFileData = readFile.toString();
    return res.send({ statusCode: 200, message: 'Build info working fine!', data: sendFileData });
    });

    app.get('/build-info-fe', (req, res) => {
        console.log('Fetching build info data for admin frontend :');
        const buildInfoPathFrontend = path.resolve(process.cwd(), BUILD_INFO_FILE_PATH_FRONTEND);
        if (!fs.existsSync(buildInfoPathFrontend)) {
            console.log('Build info file does not exists');
            return res.send({ statusCode: 200, message: 'Build info does not exist', data: '' });
        }
        const readFileFrontend = fs.readFileSync(buildInfoPathFrontend, 'utf8');
        const sendFileDataFrontend = readFileFrontend.toString();
        return res.send({ statusCode: 200, message: 'Build info working fine!', data: sendFileDataFrontend });
    });
    app.use(authenticate(appInfo));
    app.use(layerSql);
    app.use(accessControlReqUrlsForSuperAdmin);
    app.use(accessControlOnReqUrlForAdminAndSuperAdmin);
    app.use(accessControlForSecurityTabsModerator);
    app.use(accessControlForNotification);
    app.use(accessControlForAnalytics);
    app.use(accessControlForNlpManager);
    app.use(accessControlForMarketplace);
    app.use(accessControlForLlmIntegrations);
    app.use(accessControlForApps);
    app.use(accessControlForAHApp);
    app.use(require("./auth/ipCheck"));
    oauth(app);

    /*content source tracking route */
    app.use(
        "/contentAnalytics",
        require("./routes/analytics/contentSourceTracking")
    );
    /*content source tracking route*/
    app.use(
        "/resources/loginAuthorize/loginAuthorize.html",
        require("./routes/template/loginAuthorize")
    );
    app.use("/resources", express.static("resources"));
    app.use("/osapp", express.static(__dirname + "/resources/jive"));

    app.use(saveAdminAnalytics.router);
    app.use("/ext_resources", express.static(__dirname + "/resources"));
    app.use("/open", (req, res, next) => {
        // res.setHeader("Access-Control-Allow-Origin", "*");
        // res.header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type");
        next();
    });

    app.use("/open", express.static(__dirname + '/resources/Allow'));
    //-------------------for static pages-----------
    app.get("/dashboard/*", function (req, res) {
        res.sendFile(root + "/index.html");
    });
    app.get("/admin-login", function (req, res) {
        res.sendFile(root + "/index.html");
    });
    app.get("/signup", function (req, res) {
        res.sendFile(root + "/index.html");
    });
    app.get("/registerSU", function (req, res) {
        res.sendFile(root + "/index.html");
    });
    app.get("/api-docs", function (req, res) {
        res.sendFile(root + "/index.html");
    });
    app.get("/forgot-password", function (req, res) {
        res.sendFile(root + "/index.html");
    });
    app.get("/logout", (req, res) => {
        login.adminLogout(req, (err) => {
            if (!err) {
                res.clearCookie("CSRF-Token");
                res.clearCookie("_csrf");
                res.clearCookie("connect.admin_sid");
                res.send();
            }
            console.log("Error while logging out", err);
        });
    });

    app.use("/status-incoming", statusPage.router);        
    //----------------------------------------------------
    //-------------for api methods------------------------
    // app.post('/signup', login.SignUp);
    app.post("/login", manageTenantRateLimitInfo,login.adminLogin);
    app.post("/createJwt", jwtInit.createJwt);
    app.post("/validateUser", login.validateUser);
    app.post("/resendOTPMail", login.resendOTPMail);
    // app.post('/registerEmail', login.registerEmail);
    // app.post('/forgetPassword', login.forgetPassword);
    // app.post('/resendEmail', login.resendEmail);

    // app.post('/getAutoProvisionToken', generateSearchClient.getAutoProvisionToken);

    app.get("/", (req, res) => {
        connection[req.headers["tenant-id"]].query(
            "Select * from user",
            (err, docs) => {
                res.send({ tenanId: req.headers["tenant-id"], data: docs[0] });
            }
        );
    });

    app.get("/tenant-connections", (req, res) => {
        let tenantCount = 0;
        if (Object.keys(connection).length) {
            tenantCount = Object.keys(connection).length;
        }
        res.send({ data: { tenantCount, tenants: Object.keys(connection) } });
    });

    //-----------------------------------------Rajesh Plugin api code ---------------------
    var pluginsPath = constants.plugins.pluginsPath;
    var plugins = [];
    function loadPluginList(cb) {
        fs.readdir(pluginsPath, function (err, items) {
            if (items) {
                for (var i = 0; i < items.length; i++) {
                    plugins.push(items[i]);
                }
            }
            cb(null, "success");
        });
    }

    function loadPluginProperties(cb) {
        commonFunctions.errorlogger.warn("loading plugin properties...");
        var funcarr = plugins.map(function (plugin, idx) {
            //console.log('for Plugin - '+plugin);
            return function (cbin) {
                try {
                    var filepath =
                        pluginsPath +
                        "/" +
                        plugin +
                        constants.plugins.propertyFile;
                    //console.log('propertyFile: '+filepath);
                    if (fs.existsSync(filepath)) {
                        var properties = fs.readFileSync(filepath);

                        properties = JSON.parse(properties);
                        //console.log('pluginProperties: ', pluginsPath);
                        plugins[idx] = properties;
                        // properties.vname = require(pluginsPath + '/' + properties.path);
                        commonFunctions.errorlogger.info(
                            "Adding middleware for: ",
                            properties.name
                        );
                        app.use(
                            properties.exportMethods[0][1],
                            require(pluginsPath + "/" + properties.path)[
                                properties.exportMethods[0][0]
                            ]
                        );
                        /*properties.exportMethods.map(function (method) {
                            //console.log(method[0], JSON.stringify(properties.vname[method[0]]));
                            app.all(method[1], properties.vname[method[0]]);
                            return method;
                        });*/
                    } else {
                        commonFunctions.errorlogger.warn(
                            filepath,
                            " property file not present"
                        );
                    }
                } catch (e) {
                    commonFunctions.errorlogger.error(e);
                }
                cbin(null, "");
            };
        });

        async.series(funcarr, function (error, result) {
            cb(null, "");
        });
    }

    async.series(
        [loadPluginList, loadPluginProperties],
        function (err, result) {
            commonFunctions.errorlogger.warn("plugins loaded..");
        }
    );

    //--------new APIs----------
    app.use("/admin/contentSources", admin.contentSources);
    app.use("/admin/indexService", admin.indexService);
    app.use("/admin/crawlerConfig", admin.crawlerConfig);
    app.use("/admin/crawlerConfigCs", admin.crawlerConfigCs);
    app.use("/admin/schedulerConfig", admin.schedulerConfig);
    app.use("/admin/authorization", admin.oAuthorization);
    app.use('/admin/searchClient', admin.searchClient);
    app.use('/admin/sendEmail', admin.sendEmail);
    app.use('/admin/ecoSystem', admin.ecoSystem);
    app.use('/admin/ahClient', admin.ahClient);
    app.use('/admin/knowledgeGraph', admin.knowledgeGraph);
    app.use('/admin/mergeFields', mergeFields.router);
    app.use("/admin/stackoverflow", admin.stackoverflow);
    app.use('/admin/ab-testing', admin.ABTesting);
    app.use('/sc', createProxyMiddleware({ 
        target: config.get("searchClient.url"), 
        changeOrigin: true, 
        ws: true, 
        onProxyReq: fixRequestBody,
    }));
    // app.use('/crawler/confluence', crawler.confluence.router);
    // app.use('/crawler/sharepoint', crawler.sharepoint.router);
    // app.use('/crawler/mindtouch', crawler.mindtouch.router);
    // app.use('/crawler/slack', crawler.slack.router);
    // app.use('/searchAdmin', admin.searchResult);
    // app.use('/search', admin.searchResult);
    app.use("/admin/intractiveSearch", admin.intractiveSearch.router);
    app.use("/dynamicLoginPage", dynamicLoginPage.dynamicLoginPageFunc);
    app.use("/admin/higherLogic", admin.higherlogic);
    // app.use('/admin/skilljar', admin.skilljar);
    app.use("/tuning", tuning);
    app.use("/searchClientAnalytics", searchClientAnalytics);
    app.use("/personalised-tuning", personalisedTuning);
    app.use("/nlpIntent", nlpIntent.router);
    app.use("/slackApis", slackIntegrationApis.router);
    app.use("/publish", publisher.router);
    app.use("/agentForceService", agentForceService.router)
    //Endpoints for analytics
    app.use("/analytics", function (req, res, next) {
        if (
            req.path == "/tableauConnector/authTableau" ||
            req.path == "/tableauConnectorRawApi/authTableau"
        )
            next();
        else {
            analyticsMiddleware.analyticsMiddleware(
                req,
                res,
                function (err, result) {
                    if (err) {
                      if (err.statusCode) {
                        res.status(err.statusCode).send(result);
                      } else {
                        res.status(400).send(result);
                      }
                    } else {
                      res.send(result);
                    }
                }
            );
        }
    });

    //Endpoints for taxonomyAnnotation ML Service
    app.use('/taxonomyAnnotation/uploadEntities', taxonomyAnnotation.router);
    app.use("/taxonomyAnnotation", function (req, res) {
        taxonomyAnnotation.mlService(req, res, function (err, result) {
            res.send(result);
        });
    });
    // app.use('/enableMlSnippet', function (req, res) {
    //     taxonomyAnnotation.mlService(req, res, function (err, result) {
    //         res.send(result);
    //     });
    // });
    app.use("/intentCustomSynonyms", function (req, res) {
        taxonomyAnnotation.mlService(req, res, function (err, result) {
            res.send(result);
        });
    });
    app.use("/mlService", mlService.router);

    app.use(
            "/api/v2/",
            throttle(throttleOptions.getThrottleOptions(RateLimitInfoStorage.get("BURST")))
        );
        app.use(
            "/api/v3/",
            throttle(throttleOptions.getThrottleOptions(RateLimitInfoStorage.get("BURST")))
        );
        app.use(
            "/api/v2_search",
            throttle(throttleOptions.getThrottleOptions(RateLimitInfoStorage.get("BURST")))
        );
        app.use(
            "/api/v2_cs",
            throttle(throttleOptions.getThrottleOptions(RateLimitInfoStorage.get("BURST")))
        );
        app.use("/api/v2_gpt",
            throttle(throttleOptions.getThrottleOptions(RateLimitInfoStorage.get("BURST"))),
            rateLimitDosMiddleware,
            rateLimitandQuota(RateLimitInfoStorage.get("QUOTA")),
            function (req, res) {
                analyticsMiddleware.gptAPI(req, res, function (err, result) {
                    res.send(result);
                }, true);
            });
        app.use("/api/v2_search", function (req, res) {
            analyticsMiddleware.Apisearch(req, res, function (err, result) {
                if (err && err.message === "Origin is undefined") {
                    res.status(400).send({
                        statusCode: 400,
                        message: "Bad request: No origin header provided",
                    });
                    return;
                }
                res.send(result);
            }, true);
        });

        /** ANalytics api */
        app.use("/api/v2/", function (req, res) {
            analyticsMiddleware.analyticsApiMiddleware(
                req,
                function (err, result) {
                    res.setHeader(
                        "Access-Control-Allow-Origin",
                        req.headers.origin || ""
                    );
                    if (!result.body.status) {
                        res.status(result.statusCode);
                    }
                    res.send(result.body);
                }
            );
        });

        app.use("/api/v3/", function (req, res) {
            analyticsMiddleware.analyticsRawApiMiddleware(
                req,
                function (err, result) {
                    res.setHeader(
                        "Access-Control-Allow-Origin",
                        req.headers.origin || ""
                    );
                    if (!result.body.status) {
                        res.status(result.statusCode);
                    }
                    res.send(result.body);
                }
            );
        });

        app.use("/api", require("./routes/api")); // content source api route

    app.use("/api/v3/", function (req, res) {
        analyticsMiddleware.analyticsRawApiMiddleware(
            req,
            function (err, result) {
                res.setHeader(
                    "Access-Control-Allow-Origin",
                    req.headers.origin || ""
                );
                if (!result.body.status) {
                    res.status(result.statusCode);
                }
                res.send(result.body);
            }
        );
    });

    app.use("/searchAdmin", function (req, res) {
        analyticsMiddleware.searchAdmin(req, function (err, result) {
            res.send(result);
        });
    });

    app.use("/search", function (req, res) {
        analyticsMiddleware.search(req, function (err, result) {
            res.send(result);
        });
    });

    // Marketplace addon APIs (TODO: Make this endpoints secure)
    app.use("/addons", marketplace);
    app.use("/plugins", pluginRoutes);
    app.all("/synonyms/:path", synonyms.index);
    app.use("/searchunifySupport", searchunifySupport);
    app.use("/oauthApiRate", throttleOptions.getRateLimit);
    app.use("/oauthApiRateCount", throttleOptions.getCountOfApis);
    app.use("/oauthAdminApiRate", throttleOptions.fetchOauthLogs);
    app.use("/oauthApiGraphData", throttleOptions.fetchOauthApiLogsGraph);
    app.use("/oauthSearchApiGraphData", throttleOptions.fetchOauthSearchApiLogsGraph);
    app.use("/crons", accessCheck, crons);
    app.use("/scheduledCrons", scheduledCrons);
    app.use("/admin/notifications", admin.notifications);
    app.use("/admin/assetLibrary", admin.assetLibrary);
    app.use("/admin/userManagement", admin.userManagement);
    app.use("/admin/support", admin.support);
    app.use("/admin/version", admin.version);
    app.use("/admin/cron", admin.cron);
    app.use("/admin/jiraOnPrem", admin.jiraOnPrem);
    app.use("/ai", recommender.router);
    app.all("/stopwords/:path", stopwords.index);
    app.all("/stemwords/:path", stemwords.index)
    app.use("/knowledgeGraph/configuration", knowledgeGraph.configuration);
    // app.use("/kcsConfiguration/configuration", kcsConfiguration.configuration);
    app.use("/knowledgeGraph/metaDataGraph", knowledgeGraph.metaDataGraph);
    app.use("/admin/customContentSource", admin.customContentSource);
    app.use("/security", security.router);
    app.use("/advertisements", advertisements.router);
    app.use(
        "/searchCallPermissions/zendesk",
        searchCallPermissions.zendesk.router
    );
    app.use("/admin/hostedSearchUser", admin.hostedSearchUser);
    // app.use('/splitAnalyticsIndex', splitIndex.router);
    app.use("/admin/duplicacyChecker", admin.duplicacyChecker);
    app.use("/pageRating", pageRating.router);
    app.use("/saveFilteredSearches", saveFilteredSearches.router);
    app.use(
        "/admin/getSimilarSearchRecommend",
        admin.getSimilarSearchRecommend
    );
    app.use(
        "/quotaManager",
        admin.quotaManager
    )
    app.use("/communityHelper/configuration", communityHelper.settings);
    app.use("/communityHelper/authorization", communityHelper.authentication);
    app.use("/communityHelper/templates", communityHelper.templates);
    app.use("/communityHelper/sfUtility", communityHelper.sfUtility);
    app.use("/communityHelper/lithiumUtility", communityHelper.lithiumUtility);
    app.use("/analytics/tableauConnector", tableauConnector);
    app.use("/analytics/tableauConnectorRawApi", tableauConnectorRawApi);
    // app.use('/agentHelper', agentHelper);
    // app.use('/crawler/docebo', crawler.docebo.router);
    //--------end of new APIs ----------

    /**
     * For Zendesk search client..
     */
    /**
     * For Zendesk search client..
     */
    app.post("/zendeskClient", function (req, res) {
        var searchClient = req.url.split("uid=")[1].split("&")[0];
        var jwt = require("jsonwebtoken");
        var publicKey = fs.readFileSync(
            "resources/search_clients_custom/" + searchClient + "/public.pem",
            "utf8"
        );
        var decoded = jwt.decode(req.body.token, publicKey, {
            algorithms: ["RS256"],
            audience:
                req.headers.referer.split("/apps/") +
                "/api/v2/apps/installations/161453.json",
        });
        commonFunctions.errorlogger.info(decoded);
        commonFunctions.errorlogger.info("searchClientID is:", searchClient);
        commonFunctions.errorlogger.info(req.query);
        commonFunctions.errorlogger.info(res["[Symbol(outHeadersKey)]"]);
        req.session.userUrl = decoded.sub;
        req.session["my_app_params"] = req.query;
        // req.session.email = data.email;
        res.cookie("my_app_params", req.query, {
            maxAge: 900000,
            httpOnly: true,
        });
        res.redirect(
            "resources/search_clients_custom/" +
                searchClient +
                "/iframe.html?app_guid=" +
                req.query.app_guid +
                "&origin=" +
                req.query.origin
        );
    });

    /**
     * For lightning search client..
     */
    app.get("/mainStaticResource", function (req, res) {
        res.sendFile(
            path.resolve(
                process.cwd(),
                "resources/lightning/MainStaticResource.js"
            )
        );
    });

    app.get("/ChimeraToken.txt", function (req, res) {
        res.sendFile(path.resolve(process.cwd(), "resources/ChimeraToken.txt"));
    });

    /**
     * To check if the image is present in the resources folder.
     * Used for custom icons for search results in client.
     */
    app.get("/getImageAvailability", function (req, res) {
        if (!/^[a-z0-9_\-\s]+$/.test(req.body.folder)) {
            res.status(403).send("Invalid File");
            return;
        }
        if (!/^[a-z0-9_\-\s]+$/.test(req.query.type)) {
            res.status(403).send("Invalid File");
            return;
        }
        fs.access(
            path.resolve(
                process.cwd(),
                "resources/Client_styles/" +
                    req.query.folder +
                    "/" +
                    req.query.type +
                    ".png"
            ),
            fs.constants.R_OK | fs.constants.W_OK,
            (err) => {
                if (err) {
                    res.send({ isAvailable: false });
                } else {
                    res.send({ isAvailable: true });
                }
            }
        );
    });
    app.get("/statusPage", function (req, res) {
        res.send({ isAvailable: true });
    });
    app.use("/pollApi", admin.pollApi);

    app.use(function (err, req, res, next) {
        if(err.code === 'ER_BAD_DB_ERROR'){
            commonFunctions.errorlogger.error(err.message);
            res.status(500).send({status:500, message: "Something went wrong."});
        } else if (err.code === "EBADCSRFTOKEN") {
            if (req.url && req.url.indexOf("/search/socket") != 0)
                commonFunctions.errorlogger.error(err.message);
            res.status(403).send("Tampered request");
        }
        else {
            commonFunctions.errorlogger.error(err);
            commonFunctions.errorlogger.error(err.stack);

            res.status(401);
            if(req.url.includes("/dashboard")){
              res.redirect("/");
            } else {
              res.send(err);
            }
        }
    });

    // sso sign in
    app.use(passport.initialize());
    app.use(passport.session());
    app.use("/saml", ssoSaml);
    featurePublisher();

    var server;

    var startServer = function () {
        // config_1.PORT = 5556;
        if (environment.configuration == "development") {
            server = app.listen(process.env.PORT, function () {
                commonFunctions.errorlogger.info(
                    "Server is listening on port: ",
                    process.env.PORT
                );
                app._working = true;
                console.log(app._startComplete);
                app._startComplete && app._startComplete();
            });
        } else {
            server = http
                .createServer(app)
                .listen(process.env.PORT, function () {
                    commonFunctions.errorlogger.info(
                        "Express server listening on ",
                        process.env.PORT
                    );
                    app._working = true;
                    app._startComplete && app._startComplete();
                });
        }
    };
    startServer();
    const socketIO = io.listen(server);
    socketIO.use(socketAuth);
}

connection_sql
    .handleDisconnect()
    .then(async function (result) {
       await commonFunctions.fetchRateLimits();
       await publishRateLimitCrons();
       await publishStatusPageTokenCrons();
       await publishAbTestDataCrons();
        serveProd();
    })
    .catch(function (err) {
        commonFunctions.errorlogger.error("er", err);
    });

process.on("uncaughtException", (err) => {
    commonFunctions.errorlogger.error(
        "whoops! there was an error",
        new Error(err.stack)
    );
});

exports.serveProd = serveProd;
exports.app = app;
