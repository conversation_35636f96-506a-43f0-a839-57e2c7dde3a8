var express = require('express');
var bodyParser = require('body-parser');
var cookieParser = require('cookie-parser');
var debug = require('debug')('botkit:webserver');
var http = require('http');
var https = require('https');
var hbs = require('express-hbs');
var auth = require('basic-auth');
var fs = require('fs');
environment = require('../../routes/environment');

module.exports = function (controller) {

    var webserver = express();

    webserver.use(function (req, res, next) {
        req.rawBody = '';

        req.on('data', function (chunk) {
            req.rawBody += chunk;
        });

        next();
    });

    webserver.use(cookieParser());
    webserver.use(bodyParser.json());
    webserver.use(bodyParser.urlencoded({ extended: true }));

    webserver.use((req, res, next) => {
        let user = auth(req)
        if (req.path == '/receive')
            next()
        else if (user === undefined || user['name'] !== config.get('slackBot.userName') || user['pass'] !== config.get('slackBot.password')) {
            res.statusCode = 401
            res.setHeader('WWW-Authenticate', 'Basic realm="Node"')
            res.end('Unauthorized')
        } else
            next()
    })

    // set up handlebars ready for tabs
    webserver.engine('hbs', hbs.express4({ partialsDir: __dirname + '/../views/partials' }));
    webserver.set('view engine', 'hbs');
    webserver.set('views', __dirname + '/../views/');

    webserver.use(express.static('public'));
    
    if (environment.configuration == "development") {
        var server = http.createServer(webserver);
        server.listen(config.get('slackBot.port'), function () {
            console.log('Express webserver configured and listening at http://localhost:' + config.get('slackBot.port'));
        });
    } else {
        var options = {
            key: fs.readFileSync('/usr/local/ssl/startCertificates/searchunify.key'),
            cert: fs.readFileSync('/usr/local/ssl/startCertificates/searchunify.crt'),
            ca: [fs.readFileSync('/usr/local/ssl/startCertificates/intermediate.crt'),
            fs.readFileSync('/usr/local/ssl/startCertificates/intermediate1.crt')]
        };
        var server = https.createServer(options, webserver);
        server.listen(config.get('slackBot.port'), function () {
            console.log('Express webserver configured and listening at https://localhost:' + config.get('slackBot.port'));
        });
    }



    // import all the pre-defined routes that are present in /components/routes
    var normalizedPath = require("path").join(__dirname, "routes");
    require("fs").readdirSync(normalizedPath).forEach(function (file) {
        require("./routes/" + file)(webserver, controller);
    });

    controller.webserver = webserver;
    controller.httpserver = server;

    return webserver;

}
