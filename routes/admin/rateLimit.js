const elastic = require("elasticsearch");
var request = require("request");
var redis = require("redis");
var RedisStore = require("express-throttle-redis");
const { getTenantsRateLimitInfo } = require('auth-middleware');
var metadata = require('./../../metadata/mappingForDbAndElastic');
var client = redis.createClient(config.get("redis.redisPort"), config.get("redis.redisHost"));
var cron = require('./../../utils/Cron');
var async = require('async');
var appVariables = require('../../constants/appVariables');
const moment = require('moment');
const {changeDateAccordingToTimezone} = require('../../routes/analytics/serviceMiddleware');
var { updateTenantInfo } = require("../throttleApi/throttle");
var commonFunctions = require('../../utils/commonFunctions');

const getThrottleOptions = (tenantInfo) => {
    appVariables.oauthLog = tenantInfo;
    return {
        "tenantInfo": tenantInfo,
        "period": true,
        "on_throttled": function (req, res, next, bucket) {
            req.rateLimit = bucket;
            res.set("X-Rate-Limit-Limit", appVariables.oauthLog[req.headers['tenant-id']].burst);
            res.set("X-Rate-Limit-Remaining", 0);
            res.set("X-Rate-Limit-Reset", bucket.etime);
            res.status(503).send("Api limit exceeded !!");
        },
        "on_allowed": function (req, res, next, bucket) {
            req.rateLimit = bucket;
            res.set("X-Rate-Limit-Remaining", bucket.tokens);
            res.set("buckets", JSON.stringify(bucket));
            next();
        },
        "cost": function (req) {
            if (req.isoAuthSec) {
                return 1;
            } else {
                return 0;
            }
        },
        "key": function (req) {
            return config.get('adminURL') + req.baseUrl + '?tenantid='+req.headers['tenant-id'];
        },
        "store": new RedisStore(redis.createClient(config.get("redis.redisPort"), config.get("redis.redisHost")))
    }
};


const getCountOfApis = async (req,res) => {
    await changeDateAccordingToTimezone(req);
    let range = {
        "startDate":req.query.startDate,
        "endDate":req.query.endDate
    }

    getSumOfConsumedApi(req, range,(err,data)=>{
        res.send(data);
    })
}

// const getSumOfConsumedApi = (options,callback) =>{
//     let client = new elastic.Client({
//         host: 'http://' + config.get('elasticIndex.host') + ':' + config.get('elasticIndex.port')
//     });

//     let dslForSearch = {
//         "size": 0,
//             "query" :{
//                 "bool": {
//                     "must": []
//                 }
//             },
//             "aggs": {
//             "total": {
//                 "sum": {
//                     "script": "doc['consumedRate'].value"
//                 }
//             }
//         }
//     };

//     let dslForAnalytics = {
//         "size": 0,
//             "query" :{
//                 "bool": {
//                     "must": []
//                 }
//             },
//             "aggs": {
//             "total": {
//                 "sum": {
//                     "script": "doc['consumedRate'].value"
//                 }
//             }
//         }
//     };

//     let dslForContent = {
//         "size": 0,
//             "query" :{
//                 "bool": {
//                     "must": []
//                 }
//             },
//             "aggs": {
//             "total": {
//                 "sum": {
//                     "script": "doc['consumedRate'].value"
//                 }
//             }
//         }
//     };

//     if (options.startDate || options.endDate) {
//         let range = {
//             range:{
//                 ts: {
//                     format: "yyyy-MM-dd HH:mm:ss"
//                 }
//             }
//         };

//         if (options.startDate) {
//             range.range.ts.gte = options.startDate + '||/d';
//         }
//         if (options.endDate) {
//             range.range.ts.lte = options.endDate + '||/d';
//         }

//         dslForSearch.query.bool.must.push(range);
//         dslForAnalytics.query.bool.must.push(range);
//         dslForContent.query.bool.must.push(range);

//     }
    

//     async.auto({
//         searchCount: cb =>{
//             dslForSearch.query.bool.must.push({
//                 "term": {
//                     "object": "Search"
//                 }
//             });
//             client.search({
//                 index: config.get('elasticIndex.oauthLogsIndex'),
//                 type: "oauthLog",
//                 body: dslForSearch
//             }, (error, data) => {
//                 if(error)cb(error,null)
//                 cb(null,data);
//             });
//         },
//         analytics: cb => {
//             dslForAnalytics.query.bool.must.push({
//                 "term": {
//                     "object": "Analytics"
//                 }
//             });
//             client.search({
//                 index: config.get('elasticIndex.oauthLogsIndex'),
//                 type: "oauthLog",
//                 body: dslForAnalytics
//             }, (error, data) => {
//                 if(error)cb(error,null)
//                 cb(null,data);
//             });

//         },
//         contentCount: cb => {
//             dslForContent.query.bool.must.push({
//                 "term": {
//                     "object": "Content Source"
//                 }
//             });
//             client.search({
//                 index: config.get('elasticIndex.oauthLogsIndex'),
//                 type: "oauthLog",
//                 body: dslForContent
//             }, (error, data) => {
//                 if(error)cb(error,null)
//                 cb(null,data);
//             });
//         }
//     },function(error,result){
//         callback(null,{result})

//     })
// }


const getSumOfConsumedApi = (req, option,callback) =>{
    let bodyValue = {
        tenantId: req.headers['tenant-id']
      };

      if (option.startDate && option.endDate) {
        bodyValue.from = option.startDate
        bodyValue.to = option.endDate
      }

      let options = {
          method: 'POST',
          rejectUnauthorized: false,
          url: config.get('analyticsService.url') + '/api-logs/fetchCountByObject',
          body: bodyValue,
          json: true
        };  
        
        request(options, apiLogs.handler(callback))
}

class apiLogs {
    static insert(activity,tenantId, cb) {

        let options = {
          method: 'POST',
          rejectUnauthorized: false,
          url: config.get('analyticsService.url') + '/api-logs/insert',
          body: {
            ts: moment(Date.now()).format("YYYY-MM-DD HH:mm:ss") ,
            consumedRate: activity.consumedRate,
            totalRate: activity.totalRate,
            object: activity.object,
            tenant_id: tenantId
          },
          json: true
        };  

        request(options, apiLogs.handler(cb)) 


    }
    static fetch(option,tenantId, cb) {
        let bodyValue = {
          offset: option.offset || 1,
          limit: option.count || 10,
          sort: "desc",
          tenantId
        };
        if (option.object) {
          bodyValue.object = option.object
        }

        if (option.startDate && option.endDate) {
          bodyValue.from = option.startDate
          bodyValue.to = option.endDate
        }
   
        let options = {
            method: 'POST',
            rejectUnauthorized: false,
            url: config.get('analyticsService.url') + '/api-logs/fetchlogs',
            body: bodyValue,
            json: true
          };  
          
          request(options, apiLogs.handler(cb)) 

    }
    static fetchApiLogs(option,tenantId, cb) {
        let bodyValue = {
          offset: option.offset || 1,
          limit: option.count || 10,
          sort: "desc",
          tenantId
        };
        if (option.object) {
          bodyValue.object = option.object
        }

        if (option.sortType) {
            bodyValue.sortType = option.sortType;
        }

        if (option.timeFormat) {
            bodyValue.timeFormat = option.timeFormat;
        }

        if (option.startDate && option.endDate) {
          bodyValue.from = option.startDate
          bodyValue.to = option.endDate
        }
   
        let options = {
            method: 'POST',
            rejectUnauthorized: false,
            url: config.get('analyticsService.url') + '/api-logs/fetchApiLogs',
            body: bodyValue,
            json: true
          };  
          
          request(options, apiLogs.handler(cb)) 

    }
    static fetchSearchApiLogs(options, cb) {
       
        let requestOptions = {
            method: 'POST',
            rejectUnauthorized: false,
            url: config.get('analyticsService.url') + '/api-logs/search-consumption-trends',
            body: options,
            json: true
          };  
          
          request(requestOptions, apiLogs.handler(cb)) 

    }
    
    static handler(cb) {
        return (error, result) => {
            if (error) {
                console.error(error);
            }
            if (cb)
                cb(error, result);
        };
    }
}

function getRateLimit(req, res) {
    if (req.query.param == "insert") {
        insertOauthLogs(req, res);
    } else {
        fetchOauthLogs(req, res);
    }
}
function insertOauthLogs(req, res) {
    res.send({ flag: 200, message: "Success" });
    let tenantId = req.query['tenant-id'];
    async.auto({
        fetchSearch: cb => {
            client.get(`${config.get('adminURL')}/api/v2_search?tenantid=${tenantId}`, function (error, response) {
                cb(null, response);
            })
        },
        fetchAnalytics: cb => {
            client.get((`${config.get('adminURL')}/api/v2?tenantid=${tenantId}` || `${config.get('adminURL')}/api/v3?tenantid=${tenantId}`), function (error, response) {
                cb(null, response);
            })
        },
        fetchContentSource: cb => {
            client.get(`${config.get('adminURL')}/api/v2_cs?tenantid=${tenantId}`, function (error, response) {
                cb(null, response);
            })
        },
        fetchSearchUnifyGPT: cb => {
            client.get(`${config.get('adminURL')}/api/v2_gpt?tenantid=${tenantId}`, function (error, response) {
                cb(null, response);
            })
        },
        resetLogs: ["fetchSearch", "fetchAnalytics", "fetchContentSource","fetchSearchUnifyGPT", (results, cb) => {
            resetRedisLogs(tenantId, cb)
        }],
        insertLogs: ["fetchSearch", "fetchAnalytics", "fetchContentSource","fetchSearchUnifyGPT", (results, cb) => {
            async.auto({
                insertSearchLogs: callback => {
                    if (results.fetchSearch) {
                        let bucket = JSON.parse(results.fetchSearch);
                        getToken(bucket, tenantId, (error, result) => {
                            bucket = result;
                        })
                        bucket.object = "Search";
                        apiLogs.insert(bucket,tenantId, function (error, result) {
                            callback(null, []);
                        });
                    } else {
                        callback(null, [])
                    }
                },
                insertAnalyticsLogs: callback => {
                    if (results.fetchAnalytics) {
                        let bucket = JSON.parse(results.fetchAnalytics);
                        getToken(bucket, tenantId, (error, result) => {
                            bucket = result;
                        })
                        bucket.object = "Analytics";
                        apiLogs.insert(bucket,tenantId,function (error, result) {
                            callback(null, []);
                        });
                    } else {
                        callback(null, [])
                    }
                },
                insertContentSourceLogs: callback => {
                    if (results.fetchContentSource) {
                        let bucket = JSON.parse(results.fetchContentSource);
                        getToken(bucket, tenantId, (error, result) => {
                            bucket = result;
                        })
                        bucket.object = "Content Source";
                        apiLogs.insert(bucket, tenantId,function (error, result) {
                            callback(null, []);
                        });
                    } else {
                        callback(null, [])
                    }
                },
                insertSearchUnifyGPT: callback => {
                    if (results.fetchSearchUnifyGPT) {
                        let bucket = JSON.parse(results.fetchSearchUnifyGPT);
                        getToken(bucket, tenantId, (error, result) => {
                            bucket = result;
                        })
                        bucket.object = "SearchUnifyGPT";
                        apiLogs.insert(bucket, tenantId,function (error, result) {
                            callback(null, []);
                        });
                    } else {
                        callback(null, [])
                    }
                }
            }, function (error, result) {
                cb(null, [])
            })
        }]
    }, function (error, result) {
        console.log('insertOauthLogs done: err', error,' result', result);
    })
}

function fetchOauthLogs(req, res) {
    let tenantId = req.headers['tenant-id'];
    async.auto({
        fetchSearchLogs: callback => {
            client.get(`${config.get('adminURL')}/api/v2_search?tenantid=${tenantId}`, function (error, response) {
                if (response) {
                    let bucket = JSON.parse(response);
                    bucket.type = "Search";
                    getToken(bucket, tenantId, (error, result) => {
                        bucket = result;
                    })
                    callback(null, bucket);
                }
                else {
                    resetLogs(tenantId, (error, bucket) => {
                        bucket.type = "Search";
                        getToken(bucket, tenantId, (error, result) => {
                            bucket = result;
                        })
                        callback(null, bucket);
                    })
                }
            });
        },
        fetchAnalyticsLogs: callback => {
            client.get(`${config.get('adminURL')}/api/v2?tenantid=${tenantId}` || `${config.get('adminURL')}/api/v3?tenantid=${tenantId}`, function (error, response) {
                if (response) {
                    let bucket = JSON.parse(response);
                    bucket.type = "Analytics";
                    getToken(bucket,tenantId, (error, result) => {
                        bucket = result;
                    })
                    callback(null, bucket);
                }
                else {
                    resetLogs(tenantId, (error, bucket) => {
                        bucket.type = "Analytics";
                        getToken(bucket, tenantId, (error, result) => {
                            bucket = result;
                        })
                        callback(null, bucket);
                    })
                }
            })
        },
        fetchContentSourceLogs: callback => {
            client.get(`${config.get('adminURL')}/api/v2_cs?tenantid=${tenantId}`, function (error, response) {
                if (response) {
                    let bucket = JSON.parse(response);
                    bucket.type = "Content Source";
                    getToken(bucket,tenantId, (error, result) => {
                        bucket = result;
                    })
                    callback(null, bucket);
                }
                else {
                    resetLogs(tenantId, (error, bucket) => {
                        bucket.type = "Content Source";
                        getToken(bucket,tenantId, (error, result) => {
                            bucket = result;
                        })
                        callback(null, bucket);
                    })
                }
            })
        },
        fetchPastLogs: callback => {
            apiLogs.fetch(req.query, tenantId, function (error, result) {
                if (error) {
                    callback(null, []);
                } else {
                    callback(null, result);
                }
            })
        }
    }, function (error, result) {
        res.send({ "currentLogs": [result.fetchAnalyticsLogs, result.fetchSearchLogs, result.fetchContentSourceLogs], "pastLogs": result.fetchPastLogs.body.data })
    })
}

function fetchOauthApiLogsGraph(req, res) {
    let tenantId = req.headers['tenant-id'];
    async.auto({
        fetchPastApiLogs: callback => {
            apiLogs.fetchApiLogs(req.query, tenantId, function (error, result) {
                if (error) {
                    callback(null, []);
                } else {
                    callback(null, result);
                }
            })
        }
    }, function (error, result) {
        res.send({"fetchPastApiLogs": result.fetchPastApiLogs.body.data })
    })
}

function fetchOauthSearchApiLogsGraph(req, res) {
    let tenantId = req.headers['tenant-id'];
    let options = req.body ;
    options.tenantId = tenantId
    async.auto({
        fetchPastSearchApiLogs: callback => {
            apiLogs.fetchSearchApiLogs(options, function (error, result) {
                if (error) {
                    callback(null, []);
                } else {
                    callback(null, result);
                }
            })
        }
    }, function (error, result) {
        res.send({"fetchPastSearchApiLogs": result.fetchPastSearchApiLogs.body.data })
    })
}

function resetLogs(tenantId, cb) {
    const mtime = Date.now();
    let etime = mtime + 60 * 60 * 1000;
    let interval;
    let bucket = {};
    if(appVariables.oauthLog[tenantId]){
        if (appVariables.oauthLog[tenantId].frequency == "min"){
            interval = appVariables.oauthLog[tenantId].interval * 60 * 1000
            etime = mtime + parseInt(interval);
        }
        else{
            interval = appVariables.oauthLog[tenantId].interval * 60 * 60 * 1000;
            etime = mtime + parseInt(interval);
        }
        bucket = { "tokens": appVariables.oauthLog[tenantId].burst, "mtime": mtime, "etime": etime }
    }else{
        commonFunctions.errorlogger.info(`Tenant id ${tenantId} is not configured for API Limits`);
        bucket = { "tokens": 3500, "mtime": mtime, "etime": etime }
    }
    cb(null, bucket);
}

function getToken(bucket, tenantId, cb) {
    if(appVariables.oauthLog[tenantId]){
        bucket.totalRate = appVariables.oauthLog[tenantId].burst;
        bucket.consumedRate = bucket.tokens ? (bucket.totalRate - bucket.tokens) : bucket.totalRate;
    }else{
        commonFunctions.errorlogger.info(`Tenant id ${tenantId} is not configured for API Limits`);
        bucket.totalRate = 3500;
        bucket.consumedRate = bucket.tokens ? ( bucket.totalRate - bucket.tokens) :  bucket.totalRate;
    }
    cb(null, bucket);
}

function resetRedisLogs(tenantId, cb) {
    resetLogs(tenantId, (error, result) => {
        client.set(`${config.get('adminURL')}/api/v2_search?tenantid=${tenantId}`, JSON.stringify(result))
        client.set(`${config.get('adminURL')}/api/v2?tenantid=${tenantId}` || `${config.get('adminURL')}/api/v3?tenantid=${tenantId}`, JSON.stringify(result))
        client.set(`${config.get('adminURL')}/api/v2_cs?tenantid=${tenantId}`, JSON.stringify(result))
        client.set(`${config.get('adminURL')}/api/v2_gpt?tenantid=${tenantId}`, JSON.stringify(result))
        cb(null, []);
    })
}

function updateRateLimit(){
    getTenantsRateLimitInfo().then((result) => {
        commonFunctions.errorlogger.info('Updated Tenant rateLimits: ',result);
        let tenantObj = {};
        for (i in result.data)
            if (Object.keys(result.data[i]).length) tenantObj[i] = result.data[i]
        updateTenantInfo(getThrottleOptions(tenantObj))
    });
};

module.exports = {
    // throttleOptions: throttleOptions,
    getRateLimit: getRateLimit,
    resetRedisLogs: resetRedisLogs,
    getSumOfConsumedApi: getSumOfConsumedApi,
    getCountOfApis:getCountOfApis,
    // setOauthLogsCron: setOauthLogsCron
    getThrottleOptions: getThrottleOptions,
    updateRateLimit,
    fetchOauthLogs,
    fetchOauthApiLogsGraph,
    fetchOauthSearchApiLogsGraph
    
};