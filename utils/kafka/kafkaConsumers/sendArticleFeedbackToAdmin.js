const kafkaLib = require("../kafka-lib");
const config = require('config');
var sanitizeHtml = require('sanitize-html');
const searchunifyEmail = require('../../../Lib/email');
const emailTemplates = require('../../../routes/emailTemplates');
const { getTenantInfoFromTenantId } = require('auth-middleware');
const { tenantSqlConnection  } = require('../../../auth/sqlConnection');
var request = require('request');
let SECRET_KEY;

if (config.has('multiTenantAuthSecret')) { 
    SECRET_KEY = config.get('multiTenantAuthSecret');
    console.log("multiTenantAuthSecret KEY: ", SECRET_KEY);
 } else { 
    SECRET_KEY = 'SECRET_KEY';
    console.log("multiTenantAuthSecret is not defined in the configuration passing static key in sendArticleFeedbackToAdmin.js File:", SECRET_KEY); 
}

function sanitizeObject(obj) {
  Object.keys(obj).forEach(key => {
    if (typeof obj[key] === "string") {
      obj[key] = sanitizeHtml(obj[key], {
        allowedTags: [],
        allowedAttributes: {},
        disallowedTagsMode: 'discard' // Discard disallowed tags completely
      });
    } else if (typeof obj[key] === "object" && obj[key] !== null) {
      sanitizeObject(obj[key]); // Recursively sanitize nested objects
    }
  });
}

const subscribeConsumer = async () => {
try{  
  const articleFeedbackConsumerData = await kafkaLib.getConsumer({
        groupId: `admin ${config.get("kafkaTopic.articleFeedbackToAdmins")}`,
        topic: config.get("kafkaTopic.articleFeedbackToAdmins"),
        fromBeginning: false
    })
    
    await articleFeedbackConsumerData.run({
        eachMessage: async ({
          topic, parition, message, heartBeat
        }) => {
          try {
            let info = JSON.parse(message.value);
            sanitizeObject(info);
            var options = {
              method: 'POST',
              url: `${config.get('authUrl')}/user/getUsers`,
              headers: { 'content-type': 'application/json',
                          'mt-auth-secret': SECRET_KEY },
              body: JSON.stringify({
                  appId: 1,
                  tenantId: info.key
              })
          };
          request(options, function (error, response, body) {

            const rows = JSON.parse(body);
            if (!error) {
              const roleSpecificEmails = rows.filter(r => r.roleId === 1 || r.roleId === 4);
                var adminEmailsArray = roleSpecificEmails.map(item => item.email)
                var emailObject = {
                  to: adminEmailsArray,
                  subject: "Feedback Received",
                  html: emailTemplates.articleFeeback(info.data)
                }
                if(adminEmailsArray.length !== 0) {
                  searchunifyEmail.sendEmailArticleFeedback(adminEmailsArray, emailObject.subject, emailObject.html, (error, resp) => {
                    console.log('Email Sent')
                  });
                }
              }
            })
          } catch (e) {
              console.log(e)
          }
        }
      });
    }
    catch(error){
      if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
        kafkaLib.createTopic(config.get("kafkaTopic.articleFeedbackToAdmins"));
      }
    }
}

module.exports = {
    subscribeConsumer
}