var express = require('express');
var router = express.Router();
var async = require('async');
var request = require("request");
var fs = require('fs');
const fastcsv = require('fast-csv');
var elastic = require('elasticsearch');
const emailTemplates = require('../emailTemplates');
var jira = require('./jira');
var jive = require('./jive');
var drive = require('./drive');
const searchunifyEmail = require('../../Lib/email');
var box = require('./box');
var youtube = require('./youtube');
var skilljar = require('./skilljar')
var stackoverflow = require('./stackoverflow');
var confluence = require('./confluence');
const lithium = require('./lithium');
const zendesk = require('./zendesk');
const helpscout = require('./helpscout');
const cron = require('./../../utils/Cron');
//const crawler = require('./../crawlers');
var objectAndFields = require('./objectAndFields');
//var sap = require('./../crawlers/sap');
const { spawn, exec } = require('child_process');
var path = require('path');
var url = require('url');
// var git = require('git-rev');
var github = require('./github');
var sharepoint = require('./sharepoint');
var slack = require('./slack');
var litmos = require('./litmos');
var moodle = require('./moodle');
var sabaCloud = require('./sabaCloud');
var dropbox = require('./dropbox');
var servicenow = require('./servicenow');
var marketo = require('./marketo');
var archiver = require('archiver');
var dynamics = require('./dynamics');
// var receptive = require('./receptive');
var jiraOnPrem = require('./jiraOnPrem');
const appVariables = require('../../constants/appVariables');
var multipart = require('connect-multiparty');
var multipartMiddleware = multipart();
var csv = require('fast-csv');
// const analytics = require("../analytics/common");
// var socketio = require('socket.io');
let searchResultKafkaConfig = require('../admin/searchResultKafkaConfig');
var commonFunctions = require('./../../utils/commonFunctions');

const moment = require('moment');
const vimeo = require('./vimeo');
const contentSourceConfigurationTokafka = require('./../../routes/admin/contentSourceConfigForKafka');
const higherLogic = require('./higherLogic');
const discourse = require('./discourse');
const constants = require('./../../constants/constants');
const azureDevops = require('./../../routes/admin/azure-devops');
const aha = require('./../../routes/admin/aha');
var crawlerConfigCache = require('../../utils/crawler-config-cache');
var kafkaLib = require('../../utils/kafka/kafka-lib');
const os = require('os');
const docebo = require('./docebo');
var wistia = require('./wistia');
const config = require('config');
const { tenantSqlConnection  } = require('../../auth/sqlConnection');
const crypto = require('crypto');
const { jsErrors } = require('./../../constants/constants');
const { getAccessTokenFromTenantId } = require('auth-middleware');
const kafkaStatusLib = require("./../../utils/kafka-status-page/kafka-lib");
const kafkaAnalyticsPublishers = require('../../utils/kafka/kafkaPublishers/publishAnalytics.js');
const redis = require('redis');
const { listContentSources } = require('../merge-fields/service');
const redisclient = redis.createClient(config.get("redis.redisPort"), config.get("redis.redisHost"));

const oauthUtil = require('../../utils/oAuthConfluenceJiraUtil2.js')

router.get('/getAllSupportedContentSourceTypes', function (req, res, next) {
    let multiTenantCheck = '';
    let disableAddButton = false;
    let data;
    async.auto({
        multiTenantQuery: cb => {
            if(config.get('multiTenantSupport') && config.get('multiTenantSupport') === true){
                multiTenantCheck = ' WHERE multi_tenant_support = 1';
                let maxCsCheckQuery = 'SELECT COUNT(1) as count from content_sources';
                connection[req.headers['tenant-id']].execute.query(maxCsCheckQuery, function (err, result) {
                    if(result && result[0] && result[0].count >= config.get('tenantMaxCsCount')){
                        disableAddButton = true;
                    }
                    cb();
                });
            } else {
                cb();
            }
        },
        getCsTypes: ['multiTenantQuery', (body, cb) => {
            // nutch cs type is not available after opensearch release
            if(!multiTenantCheck){
                multiTenantCheck = ' WHERE id <> 9';
              } else {
                multiTenantCheck += ' AND id <> 9'
              }
            var sqlCS = `SELECT * FROM content_source_types ${multiTenantCheck}`;
            connection[req.headers['tenant-id']].execute.query(sqlCS, function (err, rows) {
                if (err) {
                    commonFunctions.errorlogger.error("Error in fecthing supported content source types");
                }
                data = rows;
                cb();
            });
        }]
    }, (err, resp) => {
        if(err){
            commonFunctions.errorlogger.error("Error in fecthing supported content source types async ", err); 
        }
        return res.send({data, disableAddButton});
    });
});

router.get('/getAllSupportedContentSourceCategories', function (req, res, next) {
    let mtCheckQuery = "";
    if(config.get('multiTenantSupport')){
        // removing search engine type & LMS category
        mtCheckQuery = " WHERE id NOT IN (6)";
    }
    var sqlCS = `SELECT * FROM content_source_categories ${mtCheckQuery}`;
    var asyncTasks = [];
    var q = connection[req.headers['tenant-id']].execute.query(sqlCS, function (err, rows) {
        if (err) {
            commonFunctions.errorlogger.error("Error in fecthing supported content source types");
        } else {
            res.send({ "data": rows });
        }
    });
});

router.get('/getAddedContentSources', function (req, res, next) {
    getAddedContentSources(req,(data, error)=>{
        if(error){
            return res.send({
                error: true,
                message: error
            });
        }
        res.send(data)
    })
});

router.get('/getUrlForAutocompleteScripts',  function (req, res, next) {
    const env =  config.get("instanceName");
    const md5Hash =  env.toLowerCase() === 'production' ? '6f174bdb99619180cda97d876d3c5ce0' : '6886f6d91ed5be1e73b3a8065875ce74'; 
    const uid = env.toLowerCase() === 'production' ? '9f0a900c-f723-11ee-bd6b-3e3f7c426c8e' : '7b34c53d-01fd-11ef-b209-aa86cae4748a'; 
    const cloudfrontURL = env.toLowerCase() === 'production' ? 'https://d24f5uogtvlja2.cloudfront.net' : 'https://d38wq0vgtrpxqz.cloudfront.net'; 
    const scripts = {
        urls: [
            `${cloudfrontURL}/${md5Hash}/search-clients/${uid}/an.js`,
            `${cloudfrontURL}/${md5Hash}/search-clients/${uid}/searchbox.js`
        ],
        styleUrl: `${cloudfrontURL}/${md5Hash}/search-clients/${uid}/searchbox.css`
    };

    res.json(scripts);
    
});

const getAddedContentSources = async (req,callback) =>{
    let sortDir = 'DESC';
    if(req.query.sortDir == 1){
        sortDir = 'ASC';
    }
    let userEmail = req.headers.session.email;
    let userRole = req.headers.session.roleId;

    allowedSUuser = [/grazitti.com/, /searchunify.com/];
    /** Searchunify user will be grazitti.com and searchunify.com */
    let searchUnifyUser = allowedSUuser[0].test(userEmail) || allowedSUuser[1].test(userEmail);
    /**
     * if allAccess is true
     * User will get all edit access on admin panel
     */
    let allAccess = searchUnifyUser || userRole == 4;
    /**Get access control settings */
    let accessControlSettings = (await getDataFromDB(`select * from access_control_settings;`,req))[0].contentSource;

    let ifUserIsScOwnerPermission;
    /** only work for admin and moderator in private access control settings case */
    if (!allAccess && accessControlSettings == 3) {
        /** check if user is owner of any search client */
        ifUserIsScOwnerPermission = (await getDataFromDB(`SELECT count(*) as total from search_clients cs where cs.email = '${userEmail}' ;`,req))[0].total ? true : false;
    }
    var sqlCS = `
    SELECT
    content_sources.id,
    content_sources.name,
    content_sources.label,
    content_sources.elasticIndexName as index_name,
    content_sources.url,
    content_sources.isFileUploaded,
    content_sources.content_source_type_id,
    content_sources.sync_start_date,
    content_sources.sync_frequency,
    content_sources.sync_status,
    content_sources.pid,
    content_sources.last_sync_date,
    content_sources.language,
    content_sources.depth,
    content_sources.sort_order,
    content_sources.sync_frequency_name,
    content_sources.crawl_status,
    content_sources.searchunifyIndexUrl,
    content_sources.logFile,
    content_sources.cron_stuck_count,
    content_sources.email,
    content_sources.adminLogFile,
    content_sources.current_crawl_start_time,
    content_sources.current_crawl_end_time,
    content_sources.current_crawl_mode,
    content_sources.is_paused,
    CASE WHEN current_crawl_mode = 'reindexing'
        THEN 'Manual crawl' 
        WHEN current_crawl_mode = 'update' OR current_crawl_mode = 'syncDeleted'
        THEN 'Automatic crawl'
        WHEN current_crawl_mode = 'object' 
        THEN 'Object crawl'
        ELSE current_crawl_mode END AS crawl_mode,
    content_sources.current_crawl_object_name,
    content_source_types.img,
    content_source_types.name AS cs_type_name,
    content_sources.sharedAccess,
    content_sources.created_by,
    content_sources.last_updated_by,
	content_source_authorization.client_id,
	content_source_authorization.authorization_type,
    COUNT(DISTINCT content_source_objects.name) AS object_count,
    COUNT(DISTINCT cso.name) AS updated_object_count,
    COUNT(DISTINCT content_source_spaces.spaceName) AS space_count,
    SUM(CASE WHEN content_source_spaces.isSelected = 1 THEN 1 ELSE 0 END)  AS isSelected_count
  FROM
    content_sources LEFT JOIN
    content_source_types ON content_sources.content_source_type_id = content_source_types.id
  LEFT JOIN
    content_source_objects ON content_sources.id = content_source_objects.content_source_id
  LEFT JOIN
    content_source_objects cso ON content_sources.id = cso.content_source_id and cso.object_status IS NOT NULL
  LEFT JOIN
    content_source_spaces ON content_sources.id = content_source_spaces.content_source_id
    LEFT JOIN
    content_source_authorization ON content_sources.id = content_source_authorization.content_source_id
  GROUP BY
    content_sources.id
  ORDER BY
    IF(content_sources.last_sync_date='0000-00-00',1,0) ${sortDir},
    content_sources.last_sync_date ${sortDir}, 
    content_sources.id ${sortDir}`;

    var asyncTasks = [];
    var q = connection[req.headers['tenant-id']].execute.query(sqlCS, function (err, rows) {
        if(err){
            commonFunctions.errorlogger.error(`error while getting content sources | ${err}`);
            return callback(null,"Something went wrong while fetching content sources");
        }
        let csWithoutFieldsQuery = `
        SELECT DISTINCT( cs.id ) AS csId 
        FROM   content_sources cs 
               LEFT JOIN content_source_objects cso 
                      ON cso.content_source_id = cs.id 
               LEFT JOIN content_source_object_fields csof 
                      ON csof.content_source_object_id = cso.id 
        WHERE  cso.id IS NOT NULL 
        AND (cs.content_source_type_id IN (?))
               AND csof.id IS NULL;`
               connection[req.headers['tenant-id']].execute.query(csWithoutFieldsQuery,[[...constants.OBJECT_CRAWL, constants.CONTENT_SOURCE_TYPE.file]], function (fieldsErr, csWithoutFields) {
            if(fieldsErr){
                commonFunctions.errorlogger.error(`error while executing csWithoutFieldsQuery | ${fieldsErr}`);
                return callback(null,"Something went wrong while fetching content sources");
            }
        commonFunctions.errorlogger.error("csWithoutFields", csWithoutFields);
        let csIdsWithoutFields = [];
        for(let i = 0; i < csWithoutFields.length; i++){
            csIdsWithoutFields.push(csWithoutFields[i].csId);
        }
        commonFunctions.errorlogger.error("csIdsWithoutFields", csIdsWithoutFields);           
        


        let headers = {
            "Content-Type": "application/json",
            "su-crawler-secret" : config.get("crawler.sharedSecret"),
            "tenant-id": req.headers['tenant-id'],
            "timeout": 10000
        }
        let tenantId = req.headers['tenant-id'];
        const  {tpk} = req.headers['session'];
        commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/content-source/doc-count', {tpk,tenantId},'', headers, function (err, data) {
            data = data || {};
            if(data && data.data){
                data = data.data;
            }
            const docCSMap = new Map();
            if(err){
                commonFunctions.errorlogger.error(`Error while fetching document count`);
                // return callback({ "data": [] });
            }else{
                const { indexCounts } = data;
                if(indexCounts){
                    for(let docIndex=0; docIndex < indexCounts.length; docIndex += 1){
                        docCSMap.set(indexCounts[docIndex].key , indexCounts[docIndex].doc_count);
                        docCSMap.set(indexCounts[docIndex].key+'_mongo' , indexCounts[docIndex].mongo_count);
                    }
                }
            }
            let { tempCsExclusions } = data;
            tempCsExclusions = tempCsExclusions || [];
            for (var i = 0; i < rows.length; i++) {
                rows[i]["count"] = docCSMap.get(rows[i]["index_name"]) || 0;
                rows[i]["tempCount"] =  docCSMap.get(`${rows[i]["index_name"]}___tempcsind`) || docCSMap.get(`${rows[i]["index_name"]}_temp`) || 0;
                if(tempCsExclusions.includes(rows[i].content_source_type_id)){
                    rows[i]["tempCsExclusions"] = true;
                }else{
                    rows[i]["tempCsExclusions"] = false;
                }
                if(csIdsWithoutFields.indexOf(rows[i].id) > -1){
                    rows[i].object_field_count = 0;
                }

                rows[i]["display"] = true;
                rows[i]["editable"] = true;
                rows[i]["shareAccess"] = accessControlSettings == 1 ? false : true;
                if(rows[i]['created_by']){
                    try {
                        let creator = JSON.parse(rows[i]['created_by']);
                        rows[i]['csOwnerName'] = allowedSUuser[0].test(creator['email']) || allowedSUuser[1].test(creator['email'])? 'SearchUnify Team': creator.name;
                    } catch (err) {
                        rows[i]['csOwnerName'] = 'SearchUnify Team'
                    }
                } else {
                    rows[i]['csOwnerName'] = 'User not Found'
                }
                if(rows[i]['last_updated_by']){
                    try {
                        let lastUpdatedUser = JSON.parse(rows[i]['last_updated_by']);
                        rows[i]['last_updated_by'] = allowedSUuser[0].test(lastUpdatedUser.email) || allowedSUuser[1].test(lastUpdatedUser.email)? 'SearchUnify Team': lastUpdatedUser.name;
                    } catch (err) {
                        rows[i]['last_updated_by'] = 'SearchUnify Team'
                    }
                } else {rows[i]['last_updated_by'] = 'User not Found'}
                /**allAccess is true for
                 * For searchunify user/Super admin or settings are public
                 * and will receive edit/delete/clone access to Content source
                */
                if (!allAccess) {
                    /**
                    * Access control settings - accessControlSettings
                    * 1 - Public
                    * 2 - Protected
                    * 3 - Public
                    */
                    if (!(accessControlSettings == 1)) {
                        rows[i]["shareAccess"] = JSON.parse(rows[i].sharedAccess).includes(userEmail) || rows[i]["email"] == userEmail ? true : false;
                        /**
                        * editable column in reposne
                        * if user is owner of SC , Will get Editable acess 
                        */
                        rows[i]["editable"] = userEmail === rows[i]["email"] || rows[i]["shareAccess"] ? true : false;
                        if (accessControlSettings == 3) {
                            /**Private settings- if user is owner of Content source
                            * Will have edit access and only CS with 
                            * edit access will be visible on Admin panel 
                            * but if user is owner of any Search client , user will get view only access
                            * to other content sources
                            */
                            if (!(rows[i]["editable"] || ifUserIsScOwnerPermission)) { rows[i]["display"] = false }
                        }
                    }
                }
                if (rows[i]["content_source_type_id"] == constants.CONTENT_SOURCE_TYPE.youtube || rows[i]["content_source_type_id"] == constants.CONTENT_SOURCE_TYPE.gmail) {
                    rows[i]["isOauthUpdated"] = false;
                    if (rows[i]["authorization_type"]== "OAuth" && (rows[i]["client_id"] ) && (rows[i]["client_id"] != commonFunctions.appVariables.youtube.clientId ) ) {
                        rows[i]["isOauthUpdated"] = true;
                    }
                    else{
                        delete rows[i]["client_id"];
                    }
                }
                if (rows[i]["content_source_type_id"] == constants.CONTENT_SOURCE_TYPE.drive) {
                    rows[i]["isOauthUpdated"] = false;
                    if (rows[i]["authorization_type"]== "OAuth" && (rows[i]["client_id"] ) && (rows[i]["client_id"] != commonFunctions.appVariables.drive.clientId )) {                      
                        rows[i]["isOauthUpdated"] = true;
                    }
                    else {
                        delete rows[i]["client_id"];
                    }
                }

                if(`${rows[i]['sync_frequency']}`.includes(',')) {
                    rows[i]['sync_frequency'] = rows[i]['sync_frequency'].split(',').map(val => Number(val.trim()));
                }
            }

            let filteredRows = rows.filter(row => row.display); // returning only the data to the user which they have access to       

            return callback({ "data": filteredRows });
        });
    });
});
}


router.get('/getObjectsDocCount', function (req, res, next) {
    commonFunctions.errorlogger.info('Get objectwise documents count', JSON.stringify(req.query));
    if (!req.query.content_source_id) {
        res.send({ "error": "Invalid Query Parameters" });
        return;
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/content-source/objects-doc-count?mysqlId=' + req.query.content_source_id + '&indexName=' + req.query.indexName, '', '', headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({ "error": error }).status(400);
        }
        else {
            res.send(result.data);
        }
    });

});

router.get('/getContentSourcesAuthData', function (req, res, next) {
    if (!req.query.content_source_id) {
        res.send({ "error": "Invalid Query Parameters" });
        return;
    }
    var contentSourceId = req.query.content_source_id;
    getContentSourceData(contentSourceId,req, function (err, result) {
        if ([
            constants.CONTENT_SOURCE_TYPE.website,
            constants.CONTENT_SOURCE_TYPE.jsWeb
        ].includes(result.get_content_source_and_auth.contentSource.content_source_type_id) && (result.get_content_source_and_auth.contentSource.isFileUploaded === 1)) {
            var fileUploadUrl = result.get_content_source_and_auth.contentSource.url.split("sitemap/");
            result.get_content_source_and_auth.contentSource.url = fileUploadUrl[fileUploadUrl.length - 1]
        }
        delete result.get_content_source_and_auth.authorization.accessToken
        delete result.get_content_source_and_auth.authorization.instanceURL
        delete result.get_content_source_and_auth.authorization.refreshToken
        delete result.get_content_source_and_auth.authorization.password
        delete result.get_content_source_and_auth.authorization.client_secret
        delete result.get_content_source_and_auth.authorization.privateKey
        delete result.get_content_source_and_auth.authorization.htaccessPassword
        if(result.get_content_source_and_auth.contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.youtube && result.get_content_source_and_auth.authorization.client_id  == commonFunctions.appVariables.youtube.clientId ||
            result.get_content_source_and_auth.contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.gmail && result.get_content_source_and_auth.authorization.client_id  == commonFunctions.appVariables.youtube.clientId ||
        result.get_content_source_and_auth.contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.drive && result.get_content_source_and_auth.authorization.client_id  == commonFunctions.appVariables.drive.clientId){
            delete result.get_content_source_and_auth.authorization.client_id;
            delete result.get_content_source_and_auth.authorization.client_secret;
            
        }
        if (result.get_content_source_and_auth.contentSource.content_source_type_id == 14)
            delete result.get_content_source_and_auth.authorization.client_id;

        if (result.get_content_source_and_auth.contentSource.content_source_type_id ==
            constants.CONTENT_SOURCE_TYPE.azureDevops
            && result.get_object_and_fields
            && result.get_object_and_fields[0]
            && result.get_object_and_fields[0].fields) {
            let fields = result.get_object_and_fields[0].fields;
            for (let i = 0; i < fields.length; i++) {
                if (fields[i].name.indexOf(azureDevops.CUSTOM_FIELD_PREFIX) > -1) {
                    fields[i].third_party_key_name = "[" + azureDevops.AZURE_DEVOPS_FIELDS[fields[i].name] + "]"
                } else if (fields[i].name.indexOf(azureDevops.CHILD_SEPARATOR) > -1) {
                    let pathArr = fields[i].name.split(azureDevops.CHILD_SEPARATOR);
                    pathArr[0] = azureDevops.AZURE_DEVOPS_FIELDS[pathArr[0]]; //changing field name to actual name
                    //fields[i].third_party_key_name = "[" + pathArr.join(".") + "]"; full path  is not required for wiql
                    fields[i].third_party_key_name = "[" + pathArr[0] + "]";
                } else {
                    fields[i].third_party_key_name = azureDevops.AZURE_DEVOPS_FIELDS[fields[i].name]
                        || fields[i].name;

                    fields[i].third_party_key_name = "[" + fields[i].third_party_key_name + "]";
                }
            }

        }
        let isAddFieldValid=true;
        try {

            // crawlerConfigCache.fetchCrawlerConfig({ tenantId: req.headers['tenant-id'], RETRY_COUNT: 1, configKey: 'nonCrawlerFile' }, function(response){
            //     const nonCrawlerFile = response.config_value;
            //     let { csWithoutAddField } = nonCrawlerFile;
            //     let contentSourceTypeId = result.get_content_source_and_auth.contentSource.content_source_type_id;
            //     let csName =  constants.CONTENT_SOURCE_TYPE_NAME[contentSourceTypeId];
            //         if(csWithoutAddField && csWithoutAddField.includes(csName)){
            //             isAddFieldValid = false;
            //         }
            // });

            result.get_content_source_and_auth.contentSource.index_name = result.get_content_source_and_auth.contentSource.elasticIndexName;
            delete result.get_content_source_and_auth.contentSource.elasticIndexName;
    
            res.send({
                "data": {
                    contentSource: result.get_content_source_and_auth.contentSource,
                    authorization: result.get_content_source_and_auth.authorization,
                    objectsAndFields: result.get_object_and_fields,
                    spacesORboards: result.get_spaces,
                    getWebConf: result.getWebConf || {},
                    apiCrawlerFields: result.get_api_crawler_fields,
                    language: result.get_content_source_languages,
                    addField: isAddFieldValid
                }
            });
        } catch(err){
            commonFunctions.errorlogger.error('error in fetching crawler config');
            commonFunctions.errorlogger.error(err);
        }
    });
});

router.post('/getSearchResultForML', async function (req, res, next) {
    const getDetails = await getAccessTokenFromTenantId(req.headers['tenant-id']);
    if (!req.body.content_source_id) {
        res.send({ "error": "Invalid Query Parameters" });
        return;
    }
    var contentSourceId = req.body.content_source_id;
    getContentSourceData(contentSourceId,req, function (err, result) {
        if ([
            constants.CONTENT_SOURCE_TYPE.website,
            constants.CONTENT_SOURCE_TYPE.jsWeb
        ].includes(result.get_content_source_and_auth.contentSource.content_source_type_id) && (result.get_content_source_and_auth.contentSource.isFileUploaded === 1)) {
            var fileUploadUrl = result.get_content_source_and_auth.contentSource.url.split("sitemap/");
            result.get_content_source_and_auth.contentSource.url = fileUploadUrl[fileUploadUrl.length - 1]
        }
        delete result.get_content_source_and_auth.authorization.accessToken
        delete result.get_content_source_and_auth.authorization.instanceURL
        delete result.get_content_source_and_auth.authorization.refreshToken
        delete result.get_content_source_and_auth.authorization.password
        delete result.get_content_source_and_auth.authorization.client_secret
        delete result.get_content_source_and_auth.authorization.privateKey
        if (result.get_content_source_and_auth.contentSource.content_source_type_id == 14)
            delete result.get_content_source_and_auth.authorization.client_id;

        if (result.get_content_source_and_auth.contentSource.content_source_type_id ==
            constants.CONTENT_SOURCE_TYPE.azureDevops
            && result.get_object_and_fields
            && result.get_object_and_fields[0]
            && result.get_object_and_fields[0].fields) {
            let fields = result.get_object_and_fields[0].fields;
            for (let i = 0; i < fields.length; i++) {
                if (fields[i].name.indexOf(azureDevops.CUSTOM_FIELD_PREFIX) > -1) {
                    fields[i].third_party_key_name = "[" + azureDevops.AZURE_DEVOPS_FIELDS[fields[i].name] + "]"
                } else if (fields[i].name.indexOf(azureDevops.CHILD_SEPARATOR) > -1) {
                    let pathArr = fields[i].name.split(azureDevops.CHILD_SEPARATOR);
                    pathArr[0] = azureDevops.AZURE_DEVOPS_FIELDS[pathArr[0]]; //changing field name to actual name
                    //fields[i].third_party_key_name = "[" + pathArr.join(".") + "]"; full path  is not required for wiql
                    fields[i].third_party_key_name = "[" + pathArr[0] + "]";
                } else {
                    fields[i].third_party_key_name = azureDevops.AZURE_DEVOPS_FIELDS[fields[i].name]
                        || fields[i].name;

                    fields[i].third_party_key_name = "[" + fields[i].third_party_key_name + "]";
                }
            }

        }
        let isAddFieldValid = true;
        try {
            result.get_content_source_and_auth.contentSource.index_name = result.get_content_source_and_auth.contentSource.elasticIndexName;
            delete result.get_content_source_and_auth.contentSource.elasticIndexName;
            var finalData = {
                contentSource: result.get_content_source_and_auth.contentSource,
                authorization: result.get_content_source_and_auth.authorization,
                objectsAndFields: result.get_object_and_fields,
                spacesORboards: result.get_spaces,
                getWebConf: result.getWebConf || {},
                apiCrawlerFields: result.get_api_crawler_fields,
                language: result.get_content_source_languages,
                addField: isAddFieldValid
            }
            var selectedCsInfo = {
                index_name: req.body.index_name,
                objectIds: req.body.object_ids|| [],
                fieldSelectedFields: req.body.selectedFields|| [],
                fieldAllFields: req.body.allFields|| [],
                searchString: req.body.searchString
            };
            var csInfo = {
                indexName: [],
                objectName: [],
                selectedFields:[],
                allFields: [],
                searchString: selectedCsInfo.searchString,
                from : req.body.from,
                tenantId:req.headers['tenant-id']
            }
            csInfo.indexName.push(finalData.contentSource.index_name);
            finalData.objectsAndFields.forEach(u => {
                const objIndex = selectedCsInfo.objectIds.findIndex(obj => obj == u.id);
                if (objIndex > -1) {
                    csInfo.objectName.push(u.name);
                    u.fields.forEach(f => {
                        // const fieldIndex = selectedCsInfo.fieldObjId.findIndex(fd => fd == u.id);
                        // if (fieldIndex > -1) {
                            if(selectedCsInfo.fieldSelectedFields.findIndex(fx => fx == f.id) > -1){
                                if (f.isMerged) {
                                    csInfo.selectedFields.push(f.name)
                                } else {
                                    csInfo.selectedFields.push(csInfo.indexName + "___" + u.name + "___" + f.name)
                                }
                            }
                            if(selectedCsInfo.fieldAllFields.findIndex(fx => fx == f.id) > -1){
                                if (f.isMerged) {
                                    csInfo.allFields.push(f.name)
                                } else {
                                    csInfo.allFields.push(csInfo.indexName + "___" + u.name + "___" + f.name)
                                }
                            }
                        // }
                    })
                }
            })
            if (getDetails.accessToken) {
                console.log("getDetails.accessToken ")
                var options = {
                    method: 'POST',
                    rejectUnauthorized: false,
                    url: config.get('searchService.url') + '/search/getResultForML',
                    headers: {
                        'content-type': 'application/json'
                    },
                    body: {
                        "accessToken": getDetails.accessToken,
                        "csInfo": csInfo,
                        "objAndFieldInfo":finalData.objectsAndFields,
                        "getRichSnippet":req.body.getRichSnippet || 0,
                        "getAnnotation":req.body.getAnnotation || 0
                    },
                    json: true
                };
                console.log("OPtions - - ",options)
                request(options, function (err, response, docs) {
                    res.send(response);
                })
            } else {
                res.send({
                    "error": "Not getting access token "
                });
            }
        } catch (err) {
            commonFunctions.errorlogger.error('error in fetching crawler config');
            commonFunctions.errorlogger.error(err);
            res.send({
                "error": err
            });
        }
    });
});

router.get('/getObjectData', function (req, res, next) {
    if (!req.query.object_id) {
        res.send({ "error": "Invalid Query Parameters" });
        res.send([]);
    }

    var q = `SELECT * FROM content_source_objects WHERE id=?`;
    connection[req.headers['tenant-id']].execute.query(q, [req.query.object_id], (err, result) => {
        if (err) {
            console.log('Error while fetching object data', err);
            res.send([]);
        } else {
            res.send(result);
        }
    })
});


const securityCheck = async (req,res,next) => {
    let contentSourceID = req.query.content_source_id || req.body.content_source_id || (req.body && req.body.contentSource && req.body.contentSource.id) || (req.body.contentSource && req.body.contentSource.content_source_id);
    let allowedFurther = await accessCheck(req.headers.session.email, req.headers.session.roleId,contentSourceID,req);
    if (!allowedFurther) {
        res.send({ flag: 401, message: 'Unauthorized' })
    } else {
        next();
    }
}

router.post('/updatecrawlerConfig', securityCheck, function (req, res, next) {
    let reqBody = {
        config_key: req.body.config_key,
        config_value: req.body.config_value
    };
    crawlerConfigCache.updateCrawlerConfig(reqBody, req.headers['tenant-id'], (err, result) => {
        if (err) {
           res.send(err);
        } else {
           res.send(result.data);
        }
    });
});

const updateLastUpdated = (req,res, next) => {
    let contentSourceID = req.query.content_source_id || req.body.content_source_id || (req.body && req.body.contentSource && req.body.contentSource.id) || (req.body.contentSource && req.body.contentSource.content_source_id);
    if(contentSourceID){
        var q = `update content_sources set last_updated_by = '${JSON.stringify({email:req.headers.session.email, name:req.headers.session.name})}' where id = ${contentSourceID}`;
        connection[req.headers['tenant-id']].execute.query(q, [], (err, res) => {
            if (err) {
                console.log('Error while updating Last updated by user for content source', err)
            } else {
                console.log(`Last updated by user updated for ${req.query.content_source_id || req.body.content_source_id || req.body.contentSource.id}`);
                next();
            }
        })
    } else {
        console.log('Last Updated by For content source is bypassed', req.url)
        next();
    }
}

router.post('/getFoldersId', securityCheck, function (req, res, next) {
    commonFunctions.errorlogger.info('storing folder tree', JSON.stringify(req.body));
    var contentSourceId = req.body.contentSourceId;
    if(req.body.contentSourceTypeId){

            var selectedFolders = req.body.treeJson || [];
            var folderArray = [];
            var parentFolderArray = [];
            var contentSourceObjectArr = [];
            for (i = 0; i < selectedFolders.length; i++) {
                folderArray.push(selectedFolders[i].id);
                parentFolderArray.push(selectedFolders[i].parent);
            }
            folderArray = folderArray.filter(function (el) {
                return !parentFolderArray.includes(el);
            });
            selectedFolders = selectedFolders.filter(function (k) {
                return folderArray.includes(k.id);
            });
            for (var i = 0; i < selectedFolders.length; i++) {
                var contentObjectToInsert = {
                    content_source_id: contentSourceId,
                    spaceName: selectedFolders[i].text,
                    spaceUrl: selectedFolders[i].path || '',
                    spaceId: selectedFolders[i].id,
                    isSelected: 1,
                    spaceKey: selectedFolders[i].id_path,
                    spaceType: selectedFolders[i].data.type || '',
                    folderType: selectedFolders[i].data.IsShared == 0 ? 'mine': 'shared'
                };
                if(req.body.contentSourceTypeId === constants.CONTENT_SOURCE_TYPE.dropbox) {
                    contentObjectToInsert.folderType =  selectedFolders[i].data.IsShared == 0 ? 'myfile': 'shared';
                }
                if (req.body.contentSourceTypeId == 12) {
                    if(selectedFolders[i].original 
                        && selectedFolders[i].original.driveId
                        && !["null","undefined"].includes(selectedFolders[i].original.driveId)
                    ) {
                        contentObjectToInsert['folderType'] = selectedFolders[i].original.driveId;
                        contentObjectToInsert['isSelected'] = 0;
                    } else if(selectedFolders[i].data 
                        && selectedFolders[i].data.driveId
                        && !["null","undefined"].includes(selectedFolders[i].data.driveId)
                    ) {
                        contentObjectToInsert['folderType'] = selectedFolders[i].data.driveId;
                        contentObjectToInsert['isSelected'] = 0;
                    } 
                    else {
                        contentObjectToInsert['folderType'] = null;
                    }
                    if (selectedFolders[i].data && selectedFolders[i].data.IsShared == 0) {
                        contentObjectToInsert['isSelected'] = selectedFolders[i].data.IsShared;
                    }
                    contentObjectToInsert['spaceType'] = selectedFolders[i].data.IsFolder;
                }
                if (req.body.contentSourceTypeId == constants.CONTENT_SOURCE_TYPE.aem) {
                    contentObjectToInsert.id = selectedFolders[i].id;
                    contentObjectToInsert.spaceType =  selectedFolders[i].data.IsFolder;
                    delete contentObjectToInsert.folderType;
                }
                contentSourceObjectArr.push(contentObjectToInsert);
            }
            commonFunctions.deleteDriveSpace(contentSourceId,req, function (err, result) {
                if (!err) {
                    commonFunctions.insertSpacesBoards(contentSourceId, contentSourceObjectArr,req,function () {
                        res.send({ msg: "Saved in DB" });
                    })
                }
            })
        
    }
});   

router.post('/getChildFile', securityCheck, function (req, res, next) {
    commonFunctions.errorlogger.info('Get child file', JSON.stringify(req.body));

    var setObj = {
        contentSourceId: req.body.contentSourceId,
        contentSourceTypeId: req.body.contentSourceTypeId,
        fileType: req.body.fileType,
        parent: req.body.parent
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        "timeout": 60000
    }
    if (req.body.driveId) {
        setObj.driveId = req.body.driveId;
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/cs-spaces/get-child-file', '', setObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({ "error": 'Some error occured' }).status(400);
        }
        else {
            res.send({"items":result.data});
        }
    });
});

router.post('/getSharedFolders', securityCheck, function (req, res, next) {
    commonFunctions.errorlogger.info('Get shared folders', JSON.stringify(req.body));

    var setObj = {
        contentSourceId: req.body.contentSourceId,
        contentSourceTypeId: req.body.contentSourceTypeId,
        parent: req.body.parent
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        "timeout": 90000
    }
    if (req.body.driveId) {
        setObj.driveId = req.body.driveId;
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/cs-spaces/get-shared-folders', '', setObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({ "error": 'Some error occured' }).status(400);
        }
        else {
            if(req.body.contentSourceTypeId === constants.CONTENT_SOURCE_TYPE.dropbox){
                res.send({ "entries": result.data });
            }else{
                res.send({ "items": result.data });
            }
        }
    });
});

router.post('/getSharedDrives', securityCheck, function (req, res, next) {
    commonFunctions.errorlogger.info('Get shared drives', JSON.stringify(req.body));

    var setObj = {
        contentSourceId: req.body.contentSourceId,
        contentSourceTypeId: req.body.contentSourceTypeId
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        "timeout": 60000
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/cs-spaces/get-shared-drives', '', setObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({ "error": 'Some error occured' }).status(400);
        }
        else {
                console.log('received from crawler ', result)
                res.send({ "items": result.data });
        }
    });
});

router.post('/getChildFolders', securityCheck, function (req, res, next) {
    commonFunctions.errorlogger.info('Get child folders', JSON.stringify(req.body));
    var setObj = {
        contentSourceId: req.body.contentSourceId,
        contentSourceTypeId: req.body.contentSourceTypeId,
        parent: req.body.parent
    }
    if (req.body && req.body.driveId) {
        setObj.driveId = req.body.driveId;
    }
    if (req.body.contentSourceTypeId === constants.CONTENT_SOURCE_TYPE.moodle) {
        setObj.folderType = req.body.folderType
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        "timeout": 90000
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/cs-spaces/get-child-folders', '', setObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send({ "error": 'Some error occured' }).status(400);
        }
        else {
            if (req.body.contentSourceTypeId === 12 || req.body.contentSourceTypeId == constants.CONTENT_SOURCE_TYPE.aem) {
                res.send({"items":result.data});
            } else {
                res.send({"entries":result.data});
            }
        }
    });
});

router.post('/updateObjectAndFields', securityCheck, async (req, res, next) => {
    const {contentSourceId, objectAndFields, deletedFields} = req.body;

    async.auto({
        getLanguages: function (cb) {
            const sql = "SELECT * from content_source_languages WHERE content_source_id = ?";
            connection[req.headers['tenant-id']].execute.query(sql, [contentSourceId], (err, languages) => {
                if(err){
                    cb(err,null);
                }else{
                    cb(null, languages);
                }       
            });
        },
        getScData: function (cb) {
            let scsql = `SELECT sc.id, sc.name, sc.uid, cs.elasticIndexName as indexName, cso.name as type, sctco.merge_results 
            FROM search_clients AS sc, search_clients_to_content_objects AS sctco, content_source_objects AS cso, content_sources AS cs
            WHERE sctco.search_client_id = sc.id and sctco.content_source_object_id = cso.id and cs.id = cso.content_source_id and cs.id = ?`
            connection[req.headers['tenant-id']].execute.query(scsql, contentSourceId, (err, rows) => {
              if(err){
                cb(err,null);
              }else{
                cb(null, rows);
              }       
            });
        },
        insert_content_source_id: function (cb) {
            let query = 'SELECT * FROM content_sources WHERE id = ?'
            connection[req.headers['tenant-id']].execute.query(query, contentSourceId, (err, rows) => {
                if(err){
                  cb(err,null);
                }else{
                  cb(null, rows[0]);
                }       
            });
        },
        get_object_and_fields: ['insert_content_source_id', function(getDataAbove, cb) {
            commonFunctions.getContentSourceObjectsAndFieldsById(contentSourceId,req, function (err, dataObjectFields) {
                if (!err) {
                    if(commonFunctions.constants.CONTENT_SOURCES_WITH_UNIQUE_FIELD_NAMES
                        .includes(getDataAbove.insert_content_source_id.content_source_type_id)){
                            dataObjectFields = commonFunctions.getFieldsApiName(dataObjectFields);
                        }
                    cb(null, dataObjectFields)
                }
                else
                    cb(err, [])
            })
        }],  
        deleteObject: function (cb) {
            if (deletedFields.length) {
                listContentSources([contentSourceId], req.headers['tenant-id']).then(contentSourceDetails => {
                    let ids = deletedFields.map(x=> x.id ? x.id : 0);
                    let query = `Delete from content_source_object_fields WHERE id in (${ids})`;
                    connection[req.headers['tenant-id']].execute.query(query, [], (err, rows) => {
                        if(err){
                            cb(err,null);
                        }else{
                            kafkaLib.publishMessage({ 
                                topic   : kafkaLib.SU_CRAWLER_TOPIC.contentSourceObjectFields, 
                                messages: [{ 
                                    value : JSON.stringify({type: kafkaLib.KAFKA_EVENT_TYPES.delete, data:{ fields: deletedFields}, tenantId: req.headers['tenant-id'], session: req.headers.session}),
                                    key: req.headers['tenant-id'] 
                                }] 
                            });
                            cb(null, rows[0]);
                        }       
                    });

                    let fieldNames = deletedFields.filter(item => item.id).map(item => ({ field: item.trueKey, id: item.id }));

                    for(const fieldName of fieldNames) {
                        let contentSource = contentSourceDetails.find(item => item.contentSourceObjectFieldId === fieldName.id);

                        if(contentSource) fieldName.indexName = `${contentSource.elasticIndexName}__${contentSource.contentSourceObjectName}`;
                    }

                    kafkaLib.publishMessage({
                        topic: config.get("kafkaTopic.mergeField"),
                        messages: [{
                            value : JSON.stringify({
                                type: kafkaLib.KAFKA_EVENT_TYPES.removeCSField,
                                data:{ fieldNames: fieldNames }, 
                                tenantId: req.headers['tenant-id'],
                                session: req.headers.session
                            }),
                            key: req.headers['tenant-id']
                        }]
                    });
                });
                
            }else{
                cb(null,[])
            }
        },
        insertObject: ['insert_content_source_id',function (getDataAbove,cb) {
            let contentSource = getDataAbove.insert_content_source_id;
            objectAndFieldsArr = [...objectAndFields];
            if ((objectAndFieldsArr.filter(x => x.name == "feeditem").length == 1 && objectAndFieldsArr.filter(x => x.name == "feeditem_1").length != 1) && !contentSource.editing) {
                objectAndFieldsArr.push(JSON.parse(JSON.stringify(objectAndFieldsArr.find(x => x.name == "feeditem"))))
                objectAndFieldsArr[objectAndFieldsArr.length - 1].name = "feeditem_1"
                objectAndFieldsArr[objectAndFieldsArr.length - 1].label = "feeditem 1"
                delete objectAndFieldsArr[objectAndFieldsArr.length - 1].id
            }
            let taskObjects = [];
            for (let o = 0; o < objectAndFieldsArr.length; o++) {
                for(const item of objectAndFieldsArr[o].fields) {
                    if(typeof item.merge_field_id !== 'number') item.merge_field_id = 0;
                }
                taskObjects.push((function (o) {
                    return function (cb) {
                        objectAndFieldsArr[o].content_source_id = contentSource.id
                        commonFunctions.insertObject(objectAndFieldsArr[o], req, function (err, insertResult) {

                            if (!objectAndFieldsArr[o].id) {
                                objectAndFieldsArr[o].id = insertResult.id;
                                // First time no feilds
                                if (!objectAndFieldsArr[o].fields.length)
                                    objectAndFieldsArr[o].fields = commonFunctions.metadata[contentSource.content_source_type_id.toString()] ? commonFunctions.metadata[contentSource.content_source_type_id.toString()].fields : []
                            }

                            //Inserting new Fields
                            if (objectAndFieldsArr[o].fields.length) {
                                commonFunctions.insertFields(getDataAbove, objectAndFieldsArr[o], req, function (err, responseObject) {
                                    if (!err) {
                                        objectAndFieldsArr[o].fields = responseObject
                                        cb(null, objectAndFieldsArr);
                                    }else
                                        cb(err, [])
                                })
                            } else {
                                cb(null, []);
                            }
                        });
                    }
                })(o))
            }
            async.parallel(taskObjects, function (err, result) {
                if (!err)
                    cb(null, objectAndFieldsArr)
                else 
                    cb(err, [])
            })
        }],
        insert_content_source_objects_and_fields_elastic: ['getLanguages', 'getScData', 'insertObject', 'get_object_and_fields', function (getDataAbove, cb) {
            let configObj = {
                contentSourceId, objectAndFields, deletedFields, session: req.headers['session']
            };
            let result = {}
            const obAndFields = getDataAbove.get_object_and_fields;
            result.objectAndFields = getDataAbove.insertObject;
            result.cs = getDataAbove.insert_content_source_id;
            console.log('-----------------------', JSON.stringify(result));
            checkForReIndex(obAndFields, result.objectAndFields, result.cs, req, async (errO, resO) => {
                if (!errO) {
                    result.objectAndFields.forEach(async (O) => {
                        const objStatus = await getObjectStatus(O.id, req);
                        O.object_status = objStatus[0].object_status;
                    });
                }

                var objectFieldsArr = JSON.parse(JSON.stringify(result.objectAndFields));
                objectFieldsArr.forEach(O => {
                    for (var m = 0; m < O.fields.length; m++) {
                        let param = commonFunctions.getObjectFieldNames(result.cs.content_source_type_id, O.fields[m], "name");
                        if (param)
                            O.fields[m].name = result.cs.elasticIndexName + "___" + O.name + "___" + O.fields[m].name;
                    }
                });

                configObj.objectAndFields = objectFieldsArr;
                configObj.contentSource = result.cs;
                let elasticConfigObj = {
                    contentSource: result.cs,
                    languagesData: getDataAbove.getLanguages,
                    scData: getDataAbove.getScData,
                    objectsAndFields: objectFieldsArr,
                    session: req.headers["session"]
                };

                kafkaLib.publishMessage({
                    topic: kafkaLib.SU_CRAWLER_TOPIC.contentSourceObjectFields,
                    messages: [{
                        value: JSON.stringify({ type: kafkaLib.KAFKA_EVENT_TYPES.update, data: configObj, tenantId: req.headers['tenant-id'], session: req.headers.session}),
                        key: req.headers['tenant-id']
                    }]
                });
                let headers = {
                    "Content-Type": "application/json",
                    "su-crawler-secret": config.get("crawler.sharedSecret"),
                    "tenant-id": req.headers['tenant-id']
                }
                commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content/create-mapping', '', elasticConfigObj, headers, function (error, result0) {
                    if (error)
                        commonFunctions.errorlogger.error("Error found in create elastic mapping: ", error);


                    findUidAndSendSCSettings(contentSourceId, req, function (errorKafka, dataKafka) {
                        if (errorKafka)
                            res.send(errorKafka);
                        else {
                            result.cs.index_name = result.cs.elasticIndexName;
                            delete result.cs.elasticIndexName;
                            if (result.cs.content_source_type_id == 3 ) {
                                for (let o = 0; o < result.objectAndFields.length; o++) {
                                    result.objectAndFields[o].fields.forEach((field) => {
                                        const regexForUnderscore = /(?:[^_]_){1}[^_]*$/;
                                        if (field.name.toLowerCase().includes('_') && !field.name.toLowerCase().endsWith('__c') 
                                        && (!field.name.toLowerCase().includes('__') || (field.name.toLowerCase().includes('__') && regexForUnderscore.test(field.name)))
                                        && field.name.toLowerCase().slice(-5) !== '_flat' && field.name.toLowerCase().slice(-10) !== '_flat_name'
                                            && field.name.toLowerCase().slice(-11) !== '_navigation' && field.name.toLowerCase().slice(-7) !== '_nested' && field.name.toLowerCase().includes("__typecasted__")
                                        && !field.name.toLowerCase().includes('attachment_') && !field.annotated) {
                                            const lastIndexOfDoubleUnderscore = field.name.lastIndexOf('__');
                                            field.name = field.name.substring(0, lastIndexOfDoubleUnderscore) + field.name.substring(lastIndexOfDoubleUnderscore).replace(/(?<!_)_(?!_)/g, '.');
                                        }
                                        field.name =  field.name.replace(/(__r)_/g, '$1.');
                                    })
                                }
                            }
                            res.send(result);
                        }
                    });
                })
            });
        }]
    }, function (err, result) {
      callback(err, result);
    })
})

router.post('/updateFieldsCondition', securityCheck, async(req, res) => {
    const { data , id , csTypeId }   = req.body
    let tempdata = {}
    try {
        tempdata = JSON.parse(data)
    } catch (err) {
        return res.status(404).send({ error:true, message: 'JSON' + err })
    }
    
    if (Object.keys(tempdata).length != 0) {
        if (!Object.keys(tempdata).includes('query_json') || !Object.keys(tempdata).includes('query_string')) {
            return res.send({error:true , message: 'Condition field is invalid'})            
        }
    }
    if(id == 0 || id === undefined){
        return res.send({ error:true, message: 'Id cant be NULL' })  
    }

    const fieldConditionQuery = 'UPDATE content_source_objects SET field_conditions = ? WHERE id =?';
    connection[req.headers['tenant-id']].execute.query(fieldConditionQuery, [data, id], function (err, result) {
        if (err) {
            commonFunctions.errorlogger.error("Error found: ", err);
            return res.send({error: true , message: 'http response '+err});
        }
        else{
          const sql = "Select * from content_source_objects where id=?"
          connection[req.headers['tenant-id']].execute.query(sql, [id], function (err, resultFields) {
              if (resultFields){
                const kafkaData  = {csTypeId, resultFields}
                kafkaData.fieldCondition = true;
                kafkaLib.publishMessage({ 
                  topic   : kafkaLib.SU_CRAWLER_TOPIC.contentSourceObjectFields, 
                  messages: [{ 
                      value : JSON.stringify({ type:kafkaLib.KAFKA_EVENT_TYPES.update, data: kafkaData, tenantId: req.headers['tenant-id'], session: req.headers.session}),
                      key: req.headers['tenant-id']
                  }]
              });
              }
              })
            return res.status(200).send({ error: false, message: "done" });
        }
    });
});






router.post('/content-source/fields/check', securityCheck, async (req, res, next) => {
    var configObj = req.body;
    delete configObj.appId;
    configObj.tenantId = req.headers['tenant-id'];
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/fields/check', '', configObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else
            res.send(result);
    });
});

router.post('/content-source/fields/create', securityCheck, async (req, res, next) => {
    var configObj = req.body;
    configObj.tenantId = req.headers['tenant-id'];
    configObj.session = req.headers['session'];
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/fields/create', '', configObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else
            res.send(result);
    });
});

router.post('/content-source/updateNewFieldMapping', securityCheck, async (req, res, next) => {
    let data = req.body.data;
    Object.assign(data, req && req.headers);
    data['tenantId'] = req.headers['tenant-id'];
    data['tenant-Id'] = req.headers['tenant-id'];

    kafkaLib.publishMessage({
        topic: kafkaLib.SU_CRAWLER_TOPIC.contentSource,
        messages: [{
            value: JSON.stringify({ type: 'ADD', data, "tenant-id": req.headers['tenant-id'] })
        }]
    });
    res.status(200).send({ error: false, message: "Update new Field Mapping Event Published Successfully" });
});

router.post('/content-source/updateField', securityCheck, async (req, res, next) => {
    var configObj = req.body;
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("suCrawler.secret"),
        "tenant-id": req.headers['tenant-id']
    }
    commonFunctions.httpRequest('POST', config.get("suCrawler.suCrawlerUrl") + '/content-source/fields/data/updateField', '', configObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else
            res.send(result);
    });
});

router.post('/content-source/fetchLanguageforCS', securityCheck, async (req, res, next) => {
    let id = req.body.cs_Id
    await commonFunctions.getLanguageForContentSource(id, (err, lang) => {
        if (err) {
            console.log("Error while fetching languages:", err);
            res.send(err);
        } else {
            console.log("Final Lang Is:", lang)
            res.status(200).send({ error: false, message: lang });
        }
    },req)
});

router.post('/content-source/fields/delete', securityCheck, async (req, res, next) => {
    var configObj = req.body;
    configObj.tenantId = req.headers['tenant-id'];
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/fields/delete', '', configObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else
            res.send(result);
    });
});

router.post('/content-source/fields/data/update', securityCheck, async (req, res, next) => {
    var configObj = req.body;
    
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/fields/data/update', '', configObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else
            res.send(result);
    });
});

router.post('/fetchFrequencyFields', securityCheck, async (req, res, next) => {
    const {content_source_type_id} = req.body;
    var configObj = req.body.csId? {csId: req.body.csId}: { content_source_type_id }
    
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/content-source/frequency-fields/get', '', configObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error foundssss: ", error);
            res.send({error: true, msg: error});
        }
        if(result.status) {
           res.send(result);
        }else {
            res.send({error: true, msg: result.data});
        }
    });
})

router.post('/fetchCSConfig', securityCheck, async (req, res, next) => {
    const {content_source_type_id} = req.body;
    let response = {
        "success": false
    }

    let params      = [];
    let queryString = `SELECT * FROM content_source_component WHERE 1`;

    if(content_source_type_id) {
      queryString += ` and content_source_component.content_source_type_id = ?`;
      params.push(content_source_type_id);
    }

    connection[req.headers['tenant-id']].execute.query(queryString, params, function (err, result) {
            if (result && result.length > 0){
                if (content_source_type_id && content_source_type_id == 17) {
                    crawlerConfigCache.fetchCrawlerConfig({ tenantId: req.headers['tenant-id'], RETRY_COUNT: 1, configKey: 'youtube' }, function(resp){
                        const youtubeConfig = resp.config_value;
                        if (youtubeConfig.playlist && youtubeConfig.playlist.crawlPlaylist){
                            result[0].spaceLabel = 'By Playlist';
                        }
                        response.success = true;
                        response.data = result;
                        return res.send(response);
                    });
                }else{
                    response.success = true;
                    response.data = result;
                    res.send(response);
                }
            }
    });
});


const getDatafromTenantHash = async (req,res,next) => {
    const {tenantHash} = req.params;
        let options = {
            method: 'GET',
            headers: {
                'content-type': 'application/json'
            },
            qs: {
                tenantHash
            },
            url: config.get('authUrl') + '/tenant/getTenantInfoFromTenantHash',
            json: true
          };  
          request(options,function(err,response){
            if(err || !response.body.length){
                console.log('error in getting data from tenantHash ',err)
            return res.send('invalid event request');
            }else{
            [req.tenantData] = response.body
            next();
        }
        })
}


router.post('/subscriptions/slack/:csId/:tenantHash' ,async (req, res, next) => {
    const {tenantHash, csId} = req.params;
    const {body} = req;
    req.body = { ...req.params, ...req.body }
    if(tenantHash && csId){
        if(body.type === 'url_verification'){
            if(body.challenge){
                return res.send({challenge:body.challenge});
            }
        }
                let headers = {
                    "Content-Type": "application/json",
                    "su-crawler-secret" : config.get("crawler.sharedSecret"),
                    "tenant-hash": tenantHash
                }
                commonFunctions.errorlogger.info('slack event hits');

            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/slack', '', req, headers, function (error, result) {
                if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                res.send(error);
                }
                else {
                    res.send(result);
                }
            })
            }
});

router.post('/subscriptions/helpscout/:csId/:tenantHash', async (req, res) => {
    const { csId, tenantHash } = req.params;
    req.body    = { ...req.body, ...req.params };
    if(!tenantHash && !csId){
        return res.status(404).send('required params not avaliable');
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": tenantHash,
        "X-Helpscout-Signature": req.header('X-Helpscout-Signature'),
        "X-Helpscout-Event": req.header("X-Helpscout-Event")
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/helpscout', '', req, headers, async function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            return res.status(502).send('Internal server error');
        }
        else {
            if(result && result.data !== 'failed'){
                return res.send("helpscout webhook event consumed successfully");
            } else {
                return res.status(502).send("error while consuming helpscout webhook event");
            }
        }
    })
});

router.post('/subscriptions/zendesk/:csId/:tenantHash', async (req, res) => {
    const { csId, tenantHash } = req.params;
    req.body    = { ...req.body, ...req.params };
    if(!tenantHash && !csId){
        return res.status(404).send('required params not avaliable');
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": tenantHash
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/zendesk', '', req, headers, async function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            return res.status(502).send('Internal server error');
        }
        else {
            if(result && result.data !== 'failed'){
                return res.send("zendesk webhook event consumed successfully");
            } else {
                return res.status(502).send("error while consuming zendesk webhook event");
            }
        }
    })
});

router.post('/subscriptions/wistia/:csId/:tenantHash', getDatafromTenantHash, function (req, res) {
    const headers = {
        'Content-Type': "application/json",
        'su-crawler-secret': config.get("crawler.sharedSecret"),
        'x-wistia-signature':  req.header('x-wistia-signature'),
        'tenant-id': req.params.tenantHash
    };
    req.body = { params: { ...req.params }, ...req.body };
    delete req.body.appId;
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/wistia', '', req.body, headers, function (error, result) {
        if (!error) {
            commonFunctions.errorlogger.info("result found: ", result);
            return res.send(result);
        }
        commonFunctions.errorlogger.error("Error found: ", error);
        res.send(error);
    });
});

router.post('/subscriptions/github/:csId/:tenantHash', getDatafromTenantHash, async (req, res, next) => {
    const { csId, tenantHash } = req.params;
    const { body } = req;
    let tenantData = req.tenantData;
    req.body    = { ...req.body, ...req.params };
     if(!tenantHash && !csId){
            return res.status(404).send('required params not avaliable');
        }
                let headers = {
                    "Content-Type": "application/json",
                    "su-crawler-secret" : config.get("crawler.sharedSecret"),
                    "tenant-id": tenantHash
                }
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/github', '', req, headers, async function (error, result) {
                if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                return res.status(502).send('Internal server error');
                }
                else {
                    if(result && result.data !== 'failed'){
                        await tenantSqlConnection(tenantData.tenant_id, tenantData.database_name);
                        if(result.data && result.data.repositoryEvent){
                            const {mysqlId} = result.data;
                            switch(result.data.repositoryEvent){
                                case 'deleted':{
                                    const sql = "DELETE FROM content_source_spaces WHERE id=?"
                                    connection[tenantData.tenant_id].execute.query(sql, [mysqlId], function (err, resultFields) {
                                        if (resultFields){
                                            console.log('deleted repo in db', resultFields);
                                        }
                                    })
                                    break;
                                }
                                case 'renamed':{
                                    const sql = "UPDATE content_source_spaces SET spaceName=?,spaceType=? WHERE id=?"
                                    connection[tenantData.tenant_id].execute.query(sql, [body.repository.name,body.repository.full_name, mysqlId], function (err, resultFields) {
                                        if (resultFields){
                                            console.log('renamed repo in db', resultFields);
                                        }
                                    })
                                    break;
                                }
                                default:{
                                    break;
                                }
                            }
                        }
                        return res.send("github webhook event event consumed successfully");
                    } else {
                        return res.status(502).send("error while consuming github webhook event");
                    }
                }
            })
            
});

router.post('/subscriptions/jira/:csId/:tenantHash', getDatafromTenantHash, async (req, res, next) => {
    const { csId, tenantHash } = req.params;
    const { body } = req;
    let tenantData = req.tenantData;
    req.body    = { ...req.body, ...req.params };
     if(!tenantHash && !csId){
        return res.status(404).send('required params not avaliable');
        }
                let headers = {
                    "Content-Type": "application/json",
                    "su-crawler-secret" : config.get("crawler.sharedSecret"),
                    "tenant-id": tenantHash
                }
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/jira', '', req, headers, async function (error, result) {
                if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                return res.status(502).send('Internal server error');
                }
                else {
                    if(result && result.data !== 'failed'){
                        await tenantSqlConnection(tenantData.tenant_id, tenantData.database_name);
                        if(body.webhookEvent){
                            const { mysqlId } = result.data;
                            switch(body.webhookEvent){
                                case 'project_soft_deleted':
                                case 'project_deleted': {
                                    const sql = "DELETE FROM content_source_spaces WHERE id=?"
                                    connection[tenantData.tenant_id].execute.query(sql, [mysqlId], function (err, resultFields) {
                                        if (resultFields){
                                            console.log('deleted project in db', resultFields);
                                        }
                                    })
                                    break;
                                }
                                default:{
                                    break;
                                }
                            }
                        }
                        return res.send("jira webhook event event consumed successfully");
                    } else {
                        return res.status(502).send("error while consuming github webhook event");
                    }
                }
            })
            
});

router.post('/subscriptions/higherLogic/:csId/:tenantHash', getDatafromTenantHash, async (req, res, next) => {
    const {tenantHash, csId} = req.params;
    let tenantData = req.tenantData;
    req.body    = { ...req.body, ...req.params };
    if(tenantHash && csId){
                let headers = {
                    "Content-Type": "application/json",
                    "su-crawler-secret" : config.get("crawler.sharedSecret"),
                    "tenant-hash": tenantHash
                 }
                commonFunctions.errorlogger.info('higherLogic event hits');
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/higherLogic', '', req, headers, function (error, result) {
                if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                res.send(error);
                }
                else {
                    if(req.body && req.body.ActivityCode){
                        const { ActivityCode } = req.body;
                        let data = req.body;
                        switch (ActivityCode){
                            case ('CommunityUpdate'):{
                                const sql = "UPDATE content_source_spaces SET spaceName=?, WHERE id=?";
                                connection[tenantData.tenant_id].execute.query(sql, [data.Title, result.mysqlId], function (err, resultFields) {
                                    if (resultFields){
                                        console.log('renamed community in db', resultFields);
                                    }
                                })
                                break;
                                }
                            case ('CommunityDelete'):{
                                const sql = "DELETE from content_source_spaces WHERE id=?";
                                connection[tenantData.tenant_id].execute.query(sql, [result.mysqlId], function (err, resultFields) {
                                    if (resultFields){
                                        console.log('deleted community from db', resultFields);
                                    }
                                })
                                break;
                            }
                        }
                    }
                    delete result.mysqlId;
                    res.send(result);
                }
            })
            }
});

router.post('/subscriptions/servicenow/:csId/:tenantHash',async (req, res, next) => {
    const {tenantHash, csId} = req.params;
    req.body    = { ...req.body, ...req.params };
    if(tenantHash && csId){
        let headers = {
            "Content-Type": "application/json",
            "su-crawler-secret" : config.get("crawler.sharedSecret"),
            "tenant-hash": tenantHash
        }
        commonFunctions.errorlogger.info('servicenow event hits');
        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/servicenow', '', req, headers, function (error, result) {
            if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                res.send('failed');
            }
            else {
                if(result.data!== 'failed') {
                    return res.send("servicenow webhook event consumed successfully");
                }else {
                    return res.status(502).send("error while consuming servicenow webhook event");
                }
            }
        })
    }
});

router.post('/subscriptions/aha/:csId/:tenantHash', getDatafromTenantHash, async (req, res, next) => {
    const { csId, tenantHash } = req.params;
    const { body } = req;
    let tenantData = req.tenantData;
    req.body    = { ...req.body, ...req.params };
     if(!tenantHash && !csId){
        return res.status(404).send('required params not avaliable');
        }
                let headers = {
                    "Content-Type": "application/json",
                    "su-crawler-secret" : config.get("crawler.sharedSecret"),
                    "tenant-id": tenantHash
                }
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/aha', '', req, headers, async function (error, result) {
                if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                return res.status(502).send('Internal server error');
                }
                else {
                    if(result && result.data !== 'failed'){
                        await tenantSqlConnection(tenantData.tenant_id, tenantData.database_name);
                        if(body.audit.auditable_type === 'project'){
                            const { mysqlId } = result.data;
                            switch(body.audit.audit_action){
                                case 'destroy': {
                                    const sql = "DELETE FROM content_source_spaces WHERE id=?"
                                    connection[tenantData.tenant_id].execute.query(sql, [mysqlId], function (err, resultFields) {
                                        if (resultFields){
                                            console.log('deleted project in db', resultFields);
                                        }
                                    })
                                    break;
                                }
                                default:{
                                    break;
                                }
                            }
                        }
                        return res.send("aha webhook event consumed successfully");
                    } else {
                        return res.status(502).send("error while consuming aha webhook event");
                    }
                }
            })
            
});

router.post('/subscriptions/khoros/:csId/:tenantHash', async (req, res, next) => {
    const { csId, tenantHash } = req.params;
    req.body    = { ...req.body, ...req.params };
     if(!tenantHash && !csId){
        return res.status(404).send('required params not avaliable');
        }
                let headers = {
                    "Content-Type": "application/json",
                    "su-crawler-secret" : config.get("crawler.sharedSecret"),
                    "tenant-id": tenantHash,
                    "timeout": 100000
                }
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/khoros', '', req, headers, async function (error, result) {
                console.log(result)
                if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                return res.status(502).send('Internal server error');
                }
                else {
                    if(result && result.data !== 'failed'){
                        return res.send("khoros webhook event consumed successfully");
                    } else {
                        return res.status(502).send("error while consuming khoros webhook event");
                    }
                }
            })
            
});

router.post('/subscriptions/confluence/:csId/:tenantHash', getDatafromTenantHash, async (req, res, next) => {
    const {tenantHash, csId} = req.params;
    let tenantData = req.tenantData;
    req.body    = { ...req.body, ...req.params };
    if(tenantHash && csId){
                let headers = {
                    "Content-Type": "application/json",
                    "su-crawler-secret" : config.get("crawler.sharedSecret"),
                    "tenant-hash": tenantHash
                 }
                commonFunctions.errorlogger.info('higherLogic event hits');
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/confluence', '', req, headers, function (error, result) {
                if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                res.send(error);
                }
                else {
                    if(result && result.data !== 'failed'){
                        return res.send("confluence webhook event consumed successfully");
                    } else {
                        return res.status(502).send("error while consuming confluence webhook event");
                    }
                }
            })
            }
});


router.get('/subscriptions/box/:csId/:tenantHash', getDatafromTenantHash, async (req, res, next) => {
    const { csId, tenantHash } = req.params;
    const { query } = req;
    let tenantData = req.tenantData;
    req.body    = { ...req.query, ...req.params };
    if(!tenantHash && !csId){
        return res.status(404).send('required params not avaliable');
        }
            let headers = {
                "Content-Type": "application/json",
                "su-crawler-secret" : config.get("crawler.sharedSecret"),
                "tenant-id": tenantHash,
                "timeout": 10000
            }
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/box', '', req, headers, async function (error, result) {
                if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                    return res.status(502).send('Internal server error');
                }
                else {
                    if(result && result.data !== 'failed'){
                        await tenantSqlConnection(tenantData.tenant_id, tenantData.database_name);
                        if(query.webhookEvent){
                            switch(query.webhookEvent){
                                case 'deleted': {
                                    const { mysqlId } = result.data;
                                    if(mysqlId){
                                        const sql = "DELETE FROM content_source_spaces WHERE id=?"
                                        connection[tenantData.tenant_id].execute.query(sql, [mysqlId], function (err, resultFields) {
                                            if (resultFields){
                                                console.log('deleted folder in db', resultFields);
                                            }
                                        })
                                    }
                                    break;
                                }
                                default:{
                                    break;
                                }
                            }
                        }
                        return res.send("box webhook event consumed successfully");
                    } else {
                        return res.status(502).send("error while consuming box webhook event");
                    }
                }
            })
});

router.post('/subscriptions/docebo/:csId/:tenantHash', getDatafromTenantHash, async (req, res, next) => {
    const { csId, tenantHash } = req.params;
    req.body = { ...req.body, ...req.params };
    if (!tenantHash && !csId) {
        return res.status(404).send('required params not avaliable');
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": tenantHash
    }

    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/docebo', '', req, headers, async function (error, result) {
        console.log(result)
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            return res.status(502).send('Internal server error');
        } else {
            if (result && result.data !== 'failed') {
                return res.send("docebo webhook event consumed successfully");
            } else {
                return res.status(502).send("error while consuming docebo webhook event");
            }
        }

    })
});
// webhook for Vanilla
router.post('/subscriptions/vanilla/:csId/:tenantHash', async (req, res, next) => {
    const { csId, tenantHash } = req.params;
    req.body    = { ...req.body, ...req.params };
     if(!tenantHash && !csId){
        return res.status(404).send('required params not avaliable');
        }
                let headers = {
                    "Content-Type": "application/json",
                    "su-crawler-secret" : config.get("crawler.sharedSecret"),
                    "tenant-id": tenantHash,
                    "timeout": 100000
                }
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/vanilla', '', req, headers, async function (error, result) {
                if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                return res.status(502).send('Internal server error');
                }
                else {
                    if(result && result.data !== 'failed'){
                        return res.send("vanilla event consumed successfully");
                    } else {
                        return res.status(502).send("error while consuming vanilla webhook event");
                    }
                }
            })
            
});


router.post('/subscriptions/insided/:csId/:tenantHash',getDatafromTenantHash,async (req,res,next) => {
    const {csId, tenantHash} = req.params;
    let tenantData = req.tenantData;
    if(!tenantHash && !csId) {
        return res.status(404).send('required params not avaliable');
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": tenantHash,
        "timeout": 10000
    };
    commonFunctions.httpRequest('POST',config.get("crawler.crawlerUrl") + '/content-source/subscriptions/insided','',req,headers,async function(error,result) {
        if(error) {
            commonFunctions.errorlogger.error("Error found: ",error);
            return res.status(502).send('Internal server error');
        }
        else {
            if(result && result.data !== 'failed') {
                await tenantSqlConnection(tenantData.tenant_id,tenantData.database_name);
                return res.send("Insided webhook event consumed successfully");
            } else {
                return res.status(502).send("Error while consuming insided webhook event");
            }
        }
    });
});

router.post('/subscriptions/freshservice/:csId/:tenantHash',getDatafromTenantHash, async (req, res, next) => {
    const {tenantHash, csId} = req.params;
    console.log(tenantHash, csId);
    req.body = { ...req.body, ...req.params };
    console.log(req.body);
    if(tenantHash && csId){
        let headers = {
            "Content-Type": "application/json",
            "su-crawler-secret" : config.get("crawler.sharedSecret"),
            "tenant-id": tenantHash,
            "timeout": 10000
        }
        commonFunctions.errorlogger.info('freshservice event hits');
        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/freshservice', '', req, headers, function (error, result) {
            if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                return res.status(502).send('Internal server error');
            }
            else {
                if(result.data !== 'failed') {
                    return res.send("freshservice webhook event consumed successfully");
                } else {
                    return res.status(502).send("error while consuming freshservice webhook event");
                }
            }
        })
    }
});

router.post('/subscriptions/freshdesk/:csId/:tenantHash',getDatafromTenantHash, async (req, res, next) => {
    const {tenantHash, csId} = req.params;
    console.log(tenantHash, csId);
    req.body = { ...req.body, ...req.params };
    console.log(req.body);
    if(tenantHash && csId){
        let headers = {
            "Content-Type": "application/json",
            "su-crawler-secret" : config.get("crawler.sharedSecret"),
            "tenant-id": tenantHash,
            "timeout": 10000
        }
        commonFunctions.errorlogger.info('freshdesk event hits');
        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/freshdesk', '', req, headers, function (error, result) {
            if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                return res.status(502).send('Internal server error');
            }
            else {
                if(result.data !== 'failed') {
                    return res.send("freshdesk webhook event consumed successfully");
                } else {
                    return res.status(502).send("error while consuming freshdesk webhook event");
                }
            }
        })
    }
});

router.post('/updateLastSyncDate', securityCheck, async (req, res, next) => {
    let response = {};
    let setObj = { last_sync_date : req.body.lastSyncDate };
    var updateObj = {
        csId: req.body.csId,
        setObj
    };
    let updateCsSql = "UPDATE content_sources SET ? WHERE id=?"
    connection[req.headers['tenant-id']].execute.query(updateCsSql, [setObj, updateObj.csId], function (errAsync, rows) {
        if (errAsync) {
            commonFunctions.errorlogger.error('Error while updating last sync date', errAsync);
        }
    });  

    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/updateLastSyncDate', '', updateObj, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            if (result.status) {
                response.value = result.data;
                response.success = true;
              } else {
                response.value = result;
                response.success = false;
              }
        }
            res.send(response);
    });
});

const findUidAndSendSCSettings = (contentSourcesId,req, cb) => {
    const queryObjectId = 'SELECT id from content_source_objects WHERE content_source_id =?'
    connection[req.headers['tenant-id']].execute.query(queryObjectId, [contentSourcesId], (err, response) => {
        if(!response || !response[0]){
            return cb();
        }
        const objectId = response[0].id;
        const query = 'SELECT uid,search_client_id FROM search_clients sc INNER JOIN search_clients_to_content_objects sctco ON sc.id = sctco.search_client_id WHERE content_source_object_id = ?'
        connection[req.headers['tenant-id']].execute.query(query, [objectId], (err, docs) => {
            cb();
            let uids = docs;
            let asyncTask = [];
            uids.forEach(doc => {
                asyncTask.push(sendDataToKafka.bind(null, doc.uid, doc.search_client_id,req));
            });
            async.series(asyncTask, (err, data) => {
                console.log("done");
            })
        })
    })
  };

router.post('/addContentSource', securityCheck ,async (req, res, next) => {
    var htmlRegex = new RegExp("<\/?[^>]+(>|$)");
    var dataToSend = req.body;
    dataToSend.verifyAuthentication = dataToSend.authorization.verifyAuthentication;
    if(dataToSend.contentSource.content_source_type_id == 15) {
        dataToSend.authorization.client_id = config.get('githubClientId');
    } else if(dataToSend.contentSource.content_source_type_id == 3){
        dataToSend.authorization.client_id = config.get('salesforceClientId');
    } else if(dataToSend.contentSource.content_source_type_id == 8){
        dataToSend.authorization.client_id = config.get('slackClientId');
    } else if(dataToSend.contentSource.content_source_type_id == 24 && dataToSend.authorization.organization_user_type == '0'){
        dataToSend.authorization.client_id = config.get('dropbox_client_id');
    } else if(dataToSend.contentSource.content_source_type_id == 33){
        dataToSend.authorization.client_id = config.get('vimeo_client_id');
    } else if (dataToSend.contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.file) {
        dataToSend.authorization.isFileUploaded = dataToSend.contentSource.isFileUploaded;
    }
    dataToSend.contentSource.email= req.headers.session.email;
    const isOuthUpdatedFromFe = !dataToSend.contentSource.editing || dataToSend.contentSource.isOauthUpdated;
    dataToSend.contentSource.last_updated_by = JSON.stringify({email:req.headers.session.email, name:req.headers.session.name});
    if(dataToSend.contentSource.index_name){
        dataToSend.contentSource.elasticIndexName = dataToSend.contentSource.index_name;
        delete dataToSend.contentSource.index_name;
    }
    if (htmlRegex.test(dataToSend.contentSource.label) != true ) {
        if (dataToSend.contentSource.label.length> 127) {
            return res.send({ status: 403, message: "name is too long" });
        }
        async.auto({
            tenantMaxCsCountCheck: function (cb){
                if(!req.body.contentSource.id && config.get('multiTenantSupport') && config.get('multiTenantSupport') === true){
                    const query = 'SELECT COUNT(1) as count from content_sources'
                    const q = connection[req.headers['tenant-id']].execute.query(query, [], function (err, rows) {
                        // console.log(q.sql);
                        if (err) {
                          commonFunctions.errorlogger.error("Error inside tenantMaxCsCountCheck",err);
                          return res.sendStatus(500);
                        } else {
                            if(rows[0].count >= config.get('tenantMaxCsCount')){
                                commonFunctions.errorlogger.error('trying to add more than alloted CS');
                                //throw new Error('Maximum content source limit reached!');   
                                res.send({ status: 403, message: 'maximum content source limit reached!' })                             
                            } else {
                                cb();
                            }
                        }
                    });
                } else {
                    cb();
                }
            },
            get_data: ['tenantMaxCsCountCheck', function (body, cb) {
                //basic authentication also requires a certificate call to saba rest api.
                //therefore adding this funtion here
                dataToSend.tenantId =req.headers['tenant-id'];
                if (req.body.contentSource.content_source_type_id == 25) {
                    sabaCloud.get_certificate_token(dataToSend, function (error, data) {
                        if (error) throw new Error(error);
                        else {
                            dataToSend.authorization.publicKey = data;
                            delete dataToSend.authorization.verifyAuthentication;
                            cb(null, dataToSend)
                        }
                    });
                }
                else if (req.body.contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.cornerstone
                    && req.body.authorization.client_secret) { //verify credentials in case of cornerstone
                    cornerstone.verify_credentials(dataToSend, function (error, data) {
                        if (error) {
                            res.send({ status: 403, message: "Invalid credentials or url" })
                            throw new Error(error);
                        }
                        else {
                            delete dataToSend.authorization.verifyAuthentication;
                            cb(null, dataToSend);
                        }
                    })
                }
                else if(dataToSend.authorization.verifyAuthentication && 
                    ((!dataToSend.authorization.authorization_type || (dataToSend.authorization.authorization_type && !dataToSend.authorization.authorization_type.includes('OAuth'))) 
                        || dataToSend.contentSource.content_source_type_id === constants.CONTENT_SOURCE_TYPE.insided
                        || dataToSend.contentSource.content_source_type_id === constants.CONTENT_SOURCE_TYPE.uservoice)){
                    commonFunctions.errorlogger.warn("Content Source added sucessfully");
                    const headers = {
                        'Content-Type': "application/json",
                        'su-crawler-secret': config.get('crawler.sharedSecret'),
                        "tenant-id": req.headers['tenant-id'],
                        "timeout": 60000
                    };
                    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/verify-cs-creds', '', dataToSend, headers, function (error, result) {
                        if (error) {
                            console.log('error in verify-creds', error);
                            kafkaAnalyticsPublishers.publishLogAnalytics(req, {
                                name: dataToSend.contentSource.name
                            });
                            return res.send({ status: 500, message: 'Internal Server Error' })
                        } else if (result && result.status) {
                            console.log('result', result);
                            delete dataToSend.authorization.url;
                            if (typeof result.data === 'Object' || typeof result.data === 'object') {
                                dataToSend.authorization.authVerification = result.data.ifAuthVerified === true;
                                if (result.data.dataToUpdate && typeof result.data.dataToUpdate === 'object') {
                                    Object.keys(result.data.dataToUpdate).forEach(key => {
                                        if (key === 'authorization') {
                                            Object.keys(result.data.dataToUpdate[key]).forEach(authKey => {
                                                dataToSend.authorization[authKey] = result.data.dataToUpdate[key][authKey];
                                            });
                                        }
                                    });
                                }
                            }

                            if (!dataToSend.authorization.authVerification) {
                                kafkaAnalyticsPublishers.publishLogAnalytics(req, {
                                    name: dataToSend.contentSource.name
                                });
                                return res.send({ status: 403, message: "Invalid Credentials" });
                            }
            
                            return cb(null, dataToSend);
                        }
                        else {
                            return res.send({ status: 403, message: "Invalid Credentials" });
                        }
                    });
                }else{
                    return cb(null, dataToSend);
                }
            }],
            getCSDataAuth: ['get_data', function (body, cb) {
                commonFunctions.getContentSourceDataById(req.body.contentSource.id, req, function (error, data) {
                    if (error) {
                      console.log("commonFunctions.getContentSourceDataById err", error);
                      throw new Error(error);
                    }
                    else {
	                 cb(null, data);
                    }
                });
            }],
            addContentSource: ['get_data', 'getCSDataAuth', function (body, cb) {
                dataToSend = body.get_data;
                delete dataToSend.authorization.verifyAuthentication;
                delete dataToSend.authorization.isFileUploaded;

                if(Object.keys(body.getCSDataAuth).length) {
                    for (const obj of constants.PROTECTED_AUTH_KEYS) {
                        dataToSend.authorization[obj] = dataToSend.authorization[obj] ? dataToSend.authorization[obj] 
                        : body.getCSDataAuth.authorization && body.getCSDataAuth.authorization[obj] 
                        ? body.getCSDataAuth.authorization[obj] : '';
                    }
                    dataToSend.contentSource.name = body.getCSDataAuth.contentSource ? body.getCSDataAuth.contentSource.name : ""
                }

                if ((req.body.contentSource.content_source_type_id == 6 && req.body.authorization.authorization_type == 'OAuth') || (req.body.contentSource.content_source_type_id == 4 && req.body.authorization.authorization_type == 'OAuth') || (req.body.contentSource.content_source_type_id == 27 && req.body.authorization.authorization_type == 'OAuth')) {
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                    dataToSend.authorization.publicKey = 'searchunify';
                    cb(null, []);
                }
                addContentSource(dataToSend,req, function (err, result) {
                    if (!err) {
                        if ([
                            constants.CONTENT_SOURCE_TYPE.website,
                            constants.CONTENT_SOURCE_TYPE.jsWeb
                        ].includes(result.insert_content_source_id.content_source_type_id) && (result.insert_content_source_id.isFileUploaded == true)) {
                            var fileUploadUrl = result.insert_content_source_id.url.split("sitemap/");
                            result.insert_content_source_id.url = fileUploadUrl[fileUploadUrl.length - 1]
                        }
                        dataToSend.authorization = { ...result.insert_content_source_authorization }
                        if(result.insert_content_source_id.editing){
                            if (result.insert_content_source_id.content_source_type_id == constants.CONTENT_SOURCE_TYPE.youtube && result.insert_content_source_authorization.client_id  == commonFunctions.appVariables.youtube.clientId ||
                                result.insert_content_source_id.content_source_type_id == constants.CONTENT_SOURCE_TYPE.gmail && result.insert_content_source_authorization.client_id  == commonFunctions.appVariables.youtube.clientId ||
                        result.insert_content_source_id.content_source_type_id == constants.CONTENT_SOURCE_TYPE.drive && result.insert_content_source_authorization.client_id  == commonFunctions.appVariables.drive.clientId){
                            if(isOuthUpdatedFromFe){
                                result.insert_content_source_id["isOauthUpdated"] = true;
                                console.log('invalid credentials provided')
                                res.send({ status: 403, message: "invalid credentials provided" })
                                throw new Error('invalid credentials provided');
                            }
                            delete result.insert_content_source_authorization.client_id;
                            delete result.insert_content_source_authorization.client_secret;
                        }
                        else {
                            result.insert_content_source_id["isOauthUpdated"] = true;
                        }
                        }
                        else {
                            result.insert_content_source_id["isOauthUpdated"] = true;
                        }

                        constants.PROTECTED_AUTH_KEYS.forEach((key) => {
                            delete result.insert_content_source_authorization[key];
                        });

                        dataToSend.contentSource.sync_frequency = Array.isArray(result.insert_content_source_id.sync_frequency) ? result.insert_content_source_id.sync_frequency : result.insert_content_source_id.sync_frequency !== null ? `${result.insert_content_source_id.sync_frequency}`.split(',').map(ele => Number(ele)) : [1];
                        dataToSend.contentSource.sync_frequency_index = Array.isArray(result.insert_content_source_id.sync_frequency_index) ? result.insert_content_source_id.sync_frequency_index : result.insert_content_source_id.sync_frequency_index !== null ? `${result.insert_content_source_id.sync_frequency_index}`.split(',').map(ele => Number(ele)) : [1];
                        dataToSend.contentSource.sync_frequency_name_index = result.insert_content_source_id.sync_frequency_name_index;
                        delete dataToSend.contentSource.logFile;
                        delete dataToSend.contentSource.adminLogFile;
                        if (!result.insert_content_source_id.contentSourceAddedFirstTime && body.getCSDataAuth && body.getCSDataAuth.contentSource && body.getCSDataAuth.contentSource.elasticIndexName) {
                            dataToSend.contentSource["elasticIndexName"] = body.getCSDataAuth.contentSource.elasticIndexName;
                        }
                        delete result.insert_content_source_id.contentSourceAddedFirstTime;
                        commonFunctions.CleanRedisCache();
                        commonFunctions.CleanRedisCache([`SU_getTypesStatistics_${req.headers["tenant-id"]}`, `SU_getAddedContent_${req.headers["tenant-id"]}`]);
                        if (config.get("kafkaTopic.enable")) {
                            var configTables = ['content_sources', 'content_source_languages', 'content_source_authorization', 'content_source_objects', 'content_source_object_fields', 'content_source_spaces', 'api_crawler_fields', 'website_setting'];
                            contentSourceConfigurationTokafka.content_Sourse_Config(configTables, result.insert_content_source_id.id,req, function (err, configResult) {

                                if (err) {
                                    res.send({
                                        contentSource: result.insert_content_source_id.id,
                                        authorization: result.insert_content_source_authorization,
                                        objectsAndFields: result.insert_content_source_objects_and_fields,
                                        spacesORboards: [],
                                        oauth: result.get_auth_intermediate_Callback,
                                        getWebConf: result.saveurlConfiguration,
                                        apiCrawlerFields: result.insert_api_crawler_fields,
                                        language: result.insert_content_source_languages.languages
                                    })
                                } else {
                                    dataToSend.objectsAndFields = [...result.insert_content_source_objects_and_fields_elastic];
                                    dataToSend.authorization.instanceURL = configResult.content_source_authorization[0].instanceURL;
                                    dataToSend.language = result.getLanguages;
                                    dataToSend.session = req.headers.session;
                                    dataToSend.authorization.is_authenticated = configResult.content_source_authorization[0].is_authenticated;
                                    dataToSend.contentSource.is_paused = configResult.content_sources[0].is_paused;
                                    result.insert_content_source_authorization.is_authenticated = dataToSend.authorization.is_authenticated;
                                    result.insert_content_source_id.is_paused = dataToSend.contentSource.is_paused;
                                    const dataToSendWithTypeName = {
                                        ...dataToSend,
                                        contentSource: {
                                            ...dataToSend.contentSource,
                                            content_source_type_name: result.get_type_name && result.get_type_name.name ? result.get_type_name.name : '',
                                        },
                                    };
                                    kafkaLib.publishMessage({ 
                                        topic   : kafkaLib.SU_CRAWLER_TOPIC.contentSource,
                                        messages: [{ 
                                            value : JSON.stringify({ type:kafkaLib.KAFKA_EVENT_TYPES.add, data:dataToSendWithTypeName, tenantId: req.headers['tenant-id'], session: req.headers.session }),
                                            key: req.headers['tenant-id']
                                        }]
                                    });
                                    if(config.get('statusPageService.kafka.host') !== config.get('kafkaTopic.host')){
                                        kafkaStatusLib.publishMessage({ 
                                            topic:config.get("statusPageService.kafka.contentSourceTopic"),
                                            messages: [{ 
                                                value : JSON.stringify({ type:kafkaLib.KAFKA_EVENT_TYPES.add, data:dataToSendWithTypeName, tenantId: req.headers['tenant-id'] })
                                            }]
                                        });
                                    }
                                    result.insert_content_source_id.index_name = result.insert_content_source_id.elasticIndexName;
                                    configResult.content_sources[0].index_name = configResult.content_sources[0].elasticIndexName;
                                    delete result.insert_content_source_id.elasticIndexName;
                                    delete configResult.content_sources[0].elasticIndexName;
                                    res.send({
                                        contentSource: result.insert_content_source_id,
                                        authorization: result.insert_content_source_authorization,
                                        objectsAndFields: result.insert_content_source_objects_and_fields,
                                        spacesORboards: [],
                                        oauth: result.get_auth_intermediate_Callback,
                                        getWebConf: result.saveurlConfiguration,
                                        apiCrawlerFields: result.insert_api_crawler_fields,
                                        language: result.insert_content_source_languages.languages,
                                        allConfig: configResult
                                    })
                                }

                            });
                        }
                        else {
                            res.send({
                                contentSource: result.insert_content_source_id,
                                authorization: result.insert_content_source_authorization,
                                objectsAndFields: result.insert_content_source_objects_and_fields,
                                spacesORboards: [],
                                oauth: result.get_auth_intermediate_Callback,
                                getWebConf: result.saveurlConfiguration,
                                apiCrawlerFields: result.insert_api_crawler_fields,
                                language: result.insert_content_source_languages.languages
                            })
                        }
                    } else {
                        //in case of error as well 200 response is being sent which is misleading
                        if (err && (err.sqlMessage || (err.stack && jsErrors.includes(err.stack.split(':')[0])))) {
                            err = {
                                code: 500,
                                message: 'Internal Server Error'
                            }
                        }
                        console.log("addContentSource error ", err);
                        res.send(err);
                        //res.send({ contentSource: dataToSend.contentSource, authorization: dataToSend.authorization, objectsAndFields: dataToSend.objectsAndFields, spacesORboards: [], oauth: '', language: dataToSend.language });
                        cb(null, []);
                    }
                })
            }],
            sendDataToKafka: ['get_data', 'getCSDataAuth', 'addContentSource',function (body, cb) {
                findUidAndSendSCSettings(req.body.contentSource.id, req,function (error, data) {
                    if (error) {
                      console.log("findUidAndSendSCSettings err", error);
                      throw new Error(error);
                    }
                    else {
                        cb(null, dataToSend)
                    }
                });
            }]
        }, function (err, result) {
            if (err) {
                if (err && (err.sqlMessage || (err.stack && jsErrors.includes(err.stack.split(':')[0])))) {
                    err = {
                        code: 500,
                        message: 'Internal Server Error'
                    }
                }
              console.log("some err", err);
              throw new Error(err);
            }
            else {
                commonFunctions.errorlogger.warn("Content Source added sucessfully");
            }
        })
    }else {
        res.send({ status: 403, message: "Only string value is allowed" })
    }
})


// get all the searchClient
router.get('/content-source/get-search-clients',securityCheck, async(req, res, next) => {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/content-source/get-search-clients','',req.query, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.setHeader("Content-Type", "text/html");
            res.send(result);
        }
    })
    
})


router.post('/deleteContentSource', securityCheck, async (req, res, next) => {
    var contentSourceId = req.body.content_source_id;
    var csTypeId =  req.body.cs_type_id;
    const delSCIds = req.body.del_sc;
    var tempArr = []
    tempArr.push(contentSourceId)
    var check = commonFunctions.checkArray(tempArr)
    var response = {
        status: commonFunctions.constants.responseFlags.PARAMETER_MISSING,
        message: "PARAMETER MISSING",
        data: []
    }
    if (!check) {
        res.send(response)
    }
    else {
          findUidAndSendSCSettings(contentSourceId,req, () => {
            commonFunctions.getContentSourceDataById(contentSourceId, req, function (err, resultData) {
                if (!err) {
                    deleteContentSource(contentSourceId, csTypeId, 1, req, function (err, result) {
                        commonFunctions.deleteMapping(resultData.contentSource.elasticIndexName, function (result) {
                            if (delSCIds && delSCIds.length) {
                                const taskArray = [];
                                delSCIds.forEach((x) => {
                                    taskArray.push(
                                        searchResultKafkaConfig.getSearchClientSettingsViaKafka.bind(
                                            null,
                                            { platformId: x }
                                        )
                                    );
                                });
                                async.auto(taskArray, (error) => {
                                    if (error) {
                                        commonFunctions.errorlogger.error("Deletion Error: ", error);
                                    } else {
                                        commonFunctions.errorlogger.error("Updated Kafka Event");
                                    }
                                });
                            }
                            // console.log('contentsourceId result', resultData, '-----', csTypeId);
                            // let reqBody = {
                            //         syncFrequencyName: "Never",
                            //         syncFrequency: 0,
                            //         contentSourceId: contentSourceId
                            // }
                            // let headers = {
                            //     "Content-Type": "application/json",
                            //     "su-crawler-secret" : config.get("crawler.sharedSecret"),
                            //     "tenant-id": req.headers['tenant-id'] 
                            // }
                            // commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/set-frequency?content_source_type_id='+csTypeId, '', reqBody, headers, function (error, result) {
                            //     console.log(result, 'delete content sourcssse');
                            //     if (error) {
                            //         commonFunctions.errorlogger.error("Cron delete error: ", error);
                            //     }
                            //     else {
                            //         commonFunctions.errorlogger.info("Cron deleted successfully, result: ", result);
                            //     }
                            // })
                        });
                        commonFunctions.CleanRedisCache([`SU_getTypesStatistics_${req.headers["tenant-id"]}`, `SU_getAddedContent_${req.headers["tenant-id"]}`]);
                        res.send(resultData);
                    })
                }
            })
        })
    }
})



const sendDataToKafka = (uid, searchClientId,req, cb) => {
    let configObj = { "platformId": searchClientId, "uid": uid }
    searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
        cb(null, "")
    });
}


router.post('/getRecordTypeFromSalesforce', function (req, res, next) {
    var contentSourceId = req.body.content_source_id;
    var objectName = req.body.object;
    commonFunctions.getRecordTypeFromSalesforce(objectName, contentSourceId, function (err, result) {
        if (err) {
            console.err(err);
        } else {
            res.send(result);
        }
    });

});

const deleteCSAfterCheck = function (contentSourceId, csTypeId, forceDelete,req, cb){
    const getDeleteObjQuery = 'SELECT GROUP_CONCAT(cso.id) as objectIds FROM `content_sources` cs left join content_source_objects cso on cs.id=cso.content_source_id WHERE cs.id=? GROUP BY cs.id'
    connection[req.headers['tenant-id']].execute.query(getDeleteObjQuery, [contentSourceId], function (err, objectsToDelete) {
        if (err) {
            commonFunctions.errorlogger.info("Error while fetching Object Ids for Content Source: ",err);
        }
        commonFunctions.errorlogger.info("Delete Content Source Data. Force Delete: ", forceDelete);

    let sql = ` Delete cs, csa,
                language, spaces, objects, object_fields, website_setting from content_sources cs
                left join
                content_source_authorization csa on cs.id=csa.content_source_id
                left join
                content_source_languages language on cs.id=language.content_source_id
                left join
                content_source_spaces spaces on cs.id=spaces.content_source_id
                left join
                content_source_objects objects on cs.id=objects.content_source_id
                left join
                content_source_object_fields object_fields on objects.id=object_fields.content_source_object_id
                left join
                website_setting on cs.id=website_setting.content_source_id
                where cs.id=? `;

    if(!forceDelete){
        // when cs is unauthticated
        sql += "and csa.connection_status!=1";
    }
    connection[req.headers['tenant-id']].execute.query(sql, [contentSourceId], function (err, resultFields) {
        if (err) {
            cb(null, {
                "status": commonFunctions.constants.responseFlags.ERROR_IN_EXECUTION,
                message: "ERROR IN EXECUTION" + err
            });
        }
        else {
            let sql = "DELETE from email_notification_contentsources where contentSources = '" + contentSourceId + "';";
            sql += "UPDATE email_notification_contentsources SET contentSources = REPLACE(REPLACE(REPLACE(contentSources, '" + contentSourceId + ",', ''), '," + contentSourceId + "', ''), '," + contentSourceId + ",', '') WHERE contentSources like '%" + contentSourceId + "%'";
            commonFunctions.errorlogger.info("Deleteting from email_notification_contentsources");
            connection[req.headers['tenant-id']].execute.query(sql, [contentSourceId], function (err, rows) {
                if (err) {
                    commonFunctions.errorlogger.error("Error while deleting from email_notification_contentsources :", err);
                    cb(null, {
                        "status": commonFunctions.constants.responseFlags.ERROR_IN_EXECUTION,
                        message: "ERROR IN EXECUTION" + err
                    })
                } else {
                    commonFunctions.errorlogger.info('Deleted all content source data for content source id:', contentSourceId);
                    if (objectsToDelete && objectsToDelete.length && objectsToDelete[0].objectIds)
                        commonFunctions.errorlogger.info('Deleted all content source object id:', objectsToDelete[0].objectIds.split(','));
                    kafkaLib.publishMessage({ 
                        topic   : kafkaLib.SU_CRAWLER_TOPIC.contentSource, 
                        messages: [{ 
                            value : JSON.stringify({
                                type:kafkaLib.KAFKA_EVENT_TYPES.delete,
                                data:contentSourceId, csTypeId ,
                                tenantId: req.headers['tenant-id'],
                                objIds: objectsToDelete && objectsToDelete.length && objectsToDelete[0].objectIds && objectsToDelete[0].objectIds.split(','),
                                session: req.headers.session
                            }),
                            key: req.headers['tenant-id']
                        }]
                    });
                    if(config.get('statusPageService.kafka.host') !== config.get('kafkaTopic.host')){
                      kafkaStatusLib.publishMessage({ 
                          topic:config.get("statusPageService.kafka.contentSourceTopic"),
                          messages: [{ 
                              value : JSON.stringify({
                                  type:kafkaLib.KAFKA_EVENT_TYPES.delete,
                                  data:contentSourceId, csTypeId ,
                                  tenantId: req.headers['tenant-id'],
                                  objIds: objectsToDelete && objectsToDelete.length && objectsToDelete[0].objectIds && objectsToDelete[0].objectIds.split(','),
                                  session: req.headers.session
                              }) 
                          }]
                      });
                    }
                    cb(null, {
                        "status": commonFunctions.constants.responseFlags.OK,
                        message: "OK"
                    })
                }
            });
        }
    });
    });
}

const deleteContentSource = function (contentSourceId, csTypeId, forceDelete,req, cb) {
    if(forceDelete){
        return deleteCSAfterCheck(contentSourceId, csTypeId, forceDelete,req, cb);
    }
    else{
        commonFunctions.getContentSourceDataById(contentSourceId, req, function (err, contentSourceData) {
            commonFunctions.checkIfCSUsedInSearchClients(contentSourceId,req, function (err, csExistsInSC) {
                commonFunctions.getCountIndexName(contentSourceData.contentSource.elasticIndexName, req, function (err, count) {    
                    if (contentSourceData 
                    && contentSourceData.contentSource 
                    && !contentSourceData.contentSource.editing 
                    && !contentSourceData.authorization.connection_status
                    && !count 
                    && !csExistsInSC) {
                        return deleteCSAfterCheck(contentSourceId, csTypeId, forceDelete,req,cb);
                    }else{
                        console.log("cannot delete");
                        cb(null, {
                            "status": 400,
                            message: "cannot delete"
                        })
                    }
                });
            });
        });
    }
}

router.post('/deleteObject', function (req, res, next) {
    var contentSourceObjectId = req.body.content_source_object_id
    var tempArr = []
    tempArr.push(contentSourceObjectId)
    var check = commonFunctions.checkArray(tempArr)
    var response = {
        status: commonFunctions.constants.responseFlags.PARAMETER_MISSING,
        message: "PARAMETER MISSING",
        data: []
    }
    if (!check) {
        res.send(response)
    }
    else {

      const csObjSql = "SELECT cso.*, cs.name AS content_source_name FROM content_source_objects cso JOIN content_sources cs ON cso.content_source_id = cs.id WHERE cso.id=?"

      connection[req.headers['tenant-id']].execute.query(csObjSql, [contentSourceObjectId], function (err, resultFields) {
        if (resultFields) {
          kafkaLib.publishMessage({
            topic: kafkaLib.SU_CRAWLER_TOPIC.contentSourceObjects,
            messages: [{
              value: JSON.stringify
              ({ type: kafkaLib.KAFKA_EVENT_TYPES.delete, data: resultFields, tenantId: req.headers['tenant-id'],key: req.headers['tenant-id'], session:req.headers.session}),
              key: req.headers['tenant-id']
            }]
          });
          if(config.get('statusPageService.kafka.host') !== config.get('kafkaTopic.host')){
            kafkaStatusLib.publishMessage({ 
              topic:config.get("statusPageService.kafka.contentSourceObjectTopic"),
              messages: [{
                value: JSON.stringify({ 
                    type: kafkaLib.KAFKA_EVENT_TYPES.delete,
                     data: resultFields,
                      tenantId: req.headers['tenant-id'],
                      key: req.headers['tenant-id'],
                      session: req.headers.session
                    }),
                    key: req.headers['tenant-id']
                }]
            });
          }
        }
      })
        const sql = "Delete from content_source_objects where id=?"
        connection[req.headers['tenant-id']].execute.query(sql, [contentSourceObjectId], function (err, resultFields) {
            if (err) {
                res.send({
                    "status": commonFunctions.constants.responseFlags.ERROR_IN_EXECUTION,
                    message: "ERROR IN EXECUTION" + err
                })
            }
            else
                res.send({
                    "status": commonFunctions.constants.responseFlags.OK,
                    message: "OK"
                })
        })
    }
});


router.post('/deleteField', async function (req, res, next) {
    var fields = req.body.fields;
    var contentSourceId = req.body.csId;
    const contentSourceObjectId = req.body.fields[0].content_source_object_id;
    var response = {
        status: commonFunctions.constants.responseFlags.PARAMETER_MISSING,
        message: "PARAMETER MISSING",
        data: []
    }

    var ids = fields.map(x => {
        if (x.id)
            return x.id
        else
            return 0
    })

    const contentSourceDetails = await listContentSources([contentSourceId], req.headers['tenant-id']);

    const sql = "Delete from content_source_object_fields where id in(" + ids.join(",") + ")"
    connection[req.headers['tenant-id']].execute.query(sql, function (err, resultFields) {
        if (err) {
            res.send({
                "status": commonFunctions.constants.responseFlags.ERROR_IN_EXECUTION,
                message: "ERROR IN EXECUTION" + err
            })
        }
        else {
            kafkaLib.publishMessage({ 
                topic   : kafkaLib.SU_CRAWLER_TOPIC.contentSourceObjectFields, 
                messages: [{ 
                    value : JSON.stringify({ type:kafkaLib.KAFKA_EVENT_TYPES.delete, data:{ fields }, tenantId: req.headers['tenant-id'], session: req.headers.session }),
                    key: req.headers['tenant-id']
                }] 
            });

            const sql = "Delete from field_boosting where content_source_object_field_id in (" + ids.join(",") + ")"
            connection[req.headers['tenant-id']].execute.query(sql, function (err, resultFields) {
                if (err) {
                    res.send({
                        "status": commonFunctions.constants.responseFlags.ERROR_IN_EXECUTION,
                        message: "ERROR IN EXECUTION" + err
                    })
                }
                else {
                    let fieldNames = fields.filter(item => item.id).map(item => ({ field: item.trueKey, id: item.id }));

                    for(const fieldName of fieldNames) {
                        let contentSource = contentSourceDetails.find(item => item.contentSourceObjectFieldId === fieldName.id);

                        if(contentSource) fieldName.indexName = `${contentSource.elasticIndexName}__${contentSource.contentSourceObjectName}`;
                    }
                    if(fieldNames.length) {
                        kafkaLib.publishMessage({
                            topic: config.get("kafkaTopic.mergeField"),
                            messages: [{
                                value : JSON.stringify({
                                    type: kafkaLib.KAFKA_EVENT_TYPES.removeCSField,
                                    data:{ fieldNames: fieldNames, indexName: `${contentSourceDetails[0].elasticIndexName}__${contentSourceDetails[0].contentSourceObjectName}` }, 
                                    tenantId: req.headers['tenant-id'],
                                    session: req.headers.session
                                }),
                                key: req.headers['tenant-id']
                            }]
                        });
                    }
                    findUidAndSendSCSettings(contentSourceId,req, () => {
                        res.send({
                            "status": commonFunctions.constants.responseFlags.OK,
                            message: "OK"
                        })
                    });
                }
            })
        } 
    })
});


router.get('/getPlaces', function (req, res, next) {
    commonFunctions.errorlogger.info("body is", req.query);
    const contentSourceId = req.query.content_source_id;
    const sort = req.query.sort;
    const reIndex = req.query.reIndex;
    async.auto({
        getAndInsertPlaces: cb => {
            commonFunctions.getContentSourceDataById(contentSourceId, req, function (err, resultData) {
                commonFunctions.getPlacesById(resultData.authorization.content_source_id,req, function (err, projects) {
                    var data = { "projects": projects, "resultData": resultData }
                    cb(null, data)
                });
            })
        },
        reIndexCheck: ['getAndInsertPlaces', function (result, cb) {
            if (reIndex == 'true' && reIndex != "undefined") {
                const sql = "Delete from content_source_spaces where content_source_id=?";
                connection[req.headers['tenant-id']].execute.query(sql, [contentSourceId], function (err, resultFields) {
                    if (err) {
                        cb(err, null);
                    }
                    else {
                        cb(null, "ReIndexed");
                    }
                })
            }
            else {
                cb(null, "No Reindexubg")
            }

        }],
        fetchPlaces: ['reIndexCheck','getAndInsertPlaces', function (result, cb) {
            var projects = result.getAndInsertPlaces.projects;
            var resultData = result.getAndInsertPlaces.resultData
            resultData.projectIds = projects.filter(o => o.isSelected).map(o => o.spaceId);
            commonFunctions.errorlogger.info("case is---", resultData.contentSource.content_source_type_id)
            if (result.reIndexCheck == 'ReIndexed' || projects.length == 0) {
                switch (resultData.contentSource.content_source_type_id) {
                    case constants.CONTENT_SOURCE_TYPE.jive: {
                        jive.getProjects(resultData, sort,req, function (err, data) {
                            cb(null, data);
                        })
                        break; //Jive
                    }
                    case constants.CONTENT_SOURCE_TYPE.moodle: {
                        moodle.getCoursesList(resultData, sort, function (err, data) {
                            cb(null, data);
                        })
                        break;
                    }
                    default: {
                        const authObj = {
                            access_token: resultData.authorization.accessToken
                        }
                        let options = {
                            method: "GET",
                            rejectUnauthorized: false,
                            url: config.get("crawler.crawlerUrl") + "/content-source/get-spaces",
                            headers: {
                                "Content-Type": "application/json",
                                "su-crawler-secret" : config.get("crawler.sharedSecret"),
                                "tenant-id": req.headers['tenant-id']
                            },
                            body: {
                                contentSourceId: resultData.authorization.content_source_id,
                                authObj,
                                session:req.headers.session
                            },
                            json: true
                        };
                        request(options, function (error, response, body) {
                            if (error) {
                                commonFunctions.errorlogger.error("Error found: ", error);
                                res.send(error);
                            }
                            else {
                                console.log(body,"space body")
                                try { 
                                    if (typeof body === 'string') { 
                                      body = JSON.parse(body); }
                                   }
                                catch (e) {
                                    console.log('Error in JSON parse!');
                                }
                                if(!body.data)
                                    return res.send(body.message);
                                else {
                                    const spaces = body.data || [];
                                    let contentSourceObjectArr = [];
                                    for (let i = 0; i < spaces.length; i++) {
                                        contentSourceObjectArr.push({
                                            content_source_id: resultData.authorization.content_source_id,
                                            spaceName: spaces[i].spaceName,
                                            spaceUrl: spaces[i].spaceUrl || '',
                                            spaceId: spaces[i].spaceId,
                                            spaceKey: spaces[i].spaceKey,
                                            spaceType: spaces[i].spaceType || '',
                                            folderType: spaces[i].folderType || '',
                                            isSelected: resultData.projectIds.includes(String(spaces[i].spaceId)) ? 1 : 0
                                        });
                                    }
                                    insertSpacesBoards(resultData, contentSourceObjectArr, sort,req, cb);
                                }
                                
                            }
                        });
                        break;
                    }
                }
            } else {
                commonFunctions.getPlacesBySortParam(resultData.authorization.content_source_id, sort,req, function (err, projectsSort) {
                    cb(null, projectsSort);
                });
            }
        }],
        kafkaEvent: ['fetchPlaces', 'reIndexCheck', function (result, cb) {
            if (result.reIndexCheck == 'ReIndexed'){
                const sql = 'SELECT * FROM `content_source_spaces` where content_source_id=? ';
                connection[req.headers['tenant-id']].execute.query(sql, [contentSourceId], function (err, resultFields) {
                    if (err) { cb(err, []) }
                    else{
                        kafkaLib.publishMessage({
                            topic   : kafkaLib.SU_CRAWLER_TOPIC.contentSourceSpaces, 
                            messages: [{ 
                                value : JSON.stringify({ type:kafkaLib.KAFKA_EVENT_TYPES.add, data:{ contentSourceId, resultFields }, tenantId: req.headers['tenant-id'],
                                session: req.headers.session }),
                                key: req.headers['tenant-id']
                            }] 
                        });
                        cb(null);
                    }
                })
            } else {
                cb(null);
            }
        }],
        getPlacesCount: ['fetchPlaces', function (result, cb) {
            const sql = 'SELECT count(*) as count, LOWER(SUBSTRING(spaceName,1,1)) as letter, sum(isSelected) as enabled FROM `content_source_spaces` where content_source_id=? group by letter';
            connection[req.headers['tenant-id']].execute.query(sql, [contentSourceId], function (err, resultFields) {
                if (err) { cb(err, []) }
                else
                    cb(null, resultFields)
            })
        }]
    }, function (error, result) {
        if (error) {
            console.log("error in getplaces: ", error);
        }
        else {
            res.send({ spacesORboards: result.fetchPlaces, placesCount: result.getPlacesCount });
        }
    })

})


router.get('/crawlData', [securityCheck, updateLastUpdated],async (req, res, next) => {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    let response = {};
    const sql = 'SELECT * FROM content_sources WHERE id = ?';
    connection[req.headers['tenant-id']].execute.query(sql, [req.query.content_source_id], function (err, resultFields) {
        if (err) { cb(err, []) }
        else {
            var configObj = { 
                "contentSourceId": req.query.content_source_id,
                "currentCrawlStartTime": new Date().toISOString(),
                "tenantId": req.headers['tenant-id'],
                "contentSourceTypeId": resultFields[0].content_source_type_id,
                "session": req.headers.session
            }
            commonFunctions.errorlogger.info("Crawling start for admin side");
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/start-crawl', '', configObj, headers, function (error, result) {
                if (error) {
                    commonFunctions.errorlogger.error("Error found: ", error);
                    res.send(error);
                }
                else {
                    if(result.status) {
                        response = result.data;
                        response["statusCode"] = 200;
                    }else if(result.status == false){
                        response = result.data;
                        response["status"] = false;
                    }else if(result.success){
                        response = result;
                    }else {
                        response = result;
                        response["status"] = false;
                    }
                    if (response.statusCode == 200) {
                        let updateCsSql = "UPDATE content_sources SET ? WHERE id=?"
                        connection[req.headers['tenant-id']].execute.query(updateCsSql, [response.setObj, req.query.content_source_id], function (errAsync, rows) {
                            if (errAsync) {
                              commonFunctions.errorlogger.error('Error while updating content source data', errAsync);
                            }
                        });
                    }
                    res.send(response);
                }
            })
        }
    });
})

router.get('/startObjectCrawling',[securityCheck, updateLastUpdated], function (req, res, next) {
    // var configObj = { "contentSourceId": req.query.content_source_id, "isCrawl": req.query.isCrawl }   
    const sql = 'SELECT * FROM content_source_objects WHERE content_source_id = ?';
    connection[req.headers['tenant-id']].execute.query(sql, [req.query.content_source_id], function (err, objectData) {
        if (objectData && objectData.length) {
            if (objectData.find(ob => ob.object_pid > 0)) {
                return res.send({
                    data: 'Object crawling already in progress'
                });
            }
        }
        let response = {};
        commonFunctions.errorlogger.info("Crawling start for admin side");
        let headers = {
            "Content-Type": "application/json",
            "su-crawler-secret" : config.get("crawler.sharedSecret"),
            "tenant-id": req.headers['tenant-id']
        }
        let startObj = {
            tenantId : req.headers["tenant-id"],
            contentSourceId :req.query.content_source_id,
            reCrawlChoice : req.query.reCrawlChoice,
            objectName : req.query.objectName,
            objectId: req.query.objectId,
            session: req.headers.session
        }
    
        const sql = 'SELECT * FROM content_sources WHERE id = ?';
        connection[req.headers['tenant-id']].execute.query(sql, [req.query.content_source_id], function (err, resultFields) {
            if (err) { cb(err, []) }
            else {
                req.query.contentSourceTypeId = resultFields[0].content_source_type_id;
                commonFunctions.errorlogger.info("Crawling start for admin side");
            
                commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/object/start-crawl?contentSourceTypeId='+resultFields[0].content_source_type_id,'', startObj, headers, function (error, result) {
                    if (error) {
                        commonFunctions.errorlogger.error("Error found: ", error);
                        res.send(error);
                    }
                    else {
                        if(result.status) {
                            response = result.data.message;
                            response.statusCode = 200;
                        } else if(result.status === false ) {
                            response = result.data;
                        } else {
                            response = result;
                        }
                        
                        if (response.statusCode == 200) {
                            let updateCsSql = "UPDATE content_sources SET ? WHERE id=?"
                            connection[req.headers['tenant-id']].execute.query(updateCsSql, [result.data.setObj, req.query.content_source_id], function (errAsync, rows) {
                                if (errAsync) {
                                    commonFunctions.errorlogger.error('Error while updating content source data', errAsync);
                                }
                            });              
                            let updateObjectSql = "UPDATE content_source_objects SET object_pid=?";
                            let params = [result.data.setObj.pid];
                            if (result.data.setObj.current_crawl_start_time) {
                                updateObjectSql += ', current_crawl_start_time=?'
                                params.push(result.data.setObj.current_crawl_start_time);
                            }
                            updateObjectSql += ', objectAdminLogFile=null, objectLogFile=null';
                            updateObjectSql += ' WHERE id=?';
                            params.push(req.query.objectId);
                            connection[req.headers['tenant-id']].execute.query(updateObjectSql, params, function (errAsync, rows) {
                                if (errAsync) {
                                    commonFunctions.errorlogger.error('Error while updating content source object data', errAsync);
                                }
                            });
                            response.pid = result.data.setObj.pid;
                            response.crawlStatus = result.data.setObj.crawl_status;
                            response.current_crawl_start_time = result.data.setObj.current_crawl_start_time;
                            response.current_crawl_mode = result.data.setObj.current_crawl_mode; 
                        }
                        res.send(response);
                    }
                })
            }
        });
    });

})

router.post('/stopObjectCrawling', [securityCheck, updateLastUpdated], function (req, res, next) {
    // var configObj = { "contentSourceId": req.query.content_source_id, "isCrawl": req.query.isCrawl }
    let response = {};
    commonFunctions.errorlogger.info("Crawling stop for admin side");
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    const sql = 'SELECT * FROM content_sources WHERE id = ?';
    connection[req.headers['tenant-id']].execute.query(sql, [req.body.contentSource.content_source_id], function (err, resultFields) {
        if (err) { cb(err, []) }
        else {
            //req.body.contentSource.contentSourceTypeId = resultFields[0].content_source_type_id;
            let stopObj = {
                pid: resultFields[0].pid,
                tenantId: req.headers["tenant-id"],
                objectName: req.body.contentSource.objectName,
                contentSourceId: req.body.contentSource.content_source_id,
                objectId: req.body.contentSource.objectId,
                session: req.body.session
            }

            commonFunctions.errorlogger.info("Crawling start for admin side");
        
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/object/stop-crawl?contentSourceTypeId='+resultFields[0].content_source_type_id, '', stopObj, headers, function (error, result) {
                if (error) {
                    commonFunctions.errorlogger.error("Error found: ", error);
                    res.send(error);
                }
                else {
                    if (result.status) {
                        response.data = result.data.data;
                        response.statusCode = 200;
                        response.flag = result.data.flag;
                    } else if(result.status === false || result.flag === false){
                        response.data = result.data.data;
                        response.flag = false;
                        if (response.data && response.data.flag == 2004){
                            response.data = response.data.data;
                        }
                    }else {
                        response = result; 
                    }
                    if (response.statusCode == 200) {
                        let updateCsSql = "UPDATE content_sources SET crawl_status=? WHERE id=?"
                        connection[req.headers['tenant-id']].execute.query(updateCsSql, [5, req.body.contentSource.content_source_id], function (errAsync, rows) {
                            if (errAsync) {
                              commonFunctions.errorlogger.error('Error while updating content source data', errAsync);
                            }
                        });
                        let updateObjectSql = "UPDATE content_source_objects SET object_pid=?";
                        let params = [stopObj.pid];
                        if (result.data.current_crawl_end_time) {
                            updateObjectSql += ', current_crawl_end_time = ?'
                            params.push(result.data.current_crawl_end_time);
                        }
                        params.push(req.body.contentSource.objectId);
                        updateObjectSql += ' WHERE id=?';

                        connection[req.headers['tenant-id']].execute.query(updateObjectSql, params, function (errAsync, rows) {
                            if (errAsync) {
                              commonFunctions.errorlogger.error('Error while updating content object source data', errAsync);
                            }
                        });
                    }
                    response.current_crawl_end_time = result.data.current_crawl_end_time;
                    response.pid = stopObj.pid;
                    res.send(response);
                }
            });
        }
    });
})

router.post('/stopCrawler', [securityCheck, updateLastUpdated],async (req, res, next) => {
    commonFunctions.errorlogger.info("@admin service !! @stopCrawler")
    let stopCrawlerConfig = {
        "pid": req.body.contentSource.pid,
        "contentSource": req.body.contentSource,
        "forcefulSuccess": req.body.contentSource.forcefulSuccess || false,
        "contentSourceId": req.body.contentSource.id,
        "tenantId": req.headers['tenant-id'],
       "session": req.headers.session
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
        "session": req.headers.session
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/stop-crawl', '', stopCrawlerConfig, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            let response = {};
            if(result.status) {
                response = result.data;
                response["statusCode"] = 200;
            }else if(result.status == false){
                
                if (result.data && result.data.flag == 2004){
                    result.data = result.data.data;
                    response = result;
                    response["flag"] = 204;
                }
                else {
                    response = result.data;
                    response["status"] = false;
                }
            }else if(result.success){
                response = result;
            }else {
                response = result;
                response["status"] = false;
            }
            let updateCsParams = [response.crawl_status];
            if (response.last_sync_date) {
                updateCsParams.push(response.last_sync_date);
            }
            updateCsParams.push(req.body.contentSource.id);

            if (response.statusCode == 200) {
                let updateCsSql = `UPDATE content_sources SET crawl_status=?${response.last_sync_date ? ', last_sync_date=?':''}  WHERE id=?`
                connection[req.headers['tenant-id']].execute.query(updateCsSql, updateCsParams, function (errAsync, rows) {
                    if (errAsync) {
                        commonFunctions.errorlogger.error('Error while updating content source data', errAsync);
                    }
                });
                // let updateObjectSql = "UPDATE content_source_objects SET object_pid=0"
                // let params = [];
                // let objectCrawlStatus = response.crawl_status;
                // if (objectCrawlStatus == 2) {
                //     updateObjectSql += ', object_status=null';
                //     if (response.last_sync_date) {
                //         updateObjectSql += ', current_crawl_end_time=?';
                //         params.push(response.last_sync_date);
                //     }
                // }
                // params.push(req.body.contentSource.id);
                // updateObjectSql += ' WHERE content_source_id=? AND object_pid > 0'
                // connection[req.headers['tenant-id']].execute.query(updateObjectSql, params, function (errAsync, rows) {
                //     if (errAsync) {
                //         commonFunctions.errorlogger.error('Error while updating content source object data', errAsync);
                //     }
                // });
            }
            res.send(response);
        }
    })   
})

router.get('/fetchObjects', function (req, res, next) {
    var contentSourceId = req.query.content_source_id
    var objectName = req.query.objectName;
    var tempArr = []
    tempArr.push(contentSourceId, objectName)
    var check = commonFunctions.checkArray(tempArr)
    var response = {
        status: commonFunctions.constants.responseFlags.PARAMETER_MISSING,
        message: "PARAMETER MISSING"
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret") 
    }
    if (!check) {
        res.send(response)
    }
    else {
        commonFunctions.getContentSourceDataById(contentSourceId, req, function (err, contentSourceData) {
            if (!err) {
                contentSourceData.authorization.tenant_id = req.headers['tenant-id'];
                switch (contentSourceData.contentSource.content_source_type_id) {
                    case 16: // SAP
                    case 18: // stackoverflow
                    case 31: // Zumin
                    case constants.CONTENT_SOURCE_TYPE.azureDevops:
                    case constants.CONTENT_SOURCE_TYPE.cornerstone:
                    case constants.CONTENT_SOURCE_TYPE.thoughtIndustries:
                    // case 32: // Docebo
                    case 1: {
                        var objectsJive = [];
                        var objToSEnd = [];
                        let mappingObjects;
                        let mappingFields;
                        if(contentSourceData.contentSource.content_source_type_id == 31){
                          commonFunctions.metadata.loadMappingForDbAndElastic(contentSourceData.contentSource.content_source_type_id.toString(), req.headers['tenant-id'], (err, metaFields) => {
                              if (err){
                                commonFunctions.errorlogger.error("Error while loading mapping for Db and Elastic");
                              } else {
                                mappingObjects = metaFields.objects;
                                mappingFields = metaFields.fields;
                                objectsJive = mappingObjects;
                                var objectFields = mappingFields;
                                for (var i = 0; i < objectsJive.length; i++) {
                                    if (objectsJive[i]["name"] == objectName) {
                                        objToSEnd = objectFields;
                                    }
                                }
                                if (objToSEnd.length > 0) {
                                    res.send({ flag: 0, Objects: objToSEnd });
                                } else {
                                    res.send({ flag: 7, Objects: objToSEnd });
                                }
                              }
                        })
                        } else {
                          mappingObjects = commonFunctions.metadata.loadMappingForDbAndElastic(contentSourceData.contentSource.content_source_type_id.toString()).objects;
                          mappingFields = commonFunctions.metadata.loadMappingForDbAndElastic(contentSourceData.contentSource.content_source_type_id.toString()).fields;
                          objectsJive = mappingObjects;
                          var objectFields = mappingFields;
                          for (var i = 0; i < objectsJive.length; i++) {
                              if (objectsJive[i]["name"] == objectName) {
                                  objToSEnd = objectFields;
                              }
                          }
                          if (objToSEnd.length > 0) {
                              res.send({ flag: 0, Objects: objToSEnd });
                          } else {
                              res.send({ flag: 7, Objects: objToSEnd });
                          }
                        }
                        break;
                    }
                    case 3: { //salesforce
                        //make requetes to su-crawler to check object availabilty
                        let headers = {
                            "Content-Type": "application/json",
                            "su-crawler-secret" : config.get("crawler.sharedSecret"),
                            timeout: 20000,
                            "tenant-id": req.headers['tenant-id']
                        }
                        contentSourceData.objectName = objectName;
                        contentSourceData.eventType = 'create-object'
                        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-source/fetch-objects`, '', contentSourceData, headers, function (error, result) {
                            if (error) {
                                commonFunctions.errorlogger.error("Error found: ", error);
                                return res.send({ flag: error, message: error.message });
                            }
                            if (!result.status) {
                                commonFunctions.errorlogger.error("Error found: ", result.error);
                                return res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: result.data });
                            }
                            return res.send({ flag: commonFunctions.constants.SUCCESS, Objects: result.data.data });
                        });
                        break;
                    }
                    case constants.CONTENT_SOURCE_TYPE.file :{
                        //file-cs
                        let headers = {
                            "Content-Type": "application/json",
                            "su-crawler-secret" : config.get("crawler.sharedSecret"),
                            timeout: 20000,
                            "tenant-id": req.headers['tenant-id']
                        }
                        contentSourceData.objectName = objectName;
                        contentSourceData.eventType = 'create-object'
                        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-source/fetch-objects`, '', contentSourceData, headers, function (error, result) {
                            console.log("result----->",result);
                            if (error) {
                                commonFunctions.errorlogger.error("Error found: ", error);
                                return res.send({ flag: error, message: error.message });
                            }
                            if (!result.status) {
                                commonFunctions.errorlogger.error("Error found: ", result.error);
                                return res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: result.data });
                            }
                            return res.send({ flag: commonFunctions.constants.SUCCESS, Objects: result.data });
                        });
                        break;

                    }
                    case 4: { //confluence
                        objectAndFields.confluence.requestTofetchObjectFields(contentSourceData.authorization, objectName,req, function (err, result) {
                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })


                        });
                        break;
                    }
                    case 6: { //jira

                        let headers = {
                            "Content-Type": "application/json",
                            "su-crawler-secret" : config.get("crawler.sharedSecret"),
                            timeout: 20000,
                            "tenant-id": req.headers['tenant-id']
                        }
                        contentSourceData.objectName = objectName;
                        contentSourceData.eventType = 'create-object'
                        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-source/fetch-objects`, '', contentSourceData, headers, function (error, result) {
                            if (error) {
                                commonFunctions.errorlogger.error("Error found: ", error);
                                return res.send({ flag: error, message: error.message });
                            }
                            if (!result.status) {
                                commonFunctions.errorlogger.error("Error found: ", result.error);
                                return res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: result.data });
                            }
                            return res.send({ flag: commonFunctions.constants.SUCCESS, Objects: result.data });
                        });
                        break;
                    }
                    case 68: { //bugzilla
                        let headers = {
                          "Content-Type": "application/json",
                          "su-crawler-secret": config.get("crawler.sharedSecret"),
                          timeout: 20000,
                          "tenant-id": req.headers['tenant-id']
                        }
                        contentSourceData.objectName = objectName;
                        contentSourceData.eventType = 'Fetch-object-fields'
                        console.log('Making request to fetch objects:', contentSourceData);
                        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-source/fetch-objects`, '', contentSourceData, headers, function (error, result) {
                          if (error) {
                            console.error('Error fetching objects:', error);
                            commonFunctions.errorlogger.error("Error found: ", error);
                            return res.send({ flag: error, message: error.message });
                          }
                          if (!result.status) {
                            console.error('Error in response:', result.error);
                            commonFunctions.errorlogger.error("Error found: ", result.error);
                            return res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: result.data });
                          }
                          console.log('Objects fetched:', result.data);
                          return res.send({ flag: commonFunctions.constants.SUCCESS, Objects: result.data });
                        });
                        break;
                    }
                    case 7: { //Zendesk

                        let headers = {
                            "Content-Type": "application/json",
                            "su-crawler-secret" : config.get("crawler.sharedSecret"),
                            timeout: 30000,
                            "tenant-id": req.headers['tenant-id']
                        }
                        contentSourceData.objectName = objectName;
                        contentSourceData.eventType = 'Fetch-object-fields'
                        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-source/fetch-objects`, '', contentSourceData, headers, function (error, result) {
                            if (error) {
                                commonFunctions.errorlogger.error("Error found: ", error);
                                return res.send({ flag: error, message: error.message });
                            }
                            if (!result.status) {
                                commonFunctions.errorlogger.error("Error found: ", result.error);
                                return res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: result.data });
                            }
                            return res.send({ flag: commonFunctions.constants.SUCCESS, Objects: result.data });
                        });
                        break;
                        // objectAndFields.zendesk.requestTofetchObjectFields(contentSourceData, objectName,req, function (err, result) {

                        //     if (!err)
                        //         res.send({ flag: result.flag, Objects: result.data })
                        //     else
                        //         res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })


                        // });
                        // break;
                    }
                    case 20: { //litmos
                        objectAndFields.litmos.requestTofetchObjectFields(contentSourceData.authorization, objectName,req,  function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                        });
                        break;
                    }
                    case 23: { //moodle
                        objectAndFields.moodle.requestTofetchObjectFields(contentSourceData.authorization, objectName,req,  function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                        });
                        break;
                    }
                    case 25: { //saba
                        objectAndFields.sabaCloud.requestTofetchObjectFields(contentSourceData.authorization, objectName,req,  function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                        });
                        break;
                    }
                    case 24: { //dropbox
                        objectAndFields.dropbox.requestTofetchObjectFields(contentSourceData.authorization, objectName,req, function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                        });
                        break;
                    }
                    case 11: { //mindtouch
                        objectAndFields.mindtouch.requestTofetchObjectFields(contentSourceData.authorization, objectName,req,  function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                        });
                        break;
                    }
                    case 26: { //servicenow
                        objectAndFields.servicenow.requestTofetchObjectFields(contentSourceData.authorization, objectName,req,  function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                        });
                        break;
                    }
                    case 15: { //github
                        objectAndFields.github.requestTofetchObjectFields(contentSourceData.authorization, objectName,req, function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })


                        });
                        break;
                    }
                    case 17: { //Youtube
                        objectAndFields.youtube.requestTofetchObjectFields(contentSourceData.authorization, objectName,req, function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })

                        });
                        break;
                    }
                    case 39: { //skilljar
                        objectAndFields.skilljar.requestTofetchObjectFields(contentSourceData.authorization, objectName,req, function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })

                        });
                        break;
                    }
                    case 12: { //drive
                        objectAndFields.drive.requestTofetchObjectFields(contentSourceData.authorization, objectName,req, function (err, result) {
                            if (!err) {
                                res.send({ flag: result.flag, Objects: result.data })
                            }
                            else {
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                            }
                        });
                        break;
                    }
                    case 13: { //box
                        objectAndFields.box.requestTofetchObjectFields(contentSourceData.authorization, objectName,req, function (err, result) {
                            if (!err) {
                                res.send({ flag: result.flag, Objects: result.data })
                            }
                            else {
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                            }
                        });
                        break;
                    }
                    case 14: { //helpscout
                        objectAndFields.helpscout.requestTofetchObjectFields(contentSourceData.authorization, objectName,req, function (err, result) {
                            if (!err) {
                                res.send({ flag: result.flag, Objects: result.data })
                            }
                            else {
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                            }
                        });
                        break;
                    }
                    case 8: { //slack
                        objectAndFields.slack.requestTofetchObjectAndFields(contentSourceData.authorization, objectName,req, function (err, result) {
                            if (!err) {
                                res.send({ flag: result.flag, Objects: result.data })
                            }
                            else {
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                            }
                        });
                        break;
                    }
                    case 28: { //marketo
                        objectAndFields.marketo.requestTofetchObjectFields(contentSourceData.authorization, objectName,req,  function (err, result) {
                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                        });
                        break;
                    }
                    case 5: { //sharepoint
                        objectAndFields.sharepoint.requestTofetchObjectAndFields(contentSourceData.authorization, objectName,req, function (err, result) {
                            if (!err) {
                                res.send({ flag: result.flag, Objects: result.data })
                            }
                            else {
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                            }
                        });
                        break;
                    }
                    case 29: { //dynamics
                        dynamics.requestTofetchObjectFields(contentSourceData.authorization, contentSourceData.contentSource, objectName, function (err, result) {
                            if (!err) {
                                res.send({ flag: result.flag, Objects: result.data })
                            }
                            else {
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                            }
                        });
                        break;
                    }
                    case 2: {
                        objectAndFields.lithium.requestTofetchObjectFields(contentSourceData.authorization, objectName,req,  function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })
                        });
                        break;
                    }

                    case 49: { //contentful
                        let headers = {
                            "Content-Type": "application/json",
                            "su-crawler-secret" : config.get("crawler.sharedSecret"),
                            timeout: 20000,
                            "tenant-id": req.headers['tenant-id']
                        }
                        contentSourceData.objectName = objectName;
                        contentSourceData.eventType = 'create-object';
                        if (!contentSourceData.authorization.instanceURL) {
                            contentSourceData.authorization.instanceURL = contentSourceData.contentSource.url;
                        };
                        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-source/fetch-objects`, '', contentSourceData, headers, function (error, result) {
                            if (error) {
                                commonFunctions.errorlogger.error("Error found: ", error);
                                return res.send({ flag: error, message: error.message });
                            }
                            if (!result.status) {
                                commonFunctions.errorlogger.error("Error found: ", result.error);
                                return res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: result.data });
                            }
                            return res.send({ flag: commonFunctions.constants.SUCCESS, Objects: result.data });
                        });
                        break;
                    }
                    case 30: { //receptive
                        objectAndFields.receptive.requestTofetchObjectFields(contentSourceData.authorization, objectName,req, function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })


                        });
                        break;
                    }
                    case 32: { //docebo
                        objectAndFields.docebo.requestTofetchObjectFields(contentSourceData.authorization, objectName,req, function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })


                        });
                        break;
                    }
                    case 43: { //discourse
                        objectAndFields.discourse.requestTofetchObjectFields(contentSourceData.authorization, objectName,req, function (err, result) {

                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" })


                        });
                        break;
                    }
                    case 50: { //wistia
                        objectAndFields.wistia.requestTofetchObjectFields(contentSourceData.authorization, objectName, req, function (err, result) {
                            if (!err)
                                res.send({ flag: result.flag, Objects: result.data })
                            else
                                res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: "Please try Again" });                                
                        });
                        break;
                    }
                    case 61: {
                        let headers = {
                            "Content-Type": "application/json",
                            "su-crawler-secret": config.get("crawler.sharedSecret"),
                            timeout: 30000,
                            "tenant-id": req.headers['tenant-id']
                        }
                        contentSourceData.objectName = objectName;
                        contentSourceData.eventType = 'Fetch-object-fields'
                        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-source/fetch-objects`, '', contentSourceData, headers, function (error, result) {
                            if (error) {
                                commonFunctions.errorlogger.error("Error found: ", error);
                                return res.send({ flag: error, message: error.message });
                            }
                            if (!result.status) {
                                commonFunctions.errorlogger.error("Error found: ", result.error);
                                return res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: result.data });
                            }
                            return res.send({ flag: commonFunctions.constants.SUCCESS, Objects: result.data });
                        })
                        break;
                    }
                    case 72: { // freshdesk
                        let headers = {
                            "Content-Type": "application/json",
                            "su-crawler-secret": config.get("crawler.sharedSecret"),
                            timeout: 30000,
                            "tenant-id": req.headers['tenant-id']
                        }
                        contentSourceData.objectName = objectName;
                        contentSourceData.eventType = 'Fetch-object-fields'
                        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-source/fetch-objects`, '', contentSourceData, headers, function (error, result) {
                            if (error) {
                                commonFunctions.errorlogger.error("Error found: ", error);
                                return res.send({ flag: error, message: error.message });
                            }
                            if (!result.status) {
                                commonFunctions.errorlogger.error("Error found: ", result.error);
                                return res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: result.data });
                            }
                            return res.send({ flag: commonFunctions.constants.SUCCESS, Objects: result.data });
                        })
                        break;
                    }
                    case 73: { // freshservice
                        let headers = {
                            "Content-Type": "application/json",
                            "su-crawler-secret": config.get("crawler.sharedSecret"),
                            timeout: 30000,
                            "tenant-id": req.headers['tenant-id']
                        }
                        contentSourceData.objectName = objectName;
                        contentSourceData.eventType = 'Fetch-object-fields'
                        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-source/fetch-objects`, '', contentSourceData, headers, function (error, result) {
                            if (error) {
                                commonFunctions.errorlogger.error("Error found: ", error);
                                return res.send({ flag: error, message: error.message });
                            }
                            if (!result.status) {
                                commonFunctions.errorlogger.error("Error found: ", result.error);
                                return res.send({ flag: commonFunctions.constants.SOMETHING_WRONG, message: result.data });
                            }
                            return res.send({ flag: commonFunctions.constants.SUCCESS, Objects: result.data });
                        })
                        break;
                    }
                    default: {
                        res.send(response)
                    }
                }

            }
            else
                res.send(response)
        });

    }
})

router.get('/downloadLogFile',[securityCheck], function (req, res, next) {
    // var configObj = { "contentSourceId": req.query.content_source_id, "isCrawl": req.query.isCrawl }
    commonFunctions.errorlogger.info("Downloading admin log file, ", req.query.file);
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/log/download?file='+req.query.file, '', '', headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.setHeader("Content-Type", "text/html");
            res.setHeader("Content-Disposition", 'attachment; filename=' + req.query.file.split("/")[1]);
            res.send(result);
        }
    })

});

router.get('/fetchManagementAndUserSegmentSets', function (req, res, next) {
    var contentSourceId = req.query.content_source_id
    var response = {
        status: commonFunctions.constants.responseFlags.PARAMETER_MISSING,
        message: "PARAMETER MISSING"
    }
    commonFunctions.getContentSourceDataById(contentSourceId, req, function (err, contentSourceData) {
        if (!err) {
            async.auto({
                fetchManagementSets: function (cb) {
                    zendesk.fetchManagementSets(
                        contentSourceData,
                        function (err, result) {
                            if (!err) {
                                cb(null, {
                                    flag: commonFunctions.constants.SUCCESS,
                                    objects: result,
                                });
                            } else {
                                cb(err, null);
                            }
                        }
                    );
                },
                fetchUserSegments: function (cb) {
                    zendesk.fetchUserSegments(
                        contentSourceData,
                        function (err, result) {
                            if (!err) {
                                cb(null, {
                                    flag: commonFunctions.constants.SUCCESS,
                                    objects: result,
                                });
                            } else {
                                cb(err, null);
                            }
                        }
                    );
                }
            },
                function(err, result) {
                    if (!err) {
                        res.send({flag : 0, userSegments: result.fetchUserSegments, managementSets: result.fetchManagementSets});
                    } else {
                        res.send({
                            flag: commonFunctions.constants.SOMETHING_WRONG,
                            message: "Please try Again",
                        });
                    }
                },
            );
        } else {
            res.send(response);
        }
    });
})

router.get('/downloadJarFile', (req, res, next) => {

    let zipArchive = archiver('zip', { zlib: { level: 9 } });
    res.setHeader('Content-disposition', 'attachment; filename="Jira-Crawler.zip"');
    zipArchive.pipe(res);
    createFolderZipRec(DIRNAME + "/resources/on_premise/1_" + req.query.contentSourceId + "/download/", zipArchive, "", (error, result) => {
        if (error) commonFunctions.errorlogger.error("Error: ", error);
        else {
            zipArchive.finalize();
        }
    });
})

var createFolderZipRec = (folder, zipArchive, base, cb) => {
    // let folders = [];
    fs.readdir(folder, (err, files) => {
        if (err) cb(err);
        else {
            async.parallel(files.map(f => {
                return cb => {
                    fs.stat(path.join(folder, f), (err, stat) => {
                        if (err) cb(err);
                        else {
                            if (stat && stat.isDirectory()) {
                                createFolderZipRec(path.join(folder, f), zipArchive, path.join(base, f), cb);
                            }
                            else {
                                let stream = fs.createReadStream(path.join(folder, f));
                                let ext = f.split(".")[f.split(".").length - 1];
                                ext = ext.toLowerCase();
                                zipArchive.append(stream, {
                                    name: f,
                                    prefix: base + "/"
                                });
                                cb(null, path.join(base, f));
                            }
                        }
                    });
                };
            }), cb);
        }
    });
};

router.post('/cloneContentSource', function (req, res, next) {
    let contentSource = [];
    contentSource.content_source_type_id = req.body.content_source_type_id;
    async.auto({
        getContentSource: cb => {
            let contentSourceId = req.body.id;
            req.clone = true;
            getContentSourceData(contentSourceId, req,function (err, result) {
                var data = {
                    contentSource: result.get_content_source_and_auth.contentSource,
                    authorization: result.get_content_source_and_auth.authorization,
                    objectsAndFields: result.get_object_and_fields,
                    spacesORboards: result.get_spaces,
                    saveurlConfiguration: result.getWebConf,
                    webConf :result.getWebConf,
                    language : result.get_content_source_languages
                }

                cb(err, data)
            });
        },
        updateContentSource: ['getContentSource', (results, cb) => {

            var dataToSend = results.getContentSource;

            dataToSend.contentSource.label = req.body.name;
            delete dataToSend.contentSource.name;
            delete dataToSend.contentSource.elasticIndexName;
            delete dataToSend.authorization.id;
            delete dataToSend.authorization.content_source_id;
            delete dataToSend.contentSource.id;
            delete dataToSend.contentSource.last_sync_date;
            delete dataToSend.contentSource.sync_frequency;
            delete dataToSend.contentSource.sync_frequency_index;
            dataToSend.contentSource.pid = 0;
            dataToSend.contentSource.crawl_status = 0;

            dataToSend.contentSource.sync_frequency_name = 'Never';
            dataToSend.objectsAndFields.map(o => {
                delete o.id
                delete o.content_source_id
                o.fields.map(f => {
                    delete f.id;
                    delete f.content_source_object_id;
                })
            })

            dataToSend.spacesORboards.map(o => {
                delete o.id
                delete o.content_source_id
            });
            if(Object.keys(dataToSend.webConf).length){
                delete dataToSend.webConf.id
                delete dataToSend.webConf.content_source_id
            }
            if(Object.keys(dataToSend.saveurlConfiguration).length){
                delete dataToSend.saveurlConfiguration.id
                delete dataToSend.saveurlConfiguration.content_source_id
            }

            addContentSource(dataToSend,req, function (err, result) {
                if (!err) {
                    if ([
                        constants.CONTENT_SOURCE_TYPE.website,
                        constants.CONTENT_SOURCE_TYPE.jsWeb
                    ].includes(result.insert_content_source_id.content_source_type_id) && (result.insert_content_source_id.isFileUploaded == true)) {
                        var fileUploadUrl = result.insert_content_source_id.url.split("sitemap/");
                        result.insert_content_source_id.url = fileUploadUrl[fileUploadUrl.length - 1]
                    }
                    
                    dataToSend.authorization = { ...result.insert_content_source_authorization }
                    if(result.insert_content_source_id.editing){
                        if (result.insert_content_source_id.content_source_type_id == constants.CONTENT_SOURCE_TYPE.youtube && result.insert_content_source_authorization.client_id  == commonFunctions.appVariables.youtube.clientId ||
                    result.insert_content_source_id.content_source_type_id == constants.CONTENT_SOURCE_TYPE.drive && result.insert_content_source_authorization.client_id  == commonFunctions.appVariables.drive.clientId){
                        if(isOuthUpdatedFromFe){
                            result.insert_content_source_id["isOauthUpdated"] = true;
                            console.log('invalid credentials provided')
                            res.send({ status: 403, message: "invalid credentials provided" })
                            throw new Error('invalid credentials provided');
                        }
                        delete result.insert_content_source_authorization.client_id;
                        delete result.insert_content_source_authorization.client_secret;
                    }
                    else {
                        result.insert_content_source_id["isOauthUpdated"] = true;
                    }
                    }
                    else {
                        result.insert_content_source_id["isOauthUpdated"] = true;
                    }

                    constants.PROTECTED_AUTH_KEYS.forEach((key) => {
                        delete result.insert_content_source_authorization[key];
                    });

                    dataToSend.contentSource.sync_frequency = Array.isArray(result.insert_content_source_id.sync_frequency) ? result.insert_content_source_id.sync_frequency : result.insert_content_source_id.sync_frequency !== null ? `${result.insert_content_source_id.sync_frequency}`.split(',').map(ele => Number(ele)) : [1];
                    dataToSend.contentSource.sync_frequency_index = Array.isArray(result.insert_content_source_id.sync_frequency_index) ? result.insert_content_source_id.sync_frequency_index : result.insert_content_source_id.sync_frequency_index !== null ? `${result.insert_content_source_id.sync_frequency_index}`.split(',').map(ele => Number(ele)) : [1];
                    dataToSend.contentSource.sync_frequency_name_index = result.insert_content_source_id.sync_frequency_name_index;
                    delete dataToSend.contentSource.logFile;
                    delete dataToSend.contentSource.adminLogFile;
                    if (!result.insert_content_source_id.contentSourceAddedFirstTime && body.getCSDataAuth && body.getCSDataAuth.contentSource && body.getCSDataAuth.contentSource.elasticIndexName) {
                        dataToSend.contentSource["elasticIndexName"] = body.getCSDataAuth.contentSource.elasticIndexName;
                    }
                    delete result.insert_content_source_id.contentSourceAddedFirstTime;
                    commonFunctions.CleanRedisCache();
                    if (config.get("kafkaTopic.enable")) {
                        var configTables = ['content_sources', 'content_source_languages', 'content_source_authorization', 'content_source_objects', 'content_source_object_fields', 'content_source_spaces', 'api_crawler_fields', 'website_setting'];
                        contentSourceConfigurationTokafka.content_Sourse_Config(configTables, result.insert_content_source_id.id,req, function (err, configResult) {

                            if (err) {
                                res.send({
                                    contentSource: result.insert_content_source_id.id,
                                    authorization: result.insert_content_source_authorization,
                                    objectsAndFields: result.insert_content_source_objects_and_fields,
                                    spacesORboards: [],
                                    oauth: result.get_auth_intermediate_Callback,
                                    getWebConf: result.saveurlConfiguration,
                                    apiCrawlerFields: result.insert_api_crawler_fields,
                                    language: result.insert_content_source_languages.languages
                                })
                            } else {
                                dataToSend.objectsAndFields = [...result.insert_content_source_objects_and_fields_elastic];
                                dataToSend.authorization.instanceURL = configResult.content_source_authorization[0].instanceURL;
                                dataToSend.tenantId = req.headers['tenant-id'] 
                                dataToSend.session = req.headers.session;
                                dataToSend.language = [];
                                dataToSend.authorization.is_authenticated = configResult.content_source_authorization[0].is_authenticated;
                                dataToSend.contentSource.is_paused = configResult.content_sources[0].is_paused;
                                kafkaLib.publishMessage({ 
                                    topic   : kafkaLib.SU_CRAWLER_TOPIC.contentSource,
                                    messages: [{ 
                                        value : JSON.stringify({ type:kafkaLib.KAFKA_EVENT_TYPES.add, data:dataToSend, tenantId: req.headers['tenant-id'] })
                                    }]
                                });
                                result.insert_content_source_authorization.is_authenticated = dataToSend.authorization.is_authenticated;
                                result.insert_content_source_id.is_paused = dataToSend.contentSource.is_paused;
                                const dataForStatusPage = {
                                    ...dataToSend,
                                    contentSource: {
                                        ...dataToSend.contentSource,
                                        content_source_type_name: result.get_type_name && result.get_type_name.name ? result.get_type_name.name : '',
                                    },
                                };
                                kafkaStatusLib.publishMessage({ 
                                    topic:config.get("statusPageService.kafka.contentSourceTopic"),
                                    messages: [{ 
                                        value : JSON.stringify({ type:kafkaLib.KAFKA_EVENT_TYPES.add, data:dataForStatusPage, tenantId: req.headers['tenant-id'] })
                                    }]
                                });
                                result.insert_content_source_id.index_name = result.insert_content_source_id.elasticIndexName;
                                configResult.content_sources[0].index_name = configResult.content_sources[0].elasticIndexName;
                                delete result.insert_content_source_id.elasticIndexName;
                                delete configResult.content_sources[0].elasticIndexName;
                                res.send({
                                    contentSource: result.insert_content_source_id,
                                    authorization: result.insert_content_source_authorization,
                                    objectsAndFields: result.insert_content_source_objects_and_fields,
                                    spacesORboards: [],
                                    oauth: result.get_auth_intermediate_Callback,
                                    getWebConf: result.saveurlConfiguration,
                                    apiCrawlerFields: result.insert_api_crawler_fields,
                                    language: result.insert_content_source_languages.languages,
                                    allConfig: configResult
                                })
                            }

                        });
                    }
                    else {
                        res.send({
                            contentSource: result.insert_content_source_id,
                            authorization: result.insert_content_source_authorization,
                            objectsAndFields: result.insert_content_source_objects_and_fields,
                            spacesORboards: [],
                            oauth: result.get_auth_intermediate_Callback,
                            getWebConf: result.saveurlConfiguration,
                            apiCrawlerFields: result.insert_api_crawler_fields,
                            language: result.insert_content_source_languages.languages
                        })
                    }
                } else {
                    //in case of error as well 200 response is being sent which is misleading
                    if (err && (err.sqlMessage || (err.stack && jsErrors.includes(err.stack.split(':')[0])))) {
                        err = {
                            code: 500,
                            message: 'Internal Server Error'
                        }
                    }
                    console.log("Clone ContentSource error ", err);
                    res.send(err);
                    //res.send({ contentSource: dataToSend.contentSource, authorization: dataToSend.authorization, objectsAndFields: dataToSend.objectsAndFields, spacesORboards: [], oauth: '', language: dataToSend.language });
                    cb(null, {
                        contentSource: dataToSend.contentSource,
                        authorization: dataToSend.authorization,
                        objectsAndFields: dataToSend.objectsAndFields,
                        spacesORboards: [],
                        oauth: dataToSend.oAuth
                    });
                }
            })
        }],
    }, (error, result) => {
        if (error)
            commonFunctions.errorlogger.error("error in cloning", error)
        else {
            delete result.updateContentSource.authorization.accessToken;
            delete result.updateContentSource.authorization.refreshToken;
            res.send(result)
        }
    });
})

const insertSpacesBoards = function(resultData, contentSourceObjectArr, sort,req, cb ){
    commonFunctions.insertSpacesBoards(resultData.authorization.content_source_id, contentSourceObjectArr,req, (err,res) => {
        if(err){
            commonFunctions.errorlogger.error("error while inserting places:", err); 
            cb(null,[]);
        }else{
            commonFunctions.getPlacesBySortParam(resultData.authorization.content_source_id, sort,req, (errPlaces,resPlaces) => {
                if(errPlaces){
                    commonFunctions.errorlogger.error("error while getting places:", errPlaces); 
                    cb(null,[]);
                }else{
                    cb(null,resPlaces);
                }
            });
        }
    });
}

const getContentSourceData = function (contentSourceId,req, cb) {
    async.auto({
        get_content_source_and_auth: function (cb) {
            commonFunctions.getContentSourceDataById(contentSourceId, req, function (err, contentSourceData) {
                if (!err)
                    cb(null, contentSourceData)
                else
                    cb(err, [])
            });
        },
        get_content_source_languages: function (cb) {
            let query = `SELECT code from content_source_languages WHERE content_source_id = ?`;
            var language_codes = [];
            connection[req.headers['tenant-id']].execute.query(query, [contentSourceId], (err, docs) => {
                if (err) {
                    cb(err, [])
                } else {
                    for (var i = 0; i < docs.length; i++) {
                        language_codes.push(docs[i].code);
                    }
                    cb(null, language_codes);
                }
            })
        },
        get_object_and_fields:['get_content_source_and_auth', function (aboveData, cb) {
            commonFunctions.getContentSourceObjectsAndFieldsById(contentSourceId,req, function (err, dataObjectFields) {
                if (!err) {
                    if(commonFunctions.constants.CONTENT_SOURCES_WITH_UNIQUE_FIELD_NAMES
                        .includes(aboveData.get_content_source_and_auth.contentSource.content_source_type_id)){
                            dataObjectFields = commonFunctions.getFieldsApiName(dataObjectFields);
                        }
                    if (aboveData.get_content_source_and_auth.contentSource.content_source_type_id == 3) {
                        for (const object of dataObjectFields) {
                            object.fields.forEach(field => {
                                const regexForUnderscore = /(?:[^_]_){1}[^_]*$/;
                                if (field.name.toLowerCase().includes('_') && !field.name.toLowerCase().endsWith('__c') 
                                && (!field.name.toLowerCase().includes('__') || (field.name.toLowerCase().includes('__') && regexForUnderscore.test(field.name)))
                                && field.name.toLowerCase().slice(-5) !== '_flat' && field.name.toLowerCase().slice(-10) !== '_flat_name'
                                    && field.name.toLowerCase().slice(-11) !== '_navigation' && field.name.toLowerCase().slice(-7) !== '_nested' && !field.name.toLowerCase().includes("__typecasted__")
                                && !field.name.toLowerCase().includes('attachment_') && !field.annotated) {
                                    const lastIndexOfDoubleUnderscore = field.name.lastIndexOf('__');
                                    field.name = field.name.substring(0, lastIndexOfDoubleUnderscore) + field.name.substring(lastIndexOfDoubleUnderscore).replace(/(?<!_)_(?!_)/g, '.');
                                }
                                field.name =  field.name.replace(/(__r)_/g, '$1.');
                            })
                        }
                    } 
                    cb(null, dataObjectFields)
                }
                else
                    cb(err, [])
            })
        }],
        get_api_crawler_fields: ['get_content_source_and_auth', function (aboveData, cb) {
            commonFunctions.getApiCrawlerFieldsById(contentSourceId,req, function (err, contentSourceData) {
                if (!err)
                    cb(null, contentSourceData)
                else
                    cb(err, [])
            });
        }],
        get_spaces: ['get_content_source_and_auth', function (aboveData, cb) {
            if (req.clone || (aboveData.get_content_source_and_auth.contentSource.content_source_type_id == 12) || (aboveData.get_content_source_and_auth.contentSource.content_source_type_id == 13) || (aboveData.get_content_source_and_auth.contentSource.content_source_type_id == 24) || (aboveData.get_content_source_and_auth.contentSource.content_source_type_id == 23) || (aboveData.get_content_source_and_auth.contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.aem)) {
                commonFunctions.getContentSourceSpaceBoardsById(contentSourceId,req, function (err, spaces) {
                    cb(null, spaces);
                })
            } else {
                cb(null, []);
            }
        }],
        getWebConf: ['get_content_source_and_auth', (result, cb) => {
            if ([
                constants.CONTENT_SOURCE_TYPE.website,
                constants.CONTENT_SOURCE_TYPE.jsWeb
            ].includes(result.get_content_source_and_auth.contentSource.content_source_type_id) || result.get_content_source_and_auth.contentSource.content_source_type_id === 10) {
                connection[req.headers['tenant-id']].execute.query("SELECT id, include_url, exclude_url,index_filters,javascript_enabled,sitemap_enabled,session,outlink_filters,jsCrawling_Filter,anchor_depth, anchor_url, authenticate_before_crawl FROM website_setting WHERE content_source_id=?", [contentSourceId], (error, rows) => {
                    if (error)
                        cb(error);
                    else {
                        cb(null, rows[0]);
                    }
                });
            }
            else {
                cb(null, {});
            }
        }]
    }, function (err, result) {
        cb(err, result);
    })
}

const addContentSource = function (data,req, cb) {
    let contentSource = data.contentSource;
    let authorization = data.authorization;
    let spacesORboards = data.spacesORboards;
    let objectsAndFields = data.objectsAndFields;
    if([
        constants.CONTENT_SOURCE_TYPE.contentful,
        constants.CONTENT_SOURCE_TYPE.customContentSource,
        constants.CONTENT_SOURCE_TYPE.jsWeb
    ].includes(contentSource.content_source_type_id)){
        for(const obj of objectsAndFields){
            const { fields } = obj;
            for (const field of fields){
                 if(field.name === 'post_time'){
                     field.isMerged = 1;
                 }
            }
         }
    }
    let apiCrawlerFields = data.apiCrawlerFields;
    var includeUrlArray = [];
    var excludeUrlArray = [];

    if ([
        constants.CONTENT_SOURCE_TYPE.website,
        constants.CONTENT_SOURCE_TYPE.jsWeb
    ].includes(contentSource.content_source_type_id)) {
        if (contentSource.isFileUploaded == false || contentSource.isFileUploaded == 'false') {
            // while (contentSource.url.slice(-1) == "/") {
            //     contentSource.url = contentSource.url.substring(0, contentSource.url.length - 1);
            // }
            var urlObj = url.parse(contentSource.url, true);
            if ((urlObj.protocol !== "http:" && urlObj.protocol !== "https:")
                || urlObj.host === "localhost"
                || urlObj.host === "127.0.0.1"
                || /(?:10|127|172\.(?:1[6-9]|2[0-9]|3[01])|192\.168)\./.test(urlObj.host)) {
                cb({ status: 403, message: "Invalid Url" });
                return;
            }
            else if (!(/^[^\\\/\*\?\"\'\<\>|\,\n\t]+$/.test(contentSource.name))) {
                cb({ status: 403, message: "Invalid Name" });
                return;
            }
            data.oAuth = {};
            addContentSourceData(data, contentSource, authorization, spacesORboards, objectsAndFields, apiCrawlerFields,req, (err, respo) => {
                cb(null, respo);
            });
        } else {
            let headers = {
                "Content-Type": "application/json",
                "su-crawler-secret" : config.get("crawler.sharedSecret"),
                "tenant-id": req.headers['tenant-id'] 
            }
            commonFunctions.httpRequest('GET', 
            config.get("crawler.crawlerUrl") + '/file/getUrls?csTypeId=' + contentSource.content_source_type_id, '', 
            {filePath : contentSource.url}, headers, function (error, result) {
                if (error) {
                    commonFunctions.errorlogger.error("getUrls error: ", error);
                    return cb(error);
                }
                else {
                    commonFunctions.errorlogger.info("Get Urls success, result: ", result);
                    if(result.data.data) {
                        result = result.data;
                    }
                    data.uploadedfileUrlResponse = result;
                    if(!data.uploadedfileUrlResponse.success){
                        return cb({status: 403, message : "uploaded file has errors"});
                    }
                    if(data.uploadedfileUrlResponse.data.invalidUrls){
                        return cb({status : 403, message: "invalid Urls in uploaded file"});
                    }
                    data.oAuth = {};
                    contentSource.url = data.uploadedfileUrlResponse.data.filePath;
                    addContentSourceData(data, contentSource, authorization, spacesORboards, objectsAndFields, apiCrawlerFields,req,(err, respo) => {
                        cb(null, respo);
                    });
                }
            })
        }
    } else {
        if (contentSource.content_source_type_id !== constants.CONTENT_SOURCE_TYPE.skillJar 
            && contentSource.content_source_type_id !== constants.CONTENT_SOURCE_TYPE.seismic
            && contentSource.content_source_type_id !== constants.CONTENT_SOURCE_TYPE.contentful
            && contentSource.content_source_type_id !== constants.CONTENT_SOURCE_TYPE.file
            && contentSource.content_source_type_id !== constants.CONTENT_SOURCE_TYPE.seismic
            && contentSource.content_source_type_id !== constants.CONTENT_SOURCE_TYPE.wistia) {
            if (data.authorization.authorization_type === 'OAuth' && contentSource.content_source_type_id === constants.CONTENT_SOURCE_TYPE.confluence) {
              contentSource.url = 'https://api.atlassian.com';
            }
            while (contentSource.url.slice(-1) == "/") {
                contentSource.url = contentSource.url.substring(0, contentSource.url.length - 1);
            }
            var urlObj = url.parse(contentSource.url, true);
            if ((urlObj.protocol !== "http:" && urlObj.protocol !== "https:")
                || urlObj.host === "localhost"
                || urlObj.host === "127.0.0.1"
                || ( 
                    constants.IP_REGEX.test(urlObj.host)
                    && constants.IS_PRIVATE_IP.test(urlObj.host)
                   )
                ) {
                cb({ status: 403, message: "Invalid Url" });
                return;
            }
        }
        else if (!(/^[^\\\/\*\?\"\'\<\>|\,\n\t]+$/.test(contentSource.name))) {
            cb({ status: 403, message: "Invalid Name" });
            return;
        }
        data.oAuth = {};
        addContentSourceData(data, contentSource, authorization, spacesORboards, objectsAndFields, apiCrawlerFields,req, (err, respo) => {
            //delete if oauth fails in jira/confluence when creating new cs
            if([constants.CONTENT_SOURCE_TYPE.confluence,constants.CONTENT_SOURCE_TYPE.jira].includes(data.contentSource.content_source_type_id) 
                && data.contentSource.editing == false &&
            data.authorization.authorization_type == 'OAuth' && respo.get_auth_intermediate_Callback.status == false){
                deleteContentSource(data.contentSource.id, data.contentSource.content_source_type_id, 1, req, (err, data) => {
                    cb({ status: 403, message: "OAuth failed" });
                });
            }
            cb(null, respo);
        });
    }
}

function addContentSourceData(data, contentSource, authorization, spacesORboards, objectsAndFields, apiCrawlerFields,req, callback) {
    async.auto({
        insert_content_source_id: function (cb) {

            commonFunctions.insertUpdateContentSource(contentSource,req, function (errAsync, contenSourceResult) {
                if (errAsync) {
                    commonFunctions.errorlogger.error("Error inside insert_content_source", errAsync);
                    cb(errAsync, [])
                }
                else {
                    contentSource = contenSourceResult;

                    if(data.contentSource && data.objectsAndFields && data.objectsAndFields.length) {
                        const mergeFieldQuery = `UPDATE merge_field_cs_object_field_mapping SET cs_label = ? WHERE cs_id = ?`;
                        
                        let objectQueries = [];
                        let fieldQueries = [];

                        for (const item of data.objectsAndFields) {
                            if(item.id && item.label) {
                                objectQueries.push({
                                    query: `UPDATE merge_field_cs_object_field_mapping SET cs_object_label = ? WHERE cs_object_id = ?`,
                                    parameters: [item.label, item.id]
                                });
                            }

                            for(const field of item.fields) {
                                if(field.id && field.label) {
                                    fieldQueries.push({
                                        query: `UPDATE merge_field_cs_object_field_mapping SET cs_object_field_label = ? WHERE cs_object_field_id = ?`,
                                        parameters: [field.label, field.id]
                                    });
                                }
                                
                            }
                        }

                        const connectionInstance = connection[req.headers['tenant-id']].execute;
    
                        const updateObjectPromise = objectQueries.map(item => (
                            new Promise((resolve, reject) => {
                                connectionInstance.query(item.query, item.parameters, (err, res) => {
                                    if (err) reject(err);
                                    else resolve(res);
                                });
                            })
                        ))
    
                        const updateObjectFieldPromise = fieldQueries.map(item => (
                            new Promise((resolve, reject) => {
                                connectionInstance.query(item.query, item.parameters, (err, res) => {
                                    if (err) reject(err);
                                    else resolve(res);
                                });
                            })
                        ));
    
                        const updateMergeFieldPromise = new Promise((resolve, reject) => {
                            connectionInstance.query(mergeFieldQuery, [data.contentSource.label, data.contentSource.id], (err, res) => {
                                if (err) reject(err);
                                else resolve(res);
                            });
                        });
    
                        Promise.all([
                            ...updateObjectPromise,
                            updateMergeFieldPromise,
                            ...updateObjectFieldPromise
                        ])
                            .then(() => {
                                cb(null, contentSource);
                            })
                            .catch(error => {
                                commonFunctions.errorlogger.error("Error executing queries", error);
                                cb(error, []);
                            });
                    }
                    else {
                        cb(null, contentSource);
                    };
                }
            })
        },
        get_type_name: function (cb) {
            commonFunctions.getContentsourceType(contentSource.content_source_type_id,req, cb)
        },
        insert_content_source_languages:['insert_content_source_id', function (dataFromAbove, cb) {
            var languages = data.language || [];
            
            let existingLanguageCodes = [];
            let toBeDeletedCodes = [];
            let toBeAddedCodes = [];
            let langCodeMap = {};
            connection[req.headers['tenant-id']].execute.query("SELECT * FROM content_source_languages WHERE content_source_id = ?", [contentSource.id], (err, res) => {
                if(err){
                    console.log("err in getting cs lang ", err);
                    cb(err);
                } else {

                    for(let i = 0 ; i < res.length; i++){
                        existingLanguageCodes.push(res[i].code);
                        if(!languages.includes(res[i].code)){
                            toBeDeletedCodes.push(res[i].code);
                        }
                    }
                    for(let i = 0 ; i < languages.length; i++){
                        if(!existingLanguageCodes.includes(languages[i])){
                            toBeAddedCodes.push(languages[i]);
                        }
                    }
                    
                    if(!toBeAddedCodes.length){
                        return cb(null, {
                            toBeDeletedCodes,
                            languages
                        });
                    } else {
                        connection[req.headers['tenant-id']].execute.query('SELECT * FROM languages', [], (err, res) => {
                            if(err){
                                console.log("error in getting languages ", err);
                                return cb(err);
                            } else {
                                for(let i = 0; i < res.length; i++){
                                    langCodeMap[res[i].code] = res[i];
                                }
                                let query = 'INSERT into content_source_languages (content_source_id, code, name, analyzer) VALUES ';
                                let placeHolders = [];
                                let values = [];
                                for(let i = 0; i < toBeAddedCodes.length; i++){
                                    values.push(contentSource.id);
                                    values.push(toBeAddedCodes[i]);
                                    values.push(langCodeMap[toBeAddedCodes[i]].name);
                                    values.push(langCodeMap[toBeAddedCodes[i]].analyzer);
                                    placeHolders.push("(?,?,?,?)");
                                }

                                query += placeHolders.join(",");
                                connection[req.headers['tenant-id']].execute.query(query, values, (err, res) => {
                                    if(err){
                                        console.log("error in inserting languages ", err);
                                        return cb(err);
                                    } else {
                                        return cb(null, {
                                            toBeDeletedCodes,
                                            languages
                                        });
                                    }
                                });
                            }
                            
                        });
                    }
                }
            });
        }],
        delete_content_source_languages:['insert_content_source_languages', function (dataFromAbove, cb) {
            const {toBeDeletedCodes} = dataFromAbove.insert_content_source_languages;
            
            if(!toBeDeletedCodes.length){
                return cb(null);
            }
            connection[req.headers['tenant-id']].execute.query("DELETE FROM content_source_languages WHERE content_source_id = ? AND code IN (?)", [contentSource.id, toBeDeletedCodes], (err, res) => {
                if(err){
                    console.log("err in deleting cs lang ", err);
                    cb(err);
                } else {
                    return cb(null);
                }
            });
        }],
        create_Cron: ['insert_content_source_id', 'get_type_name', function (dataFromAbove, cb) {
            //set cron for
            commonFunctions.errorlogger.info("Cron is going to set for crawler");
            let contentSource = dataFromAbove.insert_content_source_id
            let freqObj = {
                contentSourceId: dataFromAbove.insert_content_source_id.id,
                tenantId: req.headers["tenant-id"],
                syncFrequency: Array.isArray(dataFromAbove.insert_content_source_id.sync_frequency) ? dataFromAbove.insert_content_source_id.sync_frequency : dataFromAbove.insert_content_source_id.sync_frequency !== null ? `${dataFromAbove.insert_content_source_id.sync_frequency}`.split(',').map(ele => Number(ele)) : [1],
                syncFrequencyName: dataFromAbove.insert_content_source_id.sync_frequency_name,
                contentSourceTypeId: dataFromAbove.insert_content_source_id.content_source_type_id,
                session: req.headers.session
            }

            if(dataFromAbove.insert_content_source_id.sync_frequency_index ){
                freqObj.syncFrequencyIndex = Array.isArray(contentSource.sync_frequency_index) ? contentSource.sync_frequency_index : contentSource.sync_frequency_index !== null ? `${contentSource.sync_frequency_index}`.split(',').map(ele => Number(ele)) : [0];
            }
            if(dataFromAbove.insert_content_source_id.sync_frequency_name_index){
                freqObj.syncFrequencyNameIndex = contentSource.sync_frequency_name_index;
            }
            if ((contentSource.hasOwnProperty('sync_frequency') && contentSource.hasOwnProperty('sync_frequency_name') )|| (contentSource.hasOwnProperty('sync_frequency_index') && contentSource.hasOwnProperty('sync_frequency_name_index')) ) {
                let headers = {
                    "Content-Type": "application/json",
                    "su-crawler-secret" : config.get("crawler.sharedSecret"),
                    "tenant-id": req.headers['tenant-id'] 
                }
                commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/set-frequency', '', freqObj, headers, function (error, result) {
                    if (error) {
                        commonFunctions.errorlogger.error("Cron not set: ", error);
                        cb(error);
                    }
                    else {
                        if(result.status) {
                            result = result.data;
                        }
                        cb(null, result);
                    }
                })
            }
            else { cb(null, []) }
        }],
        changeYoutubeScopePermissions: function (cb) {
            if (authorization.changeScopePermissions && contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.youtube ) {
                youtube.revokeAccess(authorization, function (err, dataReturn) {
                    if (err) {
                        cb(err, []);
                    } else {
                       cb(null, dataReturn);
                    }
                });
            }else{
                cb(null, []);
            }
        }, 
        insert_content_source_authorization: ['insert_content_source_id', 'changeYoutubeScopePermissions' ,function (getDataAbove, cb) {
            authorization.permission =  authorization.permission ? authorization.permission : '';
            authorization.content_source_id = contentSource.id;

            if ((contentSource.content_source_type_id == 4 && authorization.authorization_type!='OAuth')
                || contentSource.content_source_type_id == 5
                || (contentSource.content_source_type_id == 6 && authorization.authorization_type != 'OAuth')
                || contentSource.content_source_type_id == 27
                || contentSource.content_source_type_id == 39 
                || contentSource.content_source_type_id == 7 
                || contentSource.content_source_type_id == 34 
                || contentSource.content_source_type_id == 59
                || contentSource.content_source_type_id == 28
                || contentSource.content_source_type_id == 70
                || contentSource.content_source_type_id == 68
                || contentSource.content_source_type_id == 72
                || contentSource.content_source_type_id == 73) {
                authorization.instanceURL = contentSource.url;
            }
            else if (contentSource.content_source_type_id == 3)
                authorization.client_secret = commonFunctions.appVariables.salesforce.appSecret;

            else if (contentSource.content_source_type_id == 5) {
                if (authorization.authorization_type != "Basic") {
                authorization.client_id = authorization.client_id;
                authorization.client_secret = authorization.client_secret;
                }
            }
            else if (contentSource.content_source_type_id == 12) {
                authorization.client_id = authorization.client_id || commonFunctions.appVariables.drive.clientId;
                if (!authorization.client_secret) {
                    delete authorization.client_secret;
               } else if (authorization.client_id == commonFunctions.appVariables.drive.clientId) {
                authorization.client_secret = commonFunctions.appVariables.drive.appSecret;
               }
            }
            else if (contentSource.content_source_type_id == 13) {
                authorization.client_id = commonFunctions.appVariables.box.clientId;
                authorization.client_secret = commonFunctions.appVariables.box.appSecret;
            }
            else if (contentSource.content_source_type_id == 17) {
                authorization.client_id = authorization.client_id || commonFunctions.appVariables.youtube.clientId;
                if (!authorization.client_secret) {
                     delete authorization.client_secret;
                } else if (authorization.client_id == commonFunctions.appVariables.youtube.clientId) {
                    authorization.client_secret = commonFunctions.appVariables.youtube.appSecret;
                }
            }
            else if (contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.gmail) {
                authorization.client_id = authorization.client_id || commonFunctions.appVariables.youtube.clientId;
                if (!authorization.client_secret) {
                     delete authorization.client_secret;
                } else if (authorization.client_id == commonFunctions.appVariables.youtube.clientId) {
                    authorization.client_secret = commonFunctions.appVariables.youtube.appSecret;
                }
            }

            else if (contentSource.content_source_type_id == 15){
                authorization.client_id = commonFunctions.appVariables.github.clientId;
                authorization.client_secret = commonFunctions.appVariables.github.appSecret;
            }
            else if (contentSource.content_source_type_id == 2) {
                authorization.instanceURL = contentSource.url;
                // authorization.authorization_type = authorization.authorization_type.split(',');
            }
            else if (contentSource.content_source_type_id == 26) {
                if (authorization.authorization_type != "Basic") {
                    authorization.client_id = authorization.client_id;
                    //authorization.client_secret = authorization.client_secret;
                }
            }

            else if (contentSource.content_source_type_id == 18) {
                authorization.client_secret = commonFunctions.appVariables.stackoverflow.clientSecret;
                authorization.client_id = commonFunctions.appVariables.stackoverflow.clientId;
            }
            else if (contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.azureDevops) {
                authorization.authorization_type = 0 //only pat supported for now
            }
            else if (contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.microsoftTeams) {
                authorization.client_secret = appVariables.microsoftTeams.appSecret;
                authorization.client_id = appVariables.microsoftTeams.clientId;
            }
            else if(contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.vimeo){
                authorization.client_secret = appVariables.vimeo.appSecret;
            }
            else if(contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.slack){
                authorization.client_secret = appVariables.slack.appSecret;
            }
            else if (contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.wistia) { // for wistia
                authorization.client_secret = appVariables.wistia.clientSecret;
                authorization.client_id = appVariables.wistia.clientId;
            }
            else if (contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.dropbox) {
                if(!Number(authorization.organization_user_type)){
                    authorization.client_secret = appVariables.dropbox.appSecret;
                }
            }
            commonFunctions.insertUpdateAuthorization(authorization,req, function (errAsync, rows) {
                if (errAsync) {
                    commonFunctions.errorlogger.error("Error inside insert_content_source_authorization", errAsync);
                    cb(errAsync, [])
                }
                else {
                    if (!authorization.id) {
                        authorization.id = rows.insertId;
                        authorization.connection_status = false;
                    }
                    if (contentSource.content_source_type_id == 27 && authorization.authorization_type == "Basic") {
                        jiraOnPrem.createMetaFolder(contentSource, authorization, function (err, result) {
                            cb(null, authorization);
                        });
                    }
                    else {
                        cb(null, authorization);
                    }
                }
            })
        }],
        insert_content_source_spaces_boards: ['insert_content_source_id', function (getDataAbove, cb) {
            if (!req.clone && (contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.aem || contentSource.content_source_type_id == 12 || contentSource.content_source_type_id == 13 || contentSource.content_source_type_id == 24 || contentSource.content_source_type_id == 23 || contentSource.content_source_type_id == 30)) {
                spacesORboards = [];
            }
            commonFunctions.insertSpacesBoards(contentSource.id, spacesORboards,req, function (errAsync, rows) {
                if (errAsync) {
                    commonFunctions.errorlogger.error("Error inside insert_content_source_boardsSpaces", errAsync);
                    cb(errAsync, [])
                }
                else {

                    spacesORboards = rows;
                    cb(null, [])
                }
            })
        }],
        getSpaces: ['insert_content_source_id', 'insert_content_source_spaces_boards', function (getDataAbove, cb){
            if(spacesORboards && spacesORboards.length){
                data.spacesORboards = spacesORboards;
                return cb(null, []);
            } else {
                commonFunctions.getContentSourceSpaceBoardsById(contentSource.id,req, function (errRows, spaces) {
                    if (errRows) {
                        commonFunctions.errorlogger.error("Error inside getSpaces", errRows);
                        cb(errRows, [])
                    }
                    else {
                        data.spacesORboards = spaces;
                        
                        cb(null, [])
                    }
                });
            }
        }],
        loadMapping: ['insert_content_source_id', function (getDataAbove, cb){
            let mapping;
            if(constants.CS_WITH_MAPPING_FOR_DB_AND_ES.includes(contentSource.content_source_type_id)){ 
                commonFunctions.metadata.loadMappingForDbAndElastic(contentSource.content_source_type_id.toString(), req.headers['tenant-id'],  (err, res) => {
                    if (err){
                        commonFunctions.errorlogger.error("Error while loading mapping for Db and Elastic");
                        cb(err, null);
                    } else {
                        mapping = res;
                        cb(null , mapping);
                    }
                })
            } else{
                mapping = commonFunctions.metadata.loadMappingForDbAndElastic(contentSource.content_source_type_id.toString());
                cb(null, mapping)
            }
        }],
        insert_content_source_objects_and_fields: ['insert_content_source_id', 'loadMapping', function (getDataAbove, cb) {

            if (!objectsAndFields.length) //first time no objects and fields
            {
              objectsAndFields = getDataAbove.loadMapping.objects ? JSON.parse(JSON.stringify(getDataAbove.loadMapping.objects)) : [];
            }

            const taskObjects = []

            if ((objectsAndFields.filter(x => x.name == "feeditem").length == 1 && objectsAndFields.filter(x => x.name == "feeditem_1").length != 1) && !getDataAbove.insert_content_source_id.editing) {
                objectsAndFields.push(Object.assign({}, objectsAndFields.find(x => x.name == "feeditem")))
                objectsAndFields[objectsAndFields.length - 1].name = "feeditem_1"
                objectsAndFields[objectsAndFields.length - 1].label = "feeditem 1"
                delete objectsAndFields[objectsAndFields.length - 1].id
            }

            for (var o = 0; o < objectsAndFields.length; o++) {
                taskObjects.push((function (o) {
                    return function (cb) {
                        objectsAndFields[o].content_source_id = contentSource.id;
                        if(contentSource.content_source_type_id === constants.CONTENT_SOURCE_TYPE.contentful && !objectsAndFields[o].field_conditions) {
                            const tempName = objectsAndFields[o].name.toString();
                              objectsAndFields[o].field_conditions = tempName;
                        }
                        commonFunctions.insertObject(objectsAndFields[o],req, function (err, responseObject) {

                            if (!err) {
                                objectsAndFields[o].id = responseObject.id;
                                if(!objectsAndFields[o].fields.length){
                                    let objTemps = getDataAbove.loadMapping.objects ? JSON.parse(JSON.stringify(getDataAbove.loadMapping.objects)) : []; 
                                    objTemps = objTemps.find(p => p.name == objectsAndFields[o].name);
                                    if (objTemps && objTemps.fields.length) {
                                        objectsAndFields[o].fields = objTemps.fields;
                                    } else if (!objTemps && contentSource.content_source_type_id === constants.CONTENT_SOURCE_TYPE.zendesk) {
                                        objectsAndFields[o].fields = [];
                                    } else {
                                        objectsAndFields[o].fields = getDataAbove.loadMapping.fields ? JSON.parse(JSON.stringify(getDataAbove.loadMapping.fields)) : [];
                                    }
                                }
                                commonFunctions.insertFields(getDataAbove, objectsAndFields[o], req,function (err, responseObject) {
                                    if (!err)
                                        objectsAndFields[o].fields = responseObject
                                    cb(null, objectsAndFields[o])
                                })
                            }
                            else {
                                cb(err, [])
                            }
                        })
                    }
                })(o))
            }
            async.parallel(taskObjects, function (err, result) {
                if (!err)
                    cb(null, result)
                else {
                  console.log("error in add cs api ", err);
                  cb(err, [])
                }
            })

        }],
        getLanguages: ['insert_content_source_id', function (getDataAbove, cb) {
            const sql = "SELECT * from content_source_languages WHERE content_source_id = ?";
            connection[req.headers['tenant-id']].execute.query(sql, [contentSource.id], (err, languages) => {
                if(err){
                    cb(err,null);
                }else{
                    console.log('languagesssss', languages);
                    cb(null, languages);
                }       
            });
        }],
        getScData: ['insert_content_source_id', function (getDataAbove, cb) {
            let scsql = `SELECT sc.id, sc.name, sc.uid, cs.elasticIndexName as indexName, cso.name as type, sctco.merge_results 
            FROM search_clients AS sc, search_clients_to_content_objects AS sctco, content_source_objects AS cso, content_sources AS cs
            WHERE sctco.search_client_id = sc.id and sctco.content_source_object_id = cso.id and cs.id = cso.content_source_id and cs.id = ?`
            connection[req.headers['tenant-id']].execute.query(scsql, contentSource.id, (err, rows) => {
              if(err){
                cb(err,null);
              }else{
                console.log('search client dataaaa', rows);
                cb(null, rows);
              }       
            });
        }],
        insert_content_source_objects_and_fields_elastic: ['insert_content_source_id','insert_content_source_objects_and_fields', function (getDataAbove, cb) {
            let fields, objectFieldArr = JSON.parse(JSON.stringify(objectsAndFields));
            if(objectFieldArr){
                for (var f = 0; f < objectFieldArr.length; f++) {
                    fields = objectFieldArr[f].fields;

                    for (var m = 0; m < fields.length; m++) {
                        let param = commonFunctions.getObjectFieldNames(contentSource.content_source_type_id, fields[m], "name");
                        if (param)
                            objectFieldArr[f].fields[m].name = contentSource.elasticIndexName + "___" + objectsAndFields[f].name + "___" + fields[m].name;
                        
                        // if (r.includes('.')) r = r.replace(/\./g, '_')
                    }
                }
            }

            var configObj = { 
                contentSource,
                languagesData: getDataAbove.getLanguages,
                scData: getDataAbove.getScData,
                objectsAndFields: objectFieldArr,  
                session: req.headers.session
            };
            let headers = {
                "Content-Type": "application/json",
                "su-crawler-secret" : config.get("crawler.sharedSecret"),
                "tenant-id": req.headers['tenant-id']
            }
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content/create-mapping', '', configObj, headers, function (error, result) {
                if (error) {
                    commonFunctions.errorlogger.error("Error found: ", error);
                    cb(error, objectFieldArr);
                }
                else {
                    cb(null, objectFieldArr);
                }
            })
        }],
        insert_api_crawler_fields: ['insert_content_source_id', function (getDataAbove, cb) {
            if (contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.customContentSource) {
                apiCrawlerFields.content_source_id = contentSource.id;
                apiCrawlerFields.content_source_authorization_id = authorization.id;
                commonFunctions.insertUpdateApiCrawlerFields(apiCrawlerFields,req, function (errAsync, rows) {
                    if (errAsync) {
                        commonFunctions.errorlogger.error("Error inside insert_api_crawler_fields", errAsync);
                        cb(errAsync, [])
                    }
                    else {
                        if (!apiCrawlerFields.id)
                            apiCrawlerFields.id = rows.insertId;
                        cb(null, apiCrawlerFields)
                    }
                })
            } else {
                cb(null, []);
            }

        }],
        get_auth_intermediate_Callback: ['insert_content_source_authorization', function (dataFromAbove, cb) {
            if ((authorization.authorization_type && authorization.authorization_type == 'OAuth' || authorization.authorization_type && authorization.authorization_type.includes('OAuth')) && (authorization.privateKey || authorization.client_secret)) {
                switch (contentSource.content_source_type_id) {
                    case constants.CONTENT_SOURCE_TYPE.confluence: 
                    case constants.CONTENT_SOURCE_TYPE.jira: {
                        if (data.tabName == 0) {
                            oauthUtil.jiraConfluenceAuthIntermediate(contentSource, authorization, req,function (dataReturn) {
                                if (dataReturn.error) {
                                    cb(null, { status: false });

                                } else {
                                    data.oAuth = dataReturn.oauth;
                                    cb(null, {
                                        status: true,
                                        url: dataReturn.oauth.url
                                    });
                                }
                            });
                        }
                        else {
                            cb(null, []);
                        }
                        break;
                    }
                    case constants.CONTENT_SOURCE_TYPE.jiraOnprem: {
                        if (data.tabName == 0) {
                            jira.jiraAuthorisationIntermediate(contentSource, authorization,req, function (dataReturn) {
                                if (dataReturn.error) {
                                    cb(null, { status: false });
                                } else {
                                    data.oAuth = dataReturn.oauth;
                                    jiraOnPrem.createMetaFolder(contentSource, authorization, function (err, result) {
                                        cb(null, {
                                            status: true,
                                            url: dataReturn.oauth
                                        });
                                    })
                                }
                            });
                        }
                        else {
                            cb(null, [])
                        }
                        break;
                    }
                    case constants.CONTENT_SOURCE_TYPE.drive: {

                        drive.driveAuthorisationIntermediate(contentSource, authorization, function (err, dataReturn) {
                            if (dataReturn.error) {
                                cb(null, { status: false });
                            } else {
                                data.oAuth = dataReturn.oauth
                                cb(null, {
                                    status: true,
                                    url: dataReturn.oauth
                                });
                            }
                        });
                        break;
                    }
                    case constants.CONTENT_SOURCE_TYPE.box: {
                        box.boxAuthorisationIntermediate(contentSource, authorization, function (err, dataReturn) {
                            if (dataReturn.error) {
                                cb(null, { status: false });
                            } else {
                                data.oAuth = dataReturn.oauth
                                cb(null, {
                                    status: true,
                                    url: dataReturn.oauth
                                });
                            }
                        });
                        break;
                    }
                    case constants.CONTENT_SOURCE_TYPE.dropbox: {
                        dropbox.dropboxAuthorisationIntermediate(contentSource, authorization, function (err, dataReturn) {
                            if (dataReturn.error) {
                                cb(null, { status: false });
                            } else {
                                data.oAuth = dataReturn.oauth
                                cb(null, {
                                    status: true,
                                    url: dataReturn.oauth
                                });
                            }
                        });
                        break;
                    }
                    case constants.CONTENT_SOURCE_TYPE.gmail:
                    case constants.CONTENT_SOURCE_TYPE.youtube: {
                        youtube.youtubeAuthorisationIntermediate(contentSource, authorization, function (err, dataReturn) {
                            if (dataReturn.error) {
                                cb(null, { status: false });
                            } else {
                                data.oAuth = dataReturn.oauth
                                cb(null, {
                                    status: true,
                                    url: dataReturn.oauth
                                });
                            }
                        });
                        break;
                    }
                    case constants.CONTENT_SOURCE_TYPE.stackOverflow: {
                        stackoverflow.stackOverflowAuthorisationIntermediate(contentSource, authorization, function (err, dataReturn) {
                            if (dataReturn.error) {
                                cb(null, { status: false });
                            } else {
                                data.oAuth = dataReturn.oauth
                                cb(null, {
                                    status: true,
                                    url: dataReturn.oauth
                                });
                            }
                        });
                        break;
                    }
                    case constants.CONTENT_SOURCE_TYPE.khorosAurora:
                    case constants.CONTENT_SOURCE_TYPE.lithium: {
                        lithium.lithiumAuthorisationIntermediate(contentSource, authorization, function (err, dataReturn) {
                            if (dataReturn.error) {
                                cb(null, { status: false });
                            } else {
                                data.oAuth = dataReturn.oauth
                                cb(null, {
                                    status: true,
                                    url: dataReturn.oauth
                                });
                            }
                        });
                        break;
                    }
                    case constants.CONTENT_SOURCE_TYPE.slack:
                    case constants.CONTENT_SOURCE_TYPE.vimeo:
                    case constants.CONTENT_SOURCE_TYPE.zendesk:
                    case constants.CONTENT_SOURCE_TYPE.docebo:
                    case constants.CONTENT_SOURCE_TYPE.jive:
                    case constants.CONTENT_SOURCE_TYPE.servicenow:
                    case constants.CONTENT_SOURCE_TYPE.dynamics:
                    case constants.CONTENT_SOURCE_TYPE.github:
                    case constants.CONTENT_SOURCE_TYPE.aha:
                    case constants.CONTENT_SOURCE_TYPE.microsoftTeams:
                    case constants.CONTENT_SOURCE_TYPE.wistia:
                    case constants.CONTENT_SOURCE_TYPE.sharepoint:
                    case constants.CONTENT_SOURCE_TYPE.helpScout:
                    case constants.CONTENT_SOURCE_TYPE.brightspace:
                    case constants.CONTENT_SOURCE_TYPE.uservoice:
                    case constants.CONTENT_SOURCE_TYPE.simpplr:
                    case constants.CONTENT_SOURCE_TYPE.salesforce:
                        csAuthorisationIntermediate(contentSource, authorization, cb);
                        break;
                    default: cb(null, { status: false })

                }
            }
            else if (authorization.authorization_type == 'Basic') {
                switch (contentSource.content_source_type_id) {
                    case 4: {

                    }
                    default: cb(null, {})
                }
            }
            else {
                switch (contentSource.content_source_type_id) {
                    case 18: {
                        authorization.accessToken = appVariables.stackoverflow.accessToken;
                        commonFunctions.insertUpdateAuthorization(authorization, req,function (error, resultSave) {
                            cb(null, "not Oauth for stackoverflow")
                        });
                        break;
                    }
                    case constants.CONTENT_SOURCE_TYPE.monday :{
                        authorization.accessToken = dataFromAbove.insert_content_source_authorization && dataFromAbove.insert_content_source_authorization.accessToken?dataFromAbove.insert_content_source_authorization.accessToken:(authorization.accessToken?authorization.accessToken:"");
                        commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                            cb(null, authorization)
                        });
                        break;
                    }
                    default: {
                      console.log(`*****  get_auth_intermediate_Callback default case for contentSource.content_source_type_id: ${contentSource.content_source_type_id}, authorization.authorization_type ${authorization.authorization_type}`)
                      cb(null, "Not OAuth Cal");
                    }
                }

            }
        }],
        saveurlConfiguration: ["insert_content_source_id", function (results, cb) {
            let urlArray = [];
            if ([
                constants.CONTENT_SOURCE_TYPE.website,
                constants.CONTENT_SOURCE_TYPE.madcap,
                constants.CONTENT_SOURCE_TYPE.jsWeb
            ].includes(contentSource.content_source_type_id)) {
                let sql;
                if (data.webConf) {
                    if (data.contentSource.isFileUploaded) {
                        //we will examine file and save filters only on first time submit
                        if(!data.webConf.id){ 
                            data.webConf.include_url   = data.uploadedfileUrlResponse.data.includeUrls;
                            data.webConf.index_filters = data.uploadedfileUrlResponse.data.excludeUrls;
                        }

                        if (data.webConf.id) {
                            sql = `UPDATE website_setting SET content_source_id = ?, include_url = ?, exclude_url = ?, index_filters=?, javascript_enabled=? ,sitemap_enabled=?,session=?,	outlink_filters=? , jsCrawling_Filter=?, anchor_depth=?, anchor_url=?, authenticate_before_crawl=? WHERE id = ?;`
                        }
                        else {
                            sql = "INSERT IGNORE INTO `website_setting` (`content_source_id`, `include_url`, `exclude_url`, `index_filters`, `javascript_enabled`,`sitemap_enabled`,`session`,`outlink_filters`,`jsCrawling_Filter`,`anchor_depth`,`anchor_url`, `authenticate_before_crawl`) VALUES (?, ?, ?, ?, ?,?,?,?,?,?,?, ?)";
                        }
                        connection[req.headers['tenant-id']].execute.query(sql, [contentSource.id, data.webConf.include_url, data.webConf.exclude_url, data.webConf.index_filters, data.webConf.javascript_enabled, data.webConf.sitemap_enabled, data.webConf.session, data.webConf.outlink_filters,data.webConf.jsCrawling_Filter, data.webConf.anchor_depth, data.webConf.anchor_url, data.webConf.authenticate_before_crawl, data.webConf.id], (error, rows) => {
                            if (error)
                                cb(error);
                            else {
                                if (!data.webConf.id)
                                    data.webConf.id = rows.insertId;
                                cb(null, data.webConf);
                            }
                        });
                    } else {
                        if (!data.webConf.id && !data.webConf.include_url) {
                            var urlObj = url.parse(data.contentSource.url);
                            data.webConf.include_url = "http://" + urlObj.host + "\nhttps://" + urlObj.host;
                        }
                        if (!data.webConf.id && !data.webConf.index_filters) {
                            var urlObj = url.parse(data.contentSource.url);
                            data.webConf.index_filters = "^http://" + urlObj.host + "\n^https://" + urlObj.host;
                        }
                        if (data.webConf.id) {
                            sql = `UPDATE website_setting SET content_source_id = ?, include_url = ?, exclude_url = ?, index_filters=?, javascript_enabled=? , sitemap_enabled=?, session =? ,outlink_filters =? ,jsCrawling_Filter=?, anchor_depth=?, anchor_url=?, authenticate_before_crawl=? WHERE id = ?;`
                        }
                        else {
                            sql = "INSERT IGNORE INTO `website_setting` (`content_source_id`, `include_url`, `exclude_url`, `index_filters`, `javascript_enabled`,`sitemap_enabled`,`session`,`outlink_filters`,`jsCrawling_Filter`, `anchor_depth`,`anchor_url`, `authenticate_before_crawl`) VALUES (? ,?, ?, ?, ?, ?,?,?,?,?,?, ?)";
                        }

                        connection[req.headers['tenant-id']].execute.query(sql, [contentSource.id, data.webConf.include_url, data.webConf.exclude_url, data.webConf.index_filters, data.webConf.javascript_enabled, data.webConf.sitemap_enabled, data.webConf.session, data.webConf.outlink_filters,data.webConf.jsCrawling_Filter, data.webConf.anchor_depth, data.webConf.anchor_url, data.webConf.authenticate_before_crawl, data.webConf.id], (error, rows) => {
                            if (error)
                                cb(error);
                            else {
                                if (!data.webConf.id)
                                    data.webConf.id = rows.insertId;
                                cb(null, data.webConf);
                            }
                        });
                    }
                }
                else
                    cb(null, {});
            } else {
                cb(null, {});
            }
        }],
        checkForReIndex: ["insert_content_source_id", "insert_content_source_objects_and_fields", function (results, cb) {
            //Bypassing this condition, since this operation is done in updateObjectAndFields API now
            if (false && contentSource.content_source_type_id == 3)
                { }
            else
                cb(null, []);
        }]
    }, function (err, result) {
        callback(err, result);
    })
}

// get git branch and tag
router.get('/getGitInfo', function (req, res) {
    /* anything mentioned in "release" property will supersede everything else */
    const release = config.get('release') || '';
    if(!!release){
        return res.send({ flag: 200, data: { branchName: release } });
    }
    const quarterlyReleaseMonths = (config.get('quarterlyReleaseMonths')) || {};
    const version = (config.get('version') || '').split('.');
    const [ year, month ] = version;
    /* check if current release is quarterly release */
    const quarterlyRelease = quarterlyReleaseMonths[month];
    const branchName = !!quarterlyRelease ? `${quarterlyRelease} '${year}` : `${year}.${month}`;
    res.send({ flag: 200, data: { branchName }});
});

// get git instance branch and tag
router.get('/getInstanceName', function (req, res, next) {
    try {
        let myAccountInstanceVariable = config.get("myAccountInstanceVariable") || '';
        let instanceEnvironmentName = config.get('instanceName') || '';
        instanceEnvironmentName = instanceEnvironmentName.toLowerCase();
        let instanceType = config.get("instanceType") || '';
        let isProd = false;

        if(constants.instanceTypes.production == instanceType){
            isProd = true;
        }
            res.send({
                flag: 200,
                data: {
                    instanceEnvironmentName,
                    myAccountInstanceVariable,
                    instanceType,
                    isProd
                }
        });
    } catch (e) {
        res.send({
            flag: 200,
            data: {
                instanceType: '',
                isProd,
                'instanceEnvironmentName': '',
                'myAccountInstanceVariable':myAccountInstanceVariable

            }
        });
    }
});

router.post('/deleteDocument', (req, res, next) => {
    console.log("req.body ::",req.body);
    let obj = {
        index: req.body.sourceName+"__"+req.body.objName,
        id: req.body._id
    }
    let options = {
        method: "POST",
        rejectUnauthorized: false,
        url: config.get("indexService.url") + "/index-service/open-search/delete-doc",
        headers: {
            "Content-Type": "application/json",
            "tenant-id": req.headers['tenant-id'],
            "index-service-secret": config.get("indexService.sharedSecret"),
            "timeout": 60000
        },
        body: obj,
        json: true
    };
    request(options, function (error, response, body) {
        if (error) {
            res.sendStatus(500);
        }
        else {
            if(body.status) {
                res.send({"data":"success"});
            }
        }
    })
});


router.post('/uploadCsvFile', multipartMiddleware, function (req, res, next){
    let reqBody = {
        uploadFile: fs.createReadStream(req.files.uploadFile.path),
        filename : req.files.uploadFile.name
    }
    const regex = /\.(txt|xml)$/i;
    if (!regex.test(reqBody.filename) && req.body.csTypeId != constants.CONTENT_SOURCE_TYPE.file) {
        res.send({
            status: false,
            code: 106
        });
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    request.post({
        url : config.get("crawler.crawlerUrl") + '/file/upload?csTypeId=' + req.body.csTypeId,
        formData : reqBody,
        rejectUnauthorized: false,
        headers
    }, function (err, response, body){
        if(err){
            res.sendStatus(500);
            return ;
        }
        if(typeof body === 'string') {
            body = JSON.parse(body);
            if(body.data) {
                body = body.data;
            }
        }
        res.send(body);
    })
});

router.post('/downloadFile', async (req, res, next) => {
    let allowedFurther = await accessCheck(req.headers.session.email, req.headers.session.roleId, req.body.id,req);
    if (!allowedFurther) {
        return res.send({ flag: 401, message: 'Unauthorized' });
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/file/downloadFile','', req.body, headers, function (error, result) {
        
        if (result.error) {
             return res.send({statusCode:404, message: "File not found!" });
                }
                else{
                    res.setHeader('Content-Disposition', `attachment; filename=${req.body.fileName.toString()}`);
                    res.setHeader('Content-Type', `text/${req.body.fileName.split('.')[1]}`);
                    res.status(200).send(result);
                }
    })

});

function callSAPCrawler(contentSourceId, cb) {
    crawler.sap.crawlSAP(contentSourceId);
    cb(null, 0);
}

// router.get('/startObjectCrawling', (req, res, next) => {
//     let objectName = req.query.objectName;
//     const contentSourceId = req.query.content_source_id

//     async.auto({
//         get_content_source_data: function (cb) {
//             commonFunctions.getContentSourceDataById(contentSourceId, function (err, contentSourceData) {
//                 if (!err)
//                     cb(null, contentSourceData)
//                 else
//                     cb(err, [])
//             });
//         },
//         save_boosting_details: ['get_content_source_data', function (dataFromAbove, cb) {
//             commonFunctions.save_boosting_details(dataFromAbove.get_content_source_data.contentSource.elasticIndexName, function (err, data) {
//                 if (err) {
//                     console.error(err);
//                     cb(err, [])
//                 } else {
//                     cb(null, []);
//                 }
//             })
//         }],
//         crawl_data_function: ['save_boosting_details', function (dataFromAbove, cb) {

//             var contentsourceId = dataFromAbove.get_content_source_data.contentSource.id;

//             let outputFile = 'crawlingLogs/salesforce_' + contentsourceId + "_" + (new Date()).toISOString() + '.log'
//             const out = fs.openSync(outputFile, 'a');
//             const err = fs.openSync(outputFile, 'a');

//             const subprocess = spawn('node', [path.resolve(process.cwd(), "routes/crawlers/salesforce.js"), "object", contentsourceId, objectName], {
//                 detached: true,
//                 stdio: ['ignore', out, err]
//             });

//             subprocess.unref();
//             let passData = {
//                 pid: subprocess.pid,
//                 filename: outputFile
//             }
//             cb(null, passData);
//         }],
//         update_content_source_crawler_id: ['crawl_data_function', 'get_content_source_data', function (dataFromAbove, cb) {
//             dataFromAbove.get_content_source_data.contentSource.pid = dataFromAbove.crawl_data_function.pid;
//             dataFromAbove.get_content_source_data.contentSource.last_sync_date = new Date();
//             dataFromAbove.get_content_source_data.contentSource.logFile = dataFromAbove.crawl_data_function.filename || '';
//             commonFunctions.insertUpdateContentSource(dataFromAbove.get_content_source_data.contentSource, cb)
//         }],
//         update_object_crawler_id: ['crawl_data_function', 'get_content_source_data', function (dataFromAbove, cb) {
//             commonFunctions.updateObjectPidAndStatus(dataFromAbove.crawl_data_function.pid, [req.query.objectId], false, function (err, result) {
//                 if (result)
//                     cb(null, [])
//             })
//         }]
//     }, function (err, result) {
//         let message = {}
//         message.flag = commonFunctions.constants.SUCCESS
//         if (!err) {

//             message.data = "crawler started for object " + objectName + " of " + result.get_content_source_data.contentSource.name;
//             res.send(message)
//         }

//         else {
//             if (commonFunctions.constants.SCHEDULING_LIMIT !== 0) {
//                 message.flag = commonFunctions.constants.SCHEDULING_LIMIT
//                 message.data = "Can not start a new refresh index process while other content source is refreshing.";
//                 res.send(message)
//             }
//             else {
//                 message.data = "SCHEDULING LIMIT " + err;
//                 res.send(message)
//             }
//         }
//     })
// });

// router.post('/stopObjectCrawling', function (req, res, next) {
//     let contentSource = { id: req.body.contentSource.content_source_id }
//     let pid = req.body.contentSource.content_source_pid;
//     let objectIds = [];
//     objectIds.push(req.body.contentSource.objectId);
//     var message = {}
//     if (typeof (pid) === 'number') {
//         async.parallel([
//             //update table
//             function (cb) {
//                 contentSource.pid = 0
//                 commonFunctions.insertUpdateContentSource(contentSource, cb)
//             },
//             //kill process
//             function (cb) {
//                 if (pid != 0)
//                     spawn('kill', ['-9', pid])
//                 cb(null)
//             },
//             function (cb) {
//                 commonFunctions.updateObjectPidAndStatus(0, objectIds, true, function (err, result) {
//                     if (result)
//                         cb(null, [])
//                 })
//             }
//         ], function (err, result) {
//             message.flag = commonFunctions.constants.SUCCESS
//             message.data = "killed"
//             res.send(message)
//         })
//     }
//     else {
//         message.flag = commonFunctions.constants.responseFlags.ACTION_FAILED
//         message.data = "Not a process id"
//         res.send(message)
//     }
// })

function checkForReIndex(preObjs, objects, cs,req, cb) {
    async.auto({
        object_exists: function (callback) {
            var asyncTasks = [];
                objects.map(x => {
                    let objectDiff = { 'id': '', 'fields': [] };
                    let oldFieldsObject = preObjs.find(o => o.name == x.name) || {};
                    let oldFields = oldFieldsObject.fields || [];

                    const fieldIds = x.object_status ? x.fields : []; 
                    oldFields = oldFields.filter(o => o.id).map(o => o.id);
                    x.fields.map((o) => {
                        if(!oldFields.includes(o.id) || fieldIds.includes(o.id)){
                            objectDiff.id = x.id;
                            objectDiff.fields.push(o.id);
                        }
                    });
                    if (x.fields.length != oldFields.length) {
                        objectDiff.id = x.id;
                        objectDiff.fields.push(4);
                    }

                    // if (!results.index_mapping[cs.elasticIndexName]) {
                    //     if(x.fields && x.fields.length){
                    //         objectDiff.id = x.id;
                    //         objectDiff.fields = x.fields.map(o => o.id);
                    //     }
                    // }
                    // else if (!results.index_mapping[cs.elasticIndexName].mappings || !results.index_mapping[cs.elasticIndexName].mappings[x.name]) {
                    //     if(x.fields && x.fields.length){
                    //         objectDiff.id = x.id;
                    //         objectDiff.fields = x.fields.map(o => o.id);
                    //     }
                    // } else {
                    //     const fieldIds = x.object_status ? x.fields : [];                     
                    //     if (results.index_mapping[cs.elasticIndexName].mappings[x.name]) {                            
                    //         oldFields = oldFields.filter(o => o.id).map(o => o.id);
                    //         x.fields.map((o) => {
                    //             if(!oldFields.includes(o.id) || fieldIds.includes(o.id)){
                    //                 objectDiff.id = x.id;
                    //                 objectDiff.fields.push(o.id);
                    //             }
                    //         });
                    //         if (x.fields.length != oldFields.length) {
                    //             objectDiff.id = x.id;
                    //             objectDiff.fields.push(4);
                    //         }

                    //         /*x.fields.map(f => {
                    //             let r;
                    //             r = f.name;
                    //             commonFunctions.modelFields.map(m => {
                    //                 let param = commonFunctions.getObjectFieldNames(cs.content_source_type_id, f, m);
                    //                 if (param)
                    //                     r = cs.elasticIndexName + "___" + x.name + "___" + f.name;
                    //                 if (r.includes('.')) r = r.replace(/\./g, '_')
                    //             })
                    //             if ((!results.index_mapping[cs.elasticIndexName].mappings[x.name].properties[r] && (!x.object_pid || x.object_pid <= 0)) || fieldIds.includes(f.id)) {
                    //                 objectDiff.id = x.id;
                    //                 objectDiff.fields.push(f.id)
                    //             }
                    //         });*/
                    //     }
                    // }

                    if (oldFieldsObject.object_status && JSON.parse(oldFieldsObject.object_status).id) {
                        objectDiff = JSON.parse(oldFieldsObject.object_status);
                    }
                    
                    asyncTasks.push(updateObjectStatus.bind(null, x.id, objectDiff.id ? JSON.stringify(objectDiff) : null,req))
                });
                async.parallel(asyncTasks, function (errAsync, dataAsync) {
                    callback(null, [])
                });
        }
    }, function (error, results) {
        cb(null, []);
    })
}

function updateObjectStatus(id, objectDiff,req, callback) {
    let sqlCS = "UPDATE content_source_objects SET object_status=? WHERE id=?"
    let q = connection[req.headers['tenant-id']].execute.query(sqlCS, [objectDiff, id], function (errAsync, rows) {
        callback(null, []);
    })
}

function getObjectStatus(id,req) {
    const sql = "select object_status from content_source_objects WHERE id =?";
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(sql, [id], (err, rows) => {
            if (err) {
                console.log(err)
            }
            resolve(rows)
        })
    });
}

router.post('/byCaseUidAuth', function (req, res) {
    console.log('In CaseUidAuth Route');
    req.body.tenantId = req.headers['tenant-id'];
    commonFunctions.httpRequest('POST', config.get('analyticsService.url') + '/api/v2/searchSession/byCaseUidAuth', '', req.body, '', function (error, result) {
        if (error) {
            console.log(`Error in byCaseUidAuth Route - ${error}`);
            res.send('Something went wrong');
        }
        else {
            caseJourneyData(req, res, result);
        }
    })
})

const caseJourneyData = (req, res, result) => {
    let filterArray = [];
    let newFormat = [];
    if (result.status) {
        newFormat = result.data;
        let userTimezone = req.body.TimeZoneSidKey;
        newFormat = newFormat.map(x => {
            x.StartDate = new Date(x.StartDate).getTime();
            return x;
        });
        newFormat = newFormat.sort(function (a, b) { return b.StartDate - a.StartDate });
        newFormat.forEach(function (entry) {
            entry.StartDate = moment(moment.tz(new Date(entry.StartDate), userTimezone)).format("D/M/YYYY h:mm:ss A");
        })
        for (let i = 0; i < result.data.length; i++) {
            if (filterArray.indexOf(result.data[i].ActivityType) == -1) {
                filterArray.push(result.data[i].ActivityType);
            }
        }
    } else {
        console.log(`Error in byCaseUidAuth Response - ${result.message}`)
    }
    if (req.query.html) {
        res.render('caseJourney.ejs', { 'array': newFormat, 'filters': filterArray, 'endpoint': config.get("adminURL") });
    } else {
        if(result.status) {
            res.send({ statusCode: 200, 'array': newFormat, 'filters': filterArray, 'endpoint': config.get("adminURL") });
        } else if(result.message == 'Validation Failed - caseUid is not Valid') {
            res.send({ statusCode: 402, message: 'No data found' });
        } else if (result.message == 'Validation Failed - TenantId is not Valid') {
            res.send({ statusCode: 402, message: 'Invalid Authentication' });
        }
    }
};

router.post('/threadValue', function (req, res, next) {
    const cpuCount = os.cpus().length
    if (cpuCount == 1) {
        res.send(cpuCount.toString());
    } else if (cpuCount >= 1) {
        var count = cpuCount - 1
        res.send(count.toString());
    }
});

router.post('/shareAccessSettings', async (req, res) => {
    let allowedFurther = await accessCheck(req.headers.session.email, req.headers.session.roleId, req.body.id,req)
    if (!allowedFurther) {
        res.send({ flag: 401, message: 'Unauthorized' })
    } else {
        if (req.body.task == 'get') {
            let users = await commonFunctions.usersForAccessSettings(req);
            let accessDetails = await getDataFromDB(`SELECT email AS ownerEmail , sharedAccess FROM  content_sources cs 
          WHERE id = '${req.body.id}';`,req);

            let result = {};
            result.owner = [];
            result.admin = [];
            result.moderator = [];
            result.accessDetails = accessDetails[0]
            result.id = req.body.id;
            users.map((e) => {
                e['shareAccess'] = JSON.parse(accessDetails[0].sharedAccess).includes(e.email) ? true : accessDetails[0].ownerEmail.includes(e.email) ? true : false;
                if (e.email == accessDetails[0].ownerEmail) {
                    result.owner.push(e);
                } else if (e.roleId == 1) {
                    result.admin.push(e)
                } else if (e.roleId == 2) {
                    result.moderator.push(e)
                }
            });
            res.send({ 'status': 200, 'data': result })
        } else if (req.body.task == 'updateAray') {
            let q = `UPDATE content_sources SET sharedAccess = '${JSON.stringify(req.body.accessArray)}'
        WHERE id ='${req.body.id}';`
            await getDataFromDB(q,req);
            res.send({ 'status': 200, 'data': `Access updated` })
        }
    }
})

const getDataFromDB = (queryString,req) => {
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(queryString, (err, rows) => {
            if (err) {
                console.log(err)
            }
            resolve(rows)
        })
    })
};

const csAuthorisationIntermediate = function (contentSource, authorization, callback) {
    const redirectUrl = "https://oauthsfdc.searchunify.com";
    let keyObj, authUrl = "", resource = "";
    let urlSuffix = "/oauth/authorize";

    switch(contentSource.content_source_type_id){
        case constants.CONTENT_SOURCE_TYPE.aha:
            keyObj = appVariables.aha;
            authUrl= contentSource.url;
            break;
        case constants.CONTENT_SOURCE_TYPE.vimeo:
            keyObj = appVariables.vimeo;
            authUrl= keyObj.baseUrl;
            authorization.client_secret = null;
            break;
        case constants.CONTENT_SOURCE_TYPE.slack:
            keyObj = appVariables.slack;
            authUrl = keyObj.baseUrl;
            break;
        case constants.CONTENT_SOURCE_TYPE.zendesk:
            keyObj    = appVariables.zendesk;
            authUrl   = contentSource.url;
            urlSuffix = keyObj.urlSuffix;
            break;
        case constants.CONTENT_SOURCE_TYPE.docebo:
            keyObj = appVariables.docebo;
            authUrl = contentSource.url;
            urlSuffix = keyObj.urlSuffix;
            break;
        case constants.CONTENT_SOURCE_TYPE.jive:
            keyObj = appVariables.jive;
            authUrl = contentSource.url;
            urlSuffix = keyObj.urlSuffix;
            break;
        case constants.CONTENT_SOURCE_TYPE.servicenow:
            keyObj = appVariables.servicenow;
            authUrl= contentSource.url;
            urlSuffix = keyObj.urlSuffix;
            break;
        case constants.CONTENT_SOURCE_TYPE.dynamics:
            keyObj   = appVariables.dynamics;
            authUrl  = keyObj.baseUrl + authorization.tenantId;
            resource = contentSource.url;
            urlSuffix = keyObj.urlSuffix;
            break;
        case constants.CONTENT_SOURCE_TYPE.github:
            keyObj  = appVariables.github;
            authUrl = contentSource.url;            
            break;
        case constants.CONTENT_SOURCE_TYPE.microsoftTeams:
            keyObj  = appVariables.microsoftTeams;
            authUrl = keyObj.url; 
            urlSuffix = keyObj.urlSuffix;        
            break;
        case constants.CONTENT_SOURCE_TYPE.wistia:
                keyObj = appVariables.wistia;
                authUrl= keyObj.url;
                break;
        case constants.CONTENT_SOURCE_TYPE.sharepoint:
            keyObj  = appVariables.sharepoint;
            authUrl = keyObj.baseUrl; 
            urlSuffix = keyObj.urlSuffix;
            break;
        case constants.CONTENT_SOURCE_TYPE.helpScout:
            keyObj = appVariables.helpScout;
            authUrl = keyObj.baseUrl;
            urlSuffix = keyObj.urlSuffix;
            break;
        case constants.CONTENT_SOURCE_TYPE.uservoice:
            keyObj = appVariables.uservoice;
            authUrl = contentSource.url;
            urlSuffix = keyObj.urlSuffix;
            break; 
        case constants.CONTENT_SOURCE_TYPE.brightspace:
            keyObj = appVariables.brightspace;
            authUrl = contentSource.url;
            urlSuffix = keyObj.urlSuffix;
            break
        case constants.CONTENT_SOURCE_TYPE.simpplr:
            keyObj = appVariables.simpplr;
            authUrl = keyObj.url;
            urlSuffix = keyObj.urlSuffix;
            break;
        case constants.CONTENT_SOURCE_TYPE.salesforce:
            keyObj = appVariables.salesforce;
            authUrl = contentSource.url;
            urlSuffix = keyObj.urlSuffix;
            break;
        default:
            return callback(null,{ status: false });
    }
    authUrl += urlSuffix;
    authUrl += '?client_id=' + (keyObj.clientId || authorization.client_id);
    if (contentSource.content_source_type_id !== constants.CONTENT_SOURCE_TYPE.simpplr) {
        authorization.client_secret || keyObj.client_secret
            ? (authUrl += `&client_secret=${
                  authorization.client_secret || keyObj.client_secret
              }`)
            : null;
    }
    if (contentSource.content_source_type_id === constants.CONTENT_SOURCE_TYPE.helpScout) {
        authUrl += `&state=${authorization.client_secret}`;
    }
    if(contentSource.content_source_type_id === constants.CONTENT_SOURCE_TYPE.salesforce)
    {
        authUrl += '&response_type=code';
    }
    if(contentSource.content_source_type_id === constants.CONTENT_SOURCE_TYPE.sharepoint)
    {
        authUrl += '&scope=' + authorization.instanceURL + "/.default offline_access"
    }
    authUrl += '&redirect_uri=' + redirectUrl;
    if(resource) authUrl += '&resource=' + resource;
    if(keyObj.additionalTags) authUrl += keyObj.additionalTags;
    return callback(null, { status: true, url: authUrl });
  }

let accessCheck = async (email, roleId, contentSourceId,req) => {
    let allowedSUuser = [/grazitti.com/, /searchunify.com/];
    /** All user with grazitti.com and searchunify.com are considered as Searchunify user */
    let searchUnifyUser = allowedSUuser[0].test(email) || allowedSUuser[1].test(email);
    /** Get Access control settings */
    let accessControlSettings = (await getDataFromDB(`select * from access_control_settings;`,req))[0].contentSource;
    if (searchUnifyUser || roleId == 4 || accessControlSettings == 1) { return true }
    else {
        // if contentSourceId is undefined --> its for creation of CS
        if(!contentSourceId){ return true }
        let csDeatil = (await getDataFromDB(`SELECT * FROM content_sources WHERE id=${contentSourceId};`,req))[0];
        let userCSAccess = JSON.parse(csDeatil.sharedAccess);
        userCSAccess.push(csDeatil.email);
        return userCSAccess.includes(email)
    }
}

router.post('/deleteIfCSUnauthenticated', function (req, res, next) {
    commonFunctions.errorlogger.info("Checking if Content Source has been authticated!")
    const contentSourceId = req.body.contentSourceId;
    const csTypeId = req.body.csTypeId;

    deleteContentSource(contentSourceId, csTypeId, 0, req, (err, data) => {
        return res.send(data);
    });
});
router.post('/subscriptions/monday/:csId/:tenantHash', function (req, res) {
    const { body } = req;
    const csId = req.params;
    commonFunctions.errorlogger.info(JSON.stringify(csId));
    if (body.challenge) {
        return res.status(200).send(req.body, 0, 2); // returning challenge token back to monday.com as mentioned in their api documentation for webhooks.
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret")
    }
    commonFunctions.errorlogger.info('Monday event hits', headers);

    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/subscriptions/monday', '', req, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);

            res.send(result);
        }
    })
});


router.get('/crawl-requests',  async (req, res, next) => {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/scheduled-jobs/crawl-requests/?status=${req.query.status}`,'', '', headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.send(result);
        }
    });
})

router.get('/crawl-requests',  async (req, res, next) => {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/scheduled-jobs/crawl-requests/?status=${req.query.status}`,'', '', headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.send(result.data);
        }
    });
})

router.delete('/crawl-requests',  async (req, res, next) => {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    
    commonFunctions.httpRequest('DELETE', config.get("crawler.crawlerUrl") + `/scheduled-jobs/crawl-requests/?crawlRequestId=${req.query.crawlRequestId}`,'', '', headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            res.send(result.data);
        }
    });
})

const getAffectedSearchClients = async (req) => {
    return new Promise((resolve) => {
        if (!req.query.csId || !req.query.fieldId) return resolve([]);
        let scsql = `SELECT 
            sc.name AS scName
        FROM 
            search_clients sc
        INNER JOIN 
            search_clients_to_content_objects AS sctco ON sctco.search_client_id = sc.id
        INNER JOIN 
            search_clients_filters AS scf ON sctco.id = scf.search_clients_to_content_object_id
        INNER JOIN 
            content_source_objects AS cso ON sctco.content_source_object_id = cso.id
        WHERE 
            cso.content_source_id = ?
            AND scf.use_as IN ('Search', 'SearchSummary', 'Tag', 'SearchFilter')
            AND scf.content_source_object_field_id = ?`
        connection[req.headers['tenant-id']].execute.query(scsql, [req.query.csId,req.query.fieldId], (error, rows) => {
            commonFunctions.errorlogger.info('-----',error, rows);
            if (error) {
                commonFunctions.errorlogger.error(error);
                resolve([]);
            } else {
                resolve(rows);
            }      
        });
    });
};

const getISConfigInfo = async (req) => {
    return new Promise((resolve) => {
        commonFunctions.fetchIndexingConfig({
            tenantId: req.headers['tenant-id'],
            RETRY_COUNT: 0,
            projection: {
                'vectorConfiguration.vectorIndexingEnabled' : 1,
                'vectorConfiguration.hybridSearchEnabled': 1, 
                'vectorConfiguration.vectorizeDataFrequency': 1
            },
        }, (response) => {
            commonFunctions.errorlogger.info('Tenant IS Config: ',response);
            resolve(response);
        });
    });
}

router.get('/getAffectedSearchClients',  async (req, res) => {
    try {
      const [isConfig, scRows] = await Promise.all([getISConfigInfo(req), getAffectedSearchClients(req)]);
      const response = {
        isConfig: isConfig.vectorConfiguration,
        scRows,
      };
      console.log(response);
      res.send(response);
    } catch (error) {
      console.error("Error in one of the functions:", error);
    }
});

router.get('/updateOAuthStatus',  async (req, res) => {
    try {
        if (req.query.oauthSuccess) {
            kafkaLib.publishMessage({ 
              topic   : kafkaLib.SU_CRAWLER_TOPIC.contentSource,
              messages: [{ 
                  value : JSON.stringify({ type: kafkaLib.KAFKA_EVENT_TYPES.updateAuthStatus, data: req.query, tenantId: req.headers['tenant-id'], session: req.headers.session
                   }),
                  key: req.headers['tenant-id']
              }]
            });
        }

        res.send({ "success": true });
    } catch (error) {
        console.log('error', error);
        res.send(error);
    }
});

router.post('/logFile',  async (req, res) => {
    try {
        let {contentSourceId, objectLogFile} = req.body;
        if(contentSourceId){
            let query;
            if(!objectLogFile){
                 query = `update content_sources set adminLogFile = NULL, logFile = NULL where id = ${contentSourceId}`;
            }else{
                query = `update content_source_objects set objectAdminLogFile = NULL, objectLogFile = NULL where id = ${contentSourceId}`;
            }
            connection[req.headers['tenant-id']].execute.query(query, (err, result) => {
                if (err) {
                    console.log('Error while updating adminLogFile status', err)
                    res.send(err);
                } else {
                    console.log(`Updated adminLogFile status for id ${contentSourceId}`);
                    res.send({ "success": true });
               }
            })
        } else {
            console.log('Nothing to update', req.url);
            res.send({ error : { message: 'Content Source Id not found' } });
        }
    } catch (error) {
        res.send(error);
    }
});
     
router.post('/getIndexedDocuments',  async (req, res) => {
    try {
        delete req.body.appId;
        let sendEmail = req.body.sendEmail ? req.body.sendEmail : false ; 
        let emailId = req.body.emailId ? req.body.emailId : "";
        let  contentSourceName = req.body.contentSourceName?  req.body.contentSourceName: '';
        delete req.body.sendEmail;
        delete req.body.emailId;
        delete req.body.contentSourceName;

        let headers = {
            "Content-Type": "application/json",
            "su-crawler-secret" : config.get("crawler.sharedSecret"),
            "tenant-id": req.headers['tenant-id'],
            "timeout": 100000
        }
    
        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-source/indexed-docs`,'', req.body, headers, function (error, result) {
            if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                res.status(error.code).send(error);
            }
            else {
                if (sendEmail && emailId) {
                    const isSuccess = createCsvAndEmail(result.data, emailId, contentSourceName);
                    if (isSuccess) {
                        const dataCount = result.data.hits.length || 0;
                        const string = `Exported ${dataCount} records from Content Source '${contentSourceName}' to mail id - '${emailId}'`
                        console.log('result.data',dataCount);
                        kafkaLib.publishMessage({
                            topic   :  config.get("kafkaTopic.publishAnalytics"), 
                            messages: [{
                                type: 'IndexedLogsEmail',
                                value: JSON.stringify({
                                generatedAnalytics: {
                                    browse : [
                                        {
                                            string,
                                            key: `sendEmail_${contentSourceName}`,
                                            field: `sendEmail_${contentSourceName}`,
                                            visibility:true
                                        }
                                    ]   
                                },
                                analyticsObject: { oldObj: {contentSourceName,emailId}, newObj: {}},
                                user: {name:req.headers.session.name, email: req.headers.session.email},
                                info: string,
                                tenantId: req.headers['tenant-id'] ,
                                ts: new Date().toISOString(),
                                object: 'Content Source',
                                eventType: 'ContentSourceAnalytics',
                                isJson: true,
                                event:'adminLog'
                                }),
                                key: req.headers['tenant-id']
                            }]
                        });
                        return res.send(result.data);
                    } else {
                        return res.status(500).send({ error: 'Error while sending email', statusCode: 500 });
                    }
                } else {
                    return res.send(result.data);
                }
                
            }
        });
    } catch (error) {
        res.send(error);
    }
});

function checkIfObject(value) {
    return Object.prototype.toString.call(value) === '[object Object]';
  }
  
  function checkIfArrayOfObject(value) {
    return Array.isArray(value) && value.some((el) => Object.prototype.toString.call(el) === '[object Object]');
  }

const createCsvAndEmail = async (data, emailId, contentSourceName) => {
    try {
        if (!data.hits) {
            return false;
        }
        const headers = new Set();
        data.hits.forEach(hit => {
            const topResults = hit.topResults || {};
            // Ensure topResults exists
            Object.values(topResults).forEach(result => {
                Object.keys(result).forEach(key => headers.add(key));  // Dynamically add headers
            });
        });

        // Convert headers Set to array
        const headerArray = Array.from(headers);

        // Map the data from each object in the array
        const csvData = data.hits.map(hit => {
            return headerArray.reduce((row, header) => {
                if (checkIfObject(hit.source[header]) || checkIfArrayOfObject(hit.source[header])) {
                    row[header] = JSON.stringify(hit.source[header]) || '';
                } else {
                    row[header] = hit.source[header] || '';
                }
                  // Ensure that missing headers don't cause issues
                return row;
            }, {});
        });

        let name = new Date().getTime();

        var dir = path.resolve(__dirname + '/../../reports/reports');
        if (!fs.existsSync(dir))
            fs.mkdirSync(dir);
        let filePath = path.resolve(__dirname + '/../../reports/reports/' + name + '.csv');

        // Write CSV to file
        const ws = fs.createWriteStream(filePath);
        fastcsv
            .write(csvData, { headers: headers })
            .pipe(ws);

        commonFunctions.errorlogger.info('CSV file created successfully with array of objects!');

        var attachment = {
            filename: `${contentSourceName}_index_docs.csv`,
            path: filePath,
            contentType: 'text/csv'
        }

        var emailObject = {
            to: emailId,
            subject: `SearchUnify: ${contentSourceName} Indexed Documents`,
            html: emailTemplates.searchResultsEmailTemplate(
                emailId,
                "",
                "",
                "",
                contentSourceName,
                `${contentSourceName} Indexed Documents`,
                ""
            ),
            attachments: attachment
        }
        searchunifyEmail.sendEmailWithAttachments(
            emailObject.to,
            emailObject.subject,
            emailObject.html,
            emailObject.attachments, 
            (err, resp) => {
                if (err) {
                    commonFunctions.errorlogger.error('Error sending email:', err);
                    return false;
                } else {
                    commonFunctions.errorlogger.info('Email sent with attachment');
                    fs.unlinkSync(filePath);  
                }
            }
        );
    } catch (error) {
        commonFunctions.errorlogger.error('Error sending email:', error);
    }
};

router.post('/deleteIndexedDocuments', (req, res, next) => {
    console.log("req.body ::",req.body);
    let obj = {
        index: req.body.index,
        id: req.body.id,
        type: req.body.type, 
        shouldCrawl: req.body.shouldCrawl
    }
    let options = {
        method: "POST",
        url: config.get("indexService.url") + "/index-service/open-search/indexed-docs/delete",
        headers: {
            "Content-Type": "application/json",
            "tenant-id": req.headers['tenant-id'],
            "index-service-secret": config.get("indexService.sharedSecret"),
            "timeout": 60000
        },
        body: obj,
        json: true
    };
    request(options, function (error, response, body) {
        if (error) {
            res.status(500).send(error);
        }
        else {
            if(body.status){
                let string = `Deleted record '${body.data.id}' from Content Source '${obj.index.split("_").pop()}'`;
                if(!body.data.shouldCrawl){
                    string += `- Marked as 'Don't crawl in future'`;
                }
                kafkaLib.publishMessage({
                    topic   :  config.get("kafkaTopic.publishAnalytics"), 
                    messages: [{
                        type: 'deleteDoc',
                        value: JSON.stringify({
                        generatedAnalytics: {
                            browse : [
                                {
                                    string,
                                    key: body.data.id,
                                    field: `deleteDoc_${obj.id}`,
                                    visibility:true
                                }
                            ]   
                        },
                        analyticsObject: { oldObj: obj, newObj: {}},
                        user: {name:req.headers.session.name, email: req.headers.session.email},
                        info: `Deleted record in '${obj.index.split("_").pop()}' content source`,
                        tenantId: req.headers['tenant-id'] ,
                        ts: new Date().toISOString(),
                        object: 'Content Source',
                        eventType: 'ContentSourceAnalytics',
                        isJson: true,
                        event:'adminLog'
                        }),
                        key: req.headers['tenant-id']
                    }]
                });
            }
            res.send(body);
        }
    })
});

router.post('/restoreIndexedDocuments', (req, res, next) => {

    let obj = {
        index: req.body.index,
        id: req.body.id,
        type: req.body.type
    }
    let options = {
        method: "POST",
        url: config.get("indexService.url") + "/index-service/open-search/indexed-docs/restore",
        headers: {
            "Content-Type": "application/json",
            "tenant-id": req.headers['tenant-id'],
            "index-service-secret": config.get("indexService.sharedSecret"),
            "timeout": 60000
        },
        body: obj,
        json: true
    };
    request(options, function (error, response, body) {
        if (error) {
            res.status(500).send(error);
        }
        else {
            if(body.status){
                console.log('req.session',req.headers.session);
                kafkaLib.publishMessage({
                    topic   :  config.get("kafkaTopic.publishAnalytics"), 
                    messages: [{
                        type: 'restoreDoc',
                        value: JSON.stringify({
                        generatedAnalytics: {
                            browse : [
                                {
                                    string:`Restored record '${body.data.id}' to the Content Source '${obj.index.split("_").pop()}'`,
                                    key: body.data.id,
                                    field: `restoreDoc_${obj.id}`,
                                    visibility:true
                                }
                            ]   
                        },
                        analyticsObject: { oldObj: obj, newObj: {}},
                        user: {name:req.headers.session.name, email: req.headers.session.email},
                        info: `Restored record in '${obj.index.split("_").pop()}' content source`,
                        tenantId: req.headers['tenant-id'] ,
                        ts: new Date().toISOString(),
                        object: 'Content Source',
                        eventType: 'ContentSourceAnalytics',
                        isJson: true,
                        event:'adminLog'
                        }),
                        key: req.headers['tenant-id']
                    }]
                });
            }
            res.send(body);
        }
    })
});

router.post('/verifyCSObjectFields', (req, res, next) => {
    console.log(">>>>>> verifyCSObjectFields >>>>>>");
    const data = {
        csData : req.body.csData,
        fieldToValidate: req.body.fieldToValidate,
    }
    let options = {
        method: "POST",
        url: config.get("indexService.url") + "/index-service/open-search/validateCSObjectFields",
        headers: {
            "Content-Type": "application/json",
            "tenant-id": req.headers['tenant-id'],
            "index-service-secret": config.get("indexService.sharedSecret"),
        },
        body: data,
        json: true,
        timeout: 60000
    };
    request(options, function (error, response, body) {
        if (error) {
            commonFunctions.errorlogger.info('Error : ', JSON.stringify(error));
            res.status(500).send(error);
        }
        else {
            commonFunctions.errorlogger.info('Response : ', JSON.stringify(response));
            res.status(response.statusCode).send(response);
        }
    })
});

router.get('/getCsConfiguration', function(req, res) {
    if (req.body.appId){
        delete req.body.appId;
    }
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/crawler-config/get-cs-configuration', '', req.body, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    })
});

router.post('/getConnectedUser', function(req, res) {
    commonFunctions.getContentSourceAuthorizationData(req.body.mysqlId, req.headers['tenant-id'], function(error, response) {
        if (req.body.appId){
            delete req.body.appId;
        }
        let headers = {
            "Content-Type": "application/json",
            "su-crawler-secret": config.get("crawler.sharedSecret"),
            "tenant-id": req.headers['tenant-id']
        };
        req.body.authorization = response;
        commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/crawler-config/fetch-connected-cs-user', '', req.body, headers, function (error, result) {
            if (error) {
                commonFunctions.errorlogger.error("Error found: ", error);
                res.send(error);
            }
            else {
                commonFunctions.errorlogger.info("result found: ", result.data);
                res.send(result);
            }
        });
    });
});

module.exports = {
    router: router,
    deleteContentSource: deleteContentSource,
    getAddedContentSources:getAddedContentSources,
    findUidAndSendSCSettings: findUidAndSendSCSettings
}

