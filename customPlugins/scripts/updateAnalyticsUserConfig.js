const createRequest = require('../../auth/src/client');
config = require('config');

const updateAnalyticsUserConfig = async (req,cb) => {
    let resp = await makeAuthRequest();
}

const makeAuthRequest = async (req, cb) => {
    try {
        const options = {
            method: 'GET',
            url: `${config.authUrl}/user/updateAnalyticsUserConfig`
        };

        const response = await createRequest(options);
        console.log("response: ",JSON.parse(JSON.stringify(response.data)));
        return response;
    } catch (error) {
        console.log(error);
        return error;
    }
}

updateAnalyticsUserConfig();