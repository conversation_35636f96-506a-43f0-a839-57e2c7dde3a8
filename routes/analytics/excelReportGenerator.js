/**
 * <AUTHOR>
 * @version 1
 */
var xl = require('excel4node');
var async = require('async');
var paths = require('path');
var commonFunctions = require('./../../utils/commonFunctions');
var json2csv = require('json2csv').Parser;

function getTemplate(data, rcb) {
  var columns;
  if(data.fields.length>=6){
    columns = 12;
  }else{
    columns = 8;
  }
  var wb = new xl.Workbook();
	// console.log(JSON.stringify(data));
  // Add Worksheets to the workbook
  var ws = wb.addWorksheet(data.title);

  var blueBack = wb.createStyle({
    alignment: {
      horizontal: "center",
      vertical: 'center'
    },
    fill: {
      type: 'pattern',
      patternType: 'solid',
      bgColor: '00404498',
      fgColor: '00404498'
    },
    font: {
      size: 10,
      color: '00FFFFFF',
      bold: true
    }
  });
  var logo = wb.createStyle({
    alignment: {
        horizontal: "center",
        vertical: 'center'
    },
    fill: {
        type: 'pattern',
        patternType: 'solid',
        bgColor: '#ffffff',
        fgColor: '#ffffff'
    },
    font: {
        size: 8,
        color: '00404498',
        bold: false
    },
    border: {
        left: {
            style: 'thin',
            color: '#000000'
        },
        right: {
            style: 'thin',
            color: '#000000'
        },
        bottom: {
            style: 'thin',
            color: '#000000'
        },
        top: {
            style: 'thin',
            color: '#000000'
        },
    }
});
  var heading = wb.createStyle({
    alignment: {
      horizontal: "left",
      vertical: 'center',
      indent: 1
    },
    font: {
      size: 15,
      color: '00404498',
      bold: true
    }
  });

  var subHeading = wb.createStyle({
    font: {
      size: 8,
      color: '00404498',
      bold: false
    }
  });

  var metricCount = wb.createStyle({
    alignment: {
      horizontal: "right",
      vertical: 'bottom'
    },
    font: {
      size: 15,
      color: '00404498',
      bold: true
    }
  });

  var metricLabel = wb.createStyle({
    font: {
      size: 8,
      color: '00404498',
      bold: false
    }
  });

  var tableLabels = wb.createStyle({
    alignment: {
      vertical: 'center'
    },
    fill: {
      type: 'pattern',
      patternType: 'solid',
      bgColor: '00545EC7',
      fgColor: '00545EC7'
    },
    font: {
      size: 10,
      color: '00FFFFFF',
      bold: true
    }
  });

  var tableLabelsCount = wb.createStyle({
    alignment: {
      vertical: 'center',
      horizontal: 'right'
    },
    fill: {
      type: 'pattern',
      patternType: 'solid',
      bgColor: '00545EC7',
      fgColor: '00545EC7'
    },
    font: {
      size: 10,
      color: '00FFFFFF',
      bold: true
    }
  });

  var col = 3;
  var row = 1;
  ws.addImage({
    path: paths.join(__dirname, '../../resources/Assets/search-unify-logo.png'),
    type: "picture",
    position: {
        type: "twoCellAnchor",
        from: {
            col: 1,
            colOff: 0,
            row: 1,
            rowOff: 0
        },
        to: {
            col: 3,
            colOff: 0,
            row: 3,
            rowOff: 0
        }
    }
});

ws.cell(1, 1, 2, 2, true).style(logo);

  // Heading part
  if(data.searchKeyword || data.subject == "Search Results"){
    ws.cell(row, col, row + 1, col + (columns / 2 - 1), true).style(heading).string('Search Report'.toUpperCase());
  }else{
    ws.cell(row, col, row + 1, col + (columns / 2 - 1), true).style(heading).string('Search Analytics Report'.toUpperCase());
  }
  col = ((col + (columns / 2 - 1)) % columns) + 1;
  ws.cell(row, col, row, col + (columns / 4 - 1), true).style(subHeading).string('Report Date'.toUpperCase());
  var d = new Date();
  ws.cell(row, col + (columns / 4), row, col + (columns / 4), true).style(subHeading).string(d.getDate() + '-' + (d.getMonth() + 1) + '-' + d.getFullYear());
  row += 1;
  if(data.searchKeyword){
    ws.cell(row, col, row, col + (columns / 4 - 1), true).style(subHeading).string('Search Query'.toUpperCase());
    ws.cell(row, col + (columns / 4), row, col + (columns / 4), true).style(subHeading).string(data.searchKeyword);
  }else if(data.from && data.to){
    ws.cell(row, col, row, col + (columns / 4 - 1), true).style(subHeading).string('Coverage Date'.toUpperCase());
    ws.cell(row, col + (columns / 4), row, col + (columns / 4), true).style(subHeading).string(data.from?data.from:'');
    ws.cell(row, col + (columns / 4 + 1), row, col + (columns / 4 + 1), true).style(subHeading).string(data.to?data.to:'');
  }
 
  row += 1;
  col = ((col + (columns / 2 - 1)) % columns) + 1;

   col = 1;
   ws.row(1).setHeight(20);
   ws.row(2).setHeight(20);
   ws.column(1).setWidth(20);
   ws.column(2).setWidth(20);
  // report title
  ws.cell(row, col, row, col + (columns + 1), true).style(blueBack).string(data.title.toUpperCase());
  // ws.cell(row, col, row, col + (columns - 1), true).style(blueBack).string(data.title.toUpperCase());
  row += 1;
  var report = data.data;
  var colFields = data.fields;
  col = 2;
	var colSize = Math.floor((columns - 2) / (colFields.length - 1));
	for (var j = 0; j < colFields.length - 1; j++) {
		ws.cell(row, col, row, col + (colSize - 1), true).style(tableLabels).string(colFields[j].toUpperCase());
		col = ((col + (colSize - 1)) % columns) + 1;
	}
	ws.cell(row, col, row, col, true).style(tableLabels).string(colFields[colFields.length - 1].toUpperCase());
	row++;
	
  if (data.isNested) {
    commonFunctions.errorlogger.warn('nested data received');
    var jsonData = data.data;
    var headerObj = {};
    col = 2;
    colFields.map(field => {
			if(field!=colFields[colFields.length-1]){
				headerObj[field] = {
					start: col,
					end: col + (colSize - 1)
				}
				col = ((col + (colSize - 1)) % columns) + 1;
			}
    });
		headerObj[colFields[colFields.length-1]] = {
			start: col,
			end: col
		}
    var serial = 1;

    getKeysData(jsonData, 0, [], function (err, data) {
      if (!err) {
        commonFunctions.errorlogger.info("heading array" , data);
        rcb(wb);
      }
		});
		
		// chandni's code
		function getKeysData(jsonData, parent, headerArray, callback) {
			if (Array.isArray(jsonData)) {
				var keys = [];
				if (typeof jsonData[0] !== 'object') {
					for (var k = 0; k < jsonData.length; k++) {
						headerArray.push(jsonData[k]);
						row = row + 1;
					}
					if (jsonData.length == k) {
						callback(null, headerArray)
					}
				} else {
					if (jsonData.length > 0) {
						var asyncArrTask = [];
						for (var arr = 0; arr < jsonData.length; arr++) {
							if (parent == 0) {
								ws.cell(row, 1).style(tableLabelsCount).string(''+serial);
								serial++;
							}
							keys = Object.keys(jsonData[arr]);
							keyData(keys, parent, 1, arr, jsonData, headerArray, function (err, ress) {
								if (arr + 1 !== jsonData.length) {
									row = row + 1;
								}
							})
						}
						callback(null, headerArray);
					}
				}
			} else {
				keys = Object.keys(jsonData);
				if (parent == 0) {
					ws.cell(row, 1).number(serial);
					serial++;
				}
				keyData(keys, parent, 0, null, jsonData, headerArray, function (err, ress) {
					callback(null, headerArray);
				})
			}
		}

		function keyData(keys, parent, isArray, index, jsonData, headerArray, callback) {
			var asyncTask = [];
			if (isArray === 1) {
				for (var i = 0; i < keys.length; i++) {
					if (typeof jsonData[index][keys[i]] === 'object') {
						getKeysData(jsonData[index][keys[i]], keys[i], headerArray, function (err, data) {
							
						})
					} else {
						if (keys[i] == "value") {
							headerArray.push(jsonData[index][keys[i]] + "value");
							ws.cell(row, headerObj[parent].start, row, headerObj[parent].end, true).string(jsonData[index][keys[i]].toString());
							
						} else {
							headerArray.push(jsonData[index][keys[i]]);
							ws.cell(row, headerObj[keys[i]].start, row, headerObj[keys[i]].end, true).string(jsonData[index][keys[i]].toString());
							
						}
					}
				}
			} else {
				for (var i = 0; i < keys.length; i++) {
					
					if (typeof jsonData[keys[i]] === 'object') {
						getKeysData(jsonData[keys[i]], keys[i], headerArray, function (err, data) {
							
						})
					} else {
						if (keys[i] === "value") {
							headerArray.push(jsonData[keys[i]] + "VALUES");
							ws.cell(row, headerObj[parent].start, row, headerObj[parent].end, true).string(jsonData[keys[i]].toString());
							
						} else {
							headerArray.push(jsonData[keys[i]]);
							ws.cell(row, headerObj[keys[i]].start, row, headerObj[keys[i]].end, true).string(jsonData[keys[i]].toString());
							
						}
					}
				}
			}
			callback(null, headerArray);
		}
		//////////////////////////////////////
  } else {
    col = 2;
    for (var i = 0; i < report.length; i++) {
      ws.cell(row, col - 1, row, col - 1, true).style(tableLabelsCount).string('' + (i + 1));
      for (var j = 0; j < colFields.length - 1; j++) {
        ws.cell(row, col, row, col + (colSize - 1), true).string(''+report[i][colFields[j]]);
        col = ((col + (colSize - 1)) % columns) + 1;
      }
      if (isUrl('' + report[i][colFields[colFields.length - 1]])) {
        ws.cell(row, col, row, col, true).style({
          alignment: {
            horizontal: 'right'
          }
        }).link('' + report[i][colFields[colFields.length - 1]], '' + report[i][colFields[colFields.length - 1]], '' + report[i][colFields[colFields.length - 1]]);
      } else {
        ws.cell(row, col, row, col, true).style({
          alignment: {
            horizontal: 'right'
          }
        }).string('' + report[i][colFields[colFields.length - 1]]);
      }
     

      col = 2;
      row += 1;
    }
    rcb(wb);
  }
}

function isUrl(str) {
  if (str.indexOf('http://') == 0 || str.indexOf('https://') == 0 || str.indexOf('file://') == 0) {
      return true;
  } else {
      return false;
  }
}

function getExcelReport(data, cb) {
  getTemplate(data, wb => {
    cb(wb);
  });

}

function moveJsonToCsv(fields, data, cb) {
  var json2csvParser = new json2csv({fields : fields});
  var result = json2csvParser.parse(data);
  cb(result);
}

module.exports = {
  getExcelReport: getExcelReport,
  moveJsonToCsv: moveJsonToCsv
}
