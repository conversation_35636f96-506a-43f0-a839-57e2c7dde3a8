var request = require("request");
const { OAuth2Client } = require('google-auth-library');
var scopes = ['https://www.googleapis.com/auth/userinfo.profile', 'https://www.googleapis.com/auth/plus.me', 'https://www.googleapis.com/auth/drive.readonly', 'https://www.googleapis.com/auth/userinfo.email'];
var commonFunctions = require('./../../utils/commonFunctions');
var { drive } = require('../../constants/appVariables');
var md5 = require('md5');
var commonfunction = require('../../utils/commonFunctions');
var md5salt = commonfunction.appVariables.analytics.md5salt;



const driveAuthorisationIntermediate = function (contentSource, authorization, callback) {

    var clientSecret = authorization.client_secret || drive.appSecret;
    var clientId = authorization.client_id || drive.clientId;
    var redirectUrl = "https://oauthsfdc.searchunify.com";
    // var auth = new googleAuth();
    var oauth2Client = new OAuth2Client(clientId, clientSecret, redirectUrl);
    var authUrl = oauth2Client.generateAuthUrl({
        access_type: 'offline',
        prompt:'consent',
        scope: scopes
    });
    commonFunctions.errorlogger.info("authURL",authUrl);
    callback(null, { "oauth": authUrl });
}

const getUser  = function(accessToken, contentSourceId, req,callback){
    var options = { 
        method: 'GET',
        url: 'https://www.googleapis.com/oauth2/v1/userinfo',
        qs: { 
            access_token: accessToken
        },
        headers: {
            'cache-control': 'no-cache' 
        } 
    };
    request(options, function (error, response, body) {
        if(error){
            callback(error, null);
        }
        else if( response.statusCode != 200){
            callback("bad status Code", null);
        }
        else{
            body = JSON.parse(body);
            userID = body.id;
            var md5Email = md5(body.email+md5salt);
            commonFunctions.getContentSourceDataById(contentSourceId, req, function(err,data){
                if(data.authorization.htaccessUsername)
                {
                    if(data.authorization.htaccessUsername !== userID){
                        commonFunctions.deleteDriveSpace(contentSourceId,req, function (err, result) {
                            if (!err) {
                                callback(null, {"userID":userID, "md5Email":md5Email});
                            }
                            else{
                                callback(err, null);
                            }
                        });                        
                    }
                    else{
                        callback(null, {"userID":userID, "md5Email":md5Email});
                    }
                }
                else{
                    callback(null, {"userID":userID, "md5Email":md5Email});
                }
            });
        }
    });
}

/* Fetch custom and default objects */

const requestTofetchObjectFields = function (authorization, objectName,req, callback) {
    var customFieldArray = [];
    var flag = 7;
    commonFunctions.getContentSourceObjectsAndFieldsById(authorization.content_source_id,req, function (err, res) {
        if (err) {
            callback(err, null)
        }
        else {
          var standardObj = commonFunctions.metadata.loadMappingForDbAndElastic("12").objects;
            var fieldsArr = [];

            for (var i = 0; i < standardObj.length; i++) {
                if (objectName == standardObj[i].name) {
                    flag = 0;
                    fieldsArr = commonFunctions.metadata.loadMappingForDbAndElastic("12").fields.concat(standardObj[i].fields);
                    break;
                }
            }
            var listToAdd = [];
            var listInt = -1;
            for (var type = 0; type < res.length; type++) {
                if (objectName == res[type].name) {
                    listInt = type;
                }
            }
            if (listInt != -1) {
                type = listInt;
                listToAdd = res[type].fields;
                for (var mn = 0; mn < fieldsArr.length; mn++) {
                    var found = 0;
                    for (var field = 0; field < res[type].fields.length; field++) {
                        if (fieldsArr[mn].name == res[type].fields[field].name) {
                            found = 1;
                            break;
                        }
                    }
                    if (found == 0) {
                        listToAdd.push(fieldsArr[mn]);
                    }
                }
                for (var field = 0; field < listToAdd.length; field++) {

                    customFieldArray.push({
                        "name": listToAdd[field].name,
                        "label": listToAdd[field].label,
                        "type": listToAdd[field].type
                    })
                }

            } else {
                for (var field = 0; field < fieldsArr.length; field++) {

                    customFieldArray.push({
                        "name": fieldsArr[field].name,
                        "label": fieldsArr[field].label,
                        "type": fieldsArr[field].type
                    })
                }
            }
            callback(null, { data: customFieldArray, flag: flag });
        }
    })
}

module.exports = {
    driveAuthorisationIntermediate: driveAuthorisationIntermediate,
    getUser: getUser,
    requestTofetchObjectFields: requestTofetchObjectFields
}
