/**
 * Created by man<PERSON><PERSON> on 13/12/17.
 */

var express = require("express");
var router = express.Router();
var async = require("async");
var request = require("request");
var fs = require("fs");
var commonFunctions = require("../../utils/commonFunctions");
const kafkaLib = require("../../utils/kafka/kafka-lib");
var path = require("path");




const { tenantSqlConnection  } = require('../../auth/sqlConnection');
var config = require("config");
const { getTenantsRateLimitInfo } = require("auth-middleware");
var commonFunctions = require("./../../utils/commonFunctions");
const { deletePlatform, getPlatforms, cloneSearchClient } = require("./searchClient");
const { promisify } = require('util')
const requestPromise = promisify(request);

router.post("/discardABTest", async (req, res) => {
  const tenant_id = req.headers["tenant-id"];
  const { abTestId } = req.body;

  if (!tenant_id) {
    return res.status(400).send({ error: "Missing tenant-id in headers." });
  }

  if (!abTestId ) {
    return res.status(400).send({ error: "Missing required fields in the request body." });
  }

  try {
    const abTestData = await commonFunctions.fetchAbTestData(tenant_id, abTestId);
    const updateResult = await discardAbTest(req, res, tenant_id, abTestId);
    const result = await commonFunctions.deleteABTestData(tenant_id, abTestId);
    const { id, uid, childScs } = abTestData;
    const messageData = {
      id,
      uid,
      tenantId: tenant_id,
      childScs,
      event: 'delete'
    };
    commonFunctions.errorlogger.info("Publishing delete AB test data to Kafka", messageData);
    await kafkaLib.publishMessage({
      topic: config.get("kafkaTopic.abTestTopic"),
      messages: [
        {
          value: JSON.stringify(messageData)
        }
      ]
    });
    return res.status(200).send(result);
  } catch (error) {
    commonFunctions.errorlogger.error("Failed to discard AB Test", error.message);
    return res.status(error.status || 500).send({ error: error.message });
  }
});

const discardAbTest = async (req, res, tenant_id, abTestId) => {
  return new Promise((resolve, reject) => {
    commonFunctions.fetchAbTestData(tenant_id, abTestId)
      .then(fetchedData => {
        const abTestData = fetchedData;
        const uid = abTestData.uid;

          req.query.clonnedAbTestSc = true;
          getPlatforms(req, (platforms, err) => {
            if (platforms && platforms.error) {
              commonFunctions.errorlogger.error("Error fetching platforms for discard AB test", platforms.error);
              reject({
                status: 500,
                message: "Failed to get search client data",
              });
              return;
            }

            const filteredPlatforms = platforms.message.filter(
              platform => platform.ab_test_parent === uid
            );

            if (filteredPlatforms.length === 0) {
              resolve({
                status: 200,
                message: "No matching platform found for the given ID",
              });
              return;
            }

            (async () => {
              try {
                const results = await Promise.all(
                  filteredPlatforms.map(platform => {
                    req.body.client = platform;
                    req.body.clonnedScLength = filteredPlatforms.length;
                    return deletePlatform(req, res, platform);
                  })
                );

                resolve({
                  message: "A/B Test updated successfully, and related search clients deleted.",
                  data: {
                    id: abTestData.id,
                  },
                });

              } catch (error) {
                commonFunctions.errorlogger.error("Error while deleting the AB Test Cloned Search Clients.");
                reject({
                  status: 500,
                  message: "Failed to delete related search clients.",
                });
              }
            })();
          });
      })
      .catch(error => {
        commonFunctions.errorlogger.error("Error fetching AB test data during discard",error);
        reject(error);
      });
  });
};



router.post("/updateABTest", async (req, res) => {
  const tenant_id = req.headers["tenant-id"];
  const { abTestId, ab_test_status } = req.body;

  if (!tenant_id) {
    return res.status(400).send({ error: "Missing tenant-id in headers." });
  }

  if (!abTestId || ab_test_status === undefined) {
    return res.status(400).send({ error: "Missing required fields in the request body." });
  }

  try {
    const updateResult = await updateABTest(req, res, tenant_id, abTestId, ab_test_status);
    commonFunctions.errorlogger.info("AB Test updated successfully", updateResult);

    const abTestData = await commonFunctions.fetchAbTestData(tenant_id, abTestId);
    let abTestStatusForUid = await commonFunctions.getAbTestStatus(req, abTestData.uid);
    const hasAbTest = abTestStatusForUid.data !== null;
    let data = {
      task: "updateAnJsVariable",
      varName: 'isABTestEnabled',
      value: hasAbTest,
      uid: abTestData.uid,
      tenantId: req.headers["tenant-id"],
      updatedBy: req.headers.session
    }

    kafkaLib.publishMessage({
      topic: config.get("kafkaTopic.searchClientTopic"),
      messages: [
        {
          value: JSON.stringify(data),
          key: abTestData.uid
        },
      ],
    });

    const {  abTestName, searchClientName, ...filteredData } = abTestData;
    filteredData.event = 'update';
    commonFunctions.errorlogger.info("Publishing updated AB test data to Kafka", filteredData);
    
    await kafkaLib.publishMessage({
      topic: config.get("kafkaTopic.abTestTopic"),
      messages: [{
        value: JSON.stringify(filteredData)
      }]
    });

    return res.status(200).send(updateResult);
  } catch (error) {
    commonFunctions.errorlogger.error("Failed to update AB Test", error.message);
    return res.status(error.status || 500).send({ error: error.message });
  }
});


const updateABTest = async( req, res, tenant_id, abTestId, ab_test_status) => {
  let start_date = null;
  let end_date = null;
  let abTestData;
  
  return new Promise((resolve, reject) => {
    commonFunctions.fetchAbTestData(tenant_id, abTestId)
      .then(fetchedData => {
        abTestData = fetchedData;
        const id = abTestData.id;
        const uid = abTestData.uid;
        
        if (ab_test_status === 1) {
          const currentDate = new Date();
          start_date = currentDate.toISOString().slice(0, 19).replace('T', ' ');
      
          const endDate = new Date(currentDate);
          endDate.setDate(endDate.getDate() + parseInt(abTestData.testDuration, 10));
          endDate.setHours(23, 59, 59, 999);
          end_date = endDate.toISOString().slice(0, 19).replace('T', ' ');
        }

        const updateQuery = ab_test_status === 1
          ? `
            UPDATE ab_test 
            SET 
              start_date = ?, 
              end_date = ?, 
              ab_test_status = ? 
            WHERE 
              tenant_id = ? AND 
              uid_ab_test = ?
          `
          : `
            UPDATE ab_test 
            SET 
              ab_test_status = ? 
            WHERE 
              tenant_id = ? AND 
              uid_ab_test = ? 
          `;

        const deleteSearchClientsQuery = `
        DELETE FROM search_clients 
        WHERE ab_test_parent = ?
      `;

        const values = ab_test_status === 1
          ? [start_date, end_date, ab_test_status, tenant_id, id]
          : [ab_test_status, tenant_id, id];

        return new Promise((resolveQuery, rejectQuery) => {
          connection[tenant_id].execute.query(updateQuery, values, (fetchError, fetchResult) => {
            if (fetchError) {
              commonFunctions.errorlogger.error("Error executing AB test update query", fetchError);
              rejectQuery({
                status: 500,
                message: "Failed to update AB test data."
              });
              return;
            }
            
            if (!fetchResult || fetchResult.affectedRows === 0) {
              rejectQuery({
                status: 400,
                message: `No matching record found for update with id: ${id}`
              });
              return;
            }

            if (ab_test_status != 1) {
              req.query.clonnedAbTestSc = true;
              getPlatforms(req, (platforms, err) => {
                if (platforms && platforms.error) {
                  commonFunctions.errorlogger.error("Error fetching platforms during AB test update", platforms.error);
                  rejectQuery({
                    status: 500,
                    message: "Failed to get search client data",
                  });
                  return;
                }
                const filteredPlatforms = platforms.message.filter(platform => platform.ab_test_parent === uid);
                if (filteredPlatforms.length === 0) {
                  resolveQuery({
                    status: 200,
                    message: "No matching platform found for the given ID"
                  });
                  return;
                }

                  (async () => {
                    try {
                      const results = await Promise.all(
                        filteredPlatforms.map(platform => {
                          req.body.client = platform;
                          req.body.clonnedScLength = filteredPlatforms.length;
                          return deletePlatform(req, res, platform);
                        })
                      );

                      resolveQuery({
                        message: "A/B Test updated successfully, and related search clients deleted.",
                        data: {
                          id: abTestData.id,
                          ab_test_status,
                        },
                      });
      
                    } catch (error) {
                      console.log("Error while deleting the AB Test Clonned Search Client.")
                      rejectQuery({
                      status: 500,
                      message: "Failed to delete related search clients.",
                    });
                    }
                  })();
              });
            } else {
              resolveQuery({
                message: "A/B Test updated successfully.",
                data: {
                  id: abTestData.id,
                  start_date,
                  end_date,
                  ab_test_status,
                },
              });
            }
        });
      })
      .then(result => {
        resolve(result);
      })
      .catch(error => {
        reject(error);
      });
  });
})
}



router.post("/createNewABTest", async(req, res) => {
  const tenant_id = req.headers["tenant-id"];

  if (!tenant_id) {
    return res.status(400).send({ error: "Missing 'tenant-id' in headers." });
  }

  const { sc_uid, test_duration, ab_test_status, name,  searchMethodTrafficSplit } = req.body;



  if (!sc_uid || !test_duration || !name || !searchMethodTrafficSplit) {
    return res.status(400).send({
      error: "Missing required fields.",
    });
  }

  let existingAbTests = await getAbTestDataFunc(tenant_id, null);
  const existingAbTest = existingAbTests.find(test => 
   ( test.searchClient.uid === sc_uid && (test.status === 0 || test.status === 1)) ||  (test.name.trim().toLowerCase() === name.trim().toLowerCase())
  );

  if (existingAbTest) {
    return res.status(500).send({
      error: "You cannot create an A/B test because an A/B test is already created/running for this UID or An A/B test with the same name already exists.",
    });
  }

  const fetchIdQuery = `SELECT id AS sc_id,search_client_type_id, language, s3_supported, name as mainScName  FROM search_clients WHERE uid = ?`;
  connection[tenant_id].execute.query(fetchIdQuery, [sc_uid], async (fetchError, fetchResult) => {
    if (fetchError) {
      commonFunctions.errorlogger.error("Error fetching details from search_clients table", fetchError);
      return res.status(500).send({ error: "Failed to fetch sc_id from search_clients." });
    }

    if (fetchResult.length === 0) {
      return res.status(500).send({
        error: `No matching record found in search_clients table for sc_uid: ${sc_uid}`,
      });
    }

    const sc_id = fetchResult[0].sc_id;
    const search_client_type_id = fetchResult[0].search_client_type_id;
    const language = fetchResult[0].language;
    const s3_supported = fetchResult[0].s3_supported;
    const mainScName = fetchResult[0].mainScName;

    const abTestPayload = {
      sc_id,
      sc_uid,
      test_duration,
      ab_test_status,
      name,
      searchMethodTrafficSplit,
      tenant_id : req.headers["tenant-id"]
    };
      try {
        const searchMethodMap = {
          neural: `${mainScName}_NeuralSearch`,
          hybrid: `${mainScName}_HybridSearch`,
          keyword: `${mainScName}_KeywordSearch`
        };
        headerSentCleanUp = 0;

        connection[req.headers['tenant-id']].execute.query("SELECT UUID() AS uid", function (error, result) {
          abTestPayload.uuidABTest =  result[0].uid;
          const cloneOperations = Object.entries(searchMethodTrafficSplit).map(async ([method,trafficValue]) => {
            const searchMethods = searchMethodMap[method];
            const searchMethodsCount = Object.keys(searchMethodTrafficSplit).length;
            const clonePayload = {
              platformId: sc_id,
              name: `${searchMethods}`,
              search_client_type_id,
              restoreSettings: false,
              uid: `${sc_uid}`, 
              restoreUid: 0,
              language,
              s3_supported,
              ab_test_status,
              trafficValue,
              method,
              searchMethodsCount
            };
      
            const cloneReq = { ...req, body: clonePayload };
            commonFunctions.errorlogger.info("Modified body for clone functionality:", clonePayload);
      
             await cloneSearchClient(cloneReq, res, abTestPayload);
          });
          
        });        
        
      } catch (error) {
        commonFunctions.errorlogger.error("Error during clone operations", error);
        return res.status(500).send({ error: "Failed to clone search clients." });
      }
      
  });
});



router.post('/getAbTestData', async (req, res) => {
  try {
    const tenantId = req.headers['tenant-id'];
    const abTestId = req.body.abTestId || null;  // Use query parameters for abTestId to allow fetching all records if abTestId is not provided
    let result = await getAbTestDataFunc(tenantId, abTestId);
    commonFunctions.errorlogger.info("AB Test data fetched successfully.", result);
    res.status(200).send({
      success: true,
      message: 'AB Test data fetched successfully.',
      data: result,
      recordCount: result.length || 0,
    });
  } catch (err) {
    commonFunctions.errorlogger.error("An error occurred while fetching AB Test data.", err.message);
    res.status(500).send({
      success: false,
      message: 'An error occurred while fetching AB Test data.',
      error: err.message,
    });
  }
});

function getAbTestDataFunc(tenantId, abTestId = null) {
  return new Promise((resolve, reject) => {
    let getAbTestDataQuery = `
    SELECT JSON_OBJECT(
        'id', ab.uid_ab_test,
        'name', ab.name,
        'sc_id', ab.sc_id,
        'sc_uid', ab.sc_uid,
        'testDuration', ab.test_duration,
        'startDate', ab.start_date,
        'endDate', ab.end_date,
        'abTestStatus', ab.ab_test_status,
        'createdAt', ab.created_at,
        'updatedAt', ab.updated_at,
        'searchClientName', sc.name,
        'children', (
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    'id', abc.ab_test_id,
                    'name', abc.name,
                    'uid', abc.uid,
                    'trafficSplit', abc.traffic_split
                )
            )
            FROM ab_test_children AS abc
            WHERE abc.ab_test_id = ab.uid_ab_test
        )
    ) AS result
    FROM ab_test AS ab
    LEFT JOIN search_clients AS sc ON ab.sc_id = sc.id
    WHERE ab.tenant_id = ?
`;

    // If abTestId is provided, add it to the query
    if (abTestId) {
      getAbTestDataQuery += ` AND ab.uid_ab_test = ?`;
    }

    // Add ORDER BY clause to sort by created_at in descending order (newest first)
    getAbTestDataQuery += ` ORDER BY ab.created_at DESC`;

    // Execute the query with parameters
    const queryParams = abTestId ? [tenantId, abTestId] : [tenantId];


    connection[tenantId].execute.query(getAbTestDataQuery, queryParams, (error, resultData) => {
      if (error) {
        return reject(new Error('Database query error: ' + error.message));
      }

      if (resultData && resultData.length > 0) {
        try {
          // If there are multiple records, we need to parse each one
          const parsedResults = resultData.map(row => JSON.parse(row.result));
          const formattedResults = parsedResults.map(record => ({
            id: record.id,
            name: record.name,
            searchClient: {
              id: record.sc_id,
              uid: record.sc_uid,
              name: record.searchClientName,
            },
            duration: record.testDuration,
            status: record.abTestStatus,
            period: {
              start: record.startDate,
              end: record.endDate,
            },
            children: record.children || [], // Ensure children are always an array
          }));

          resolve(formattedResults);
        } catch (parseError) {
          reject(new Error('Error parsing the result data: ' + parseError.message));
        }
      } else {
        resolve([]);
      }
    });
  });
}




router.post("/deleteABTest", async (req, res) => {
  const tenant_id = req.headers["tenant-id"];
  const { abTestId } = req.body;

  try {
    const abTestData = await commonFunctions.fetchAbTestData(tenant_id, abTestId);
    const result = await commonFunctions.deleteABTestData(tenant_id, abTestId);

    if (result.status === 200) {
      const { id, uid, childScs } = abTestData;
      const messageData = {
        id,
        uid,
        tenantId: tenant_id,
        childScs,
        event: 'delete'
      };
      commonFunctions.errorlogger.info("Publishing delete AB Test event to Kafka", messageData);

      await kafkaLib.publishMessage({
        topic: config.get("kafkaTopic.abTestTopic"),
        messages: [
          {
            value: JSON.stringify(messageData)
          }
        ]
      });
    }

    res.status(result.status).send(result);

  } catch (error) {
    commonFunctions.errorlogger.error("Error in deleteABTest", error.message);
    res.status(error.status || 500).send({ 
      error: error.message || "Unexpected server error." 
    });
  }
});








router.delete("/deleteExpiredAbTests", async (req, res) => {
  commonFunctions.errorlogger.info("Triggered deleteExpiredAbTests route through Cron Job.");


  const adminSecret = req.headers["admin-secret"];
  if (adminSecret !== config.get("internalAdminSecret")) {
    return res.sendStatus(401);
  }

  try {
    // Fetch tenant info
    let tenantInfo;
    try {
      const result = await getTenantsRateLimitInfo();
      tenantInfo = {};
      for (let i in result.data) {
        if (Object.keys(result.data[i]).length) {
          tenantInfo[i] = result.data[i];
        }
      }
    } catch (err) {
      commonFunctions.errorlogger.error("Error fetching tenant rate limit info:", err);
      throw new Error("Failed to fetch tenant information.");
    }

    commonFunctions.errorlogger.info("Tenant rate limits info:", tenantInfo);
    const tenantIds = Object.keys(tenantInfo);
    console.log("Tenant IDs:", tenantIds);
    commonFunctions.errorlogger.info("Tenant IDs:", tenantIds);

    const results = [];
    for (const tenantId of tenantIds) {
      commonFunctions.errorlogger.info(`Processing tenant: ${tenantId}`);

      try {
        // Get expired tests
        const query = `
          SELECT uid_ab_test, sc_uid 
          FROM ab_test 
          WHERE tenant_id = ? AND DATE(end_date) <= DATE(NOW()) AND ab_test_status = 1
        `;

        const expiredABTests = await new Promise((resolve, reject) => {
          connection[tenantId].execute.query(query, [tenantId], (error, results) => {
            if (error) {
              commonFunctions.errorlogger.error(`Error executing query for tenant ${tenantId}:`, error);
              return reject({ status: 500, message: "Database query failed." });
            }
            resolve(results);
          });
        });

        if (expiredABTests.length === 0) {
          commonFunctions.errorlogger.info(`No expired A/B Tests found for tenant ${tenantId}.`);
          continue;
        }


        commonFunctions.errorlogger.info(`Found ${expiredABTests.length} expired A/B Tests for tenant ${tenantId}`);

        // Delete each expired test
        for (const test of expiredABTests) {
          try {
            await executeABTestCleanup(req, res, tenantId, test.uid_ab_test, 3);
            results.push({
              tenantId,
              testId: test.uid_ab_test,
              status: 'success'
            });
          } catch (error) {
            commonFunctions.errorlogger.error(
              `Error deleting A/B Test data for tenant ${tenantId}, id ${test.uid_ab_test}:`,
              error.message
            );
            results.push({
              tenantId, 
              testId: test.uid_ab_test,
              status: 'failed',
              error: error.message
            });
          }
        }
      } catch (err) {
        commonFunctions.errorlogger.error(`Error processing tenant ${tenantId}:`, err);
        results.push({
          tenantId,
          status: 'failed',
          error: err.message 
        });
      }
    }

    res.status(200).send({ 
      message: "Expired A/B Test data cleanup completed.",
      results
    });

  } catch (error) {
    commonFunctions.errorlogger.error("Error in deleteExpiredAbTests route:", error);
    res.status(500).send({ error: error.message || "Unexpected server error." });
  }
});

const executeABTestCleanup = async (req, res, tenantId, id, status) => {
  try {
    // Get sc_uid first
    const getScUidQuery = `
      SELECT sc_uid 
      FROM ab_test 
      WHERE uid_ab_test = ? AND tenant_id = ?
    `;

    const scUidResult = await new Promise((resolve, reject) => {
      connection[tenantId].execute.query(getScUidQuery, [id, tenantId], (error, results) => {
        if (error) {
          return reject(new Error(`Failed to get sc_uid for id ${id}: ${error.message}`));
        }
        if (!results || results.length === 0) {
          return reject(new Error(`No sc_uid found for id ${id}`));
        }
        resolve(results[0].sc_uid);
      });
    });

    // Delete child search clients - removed tenant_id from WHERE clause since it doesn't exist
    try {
      req.query.clonnedAbTestSc = true;
      req.query.abTestCron = true;
      req.headers["tenant-id"] = tenantId;
      const platforms = await new Promise((resolve, reject) => {
        getPlatforms(req, (result) => {
          if (result) {
            resolve(result);
          } else {
            reject("Error while fetching Search client list.");
          }
        });
      });
      const filteredPlatforms = platforms.message.filter(platform => platform.ab_test_parent === scUidResult);
      if (filteredPlatforms.length === 0) {
        throw new Error("No matching platform found for the given ID");
      }
     

      await Promise.all(
        filteredPlatforms.map(async platform => {
          req.body.client = platform;
          req.body.clonnedScLength = filteredPlatforms.length;
          await deletePlatform(req, res, platform);
        })
      );
    } catch (error) {
      commonFunctions.errorlogger.error("Error while processing platforms:", error);
      throw new Error("Failed to delete related search clients.");
    }


    // Update AB test status
    await new Promise((resolve, reject) => {
      const updateABTestStatusQuery = `
        UPDATE ab_test 
        SET ab_test_status = ?
        WHERE uid_ab_test = ? AND tenant_id = ?
      `;
      connection[tenantId].execute.query(updateABTestStatusQuery, [status, id, tenantId], (error) => {
        if (error) {
          return reject(new Error(`Failed to update ab_test_status for id ${id}: ${error.message}`));
        }
        resolve();
      });
    });

    // Fetch and publish event
    const abTestData = await commonFunctions.fetchAbTestData(tenantId, id);
    if (!abTestData) {
      throw new Error(`Failed to fetch AB test data for id ${id}`);
    }

    const {  abTestName, searchClientName, ...filteredData } = abTestData;
    filteredData.event = 'update';
    commonFunctions.errorlogger.info("Publishing A/B test update to Kafka:", filteredData);
    await kafkaLib.publishMessage({
      topic: config.get("kafkaTopic.abTestTopic"), 
      messages: [{
        value: JSON.stringify(filteredData)
      }]
    });

    commonFunctions.errorlogger.info(`Successfully cleaned up A/B Test for id ${id} with status ${status}`);

  } catch (error) {
    throw new Error(`Error in cleanup for id ${id}: ${error.message}`);
  }
};



router.post("/getAbTestAnalyticsData", async (req, res) => {
  commonFunctions.errorlogger.info("@admin service !! @admin panel search");
  let tenant_id = req.headers['tenant-id']
  var options = {
      method: 'POST',
      rejectUnauthorized: false,
      url: config.get('analyticsService.url') + "/reports/childReports",
      headers: {
        'analytics-secret': config.get('analyticsSecretKey'),
        'tenantId' : req.headers['tenant-id']
    },
      body : req.body,
      json: true
  };
  options.body.tenantId = tenant_id;
  try {
      const response = await requestPromise(options);
      res.send(response.body);
  } catch (error) {
      commonFunctions.errorlogger.info("Error occurred while fetching analytics report data:", error);
      return res.status(500).send({ error: "Failed to fetch analytics data" });
  }
});




module.exports = {
  router: router
}

