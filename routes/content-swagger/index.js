const app = require('express')();
const config = require('config').get('swagger');
const adminURL = require('config').get('adminURL')
const swaggerUi = require('swagger-ui-express');
const YAML = require('yamljs');
const swaggerRoutes = require('./api/content-swagger.js');

const port = config.get('port');

const option = {
  openapi: '3.0.0',
  info: {
    title: 'SearchUnify Content API Documentation',
    description: 'SearchUnify Content API Documentation using Swagger',
    version: '1.0'
  },
  produces: ['application/json'],
  servers: [
    {
      url: adminURL+"/api/",
      description: 'Development server'
    }
  ],
  tags: [
    {
      name: "ContentSource",
      description: "Know all about content sources",
      externalDocs: {
        description: "Find out more",
        url: "https://docs.searchunify.com/Content/Developer-Guides/Content.htm"
      }
    },
    {
      name: "APIData",
      description: "Know all about Data stored in Content Source APIs",
      externalDocs: {
        description: "Find out more",
        url: "https://docs.searchunify.com/Content/Developer-Guides/Content.htm"
      }
    }
  ],
  
  components : {
    securitySchemes: {
      oAuth2Password: {
        type: 'oauth2',
        flows: {
          password: {
            tokenUrl: adminURL+'/oauth/token'
          }
        }
      }
    },
    security: [
      {
        oAuth2Password: []
      }
    ]
  }
};

if (app.get('env') === 'development' && port !== undefined) {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup({ ...option, ...swaggerRoutes }));
}

app.listen(port, () => {
  console.log(`Swagger UI running on http://${config.get('host')}:${port}/api-docs`);
});
