/**
 * Created by man<PERSON><PERSON> on 30/8/16.
 */
var universal = require('./../universal');
var constants = require('../../constants/appVariables')
var async = require('async');
var moment = require('moment');
var request = require("request");
var geoip = require('geoip-lite');
// var analytics = require('./../analytics/common');
var versions = require('./version');
var searchunifyEmail = require('../../Lib/email');
var emailTemplates = require('../emailTemplates');
var kafkaLib = require('./../../utils/kafka/kafka-lib');
const express = require('express');
const router = express.Router();
const commonFunctions = require('./../../utils/commonFunctions');
const cron = require('./../../utils/Cron');
const config = require('config');
const {publishCrons} = require('../../utils/kafka/kafkaConsumers/publishCrons');
const { contentSources } = require('.');

var duplicateCheck = (id, headers, contentSources, subscriberEmailsReq) => {
  try{
   return new Promise((resolve, reject) => {
    let sESplitNew = [];
    let seSplit = subscriberEmailsReq.split(',')
    if(seSplit.length > 0) {
      for(const x in seSplit) {
        sESplitNew.push(`subscriberEmails LIKE '%${seSplit[x].trim()}%'`);
      }
    }
    let finalSE = sESplitNew.join(" OR ");
    let idStringInQuery = id ? `AND id != ${id}` : ``
    var sql = `SELECT * FROM email_notification_contentsources WHERE (${finalSE}) ${idStringInQuery}`;
    connection[headers['tenant-id']].execute.query(sql, function (err, rows) {
      if (!err) {
        let isDuplicate = false
        for (let i = 0; i < rows.length; i++){
          if(!isDuplicate){
            let subscriberEmailReqArr = subscriberEmailsReq.split(',')
            for (let j = 0; j < subscriberEmailReqArr.length; j++){
              if (rows[i].subscriberEmails.includes(subscriberEmailReqArr[j])){
                let contentSourceFromDb = JSON.parse(rows[i].contentSources.replace(/ /g, ""))
                let csFromDbArr = Object.values(contentSourceFromDb.map(a => a.csId))
                let csFromReqArr = Object.values(JSON.parse(contentSources).map(a => a.csId))
                const contains = csFromDbArr.some(el => {
                  return csFromReqArr.includes(el)
                })
                if ( contains ){
                  isDuplicate = true;
                  break;
                }
              }
            }
          } else break;
        }
        if ( isDuplicate ){
          return reject({ error: "failed", flag : "406", message : "Duplicate error"})
          }
          else {
            return resolve()
          }
      }
      else{
        return reject({error: err})
      }
    })
   })
  }catch(err){
    throw err;
  }

}

/*
 * get all already saved email notification preferences from database
 **/
router.get('/getAllNotificationPreferences', function (req, res, next) {


  async.parallel([
    function (cb) { //get data for keyword search

      var sql = "SELECT id,subscriberEmails,notificationFrequency from email_notification_preferences_analytics";
      connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
        cb(null, rows)
      })
    },
    function (cb) { // get data for weekly report

      var sql = "SELECT id,searchkeywords,subscriberEmails,notificationFrequency from email_notification_preferences";
      connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
        cb(null, rows)
      })
    },
    function (cb) { // get data for crawling subscribers

      var sql = "SELECT id,subscriberEmails,contentSources from email_notification_contentsources";
      connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
        rows.forEach(function(r) {
          r.contentSources = r.contentSources.split(',');
        })
        cb(null, rows)
      })
    }
  ], function (err, result) {
    res.send({
      flag: 200,
      listOfExistingPreferencesKeyword: result[1],
      listOfExistingPreferencesWeekly: result[0],
      listOfContentSourceSubcribers: result[2]
    })

  })
})
/*
 * save email notification preferences in databas
 **/
router.post('/saveNotificationPreferences', function (req, res, next) {
  // checking alert counter before adding
  
  var validateHtmlrequest = commonFunctions.htmlvalidate(req.body.notificationPreferencesData);
  if(validateHtmlrequest == true){
  var table = ""
  var type = req.body.notificationPreferencesData.subscriptionType;
  let url = ''
  let notificationType = '';
  if (type === "Keyword") {
    table = "email_notification_preferences";
    url = "/admin/notifications/sendNotifications?type=";
    notificationType = 'sendNotify';
  }
  else {
    table = "email_notification_preferences_analytics";
    url = "/admin/notifications/sendReport?type=";
    notificationType = 'sendReport';
  }
  

  var id = req.body.notificationPreferencesData.id;

  var searchkeywords = req.body.notificationPreferencesData.searchkeywords || "Weekly";
  var subscriberEmails = req.body.notificationPreferencesData.subscriberEmails;
  var currentEmailsArray = subscriberEmails.split(',')
  var multipleLimitExceeded = false;
  if (currentEmailsArray.length > 20){
    multipleLimitExceeded = true;
    return res.send({
      flag: 406,
      message: `can't set more than 20 emails at once`
    })
  }
  if (!multipleLimitExceeded){
    var notificationFrequency = req.body.notificationPreferencesData.notificationFrequency;
    let seSplitNew = [];
    let seSplit = subscriberEmails.split(',')
    if(seSplit.length > 0) {
      for(const x in seSplit) {
        seSplitNew.push(`subscriberEmails LIKE '%${seSplit[x].trim()}%'`);
      }
    }
    let finalSE = seSplitNew.join(" OR ");

    let searchKeywordSplitNew = [];
    let searchKeyword = searchkeywords.split(',')
    if(searchKeyword.length > 0) {
      for(const x in searchKeyword) {
        searchKeywordSplitNew.push(`searchKeywords LIKE '%${searchKeyword[x].trim()}%'`);
      }
    }
    let finalSearchKeyword = searchKeywordSplitNew.join(" OR ");
    let idStringInQuery = id ? `AND id != ${id}` : ``
    var existingEmailsSql = `SELECT * FROM ${table} WHERE (${finalSE}) AND (${finalSearchKeyword}) AND notificationFrequency = '${notificationFrequency}' ${idStringInQuery}`; 
    connection[req.headers['tenant-id']].execute.query(existingEmailsSql, function (err, rows) {
    if (!rows.length){
      var sql = "INSERT INTO " + table + "(id,searchkeywords, subscriberEmails, notificationFrequency) VALUES (?,?,?,?) ON DUPLICATE KEY UPDATE id=values(id), searchkeywords=values(searchkeywords),subscriberEmails=values(subscriberEmails), notificationFrequency=values(notificationFrequency)";
      connection[req.headers['tenant-id']].execute.query(sql, [id, searchkeywords, subscriberEmails, notificationFrequency], function (err, rows) {
    
        if (!err) {
          setNotificationsCron(notificationType, req, notificationFrequency, url);
          res.send({
            flag: 200,
            message: "done"
          });
        } else res.send(err);
      });
     }
     if (rows.length){
      res.send({
        flag: 406,
        message: "Duplicate Alert"
      });
     }
   
  })}

  
}else{
  res.send({
    flag: 403,
    message: "Only string values are allowed"
  });
}
})




router.post('/deleteNotificationPreferences', function (req, res, next) {
  commonFunctions.errorlogger.info("data is", req.body);
  var table = ""
  var type = req.body.notificationPreferencesData.subscriptionType
  if (type == "Keyword")
    table = "email_notification_preferences"
  else
    table = "email_notification_preferences_analytics"

  var id = req.body.notificationPreferencesData.id;
  var searchkeywords = req.body.notificationPreferencesData.searchkeywords;
  var subscriberEmails = req.body.notificationPreferencesData.subscriberEmails;
  var notificationFrequency = req.body.notificationPreferencesData.notificationFrequency;
  var sql = "DELETE FROM " + table + " WHERE id = ?";
  connection[req.headers['tenant-id']].execute.query(sql, [id], function (err, rows) {
    if (!err) {
      commonFunctions.errorlogger.warn("Done");
      res.send({
        flag: 200,
        message: "done"
      });
    } else res.send(err);
  });
});

function setCron() {
  var startTime = { "HH": 0, "mm": 0 }; //  HH:mm
  var day = 1; //Day of week for weekly email 1=Monday
  var weeklyCron = moment().utc().day(7 + day).set('hour', startTime.HH).set('minute', startTime.mm).set('second', 0).valueOf() - Date.now();
  var dailyCron = moment().utc().hour(24 + startTime.HH).minute(0 + startTime.mm).set('second', 0).valueOf() - Date.now();
  setTimeout(function () {
    sendNotifications("Weekly");
    setCron();
  }, weeklyCron);
  setTimeout(function () {
    sendNotifications("Daily");
    setCron();
  }, dailyCron);
}

router.get('/sendNotifications', function (req, res, next) {
  let type = req.query.type;
  req.headers.tenantId = req.query['tenant-id'];
  res.send('Done');
  var sql = "SELECT `searchkeywords`, GROUP_CONCAT(DISTINCT `subscriberEmails` SEPARATOR ';') AS emails FROM `email_notification_preferences` WHERE notificationFrequency='" + type + "' group by `searchkeywords`;";
  connection[req.headers.tenantId].execute.query(sql, function (err, rows) {
    commonFunctions.errorlogger.info("rows", rows);
    rows.forEach(r => {
      r.emails = r.emails.split(';');
      // var dsl = {
      //   "size": 150,
      //   "query": {
      //     "bool": {
      //       "must": { "bool": { "should": [] } },
      //       "filter": [{ "range": { "search_date": { "gte": "now-300d" } } }]
      //     }
      //   },
      //   "sort": [ { "search_date": { "order": "asc" } }]
      // };
      // r.searchkeywords = r.searchkeywords.split(',');
      // r.searchkeywords.map(x=>{
      //     dsl.query.bool.must.bool.should.push({"wildcard":{"text_entered" : x}});
      //     dsl.query.bool.must.bool.should.push({"wildcard":{"text_entered" : ("* " + x + " *")}});
      // })
      // if (type == "daily" || type == "Daily")
      //   dsl.query.bool.filter[0].range.search_date.gte = "now-1d/d";
      // else
      //   dsl.query.bool.filter[0].range.search_date.gte = "now-7d/d";
      // var options = {
      //   method: 'POST',
      //   url: "http://" + config.get('elasticIndex.host') + ":" + config.get('elasticIndex.port') + "/" + config.get('elasticIndex.analytics') + '/search_keyword/_search',
      //   headers: { 'content-type': 'application/json' },
      //   body: dsl,
      //   json: true
      // };
       // request(options, function (error, response, body) {
      //   if (error) {
      //     commonFunctions.errorlogger.error(error)
      //     return;
      //   }
      //   commonFunctions.errorlogger.info("body", body);
      //   body.hits.hits.length && r.emails.forEach(email => {
      //     var emailObject = {
      //       to: email,
      //       subject: "SearchUnify queries subscription report for keyword: " + r.searchkeywords,
      //       html: emailTemplates.createMessage(r.emails, body, r, type)
      //     }
      //     searchunifyEmail.sendEmail(emailObject.to, emailObject.subject, emailObject.html, (error, response) => {
      //       commonFunctions.errorlogger.error("err,response ", error, response);
      //     });
      //   });
      // });
      var days = 1;
      if (type == 'Weekly' || type == 'weekly')
        days = 7;
      if (type == 'Monthly' || type.toLowerCase() == 'monthly')
        days = 30;
        var date = new Date();
        var d = new Date(date.getTime() - (24 * 60 * 60 * 1000));
        var to = d.toISOString().split("T")[0];
        var last = new Date(date.getTime() - (days * 24 * 60 * 60 * 1000));
        var day = last.getDate();
        var month = last.getMonth() + 1;
        var year = last.getFullYear();
        var from = last.toISOString().split("T")[0];
        range = {
          from: from,
          to: to,
        }    
        let query ={
          search_keyword: r.searchkeywords,
          startDate: range.from,
          endDate: range.to,
          tenantId: req.headers.tenantId
        }
        var options = {
          method: 'GET',
          rejectUnauthorized: false,
          url: config.get('analyticsService.url') +`/notification/keywordsNotification`,
          qs: query
        };
        request(options, function (error, response, body) {
          let resultData = {};
          response.body = JSON.parse(response.body);
          if (error) {
            commonFunctions.errorlogger.error(error)
            return;
          }
          commonFunctions.errorlogger.info("body", body);
          response && response.body && response.body.data && response.body.data.length && r.emails.forEach(email => {
            var emailObject = {
              to: email,
              subject: "SearchUnify queries subscription report for keyword: " + r.searchkeywords,
              html: emailTemplates.createMessage(r.emails, response.body.data, r, type)
            }
            searchunifyEmail.sendEmail(emailObject.to, emailObject.subject, emailObject.html, (error, response) => {
              commonFunctions.errorlogger.error("err,response ", error, response);
            });
          });
        })     
    });
  });
});


router.get('/sendVersionMail', function (req, res, next) {
  if (!req.query.email) {
    return
  }
  versions.getAllVersions(req,function (versionData) {

    var currentVersion = versionData.versions.pastRelease[0];
    var emailObject = {
      to: req.query.email,
      subject: "Check out the new version of SearchUnify!",
      html: emailTemplates.createVersionMessage(req.query.email, currentVersion)
    }
    searchunifyEmail.sendEmail(emailObject.to, emailObject.subject, emailObject.html, (error, response) => {
      commonFunctions.errorlogger.info("response", response);
      if (response) res.send("Done")
    });

  });
})

router.get('/sendReport', function (req, res, next) {
  let type = req.query.type;
  req.headers.tenantId = req.query['tenant-id'];
  let range;
  let sql = "SELECT uid, name FROM `search_clients`";
  let task = [];
  let searchClients = [];
  connection[req.headers.tenantId].execute.query(sql, function (err, rows) {
    searchClients = rows;

    var days = 1;
    if (type == 'Weekly' || type == 'weekly')
      days = 7;
    if (type.toLowerCase() == 'monthly')
      days = 30;

    var date = new Date();
    var d = new Date(date.getTime() - (24 * 60 * 60 * 1000));
    //d.setDate(d.getDate() + 1);
    var to = d.toISOString().split("T")[0];

    var last = new Date(date.getTime() - (days * 24 * 60 * 60 * 1000));
    var day = last.getDate();
    var month = last.getMonth() + 1;
    var year = last.getFullYear();
    var from = last.toISOString().split("T")[0];

    range = {
      from: from,
      to: to,
    }

    for (var i = 0; i < searchClients.length; i++) {
      task.push((function (i) {
        return function (cb) {
          let query ={
            uid: searchClients[i].uid,
            startDate: range.from,
            endDate: range.to,
            tenantId: req.headers.tenantId
          } 
          var options = {
            method: 'GET',
            rejectUnauthorized: false,
            url: config.get('analyticsService.url') +`/notification/analyticsNotification`,
            qs: query
          };
          request(options, function (error, response, body) {
            let resultData = {};
            response.body = JSON.parse(response.body);
            if(response && body){
              resultData = response.body.data;
              resultData.name = searchClients[i].name;
            }
            cb(null, resultData)
          })
          // intermediateAnalyticsNotifications(searchClients[i].uid, range, function (err, result) {
          //   result.name = searchClients[i].name;
          //   cb(null, result)
          // })
        }
      })(i))
    }

    async.parallel(task, function (error, result) {
      if (error) commonFunctions.errorlogger.error(error)
      else {

        let interMediateTemplate = '';
        let email = [];
        let sql = "SELECT GROUP_CONCAT(DISTINCT `subscriberEmails` SEPARATOR ';') AS emails FROM `email_notification_preferences_analytics` WHERE notificationFrequency='" + type + "'";
        connection[req.headers.tenantId].execute.query(sql, function (err, results) {

          results.forEach(r => {
            email = r.emails.split(";");
          })
          //get unique email list
          email = email.filter((x, i, a) => a.indexOf(x) == i)

          result.map(r => {
            interMediateTemplate += emailTemplates.analyticsIntermediateTemplate(type, email, range, r.name, r)
          })

          email.forEach(email => {
            var emailObject = {
              to: email,
              subject: "SearchUnify: " + type + " Analytics",
              html: emailTemplates.analyticsTemplate(type, email, range, interMediateTemplate)
            }
            searchunifyEmail.sendEmail(emailObject.to, emailObject.subject, emailObject.html, (error, response) => {
              commonFunctions.errorlogger.error("err", error );
            });
          })
        })
        res.send("DONE");
      }
    });
  })
})

function sendAnalyticsTrackinReport(sessionInfo, range, url) {
  console.log("info: ", sessionInfo);
  range.from = range.from.split("||")[0];
  range.to = range.to.split("||")[0];
  var interMediateTemplate = url;
  var email = sessionInfo.email;
  sessionInfo.between = (range.from && range.to) ? "To": "";
  if (sessionInfo.internalUser == true || sessionInfo.internalUser == "true")
    sessionInfo.internalUser = "Internal User";
  if (sessionInfo.internalUser == false || sessionInfo.internalUser == "false")
    sessionInfo.internalUser = "External User";
  var emailObject = {
    to: email,
    subject: "SearchUnify: " + sessionInfo.label + " Analytics",
    html: emailTemplates.analyticsEmailTemplate(sessionInfo, email, range, interMediateTemplate)
  }
  searchunifyEmail.sendEmail(emailObject.to, emailObject.subject, emailObject.html, (error, response) => {
    commonFunctions.errorlogger.error("err",error);
  });
  return "DONE";
}

function intermediateAnalyticsNotifications(uid, range, callback) {

  let sendData = {
    body: {
      range: range,
      count: 10,
      uid: uid
    }
  }
  async.parallel([
    //different functions to get different results;

    function (cb) { //top search query
      analytics.queryCount(sendData, function (error, body) {
        if (error) cb(error, null);
        else {
          var myData = [];
          if(body.aggregations)
          body.aggregations.text_entered.buckets.forEach(b => {
            myData.push({ Query: b.key, Count: b.doc_count });
          });
          cb(null, myData);
        }
      });
    },
    function (cb) { //get no result chart
      analytics.missedQuery(sendData, function (err, body) {
        if (err) {
          cb(err, null);
        } else {
          var myData = [];
          if(body.aggregations)
          body.aggregations.text_entered.buckets.forEach(b => {
            myData.push({ Query: b.key, Count: b.doc_count });
          });
          cb(null, myData);
        }
      });
    },
    function (cb) { //top conversion
      analytics.getCoversion(sendData, function (body) {
        var myData = []
        body.forEach(b => {
          myData.push({ Subject: b.subject, Clicks: b.doc_count, Url: b.url });
        });
        cb(null, myData);
      });
    },
    function (cb) { //least click
      analytics.getLonelyDoc(sendData, function (body) {
        var myData = []
        body.forEach(b => {
          myData.push({ Subject: b.subject, Clicks: b.doc_count, Url: b.url });
        });
        cb(null, myData);
      });
    },
    function (cb) { //ready help articles
      analytics.readyHelpArticle(sendData, function (body) {
        var myData = []
        body.forEach(b => {
          myData.push({ Subject: b.subject, Clicks: b.doc_count, Url: b.url });
        });
        cb(null, myData);
      });
    }
  ], function (err, result) {
    // if (email.length == 0)
    //     var email = config.get('analyticsEmailList')
    callback(err, result)
  });
}

function setNotificationsCron(notificationType, req, type, adminURL) {
  let parameter = "@" + type.toLowerCase();
  const cronType = type;
  // const parameter = "*/20 * * * *";
  const values = {
    type: 'ADD',
    service: 'admin',
    frequency: parameter,
    tenantId: req.headers['tenant-id'],
    cronId: `${notificationType}_${cronType}_${req.headers.session.tpk}`,
    command: `curl '${config.get('adminURL')}${adminURL}${cronType}&tenant-id=${req.headers['tenant-id']}'`
}
    publishCrons(values);
}

/*
  save crawling subscribers data
*/
router.post('/saveNotificationPreferencesCrawling', async function (req, res, next) {
  var type = req.body.notificationPreferencesData.subscriptionType;
  var id = req.body.notificationPreferencesData.id;
  var contentSources = req.body.notificationPreferencesData.contentSources;
  var subscriberEmails = req.body.notificationPreferencesData.subscriberEmails;

  //Error check for subscriber emails
  var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@[0-9A-Za-z]((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9])+[A-Za-z0-9]+\.+[a-zA-Z]{2,}))$/
  if(subscriberEmails && subscriberEmails != ""){
    subscriberEmails.split(",").map(email => {  
      if(!re.test(email))
        res.send({error: true , message: "Invalid email provided"})
        return;
    })
  }else {
    res.send({error: true , message: "No email address provided"});
    return;
  }

  try{
    //Error check for content sources
    let tempCS = JSON.parse(contentSources);
    if(!tempCS || !tempCS.length){
      res.send({error: true, message: 'Select atleast one content source'});
      return;
    }
  }catch(e){
    res.send({error: true, message: "Invalid set of content source provided"});
    return; 
  }

  duplicateCheck( id,  req.headers, contentSources, subscriberEmails ).then(()=>{
    if(subscriberEmails && subscriberEmails != "")
    subscriberEmails = Array.from(new Set(subscriberEmails.split(","))).join();
  
    var sql = "INSERT INTO email_notification_contentsources (id, contentSources, subscriberEmails) VALUES (?,?,?) ON DUPLICATE KEY UPDATE id=values(id), contentSources=values(contentSources),subscriberEmails=values(subscriberEmails)";
    connection[req.headers['tenant-id']].execute.query(sql, [id, contentSources, subscriberEmails], function (err, rows) {
      if (!err) {
        id = rows.insertId;
        const kafkaData = {id, contentSources, subscriberEmails}
        try {
          kafkaLib.publishMessage({
            topic       : kafkaLib.SU_CRAWLER_TOPIC.emailSubscriptionPreferences,
            messages    : [{
              value     : JSON.stringify({ type: kafkaLib.KAFKA_EVENT_TYPES.add, data: kafkaData, tenantId: req.headers['tenant-id'] }),
              key: req.headers['tenant-id']
            }]
          });
          commonFunctions.errorlogger.info('saveNotificationPreferencesCrawling instruction has been sent to su-crawler Service by Kafka ')
        } catch (e) {
          commonFunctions.errorlogger.error('Error while sending saveNotificationPreferencesCrawling instruction to su-crawler Service by Kafka => ', e)
        }

        res.send({
          flag: 200,
          message: "done"
        });
      } else res.send({error : err});
  });
  }).catch((err) =>{
    res.send(err)
  })

  
})

router.post('/deleteNotificationPreferencesCrawling', function (req, res, next) {
  var id = req.body.notificationPreferencesData.id;
  var contentSources = req.body.notificationPreferencesData.contentSources;
  var subscriberEmails = req.body.notificationPreferencesData.subscriberEmails;
  var sql = "DELETE FROM email_notification_contentsources WHERE id = ?";
  connection[req.headers['tenant-id']].execute.query(sql, [id], function (err, rows) {
    if (!err) {
      const kafkaData = {id}
      try {
        kafkaLib.publishMessage({
          topic       : kafkaLib.SU_CRAWLER_TOPIC.emailSubscriptionPreferences,
          messages    : [{
            value     : JSON.stringify({ type: kafkaLib.KAFKA_EVENT_TYPES.delete, data: kafkaData, tenantId: req.headers['tenant-id'] }),
            key: req.headers['tenant-id']
          }]
        });
        commonFunctions.errorlogger.info('deleteNotificationPreferencesCrawling instruction has been sent to su-crawler Service by Kafka ')
      } catch (e) {
        commonFunctions.errorlogger.error('Error while sending deleteNotificationPreferencesCrawling instruction to su-crawler Service by Kafka => ', e)
      }

      console.log("Done");
      res.send({
        flag: 200,
        message: "done"
      });
    } else res.send(err);
  });
});

module.exports = {
  router: router,
  sendAnalyticsTrackinReport: sendAnalyticsTrackinReport
}
