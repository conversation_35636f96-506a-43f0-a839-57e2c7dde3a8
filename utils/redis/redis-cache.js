const redisCacheSet = async (redisClient, key) => {
  try {
    return await redisClient.getAsync(key);
  } catch (error) {
    return error;
  }
};

const mSearchCacheSet = async (redisClient, key, suQuery) => {
  try {
    await redisClient.set(key, JSON.stringify(suQuery), 'EX', 60 * 300);

    return {};
  } catch (error) {
    return error;
  }
};

const hIncrByOne = async (redisClient,  hashName, key) => {
  await redisClient.hincrby(hashName, key, 1);
};

const getKeyHash = async (redisClient, hashName, key) => {
  return await redisClient.hget( hashName, key);
};

const exists = async (redisClient, key) => {
  return await redisClient.exists( key);
};

const expireKey = async (redisClient, hashName, timeout) => {
  await redisClient.expire( hashName, timeout);
};

const ttlForKey = async (redisClient, hashName) => {
  return await redisClient.pttl( hashName);
};

const setKey = async (redisClient, key, timeout,  value = 1) => {
  await redisClient.set(key, value, 'EX', timeout);
};

const setHkey = async (redisClient, hashName, key, value) => {
  return await redisClient.hsetAsync( hashName, key,value);
};
async function hDecByOne(redisClient, hashName, key,defaulValue=-1) {
  return await redisClient.hincrbyAsync(hashName, key, defaulValue);
}
async function deleteKey(redisClient,key) {
  return await redisClient.delAsync(key);
}
 
async function getValue(redisClient,key,jsonParse=false) {
  return new Promise((resolve, reject) => {
    redisClient.get(key, (err, value) => {
        if (err) reject(err);
      else {
        if(!jsonParse) resolve(value);
        try{ 
          let parsedValue = JSON.parse(value);
          resolve(parsedValue);
      }catch (err){
        console.log("Error while parsing:",err);
        resolve({});
      }
    }
    });
  });
}
module.exports = {
redisCacheSet,
mSearchCacheSet,
hIncrByOne,
getKeyHash,
expireKey,
setKey,
ttlForKey,
exists,
getValue,
setHkey,
hDecByOne,
deleteKey
};
