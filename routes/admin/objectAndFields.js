module.exports = {
  jira: require('./jira')
  , drive: require('./drive')
  , box: require('./box')
  , jive: require('./jive')
  , confluence: require('./confluence')
  , zendesk: require('./zendesk')
  , lithium: require('./lithium')
  , helpscout: require('./helpscout')
  , box: require('./box')
  , github: require('./github')
  , youtube: require('./youtube')
  , sharepoint: require('./sharepoint')
  , stackoverflow: require('./stackoverflow')
  , slack: require('./slack')
  , litmos: require('./litmos')
  , moodle: require('./moodle')
  , dropbox: require('./dropbox')
  , marketo: require('./marketo')
  , mindtouch: require('./mindtouch')
  , dynamics: require('./dynamics')
  , servicenow: require('./servicenow')
  , receptive: require('./receptive')
  , docebo: require('./docebo')
  , vimeo: require('./vimeo')
  , sabaCloud: require('./sabaCloud')
  , skilljar : require('./skilljar')
  , discourse: require('./discourse')
  , wistia: require('./wistia')
};
