const kafka = require("../kafka-lib");


const updateOAuthStatus = async ({ authorization, req }) => {
    try {
        console.log('updating auth data in sucrawler', {
            contentSourceId: authorization.content_source_id,
            oauthSuccess: true
        });
        kafka.publishMessage({ 
            topic   : kafka.SU_CRAWLER_TOPIC.contentSource,
            messages: [{ 
                value : JSON.stringify({ type: kafka.KAFKA_EVENT_TYPES.updateAuthStatus, data: {
                    contentSourceId: authorization.content_source_id,
                    authorization: authorization,
                    oauthSuccess: true
                }, tenantId: req.headers['tenant-id'] })
            }]
        });
    } catch (e) {
        console.log('ERROR while publishing kafka for updating auth data', e);
        throw e;
    }
};

module.exports = {
    updateOAuthStatus
};
