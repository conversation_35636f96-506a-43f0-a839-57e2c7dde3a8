/**
 * Created by <PERSON><PERSON><PERSON> on 14-05-2016.
 */
var restRequest = require("request");
var oauthServer = require('oauth2-server');
const crypto = require('crypto');
var Request = oauthServer.Request;
var Response = oauthServer.Response;
// var config = require('../../config');
var db = config.db === 'mongo' ? require('./mongodb') : require('./sqldb');
const models = require('./models.js');
var oauth = require('./oauth');

module.exports = function (app) {

  app.post('/authorise', function (req, res) {
    var request = new Request(req);
    var response = new Response(res);

    return oauth.authorize(request, response).then(function (success) {
      //  if (req.body.allow !== 'true') return callback(null, false);
      //  return callback(null, true, req.user);
      res.json(success)
    }).catch(function (err) {
      res.status(err.code || 500).json(err)
    });
  });

  app.get('/authorise', function (req, res) {
    console.log("express.js /authorise session", req.session);
    console.log("express.js /authorise cookie ", req.cookies);
    if (res.locals && res.locals.authorised) {
      res.render('authorization.ejs', { model: res.locals, base_url: config.get('adminURL'), username: res.locals ? res.locals.name : req.cookies.name});
      return;
    } else {
      return res.redirect('resources/loginAuthorize/loginAuthorize.html?redirect=' + encodeURIComponent(config.get('adminURL') + req.url));
    }
  });

  app.get('/authorise_success', function (req, res) {
    if (res.locals && res.locals.authorised) {
      res.redirect(
        301, 
        req.query.redirect_uri + "?code=" + res.locals.authorizationCode + "&state=" + (req.headers.referer && (req.headers.referer.indexOf('tableauConnectorRawApi') || req.headers.referer.indexOf('tableauConnector')) ? res.locals.client.client_id+'___'+res.locals.client.client_secret : req.query.state)
        );
    } else {
      res.redirect('resources/loginAuthorize/loginAuthorize.html?redirect=' + config.get('adminURL') + '/authorise');
    }
  });
}
