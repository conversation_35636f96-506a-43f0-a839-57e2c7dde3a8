var async = require('async');
var commonFunctions = require('../../utils/commonFunctions');
const kafkaLib = require("../../utils/kafka/kafka-lib");

var knowledgeGraphSettings = function (configuration,req,callback) {

    var settings = {};
    var platformId = configuration.platformId;
    settings.tenantId = req.headers['tenant-id'];
    async.auto({
        search_client_uid: cb => {
            var sql = 'SELECT id, uid FROM search_clients where id = ';
            sql += platformId ? '?' : `(Select search_client_id from ${configuration.tableName} where id = ?)`;
            connection[req.headers['tenant-id']].execute.query(sql, [platformId ? platformId : configuration.id], function (err, data) {
                if (err) {
                    cb(null, []);
                }
                else {
                    platformId = data[0].id;
                    cb(null, data);
                }
            });
        },
        knowledge_graph_relation: ['search_client_uid', (dataFromAbove, cb) => {
            const sql = ` Select kgr.name, kgr.id, sc.name as search_client_name, sc.id as search_client_id, cs1.id as cs_source_id, cs1.name as cs_source_name, cs1.elasticIndexName as cs_source_elasticIndexName, cs2.elasticIndexName as cs_destination_elasticIndexName, cs2.name as cs_destination_name, cs2.id as cs_destination_id, cs2.content_source_type_id as cs_destination_type_id
                from knowledge_graph_relation kgr
                LEFT JOIN search_clients sc ON kgr.search_client_id = sc.id
                LEFT JOIN content_sources cs1 ON kgr.cs_source_id = cs1.id
                LEFT JOIN content_sources cs2 ON kgr.cs_destination_id = cs2.id
                WHERE search_client_id = ?`
            connection[req.headers['tenant-id']].execute.query(sql, [platformId], function (err, data) {
                if (err) {
                    cb(null, []);
                }
                else {
                    cb(null, data);
                }
            })
        }],
        k_graph_fields_relation: ['search_client_uid', (dataFromAbove, cb)  => {
            const sql = `SELECT kgfr.id, kgfr.relation_id, kgfr.mapper_obj_id, kgfr.mapping_obj_id, kgfr.mapper_field_id, kgfr.mapping_field_id, kgfr.tag_line, kgfr.heading_1_id, kgfr.heading_2_id, kgfr.link_id, kgfr.img_id as image_id, cso1.name as mapper_obj_name, cso1.label as mapper_obj_label, cso2.name as mapping_obj_name, cso2.label as mapping_obj_label, csof1.name as mapper_field_name, csof1.label as mapper_field_label, csof2.name as mapping_field_name, csof2.label as mapping_field_label, csh1.name as heading_1_name, csh1.label as heading_1_label, csh2.name as heading_2_name, csh2.label as heading_2_label, csimage.name as image_name, sctco.base_href as mapping_obj_base_href 
                FROM k_graph_fields_relation kgfr
                LEFT JOIN content_source_objects cso1 ON cso1.id = kgfr.mapper_obj_id
                LEFT JOIN content_source_objects as cso2 ON kgfr.mapping_obj_id = cso2.id
                LEFT JOIN content_source_object_fields csof1 ON csof1.id = kgfr.mapper_field_id
                LEFT JOIN content_source_object_fields csof2 ON csof2.id = kgfr.mapping_field_id
                LEFT JOIN content_source_object_fields csh1 ON csh1.id = kgfr.heading_1_id
                LEFT JOIN content_source_object_fields csh2 ON csh2.id = kgfr.heading_2_id
                LEFT JOIN content_source_object_fields csimage ON csimage.id = kgfr.img_id
                LEFT JOIN knowledge_graph_relation kgr ON kgfr.relation_id = kgr.id
                LEFT JOIN search_clients_to_content_objects sctco ON sctco.search_client_id = kgr.search_client_id 
                WHERE sctco.content_source_object_id = kgfr.mapping_obj_id 
                AND kgr.search_client_id = ?`;
            connection[req.headers['tenant-id']].execute.query(sql, [platformId], function (err, data) {
                if (err) {
                    cb(null, []);
                }
                else {
                    cb(null, data);
                }
            })
        }],
        meta_data_relation: ['search_client_uid', (dataFromAbove, cb) => {
            const sql = ` SELECT mdr.id as id, mdr.name as name, mdr.search_client_id as search_client_id, sc.name as search_client_name, mdr.content_source_id as content_source_id , cs.label as content_source_label, cs.elasticIndexName as content_source_elasticIndexName, mdr.object_id, cso.label as object_label, cso.name as object_name, mdr.field_id, csof1.name as field_name, csof1.label as field_label, mdr.title_id, csof2.name as title_name, csof2.label as title_label, mdr.subtitle_id, csof3.name as subtitle_name, csof3.label as subtitle_label, mdr.link_id, csof4.name as link_name, csof4.label as link_label, mdr.description_id, csof5.name as description_name, csof5.label as description_label, mdr.img_id as image_id, csof6.name as image_name, csof2.label as image_label, mdr.layout as layout
                from meta_data_relation as mdr
                LEFT JOIN search_clients sc ON sc.id = mdr.search_client_id
                LEFT JOIN content_sources as cs ON mdr.content_source_id = cs.id
                LEFT JOIN content_source_objects cso ON cso.id = mdr.object_id
                LEFT JOIN content_source_object_fields csof1 ON csof1.id = mdr.field_id
                LEFT JOIN content_source_object_fields csof2 ON csof2.id = mdr.title_id
                LEFT JOIN content_source_object_fields csof3 ON csof3.id = mdr.subtitle_id
                LEFT JOIN content_source_object_fields csof4 ON csof4.id = mdr.link_id
                LEFT JOIN content_source_object_fields csof5 ON csof5.id = mdr.description_id
                LEFT JOIN content_source_object_fields csof6 ON csof6.id = mdr.img_id
                WHERE mdr.search_client_id = ? `;
            connection[req.headers['tenant-id']].execute.query(sql, [platformId], function (err, data) {
                if (err) {
                    cb(null, []);
                }
                else {
                    cb(null, data);
                }
            })
        }],
        meta_data_relation_fields: ['search_client_uid', (dataFromAbove, cb) => {
            var sql = `SELECT mdrf.id, mdrf.meta_data_relation_id as meta_data_graph_relation_id, mdrf.label as meta_data_label, mdrf.content_source_object_field_id, csof.name as content_source_object_field_name, csof.label as content_source_object_field_label
                FROM meta_data_relation_fields mdrf
                LEFT JOIN content_source_object_fields csof ON mdrf.content_source_object_field_id = csof.id
                LEFT JOIN meta_data_relation mdr ON mdr.id = mdrf.meta_data_relation_id
                WHERE mdr.search_client_id = ?`;
            connection[req.headers['tenant-id']].execute.query(sql, [platformId], function (err, data) {
                if (err) {
                    commonFunctions.errorlogger.error(err);
                    cb(null, []);
                }
                else {
                    cb(null, data);
                }
            })
        }],
        final: ['search_client_uid','knowledge_graph_relation','k_graph_fields_relation','meta_data_relation','meta_data_relation_fields',(dataFromAbove, cb) => {
            settings.relatedTiles = dataFromAbove.knowledge_graph_relation;
            settings.metaDataGraphs = dataFromAbove.meta_data_relation;
            settings.relatedTiles.forEach((tile) => {
                tile.relatedTilesFieldRelation =
                 dataFromAbove.k_graph_fields_relation.filter(fields => fields.relation_id == tile.id)
            });
            settings.metaDataGraphs.forEach((graph) => {
                graph.metaDataGraphMetaFields =
                 dataFromAbove.meta_data_relation_fields.filter(fields => fields.meta_data_graph_relation_id == graph.id)
            });
            settings.uid = dataFromAbove.search_client_uid[0].uid;
            if (settings.metaDataGraphs || settings.relatedTiles) {
                // kafka producer
                try{
                    kafkaLib.publishMessage({
                        topic:config.get("kafkaTopic.knowledgeGraphTopic"),
                        messages:[{
                            value:JSON.stringify(settings),
                            key: req.headers['tenant-id']
                        }]
                    })
                }catch(e){
                    console.log(e);
                }    
            }
            cb(null, settings);
        }]

    }, (error, result) => {
        if (error) {
            commonFunctions.errorlogger.error("error", error);
            callback(error, [])
        }
        else {
            callback(null, settings);
        }
    });
}

module.exports = {
    knowledgeGraphSettings: knowledgeGraphSettings,
}