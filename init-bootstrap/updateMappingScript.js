var path = require("path");
var fs = require("fs");
environment = require("../routes/environment");
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, "../config");
process.env.NODE_CONFIG_DIR = configPath;
config = require("config");
var async = require("async");
const redis = require("redis");
const redisclient = redis.createClient(
    config.get("redis.redisPort"),
    config.get("redis.redisHost")
);
const elastic = require("elasticsearch");
let request = require("request");

var esclient = new elastic.Client({
    host: `http://${config.get("elasticIndexCS.host")}:${config.get(
        "elasticIndexCS.port"
    )}`,
});

const connection_sql = require("../utils/connection");

const createIndex = ( originalIndexName, newIndexName, fieldName, bodyToSend, cb ) => {
    console.log("----inside create function----");
    esclient.indices.create({
        index: newIndexName,
        body: bodyToSend,
    }, function ( err, resp, respcode ) {   
        console.log( err, resp, respcode );
        console.log("---create function called----");
        let reIndexData = {
            "waitForCompletion": false,
            "refresh": true,
            "body": {
                "source": {
                    "index": originalIndexName,
                    "_source": [`${fieldName}`]
                },
                "dest": {
                    "index": newIndexName
                }
            }
        };
        esclient.reindex(reIndexData, function (err, resp, respcode) {
            console.log("---origin index name---", JSON.stringify(originalIndexName));
            console.log("re index pass data", JSON.stringify(reIndexData));
            console.log("new index name", JSON.stringify(newIndexName));
            if (err){
                console.log("----reindexing error----",err);

            }
            else{
                console.log("--resp---", JSON.stringify(resp));
                console.log("--respcode---", JSON.stringify(respcode));
                cb(err, resp, respcode)
            }
        })
    })
    
}

// const indexExistsCheck = ( newIndexName, bodyToSend, callback ) => {
//     esclient.indices.exists({
//         index: newIndexName }, function (err, res) {
//             console.log("index "+newIndexName+" exists "+res);
//             if (!res){
//                 callback(newIndexName, bodyToSend)
//             }

//         });
// }

const logsFolderExistCheck = async (file, uid) => {
  try {
    if (!fs.existsSync('logs')) {
      fs.mkdirSync('logs');
    }
  } catch (err) {
    console.error(err);
  }
}
const logPretty = (file, message, data) => {
    let loggerString = "";
    loggerString += message + "\n";
    loggerString += data + "\n";
    loggerString += "--------------------\n";
    fs.appendFileSync(file, loggerString);
};
const objectValue = {
    formula_field: {
        type: "text",
        fields: {
            en: {
                type: "text",
                analyzer: "custom_lowercase_stemmed",
                search_analyzer: "custom_lowercase_synonym",
            },
            copy: {
                type: "text",
                analyzer: "custom_lowercase_stemmed",
            },
            keyword: {
                type: "keyword",
                ignore_above: 256,
            },
            special: {
                type: "text",
                analyzer: "custom_special_analyzer",
            },
        },
    },
};

const getElasticMapping = (file, indexName, csObject, fieldName, cb) => {
    logPretty(file, "gk 13 index name to get elastic mapping ", indexName);
    esclient.indices.getMapping(
        {
            index: indexName,
            type: csObject,
        },
        function (error, result) {
            if (error) {
                logPretty(file, "ERROR", error);
            } else {
                const response = JSON.parse(JSON.stringify(result));
                const mainResponse = Object.keys(response);
                mainResponse.forEach((a) => {
                    const mappings = Object.keys(response[a].mappings);
                    mappings.forEach((x) => {
                        const properties = Object.keys(
                            response[a].mappings[x].properties
                        );
                        response[a].mappings[x].properties.formula_field =
                            objectValue.formula_field;
                        properties.forEach((item) => {
                            if (fieldName.includes(item)) {
                                if (
                                    response[a].mappings[x].properties[item] &&
                                    response[a].mappings[x].properties[item]
                                        .copy_to
                                ) {
                                    if (
                                        response[a].mappings[x].properties[
                                            item
                                        ].copy_to.includes("formula_field") ===
                                        false
                                    ) {
                                        response[a].mappings[x].properties[
                                            item
                                        ].copy_to.push("formula_field");
                                    }
                                }
                            }
                        });
                    });
                });
                cb(null, {
                    response: response,
                    indexName,
                    csObject,
                });
            }
        }
    );
};

const putFieldMapping = (file, indexName, csObject, response, cb) => {

    request(
        {
            method: "POST",
            url:
                "http://" +
                config.get("elasticIndexCS.host") +
                ":" +
                config.get("elasticIndexCS.port") +
                "/" +
                indexName +
                "/_mapping/" +
                csObject,
            headers: {
                "content-type": "application/json",
            },
            body: JSON.stringify(response[indexName].mappings[csObject]),
        },
        (error, resp) => {
            if (error) {
                logPretty(file, "ERROR", error);
            } else {
                logPretty(
                    file,
                    `Body Response And Update started for ${indexName}`,
                    resp.body
                );
            }
        }
    );
    cb(null, {
        indexName,
        csObject,
    });
};

const updateMapping = (file, indexName, csObject, cb) => {
    console.log("gk --> updated mapping called");
    let options = {
        url:
            "http://" +
            config.get("elasticIndexCS.host") +
            ":" +
            config.get("elasticIndexCS.port") +
            `/${indexName}/${csObject}/_update_by_query?requests_per_second=30&conflicts=proceed&refresh=wait_for`,
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
    };
    request(options, (err, result) => {
        if (err) {
            logPretty(file, "ERROR", err);
        } else {
            const parsedResult = JSON.parse(result.body);
            logPretty(
                file,
                `Total Documents for ${indexName}`,
                parsedResult.total
            );
            logPretty(
                file,
                `Updated Documents for ${indexName}`,
                parsedResult.updated
            );
        }
        cb(null);
    });
};

const autoSuggest = async (file, uid, callback) => {
    if( config.get("autoSuggestMapping")){
      console.log("---iinside if----");
      async.auto({
        fetchContentSources: [
            function (cb) {
                const query = "SELECT id from search_clients WHERE uid = ?";
                connection.query(query, [uid], (err, result) => {
                    if (err) {
                        console.log(err);
                        cb(err, null);
                    } else {
                        cb(null, result);
                    }
                });
            },
        ],
        one: [
            "fetchContentSources",
            function (dataFromAbove, cb) {
                const temp = [];
                dataFromAbove.fetchContentSources.map((item) => {
                    temp.push(item.id);
                });
                const query =
                    "SELECT title_field_id, content_source_object_id FROM search_clients_to_content_objects WHERE search_client_id IN (?)";
                connection.query(query, [temp], (err, result) => {
                    if (err) {
                        cb(err, null);
                    } else {
                        cb(null, result);
                    }
                });
            },
        ],
        two: [
            "one",
            function (dataFromAbove, cb) {
                const temp = [];
                dataFromAbove.one.map((item) => {
                    temp.push(item.title_field_id);
                });
                const query =
                    "SELECT name, content_source_object_id FROM content_source_object_fields where id IN (?)";
                connection.query(query, [temp], (err, result) => {
                    if (err) {
                        console.log(err);
                        cb(err, null);
                    } else {
                        cb(null, result);
                    }
                });
            },
        ],
        three: [
            "one",
            function (dataFromAbove, cb) {
                const temp = [];
                dataFromAbove.one.map((item) => {
                    temp.push(item.content_source_object_id);
                });
                const query =
                    "SELECT id, name, content_source_id FROM content_source_objects where id IN (?)";
                connection.query(query, [temp], (err, result) => {
                    if (err) {
                        console.log(err);
                        cb(err, null);
                    } else {
                        cb(null, result);
                    }
                });
            },
        ],
        four: [
            "three",
            function(dataFromAbove, cb) {
                const temp = [];
                dataFromAbove.three.map((item) => {
                    temp.push(item.content_source_id)
                })
                const query =
                    "SELECT id, elasticIndexName FROM content_sources where id IN (?)";
                connection.query(query, [temp], (err, result) => {
                    if (err) {
                        console.log(err);
                        cb(err, null);
                    } else {
                        cb(null, result);
                    }
                });
            }
        ],
        five: [
            "two",
            "three",
            "four",
            function (dataFromAbove, cb){
                var temp = [];
                dataFromAbove.two.map((item) =>{
                    var currentObject = dataFromAbove.three.find(element => element.id === item.content_source_object_id)
                    var contentSourceObject = dataFromAbove.four.find(element => element.id === currentObject.content_source_id)
                    var itemNameCopy = `${item.name}_copy`;
                    var itemNameSpecial = `${item.name}_special`;
                    var staticMappingContent = {
                        "type": "text",
                        "fields": {
                            "en": {
                                "type": "text",
                                "analyzer": "custom_lowercase_stemmed",
                                "search_analyzer": "custom_lowercase_synonym"
                            },
                            "keyword": {
                            "type": "keyword",
                            "ignore_above": 256
                            }
                        }
                    };
                    var staticMappingContentType = JSON.parse(JSON.stringify(staticMappingContent))
                    staticMappingContentType.copy_to = ["title_copy", "title_special"]
                    var staticMappingContentCopy = JSON.parse(JSON.stringify(staticMappingContent))
                    delete staticMappingContentCopy.fields.en.search_analyzer
                    var staticMappingContentSpecial = JSON.parse(JSON.stringify(staticMappingContentCopy));
                    staticMappingContentSpecial.fields.en.analyzer = "custom_special_analyzer";
                    var objMapping = {
                        "response" : {
                            [contentSourceObject.elasticIndexName] : {
                                "mappings": {
                                    [currentObject.name]: {
                                        "properties": {
                                            [item.name] : staticMappingContentType,
                                            [itemNameCopy]: staticMappingContentCopy,
                                            [itemNameSpecial]: staticMappingContentSpecial
                                        }
                                    }
                                }
                            }
                        }
                    }
                    temp.push(objMapping);
                
                });
                console.log("-----temp final ----");
                cb(null, temp);
            }
        ],
        six: [
            "five",
             function (dataFromAbove, cb){
                var indexInfo = []
                dataFromAbove.five.map((item) => {
                    let originalIndexName = Object.keys(item.response)[0];
                    let newIndexName = `auto_suggest_${originalIndexName}`
                    esclient.indices.exists({
                        index: newIndexName 
                    }, function (err, res) {
                            item.indexExist = res;
                            if(!res){
                            }
                            indexInfo.push(item);
                            if(indexInfo.length === dataFromAbove.five.length){
                                cb(null, indexInfo)
                            };
                        })
                })
            }
        ],
        seven: [
            "six",
            async function(dataFromAbove, cb){
                let jobs = [];
                dataFromAbove.six.map((item)=> {
                    let originalIndexName = Object.keys(item.response)[0];
                    const indexSettings = {
                        "index": {
                          "mapping": {
                            "total_fields": {
                              "limit": "1000"
                            }
                          },
                          "number_of_shards": "5",
                          "analysis": {
                            "filter": {
                              "french_stop": {
                                "type": "stop",
                                "stopwords": "_french_"
                              },
                              "irish_stop": {
                                "type": "stop",
                                "stopwords": "_irish_"
                              },
                              "arabic_keywords": {
                                "keywords": [
                                  "مثال"
                                ],
                                "type": "keyword_marker"
                              },
                              "italian_stop": {
                                "type": "stop",
                                "stopwords": "_italian_"
                              },
                              "armenian_stop": {
                                "type": "stop",
                                "stopwords": "_armenian_"
                              },
                              "bulgarian_stemmer": {
                                "type": "stemmer",
                                "language": "bulgarian"
                              },
                              "czech_keywords": {
                                "keywords": [
                                  "příklad"
                                ],
                                "type": "keyword_marker"
                              },
                              "galician_stemmer": {
                                "type": "stemmer",
                                "language": "galician"
                              },
                              "thai_stop": {
                                "type": "stop",
                                "stopwords": "_thai_"
                              },
                              "czech_stemmer": {
                                "type": "stemmer",
                                "language": "czech"
                              },
                              "sorani_keywords": {
                                "keywords": [
                                  "mînak"
                                ],
                                "type": "keyword_marker"
                              },
                              "danish_stemmer": {
                                "type": "stemmer",
                                "language": "danish"
                              },
                              "turkish_stop": {
                                "type": "stop",
                                "stopwords": "_turkish_"
                              },
                              "basque_keywords": {
                                "keywords": [
                                  "Adibidez"
                                ],
                                "type": "keyword_marker"
                              },
                              "german_stop": {
                                "type": "stop",
                                "stopwords": "_german_"
                              },
                              "greek_keywords": {
                                "keywords": [
                                  "παράδειγμα"
                                ],
                                "type": "keyword_marker"
                              },
                              "swedish_stop": {
                                "type": "stop",
                                "stopwords": "_swedish_"
                              },
                              "brazilian_stemmer": {
                                "type": "stemmer",
                                "language": "brazilian"
                              },
                              "galician_keywords": {
                                "keywords": [
                                  "exemplo"
                                ],
                                "type": "keyword_marker"
                              },
                              "irish_keywords": {
                                "keywords": [
                                  "sampla"
                                ],
                                "type": "keyword_marker"
                              },
                              "my_custom_stop": {
                                "type": "stop",
                                "stopwords_path": "nlp/micro_stopwords.txt"
                              },
                              "indonesian_stemmer": {
                                "type": "stemmer",
                                "language": "indonesian"
                              },
                              "spanish_stop": {
                                "type": "stop",
                                "stopwords": "_spanish_"
                              },
                              "portuguese_stop": {
                                "type": "stop",
                                "stopwords": "_portuguese_"
                              },
                              "turkish_keywords": {
                                "keywords": [
                                  "örnek"
                                ],
                                "type": "keyword_marker"
                              },
                              "russian_stemmer": {
                                "type": "stemmer",
                                "language": "russian"
                              },
                              "greek_lowercase": {
                                "type": "lowercase",
                                "language": "greek"
                              },
                              "russian_stop": {
                                "type": "stop",
                                "stopwords": "_russian_"
                              },
                              "russian_keywords": {
                                "keywords": [
                                  "пример"
                                ],
                                "type": "keyword_marker"
                              },
                              "catalan_keywords": {
                                "keywords": [
                                  "exemple"
                                ],
                                "type": "keyword_marker"
                              },
                              "finnish_keywords": {
                                "keywords": [
                                  "esimerkki"
                                ],
                                "type": "keyword_marker"
                              },
                              "portuguese_keywords": {
                                "keywords": [
                                  "exemplo"
                                ],
                                "type": "keyword_marker"
                              },
                              "french_stemmer": {
                                "type": "stemmer",
                                "language": "light_french"
                              },
                              "romanian_keywords": {
                                "keywords": [
                                  "exemplu"
                                ],
                                "type": "keyword_marker"
                              },
                              "sorani_stemmer": {
                                "type": "stemmer",
                                "language": "sorani"
                              },
                              "dutch_stemmer": {
                                "type": "stemmer",
                                "language": "dutch"
                              },
                              "french_keywords": {
                                "keywords": [
                                  "Exemple"
                                ],
                                "type": "keyword_marker"
                              },
                              "italian_keywords": {
                                "keywords": [
                                  "esempio"
                                ],
                                "type": "keyword_marker"
                              },
                              "dutch_keywords": {
                                "keywords": [
                                  "voorbeeld"
                                ],
                                "type": "keyword_marker"
                              },
                              "ja_stop": {
                                "type": "ja_stop",
                                "stopwords": [
                                  "_japanese_",
                                  "ストップ"
                                ]
                              },
                              "catalan_stop": {
                                "type": "stop",
                                "stopwords": "_catalan_"
                              },
                              "czech_stop": {
                                "type": "stop",
                                "stopwords": "_czech_"
                              },
                              "finnish_stemmer": {
                                "type": "stemmer",
                                "language": "finnish"
                              },
                              "hindi_keywords": {
                                "keywords": [
                                  "उदाहरण"
                                ],
                                "type": "keyword_marker"
                              },
                              "bulgarian_stop": {
                                "type": "stop",
                                "stopwords": "_bulgarian_"
                              },
                              "irish_stemmer": {
                                "type": "stemmer",
                                "language": "irish"
                              },
                              "swedish_stemmer": {
                                "type": "stemmer",
                                "language": "swedish"
                              },
                              "french_elision": {
                                "type": "elision",
                                "articles": [
                                  "l",
                                  "m",
                                  "t",
                                  "qu",
                                  "n",
                                  "s",
                                  "j",
                                  "d",
                                  "c",
                                  "jusqu",
                                  "quoiqu",
                                  "lorsqu",
                                  "puisqu"
                                ],
                                "articles_case": "true"
                              },
                              "my_stop": {
                                "type": "stop",
                                "stopwords": "_english_"
                              },
                              "dutch_stop": {
                                "type": "stop",
                                "stopwords": "_dutch_"
                              },
                              "armenian_keywords": {
                                "keywords": [
                                  "օրինակ"
                                ],
                                "type": "keyword_marker"
                              },
                              "armenian_stemmer": {
                                "type": "stemmer",
                                "language": "armenian"
                              },
                              "hindi_stemmer": {
                                "type": "stemmer",
                                "language": "hindi"
                              },
                              "spanish_keywords": {
                                "keywords": [
                                  "ejemplo"
                                ],
                                "type": "keyword_marker"
                              },
                              "german_keywords": {
                                "keywords": [
                                  "Beispiel"
                                ],
                                "type": "keyword_marker"
                              },
                              "indonesian_stop": {
                                "type": "stop",
                                "stopwords": "_indonesian_"
                              },
                              "hungarian_keywords": {
                                "keywords": [
                                  "példa"
                                ],
                                "type": "keyword_marker"
                              },
                              "norwegian_stemmer": {
                                "type": "stemmer",
                                "language": "norwegian"
                              },
                              "romanian_stemmer": {
                                "type": "stemmer",
                                "language": "romanian"
                              },
                              "basque_stemmer": {
                                "type": "stemmer",
                                "language": "basque"
                              },
                              "basque_stop": {
                                "type": "stop",
                                "stopwords": "_basque_"
                              },
                              "danish_keywords": {
                                "keywords": [
                                  "eksempel"
                                ],
                                "type": "keyword_marker"
                              },
                              "irish_elision": {
                                "type": "elision",
                                "articles": [
                                  "d",
                                  "m",
                                  "b"
                                ],
                                "articles_case": "true"
                              },
                              "synonym": {
                                "type": "synonym_graph",
                                "synonyms_path": "nlp/micro_synonyms.txt"
                              },
                              "italian_elision": {
                                "type": "elision",
                                "articles": [
                                  "c",
                                  "l",
                                  "all",
                                  "dall",
                                  "dell",
                                  "nell",
                                  "sull",
                                  "coll",
                                  "pell",
                                  "gl",
                                  "agl",
                                  "dagl",
                                  "degl",
                                  "negl",
                                  "sugl",
                                  "un",
                                  "m",
                                  "t",
                                  "s",
                                  "v",
                                  "d"
                                ],
                                "articles_case": "true"
                              },
                              "custom_english_stemmer": {
                                "name": "english",
                                "type": "stemmer"
                              },
                              "greek_stop": {
                                "type": "stop",
                                "stopwords": "_greek_"
                              },
                              "catalan_stemmer": {
                                "type": "stemmer",
                                "language": "catalan"
                              },
                              "finnish_stop": {
                                "type": "stop",
                                "stopwords": "_finnish_"
                              },
                              "hungarian_stemmer": {
                                "type": "stemmer",
                                "language": "hungarian"
                              },
                              "arabic_stemmer": {
                                "type": "stemmer",
                                "language": "arabic"
                              },
                              "hungarian_stop": {
                                "type": "stop",
                                "stopwords": "_hungarian_"
                              },
                              "turkish_stemmer": {
                                "type": "stemmer",
                                "language": "turkish"
                              },
                              "greek_stemmer": {
                                "type": "stemmer",
                                "language": "greek"
                              },
                              "galician_stop": {
                                "type": "stop",
                                "stopwords": "_galician_"
                              },
                              "turkish_lowercase": {
                                "type": "lowercase",
                                "language": "turkish"
                              },
                              "brazilian_keywords": {
                                "keywords": [
                                  "exemplo"
                                ],
                                "type": "keyword_marker"
                              },
                              "bulgarian_keywords": {
                                "keywords": [
                                  "пример"
                                ],
                                "type": "keyword_marker"
                              },
                              "indonesian_keywords": {
                                "keywords": [
                                  "contoh"
                                ],
                                "type": "keyword_marker"
                              },
                              "irish_lowercase": {
                                "type": "lowercase",
                                "language": "irish"
                              },
                              "german_stemmer": {
                                "type": "stemmer",
                                "language": "light_german"
                              },
                              "portuguese_stemmer": {
                                "type": "stemmer",
                                "language": "light_portuguese"
                              },
                              "my_katakana_stemmer": {
                                "type": "kuromoji_stemmer",
                                "minimum_length": "4"
                              },
                              "catalan_elision": {
                                "type": "elision",
                                "articles": [
                                  "d",
                                  "l",
                                  "m",
                                  "n",
                                  "s",
                                  "t"
                                ],
                                "articles_case": "true"
                              },
                              "romanian_stop": {
                                "type": "stop",
                                "stopwords": "_romanian_"
                              },
                              "sorani_stop": {
                                "type": "stop",
                                "stopwords": "_sorani_"
                              },
                              "norwegian_keywords": {
                                "keywords": [
                                  "eksempel"
                                ],
                                "type": "keyword_marker"
                              },
                              "norwegian_stop": {
                                "type": "stop",
                                "stopwords": "_norwegian_"
                              },
                              "danish_stop": {
                                "type": "stop",
                                "stopwords": "_danish_"
                              },
                              "arabic_stop": {
                                "type": "stop",
                                "stopwords": "_arabic_"
                              },
                              "swedish_keywords": {
                                "keywords": [
                                  "exempel"
                                ],
                                "type": "keyword_marker"
                              },
                              "hindi_stop": {
                                "type": "stop",
                                "stopwords": "_hindi_"
                              },
                              "spanish_stemmer": {
                                "type": "stemmer",
                                "language": "light_spanish"
                              },
                              "italian_stemmer": {
                                "type": "stemmer",
                                "language": "light_italian"
                              },
                              "brazilian_stop": {
                                "type": "stop",
                                "stopwords": "_brazilian_"
                              }
                            },
                            "char_filter": {
                              "special_mapping": {
                                "type": "mapping",
                                "mappings": [
                                  "&=>\\u0020scampersand\\u0020",
                                  "@=>\\u0020scasperand\\u0020",
                                  ">=>\\u0020scgreaterthan\\u0020",
                                  "<=>\\u0020sclessthan\\u0020",
                                  "$=>\\u0020scdollar\\u0020",
                                  "%=>\\u0020scpercentage\\u0020",
                                  "^=>\\u0020sccaret\\u0020",
                                  "==>\\u0020scequalto\\u0020",
                                  ":=>\\u0020sccolon\\u0020",
                                  ".=>\\u0020scfullstop\\u0020",
                                  "/=>\\u0020scslash\\u0020 ",
                                  "-=>\\u0020schyphen\\u0020",
                                  "+=>\\u0020scplus\\u0020",
                                  "*=>\\u0020scasterisk\\u0020"
                                ]
                              }
                            },
                            "normalizer": {
                              "custom_normalizer": {
                                "filter": [
                                  "lowercase",
                                  "asciifolding"
                                ],
                                "type": "custom"
                              }
                            },
                            "analyzer": {
                              "custom_tr_analyzer_stemmed": {
                                "filter": [
                                  "apostrophe",
                                  "turkish_lowercase",
                                  "turkish_stop",
                                  "my_custom_stop",
                                  "turkish_keywords",
                                  "turkish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_da_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "danish_stop",
                                  "my_custom_stop",
                                  "danish_keywords",
                                  "synonym",
                                  "danish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_eu_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "basque_stop",
                                  "my_custom_stop",
                                  "basque_keywords",
                                  "synonym",
                                  "basque_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ga_analyzer_stemmed": {
                                "filter": [
                                  "irish_elision",
                                  "irish_lowercase",
                                  "irish_stop",
                                  "my_custom_stop",
                                  "irish_keywords",
                                  "irish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_cs_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "czech_stop",
                                  "my_custom_stop",
                                  "czech_keywords",
                                  "czech_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_th_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "decimal_digit",
                                  "thai_stop",
                                  "my_custom_stop"
                                ],
                                "tokenizer": "thai"
                              },
                              "custom_ga_analyzer": {
                                "filter": [
                                  "irish_elision",
                                  "irish_lowercase",
                                  "irish_stop",
                                  "my_custom_stop",
                                  "irish_keywords",
                                  "synonym",
                                  "irish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_hy_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "armenian_stop",
                                  "my_custom_stop",
                                  "armenian_keywords",
                                  "synonym",
                                  "armenian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_gl_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "galician_stop",
                                  "my_custom_stop",
                                  "galician_keywords",
                                  "galician_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_el_analyzer": {
                                "filter": [
                                  "greek_lowercase",
                                  "greek_stop",
                                  "my_custom_stop",
                                  "greek_keywords",
                                  "synonym",
                                  "greek_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_special_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "my_stop",
                                  "my_custom_stop",
                                  "custom_english_stemmer",
                                  "asciifolding"
                                ],
                                "char_filter": [
                                  "special_mapping"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_fi_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "finnish_stop",
                                  "my_custom_stop",
                                  "finnish_keywords",
                                  "synonym",
                                  "finnish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ca_analyzer_stemmed": {
                                "filter": [
                                  "catalan_elision",
                                  "lowercase",
                                  "catalan_stop",
                                  "my_custom_stop",
                                  "catalan_keywords",
                                  "catalan_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_it_analyzer": {
                                "filter": [
                                  "italian_elision",
                                  "lowercase",
                                  "italian_stop",
                                  "my_custom_stop",
                                  "italian_keywords",
                                  "synonym",
                                  "italian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_es_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "spanish_stop",
                                  "my_custom_stop",
                                  "spanish_keywords",
                                  "synonym",
                                  "spanish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ro_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "romanian_stop",
                                  "my_custom_stop",
                                  "romanian_keywords",
                                  "romanian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_el_analyzer_stemmed": {
                                "filter": [
                                  "greek_lowercase",
                                  "greek_stop",
                                  "my_custom_stop",
                                  "greek_keywords",
                                  "greek_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ar_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "arabic_stop",
                                  "my_custom_stop",
                                  "arabic_keywords",
                                  "arabic_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_hu_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "hungarian_stop",
                                  "my_custom_stop",
                                  "hungarian_keywords",
                                  "hungarian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ar_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "arabic_stop",
                                  "my_custom_stop",
                                  "synonym",
                                  "arabic_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_da_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "danish_stop",
                                  "my_custom_stop",
                                  "danish_keywords",
                                  "danish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_pt_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "portuguese_stop",
                                  "my_custom_stop",
                                  "portuguese_keywords",
                                  "synonym",
                                  "portuguese_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_fr_analyzer_stemmed": {
                                "filter": [
                                  "french_elision",
                                  "lowercase",
                                  "french_stop",
                                  "my_custom_stop",
                                  "french_keywords",
                                  "french_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_nn_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "norwegian_stop",
                                  "my_custom_stop",
                                  "norwegian_keywords",
                                  "norwegian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ja_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "ja_stop",
                                  "my_custom_stop",
                                  "my_katakana_stemmer"
                                ],
                                "type": "custom",
                                "tokenizer": "kuromoji_user_dict"
                              },
                              "custom_gl_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "galician_stop",
                                  "my_custom_stop",
                                  "galician_keywords",
                                  "synonym",
                                  "galician_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_hy_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "armenian_stop",
                                  "my_custom_stop",
                                  "armenian_keywords",
                                  "armenian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ru_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "russian_stop",
                                  "my_custom_stop",
                                  "russian_keywords",
                                  "russian_stemmer",
                                  "synonym"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_lowercase_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "my_stop",
                                  "my_custom_stop",
                                  "custom_english_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_lowercase_synonym": {
                                "filter": [
                                  "lowercase",
                                  "my_stop",
                                  "my_custom_stop",
                                  "synonym",
                                  "custom_english_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_pt_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "portuguese_stop",
                                  "my_custom_stop",
                                  "portuguese_keywords",
                                  "portuguese_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_id_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "indonesian_stop",
                                  "my_custom_stop",
                                  "indonesian_keywords",
                                  "synonym",
                                  "indonesian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_nl_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "dutch_stop",
                                  "my_custom_stop",
                                  "dutch_keywords",
                                  "synonym",
                                  "dutch_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ku_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "sorani_stop",
                                  "my_custom_stop",
                                  "sorani_keywords",
                                  "sorani_stemmer",
                                  "synonym"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_pt-br_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "brazilian_stop",
                                  "my_custom_stop",
                                  "brazilian_keywords",
                                  "synonym",
                                  "brazilian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_de_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "german_stop",
                                  "my_custom_stop",
                                  "german_keywords",
                                  "german_normalization",
                                  "synonym",
                                  "german_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_fi_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "finnish_stop",
                                  "my_custom_stop",
                                  "finnish_keywords",
                                  "finnish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_es_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "spanish_stop",
                                  "my_custom_stop",
                                  "spanish_keywords",
                                  "spanish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_pt-br_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "brazilian_stop",
                                  "my_custom_stop",
                                  "brazilian_keywords",
                                  "brazilian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ja_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "ja_stop",
                                  "my_custom_stop",
                                  "synonym",
                                  "my_katakana_stemmer"
                                ],
                                "type": "custom",
                                "tokenizer": "kuromoji_user_dict"
                              },
                              "custom_bg_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "bulgarian_stop",
                                  "my_custom_stop",
                                  "bulgarian_keywords",
                                  "synonym",
                                  "bulgarian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_it_analyzer_stemmed": {
                                "filter": [
                                  "italian_elision",
                                  "lowercase",
                                  "italian_stop",
                                  "my_custom_stop",
                                  "italian_keywords",
                                  "italian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_fr_analyzer": {
                                "filter": [
                                  "french_elision",
                                  "lowercase",
                                  "french_stop",
                                  "my_custom_stop",
                                  "french_keywords",
                                  "synonym",
                                  "french_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ca_analyzer": {
                                "filter": [
                                  "catalan_elision",
                                  "lowercase",
                                  "catalan_stop",
                                  "my_custom_stop",
                                  "catalan_keywords",
                                  "synonym",
                                  "catalan_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_cs_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "czech_stop",
                                  "my_custom_stop",
                                  "czech_keywords",
                                  "synonym",
                                  "czech_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_sv_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "swedish_stop",
                                  "my_custom_stop",
                                  "swedish_keywords",
                                  "swedish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_hu_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "hungarian_stop",
                                  "my_custom_stop",
                                  "hungarian_keywords",
                                  "synonym",
                                  "hungarian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ku_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "sorani_stop",
                                  "my_custom_stop",
                                  "sorani_keywords",
                                  "sorani_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_nl_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "dutch_stop",
                                  "my_custom_stop",
                                  "dutch_keywords",
                                  "dutch_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_bg_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "bulgarian_stop",
                                  "my_custom_stop",
                                  "bulgarian_keywords",
                                  "bulgarian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_de_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "german_stop",
                                  "my_custom_stop",
                                  "german_keywords",
                                  "german_normalization",
                                  "german_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_hi_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "hindi_keywords",
                                  "hindi_stop",
                                  "my_custom_stop",
                                  "synonym",
                                  "hindi_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_th_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "decimal_digit",
                                  "thai_stop",
                                  "my_custom_stop",
                                  "synonym"
                                ],
                                "tokenizer": "thai"
                              },
                              "custom_eu_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "basque_stop",
                                  "my_custom_stop",
                                  "basque_keywords",
                                  "basque_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_id_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "indonesian_stop",
                                  "my_custom_stop",
                                  "indonesian_keywords",
                                  "indonesian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_tr_analyzer": {
                                "filter": [
                                  "apostrophe",
                                  "turkish_lowercase",
                                  "turkish_stop",
                                  "my_custom_stop",
                                  "turkish_keywords",
                                  "synonym",
                                  "turkish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ru_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "russian_stop",
                                  "my_custom_stop",
                                  "russian_keywords",
                                  "russian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_sv_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "swedish_stop",
                                  "my_custom_stop",
                                  "swedish_keywords",
                                  "synonym",
                                  "swedish_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_hi_analyzer_stemmed": {
                                "filter": [
                                  "lowercase",
                                  "hindi_keywords",
                                  "hindi_stop",
                                  "my_custom_stop",
                                  "hindi_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_nn_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "norwegian_stop",
                                  "my_custom_stop",
                                  "norwegian_keywords",
                                  "synonym",
                                  "norwegian_stemmer"
                                ],
                                "tokenizer": "standard"
                              },
                              "custom_ro_analyzer": {
                                "filter": [
                                  "lowercase",
                                  "romanian_stop",
                                  "my_custom_stop",
                                  "romanian_keywords",
                                  "synonym",
                                  "romanian_stemmer"
                                ],
                                "tokenizer": "standard"
                              }
                            },
                            "tokenizer": {
                              "kuromoji_user_dict": {
                                "mode": "search",
                                "type": "kuromoji_tokenizer",
                                "discard_punctuation": "false"
                              }
                            }
                          },
                          "number_of_replicas": "1"
                        }
                      }
                    let newIndexName = `auto_suggest_${originalIndexName}`
                    let bodyToSend = item.response[originalIndexName];
                    bodyToSend.settings = indexSettings;
                    let objectName = Object.keys(item.response[originalIndexName].mappings)[0];
                    let fieldName = Object.keys(item.response[originalIndexName].mappings[objectName].properties)[0];
                    if (!item.indexExist){
                        jobs.push(
                            createIndex.bind(
                                null,
                                originalIndexName,
                                newIndexName,
                                fieldName,
                                bodyToSend
                            )
                        )
                    }
                });
                async.series(jobs, (err, results) => {
                    if (err) {
                        cb(err);
                    } else {
                        console.log("results from bind function----", JSON.stringify(results));
                    }
                });
                
            }
        ]
    },
        function (err, _r) {
            if (err) {
                callback(err);
            }
            callback(null);
        }
    );
    }
    else{
      console.log("---iinside else----");
      callback(null)
    }
    
}

const mainFunction = async (file, uid, callback) => {
    await logsFolderExistCheck(file, uid);
    await autoSuggest(file, uid, callback);
    const fieldName = [];
    logPretty(file, "Search Client UID - ", uid);
    async.auto(
        {
            fetchContentSources: [
                function (cb) {
                    const query = "SELECT id from search_clients WHERE uid = ?";
                    connection.query(query, [uid], (err, result) => {
                        if (err) {
                            console.log(err);
                            cb(err);
                        } else {
                            cb(null, result);
                        }
                    });
                },
            ],
            one: [
                "fetchContentSources",
                function (dataFromAbove, cb) {
                    const temp = [];
                    dataFromAbove.fetchContentSources.map((item) => {
                        logPretty(file, "gurkirat content sources temp -", JSON.stringify(item))
                        temp.push(item.id);
                    });
                    const query =
                        "SELECT content_source_object_id, id FROM search_clients_to_content_objects WHERE search_client_id IN (?)";
                    connection.query(query, [temp], (err, result) => {
                        if (err) {
                            cb(err);
                        } else {
                            cb(null, result);
                        }
                    });
                },
            ],
            two: [
                "one",
                function (dataFromAbove, cb) {
                    const temp = [];
                    dataFromAbove.one.map((item) => {
                        temp.push(item.content_source_object_id);
                    });
                    const query =
                        "SELECT cso.content_source_id AS content_source_id FROM content_source_objects AS cso where cso.id IN (?)";
                    connection.query(query, [temp], (err, result) => {
                        if (err) {
                            console.log(err);
                            cb(err);
                        } else {
                            cb(null, result);
                        }
                    });
                },
            ],
            three: [
                "two",
                function (dataFromAbove, cb) {
                    const temp = [];
                    dataFromAbove.two.map((item) => {
                        temp.push(item.content_source_id);
                    });
                    const query =
                        "SELECT * from content_sources as cs where cs.id IN (?)";
                    connection.query(query, [temp], (err, result) => {
                        if (err) {
                            console.log(err);
                            cb(err);
                        } else {
                            cb(null, result);
                        }
                    });
                },
            ],
            four: [
                "three",
                function (dataFromAbove, cb) {
                    const temp = [];
                    dataFromAbove.three.map((item) => {
                        temp.push(item.id);
                    });
                    const csobjQuery =
                        "SELECT name, content_source_id  FROM content_source_objects WHERE content_source_id IN (?) ";
                    connection.query(csobjQuery, [temp], (err, result) => {
                        if (err) {
                            console.log(err);
                        } else {
                            cb(null, result);
                        }
                    });
                },
            ],
            five: [
                "one",
                function (dataFromAbove, cb) {
                    const temp = [];
                    dataFromAbove.one.map((item) => {
                        temp.push(item.id);
                    });
                    const fieldQuery =
                        "SELECT scf.content_source_object_field_id AS scf_content_source_object_field_id FROM search_clients_filters AS scf WHERE (scf.use_as= 'Search' OR scf.use_as = 'SearchSummary' OR scf.use_as= 'SearchFilter' OR scf.use_as= 'Tag') AND scf.search_clients_to_content_object_id IN (?) ";
                    connection.query(fieldQuery, [temp], (err, result) => {
                        if (err) {
                            console.log(err);
                        } else {
                            cb(null, result);
                        }
                    });
                },
            ],
            six: [
                "five",
                function (dataFromAbove, cb) {
                    const temp = [];
                    dataFromAbove.five.map((item) => {
                        temp.push(item.scf_content_source_object_field_id);
                    });
                    const lableQuery =
                        "SELECT name FROM content_source_object_fields WHERE id IN (?) ";
                    connection.query(lableQuery, [temp], (err, result) => {
                        if (err) {
                            console.log(err);
                        } else {
                            cb(null, result);
                        }
                    });
                },
            ],
            seven: [
                "six",
                function (dataFromAbove, cb) {
                    dataFromAbove.six.map((item) => {
                        fieldName.push(item.name);
                    });
                    cb(null, fieldName);
                },
            ],
            getMapping: [
                "three",
                "four",
                "seven",
                function (dataFromAbove, cb) {
                    const jobs = [];
                    dataFromAbove.four.map((val) => {
                        dataFromAbove.three.map((val1) => {
                            console.log("gk -> val 1 id" +val1.id + " val content_source_id "+val.content_source_id);
                            if (val1.id === val.content_source_id) {
                                console.log("gk --> content sources data and contentSource objejct data "+JSON.stringify(val1)+" "+JSON.stringify(val));
                                jobs.push(
                                    getElasticMapping.bind(
                                        null,
                                        file,
                                        val1.elasticIndexName,
                                        val.name,
                                        dataFromAbove.seven
                                    )
                                );
                            }
                        });
                    });
                    async.series(jobs, (err, results) => {
                        if (err) {
                            cb(err);
                        } else {
                            cb(null, results);
                        }
                    });
                },
            ],
            putMapping: [
                "getMapping",
                function (dataFromAbove, cb) {
                    const jobs = [];
                    dataFromAbove.getMapping.map((val, i) => {
                        console.log("gk 13 inside put mapping index -->"+i);
                        jobs.push(
                            putFieldMapping.bind(
                                null,
                                file,
                                val.indexName,
                                val.csObject,
                                val.response
                            )
                        );
                    });
                    async.series(jobs, (err, results) => {
                        if (err) {
                            cb(err);
                        } else {
                            console.log("gk --> put mapping jobs log "+JSON.stringify(jobs)+" result length "+jobs.length);
                            logPretty(
                                file,
                                "Wait for the update process to complete",
                                ""
                            );
                            cb(null, results);
                        }
                    });
                },
            ],
            updateMap: [
                "putMapping",
                function (dataFromAbove, cb) {
                    const jobs = [];
                    dataFromAbove.putMapping.map((val, i) => {
                        console.log("gk 13 inside update mapping---"+JSON.stringify(val)+" index "+i);
                        jobs.push(
                            updateMapping.bind(
                                null,
                                file,
                                val.indexName,
                                val.csObject
                            )
                        );
                    });
                    async.series(jobs, (err, results) => {
                        if (err) {
                            cb(err);
                        } else {
                            logPretty(
                                file,
                                "UPDATE OF MAPPING IS COMPLETED",
                                ""
                            );
                        }
                    });
                },
            ],
        },
        function (err, _r) {
            if (err) {
                callback(err);
            }
            callback(null);
        }
    );
};

module.exports = function (job) {
    return new Promise(function (resolve, reject) {
        connection_sql
            .handleDisconnect()
            .then(() => {
                mainFunction(job.data.file, job.data.uid, (err, _r) => {
                    if (err) {
                        reject(err);
                    }
                    resolve();
                });
            })
            .catch((err) => {
                reject(err);
            });
    });
};
