var commonFunctions = require('./../../utils/commonFunctions');

const requestTofetchObjectFields = function (authorization, objectName,req,  callback) {
    var customFieldArray = [];
    var flag = 7;

    commonFunctions.getContentSourceObjectsAndFieldsById(authorization.content_source_id,req,  function (err, res) {
        if (err) {
            callback(err, null)
        }
        else {
            var standardObj = commonFunctions.metadata.loadMappingForDbAndElastic("20").objects;
            var fieldsArr = [];

            for (var i = 0; i < standardObj.length; i++) {
                if (objectName == standardObj[i].name) {
                    flag = 0;
                    fieldsArr = commonFunctions.metadata.loadMappingForDbAndElastic("20").fields.concat(standardObj[i].fields);
                    break;
                }
            }
            var listToAdd = [];
            var listInt = -1;
            for (var type = 0; type < res.length; type++) {
                if (objectName == res[type].name) {
                    listInt = type;
                }
            }
            if (listInt != -1) {
                type = listInt;
                listToAdd = res[type].fields;
                for (var mn = 0; mn < fieldsArr.length; mn++) {
                    var found = 0;
                    for (var field = 0; field < res[type].fields.length; field++) {
                        if (fieldsArr[mn].name == res[type].fields[field].name) {
                            found = 1;
                            break;
                        }
                    }
                    if (found == 0) {
                        listToAdd.push(fieldsArr[mn]);
                    }
                }
                for (var field = 0; field < listToAdd.length; field++) {

                    customFieldArray.push({
                        "name": listToAdd[field].name,
                        "label": listToAdd[field].label,
                        "type": listToAdd[field].type
                    })
                }

            } else {
                for (var field = 0; field < fieldsArr.length; field++) {

                    customFieldArray.push({
                        "name": fieldsArr[field].name,
                        "label": fieldsArr[field].label,
                        "type": fieldsArr[field].type
                    })
                }
            }
            callback(null, { data: customFieldArray, flag: flag });
        }
    })

}

module.exports = {
    requestTofetchObjectFields: requestTofetchObjectFields
}
