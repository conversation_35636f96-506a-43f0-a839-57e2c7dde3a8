const { CS_MERGE_STATUS } = require("../../../constants/constants");
const kafkaLib = require("../../../utils/kafka/kafka-lib");
var config = require('config');

const listContentSources = (contentSources, tenantId) => {
	return new Promise((resolve, reject) => {
		let placeHolders = contentSources.map(() => '?').join(', ');
		let sqlQuery = `
			SELECT
				cso.name as contentSourceObjectName, cso.id as contentSourceObjectId, cso.label as contentSourceObjectLabel,
				cs.label as contentSourceLabel, cs.elasticIndexName, cs.id,
				cst.id as content_source_type_id,
				csof.label as contentSourceObjectFieldLabel, csof.name as contentSourceObjectFieldName, csof.id as contentSourceObjectFieldId, csof.type as contentSourceObjectFieldType
			FROM
				content_sources cs 
			LEFT JOIN content_source_objects cso on cso.content_source_id = cs.id
			LEFT JOIN content_source_object_fields csof on cso.id = csof.content_source_object_id
			LEFT JOIN content_source_types cst on cst.id = cs.content_source_type_id
			WHERE cs.id IN (${placeHolders});
		`;

		connection[tenantId].execute.query(sqlQuery, contentSources, function (err, result) {
			if (err) {
				reject(err);
			}
			else resolve(result);
		});
	});
};

const updateMergeFieldStatus = (mergeFieldId, status, tenantId) => {
	return new Promise((resolve, reject) => {
		let sqlQuery = `UPDATE merge_fields SET status = ? WHERE id = ?`;
		const payload = [status, mergeFieldId];

		connection[tenantId].execute.query(sqlQuery, payload, function (err, result) {
			if (err) {
				reject(err);
			}
			else resolve(result);
		});
	});
};

const bulkInsertMergeFieldObjectMapping = (mergeFields, tenantId) => {
	const objectMappings = mergeFields.map(item => Object.values(item));

	return Promise.all(
		objectMappings.map(row => {
			const sqlQuery = `
				INSERT INTO merge_field_cs_object_field_mapping 
				(tenant_id, cs_id, merge_field_id, cs_label, cs_object_id, cs_object_label, cs_object_field_id, cs_object_field_label, cs_type_id) 
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);
			`;
			return new Promise((resolve, reject) => {
				connection[tenantId].execute.query(sqlQuery, row, (err, result) => {
					if (err) reject(err);
					else resolve(result);
				});
			});
		})
	);
};

const createNewContentSourceObject = (tenantId, payload) => {
	return Promise.all(
		payload.map(row => {
			const sqlQuery = `
				INSERT into content_source_object_fields
				(content_source_object_id, name, label, type, isFilterable, isSortable, isSearchable, isActive, selector, single_multiple, merge_field_id)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`;
	
			return new Promise((resolve, reject) => {
				connection[tenantId].execute.query(sqlQuery, row, (err, result) => {
					if (err) reject(err);
					else resolve(result);
				});
			});
		})
	);
};

const getAliasFieldsFromObjects = (tenantId, mergeFieldId) => {
	return new Promise((resolve, reject) => {
		const sqlQuery = `SELECT * FROM content_source_object_fields WHERE merge_field_id = ?`
		connection[tenantId].execute.query(sqlQuery, [mergeFieldId], (err, result) => {
			if (err) reject(err);
			else resolve(result);
		});
	});
};

const deleteContentSourceObject = (tenantId, payload) => {
	const sqlQuery = `
		DELETE FROM content_source_object_fields WHERE merge_field_id = ?
	`;

	return new Promise((resolve, reject) => {
		connection[tenantId].execute.query(sqlQuery, payload, (err, result) => {
			if (err) reject(err);
			else resolve(result);
		});
	});
};

const processMergeFields = async (tenantId, mergeFields, csData, mergeFieldId, name) => {
	const csMergeFieldsMappingsObjects = [];
	const indexKafkaPayload = [];

	try {
		for (const mergeField of mergeFields) {
			const contentSource = csData.find((cs) => cs.id === mergeField.contentSourceId && cs.contentSourceObjectFieldId === mergeField.csObjectFieldId && cs.contentSourceObjectId === mergeField.csObjectId);
			
			if (!contentSource) {
				throw new Error(`Invalid mergeField Data: contentSourceId ${mergeField.contentSourceId} is invalid.`);
			}

			if(!contentSource.contentSourceObjectId)  {
				throw new Error(`Invalid mergeField Data: csObjectId ${mergeField.csObjectId} is invalid.`);
			}

			const obj = {
				tenant_id: tenantId,
				cs_id: contentSource.id,
				merge_field_id: mergeFieldId,
				cs_label: contentSource.contentSourceLabel,
				cs_object_id: contentSource.contentSourceObjectId,
				cs_object_label: contentSource.contentSourceObjectLabel,
				cs_object_field_id: contentSource.contentSourceObjectFieldId,
				cs_object_field_label: contentSource.contentSourceObjectFieldLabel,
				cs_type_id: contentSource.content_source_type_id,
				cs_object_field_type: contentSource.contentSourceObjectFieldType,
			}

			csMergeFieldsMappingsObjects.push(obj);

			indexKafkaPayload.push({
				fieldName: contentSource.contentSourceObjectFieldName,
				// indexName: contentSource.elasticIndexName,
				objectName: contentSource.contentSourceObjectName,
				indexLabel: contentSource.contentSourceLabel,
				objectId: contentSource.contentSourceObjectId,
				contentSourceId: contentSource.id,
				indexName: `${contentSource.elasticIndexName}__${contentSource.contentSourceObjectName}`,
				objectType: contentSource.contentSourceObjectFieldType,
			});
		}

		await updateMergeFieldStatus(mergeFieldId, CS_MERGE_STATUS.complete, tenantId);
	}
	catch (err) {
		await updateMergeFieldStatus(mergeFieldId, CS_MERGE_STATUS.fail, tenantId);
		throw err;
	}

	return { csMergeFieldsMappingsObjects, indexKafkaPayload };
};

const checkExistingField = (payload, tenantId) => {
	return new Promise((resolve, reject) => {
		const sqlQuery = `SELECT id from merge_fields where name LIKE ? OR label LIKE ? AND tenant_id = ?`;

		const sqlPayload = [payload.name, payload.label, tenantId];

		connection[tenantId].execute.query(sqlQuery, sqlPayload, (err, result) => {
			if (err) reject(err);
			else resolve(result);
		});
	});
};

const getExistingMergeField = (mergeFieldId, tenantId) => {
	return new Promise((resolve, reject) => {
		const sqlQuery = `SELECT * from merge_fields where id = ? AND tenant_id = ?`;

		const sqlPayload = [mergeFieldId, tenantId];

		connection[tenantId].execute.query(sqlQuery, sqlPayload, (err, result) => {
			if (err) reject(err);
			else resolve(result);
		});
	});
};

const getMergeFieldObjectsById = (mergeFieldId, tenantId) => {
	return new Promise((resolve, reject) => {
		const sqlQuery = `SELECT
			cs.label as indexLabel, cso.name as objectName, cso.id as objectId,
			cs.id as contentSourceId, cs.elasticIndexName, cso.name as contentSourceObjectName, csof.type as objectType,
			csof.name as contentSourceObjectFieldName
			from merge_field_cs_object_field_mapping mfo
			LEFT JOIN content_sources cs on cs.id = mfo.cs_id
			LEFT JOIN content_source_objects cso on cso.id = mfo.cs_object_id
			LEFT JOIN content_source_object_fields csof on csof.id = mfo.cs_object_field_id
			where mfo.merge_field_id = ?`;

		const sqlPayload = [mergeFieldId];

		connection[tenantId].execute.query(sqlQuery, sqlPayload, (err, result) => {
			if (err) reject(err);
			else resolve(result);
		});
	});
};

const mergeFields = {
	createNew: async (tenantId, payload) => {

		return new Promise(async (resolve, reject) => {
			try {
				const csMergeFieldsObject = [
					tenantId,
					payload.name,
					payload.label,
					CS_MERGE_STATUS.inProgress
				];
	
				const sqlQuery = `Insert into merge_fields(tenant_id, name, label, status) values(?,?,?,?)`;
	
				const contentSourceIds = payload.fields.map((field) => field.contentSourceId);
				const contentSources = await listContentSources(contentSourceIds, tenantId);
	
				const existingFields = await checkExistingField(payload, tenantId);
	
				if(existingFields.length) {
					throw new Error(`Field name or label already exists`);
				}
	
				connection[tenantId].execute.query(sqlQuery, csMergeFieldsObject, async function (err, result) {
					try {
						if (err) {
							reject(err);
						}
						else {
							const { csMergeFieldsMappingsObjects, indexKafkaPayload } = await processMergeFields(tenantId, payload.fields, contentSources, result.insertId, payload.name);
		
							if (csMergeFieldsMappingsObjects.length > 0) {
								await bulkInsertMergeFieldObjectMapping(csMergeFieldsMappingsObjects, tenantId);
		
								const csObjectFieldsPayload = csMergeFieldsMappingsObjects.map(item => [
									item.cs_object_id,
									payload.name,
									payload.label,
									item.cs_object_field_type,
									1,1,0,1,
									'single',
									'single',
									result.insertId
								]);
		
								await createNewContentSourceObject(tenantId, csObjectFieldsPayload);
								const aliasFieldinObjects = await getAliasFieldsFromObjects(tenantId, result.insertId);
								await kafkaLib.publishMessage({
									topic: config.get("kafkaTopic.mergeField"),
									messages:[{
										value: JSON.stringify({ 
											fields: indexKafkaPayload, 
											tenantId, 
											type: 'ADD',
											mergeFieldId: result.insertId,
											mergeFieldName: payload.name,
											mergeFieldLabel: payload.label,
											aliasFieldinObjects
										}),
									}]
								})
							}
							resolve(result);
						}
					}
					catch(err) {
						reject(err);
					}
				});
			}
			catch(err) {
				reject(err);
			}
		});
	},

	list: async (tenantId) => {
		return new Promise((resolve, reject) => {
			const sqlQuery = `
				SELECT mf.name, mf.label, mf.status, mf.tenant_id, mf.id as merge_field_id,
				mfom.id as merge_field_mapping_id, mfom.createdAt, mfom.updatedAt,
				cs.id as actual_cs_id, cs.label as actual_cs_label,
				mfom.cs_id as cs_id, mfom.cs_label as cs_label, mfom.cs_object_label, mfom.cs_object_id, mfom.cs_object_field_label, mfom.cs_object_field_id,
				cso.id as actual_cs_object_id,
				csof.id as actual_cs_object_field_id,
				cst.img as content_source_type_url, cst.name as content_source_type_label
				from merge_fields mf
				LEFT JOIN merge_field_cs_object_field_mapping mfom on mfom.merge_field_id = mf.id
				LEFT JOIN content_sources cs on cs.id = mfom.cs_id
				LEFT JOIN content_source_objects cso on mfom.cs_object_id = cso.id
				LEFT JOIN content_source_object_fields csof on csof.id = mfom.cs_object_field_id
				LEFT JOIN content_source_types cst ON mfom.cs_type_id = cst.id
				WHERE mf.tenant_id = ? AND mf.is_deleted = 0
			`;

			const payload = [tenantId];

			connection[tenantId].execute.query(sqlQuery, payload, async function (err, result) {
				if(err) reject(err);

				const groupedData = result.reduce((acc, row) => {
					const mergeFieldId = row.merge_field_id;
			
					if (!acc[mergeFieldId]) {
						acc[mergeFieldId] = {
							merge_field_id: row.merge_field_id,
							tenant_id: row.tenant_id,
							name: row.name,
							label: row.label,
							status: row.status,
							MergeFieldsCsObjectMappings: [],
						};
					}

					if(!row.actual_cs_object_field_id || !row.actual_cs_object_id || !row.actual_cs_id) {
						acc[mergeFieldId].status = CS_MERGE_STATUS.fail
					}

					acc[mergeFieldId].MergeFieldsCsObjectMappings.push({
						merge_field_mapping_id: row.merge_field_mapping_id,
						tenant_id: row.tenant_id,
						cs_id: row.cs_id,
						cs_label: row.cs_label,
						cs_object_label: row.cs_object_label,
						cs_object_id: row.cs_object_id,
						cs_object_field_id: row.cs_object_field_id,
						cs_object_field_label: row.cs_object_field_label,
						createdAt: row.createdAt,
						updatedAt: row.updatedAt,
						content_source_type_url: row.content_source_type_url,
						content_source_type_label: row.content_source_type_label,
						is_object_field_deleted: row.actual_cs_object_field_id === null,
						is_object_deleted: row.actual_cs_object_id === null,
						is_content_source_deleted: row.actual_cs_id === null,
					});
			
					return acc;
				}, {});

				const formattedData = Object.values(groupedData);
				resolve(formattedData);
			});
		});
	},

	delete: (mergeFieldId, tenantId) => {
		return new Promise((resolve, reject) => {
			const sqlQuery = `UPDATE merge_fields SET is_deleted = 1 WHERE id = ? AND tenant_id = ?`;
			const payload = [mergeFieldId, tenantId];

			connection[tenantId].execute.query(sqlQuery, payload, async function(err, result) {
				if(err) reject(err);

				const deletePayload = [mergeFieldId];

				await deleteContentSourceObject(tenantId, deletePayload);

				await kafkaLib.publishMessage({
					topic: config.get("kafkaTopic.mergeField"),
					messages:[{
						value: JSON.stringify({
							tenantId, 
							type: 'DELETE',
							mergeFieldId,
						}),
					}]
				})
				resolve(result);
			});
		});
	},

	checkAvailable: (query, tenantId) => {
		return new Promise((resolve, reject) => {
			let sqlQuery = `SELECT id, is_deleted FROM merge_fields WHERE tenant_id = ? AND LOWER(${query.type}) = LOWER(?);`;
			const payload = [tenantId, query.value];

			connection[tenantId].execute.query(sqlQuery, payload, async function(err, result) {
				if(err) reject(err);

				if(result.length) resolve({ isExisted: true, isDeleted: Boolean(result[0].is_deleted) });
				else resolve({ isExisted: false, isDeleted: false });
			});
		});
	},

	republishMergeField: async (mergeFieldId, tenantId) => {
		return new Promise(async (resolve, reject) => {
			try {
				const [mergeFieldResponse] = await getExistingMergeField(mergeFieldId, tenantId);
				const aliasFieldinObjects = await getAliasFieldsFromObjects(tenantId, mergeFieldId);

				const tempFields = await getMergeFieldObjectsById(mergeFieldId, tenantId);

				const fields = tempFields.map(item => ({
					indexName: `${item.elasticIndexName}__${item.contentSourceObjectName}`,
					objectType: item.objectType,
					contentSourceId: item.contentSourceId,
					objectId: item.objectId,
					objectName: item.objectName,
					fieldName: item.contentSourceObjectFieldName,
					indexLabel: item.indexLabel
				}));

				if (mergeFieldResponse) {
					if(!fields.length || !aliasFieldinObjects.length) {
						resolve({ message: 'Merge field does not contain enough data to proceed.' });
					}
					else {
						const indexKafkaPayload = {
							"type": "ADD",
							mergeFieldId,
							mergeFieldName: mergeFieldResponse.name,
							mergeFieldLabel: mergeFieldResponse.label,
							tenantId,
							aliasFieldinObjects,
							fields
						};
	
						await kafkaLib.publishMessage({
							topic: config.get("kafkaTopic.mergeField"),
							messages:[{
								value: JSON.stringify(indexKafkaPayload),
							}]
						})
	
						resolve({ status: 200 , message: 'Merge field published successfully' });
					};
				}
				else {
					resolve({ message: 'Invalid Merge Field ID' });
				};

				
			}
			catch (err) {
				reject(err);
			}
		});
	},
};

module.exports = {
	mergeFields,
	listContentSources
};