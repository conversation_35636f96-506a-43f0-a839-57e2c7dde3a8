module.exports = {
  "objects": [{
      "name": "courses",
      "label": "Courses",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": []

  }],
  "fields": [
    {
      name: 'id',
      label: 'Course ID',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1,
    },
    {
      name: 'name',
      label: 'Course Name',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'version',
      label: 'Course Version',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'created_at',
      label: 'Creation Date',
      type: 'datetime',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'sellable',
      label: 'Sellable',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'cataloged',
      label: 'Cataloged',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'keywords',
      label: 'Keywords',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'num_enrolled',
      label: 'Enrolled Learners',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'description_text',
      label: 'Course Description',
      type: 'string',
      isFilterable: 0,
      isSortable: 0,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'price',
      label: 'Price',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'number_of_reviews',
      label: 'Number of Reviews',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'date_published',
      label: 'Date Published',
      type: 'datetime',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'course_length_unit',
      label: 'Course Length Unit',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'number_of_modules',
      label: 'Number of Modules',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'published_status_id',
      label: 'Published Status ID',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'difficulty_level',
      label: 'Difficulty Level',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'thumbnail_image_url',
      label: 'Thumbnail Image URL',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'owner_first_name',
      label: 'Owner First Name',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'owner_last_name',
      label: 'Owner Last Name',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'owner_email',
      label: 'Owner Email',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'owner_id',
      label: 'Owner ID',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 0,
      isActive: 1
    },
    {
      name: 'module_names',
      label: 'Module Names',
      type: 'string',
      isFilterable: 1,
      isSortable: 1,
      isSearchable: 1,
      isActive: 1
    },
    {
      name: 'view_href',
      label: 'View Href',
      type: 'string',
      isFilterable: 1,
      isSortable: 0,
      isSearchable: 1,
      isActive: 1,
    },
  ]
}
