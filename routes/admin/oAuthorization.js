
const jsforce = require('jsforce');
const async = require('async');
const commonFunctions = require('./../../utils/commonFunctions');
const oauthUtil2 = require('../../utils/oAuthConfluenceJiraUtil2.js');
const appVariables = require('../../constants/appVariables');
const express = require('express');
const router = express.Router();
const request = require('request');
const contentSource = require('./contentSources');
const jive = require('./jive');
const drive = require('./drive');
const box = require('./box');
const youtube = require('./youtube');
const sharepoint = require('./sharepoint');
const slack = require('./slack');
const dropbox = require('./dropbox');
const dynamics = require('./dynamics');
const aes256 = require('nodejs-aes256');
const servicenow = require('./servicenow');
const vimeo = require('./vimeo');
const constants = require('./../../constants/constants');
const htmlToJson = require('html-to-json');
const OAuthkafkaPublisher = require('../../utils/kafka/kafkaPublishers/updateOAuthStatus');
const { getAuroraAccessToken } = require('./khorosAurora');

router.get('/oAuthCallback', function (req, res) {
  const code = req.query.code;
  const state = req.query.state;
  const contentSourceId = req.query.state;
  const tenantId = req.query["tenant-id"]
  commonFunctions.getContentSourceDataById(contentSourceId,req, function (err, resultData) {
    const authorization = resultData.authorization
    const csTypeId = resultData.contentSource.content_source_type_id;
    switch (resultData.contentSource.content_source_type_id) {
      case 1:
        jive.JVConnection(code, resultData, function (resultConnection) {
          if (resultConnection != 0) {
            authorization.accessToken = resultConnection.access_token;
            authorization.refreshToken = resultConnection.refresh_token;
            //authorization.instanceURL = resultConnection.instanceUrl;
            authorization.connection_status = true;
            authorization.authVerification = true;
            commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
              if (!error) {
                OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                res.render('success.ejs', { id: "btnNextJive" });
              }
              else {
                contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                res.render('fail.ejs', {});
              }
            })
          }
          else {
            //connection fail
            contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
            res.render('fail.ejs', {});
          }
        })
        break; //Jive

      case constants.CONTENT_SOURCE_TYPE.khorosAurora:
        let khorosCode;
        const match = req.originalUrl.match(/[?&]code=([^&]+)/);
        console.log(match, '===mac')
        if (match) {
          khorosCode = match[1]; // Extracted code as-is
        }
        console.log(khorosCode, '====khorosCode');
        const encodedCode = encodeURIComponent(khorosCode);
        console.log(encodedCode, '===encodededCode');
        getAuroraAccessToken(resultData.contentSource, authorization, encodedCode, (err, result) => {
          console.log(err, '====errrrr');
          if(err) {
            contentSource.deleteContentSource(contentSourceId, csTypeId, 0, req, x => { })
            return res.render('fail.ejs', {});
          } else {
            authorization.accessToken = result.accessToken || result.access_token || result.data.access_token || result.data.accessToken;
            authorization.refreshToken = result.refreshToken || result.refresh_token || result.data.refresh_token || result.data.refreshToken;
            if(tenantId) {
              authorization.tenantId = tenantId;
              authorization.connection_status = true;
              authorization.authVerification = true;
              commonFunctions.insertUpdateAuthorization(authorization, req, (error, resultSave) => {
                if (!error) {
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnNextContentSource" });
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                  return res.render('fail.ejs', {});
                }
              })
            }
          }
        });
        break;
      case 2: //Lithium
          let url = resultData.contentSource.url;
            if (resultData.authorization_type.indexOf('htaccess') > -1 && resultData.authorization.htaccessUsername && resultData.authorization.htaccessPassword){
                let start = url.indexOf('//')+2;
                url = url.substring(0,start)+resultData.authorization.htaccessUsername+':'+resultData.authorization.htaccessPassword+'@'+url.substring(start);
            }
        connectLithiumLocally(code,url, authorization.client_id, authorization.client_secret, function (resultConnection) {

          if (resultConnection != 0) {
            //
            authorization.accessToken = resultConnection.data.access_token;
            authorization.refreshToken = resultConnection.data.refresh_token;
            if (tenantId)
              authorization.tenantId = tenantId
            authorization.connection_status = true
            authorization.authVerification = true;
            commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
              //
              if (!error) {
                //
                OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                res.render('success.ejs', { id: "btnNextLithium" });
                //
              }
              else {
                contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                res.render('fail.ejs', {});
              }
            })
          }
          else {
            //connection fail
            console.log('lithium connection failed');
            contentSource.deleteContentSource(contentSourceId, csTypeId, 0, req, x => { })
            res.render('fail.ejs', {});
          }
        })
        break;
      case 3: {  //salesforce
        connectSalesforceLocally(code, resultData.contentSource.url, authorization.client_id, authorization.client_secret, function (resultConnection) {
          if (resultConnection != 0) {
            //
            authorization.accessToken = resultConnection.accessToken;
            authorization.refreshToken = resultConnection.refreshToken;
            authorization.instanceURL = resultConnection.instanceUrl;
            authorization.connection_status = true
            authorization.authVerification = true;
            commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {

              if (!error) {
                //
                OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                res.render('success.ejs', { id: "btnNextSalesforce" });
                //
              }
              else {
                contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                res.render('fail.ejs', {});
              }
            })
          }
          else {
            //connection fail
            contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
            res.render('fail.ejs', {});
          }
        })
        break;
      }
      case 5: { // sharepoint
        sharepoint.getAccessToken(resultData.contentSource.content_source_type_id, code, "https://login.microsoftonline.com", resultData.authorization.client_id, resultData.authorization.client_secret , '/common/oauth2/v2.0/token', "https://oauthsfdc.searchunify.com",resultData, function (err, resultConnection) {            
        if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;

              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;
              commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                if (!error) {
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnNextSharepoint" });
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                  res.render('fail.ejs', {});
                }
              })
            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })

              res.render('fail.ejs', {});
            }
          }

        })
        break;
      }
      case 7: {
        getAccessToken(resultData.contentSource.content_source_type_id, code, resultData.contentSource.url, authorization.client_id, authorization.client_secret, "/oauth/tokens", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          } else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              authorization.connection_status = true;
              authorization.authVerification = true;
              commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                //
                if (!error) {
                  //
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnnextZendesk" });
                  //
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                  res.render('fail.ejs', {});
                }
              })
  
            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }
        })
        break;
      }
      case 12: {
        getAccessToken(resultData.contentSource.content_source_type_id, code, resultData.contentSource.url, authorization.client_id, authorization.client_secret, "/oauth2/v4/token", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;
              drive.getUser(resultConnection.access_token, authorization.content_source_id,req, function (err, data) {
                if (err) {
                  res.render('fail.ejs', {});
                }
                else {
                  authorization.htaccessUsername = data.userID;
                  authorization.username = data.md5Email;
                  commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                    if (!error) {
                      OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                      res.render('success.ejs', { id: "btnnextDrive" });
                    }
                    else {
                      contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                      res.render('fail.ejs', {});
                    }
                  })
                }
              })

            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }

        })
        break;
      }
      case 13: {
        getAccessToken(resultData.contentSource.content_source_type_id, code, resultData.contentSource.url, authorization.client_id, authorization.client_secret, "/oauth2/token", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;
              box.getUser(resultConnection.access_token, authorization.content_source_id, req, function (err, uID) {
                if (err) {
                  res.render('fail.ejs', {});
                }
                else {
                  authorization.htaccessUsername = uID;
                  commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                    if (!error) {
                      OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                      res.render('success.ejs', { id: "btnnextBox" });
                    }
                    else {
                      contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                      res.render('fail.ejs', {});
                    }
                  })
                }
              })

            }
            else {
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }

        })
        break;
      }
      case 15: { //github
        connectGithubLocally(code, authorization.client_id, authorization.client_secret, function (resultConnection) {
          if (resultConnection != 0) {
            authorization.accessToken = resultConnection.access_token;
            authorization.connection_status = true
            authorization.authVerification = true;
            commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
              //
              if (!error) {
                //
                OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                res.render('success.ejs', { id: "btnNextGithub" });
                //
              }
              else {
                contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                res.render('fail.ejs', {});
              }
            })
          }
          else {
            //connection fail
            contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
            res.render('fail.ejs', {});
          }
        })
        break;
      }
      case 18: {
        var redirectUri = "https://oauthsfdc.searchunify.com";
        //redirectUri = decodeURIComponent(redirectUri);
        commonFunctions.errorlogger.info('redirectPath: ' , redirectUri);
        getStackOverflowAccessToken(appVariables.stackoverflow.clientId, appVariables.stackoverflow.clientSecret, code, redirectUri, function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              authorization.connection_status = true;
              authorization.authVerification = true;
              commonFunctions.errorlogger.info(resultConnection.access_token)
              commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                if (!error) {
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnnextStackOverflow" });
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                  res.render('fail.ejs', {});
                }
              })

            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }

        })
        break;
      }
      case 42: {
        getAccessToken(resultData.contentSource.content_source_type_id, code, resultData.contentSource.url, authorization.client_id, authorization.client_secret, "/oauth/token", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;
              commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                if (!error) {
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnnextBox" });
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                  res.render('fail.ejs', {});
                }
              });

            }
            else {
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }

        })
        break;
      }
      case 17: {
        getAccessToken(resultData.contentSource.content_source_type_id, code, resultData.contentSource.url, authorization.client_id, authorization.client_secret, "/oauth2/v4/token", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            youtube.getTokenInfo(resultConnection.access_token, function(err, response){
              if(err){
                res.render('youtubeFail.ejs', {});
              }else{
                if (resultConnection != 0) {
                  authorization.accessToken = resultConnection.access_token;
    
                  if (resultConnection.refresh_token) {
                    authorization.refreshToken = resultConnection.refresh_token;
                  }
                  authorization.connection_status = true;
                  authorization.authVerification = true;
                  youtube.getUser(resultConnection.access_token, authorization.content_source_id,req, function (err, uID) {
                    if (err) {
                      res.render('fail.ejs', {});
                    }
                    else {
                      authorization.htaccessUsername = uID;
                      commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                        if (!error) {
                          OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                          const scopes =  response.scope.split(' ');
                          if(!( scopes.includes(constants.YOUTUBE_REQ_SCOPES.readAccess) || 
                                scopes.includes(constants.YOUTUBE_REQ_SCOPES.fullAccess)
                          )){
                            res.render('success.ejs', { error: true, id: "btnnextYoutube"});
                          } else {
                            res.render('success.ejs', { id: "btnnextYoutube" });
                          }
                        }
                        else {
                          contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                          res.render('fail.ejs', {});
                        }
                      })
                    }
                  })
                }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0, req, x => { })

              res.render('fail.ejs', {});
            }
          }
        })
          }

        })
        break;
      }

      //gmail 
      case constants.CONTENT_SOURCE_TYPE.gmail: {
        getAccessToken(resultData.contentSource.content_source_type_id, code, resultData.contentSource.url, authorization.client_id, authorization.client_secret, "/oauth2/v4/token", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
    
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;
                  commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                    if (!error) {
                      OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                      res.render('success.ejs', { id: "btnnextYoutube" });
                    }
                    else {
                      contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                      res.render('fail.ejs', {});
                    }
                  })
            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0, req, x => { })
    
              res.render('fail.ejs', {});
            }
          }
    
        })
        break;
      }
      
      //slack
      case 8: {
        getAccessToken(resultData.contentSource.content_source_type_id, code, resultData.contentSource.url, appVariables.slack.clientId, appVariables.slack.appSecret, "https://slack.com/api/oauth.access", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;
              slack.getUser(resultConnection.access_token, authorization.content_source_id, function (err, data) {
                if (err) {
                  res.render('fail.ejs', {});
                }
                else {
                  authorization.htaccessUsername = data.userID;
                  authorization.username = data.user;
                  commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                    if (!error) {
                      OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                      res.render('success.ejs', { id: "btnnextSlack" });
                    }
                    else {
                      contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                      res.render('fail.ejs', {});
                    }
                  })
                }
              })

            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }

        })
        break;
      }
      case 24: { //dropbox
        dropbox.getAccessToken(code, resultData.contentSource.url, authorization,  "/oauth2/token", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;
 
              commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                if (!error) {
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnnextDropbox" });
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                  res.render('fail.ejs', {});
                }
              })
            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }

        })
        break;
      }
      case 26: { //service now
        servicenow.getAccessToken(code, state, resultData, resultData.contentSource.url, "/oauth_token.do", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;

              commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                if (!error) {
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnNextJira" });
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                  res.render('fail.ejs', {});
                }
              })
            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }

        })
        break;
      }
      case 29: { //Dynamics
        dynamics.getAccessToken(resultData, code, "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;

              commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                if (!error) {
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnnextDynamics" });
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                  res.render('fail.ejs', {});
                }
              })
            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }

        })
        break;
      }
      case 32: {
        if (code && code != "undefined") {
          getAccessToken(resultData.contentSource.content_source_type_id, code, resultData.contentSource.url, authorization.client_id, authorization.client_secret, "/oauth2/token", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
            if (err) {
              return res.render('fail.ejs', {});
            } else {
            if (resultConnection != 0) {
              console.log("result connection recieved: ", resultConnection);
              authorization.accessToken = resultConnection.access_token;
              authorization.refreshToken = resultConnection.refresh_token;
              authorization.connection_status = true;
              authorization.authVerification = true;
              commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                //
                if (!error) {
                  //
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnnextDocebo" });
                  //
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                  res.render('fail.ejs', {});
                }
              })
            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }

          })
        }
        else {
          res.render('fail.ejs', {});
        }
        break;
      }
      case 33:{ //Vimeo

        vimeo.getAccessToken(code, resultData.contentSource.url, authorization.client_id, "/oauth/authorize/client", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;

              commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                if (!error) {
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnnextVimeo" });
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                  res.render('fail.ejs', {});
                }
              })
            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }

        })
        break;
    
      }
      case 46: {
        getAccessToken(resultData.contentSource.content_source_type_id, code, appVariables.microsoftTeams.url , authorization.client_id, authorization.client_secret, `oauth2/v2.0/token`, "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;
              commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
                if (!error) {
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnnextBox" });
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
                  res.render('fail.ejs', {});
                }
              });
            }}}  )
            break;
      }
      //brightspace
      case constants.CONTENT_SOURCE_TYPE.brightspace: {
        getAccessToken(resultData.contentSource.content_source_type_id, code, resultData.contentSource.url , authorization.client_id, authorization.client_secret, appVariables.brightspace.accessTokenUrl, appVariables.brightspace.redirectUri, function (err, resultConnection) {
          if(err) {
            res.render('fail.ejs', {});
          } else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              commonFunctions.insertUpdateAuthorization(authorization,req, function (error, resultSave) {
  
                if (!error) {
                  res.render('success.ejs', { id: "btnnextBrightspace" });
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, 0,req, x => { })
                  res.render('fail.ejs', {});
                }
              })
            }
            else {
              //connection fail
              contentSource.deleteContentSource(contentSourceId, 0,req, x => { })
              res.render('fail.ejs', {});
            }
          }
        })
        break;
      }
      case constants.CONTENT_SOURCE_TYPE.wistia: { // Wistia Content Source
        const baseUrl = appVariables.wistia.url; // get api base url;
        getAccessToken(resultData.contentSource.content_source_type_id, code, baseUrl , authorization.client_id, authorization.client_secret, "/oauth/token", "https://oauthsfdc.searchunify.com", function (err, resultConnection) {
          if (err) {
            res.render('fail.ejs', {});
          }
          else {
            if (resultConnection != 0) {
              authorization.accessToken = resultConnection.access_token;
              if (resultConnection.refresh_token) {
                authorization.refreshToken = resultConnection.refresh_token;
              }
              authorization.connection_status = true;
              authorization.authVerification = true;
              commonFunctions.insertUpdateAuthorization(authorization, function (error, resultSave) {
                if (!error) {
                  OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                  res.render('success.ejs', { id: "btnnextBox" });
                }
                else {
                  contentSource.deleteContentSource(contentSourceId, csTypeId, 0, x => { })
                  res.render('fail.ejs', {});
                }
              });

            }
            else {
              contentSource.deleteContentSource(contentSourceId, csTypeId, 0, x => { })
              res.render('fail.ejs', {});
            }
          }

        })
        break;
      }
      case constants.CONTENT_SOURCE_TYPE.helpScout: {
        const { helpScout: {  accessTokenUrl } } = appVariables;
        const { client_id: clientId, client_secret: clientSecret }  = authorization;
         getAccessToken(resultData.contentSource.content_source_type_id, code, accessTokenUrl, clientId, clientSecret, '', '', (err, result) => {        
          if (err) {
              contentSource.deleteContentSource(contentSourceId, 0, x => {});
              res.render('fail.ejs', {});
             } else {
                authorization.accessToken = result.access_token;
                authorization.refreshToken = result.refresh_token;
                authorization.authVerification = true;
                commonFunctions.insertUpdateAuthorization(authorization, req, function (error, resultSave) {
                  if (!error) {
                    OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                    res.render('success.ejs', { id: 'btnnextContentSource' });
                  }
                  else {
                    contentSource.deleteContentSource(contentSourceId, 0, x => {});
                    res.render('fail.ejs', {});
                  }
                });
             }
         });
         break;
      }
      //uservoice
      case constants.CONTENT_SOURCE_TYPE.uservoice: {
        getAccessToken(resultData.contentSource.content_source_type_id, code, resultData.contentSource.url , authorization.client_id, authorization.client_secret, appVariables.uservoice.accessTokenUrl, appVariables.uservoice.redirectUri, function (err, resultConnection) {
          if (resultConnection != 0) {
            authorization.accessToken = resultConnection.access_token;
            authorization.connection_status = true;
            authorization.authVerification = true;
            commonFunctions.insertUpdateAuthorization(authorization, req, function (error, resultSave) {
              //
              if (!error) {
                OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                res.render('success.ejs', { id: 'btnnextContentSource' });
              }
              else {
                contentSource.deleteContentSource(contentSourceId, 0,req, x => { })
                res.render('fail.ejs', {});
              }
            })
          }
          else {
            //connection fail
            contentSource.deleteContentSource(contentSourceId, 0,req, x => { })
            res.render('fail.ejs', {});
          }

        })
        break;
      }

      case constants.CONTENT_SOURCE_TYPE.simpplr: {
        getAccessToken(resultData.contentSource.content_source_type_id, code, appVariables.simpplr.url , authorization.client_id, authorization.client_secret, appVariables.simpplr.accessTokenUrl, appVariables.simpplr.redirectUri, function (err, resultConnection) {
          if (resultConnection != 0) {
            authorization.accessToken = resultConnection.access_token;
            authorization.refreshToken = resultConnection.refresh_token;
            authorization.connection_status = true;
            authorization.authVerification = true;
            commonFunctions.insertUpdateAuthorization(authorization, req, function (error, resultSave) {
              if (!error) {
                OAuthkafkaPublisher.updateOAuthStatus({ authorization, req });
                res.render('success.ejs', { id: 'btnnextContentSource' });
              }
              else {
                contentSource.deleteContentSource(contentSourceId, 0,req, x => { })
                res.render('fail.ejs', {});
              }
            })
          }
          else {
            contentSource.deleteContentSource(contentSourceId, 0,req, x => { })
            res.render('fail.ejs', {});
          }
        })
        break;
      }

      default: {
        contentSource.deleteContentSource(contentSourceId, csTypeId, 0,req, x => { })
        res.render('fail.ejs', {})
        break;
      }
    }

  })
})

connectLithiumLocally = function (code, loginUrl, clientId, clientSecret, callback) {
  var options = {
    method: 'POST',
    url: loginUrl + '/api/2.0/auth/accessToken',
    qs: { client_id: clientId },
    headers:
      { 'content-type': 'application/json' },
    body:
    {
      code: code.replace(/\s/g, '+'),
      client_id: clientId,
      client_secret: clientSecret,
      redirect_uri: appVariables.oAuthRedirectURI,
      grant_type: 'authorization_code'
    },
    json: true
  };

  request(options, async function (error, response, body) {
    if(body && typeof body == 'string') {
      await htmlToJson.parse(body, {
        'text': function ($doc) {
          return $doc.find('title').text();
        }
      }, function (err, result) {
        if (result.text == '401 Authorization Required') {
          console.log(result);
          body = 0;
        }
      });
    }
    if (error) {
      console.log('error while connecting lithium', error);
      body = 0;
    }
    callback(body);
  });
}

const connectSalesforceLocally = function (code, loginUrl, clientId, clientSecret, callback) {

  var oauth2 = new jsforce.OAuth2({
    loginUrl: loginUrl,
    clientId: clientId,
    clientSecret: clientSecret,
    redirectUri: appVariables.oAuthRedirectURI//'https://localhost/backend'+appVariables.salesforce.redirectUri
  });

  commonFunctions.errorlogger.info("OAuth object is", oauth2)

  const connSF = new jsforce.Connection({
    oauth2: oauth2
  });
  connSF.authorize(code, function (err, userInfo) {
    if (err) {
      commonFunctions.errorlogger.error("err", err)

      return callback(0)

    }
    commonFunctions.errorlogger.info('connNewOauth.accessToken', connSF.accessToken);
    commonFunctions.errorlogger.info('connNewOauth.refreshToken', connSF.refreshToken);
    commonFunctions.errorlogger.info('connNewOauth.instanceUrl', connSF.instanceUrl);
    commonFunctions.errorlogger.info('connNewOauth.oauth2.loginUrl', connSF.oauth2.loginUrl);
    callback(connSF)
  });
}

// stackoverflow access token
function getStackOverflowAccessToken(clientId, clientSecret, code, redirectUri, callback) {
  commonFunctions.errorlogger.info(clientId, clientSecret, code, redirectUri);
  var options = {
    method: 'POST',
    url: 'https://stackexchange.com/oauth/access_token/json',
    headers: {
      'cache-control': 'no-cache',
      'content-type': 'multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW',
      'User-Agent': "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:56.0) Gecko/20100101 Firefox/56.0"
    },
    formData: {
      client_id: clientId,
      client_secret: clientSecret,
      code: code,
      redirect_uri: redirectUri
    }
  };
  request(options, function (error, response, body) {
    if (error) {
      callback(error, null);
      return;
    }
    commonFunctions.errorlogger.info(body);
    callback(null, JSON.parse(body));
  });
}

//Get Zendesk access token

const getAccessToken = function (contentSourceTypeId, code, loginUrl, clientId, clientSecret, tokenUrl, redirect_Uri, callback) {
  var headers = {
    'Content-Type': 'application/json'
  };
  var dataString = {
    "grant_type": "authorization_code", "code": code,
    "client_id": clientId, "client_secret": clientSecret,
    "redirect_uri": redirect_Uri
  };
  if (contentSourceTypeId == 5) {
    dataString["resource"] = clientId;
  }
  if (contentSourceTypeId == 7) {
    dataString["scope"] = "read write"
  }
  let options;
  if (contentSourceTypeId == 13 || contentSourceTypeId == 5 || contentSourceTypeId == 32) {
    options = {
      url: loginUrl + tokenUrl,
      method: 'POST',
      headers: headers,
      formData: dataString
    };
  }
  else if(contentSourceTypeId == 46) {
    dataString["scope"] = "user.read mail.read";
    options = {
      url: loginUrl + tokenUrl,
      method: 'POST',
      headers: headers,
      formData: dataString
    };
  }
  else if (contentSourceTypeId == 8) {
    options = {
      url: tokenUrl,
      method: 'POST',
      headers: headers,
      formData: dataString
    };
  } else if (contentSourceTypeId === constants.CONTENT_SOURCE_TYPE.helpScout) {
    delete dataString.redirect_uri;
    options = {
      url: loginUrl,
      method: 'POST',
      headers,
      formData: dataString,
    }
  } else if(contentSourceTypeId === constants.CONTENT_SOURCE_TYPE.brightspace) {
    options = {
      url: loginUrl + tokenUrl,
      method: 'POST',
      headers: headers,
      formData: dataString
    };
  } else {
    options = {
      url: loginUrl + tokenUrl,
      method: 'POST',
      headers: headers,
      qs: dataString
    };
  }
  request(options, function (error, response, body) {
    if(response && response.statusCode === 302){
      console.log("Error while fetching token, status code 302 : ", error);
      return callback(error || "Error while fetching token", null);
    }
    if (error) {
      console.log("Error while fetching token : ", error);
      callback(error, null);
    }
    else {
      try{
        body = JSON.parse(body);
      }catch(e){
        console.log("Error while fetching token Body is not JSON : ", body);
        return callback(body, null);
      }
      if (body.error) {
        console.log("Error while fetching token from body : ", body.error);
        callback(body.error, null);
      }
      else {
        commonFunctions.errorlogger.info(body);
        callback(null, body);
      }
    }
  });

}

const oAuthCallbackJiraConfluence = function(req, res) {
  const code = req.query.code;
  const csId = req.query.state.split("Callbacksu_csid")[1];

  commonFunctions.getContentSourceDataById(csId, req, function(error, data) {
    if(error) {
      res.render("fail.ejs", {});
    } else {
      const config = {
        client_id: data.authorization.client_id,
        client_secret: data.authorization.client_secret,
        callback_url: data.authorization.callBackUrl,
        code
      };

      oauthUtil2.getOAuthAccessToken(config)
        .then((response) => {
          config.refresh_token = response.data.refresh_token;
          return oauthUtil2.getRefreshToken(config);
        })
        .then(response => {
          config.refresh_token = response.data.refresh_token;
          config.access_token = response.data.access_token;
          return oauthUtil2.getCloudId(config);
        })
        .then(response => {
          if ((response.data && response.data.statusCode === 401) || response.statusCode == 403) {
            return res.render('fail.ejs', {});
          }
          if (data.contentSource.content_source_type_id == constants.CONTENT_SOURCE_TYPE.jira) {
            let instance = response.data.find(atlassianInstance => atlassianInstance.url === data.contentSource.url);

            if (!instance) {
              return res.render('fail.ejs', {});
            }

            config.cloud_id = instance.id;
          } else {
            config.cloud_id = response.data[0].id;
          }
        
          data.authorization.accessToken = config.access_token;
          data.authorization.refreshToken = config.refresh_token;
          data.authorization.htaccessUsername = config.cloud_id;
          data.authorization.connection_status = true;
          data.authorization.authVerification = true;
          
          const tempVar = constants.CONTENT_SOURCE_TYPE_NAME[data.contentSource.content_source_type_id];
          data.authorization.instanceURL = `https://api.atlassian.com/ex/${tempVar}/${config.cloud_id}`;

          commonFunctions.insertUpdateAuthorization(data.authorization, req, async(err, result) => {
            if(err) {
              res.render('fail.ejs', {});
            } else {
              await OAuthkafkaPublisher.updateOAuthStatus({ authorization: data.authorization, req });
              res.render('success.ejs', { id: "btnNextContentSource" });
            }
          });
        })
        .catch(error => {
          res.render('fail.ejs', {});
        });
    }
  });
};


router.get('/oAuthCallbackJira', oAuthCallbackJiraConfluence);
router.get('/oAuthCallbackConfluence', oAuthCallbackJiraConfluence);

connectGithubLocally = function (code, clientId, clientSecret, callback) {
  var options = {
    host: 'github.com',
    method: 'POST',
    url: 'https://github.com/login/oauth/access_token',
    // qs: { client_id: clientId },
    headers:
      { 'content-type': 'application/json' },
    body:
    {
      code: code,
      client_id: clientId,
      client_secret: clientSecret,
      redirect_uri: appVariables.oAuthRedirectURI
    },
    json: true
  };

  request(options, function (error, response, body) {
    if (error) {
      commonFunctions.errorlogger.error(error)
      body = 0;
    }
    else
      commonFunctions.errorlogger.info(body);
    callback(body);
  });
}

module.exports = {
  router: router,
  connectSalesforceLocally: connectSalesforceLocally,
  connectLithiumLocally: connectLithiumLocally,
  getAccessToken: getAccessToken
}
