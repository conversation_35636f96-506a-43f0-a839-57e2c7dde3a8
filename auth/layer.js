const url = require("url");
const csrfProtection = require('csurf')({ cookie: true, secure: true, httpOnly: true, ignoreMethods: ['HEAD', 'OPTIONS'], sameSite: true });
const authenticate = require('../routes/components/oauth/authenticate');
const commonFunctions = require('../utils/commonFunctions');
var jwt = require('jsonwebtoken');
const base64url = require('base64url');
const crypto = require('crypto');
const express = require('express');
const router = express.Router();
var openUrls = [
    /^\/contentAnalytics\/getDataByDocId/,
    /^\/stopwords\/read/,
    /^\/socket.io/,
    /^\/agentHelper\/trainingStatus/,
    /^\/admin\/searchClient\/check-react-search-client-status/,
    /^\/communityHelper/,
    /^\/communityHelper\/authorization/, 
    /^\/crawler\/slack\/slackevents/,
    /^\/admin\/searchClient\/readAdHTML/,
    /^\/api\/v2\/searchSession\/byCaseUid/,
    /^\/admin-login/,
    /^\/admin\/authorization/,
    /^\/admin\/lithium\/crawlLithium/,
    /^\/admin\/lithium\/eventCreateThread/,
    /^\/admin\/lithium\/eventDeleteThread/,
    /^\/admin\/higherLogic\/deleteEvent/,
    /^\/admin\/higherLogic\/insertEvent/,
    /^\/admin\/notifications/,
    /^\/admin\/searchClient\/getsearchclientSettings/,
    /^\/bot\/checkToken/,
    /^\/bot\/createToken/,
    /^\/addons/,
    /^\/bot\/getUid/,
    /^\/admin\/userManagement\/checkSsoEnable/,
    /^\/admin\/userManagement\/checkUser/,
    /^\/ai\/getLastSearchQuery/,
    /^\/ai\/getPreviewResult/,
    /^\/ai\/getRecommendedResult/,
    /^\/analytics\/an\/mytrail/,
    /^\/analytics\/track.png/,
    /^\/assets/,
    /^\/authorise/,
    /^\/searchCallPermissions\/zendesk\/getContent/,
    /^\/searchCallPermissions\/zendesk\/getContent/,
    /^\/dashboard/,
    /^\/ext_resources/,
    /^\/favicon.ico$/,
    /^\/getAllContentLithiumv2/,
    /^\/getAutoProvisionToken/,
    /^\/getImageAvailability/,
    /^\/getResults/,
    /^\/dynamicLoginPage/,
    /^\/html/,
    /^\/jive\/oauth/,
    /^\/LithiumResults/,
    /^\/login/,
    /^\/validateUser/,
    /^\/resendOTPMail/,
    /^\/logout/,
    /^\/mainStaticResource/,
    /^\/node_modules/,
    /^\/open/,
    /^\/osapp/,
    /^\/pollApi/,
    /^\/registerSU/,
    /^\/readAllCss/,
    /^\/readclientTemplate/,
    /^\/readGrazittihtml/,
    /^\/readLithiumClientHtml/,
    /^\/readLithiumMain/,
    /^\/readMain/,
    /^\/forgetPassword/,
    /^\/reports/,
    /^\/resources/,
    /^\/routeSso/,
    /^\/saml/,
    /^\/signup/,
    /^\/savecredsZendesk/,
    /^\/tuning\/searchtuning\/exportSearchResultByPost/,
    /^\/SearchResults/,
    /^\/semanticSearch/,
    /^\/forgot-password/,
    /^\/statusPage/,
    /^\/UserDataSyncV2/,
    /^\/zendeskClient/,
    /^\/plugins\/getAddonsStatus/,
    /^\/oauthApiRate/,
    /^\/splitAnalyticsIndex\/splitIndex/,
    /^\/tuning\/obfuscation\/getObfuscatedCode/,
    /^\/tuning\/deobfuscate\/getDeObfuscatedCode/,
    /^\/tuning\/searchBoxObfuscation\/getSearchBoxObfuscatedCode/,
    /^\/tuning\/SearchBoxDeobfuscation\/getSearchBoxDeObfuscatedCode/,
    /^\/admin\/userManagement\/getSignupSettings/,
    /^\/pageRating\/getPageRatingData/,
    /^\/pageRating\/getPageRatingDataInstance/,
    /^\/admin\/userManagement\/getIdpSettings/,
    /^\/admin\/hostedSearchUser\/addHostedUser/,
    /^\/analytics\/tableauConnector\/authTableau/,
    /^\/analytics\/tableauConnectorRawApi\/authTableau/,
    /^\/admin\/getSimilarSearchRecommend\/getRecommendations/,
    /^\/crons\/updateCronModel/,
    /^\/searchClientAnalytics\/getContentAuthData/,
    /^\/synonyms\/dymTrainingStatus/,
    // /^\/crons\/getCronLogs/,
    // /^\/crons\/startCrons/
    /^\/crons\/mlConversion/,
    /^\/admin\/userManagement\/statusPage/,
    /^\/slackApis\/getSlackChannels/,
    /^\/slackApis\/slackAuth/,
    /^\/slackApis\/postMessageOnSlack/,
    /^\/api-docs/,
    /^\/pollApiCheck/
];
const oAuthSecure = [/^\/api/,/^\/api\/analytics/,/^\/api\/v3/,/^\/api\/v2_search/,/^\/authorise/, /^\/authorise_success/,/^\/agentHelper\/query-stage/,/^\/search\/searchResultByPost/];
const provisonKeySecure = [
    /^\/analytics\/sa\/getSessionChartDetails/,
    /^\/analytics\/sa\/getSessionChartSearches/,
    /^\/analytics\/sa\/getSessionChartPageViews/,
    /^\/api\/v2\/searchSession\/byCaseUid/,
    /^\/agentHelper\/query/,
    /^\/api\/v2\/searchSession\/byCookie/,
    /^\/plugins\/getAddonsStatus/,
    /^\/bot\/createToken/,
    /^\/bot\/getUid/,
    /^\/search\/searchResultByGet/,
    /^\/search\/searchResultByPost/,
    /^\/search\/getResultForML/,
    /^\/search\/autoTuningResults/,
    /^\/createJwt/,
];
const JWTSecure=[/^\/search\/SUSearchResults/,/^\/admin\/contentSources\/byCaseUidAuth/,/^\/agentHelper\/query/,/^\/ai\/authSURecommendation/]

//code for get /DiscussionsReadyToBecomeArticles api "D" to "d"
router.use(checkDiscussionUrl);
router.use(sessionValidation, csrfProtection);
router.use(accessTabs);
//Will reach here with error hence using 4 arguments of middleware
router.use(publicUrlCheck);
router.use(oAuthValidation);
router.use(provisionKeyCheck);
router.use(JWTSecureCheck);
router.use((error, req, res, next) => {
    let urlObject = url.parse(req.url, true);
    if (/\/login/.test(urlObject.path)
        || /\/admin-login/.test(urlObject.path)
        || /\/saml/.test(urlObject.path)
        || /\/validateUser/.test(urlObject.path)
    ) {
        csrfProtection(req, res, e => {
            // console.log(e);
            next();
        });
    }
    else {
        next(error);
    }
});
// WE are using these routes for EP-addon to impliment csrf auth
router.use((error, req, res, next) => {
    let urlObject = url.parse(req.url, true);
    if (/\/api\/v2_cs/.test(urlObject.path)
        || /\/escalationPrediction/.test(urlObject.path)
        || /\/admin\/contentSources\/fetchObjects/.test(urlObject.path)
    ) {
        csrfProtection(req, res, e => {
            if(e){
                console.log(e);
                next(e);
            }
            else{
                next();
            }
        });
    }
    else {
        next(error);
    }
});


router.use('/downloads',(error, req, res, next)=>{
    let allowedMethods = ['GET', 'POST'];
    if(req.session.email && allowedMethods.includes(req.method))
        next();
    else
        next(error);
});
router.use('/downloads',express.static(DIRNAME + '/crawlingLogs'));


function checkDiscussionUrl(req,res,next){
     let endUrl = req.url.split("?")[1];
     let requestUrl = req.path.split("/");
    if(requestUrl[requestUrl.length-1] == "DiscussionsReadyToBecomeArticles"){
        requestUrl[requestUrl.length-1] = "discussionsReadyToBecomeArticles";
        req.path = requestUrl.join("/");
        req.url = requestUrl.join("/") + "?" +  endUrl;
    }
    next();
}

function sessionValidation(req, res, next) {
    let allowedMethods = ['GET', 'POST'];
    if (req.session.email && allowedMethods.includes(req.method))
        next();
    else {
        next(["Invalid Session"]);
    }
}

function accessTabs(req, res, next) {
    let url = req.headers.referer;
    let route = url.split('/').pop();
    if (req.session.roleId == 2 && route != "home" && route != "account" && url.indexOf('/dashboard/') > -1) {
        if ( req.session.selectedTabs == req.headers.selectedtabs ) {
            next();
        }
        else 
            next(["Invalid Session"]);
    }
    else
        next();
}

function publicUrlCheck(error, req, res, next) {
    var urlObject = url.parse(req.url, true);
    let valid = false;
    for (let i = 0; i < openUrls.length; i++) {
        const p = openUrls[i];
        if (p.test(urlObject.path)) {
            valid = true;
            break;
        }
    }
    if (valid) {
        if (/\/login/.test(urlObject.path)
            || /\/admin-login/.test(urlObject.path)
            || /\/saml/.test(urlObject.path)
            || /\/validateUser/.test(urlObject.path)
        ) {
            csrfProtection(req, res, e => {
                // console.log(e);
                next();
            });
        }
        else {
            next();
        }
    }
    else
        next(error);
}

function oAuthValidation(error, req, res, next) {
    var urlObject = url.parse(req.url, true);
    let isoAuthSec = false;
    for (let i = 0; i < oAuthSecure.length; i++) {
        if (oAuthSecure[i].test(urlObject.path)) {
            isoAuthSec = true;
            break;
        }
    }
    if (isoAuthSec) {
        req.isoAuthSec = true;
        authenticate()(req, res, next);
    }
    else
        next(error);
}

function provisionKeyCheck(error, req, res, next) {
    var urlObject = url.parse(req.url, true);
    let isProvisionKeysecure = false;
    for (let i = 0; i < provisonKeySecure.length; i++) {
        if (provisonKeySecure[i].test(urlObject.path)) {
            isProvisionKeysecure = true;
            break;
        }
    }
    if (isProvisionKeysecure) {
        commonFunctions.getAutoprovisonToken("", req.body.accessToken, req,u => {
            if (u) {
                res.statusCode = 200;
                next();
            }
            else
                next(error);
        });
    }
    else {
        next(error);
    }
}

function JWTSecureCheck(error, req, res, next) {
  var urlObject = url.parse(req.url, true);
  let isJWTKeysecure = false;
  for (let i = 0; i < JWTSecure.length; i++) {
    if (JWTSecure[i].test(urlObject.path)) {
      isJWTKeysecure = true;
      break;
    }
  }
  if (isJWTKeysecure) {
    commonFunctions.getAccessToken(req,u => {
      if (u) {
        var authToken=req.headers.authorization
        authToken=authToken.replace('bearer ','')
        res.statusCode = 200;
        var payload=authToken.split('.')[1]
        var UserInfo=decode(u.access_token,payload)

        authToken=authToken.replace(payload,base64url(UserInfo))
        var id=JSON.parse(UserInfo).UserId

        jwt.verify(authToken,Buffer.from(u.access_token+id, 'base64') , function(err, decoded) {
          res.header("Access-Control-Request-Headers", "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken");
          res.setHeader("Access-Control-Allow-Credentials", true);
          res.setHeader("Access-Control-Allow-Origin", req.headers.origin);
          res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Key,  X-UserToken,Authorization")
          if(!err)// bar
          {
            req.body.caseUid = decoded.caseUid || "";
            req.body.UserId = decoded.UserId || "";
            req.body.ProfileID = decoded.ProfileID || "";
            req.body.UserType = decoded.UserType  || "";
            req.body.ContactId = decoded.ContactId || ""
            req.body.AccountID = decoded.AccountId  || ""
            req.body.email = decoded.email || ""
            req.body.TimeZoneSidKey = decoded.TimeZoneSidKey || "America/Los_Angeles";
            console.log(req.body);
            next()
          }
          else if(err.message=='jwt expired')
          {
             var response={flag:402,message:"Authentication Expired"}
            res.send((response));
          }
          else
          {
            var response={flag:500,message:"Invalid Authentication"}
            res.send((response));
          }
        });

      }
      else
        next(error);
    });
  }
  else {
    next(error);
  }
}
function decode(password,cryptoStr)
{

  var buf = new Buffer(cryptoStr, 'base64');
  var iv = buf.slice(0, 16);
  var crypt = buf.toString('base64', 16);

  var decipher = crypto.createDecipheriv('aes-256-cbc', password, iv);
  var dec = decipher.update(crypt,'base64','utf-8');
  dec += decipher.final('utf-8');
  return dec;
}

module.exports = router;
