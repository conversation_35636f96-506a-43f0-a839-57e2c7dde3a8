var express = require('express');
var router = express.Router();
var commonFunctions = require('../../utils/commonFunctions');
const config = require('config');
const { mergeFields } = require('./service');

router.get('/getMergeFields', async(req,res,next)=>{
    try {
        const tenantId = req.headers['tenant-id'];

        const response = await mergeFields.list(tenantId);
        res.status(200).json({ message: 'Merge field added successfully', data: response });
    }
    catch(err) {
        res.status(500).send(err);
    }
});

router.post('/addMergeField', async(req,res,next)=>{
    try {
        const payload = req.body;
        const tenantId = req.headers['tenant-id'];

        if (!tenantId || !payload.name || !payload.label) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        const response = await mergeFields.createNew(tenantId, payload);
        res.status(200).json({ message: 'Merge field added successfully', data: response });
    }
    catch(err) {
        res.status(500).send({ message: err.message });
    }
});

router.post('/deleteMergeField', async(req,res,next)=>{
    try {
        const payload = req.body;
        const tenantId = req.headers['tenant-id'];

        const response = await mergeFields.delete(payload.mergeFieldId, tenantId);
        res.status(200).json({ message: 'Merge field removed successfully', data: response });
    }
    catch(err) {
        res.status(500).send(err);
    }
});

router.get('/checkAvailable', async (req, res, next) => {
    try {
        const query = req.query;
        const tenantId = req.headers['tenant-id'];

        const response = await mergeFields.checkAvailable(query, tenantId);
        res.status(200).json({ message: 'Checked merge field availability', data: response });
    }
    catch(err) {
        res.status(500).send(err);
    }
});

router.post('/republishMergeField', async(req,res,next)=>{
    try {
        const payload = req.body;
        const tenantId = req.headers['tenant-id'];

        if (!tenantId || !payload.mergeFieldId) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        const response = await mergeFields.republishMergeField(payload.mergeFieldId, tenantId);
        res.status(200).json(response);
    }
    catch(err) {
        res.status(500).send({ message: err.message });
    }
});

module.exports = {
    router: router
}