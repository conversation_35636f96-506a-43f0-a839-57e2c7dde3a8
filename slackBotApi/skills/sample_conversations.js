
var request = require("request");
var sentiment = require('sentiment');
var emotional = require("emotional");
var compromise = require('compromise');
var async = require('async');

var userFirstName = 'My friend';
module.exports = function (controller) {

    controller.hears(['color'], 'direct_message,direct_mention', function (bot, message) {

        bot.startConversation(message, function (err, convo) {
            convo.say('This is an example of using convo.ask with a single callback.');

            convo.ask('What is your favorite color?', function (response, convo) {

                convo.say('Cool, I like ' + response.text + ' too!');
                convo.next();

            });
        });

    });


    controller.hears(['what is (.*)?'], 'direct_message,direct_mention', function (bot, message) {
        console.log(message.match);
        bot.startConversation(message, function (err, convo) {
            convo.say("SearchUnify is the right enterprise search solution for you. Get a live demo in your environment of choice and see how AI-based search transforms user engagement and helps you achieve your business objectives.");
            convo.ask("Do you want me to send more information about Searchunify", function (response, convo) {
                if (response.text.toUpperCase() == "NO") {
                    convo.say("NO")
                } else {
                    convo.say("You can get more information on our website <https://www.searchunify.com/|SearchUnify>");
                }
                convo.next();
            })
        })
    })

    controller.on(['direct_mention'], function (bot, message) {
        console.log("bot data is", JSON.stringify(message));
        if (message.channel) {
            getSlackResults(message, [], (err, docs) => {
                if (docs.body.result && docs.body.result.hits && docs.body.result.hits.length) {
                    var join = "\n";
                    var messagesLinks = [];
                    var attachments = [];
                    for (var i = 0; i < docs.body.result.hits.length; i++) {
                        // var a = docs.body.result.hits[i]._id.split("/");
                        // console.log(a[a.length-1]);
                        // var keyWord = a[a.length-1];
                        // keyWord = keyWord.slice(0,keyWord.length-4)

                        var TitleToDisplay = parseHtmlEntities(docs.body.result.hits[i].highlight.TitleToDisplay[0].replace(/<\/span>/g, '_').replace(/<span class='highlight'>/g, '_'));
                        var SummaryToDisplay = docs.body.result.hits[i].highlight.SummaryToDisplay[0].replace(/<\/span>/g, '_*').replace(/<span class='highlight'>/g, '*_');
                        var customURL = docs.body.result.hits[i].href.includes('https') ? docs.body.result.hits[i].href : "https://app.slack.com";

                        attachments.push({
                            "text": `<${customURL}|*${TitleToDisplay.trim()}*>`,
                            "fields": [
                                {
                                    "value": SummaryToDisplay,
                                    "short": false
                                }
                            ],
                            "mrkdwn_in": ["text", "fields"]





                            // "text": `<${customURL}|*${TitleToDisplay.trim()}*> \n ${SummaryToDisplay}`,

                            // "attachments": [
                            //     {
                            //         "color": "#36a64f",
                            //         "title":str4,
                            //         "title_link": docs.body.result.hits[i]._id,
                            //         "text": docs.body.result.hits[i].highlight.SummaryToDisplay[0],
                            //         "footer": "SearchUnify",
                            //         "footer_icon": "https://searchunify.searchunify.com/resources/Asset-Library/su-blog.PNG"
                            //     }
                            // ]

                            // "attachments": [
                            //     {
                            //         "color": "#36a64f",
                            //         "text": `<${docs.body.result.hits[i]._id}|*${TitleToDisplay.trim()}*>`,
                            //         "fields": [
                            //             {
                            //                 "value":SummaryToDisplay,
                            //                 "short": false
                            //             }
                            //         ],
                            //         "mrkdwn_in": ["text","fields"],
                            //         "footer": "SearchUnify",
                            //         "footer_icon": "https://searchunify.searchunify.com/resources/Asset-Library/su-blog.PNG"
                            //     }
                            // ]




                        })
                        // join += `*<${docs.body.result.hits[i]._id}|${docs.body.result.hits[i].highlight.TitleToDisplay[0]}>*\n`;
                    }
                    bot.reply(message, { text: "SearchUnify Results...", attachments: attachments });
                } else
                    bot.reply(message, "Sorry no results found");
            })
        }
        else
            bot.reply(message, "Sorry no results found");
    })


    controller.hears(['^hello$', '^hi', '^hey'], 'direct_message,direct_mention', function (bot, message) {
        console.log(message.text);
        bot.startConversation(message, function (err, convo) {
            convo.addQuestion('Hey! How are you?', function (response, convo) {

                // Quick Sentiment analysis
                emotional.load(function () {
                    var sentimentResponse = emotional.positive(response.text, 0.4); // { polarity: [-1,1], subjectivity: [0,1], assessments: ... };
                    //console.log(sentimentResponse);
                    userMood = sentimentResponse;
                    // Lets do a quick sentiment analysis
                    var ReplyOnGreetResponse = '';
                    if (!sentimentResponse) { // Negative
                        ReplyOnGreetResponse = 'Hey! I am sure things would be fine.';
                    } else {
                        ReplyOnGreetResponse = 'That\'s great.';
                    }

                    // Check for a similar question for Bot
                    compo = compromise(response.text);
                    var pronouns = compo.match('(are|about) #Pronoun').out('freq');
                    console.log(pronouns);
                    console.log(compo.tag());
                    var attachedResponse = '';
                    for (index = 0; index < pronouns.length; index++) {
                        if (pronouns[index].normal == 'are you' || pronouns[index].normal == 'You') {
                            ReplyOnGreetResponse += ' I am good. Thanks for asking! ';
                        }
                    }
                    convo.say(ReplyOnGreetResponse);
                    convo.next();

                    // Check for FirstName
                    findName(convo, (userFirstName) => {
                        // Greet the user with his First Name and prompt him to choose the top level topic
                        console.log(userFirstName);
                        if (userFirstName != 'My friend') {
                            convo.say('Hello ' + userFirstName + '! Now, We are friends.');
                        } else {
                            convo.say('I don\'t think I can pronounce that :(. I will just call you My friend!');
                        }
                        convo.next();
                        // openTopLevelHelpDialogFlow(convo);
                        convo.next();
                    });

                }, {}, 'default');
            })
        });
    });


    function findName(convo, cb) {
        convo.addQuestion('Could you please tell me your name?', function (response, convo) {
            compo = compromise(response.text);
            personName = compo.people().out('freq');
            if (personName.length > 0) {
                cb(personName[0].normal);
            } else {
                cb("My friend");
            }
        })
    }

    controller.hears(['^bye$', '^Thank you', '^awesome'], 'direct_message,direct_mention', function (bot, message) {
        bot.reply(message, {
            "mrkdwn": true,
            "text": "Thanks, *searchify*! for being a fantastic colleague.\n Follow us on <a href='https://www.facebook.com/grazitti.interactive'>FB</a>"
        });
    });


    controller.hears(['question'], 'direct_message,direct_mention', function (bot, message) {

        bot.createConversation(message, function (err, convo) {

            // create a path for when a user says YES
            convo.addMessage({
                text: 'How wonderful.',
            }, 'yes_thread');

            // create a path for when a user says NO
            // mark the conversation as unsuccessful at the end
            convo.addMessage({
                text: 'Cheese! It is not for everyone.',
                action: 'stop', // this marks the converation as unsuccessful
            }, 'no_thread');

            // create a path where neither option was matched
            // this message has an action field, which directs botkit to go back to the `default` thread after sending this message.
            convo.addMessage({
                text: 'Sorry I did not understand. Say `yes` or `no`',
                action: 'default',
            }, 'bad_response');

            // Create a yes/no question in the default thread...
            convo.ask('Do you like cheese?', [
                {
                    pattern: bot.utterances.yes,
                    callback: function (response, convo) {
                        convo.gotoThread('yes_thread');
                    },
                },
                {
                    pattern: bot.utterances.no,
                    callback: function (response, convo) {
                        convo.gotoThread('no_thread');
                    },
                },
                {
                    default: true,
                    callback: function (response, convo) {
                        convo.gotoThread('bad_response');
                    },
                }
            ]);

            convo.activate();

            // capture the results of the conversation and see what happened...
            convo.on('end', function (convo) {

                if (convo.successful()) {
                    // this still works to send individual replies...
                    bot.reply(message, 'Let us eat some!');
                    // and now deliver cheese via tcp/ip...
                }
            });
        });

    });

    controller.on('slash_command', function (bot, message) {
        bot.replyPublic(message, `Sending Searchunify Facets ` + `${message.text ? 'for ' + message.text : ''}` + '...');
        if (message.channel) {
            async.auto({
                "getFacets": cb => {
                    getSlackResults(message, [], (err, docs) => {
                        if (docs && docs.body && docs.body.aggregationsArray) {
                            var join = "\n";
                            var messagesLinks = [];
                            var sendJson = [{ "type": "section", "text": { "type": "mrkdwn", "text": message.text ? message.text : "Select facets to filter SU results" }, "accessory": { "type": "multi_static_select", "placeholder": { "type": "plain_text", "text": "Select facets to filter SearchUnify results", "emoji": true }, "option_groups": [] } }]
                            for (var i = 0; i < docs.body.aggregationsArray.length; i++) {
                                if (docs.body.aggregationsArray[i].values && docs.body.aggregationsArray[i].values.length) {
                                    sendJson[0].accessory.option_groups.push({
                                        "label": {
                                            "type": "plain_text",
                                            "text": docs.body.aggregationsArray[i].label.slice(0, 30)
                                        }, options: []
                                    });
                                }
                                for (var j = 0; j < docs.body.aggregationsArray[i].values.length; j++) {
                                    if ((docs.body.aggregationsArray[i].values[j].displayName && docs.body.aggregationsArray[i].values[j].displayName.length) || (docs.body.aggregationsArray[i].values[j].Contentname && docs.body.aggregationsArray[i].values[j].Contentname.length)) {
                                        sendJson[0].accessory.option_groups[sendJson[0].accessory.option_groups.length - 1].options.push({
                                            "text": {
                                                "type": "plain_text",
                                                "text": docs.body.aggregationsArray[i].values[j].displayName ? docs.body.aggregationsArray[i].values[j].displayName.slice(0, 30).replace(/\n/g, " ") : docs.body.aggregationsArray[i].values[j].Contentname.slice(0, 30).replace(/\n/g, " ")
                                            },
                                            "value": (docs.body.aggregationsArray[i].key.slice(0, 30) + '@===@' + docs.body.aggregationsArray[i].values[j].Contentname)
                                        })
                                    }
                                }
                            }
                            if (sendJson[0] && sendJson[0].accessory && sendJson[0].accessory.option_groups && sendJson[0].accessory.option_groups.length) {
                                if (!sendJson[0].accessory.option_groups[sendJson[0].accessory.option_groups.length - 1].options.length)
                                    sendJson[0].accessory.option_groups.pop();
                                sendJson = JSON.stringify(sendJson);
                                cb(null, sendJson)
                            } else {
                                bot.replyInteractive(message, "Sorry no results found");
                                cb(null, '')
                            }
                        }
                    })
                },
                "sendChatResponse": ["getFacets", (dataFromAbove, cb) => {
                    if (dataFromAbove.getFacets) {
                        var options2 = {
                            method: 'POST',
                            url: 'https://slack.com/api/chat.postMessage',
                            headers: {
                                'content-type': 'application/json',
                                'Authorization': 'Bearer ' + config.get('slackBot.token')
                            },
                            body: {
                                "channel": message.channel,
                                "text": "Select SearchUnify Facets to filter results",
                                "blocks": dataFromAbove.getFacets
                            },
                            json: true
                        };

                        request(options2, function (err, docs) {
                            cb(null, []);
                        })
                    }
                }]
            }, (error, results) => {
                console.log('sent chat response');
            });
        }
    })

    controller.on('block_actions', function (bot, message) {
        message.text = message.message.blocks[0].text.text || "";
        let facets = message.actions[0].selected_options;
        let aggregations = [];
        facets.map(x => {
            var isExists = aggregations.findIndex(y => y.type == x.value.split('@===@')[0]);
            if (isExists > -1)
                aggregations[isExists].filter.push(x.value.split('@===@')[1])
            else
                aggregations.push({ "type": x.value.split('@===@')[0], "filter": [x.value.split('@===@')[1]] });
        })
        getSlackResults(message, aggregations, (err, docs) => {
            if (docs.body.result && docs.body.result.hits && docs.body.result.hits.length) {
                var attachments = [];
                for (var i = 0; i < docs.body.result.hits.length; i++) {
                    var TitleToDisplay = parseHtmlEntities(docs.body.result.hits[i].highlight.TitleToDisplay[0].replace(/<\/span>/g, '_').replace(/<span class='highlight'>/g, '_'));
                    var SummaryToDisplay = docs.body.result.hits[i].highlight.SummaryToDisplay[0].replace(/<\/span>/g, '_*').replace(/<span class='highlight'>/g, '*_');
                    var customURL = docs.body.result.hits[i].href.includes('https') ? docs.body.result.hits[i].href : "https://app.slack.com";

                    attachments.push({
                        "text": `<${customURL}|*${TitleToDisplay.trim()}*>`,
                        "fields": [
                            {
                                "value": SummaryToDisplay,
                                "short": false
                            }
                        ],
                        "mrkdwn_in": ["text", "fields"]
                    })
                }
                bot.replyInteractive(message, { "text": "SearchUnify Results...", "attachments": attachments });
            } else
                bot.replyInteractive(message, "Sorry no results found")
        })
    });

    function getSlackResults(message, aggregationsArray, cb) {
        connection.query(`SELECT uid FROM search_clients WHERE channel_id=?`, message.channel, (e, r) => {
            if (r[0] && r[0].uid) {
                var options = {
                    method: 'POST',
                    url: config.get('adminURL') + '/search/searchResultByPost',
                    headers: {
                        'content-type': 'application/json'
                    },
                    body: {
                        "searchString": message.text,
                        "from": 0,
                        "sortby": "_score",
                        "orderBy": "desc",
                        "aggregations": aggregationsArray,
                        "resultsPerPage": 5,
                        "accessToken": "b72cbbab2728563ec74e93aa3223a7dc",
                        "uid": r[0].uid
                    },
                    json: true
                };

                request(options, function (err, docs) {
                    cb(null, docs);
                })
            }
        })
    }

    function getUserInfo(message, cb) {
        var options3 = {
            method: 'GET',
            url: 'https://slack.com/api/users.info?user=' + message.user,
            headers: {
                'content-type': 'application/x-www-form-urlencoded',
                'Authorization': 'Bearer ' + config.get('slackBot.token')
            }
        };

        request(options3, function (err, docs) {
            let userInfo = docs.body ? (JSON.parse(docs.body) ? JSON.parse(docs.body).user.profile.email : '') : '';
            cb(null, userInfo);
        })
    }

    function parseHtmlEntities(str) {
        return str.replace(/&#([0-9]{1,3});/gi, function (match, numStr) {
            var num = parseInt(numStr, 10);
            return String.fromCharCode(num);
        });
    }
}