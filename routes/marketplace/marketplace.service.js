
const fetch = require('node-fetch');
const config = require('config');
const request = require('request');
const commonFunctions = require('../../utils/commonFunctions');
var appVariables = require('../../constants/appVariables')
const repository = require('./marketplace.repository');
const aes256 = require('nodejs-aes256');
const jwt = require('../../routes/jwt');
const fs = require('fs');
const emailTemplates = require('./../emailTemplates');
const searchunifyEmail = require('../../Lib/email');
const { promisify } = require('util');
const readdir = promisify(require('fs').readdir);
const manifest = require('./localMarketplace/custom/manifest.json');
const { fetchClientInfo } = require('../../utils/commonFunctions');


const getMarketplaceAddons = async (headers) => {
    let response = await repository.getInstalledAddons(headers['tenant-id']);
    let marketplaceAddons = await getAddonsList(0, 50, []);
    if ( marketplaceAddons && marketplaceAddons.length) {
        marketplaceAddons.map(allAddons => {
            if (allAddons.appId > 1 ) {
                allAddons['addonAccess'] = headers.session.apps ? headers.session.tenantApps.filter(app => app.id == allAddons.appId).length > 0 : false;
            }
            if(response.length) {
                response.map(installedAddons => {
                    if (installedAddons.uid == allAddons.uid) {
                        commonFunctions.errorlogger.info(`Matched addons found`);
                        allAddons.app['addons_status'] = installedAddons;
                    } else if (typeof allAddons.app['addons_status'].is_installed == 'undefined') {
                        allAddons.app['addons_status'].is_installed = 0;
                    }
                });
            } else {
                allAddons.app['addons_status'].is_installed = 0;
            }
        });
    }
    return marketplaceAddons;
}

const addUserAppMapping = (headers, appId) => {
    let dataString = `{"appId":[${appId}],"tenantId":"${headers['tenant-id']}","email":"${headers.session.email}"}`;

    let options = {
        url: `${config.get('authUrl')}/tenant/editTenants`,
        method: 'POST',
        headers: {
            "content-type": headers['content-type'],
            "tenant-id": headers['tenant-id'],
            cookie: headers.cookie,
            session: headers.session
        },
        body: dataString
    };

    console.log('options for addUserMapping', JSON.stringify(options));
    function callback(error, response, body) {
        if (!error && response.statusCode == 200) {
            console.log('after user app registration ', body);
        }
    }

    request(options, callback);

}

const sendEmailToSearchunify = async (value) => {
    let emailObject = {
        to: config.get('salesEmail'),
        subject: `Demo requested for ${value.app || ''}`,
        html: emailTemplates.demoRequestTemplate(value)
    }
    searchunifyEmail.sendEmail(emailObject.to, emailObject.subject, emailObject.html, (error, resp) => commonFunctions.errorlogger.log(resp));
}

const installMarketplaceAddons = async (addon, headers) => {
    try {
        if (addon.name === 'Escalation Predictor' && !addon.oauth) {
            return {
                "status": "FAIL",
                "oauth": false
            };
        }
        let object = {}, flag = false;
        delete addon.routeUrl;

        let res = await repository.getInstalledAddons(headers['tenant-id']);

        for (let i = 0; i < res.length; i++) {

            if (addon.app && res[i].addon_id === addon.app.addon_id) {
                return [];
            }

        }
        if (addon.app) {
            // check manifest of the addon for permissions
            if (addon.oauth && !addon.oauth.authenticated) {
                return {
                    "status": "OK",
                    "oauth": true
                };
            }
            // validate username and password against the selected app
            if (addon.oauth && addon.oauth.authenticated) {
                let accessTokenAndRefreshToken = await getAccessTokenAndRefreshToken(addon);
                if (accessTokenAndRefreshToken.access_token) {
                    let send = await sendAccessTokenAndRefreshToken(accessTokenAndRefreshToken, addon, headers['tenant-id']);
                } else {
                    throw {
                        success: false,
                        message: "Could Not save Token"
                    }
                }
            }
            // after validation go further on next steps
            flag = true;
            let kcsUser = headers.session.tenantApps ? headers.session.tenantApps.filter(app => app.id == 2).length > 0 : false;
            let suvaUser = headers.session.tenantApps ? headers.session.tenantApps.filter(app => app.id == 3).length > 0 : false;

            let addonUrl;
            let localMarketPlaceSubPath;
            if(addon.app && addon.app.name.toLowerCase() === 'suva') {
                localMarketPlaceSubPath = 'suva';
            } else if (addon.app && addon.app.name.toLowerCase() === 'knowbler') {
                localMarketPlaceSubPath = 'kcs';
            } else if(addon.app && addon.app.name.toLowerCase() === 'Escalation Predictor'.toLowerCase()) {
                localMarketPlaceSubPath = 'custom';
            }

            if(config.get('marketplace.local')){
                let localMarketPlacePath = config.get('marketplace.localPath');
                if(fs.existsSync(localMarketPlacePath+localMarketPlaceSubPath+'/manifest.json')){
                    let data = fs.readFileSync(localMarketPlacePath+localMarketPlaceSubPath+'/manifest.json', 'utf-8');
                    data = JSON.parse(data);
                    addonUrl = data["addonUrl"];
                }
            }
            
            object = {
                "name": addon.app.name,
                "addon_id": addon.app.addon_id,
                "is_installed": ((addon.uid == "f7243bdd-b995-11ec-ad68-0242ac120014" && !kcsUser) || (addon.uid == '19d9a048-dbfa-11ec-a60e-0242ac120015' && !suvaUser)) ? 2 : 1,
                "url": addonUrl,
                "installed_at": (new Date()).toISOString(),
                "is_search_client": addon.searchClientEnabled,
                "search_client_url": addon.clientUrl,
                "uid": addon.uid,
                "logo": addon.app.logo,
                "platform": addon.app.platform ? addon.app.platform.toLowerCase() : config.get("marketplace.platform").toLowerCase(),
                "api_name": addon.name,
                "installed_by": headers.session.email
            }
            if (parseInt(addon.app.is_installed) == 0)
                object["id"] = addon.app.addons_status.id;
        }
        let params = flag ? object : addon;
        if (flag && addon.uid == "f7243bdd-b995-11ec-ad68-0242ac120014" || addon.uid == '19d9a048-dbfa-11ec-a60e-0242ac120015') {
            await fetchClientInfo(headers['tenant-id'],(err,data)=>{
                let accountName = "";
                if(err){
                    res.status(400).send({ "message": 'Unable to retrieve information'});
                }else{
                    if (data && data.name) {
                        accountName = data.name
                    }
                }
                const userDetails = {
                    name: headers.session.name,
                    email: headers.session.email,
                    app: addon.app.name,
                    account: accountName
                }
                sendEmailToSearchunify(userDetails);
            });
            
        }
        let response = await repository.save(params,headers['tenant-id']);

        return response;

    } catch (err) {
        commonFunctions.errorlogger.log(err);
        return err
    }

}

const activateMarketplaceAddons = async (addon, headers) => {
    try {
        const params ={
            "uid" : addon.uid,
            "installed_at": (new Date()).toISOString()
        }
        let response = await repository.activate(params,headers['tenant-id']);

        return response;
    } catch (err) {
        commonFunctions.errorlogger.log(err);
        
        return err
    }
}

const getAccessTokenAndRefreshToken = async (addon) => {
    try {
        let  headers = {
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/x-www-form-urlencoded',
            'authorization': 'Basic ' + new Buffer(addon.client_id + ":" + addon.client_secret).toString("base64")
        };
       
       let data = "grant_type=password&username="+ encodeURIComponent(addon.username) + "&password="+addon.password;
        var options = {
            method: 'POST',
            headers: headers,
            body: data
        };
        const result = await fetch(`${config.get('adminURL')}/oauth/token`, options);
        const response = await result.json();
    
        return response;
        
    } catch (err) {
        throw err
    }
   
}
const deleteEpAddonData = (tenantId) => {
    return new Promise(async (resolve, reject) => {
        try {
            let headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            };
            data = {
                tenantId: tenantId
            }
            let options = {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(data)
            };
            const result = await fetch(`${manifest.epServiceUrl}${manifest.oauth.deleteAddon}`+'?s_key='+config.get("epaddon.secretKey"), options);
            const response = await result.json();
            resolve(response);

        }
        catch (err) {
            reject(err);
        }
    })

}

const sendAccessTokenAndRefreshToken = async (accessTokenAndRefreshToken, oauthCred, tenantId) => {
    let  headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
     };

    let data = accessTokenAndRefreshToken;
    data['client_secret'] = oauthCred.client_secret;
    data['tenantId'] = tenantId;
    data['instance'] = config.get('adminURL');
    var options = {
         method: 'POST',
         headers: headers,
        body: JSON.stringify(data)
     };
    const result = await fetch(`${manifest.epServiceUrl}${manifest.oauth.context}`+'?s_key='+config.get("epaddon.secretKey"), options);
     const response = await result.json();

     return response;
 }

const installedAddons = async (headers) => {
    let response = await repository.getInstalledAddons(headers['tenant-id']);
    let marketplaceAddons = await getAddonsList(0, 50, []);
    let savedAddons = [];
    let apps = headers.session.apps;
    // console.log('apps', headers.session.apps);
    let kcsUser = headers.session.apps ? headers.session.apps.filter(app => app.appId == 2).length > 0 : false;
    headers['instance'] = config.get('adminURL');
    if (environment.configuration != "production") {
        headers.instance = config.get("marketplace.localUrl");
    }
    let appIdMap = {};
    let appIdsUid = {};
    // console.log(JSON.stringify(marketplaceAddons));
    marketplaceAddons.map(addon => {
        appIdsUid[addon.uid] = addon.appId;
        if(addon.app.open_in) {
            appIdMap[addon.uid] = addon.app.open_in;
        }
    });
    let copyHeaders = JSON.parse(JSON.stringify(headers));
    copyHeaders.cookie = copyHeaders.cookie.split('; ')
        .filter(c => { 
            return c.split('=')[0] === 'connect.admin_sid' || c.split('=')[0] === '_csrf'
            || c.split('=')[0] === 'connect.sid' || c.split('=')[0] === 'smartFacets'
        }).join('; ');
    let jwtToken = await jwt.addonJwt(copyHeaders);
    response.map(element => {
        if (element.is_installed) {
            let url;
            if (element.platform == "angular") {
                url = `${element.url}/root?jwt=${jwtToken}`;
            }
            else {
                url = `${element.url}/root?jwt=${jwtToken}`;
            }
            element['addonUrl'] = url;
            element['jwtToken'] = jwtToken;
            element['unauthorized'] = apps.filter(app => appIdsUid[element.uid] === app.appId).length == 0;
            element['open_in'] = appIdMap[element.uid] ? appIdMap[element.uid] : 'iframe';
            if(element.uid === 'f7243bdd-b995-11ec-ad68-0242ac120014') {
                element['user_allowed'] = kcsUser;
            } else {
                element['user_allowed'] = true;
            }
            savedAddons.push(element);
        }
    });
    return savedAddons;
}


const getSearchAddons = async (uid,headers) => {
    let response = await repository.getSearchClientAddons(uid.uid,headers['tenant-id']);
    return response;
}

const deleteAddons = async (params, headers) => {
    let response = await repository.deleteAddon(params.id, headers['tenant-id']);
    return response;
}

const jwtVerification = async (params) => {
    if (params.jwt) {
        let response = await jwt.verifyAddonJwt(params.jwt);
        return response;
    }
    else {
        return 'jwt not found';
    }
}

const generateUuid = function (){
    var dt = new Date().getTime();
    var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (dt + Math.random()*16)%16 | 0;
        dt = Math.floor(dt/16);
        return (c=='x' ? r :(r&0x3|0x8)).toString(16);
    });
    return uuid;
}

const getAddonsList = async (page, size, addons) => {

    if(config.get('marketplace.local')){
        let localMarketPlacePath = config.get('marketplace.localPath');
        const localAddons = await readdir(localMarketPlacePath);
        let appsData = [];
        for (const addon of localAddons) {
            commonFunctions.errorlogger.info(addon);
            if(fs.existsSync(localMarketPlacePath+addon+'/manifest.json')){
                let obj = {};
                let data = fs.readFileSync(localMarketPlacePath+addon+'/manifest.json', 'utf-8');
                data = JSON.parse(data);
                
                data.app["addons_status"] = {};
                data.app["addon_id"] = generateUuid();
                data.app["addonUrl"] = data["addonUrl"];
                
                addons.push(data);
            }
        }
        return addons;
    }else{
        let url = config.get("marketplace.host") + `/api/marketplace/packages?page=${page}&size=${size}`;
        let options = {
            headers: {
                'authorization': config.get("marketplace.authorization")
            }
        }
        const result = await fetch(url, options);
        const response = await result.json();
        response.data.forEach(x => {
            x.app["addons_status"] = {};
            x.app["addon_id"] = x["_id"];
            x.app["addonUrl"] = x["addonUrl"];
        });
        addons = addons.concat(response.data);
        if (response.data.length) {
            page = page + 1;
            return await getAddonsList(page, size, addons);
        }
        else {
            return addons;
        }
    }
}

const installMarketplaceAddonsFromAuthService = async (tenantId, addonId, tenantEmail) => {
    try {
        let addons = [];
        await getAddonsList(0, 10, addons);

        let appUids = {
            2: 'f7243bdd-b995-11ec-ad68-0242ac120014',
            3: '19d9a048-dbfa-11ec-a60e-0242ac120015'
        }
        let addon;
        addon = addons.filter(a => a.uid === appUids[addonId]);

        
        if (addon && addon.length) {
            addon = addon[0];
            
            // after validation go further on next steps
            flag = true;
            object = {
                "name": addon.app.name,
                "addon_id": addon.app.addon_id,
                "is_installed": 1,
                "url": addon.addonUrl,
                "installed_at": (new Date()).toISOString(),
                "is_search_client": addon.searchClientEnabled,
                "search_client_url": addon.clientUrl,
                "uid": addon.uid,
                "logo": addon.app.logo,
                "platform": addon.app.platform ? addon.app.platform.toLowerCase() : config.get("marketplace.platform").toLowerCase(),
                "api_name": addon.name,
                "installed_by" : tenantEmail
            }
            if (parseInt(addon.app.is_installed) == 0)
                object["id"] = addon.app.addons_status.id;

            let response = await repository.save(object, tenantId);
    
            return response;
        }
        return null;
    } catch (err) {
        console.log("Error while installing marketplace addon while registering tenant", err)
        return err
    }
}

module.exports = {
    getMarketplaceAddons,
    installMarketplaceAddons,
    activateMarketplaceAddons,
    installedAddons,
    getSearchAddons,
    deleteAddons,
    jwtVerification,
    deleteEpAddonData,
    installMarketplaceAddonsFromAuthService
}
