module.exports = {
  objects: [
    {
      name: 'articles',
      label: 'Article Versions',
      status: 1,
      boosting_factor: 1,
      base_url: '', // Will be constructed dynamically
      fields: [
        {
          name: 'id', label: 'ID', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'title', label: 'Title', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'body', label: 'Body', type: 'string', isFilterable: 0, isSortable: 0, isSearchable: 1, isActive: 1
        },
        {
          name: 'view_href', label: 'URL', type: 'string', isFilterable: 0, isSortable: 0, isSearchable: 0, isActive: 1
        },
        {
          name: 'created_at', label: 'Created Date', type: 'datetime', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'updated_at', label: 'Updated Date', type: 'datetime', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'author_name', label: 'Author Name', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'updated_by', label: 'Updated By', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'status', label: 'Status', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'language', label: 'Language', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'category', label: 'Category', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'versionNumber', label: 'Version Number', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'parentArticle', label: 'Parent Article HREF', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        }
      ]
    },
    {
      name: 'cases',
      label: 'Cases',
      status: 1,
      boosting_factor: 1,
      base_url: '',
      fields: [
        {
          name: 'id', label: 'ID', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'title', label: 'title', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'description', label: 'Description', type: 'string', isFilterable: 0, isSortable: 0, isSearchable: 1, isActive: 1
        },
        {
          name: 'view_href', label: 'URL', type: 'string', isFilterable: 0, isSortable: 0, isSearchable: 0, isActive: 1
        },
        {
          name: 'created_at', label: 'Created Date', type: 'datetime', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'updated_at', label: 'Updated Date', type: 'datetime', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'status', label: 'Status', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'created_by', label: 'Created By Name', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'created_for', label: 'Created For Name', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'assignee_name', label: 'Assignee Name', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'type', label: 'Type', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'serviceTeam', label: 'Service Team', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'confidential', label: 'Confidential', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'caseReopenStatus', label: 'Case Reopen Status', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'labels', label: 'Confidential', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        }
      ]
    },
    {
      name: 'projects',
      label: 'Projects',
      status: 1,
      boosting_factor: 1,
      base_url: '',
      fields: [
        {
          name: 'id', label: 'ID', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'name', label: 'Name', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'description', label: 'Description', type: 'string', isFilterable: 0, isSortable: 0, isSearchable: 1, isActive: 1
        },
        {
          name: 'view_href', label: 'URL', type: 'string', isFilterable: 0, isSortable: 0, isSearchable: 0, isActive: 1
        },
        {
          name: 'created_at', label: 'Created Date', type: 'datetime', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'endDate', label: 'End Date', type: 'datetime', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'currency', label: 'Currency', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'status', label: 'Status', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'successRating', label: 'Success Rating', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'customer', label: 'Customer', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'overview', label: 'Overview', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'billable', label: 'Billable', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'objective', label: 'Objective', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'estimatedBudget', label: 'Estimated Budget', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'probabilityOfSuccess', label: 'Probability Of Success', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'realizedRevenueSavings', label: 'Realized Revenue Savings', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'inactive', label: 'Is Inactive', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'estimatedRevenueSavings', label: 'Estimated Revenue Savings', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'percentComplete', label: 'Percent Complete', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'totalSavingsRemaining', label: 'Total Savings Remaining', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'capital', label: 'Capital', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'owner', label: 'Owner', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'company', label: 'Company', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'priority', label: 'priority', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'groups', label: 'groups', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'importanceRating', label: 'Importance Rating', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'worktags', label: 'Worktags', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'problemStatement', label: 'Problem Statement', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'measuresOfSuccess', label: 'Measures Of Success', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        },
        {
          name: 'inScope', label: 'In Scope', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 0, isActive: 1
        }
      ]
    },
    {
      name: 'workers',
      label: 'Workers',
      status: 1,
      boosting_factor: 1,
      base_url: '',
      fields: [
        {
          name: 'id', label: 'ID', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'fullName', label: 'Name', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'email', label: 'Email', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'businessTitle', label: 'Title', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'primarySupervisoryOrganization', label: 'Supervisory Organization', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        },
        {
          name: 'view_href', label: 'View Href', type: 'string', isFilterable: 1, isSortable: 1, isSearchable: 1, isActive: 1
        }
      ]
    }
  ],
  fields: [
  ]
}