
const express = require("express");
const marketplace = require('./marketplace.service');
const { Response } = require('./../../utils/response');
var commonFunctions = require('./../../utils/commonFunctions');
const router = express.Router();

/**
 * Marketplace addons APIs
 */
router.get("/list", async (req, res, next) => {
    try {
        let response = await marketplace.getMarketplaceAddons(req.headers);
        commonFunctions.errorlogger.info(`Marketplace addons get successfully`);
        return res.json(new Response(true, 'Marketplace addons get successfully.', response));
    } catch (error) {
        next(error);
    }
});

router.post("/install", async (req, res, next) => {
    try {
        let response = await marketplace.installMarketplaceAddons(req.body, req.headers);
        commonFunctions.errorlogger.info(`Marketplace addons installed`);
        return res.json(new Response(true, 'Marketplace addons installed.', response));
    } catch (error) {
        next(error);
    }
});

router.get("/activate", async (req, res, next) => {
    try {
        let response = await marketplace.activateMarketplaceAddons(req.query, req.headers);
        commonFunctions.errorlogger.info(`Marketplace addons Activated`);

        return res.json(new Response(true, 'Marketplace addons activated.', response));
    } catch (error) {
        next(error);
    }
});

router.get("/getAddedAddon", async (req, res, next) => {
    try {
        let response = await marketplace.installedAddons(req.headers);
        commonFunctions.errorlogger.info(`Installed addons list`);
        return res.json(new Response(true, 'Installed addons list.', response));
    } catch (error) {
        next(error);
    }
});

router.get("/get", async (req, res, next) => {
    try {
        let response = await marketplace.getSearchAddons(req.query,req.headers);
        commonFunctions.errorlogger.info(`Search addons get successfully`);
        return res.json(new Response(true, 'Search addons get successfully.', response));
    } catch (error) {
        next(error);
    }
});

router.delete("/delete", async (req, res, next) => {
    try {
        let response = await marketplace.deleteAddons(req.body, req.headers);
        commonFunctions.errorlogger.info(`Addons deleted successfully`);
        return res.json(new Response(true, 'Addon deleted successfully.', response));
    } catch (error) {
        next(error);
    }
});

router.post("/deleteAddon", async (req, res, next) => {
    try {
        let response = await marketplace.deleteAddons(req.body, req.headers);
        
        if(req.body.id == 'bfe80f39-af0c-11eb-818a-0242ac12000a'){
            marketplace.deleteEpAddonData(req.headers['tenant-id']).catch(e => {
                commonFunctions.errorlogger.info(`Error found ${e}`);
            })
        }
        commonFunctions.errorlogger.error(`Addons deleted successfully`);
        return res.json(new Response(true, 'Addon deleted successfully.', response));
    } catch (error) {
        next(error);
    }
});

router.post("/verifyJwt", async (req, res, next) => {
    try {
        let response = await marketplace.jwtVerification(req.body);
        commonFunctions.errorlogger.info(`Jwt verified successfully`);
        return res.json(new Response(true, 'Jwt verified successfully.', response));
    } catch (error) {
        next(error);
    }
});

router.post("/webhook", async (req, res, next) => {
    try {
        let response = await marketplace.postWebhook(req.body, req.headers);
        commonFunctions.errorlogger.info(`Addon webhook fired`);
        return res.json(new Response(true, 'Addon webhook fired.', response));
    } catch (error) {
        next(error);
    }
});


module.exports = router;