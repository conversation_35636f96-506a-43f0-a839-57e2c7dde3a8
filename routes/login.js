/**
 * Created by man<PERSON><PERSON> on 23/8/16.
 */
const md5 = require('md5')
const async = require('async');
const moment = require('moment');
var samlController = require('./saml/samlController');
const constants = require('./../constants/constants');
const universal = require('./universal');
const router = require('express').Router();
const searchunifyEmail = require('../Lib/email');
const emailTemplates = require('./emailTemplates');
const request = require("request");
var jwt = require('jsonwebtoken');
var commonFunctions = require('./../utils/commonFunctions');
var redis = require("redis");
var client = redis.createClient(config.get("redis.redisPort"), config.get("redis.redisHost"));
const userKafkaConfigurations = require('./admin/userKafkaConfig');
const { fetchClientInfo } = require('../utils/commonFunctions');
const { getSignupSettings } = require('./admin/user')
const setCookie = require('set-cookie-parser');
const urlAuth  = config.get('authUrl');


const addAddOnStatusOnUserResponse = (req,res,cb) => {
  const splitCookie = setCookie.splitCookiesString(req.headers['set-cookie']);
  const cookies = setCookie.parse(splitCookie);
  cookies.forEach((item) => {
    res.cookie(item.name, item.value, { maxAge: 1800000 , httpOnly: true });
  });

  // res.cookie('connect.admin_sid', cookies['connect.admin_sid'], { maxAge: 1800000 , httpOnly: true })
  commonFunctions.getAddonsStatus(req, (error, result) => {
    let addons = [];
    if (result && result.length) {
      addons = result;
    }
    req.body.userData.addons = addons;

    if(req.body && req.body.userData && req.body.userData.userInfo){
      delete req.body.userData.userInfo;
    }

    cb(req.body.userData);
});
}


exports.adminLogin = function (req, res,) {
  addAddOnStatusOnUserResponse(req, res, (data) => {
    res.send(data)
  })
}

exports.validateUser = (req, res) => {
  addAddOnStatusOnUserResponse(req, res, (data) => {
    res.send(data)
  })
}

exports.SignUp = function (req, res) {
  getSignupSettings(req, function(data) {
    if (req.body.email == data.email && data.linkActive) {
      var name = req.body.name;
      var email = req.body.email;
      var password = req.body.password || req.body.newPassword;
      var access_token = md5(email + password);
      var showSecurityQues = false; //= req.body.securityQues;
      var ques_ans = '';
      if(req.body.ques!='' && req.body.ans!= ''){
        var ques_ans = md5(req.body.ques + req.body.ans) ;
      }
      var forgotPassword = req.body.password ? false : true;
      var match = false;
      var typeMismatch = false;
      var re = new RegExp("^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})");
      if (!re.test(password)) {
        response = {
          flag: 104,
          message: "Password Incorrect"
        }
        res.send(response);
      } else {
        var access_token = md5(email + password);
        var checkAlreadyExist = 0;
        async.series([
          function (cb) {
            //0 for check user, 1 for login user
            checkUserOrLogin(email, 0, 0, null, req, function (resultCheck) {
              if (resultCheck.status == 1) {
                checkAlreadyExist = 1;
                if (resultCheck.is_active == 1 && !forgotPassword) 
                  typeMismatch = true;
                showSecurityQues = resultCheck.ques_ans ? true : false;
                if (!typeMismatch && forgotPassword && (!showSecurityQues || (ques_ans == resultCheck.ques_ans))) {
                  match = true;
                }
                cb(null, "1")
              } else {
                cb(null, "0")
              }
            })
          },
          //register user insert user
          function (cb) {
            if (!typeMismatch && checkAlreadyExist && ((!forgotPassword && ques_ans!= '') || (forgotPassword && match))) {
             console.log(checkAlreadyExist,forgotPassword,ques_ans);
              registerUser(email, access_token, name, ques_ans, forgotPassword,req, function (result) {
                cb(null, "inserted")
              })
            } else {
              cb(null, "exist")
            }
          }
        ], function (err, result) {
          var response;
          if (typeMismatch){
            response = {
              flag: 110,
              message: "Invalid Request"
            }
            res.send(response);
          }
          else if (!checkAlreadyExist) {
            response = {
              flag: 106,
              message: "Invalid username or password"
            }
          
            res.send(response);
          } else if (forgotPassword && !match){
            response = {
              flag: 107,
              message: "Incorrect Security Question/Answer"
            }
            res.send(response);
          }else if (!forgotPassword && ques_ans == ''){
            response = {
              flag: 107,
              message: "Invalid Security Question/Answer "
            }
            res.send(response);
          }
          else {
            response = {
              flag: 200,
              message: "User Registered Successfully"
            }
            if (!forgotPassword) {
              let em = req.body.email.toLowerCase();
              client.del(`node-forget-${em}`);
              var emailObject = {
                  to: req.body.email,
                  subject: "Welcome to SearchUnify!",
                  html: emailTemplates.welcomeMessage(email)
                }
              searchunifyEmail.sendEmail(emailObject.to, emailObject.subject, emailObject.html, (error, resp) => {
                res.send(response);
              });
            }
            else{
              // Expire the link
              let hour = 1000 * 60 * 60 * (config.get('linkExpiration') ? config.get('linkExpiration') : 1);
              let expiredTime = Date.now() - hour  ;
              email = email.toLowerCase();
              let emailObj = { "email": `node-forget-${email}`, "startTime": expiredTime, "currentTime": expiredTime, "counter": 1 };
              client.set(`node-forget-${email}`, JSON.stringify(emailObj));
              client.del(`node-admin-login-${email}`);
              res.send(response);
            } 
          }
        })

      }
    }
    else {
      response = {
        flag: 106,
        message: "Invalid User"
      }
      res.send(response);
    }
  });

}

exports.registerEmail = function (req, res) {
  // let hour = 1000 * 60 * 60 * 24;
  // checkRedis(`register-${req.body.email}`, 1, hour, (error, result) => {
  if (req.body) {
    req.body.email = req.body.email.toLowerCase();
    resetTimeInterval(`node-register-${req.body.email}`, Date.now(), Date.now(), 1, null, (err, response) => {
      req.body.register = 0;
      req.body.type = 'add';
      registerAndForget(req, res);
    })
  }
    else
      res.send({ flag: 403, message: "Invalid username or password" });
}

exports.resendEmail = function (req, res) {
  if (req.body) {
    req.body.email = req.body.email.toLowerCase();
    resetTimeInterval(`node-register-${req.body.email}`, Date.now(), Date.now(), 1, null, (err, response) => {
      req.body.register = 0;
      req.body.type = 'resend';
      registerAndForget(req, res);
    })
  }
    else
      res.send({ flag: 403, message: "Invalid username or password" });
}

exports.forgetPassword = function (req, res) {
  req.body.email = req.body.email.toLowerCase();
  let hour = 1000 * 60 * 60 * 1;
  checkRedis(`forget-${req.body.email}`, 1, hour, (error, result) => {
    if (result == 2) { //if start time updated
      // resetTimeInterval(`node-forgot-${req.body.email}`, Date.now(), Date.now(), 1, (err, response) => {
        req.body.register = 1;
        registerAndForget(req, res);
      // })
    } else
      res.send({ flag: 403, message: "Invalid username or password" });
  })
}

exports.samllogin = (req, res) => {
  samlLoginFunction(req, res);
};

exports.ssoLogin = (req, res) => {
  let option = {
    method: "POST",
    url: config.get("centralLogin"),
    headers: { 'cache-control': 'no-cache', "content-type": "application/x-www-form-urlencoded" },
    form: { tempCode: req.query.code },
    json: true
  };
  request(option, (error, response, body) => {
    if (error) {
      console.error(error);
      res.status(401).send();
    }
    else {
      commonFunctions.errorlogger.info(body);
      if (body.error) {
        res.status(401).send(body);
      }
      else {
        checkUserOrLogin(body.data.email, null, null, null, req, data => {
          if (data.status === 1) {
            // res.csrfToken = req.csrfToken();
            req.session.accessToken = data.accessToken;
            req.session.name = data.name;
            req.session.email = data.email;
            req.session.roleName = data.role;
            req.session.roleId = data.roleId;
            req.session.userId = data.id;
            req.session.user_id = data.id;
            let redirect = config.get("adminURL");
            if (req.query.redirect)
              redirect = req.query.redirect;
            // res.setHeader("content-type", "text/html; charset=UTF-8");
            res.render(DIRNAME + '/resources/loginAuthorize/ssoLogin.html', {
              csrf: req.csrfToken(),
              roleId: data.roleId,
              name: data.name,
              email: data.email,
              redirect: req.query.redirect,
              userId: data.id
            });
            // res.redirect(redirect);
          }
          else {
            res.status(401).send({ Error: "User does not exist" });
          }
        });
      }
    }
  });
}

exports.resendOTPMail = function (req, res) {
  // moved to auth
}

function checkUserOrLogin(email, access_token, checkUser, password,req, callback) {
  async.auto({
    adminLogin: cb => {
      var sql = "";
      // let token = checkUser ? access_token : email;
      if (checkUser) sql = "select u.id id,r.id roleId,u.user_email email,u.name name,r.role role, u.is_federated is_federated, u.is_active is_active, u.ques_ans ques_ans, u.selectedTabs selectedTabs, u.is_blocked is_blocked, access_token access_token from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where user_email like ? and (is_federated != 1 or is_federated is NULL) Limit 1";
      else sql = "select u.id id,r.id roleId,u.user_email email,u.name name,r.role role, u.access_token access_token, u.is_federated is_federated, u.is_active is_active, u.ques_ans ques_ans, u.selectedTabs selectedTabs, u.is_blocked is_blocked from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where u.user_email=? and (is_federated != 1 or is_federated is NULL)"
      connection[req.headers['tenant-id']].execute.query(sql, [email], function (err, rows) {
        commonFunctions.errorlogger.error("check err", err);
        let data = {
          name: "",
          email: "",
          status : 0
        };
        if (rows.length > 0) {
          data.email = rows[0].email;
          data.is_active = rows[0].is_active;
          if (!checkUser || (checkUser && access_token == rows[0].access_token)) {
            data = {...rows[0]};
            data.access_token_matched = 1;
          }
          data.status = 1; //email exists
        }
        cb(null, data);
      })
    },
    scenario2EmailInDb: ['adminLogin', (dataFromAbove, cb) => {
      if ( checkUser && !dataFromAbove.adminLogin.access_token_matched && dataFromAbove.adminLogin.email )
        verifyUserLoginScenario (dataFromAbove.adminLogin, password, req, (error, adminLoginData) => {
          dataFromAbove.adminLogin = adminLoginData;
          cb(error, []);
        });
      else
        cb(null, []);
    }],
    scenario3LowerCase: ['adminLogin', 'scenario2EmailInDb', (dataFromAbove, cb) => {
      if ( checkUser && !dataFromAbove.adminLogin.access_token_matched && dataFromAbove.adminLogin.email ) {
        dataFromAbove.adminLogin.email = dataFromAbove.adminLogin.email.toLowerCase();
        verifyUserLoginScenario (dataFromAbove.adminLogin, password, req, (error, adminLoginData) => {
          dataFromAbove.adminLogin = adminLoginData;
          dataFromAbove.adminLogin.email = dataFromAbove.adminLogin.email.toLowerCase();
          cb(error, []);
        });
      }
      else
        cb(null, []);
    }],
    updateUserEmail: ['scenario3LowerCase', (dataFromAbove, cb) => {
      if (checkUser && dataFromAbove.adminLogin.access_token_matched) {
        let new_access_token = md5(dataFromAbove.adminLogin.email.toLowerCase() + password);
        updateAccessToken(dataFromAbove.adminLogin, new_access_token,req, cb)
      }
      else
        cb(null, '');
    }],
    addonsStatus: cb => {
      commonFunctions.getAddonsStatus(req, (error, result) => {
        if (result && result.length) {
          cb(null, result)
        } else {
          cb(null, [])
        }
      });
    },
    userCount: cb => {
      let restrict = config.get('restrictUserAccount');
      var sql = "Select count(*) as count from user";
      if (restrict.length){
        sql += ' where';
          for (var i = 0; i < restrict.length; i++){
              sql+=` FIND_IN_SET(right(user_email, ${restrict[i].length}),'${restrict[i]}')=0`;
              sql+= i < restrict.length - 1 ? ' and' : '';
          }
      }
      connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
        cb(null, rows[0].count);
      })
    },
    unblockUser: ['scenario3LowerCase', (dataFromAbove, cb) => {
      if (checkUser && dataFromAbove.adminLogin.is_blocked) {
        commonFunctions.unblockUser(email,req, (error, result) => {
          dataFromAbove.adminLogin.is_blocked = 0;
          commonFunctions.errorlogger.info("Unblocked User: ",email);
          cb(null,result);
        })
      }
      else
        cb(null, []);
    }]
  }, (error, result) => {
    resultToSend = [];
    resultToSend.push(result.adminLogin);
    resultToSend[0].addons = [];
    resultToSend[0].addons = result ? result.addonsStatus : [];
    resultToSend[0].userCount =  result ? result.userCount : [];
    return callback(resultToSend[0])
  })
}


function registerEmail(email, forget_password_token, selectedTabs, role, req, callback) {
  var sql = "Insert into user (user_email, forget_password_token, selectedTabs, scope) values (?,?,?,?) ON DUPLICATE KEY UPDATE forget_password_token = VALUES(forget_password_token), user_email = VALUES(user_email)";
  connection[req.headers['tenant-id']].execute.query(sql, [email, forget_password_token, selectedTabs, 'None'], function (err, rows) {
    if (!err) {
      var sql = "Insert into user_roles (userId,roleId) values (?,?) ON DUPLICATE KEY UPDATE userId=VALUES(userId), roleId=VALUES(roleId) ";
      connection[req.headers['tenant-id']].execute.query(sql, [rows.insertId, role], function (err, rows) { //removed hardcoded value of role
        if (!err) {
          if (config.get("kafkaTopic.enable")) {
            userKafkaConfigurations.publishUsersViaKafka(email,req, function (err, searchConfig) {
              return callback(1);
            })
          }
        }
      })
    }
  })
}

exports.ssoSamlLogin = (req, res) => {
  let resultData = req.body;
  let method = resultData.method;
  let data = resultData.data;
  let headers = {
    'Content-Type': 'application/json',
  }
  let urlData = '';
  urlData = config.get(resultData.url);
  if (resultData.url === 'samlLoginWithState')
    urlData += config.get("adminURL") + '/samllogin';
  commonFunctions.httpRequest(method, urlData, '', data, headers, function (error, body) {
    res.send(body)
  });
}

function registerUser(email, token, name, qa, forgotPassword, req, callback) {
  let activation_date = new Date();
  var sql = "Update user set access_token = ? , is_active = 1 "; //to set user active on completing registration; 1 - registered ; 2 - not registered
  sql += forgotPassword ? '' : ", name = ? , activation_date = ? , ques_ans = ? "; //if registering user then set name, activation date and question answer
  sql += " where user_email='"+ email+"';"
  let data = [token];

  if (!forgotPassword)
    data = data.concat([name, activation_date, qa]);

    connection[req.headers['tenant-id']].execute.query(sql, data, function (err, rows) {
    if (!err) {
      if (config.get("kafkaTopic.enable")) {
        userKafkaConfigurations.publishUsersViaKafka(email,req, function (err, searchConfig) {
          return callback(1);
        })
      }
    }
  })
}

function generateRandom(a) {
  return a ? (a ^ ((Math.random() * 16) >> (a / 4))).toString(16) : ([1e10] + 1e10 + 1e9).replace(/[01]/g, generateRandom);
}

function samlLoginFunction(req, res) {
  if (req.query.code) {
    var token = req.query.code;
    let security_key = config.get("JWT_SECRET_KEY");

    jwt.verify(req.query.code, security_key, function (err, decoded) {
      commonFunctions.errorlogger.info(decoded); // bar
      if ((decoded.iat - Date.now()) / (1000 * 60) < 5) {
        checkUserOrLogin(decoded.email, null, null, req, data => {
          if (data.is_federated) {
            if (data.status === 1) {
              req.session.accessToken = data.accessToken;
              req.session.name = data.name;
              req.session.email = data.email;
              req.session.roleName = data.role;
              req.session.roleId = data.roleId;
              req.session.userId = data.id;
              req.session.user_id = data.id;
              let redirect = config.get("adminURL");
              if (req.query.redirectUrl)
                redirect = req.query.redirectUrl;
              res.setHeader("content-type", "text/html; charset=UTF-8");
              commonFunctions.errorlogger.info("data", data);
              res.render(DIRNAME + '/resources/loginAuthorize/ssoLogin.html', {
                csrf: req.csrfToken(),
                roleId: data.roleId,
                name: data.name,
                email: data.email,
                redirect: redirect,
                userId: data.id,
                is_federated: data.is_federated,
                addons: data.addons
              });
            } else {
              res.status(401).send({ Error: "User does not exist" });
            }
          } else {
            res.redirect(config.get("adminURL") + '/admin-login')
          }
        });
      } else {
        res.status(401).send({ Error: "Invalid Token" });
      }
    });
  } else {
    res.status(401).send({ Error: "No Token found" });
  }
}

function registerAndForget(req, res) {
  var email = req.body.email;
  var forgotPassword = req.body.register;
  var role = req.body.role || 2;
  var selectedTabs = req.body.selectedTabs;
  var checkAlreadyExist = 0;
  var forget_password_token;
  var incompleteReg = false;
  var tokenToSend;
  var type = req.body.type || '';
  let license = true;
  let instanceLimits = {};
  async.series([
    //email already exist
    function (cb){
      fetchClientInfo((err,data)=>{
        if(!err){
          instanceLimits = data;
        }
        cb(null,'done')
      })
    },
    function (cb) {
      let adminLicenseLimit = config.get('addUserLimit');
      if(instanceLimits && instanceLimits.usages && instanceLimits.usages.adminLicenseLimit >= 0){
        adminLicenseLimit = instanceLimits.usages.adminLicenseLimit;
      }
      //0 for check user, 1 for login user
      checkUserOrLogin(email, 0, 0, null, req, function (resultCheck) {
        if( req.body.type != 'add' ||  resultCheck.userCount < adminLicenseLimit){
          license = false;
          if (resultCheck.status == 1) { //&& resultCheck.is_active == 1 is_active to check if user completed the registration; 1 - registered ; 2 - not registered
            checkAlreadyExist = 1;
            incompleteReg = resultCheck.is_active != 1 && type != "add" ? true : false;
            cb(null, "1")
          } else {
            incompleteReg = type == "add" ? true : false;
            cb(null, "0")
          }
      }else{
        checkAlreadyExist = 1;
        license = true;
        cb(null, "1");
      }
      })
    },
    function (cb) {
      forget_password_token = generateRandom();
      cb(null, 'forget_password_token')
    },
    function (cb) {
      if (!checkAlreadyExist && !forgotPassword && type == "add") {
        registerEmail(email, forget_password_token, selectedTabs, role, req, function (result) { //to set role by admin on registration
          cb(null, "inserted")
        })
      } else {
        cb(null, "exist")
      }
    },
    function (cb) {
      if (checkAlreadyExist && incompleteReg && !forgotPassword){
        updateCount(req.body.email, req, function(err, res) {
          cb(null, "updated count");
        })
      }
      else
        cb(null, "count not updated");
    },
    function (cb) {
      commonFunctions.registerEmailJwt({email, forget_password_token}, function(err, result){
        if(err) cb('Error while creating token');
        else {
          tokenToSend = result.token;
          cb(null, "token created");
        }
      })
    }
  ], function (err, result) {
    var response;
    if(license){
      response = {
        flag: 104,
        message: "License limit exceeded"
      }
    }else{
    if ((checkAlreadyExist && !forgotPassword && !incompleteReg) || (checkAlreadyExist && forgotPassword && incompleteReg) ) {
      response = {
        flag: 104,
        message: "Invalid username or password"
      }
    } else {
      if ((forgotPassword && !checkAlreadyExist) || (!incompleteReg && !checkAlreadyExist) ) {
        response = {
          flag: 106,
          message: "Invalid username or password"
        }
      }
      else {
        response = {
          flag: 200,
          message: "Mail Sent"
        }
        if (forgotPassword) {
          var emailObject = {
            to: req.body.email,
            subject: "Reset Password",
            html: emailTemplates.forgetMessage(email, tokenToSend)
          }
        } else if (!forgotPassword && incompleteReg){
          var emailObject = {
            to: req.body.email,
            subject: "SearchUnify User Registration!",
            html: emailTemplates.registerMessage(email, tokenToSend)
          }
        } else {
          response = {
            flag: 400,
            message: "Unable to send invite"
          }
        }
        searchunifyEmail.sendEmail(emailObject.to, emailObject.subject, emailObject.html, (error, response) => {
          if (response) res.send("Done")
        });
      }
    }
    }
    res.send(response);
  })
}

function updateCount (email, req, callback){
  let sql = "update user set invite_count = invite_count + 1 where user_email = ? ";
  connection[req.headers['tenant-id']].execute.query(sql, email, function (err, rows) {
    if (!err) {
      return callback();
    }
  });
}

function checkRedis(email, counter, hour, cb) {
  email = email.toLowerCase();
  let hourAgo = Date.now() - hour;
  client.get(`node-${email}`, (error, response) => {
    response = JSON.parse(response);
    if (response && response.email.indexOf(email) > -1 && response.startTime > hourAgo) {
        if (response.counter < counter) {
          resetTimeInterval(`node-${email}`, response.startTime, Date.now(), (response.counter + 1), null, (err, res) => {
            cb(null, 1);
          })
        } 
        else
          cb(null, 1);
    } else {
      resetTimeInterval(`node-${email}`, Date.now(), Date.now(), 1, null, (err, res) => {
        cb(null, 2);
      })
    }
  })
}

function checkRedisCount(email, counter, hour, cb) {
  let hourAgo = Date.now() - hour;
  email = email.toLowerCase();
  client.get(`node-${email}`, (error, response) => {
    response = JSON.parse(response);
    if (response && response.email.indexOf(email) > -1) {
      if (response.startTime > hourAgo) {
        if (response.counter < counter) {
          cb(null, counter - response.counter);
        } else {
          cb(null, 0);
        }
      } else {
        cb(null, counter);
      }
    } else {
      cb(null, counter);
    }
  })
}

function resetTimeInterval(email, startTime, currentTime, counter, expireTime, cb) {
  let emailObj = {};
  email = email.toLowerCase();
  emailObj = { "email": email, "startTime": startTime, "currentTime": currentTime, counter: counter };
  client.set(email, JSON.stringify(emailObj));
  if (expireTime)
    client.set(email, JSON.stringify(emailObj), "PX", expireTime);
  cb(null, []);
}

function addOTPToken(email, callback){
  let otp_token = email + '_otp_token'
  let randomNum = Math.floor(Math.pow(10, 6-1) + Math.random() * (Math.pow(10, 6) - Math.pow(10, 6-1) - 1));
  let dataToSet = {};
  let expireTime = 1000*60*5;
  dataToSet = { "email": email.toLowerCase(), "otp_token": randomNum, "startTime": Date.now(), "expireTime": Date.now() + expireTime};
  client.set(otp_token.toLowerCase(), JSON.stringify(dataToSet), "PX", expireTime); //PX for milliseconds, EX for minutes
  var emailObject = {
    to: email,
    subject: "Verify your SearchUnify login (using OTP)",
    html: emailTemplates.verifyUser(email, randomNum)
  }
  searchunifyEmail.sendEmail(emailObject.to, emailObject.subject, emailObject.html, (error, resp) => {commonFunctions.errorlogger.log(resp);});
  callback();
}

function generateSession(req, email, resultCheck, accessToken, response, callback) {
  response.addons = resultCheck.addons;
  //res.cookie('XSRF-TOKEN', req.csrfToken())
  response.csrfToken = req.csrfToken();
  req.session.regenerate((err) => {
    var sess = req.session;
    sess.accessToken = accessToken;
    sess.name = resultCheck.name;
    sess.email = resultCheck.email;
    sess.roleName = resultCheck.role;
    sess.roleId = resultCheck.roleId;
    sess.userId = resultCheck.id;
    sess.user_id = resultCheck.id;
    sess.is_federated = resultCheck.is_federated;
    sess.ques_ans = resultCheck.ques_ans;
    sess.selectedTabs = resultCheck.selectedTabs;
    // update last login time 01-01-1970 / 12:01 AM
    connection[req.headers['tenant-id']].execute.query(`select * from user where user_email = '${sess.email}'`, (err, data) => {
      var last_login = JSON.parse(data[0].last_login).current_login
      var today = new Date();
      var dd = String(today.getDate()).padStart(2, '0');
      var mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
      var yyyy = today.getFullYear();
      var hours = String(today.getHours()).padStart(2, '0');
      var minutes = String(today.getMinutes()).padStart(2, '0');
      var current_login =  mm + '-' + dd + '-' + yyyy + ' / '+  hours+ ':' + minutes + " UTC"  ;
      var update_time = JSON.stringify({"last_login": last_login,"current_login": current_login})
      connection[req.headers['tenant-id']].execute.query(`Update user set last_login = '${update_time}' where user_email =  '${sess.email}'`, (err, data) => {
        console.log('Last login updated');
      });
    });
    resetTimeInterval(`admin-login-${email}`, Date.now(), Date.now(), 1, null, (err, result) => {
      encodeDecodeData({ encode:1, objectToEncode: {email:resultCheck.email, allowFurther:true} }, function(error, result){
        response.access = result
        callback(null, response);
      });
    })
  })
}

function encodeDecodeData (data, cb){
  var secret = config.get("JWT_SECRET_KEY") || '';
  if (data.encode) {
    let token = jwt.sign(data.objectToEncode, secret);
    cb(null, token);
  }
  else if (data.token){
    let decoded = jwt.verify(data.token, secret);
    cb(null, decoded);
  }
  else 
    cb(null, {})
}

function blockUser(email, req, callback) {
  let expireTime = config.get("blockUserMinutes") ? 1000 * 60 * config.get("blockUserMinutes") : 1000 * 60 * 15; //block User Time
  let sql = 'Update user set is_blocked = 1 where user_email = ?'
  connection[req.headers['tenant-id']].execute.query(sql, [email], function (err, rows) {
      if (rows){
        email = email.toLowerCase();
        resetTimeInterval(`node-admin-login-${email}`, Date.now(), Date.now(), 3, expireTime, (err, res) => {
          client.del([`${email}_otp_token`, `${email}_resend_count`, `${email}_incorrect_count`]);
          commonFunctions.errorlogger.info("Account blocked for ", email);
        })
      }
      callback(null, '');
    })
}

function updateAccessToken(adminLoginData, access_token, req,callback) {
  let sql = 'Update user set user_email = ?, access_token = ? where user_email = ?';
  connection[req.headers['tenant-id']].execute.query(sql, [adminLoginData.email.toLowerCase(), access_token, adminLoginData.email], function (err, rows) {
    if (rows){
      commonFunctions.errorlogger.info("Updated user email and access token for user: ",adminLoginData.email);
      adminLoginData.email = adminLoginData.email.toLowerCase();
      callback(null, 'Updated row');
    }
    else
      callback(err, null);
  });
}

function verifyUserLoginScenario (adminLoginData, password, req,callback) {
  let check_access_token = md5(adminLoginData.email + password);
  commonFunctions.errorlogger.info("Login using scenario 2 for user: ",adminLoginData.email);
  let sql = "select u.id id,r.id roleId,u.access_token access_token, u.user_email email,u.name name,r.role role, u.is_federated is_federated, u.is_active is_active, u.ques_ans ques_ans, u.selectedTabs selectedTabs, u.is_blocked is_blocked from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where access_token = ? and (is_federated != 1 or is_federated is NULL) Limit 1";
  connection[req.headers['tenant-id']].execute.query(sql, [check_access_token], function (err, rows) {
    commonFunctions.errorlogger.error("Check error in sql query: ", err);
    if (rows.length > 0) {
      rows[0].access_token_matched = 1;
      adminLoginData = {...rows[0]}
    }
    callback(err, adminLoginData);
  })
}


exports.adminLogout = (req, cb) => {
  var options = {
    "method": "GET",
    "url": `${urlAuth}/logout`,
    "headers": { 'content-type': 'application/json', 'Cookie': req.headers.cookie, 'CSRF-Token' : req.headers['csrf-token']}
};
request(options, function (err, response, body) {
    if (err) {
        console.log("ERROR :: " + err);
        cb(err);
    } else {
        try{
          cb();
        }catch(error){
            // cb({statusCode : 400, message: 'Bad Request'}, null);
            console.log("Error while connecting to auth server")
            cb(error);
        }
    }
})
}
