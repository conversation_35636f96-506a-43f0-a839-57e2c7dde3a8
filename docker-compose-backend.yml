# docker compose for admin panel service
version: "3.0"

services:
  backend:
    container_name: backend
    build:
      context: .
      dockerfile: Dockerfile
    image: admin:latest
    volumes:
      - ".:/home/<USER>"
      - "/home/<USER>/node_modules"
      - "admin_generic:/home/<USER>/resources/search_clients_custom"
      - "synonyms:/home/<USER>/synonyms"
      - "backend_node_modules:/home/<USER>/node_modules"
      - "admin_crontab:/var/spool/cron/crontabs/"
      - "asset_library:/home/<USER>/resources/Asset-Library"
      - analytics_reports:/home/<USER>/reports
    networks:
      - shared_network
    deploy:
      resources:
        limits:
          memory: 2G
    command: >
      sh -c "npm install
      && crond -b -l 8
      && ${NODE_RUN_ENV}"
    restart: always
    ports:
      - "6009:6009"
      
volumes:
  admin_generic:
  synonyms:
  backend_node_modules:
  admin_crontab:
  asset_library:
  analytics_reports:

networks:
  shared_network:
    external:
      name: shared_network
