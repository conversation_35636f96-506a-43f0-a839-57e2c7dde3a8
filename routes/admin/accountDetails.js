var express = require('express');
var router = express.Router();
var request = require('request');
var path = require('path');
var async = require('async');
environment = require(path.join(__dirname, '../environment.js'));
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
const moment = require("moment");
const { getPlatforms } = require("./searchClient");
const { getSumOfConsumedApi } = require("./rateLimit")
const { getAddedContentSources } = require("./contentSources");
const { fetchClientInfo } = require("../../utils/commonFunctions");
const { getEsClient } = require('../../utils/elastic');
let SECRET_KEY;

if (config.has('multiTenantAuthSecret')) { 
    SECRET_KEY = config.get('multiTenantAuthSecret');
    console.log("multiTenantAuthSecret KEY: ", SECRET_KEY);
 } else { 
    SECRET_KEY = 'SECRET_KEY';
    console.log("multiTenantAuthSecret is not defined in the configuration passing static key in accountDetails.js file:", SECRET_KEY); 
}

const accountDetails = async (req,cb) => {
    const esClient = await getEsClient(req.headers['tenant-id']);
    const lastDayOfMonth = moment().subtract(1, 'days');
    async.auto({
        getStatusPageInfo:cb =>{
            fetchClientInfo(req.headers['tenant-id'], (err,data)=>{
                if(err) cb(err,null);
                else cb(null,data);
            })
        },
        getSearchClients:cb =>{
            let params = {
                query: {},
                session: {
                    userEmail: '<EMAIL>',
                    userRole: 4
                }
            }                
            getPlatforms(req,(data)=>{
                cb(null,data)
            })
        },
        getSearches:cb=>{
            var options = {
                method: 'POST',
                rejectUnauthorized: false,
                url: config.get('analyticsService.url') + '/overview/tileData',
                headers: {
                    'content-type': 'application/json'
                },
                body:{
                    from: `${moment(lastDayOfMonth).startOf('month').format("YYYY-MM-DD")}`,
                    to: `${moment(lastDayOfMonth).format("YYYY-MM-DD")}`,
                    internalUser: 'all',
                    tenantId: req.headers['tenant-id']
                },
                json: true
            }
    
            request(options,function(err,response,docs){
                if(err)cb(err,null)
                else cb(null,docs);
            })
        },
        getApiCount:cb=>{
            let range = {
                startDate: `${moment(lastDayOfMonth).startOf('month').format("YYYY-MM-DD 00:00:00")}`,
                endDate: `${moment(lastDayOfMonth).format("YYYY-MM-DD 23:59:59")}`
            }
            getSumOfConsumedApi(req, range,(err,data)=>{
                if(err)cb(err,null)
                else cb(null,data)
            })
        },
        getUsers:cb => {
            let tenantId = req.headers['tenant-id']
            getUserCount(tenantId, (err, data) => {
                if(err){
                    cb(err, null);
                }else cb(null,data);
            });
             
        },
        getContentSourceData:cb=>{
            let params = {
                query:{},
                session: {
                    userEmail: '<EMAIL>',
                    userRole: 4
                }
            }            
             getAddedContentSources(req,  (data) => {
                let apis = [];
                data.data.forEach((idx)=>{
                    apis.push(function (cbl) {
                            esClient.indices.stats({
                                index: idx.elasticIndexName
                            }, 
                            function (error, response, body) {
                                    if (error) {
                                        cbl(error,null);
                                    }
                                    else {
                                        body = response.body
                                        let size = body._all ? bytesToSize(body._all.primaries.store ? body._all.primaries.store.size_in_bytes : 0) : 0;
                                        cbl(null,{size:size,count:idx.count});
                                    }
                                  });
                    });
                })
                async.parallel(apis,(error,data)=>{
                    if(error) cb(error,null);
                    else cb(null,data);
                })
            })
        }
    },(err,docs)=>{
        let statusPageData = docs.getStatusPageInfo;
        let licenseLimit = 0;
        let indexSizeLimit = 0;
        let contentSourceLimit = 0;
        let documentsLimit = 0;
        let searchesLimit = 0;
        let searchApiLimit = 0;
        let analyticsApiLimit = 0;
        let contentApiLimit = 0;
        let searchClientsLimit = 0;

        if(statusPageData && statusPageData.statusCode!== 404 && Object.keys(statusPageData)){
            licenseLimit       =  statusPageData.usages.adminLicenseLimit;
            indexSizeLimit     =  statusPageData.usages.indexSizeLimit;
            contentSourceLimit =  statusPageData.usages.contentSourceLimit;
            documentsLimit     =  statusPageData.usages.documentsLimit;
            searchesLimit      =  statusPageData.usages.searchesLimit;
            searchApiLimit     =  statusPageData.usages.searchApiLimit.monthly;
            analyticsApiLimit  =  statusPageData.usages.analyticsApiLimit.monthly;
            contentApiLimit    =  statusPageData.usages.contentApiLimit.monthly;
            searchClientsLimit =  statusPageData.usages.searchClientsLimit
        }

        
        let contentSourceConsumed     = parseInt(docs.getContentSourceData.length);
        let searchClientCountConsumed = parseInt(docs.getSearchClients.message.length);
        let searchesConsumed          = parseInt(docs.getSearches.data.searches);
        let searchApiConsumed         = parseInt(docs.getApiCount.body.data.searchCount[0].sum ? docs.getApiCount.body.data.searchCount[0].sum : 0);
        let contentApiConsumed        = parseInt(docs.getApiCount.body.data.contentSourceCount[0].sum ? docs.getApiCount.body.data.contentSourceCount[0].sum : 0);
        let analyticsApiConsumed      = parseInt(docs.getApiCount.body.data.analyticsCount[0].sum ? docs.getApiCount.body.data.analyticsCount[0].sum : 0);
        let licenseConsumed           = parseInt(docs.getUsers.userCount);
        let indexSizeConsumed = 0
        let documentsConsumed = 0;
        docs.getContentSourceData.forEach((d)=>{
            documentsConsumed += d.count;
            if(d.size){
                if(d.size.includes("KB")){
                    indexSizeConsumed += parseInt(d.size.split(" ")[0]) / 1000000;
                }else if(d.size.includes("MB")){
                    indexSizeConsumed += parseInt(d.size.split(" ")[0]) / 1000;
                }else if(d.size.includes("GB")){
                    indexSizeConsumed += parseInt(d.size.split(" ")[0]);
                }
            }
        })
        indexSizeConsumed = indexSizeConsumed.toFixed(1);


        let columns  = ['licenseConsumed','licenseLimit','indexSizeConsumed','indexSizeLimit','contentSourceConsumed','contentSourceLimit','documentsConsumed','documentsLimit','searchClientsConsumed','searchClientsLimit','searchesConsumed','searchesLimit','searchApiConsumed','searchApiLimit','analyticsApiConsumed','analyticsApiLimit','contentApiConsumed','contentApiLimit','month','year'];

        let monthName = moment().month(moment().month()).format("MMMM");
        let year = moment().year();
        let parameters = [licenseConsumed,licenseLimit,indexSizeConsumed,indexSizeLimit,contentSourceConsumed,contentSourceLimit,documentsConsumed,documentsLimit,searchClientCountConsumed,searchClientsLimit,searchesConsumed,searchesLimit,searchApiConsumed,searchApiLimit,analyticsApiConsumed,analyticsApiLimit,contentApiConsumed,contentApiLimit,monthName,year];

       const sql = "INSERT INTO accountDetails" + " (" + columns +
       ") VALUES (" + columns.map(x => {
           return '?'
       }).join(',') + ") "

       connection[req.headers['tenant-id']].execute.query(sql,parameters,(err,docs)=>{
        if(!err){
            console.log("Entry to db done for month",monthName);
        }
    })
        cb();
    })    
}

function bytesToSize(bytes) {
    var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes == 0) return '0 Byte';
    var i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
    return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i];
  };

  const getUserCount = (tenantId, cb) => {
    let options = {
        method: 'GET',
        headers: {
            'content-type': 'application/json',
            'mt-auth-secret': SECRET_KEY
        },
        qs: {
            tenantId
        },
        url: config.get('authUrl') + '/user/getUserCount',
        body: {
            appId: 1
        },
        json: true
      };  
      request(options,function(err,response){
        if(err)cb(err,null)
        else cb(null,{userCount: response.body && response.body.count});
    })
  }

  module.exports = {
    accountDetails
  }