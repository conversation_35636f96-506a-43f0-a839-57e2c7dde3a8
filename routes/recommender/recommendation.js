/**
 * <AUTHOR> (Graz<PERSON>i)
 * @version 1
 */
var express = require('express');
var async = require('async');
var router = express.Router();
var request = require('request');
var md5 = require('md5');
var universal = require('./../universal')
var accessKey = config.get('predictionIO.accessKey');
var recommenderRoutes = {};
var elastic = require('elasticsearch');
var commonFunctions = require('../../utils/commonFunctions');
var searchClient = require('../../routes/admin/searchClient');
const lithiumCrawler =require('./../ApisForLithium');
var md5salt = commonFunctions.appVariables.analytics.md5salt;
var client = new elastic.Client({
  host: 'http://' + config.get('elasticIndexCS.host') + ':' + config.get('elasticIndexCS.port')
});


function getSearchclientSettings(uid,req,callback) {
  connection[req.headers['tenant-id']].execute.query(`SELECT id FROM search_clients WHERE uid=?`, [uid], (e, r, f) => {
    if (e || !r.length) callback(e);
    else
    {
      searchClient.getSearchClient(r[0].id,req,callback)
    }
  });
}

let createNewPageEvent = function (pageId, ObjectToSave, cb) {
  commonFunctions.errorlogger.info('pageId: ' , pageId);
  var headers = {
    'Content-Type': 'application/json'
  };

  var uid = ObjectToSave.uid;
  if(uid!=null && uid !=''){
    var boardNames = ["-1"];
    async.series([
      (cbin) => {
        commonFunctions.errorlogger.warn('get boardName for document');
        client.get({
          index: pageId.split('/')[0],
          type: pageId.split('/')[1],
          id: decodeURIComponent(pageId.split('/')[2])
        }, (ein, rin) =>{
          if(!ein){
            //console.log(JSON.stringify(rin));
            if(rin.found && rin._source.boardName){
              boardNames.concat(rin._source.boardName);
            }
          }
          cbin(null, 'done');
        });
      },
      (cbin) => {

        var dataString = {
          "event": "$set",
          "entityType": "item",
          "entityId": pageId,
          "properties": {
            "count": 1,
            "boardName": boardNames,
            "uid": uid
          },
          "eventTime": new Date().toISOString()
        };

        var options = {
          url: 'http://' + config.get('predictionIO.eventServerHost') + '/events.json?accessKey=' + accessKey,
          method: 'POST',
          headers: headers,
          body: JSON.stringify(dataString)
        };

        function callback(error, response, body) {
          if (!error && (response.statusCode == 200 || response.statusCode == 201)) {
            commonFunctions.errorlogger.info('body', body);
            cbin(null, body);
          } else {
            commonFunctions.errorlogger.error('error', error);
            cbin(error, null);
          }
        }

        request(options, callback);
      }], function(err, res){
        if(!err)
          cb(null, res);
        else
          cb(err, null);
      }
    )
  }else{
    cb("Search client id cannot be null", null);
  }
}

let createNewUserEvent = function (userEmail, cookie, ip, cb) {
  var headers = {
    'Content-Type': 'application/json'
  };

  var dataString = {
    "event": "$set",
    "entityType": "user",
    "entityId": ip,
    "properties": {
      "id": cookie
    },
    "eventTime": new Date().toISOString()
  };

  if(cookie){
    dataString.entityId = cookie;
    dataString.properties.id = !userEmail ? cookie : userEmail;
  }else{
    commonFunctions.errorlogger.info('using ip as session identifier: ',ip);
    dataString.entityId = ip;
    dataString.properties.id = ip;
  }

  var options = {
    url: 'http://' + config.get('predictionIO.eventServerHost') + '/events.json?accessKey=' + accessKey,
    method: 'POST',
    headers: headers,
    body: JSON.stringify(dataString)
  };

  function callback(error, response, body) {
    if (!error && (response.statusCode == 200 || response.statusCode == 201)) {
      commonFunctions.errorlogger.info(': no error', body);
      cb(null, 'success');
    } else {
      cb(error, null);
    }

  }

  request(options, callback);
}

let createNewConversionEvent = function (userEmail, cookie, ip, pageId, cb) {
  var headersIn = {
    'Content-Type': 'application/json'
  };

  var dataStringIn = {
    "event": "$set",
    "entityType": "user",
    "entityId": ip,
  };

  if(cookie){
    dataStringIn.entityId = cookie;
  }else{
    commonFunctions.errorlogger.info('using ip as session identifier: ',ip);
    dataStringIn.entityId = ip;
  }

  dataStringIn.event = 'buy';
  dataStringIn.targetEntityType = 'item';
  dataStringIn.targetEntityId = pageId;

  var optionsIn = {
    url: 'http://' + config.get('predictionIO.eventServerHost') + '/events.json?accessKey=' + accessKey,
    method: 'POST',
    headers: headersIn,
    body: JSON.stringify(dataStringIn)
  };

  function callbackIn(errorin, responsein, bodyin) {
    if (!errorin && (responsein.statusCode == 200 || responsein.statusCode == 201)) {
      commonFunctions.errorlogger.info(': no error', bodyin);
      cb(null, 'success');
    } else {
      commonFunctions.errorlogger.error('error while saving conversion ',errorin);
      cb(errorin, null);
    }

  }
  request(optionsIn, callbackIn);
}

let responseFormat = function (resultArr, userObj,req, callback) {
  connection[req.headers['tenant-id']].execute.query(`SELECT id FROM search_clients WHERE uid=?`, [userObj.uid], (e, r, f) => {
    if (e || !r.length) callback(e);
    else {
      searchClient.getSearchClient(r[0].id, req,function (err, settings) {
        commonFunctions.getDisplayFields(userObj.uid,req, function (rowsDisplay) {
          commonFunctions.errorlogger.warn("here in get_display_fields ")

          for (var iy = 0; iy < rowsDisplay.length; iy++) {
            if (rowsDisplay[iy]["display_field_name"] == "href") {
              href = rowsDisplay[iy]["elastic_field_name"];
            }
          }
          let highlightFields = {};
          var arrtypes = [];
          var cJson = {};
          var typeName = '';
          for (var inq = 0; inq < rowsDisplay.length; inq++) {
            if (typeName != rowsDisplay[inq]["elastic_object_name"]) {
              if (inq != 0) {
                arrtypes.push(cJson);
              }
              cJson = {};
              cJson["type"] = rowsDisplay[inq]["elastic_object_name"];
              cJson["values"] = [];
              typeName = rowsDisplay[inq]["elastic_object_name"];
            }
            cJson["values"].push({
              "fieldName": rowsDisplay[inq]["elastic_field_name"],
              "Displaytype": rowsDisplay[inq]["display_field_name"]
            });
          }
          arrtypes.push(cJson);
          for (var it = 0; it < resultArr.length; it++) {

            //logic for icon----------------**********************************
            if (settings.sources.filter(x => x.enabled).map(y => { let o = y.objects.filter(z => z.enabled); return o }).reduce(commonFunctions.reduceObject).find(x => x.name == resultArr[it]["_type"]) && settings.sources.filter(x => x.enabled).map(y => { let o = y.objects.filter(z => z.enabled); return o }).reduce(commonFunctions.reduceObject).find(x => x.name == resultArr[it]["_type"]).icon)
              resultArr[it]["icon"] = config.get('adminURL') + '/' + settings.sources.filter(x => x.enabled).map(y => { let o = y.objects.filter(z => z.enabled); return o }).reduce(commonFunctions.reduceObject).find(x => x.name == resultArr[it]["_type"]).icon;
            // -------------------*******************************************=======================

            //logic for href----------------**********************************
            if (settings.sources.filter(x => x.enabled).map(y => { let o = y.objects.filter(z => z.enabled); return o }).reduce(commonFunctions.reduceObject).find(x => x.name == resultArr[it]["_type"])) {
              resultArr[it]["href"] = settings.sources.filter(x => x.enabled).map(y => { let o = y.objects.filter(z => z.enabled); return o }).reduce(commonFunctions.reduceObject).find(x => x.name == resultArr[it]["_type"]).base_href
              var testRegex = /{{.*?}}/g;
              var str = resultArr[it]["href"];
              var m;

              while ((m = testRegex.exec(str)) !== null) {
                // This is necessary to avoid infinite loops with zero-width matches
                if (m.index === testRegex.lastIndex) {
                  testRegex.lastIndex++;
                }
                m.forEach((match, groupIndex) => {
                  resultArr[it]["href"] = resultArr[it]["href"].replace(match, resultArr[it]["_source"][match.replace("{{", "").replace("}}", "")])
                  commonFunctions.errorlogger.info(match);
                });
              }
            }
            if (!resultArr[it]["href"])
              resultArr[it]["href"] = resultArr[it]["_id"] || resultArr[it]["_source"]['id'];

            // -------------------*******************************************=======================

            var typeData = arrtypes.find(x => x.type == resultArr[it]["_type"])
            // highlight title and summary
            resultArr[it]["highlight"] = {};
            if (resultArr[it]["highlight"] && typeData) {

              var titleFieldName = typeData["values"].find(x => { if (x["Displaytype"] == "Title") return x }) ? typeData["values"].find(x => { if (x["Displaytype"] == "Title") return x })["fieldName"] : "_id"

              var titleData = resultArr[it]["highlight"][titleFieldName] ? resultArr[it]["highlight"][titleFieldName][0] : resultArr[it]["_source"][titleFieldName]
              var titleDataWithoutHtml = resultArr[it]["_source"][titleFieldName] ? resultArr[it]["_source"][titleFieldName] : resultArr[it]["_source"][titleFieldName]

              var summaryFieldsName = typeData["values"].filter(x => { if (x["Displaytype"] == "Summary") return x }).length ? typeData["values"].filter(x => { if (x["Displaytype"] == "Summary") return x }).map(y => { var data = y["fieldName"].includes("attachment_") ? y["fieldName"] + ".body" : y["fieldName"]; return data }) : ["Summary"]

              var summaryData = summaryFieldsName.map(x => { return resultArr[it]["highlight"][x] ? resultArr[it]["highlight"][x][0] : "" }).filter(n => n).map(y => y + "....")

              if (summaryData.length)
                resultArr[it]["highlight"]["SummaryToDisplay"] = (summaryData)
              else {
                summaryData = [summaryFieldsName.map(x => { return resultArr[it]["_source"][x] ? resultArr[it]["_source"][x].toString().substring(0, 100) : " " }).filter(n => n).join(" ").substring(0, 250) + "...."]
                resultArr[it]["highlight"]["SummaryToDisplay"] = (summaryData)
              }
              resultArr[it]["highlight"]["TitleToDisplay"] = [(titleData)]
              resultArr[it]["highlight"]["TitleToDisplayString"] = [(titleDataWithoutHtml)]
              resultArr[it]["highlight"]["AttachmentToDisplay"] = resultArr[it].inner_hits ? resultArr[it].inner_hits.mustNestedCondition.hits.hits.map(h => { if (h._source) return { name: h._source.name, url: settings.client.client_href + h._source.url } }) : []
              resultArr[it]["Id"] = resultArr[it]["_source"]["Id"] ? resultArr[it]["_source"]["Id"] : resultArr[it]["_source"]["id"]
            }
            //adding property
            resultArr[it]['objName'] = resultArr[it]['_type']
            resultArr[it]['sourceName'] = resultArr[it]['_index'];
            delete resultArr[it]["_source"];

            if (Object.keys(resultArr[it]["highlight"]).length === 0) {
              resultArr.splice(it, 1);
              it--;
            }
          }
          var es = {
            "hits": resultArr.splice(0, 5)
          }
          callback(null, es);
        })
      })
    }
  });
}

/**
 * Permission check method
 * @param {*} boards  - array of strings where each string represents board name
 * @param {*} callback
 */
function getItemsByPermission(uid, settings, callback){
  var boards = settings.boards;
  var indices = settings.indices;
  var options={
    url: 'http://localhost:7070/events.json?limit=-1&entityType=item&event=%24set',
    qs: { accessKey: accessKey },
    headers: {
      'cache-control': 'no-cache'
    }
  };
  request(options, function(error, response, body){
    if(error){
      callback(error, null);
    }
    else if(response.statusCode != 200){
      callback('bad response Code', null);
    }
    else{
      body = JSON.parse(body);
      body = body.filter(x=>{
        if(x.event == '$set' && x.entityType=="item" && x.properties.uid && x.properties.uid == uid){
          var flag = false;
          if(indices.indexOf(x.entityId.split('/')[0])>=0){
            if(boards.length>0){
              boards.map(board => {
                try{
                  if(x.properties.boardName && x.properties.boardName.indexOf(board) > -1)
                    flag = true;
                  else if(x.properties.boardName && x.properties.boardName.length == 1 && x.properties.boardName[0] == "-1")
                    flag = true;
                }catch(e){}
              });
            }else{
              if(x.properties.boardName && x.properties.boardName.length == 1 && x.properties.boardName[0] == "-1")
                  flag = true;
            }
          }
          return flag;
        }
        return false;
      });
      var itemArray = [];
      body.forEach(x=>{
        itemArray.push(x.entityId);
      })
      callback(null, itemArray);
    }
  })
}

function getOpenLithiumBoards(contentSourceId, callback) {
  commonFunctions.getContentSourceDataById(contentSourceId, function (err, resultData) {
    var authObj = {}
    authObj.contentSourceId = resultData.contentSource.id
    authObj.communityUrl = resultData.contentSource.url;
    authObj.client_id = resultData.authorization.client_id
    authObj.username = resultData.authorization.username;
    authObj.password = resultData.authorization.password;
    authObj.popup_username = resultData.authorization.htaccessUsername;
    authObj.popup_password = resultData.authorization.htaccessPassword;
    authObj.basicAuthEnabled = resultData.authorization.authorization_type.toLowerCase().includes("htaccess") ? 1 : 0;  //https enabled
    authObj.isOpen = resultData.authorization.authorization_type.includes("No Authentication") ? 1 : 1;
    authObj.tenantId = resultData.authorization.tenantId
    authObj.type = resultData.authorization.authorization_type.includes("OAuth") ? "v1" : "v1";
    if( resultData.authorization.authorization_type.includes("Api user"))
    {
      authObj.username = resultData.authorization.publicKey;
      authObj.password = resultData.authorization.privateKey;
    }
    if (!resultData.authorization.password && !resultData.authorization.access_token) {
      if (authObj.type == "v1")
        lithiumCrawler.getBulkContentV1.crawlallContentBoards(authObj, "", 100, 1, [], function (err, boards) {
          let boardsArr = []
          boards.forEach(x => {
            boardsArr.push(x.id['$'])
          });
          callback(null, boardsArr)
        })
      else if (authObj.type == "v2") {
        lithiumCrawler.getBulkContentV2.getAllboards(authObj, authObj.contentSourceId, [], 100, 0, "", function (err, boards) {
          let boardsArr = []
          boards.forEach(x => {
            boardsArr.push(x.id)
          });
          callback(null, boardsArr)

        })
      }
    } else {
      callback(null, [])
    }
  })
}

function getBoardNamesByUser(userObj,req, callback){
  userObj.boardsArr = [];
  async.auto({
    get_search_client_settings: function(cb){
      getSearchclientSettings(userObj.uid,req,cb)
    },
    get_lithium_boards:['get_search_client_settings',function (dataFromAbove,cb) { //get boards array out of the community
      if(userObj.boardsArr.length<=1 && !userObj.searchTuning && dataFromAbove.get_search_client_settings.sources.find(x=>{if(x.enabled && x.content_source_type_id==2) return x})?dataFromAbove.get_search_client_settings.sources.find(x=>{if(x.enabled && x.content_source_type_id==2) return x}).id:"")
      {
        getOpenLithiumBoards(dataFromAbove.get_search_client_settings.sources.find(x=>{if(x.enabled && x.content_source_type_id==2) return x}).id,function (err,boards) {
          cb(null,{
            boards:boards,
            indices: dataFromAbove.get_search_client_settings.sources.filter(x=>{return x.enabled}).map(x=>{return x.index})
          })
        })
      }
      else
      {
        cb(null,{boards: [], indices: dataFromAbove.get_search_client_settings.sources.filter(x=>{return x.enabled}).map(x=>{return x.index})})
      }
    }]
  }, function(error, result){
    if(!error){
      //console.log('boards: '+JSON.stringify(result));
      callback(null, result.get_lithium_boards);
    }else{
      callback(null, {boards: [], indices: []});
    }
  });
}

// API's
router.post("/authSURecommendation", function (req, res, next) {
  res.setHeader("Access-Control-Allow-Origin", req.headers.origin);
  req.body.auth = true;
  getRecommendationResult(req, res);
});

router.post("/getRecommendedResult", function (req, res, next) {
  res.setHeader("Access-Control-Allow-Origin", "*");
  getRecommendationResult(req, res);
});

var getRecommendationResult = function (req, res) {
  var userObj = {};
  var preview = 0;
  if (req.body.useremail) userObj.user = req.body.useremail;
  var ip = req.header('x-forwarded-for') || req.connection.remoteAddress;
  if(!ip || ip == 1)try{ip = req.headers['x-client-ip'];         } catch (e) {}
  if(!ip || ip == 1)try{ip = req.headers['x-forwarded-for'];     } catch (e) {}
  if(!ip || ip == 1)try{ip = req.headers['cf-connecting-ip'];    } catch (e) {}
  if(!ip || ip == 1)try{ip = req.headers['true-client-ip'];      } catch (e) {}
  if(!ip || ip == 1)try{ip = req.headers['x-real-ip'];           } catch (e) {}
  if(!ip || ip == 1)try{ip = req.headers['x-forwarded'];         } catch (e) {}
  if(!ip || ip == 1)try{ip = req.headers['forwarded-for'];       } catch (e) {}
  if(!ip || ip == 1)try{ip = req.headers.forwarded;              } catch (e) {}
  if(!ip || ip == 1)try{ip = req.connection.remoteAddress;       } catch (e) {}
  if(!ip || ip == 1)try{ip = req.connection.socket.remoteAddress;} catch (e) {}
  if(!ip || ip == 1)try{ip = req.socket.remoteAddress;           } catch (e) {}
  if(!ip || ip == 1)try{ip = req.info.remoteAddress;             } catch (e) {}

  if(ip && ip.indexOf(":") > -1) {
      ip = ip.split(":");
      ip = ip[ip.length - 1];
  }

  if ((!userObj.user) || userObj.user == "<EMAIL>" || userObj.user == "undefined") userObj.user = req.cookies._uemail;
  if ((!userObj.user) || userObj.user == "undefined") userObj.user = ip;
  if (req.session.id)
    userObj.cookie = req.session.id;

    async.series([
      (cbin) => {
        if (req.query.sid) {
          var taidQuery = {
            "query": {
              "match": {
                "cookie": req.query.sid
              }
            }
          }
          var optionToGetTaid = {
            url: 'http://localhost:8035/' + config.get('elasticIndex.analytics') + '/log/_search?size=1',
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(taidQuery)
          };
          request(optionToGetTaid, function (error, response, data) {
            if (!error && (response.statusCode==200 || response.statusCode==201)) {
              data = JSON.parse(data);
              if(data.hits.hits && data.hits.hits.length>0)
                userObj.cookie = data.hits.hits[0]._source.taid;
              else
                userObj.cookie = req.query.sid;
              cbin(null, userObj);
            }
            else{
              cbin(null, {});
            }
          })
        }
        else{
          cbin(null, userObj);
        }
      }],
      function(err, asyncRes){
        userObj.uid = req.body.uid;
        commonFunctions.errorlogger.info('recommender: ' , JSON.stringify(userObj));
        if (userObj.uid) {
          getBoardNamesByUser(userObj,req, (e, settings) => {
            getItemsByPermission(userObj.uid, settings, (ein, items) => {
              var userEmail = md5(userObj.user + md5salt);
              var cookie = userObj.cookie;

              /* universal.getIndexNames(userObj.generateSearchClientID,function(err, indices){ */
              try {
                // predictionIO query to engine for retrieving recommendation urls
                var headers = {
                  'Content-Type': 'application/json'
                };

                var dataString = {
                  "user": userObj.cookie || ip,
                  "num": 4,
                  "whiteList": items
                };
                /* if (typeof userEmail == 'undefined' || userEmail.indexOf('@nomail.com') > 0 || userEmail.trim() == '') {
                 dataString.user = cookie;
                 } */

                var options = {
                  url: 'http://' + config.get('predictionIO.engineServerHost') + '/queries.json',
                  method: 'POST',
                  headers: headers,
                  body: JSON.stringify(dataString)
                };

                function callback(errorin, responsein, bodyin) {
                  if (!errorin && (responsein.statusCode == 200 || responsein.statusCode == 201)) {

                    var funcarray = [];
                    if (typeof bodyin == 'string')
                      bodyin = JSON.parse(bodyin);
                    commonFunctions.errorlogger.info('recommender response success', bodyin);
                    bodyin.itemScores.map(function (urlObj) {
                      var url = urlObj.item;
                      var obj = {}
                      obj["index"] = url.split('/')[0];
                      obj["type"] = url.split('/')[1];
                      obj["id"] = decodeURIComponent(url.split('/')[2]);
                      funcarray.push(function (cb) {
                        client.get(obj, function (errin, datain) {
                          //console.log(errin, datain);
                          if (!errin)
                            cb(null, datain);
                          else {
                            cb(null, {"found": false});
                          }
                        });
                      });

                      return urlObj;
                    });
                    if (funcarray.length > 0) {
                      async.parallel(funcarray, function (errorp, resultsp) {
                        resultsp = resultsp.filter(function (source_item) {
                          return source_item.found;
                        });
                        responseFormat(resultsp, userObj,req, function (err, resToSend) {
                          if (!err) {
                            res.send({
                              "result": resToSend,
                              "aggregationsArray": [],
                              "suggest": {},
                              "message": "success",
                              "preview": preview
                            });
                          }
                          else {
                            res.send({
                              "result": [],
                              "aggregationsArray": {},
                              "suggest": {},
                              "preview": preview,
                              "message": " Some error" + err
                            });
                          }
                        });
                      });
                    } else {
                      commonFunctions.errorlogger.warn('recommender  empty response');
                      res.send({
                        "result": [],
                        "aggregationsArray": {},
                        "suggest": {},
                        "preview": preview,
                        "message": "Empty response from recommender"
                      });
                    }
                  } else {
                    commonFunctions.errorlogger.error(errorin);
                    commonFunctions.errorlogger.info('recommender response and body', responsein, bodyin);
                    res.send({
                      "result": {
                        "hits": []
                      },
                      "aggregationsArray": {},
                      "suggest": {},
                      "preview": preview,
                      "message": " Some error" + errorin
                    });
                  }
                }

                if (req.body.auth) {
                  toggleRecommendations(userObj.uid,req, function (err, rec) {
                    togglePreview(userObj.uid,req, function (errP, previewR) {
                      preview = previewR;
                      request(options, callback);
                    })
                  })
                }
                else {
                  commonFunctions.getAutoprovisonToken("", req.body.accessToken || req.body.provisionToken, req,function (resultToken) {
                    toggleRecommendations(userObj.uid,req, function (err, rec) {
                      togglePreview(userObj.uid,req, function (errP, previewR) {
                        preview = previewR;
                        if (!resultToken || !rec) {
                          commonFunctions.errorlogger.warn('invalid accessToken');
                          res.send({
                            "result": {
                              "hits": []
                            },
                            "aggregationsArray": {},
                            "suggest": {},
                            "message": " Invalid Authentication",
                            "preview": preview
                          })
                        }
                        else request(options, callback);
                      })
                    })

                  })
                }
              } catch (e) {
                commonFunctions.errorlogger.error(e);
                res.send([]);
              }
            });
          });
          /* }); */
        } else {
          commonFunctions.errorlogger.error('uid null');
          res.send([]);
        }
      }
    )
}

router.post("/getPreviewResult", function (req, res, next) {
  connection[req.headers['tenant-id']].execute.query(`SELECT id FROM content_sources WHERE elasticIndexName=?`, [req.body.sourceName], function (err, results) {
    var content_source_id = results[0].id;
    commonFunctions.errorlogger.info("Content Sources id is: " , results[0].id);
    commonFunctions.getContentSourceDataById(content_source_id,req, function (err, resultData) {
      if (err) {
        commonFunctions.errorlogger.error("Error", err);
      }
      else {
        var options = {
          url: 'http://localhost:8035/' + resultData.contentSource.elasticIndexName + '/' + req.body.objName + '/' + req.body.id,
          method: 'GET',

        };
        request(options, function (error, response, body) {
          if (error) {
            res.status(400).send({ "error": error });
          }
          else {
            var body = JSON.parse(body);
            res.send({ "result": body._source.html_body });
          }
        });

      }
    });
  });
});


router.post("/getLastSearchQuery", function (req, res, next) {

  // console.log("cokkie id is--->", req.cookies.cookie.id);
  var userEmail = md5(req.body.user + md5salt);
  var uid = req.body.uid;
  if (uid) {
    var getCokkie = {
      "from": 0,
      "size": 1,
      "query": {
        "bool": {
          "must": [{
            "term": {
              "email": userEmail
            }
          }]
        }
      },
      "sort": {
        "ts": {
          "order": "desc"
        }
      }
    };
    var optionsGetCokkie = {
      url: 'http://localhost:8035/' + config.get('elasticIndex.analytics') + '/log/_search',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(getCokkie)
    };
    try {
      function callbackGetCokkie(errorin, responsein, bodyin) {
        if (!errorin && (responsein.statusCode == 200 || responsein.statusCode == 201)) {
          var funcarray = [];
          if (typeof bodyin == 'string')
            bodyin = JSON.parse(bodyin);
            commonFunctions.errorlogger.info('last search query response success', bodyin);
          var dataString = {
            "from": 0,
            "size": 1,
            "query": {
              "bool": {
                "must": [{
                  "term": {
                    "cookie": bodyin.hits.hits[0]._source.cookie
                  }
                }]
              }
            },
            "sort": {
              "search_date": {
                "order": "desc"
              }
            }
          };

          var options = {
            url: 'http://localhost:8035/' + config.get('elasticIndex.analytics') + '/search_keyword/_search',
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(dataString)
          };
          function callback(errorin, responsein, bodyin) {
            if (!errorin && (responsein.statusCode == 200 || responsein.statusCode == 201)) {
              var funcarray = [];
              if (typeof bodyin == 'string')
                bodyin = JSON.parse(bodyin);
                commonFunctions.errorlogger.info('last search query response success', bodyin);
              var lastsearchedquery = (bodyin && bodyin.hits && bodyin.hits.hits[0]) ? bodyin.hits.hits[0]['_source']['text_entered'] : '';

              res.send({ "lastsearchedquery": lastsearchedquery });
            } else {
              commonFunctions.errorlogger.error(errorin);
              commonFunctions.errorlogger.info('lastSearchQuery---search_keyword_index', responsein, bodyin);
              res.send({ "lastsearchedquery": "" });
            }

          }
          request(options, callback);
          // res.send({ "data": bodyin });
        } else {
          commonFunctions.errorlogger.error(errorin);
          commonFunctions.errorlogger.info('lastSearchQuery---analytics_log_index',responsein, bodyin);
          res.send({ "lastsearchedquery": "" });
        }

      }
      commonFunctions.getAutoprovisonToken("", req.body.accessToken,req, function (resultToken) {
        if (!resultToken) {
          commonFunctions.errorlogger.warn('invalid accessToken');
          res.send({ "lastsearchedquery": "" });
        }
        else request(optionsGetCokkie, callbackGetCokkie);
      })

    } catch (e) {
      commonFunctions.errorlogger.error(e);
      res.send({ "lastsearchedquery": "" });
    }
  } else {
    commonFunctions.errorlogger.warn('uid null');
    res.send({ "lastsearchedquery": "" });
  }
});

function toggleRecommendations(uid,req, cb) {
  var sql = "Select recommendations from search_clients where uid=?"
  connection[req.headers['tenant-id']].execute.query(sql, [uid], function (err, results) {
    cb(null, results[0].recommendations ? true : false)
  })
}

function togglePreview(uid,req, cb) {
  var sql = "Select preview from search_clients where uid=?"
  connection[req.headers['tenant-id']].execute.query(sql, [uid], function (err, results) {
    cb(null, results[0].preview ? true : false)
  })
}


module.exports = {
  router: router,
  createNewPageEvent: createNewPageEvent,
  createNewUserEvent: createNewUserEvent,
  createNewConversionEvent: createNewConversionEvent
};
