const async = require("async");

const updateFieldCollation = (query,cb) => {
    let q = connection.query(query, (err, result) => {
        cb(err, result);
    })
}

const changeCollation = function () {
    async.auto({
        changeDbCollation: (cb) => {
            const dbCollation = 'ALTER DATABASE ' + config.get('databaseSettings.database') +
                ' DEFAULT CHARSET=utf8 COLLATE utf8_general_ci';
            let q = connection.query(dbCollation, (err, result) => {
                cb(err, result);
            })
        },
        changeTableCollation: (cb) => {
            let asyncTasks = [];
            const tableCollation = `SELECT CONCAT("ALTER TABLE ", TABLE_SCHEMA, '.', TABLE_NAME 
                                    ," COLLATE utf8_general_ci;") AS ExecuteTheString 
                                    FROM INFORMATION_SCHEMA.TABLES 
                                    WHERE TABLE_SCHEMA='${config.get('databaseSettings.database')}' 
                                    AND TABLE_TYPE="BASE TABLE" AND INFORMATION_SCHEMA.TABLES.TABLE_COLLATION NOT IN ('utf8_general_ci');`
            let q = connection.query(tableCollation, (err, result) => {
                if (err) cb(err, null);
                if (result) {
                    result.map(x => {
                        asyncTasks.push(updateFieldCollation.bind(null, Object.values(x)[0]));
                    })
                    cb(err, asyncTasks);
                }
            })
        },
        fieldCollation: (cb) => {
            let asyncTasks = [];
            const fieldCollation = `SELECT concat
            (
                'ALTER TABLE ', 
                    t1.TABLE_SCHEMA, 
                    '.', 
                    t1.table_name, 
                    ' MODIFY ', 
                    t1.column_name, 
                    ' ', 
                    t1.column_type,
                    ' CHARACTER SET utf8 COLLATE utf8_general_ci',
                    if(t1.is_nullable='YES', ' NULL', ' NOT NULL'),
                    if(t1.column_default is not null, concat(' DEFAULT \'', t1.column_default, '\''), ''),
                    ';'
            )
        from 
            information_schema.columns t1
        where 
            t1.TABLE_SCHEMA like '${config.get('databaseSettings.database')}' AND
            t1.COLLATION_NAME IS NOT NULL AND
            t1.COLLATION_NAME NOT IN ('utf8_general_ci');`
            let q = connection.query(fieldCollation, (err, result) => {
                if (err) cb(err, null);
                else {
                    result.map(x => {
                        asyncTasks.push(updateFieldCollation.bind(null, Object.values(x)[0]));
                    })
                    cb(err, asyncTasks);
                }
            })
        },
        implementCollation: ["changeTableCollation", "fieldCollation", (dataFromAbove, cb) => {
            if (dataFromAbove.changeTableCollation.length && dataFromAbove.fieldCollation.length) {
                let allQueries = dataFromAbove.changeTableCollation.concat(dataFromAbove.fieldCollation);
                if (allQueries.length) {
                    async.parallel(allQueries, function (errAsync, dataAsync) {
                        cb(errAsync, dataAsync)
                    });
                } else {
                    cb(null, null);
                }
            }
        }]
    }, (error, result) => {
        if (error)
            console.log('Error in collation update');
        else
            console.log('Collation Update done');

    })
}

module.exports = {
    changeCollation: changeCollation
}