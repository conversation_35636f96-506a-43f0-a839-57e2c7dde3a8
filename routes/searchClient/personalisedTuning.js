/**
 * <AUTHOR>
 * @version 1(17-05-2018)
 * @description Program for personalised tuning based on user conversions and common facets
 */
var path = require('path');
var async = require('async');
var router = require('express').Router();
var commonFunctions = require('./../../utils/commonFunctions');
var elastic = require('elasticsearch');
var universal = require('../universal');

client = new elastic.Client({
  host: config.get('elasticIndex.host')+':'+config.get('elasticIndex.port'),
  log: [{
    type: 'stdio',
    levels: ['error'] // change these options
  }]
});

// 1. method to get personalisation facets for user
// 2. method to
module.exports = router;