const path = require('path');
const fs = require('fs');
environment = require('../../../routes/environment');
process.env.NODE_ENV = environment.configuration;
const configPath = path.join(__dirname, '../../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
const request = require("request");
const connection_sql = require('../../../utils/connection');
const async = require('async');
const router = require('express').Router();
const jsforce = require('jsforce');
const commonFunctions = require('../../../utils/commonFunctions');
const appVariables = require('../../../constants/appVariables');
const botAnalytics = require('../analytics/botAnalyticsLib');
const communityHelperSettings = require('../communityHelperSettings/communityHelperSettings');
const templates = require('../personalizedTemplateSettings/templateSettings');
const getSearchResults = require('../getSearchResults/getSearchResults');
const { spawn } = require('child_process');

//SQL issue
// var feedsTrain = {
//   get runFeedTrainer() {
//     if (process.argv[1].endsWith('feedPost.js') && process.argv[2])  {
//       connection_sql.handleDisconnect().then(function (result) {
//         commonFunctions.errorlogger.info("process started for ", process.argv[2]);
//         preProcessFeedPost(process.argv[2], tenantId, (error, results) => {
//           if (results) {
//             commonFunctions.errorlogger.info('going to publish');
//             botAnalytics.insertBotAnalytics(results, 1, (aError, aResult) => {
//               process.exit(0);
//             })
//           } else {
//             commonFunctions.errorlogger.info('unable to publish', error);
//             process.exit(0);
//           }

//         })
//       });
//     }
//   },
// };

const meanSalesforceFunction = (csData) => {

  const connSF = new jsforce.Connection({
    "oauth2": {
      loginUrl: csData.url,
      clientId: csData.client_id,
      clientSecret: csData.client_secret,
      redirectUri: appVariables.oAuthRedirectURI
    },
    "instanceUrl": csData.instanceURL,
    "accessToken": csData.accessToken,
    "refreshToken": csData.refreshToken,
    "version":"47.0"
  });
  return connSF
}

const initializeSFConnection = (botId, tenantId, callback) => {
  async.auto({
    getBotSettings: (cb) => {
      if (botId)
        communityHelperSettings.getBotSettings(botId, tenantId, cb);
      else
        cb([], null);
    },
    initializeConnection: ["getBotSettings", (dataFromAbove, cb) => {
      if (dataFromAbove.getBotSettings) {
        let connSF = meanSalesforceFunction(dataFromAbove.getBotSettings);
        cb(null, connSF);
      }
      else
        cb([], null);
    }]
  }, function (error, result) {
    callback(error, result);
  })
}

const fetchFeedFields = (connSF, fields, condition, cb) => {
  const soql = "SELECT " + fields.join(',') + " FROM FeedItem WHERE " + condition;
  connSF.query(soql, function (err, result) {
    cb(err, result)
  });
};

const getConnectedUserInfo = (connSf, userId, cb) => {
  let soql = "SELECT Id, Name, Email, UserRole.Name, ProfileId, AccountId, ContactId, UserType FROM User where id = '" + userId + "'";
  connSf.query(soql, function (err, result) {
    commonFunctions.errorlogger.info("err", err)
    if (result) result = result.records;
    cb(err, result);
  });
};

const getUidForBot = (botId, tenantId, cb) => {
  let sql = `select community_helper_bot.id, community_helper_bot.name, community_helper_bot.platform_type_id, search_client_id, 
              community_helper_bot.community, community_helper_bot.id, search_clients.uid as uid,
              community_helper_bot_responses.response_freq_type, community_helper_bot_responses.response_freq_time
              from community_helper_bot left join search_clients on 
              community_helper_bot.search_client_id = search_clients.id left join 
              community_helper_bot_responses on 
              community_helper_bot.id = community_helper_bot_responses.community_helper_bot_id 
              where community_helper_bot.id=?`;
    connection[tenantId].execute.query(sql, botId, function (err, rows) {
    if (!err && rows.length) {
      let resultToSend = { "id": rows[0].search_client_id, "name": rows[0].name, "uid": rows[0].uid, "community": JSON.parse(rows[0].community).Id, "frequencyType": rows[0].response_freq_type, "frequencyTime": rows[0].response_freq_time, "platform_id": rows[0].platform_type_id };
      cb(null, resultToSend);
    }
    else {
      commonFunctions.errorlogger.info("fetch all community helper bots", err);
      cb(null, []);
    }
  })
};

const preProcessFeedPost = (botId, req, callback) => {
  const tenantId = req.headers['tenant-id'];
  async.auto({
    initializeConnection: (cb) => {
      if (botId)
        initializeSFConnection(botId,tenantId, cb);
      else
        cb(error, []);
    },
    getUidForBot: (cb) => {
      getUidForBot(botId, tenantId, cb);
    },
    getBotContentSources: ["getUidForBot", (dataFromAbove, cb) => {
      if (dataFromAbove.getUidForBot && dataFromAbove.getUidForBot.id) {
        communityHelperSettings.getBotContentSources(dataFromAbove.getUidForBot.id, botId, req, (error, result) => {
          if (error) {
            cb(error, null);
          } else {
            let resultToSend = [{ "type": "_type", "filter": [] }];
            result.map(x => {
              if (x.enabled) {
                x.objects.map(o => {
                  if (o.enabled) {
                    resultToSend[0].filter.push(o.name);
                  }
                })
              }
            })
            cb(null, resultToSend);
          }
        });
      } else
        cb([], null);
    }],
    fetchTemplate: (cb) => {
      templates.fetchAllTemplates(botId, tenantId, (error, result) => {
        if (!error && result && result.length) {
          let randomTemplate = result[Math.floor(Math.random() * result.length)];
          let templateBody = randomTemplate.template_body;
          cb(null, templateBody);
        } else {
          cb(error, '');
        }
      });
    },
    getTemplateFields: ["fetchTemplate", (dataFromAbove, cb) => {
      let template = dataFromAbove.fetchTemplate;
      let searchFields = templates.getSearchFields(template);
      let platformFields = templates.getPlatformFields(template);
      let allFields = { searchFields, platformFields };
      cb(null, allFields);
    }],
    fetchDailyFeeds: ["initializeConnection", "getUidForBot", (dataFromAbove, cb) => {
      if (dataFromAbove.initializeConnection && dataFromAbove.getUidForBot) {
        let connSF = dataFromAbove.initializeConnection.initializeConnection;
        let frequencyTime = dataFromAbove.getUidForBot.frequencyTime;
        let frequencyType = dataFromAbove.getUidForBot.frequencyType;
        let date = new Date(new Date().setHours(new Date().getHours() - 12)).toISOString();
        if (frequencyType === 'Hours')
          date = new Date(new Date().setHours(new Date().getHours() - frequencyTime)).toISOString();
        if (frequencyType === 'Minutes')
          date = new Date(new Date().setMinutes(new Date().getMinutes() - frequencyTime)).toISOString();
        if (frequencyType === 'Days')
          date = new Date(new Date().setDate(new Date().getDate() - frequencyTime)).toISOString();
        let condition = " CreatedDate < " + date + " And CommentCount = 0 AND networkscope = '" + dataFromAbove.getUidForBot.community + "'";
        let fields = ["Id", "status", "body", "type", "title", "createdbyid", "createddate"];

        fetchFeedFields(connSF, fields, condition, (error, result) => {
          if (!error) {
            commonFunctions.errorlogger.info("count of result===", result.records.length)
            cb(null, result.records);
          } else {
            cb(error, []);
          }
        })
      } else {
        cb(error, []);
      }
    }]
  }, (error, result) => {
    if (error) {
      commonFunctions.errorlogger.info(error);
      callback('Some error occured!!', null);
    } else {
      if (result && result.initializeConnection && result.getUidForBot && result.fetchDailyFeeds && result.initializeConnection && result.getTemplateFields) {
        let asyncTasks = [];
        let platformFields = result.getTemplateFields.platformFields;
        for (var j = 0; j < result.fetchDailyFeeds.length; j++) {
          let allPlatformFields = platformFields.find((x) => !result.fetchDailyFeeds[j][x]);
          if (!allPlatformFields) {
            let searchString = '';
            if (result.fetchDailyFeeds[j].Title)
              searchString = result.fetchDailyFeeds[j].Title;
            if (result.fetchDailyFeeds[j].Body)
              searchString = searchString + ' ' + result.fetchDailyFeeds[j].Body.substring(0, 100);
            searchString = commonFunctions.stripHtml(searchString);
            //  searchString = templates.getTokenizeText(searchString);
            let feedFields = [];
            for (let x in result.fetchDailyFeeds[j]) {
              feedFields.push({ "name": x, "value": result.fetchDailyFeeds[j][x] })
            }
            let processVariables = {
              botId,
              searchString,
              connSF: result.initializeConnection.initializeConnection,
              feedFields,
              userId: result.fetchDailyFeeds[j].CreatedById,
              feedId: result.fetchDailyFeeds[j].Id,
              getUidForBot: result.getUidForBot,
              connectionInfo: result.initializeConnection.initializeConnection,
              getBotContentSources: result.getBotContentSources,
              fetchTemplate: result.fetchTemplate,
              getTemplateFields: result.getTemplateFields
            }
            asyncTasks.push(sendFeedPost.bind(null, processVariables, req));
          } else {
            commonFunctions.errorlogger.info('Unable to send result as feed fields are', allPlatformFields);
          }
        }
        async.series(asyncTasks, function (err, data) {
          if (err) {
            commonFunctions.errorlogger.info(error);
            callback('Some error occured!!', null);
          } else {
            callback(null, data);
          }
        });
      } else {
        commonFunctions.errorlogger.info(result);
        callback('Some error occured!!', null);
      }
    }
  })

};

const sendFeedPost = (processVariables, req, callback) => {
  async.auto({
    getUserInfo: (cb) => {
      let userId = processVariables.userId;
      let connSF = processVariables.connectionInfo;

      if (connSF) {
        getConnectedUserInfo(connSF, userId, (error, result) => {
          if (error)
            cb(error, []);
          else {
            cb(null, result[0]);
          }
        });
      } else cb('No connection', null);
    },
    getSearchResult: ["getUserInfo", (dataFromAbove, cb) => {
      if (dataFromAbove.getUserInfo) {
        let searchObj = {
          "uid": processVariables.getUidForBot.uid,
          "AccountId": dataFromAbove.getUserInfo.AccountId || '',
          "ContactId": dataFromAbove.getUserInfo.ContactId || '',
          "ProfileId": dataFromAbove.getUserInfo.ProfileId || '',
          "UserId": dataFromAbove.getUserInfo.Id,
          "UserType": dataFromAbove.getUserInfo.UserType,
          "email": dataFromAbove.getUserInfo.Email,
          "aggregationsArray": processVariables.getBotContentSources,
          "searchString": processVariables.searchString
        };
        getSearchResults.getSearchResults(searchObj, processVariables.getTemplateFields, req, (error, result) => {
          cb(error, result);
        });
      } else {
        cb('No user info!', []);
      }
    }],
    evaluateTemplateBody: ["getSearchResult", (dataFromAbove, cb) => {
      let templateValue = '';
      if (dataFromAbove.getSearchResult && dataFromAbove.getSearchResult.length && processVariables.fetchTemplate && processVariables.feedFields) {
        templateValue = templates.getTemplateWithValues(processVariables.fetchTemplate, dataFromAbove.getSearchResult, processVariables.feedFields);
      }
      cb(null, templateValue);
    }],
    sendFeedPost: ["evaluateTemplateBody", (dataFromAbove, cb) => {
      let templateBody = dataFromAbove.evaluateTemplateBody;
      let communityId = processVariables.getUidForBot.community;
      if (processVariables.connSF && processVariables.feedId && templateBody && communityId) {

        let commentsUrl = '/connect/communities/' + communityId + '/chatter/feed-elements/' + processVariables.feedId + '/capabilities/comments/items';
        processVariables.connSF.chatter.resource(commentsUrl).create({
          body: {
            messageSegments: templateBody
          }
        }, function (err, result) {
          if (err) { cb(err, null); } else {
            commonFunctions.errorlogger.info('output feedposts======', JSON.stringify(result));
            cb(null, result);
          }
        });

      } else {
        commonFunctions.errorlogger.info(processVariables)
        cb('Process variables issue', null);
      }

    }]
  }, function (error, result) {
    let response = { "flag": 400, "data": [] }
    if (error) {
      commonFunctions.errorlogger.info("add comment in community helper", error);
      callback(null, null)
    } else {
      let finalResult = {};
      if (result && result.sendFeedPost && processVariables)
        finalResult = { postData: result.sendFeedPost, botData: processVariables };
      callback(null, finalResult);
    }
  })
};

router.get('/getCommunities', function (req, res) {
  const tenantId = req.headers['tenant-id'];
  async.auto({
    initializeConnection: (cb) => {
      if (req.query.botId)
        initializeSFConnection(req.query.botId, tenantId, cb);
      else
        cb([], null);
    },
    getCommunities: ["initializeConnection", (dataFromAbove, cb) => {
      let connSF = dataFromAbove.initializeConnection.initializeConnection;

      if (connSF) {
        let soql = "SELECT Id, Name FROM Network";
        connSF.query(soql, function (err, result) {
          commonFunctions.errorlogger.info("err", err)
          if (result) result = result.records;
          cb(err, result);
        });
      } else cb([], null);
    }]
  }, function (error, result) {
    let response = { "flag": 400, "data": [] }
    if (error) {
      commonFunctions.errorlogger.info("get communities in community helper", error);
    } else {
      response.flag = 200;
      response.data = result.getCommunities;
    }
    res.send(response);
  })
});

router.get('/getAgentsAndFeedFields', function (req, res) {
  const tenantId = req.headers['tenant-id'];
  async.auto({
    initializeConnection: (cb) => {
      if (req.query.botId)
        initializeSFConnection(req.query.botId, tenantId, cb);
      else
        cb([], null);
    },
    getConnectedAgent: ["initializeConnection", (dataFromAbove, cb) => {
      let connSF = dataFromAbove.initializeConnection.initializeConnection;
      connSF.identity(cb);
    }],
    getAgentList: ["getConnectedAgent", (dataFromAbove, cb) => {
      let userId = dataFromAbove.getConnectedAgent.user_id;
      let connSF = dataFromAbove.initializeConnection.initializeConnection;

      if (connSF) {
        getConnectedUserInfo(connSF, userId, cb);
      } else cb([], null);

    }],
    getFeedFields: ["initializeConnection", (dataFromAbove, cb) => {
      let connSF = dataFromAbove.initializeConnection.initializeConnection;

      if (connSF) {
        connSF.sobject('feeditem').describe(function (err, meta) {
          var result = [];
          let fields = ["title", "status", "body", "type", "createdbyid", "createddate"];
          if (!err) {
            meta.fields.map(r => {
              if (fields.includes(r.name.toLowerCase())) {
                if (r.name.toLowerCase() === "createdbyid") {
                  r.label = "Name"
                }
                result.push(r);
              }
            })
            cb(null, result)
          }
          else {
            cb(err, '')
          }
        });
      }
      else cb([], null);
    }]
  }, function (error, result) {
    let response = { "flag": 400, "data": [] }
    if (error) {
      commonFunctions.errorlogger.info("add comment in community helper", error);
    } else {
      response.flag = 200;
      response.data = { "agents": result.getAgentList, "feedFields": result.getFeedFields };
    }
    res.send(response);
  })
});

router.get('/feedPost', function (req, res) {
  var botId = req.query.botId;
  const tenantId = req.headers['tenant-id'];
  preProcessFeedPost(botId, tenantId, (error, result) => {
    if (result) {
      botAnalytics.insertBotAnalytics(result, 1, tenantId, (aError, aResult) => {
        res.send(aResult);
      })
    } else {
      res.send('Sorry Error Occured!!');
    }
  })
});

router.get('/kudosSolved', function (req, res) {
  botAnalytics.pitchAnalyticsForIds((error, results) => {
  })
});

// feedsTrain.runFeedTrainer

module.exports = {
  router: router,
  initializeSFConnection: initializeSFConnection,
  preProcessFeedPost
};