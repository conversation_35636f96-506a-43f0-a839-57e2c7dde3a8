<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="30" height="10" viewBox="0 0 30 10">
  <defs>
    <clipPath id="clip-Cluster_space_differential_check_and_invalid_WORM_lock">
      <rect width="30" height="10"/>
    </clipPath>
  </defs>
  <g id="Cluster_space_differential_check_and_invalid_WORM_lock" data-name="Cluster space differential check and invalid WORM lock" clip-path="url(#clip-Cluster_space_differential_check_and_invalid_WORM_lock)">
    <g id="Group_4681" data-name="Group 4681" transform="translate(-106 -288)">
      <g id="Rectangle_4642" data-name="Rectangle 4642" transform="translate(107 289)" fill="#a4a4a4" stroke="#a4a4a4" stroke-width="0.4">
        <rect width="8" height="8" rx="1" stroke="none"/>
        <rect x="0.2" y="0.2" width="7.6" height="7.6" rx="0.8" fill="none"/>
      </g>
      <g id="Rectangle_4643" data-name="Rectangle 4643" transform="translate(118 289)" fill="#a4a4a4" stroke="#a4a4a4" stroke-width="0.4">
        <rect width="8" height="8" rx="1" stroke="none"/>
        <rect x="0.2" y="0.2" width="7.6" height="7.6" rx="0.8" fill="none"/>
      </g>
      <g id="arrow-forward" transform="translate(131 293)">
        <g id="arrow-forward-2" data-name="arrow-forward" transform="translate(-5 -5)">
          <g id="arrow-forward-3" data-name="arrow-forward">
            <rect id="Rectangle_4641" data-name="Rectangle 4641" width="10" height="10" transform="translate(0 10) rotate(-90)" fill="#a4a4a4" opacity="0"/>
            <path id="Path_6129" data-name="Path 6129" d="M10.25,8.333H5.308L6.821,10.15a.417.417,0,1,1-.642.533L4.1,8.183a.5.5,0,0,1-.037-.062c0-.021-.021-.033-.029-.054a.4.4,0,0,1,0-.3c0-.021.021-.033.029-.054A.5.5,0,0,1,4.1,7.65l2.083-2.5a.417.417,0,1,1,.642.533L5.308,7.5H10.25a.417.417,0,0,1,0,.833Z" transform="translate(-2.333 -2.917)" fill="#a4a4a4"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
