'use strict';

const constants = require('../constants/appVariables');
const Jwt = require('jsonwebtoken');
const async = require('async');
//const Service = require('../Services');


const getTokenFromDB = function (userId, userType,token, callback) {
    const userData = null;
    const criteria = {
        _id: userId,
        accessToken : token
    };
    async.series([
        function (cb) {
            // if (userType == constants.userRole.Admin){
            //     Service.adminService.getAdmin(criteria,{},{lean:true}, function (err, dataAry) {
            //         if (err){
            //             cb(err)
            //         }else {
            //             if (dataAry && dataAry.length > 0){
            //                 userData = dataAry[0];
            //                 cb();
            //             }else {
            //                 cb(constants.STATUS_ERROR.INVALID_TOKEN)
            //             }
            //         }
            //
            //     });
            //
            // }
            // else if (userType == constants.userRole.Moderator){
            //     Service.moderatorService.getModerator(criteria,{},{lean:true}, function (err, dataAry) {
            //         if (err){
            //             cb(err)
            //         }else {
            //             if (dataAry && dataAry.length > 0){
            //                 userData = dataAry[0];
            //                 cb();
            //             }else {
            //                 cb(constants.STATUS_ERROR.INVALID_TOKEN)
            //             }
            //         }
            //
            //     });
            //
            // }
            // else {
            //     cb(constants.STATUS_ERROR.IMP_ERROR)
            // }
        }
    ], function (err, result) {
        if (err){
            callback(err)
        }else {
            if (userData && userData._id){
                userData.id = userData._id;
                userData.type = userType;
            }
            callback(null,{userData: userData})
        }

    });
};

const setTokenInDB = function (userId,userType, tokenToSave, callback) {
    const criteria = {
        _id: userId
    };
    const setQuery = {
        accessToken : tokenToSave
    };
    async.series([
        function (cb) {
            //  if (userType == constants.userRole.Admin){
            //     Service.adminService.updateAdmin(criteria,setQuery,{new:true}, function (err, dataAry) {
            //         if (err){
            //             cb(err)
            //         }else {
            //             console.log("error",err);
            //             if (dataAry){
            //                 cb();
            //             }else {
            //                 cb(constants.STATUS_ERROR.IMP_ERROR)
            //             }
            //         }
            //     });
            //
            // }
            // else if (userType == constants.userRole.Moderator){
            //     Service.moderatorService.updateModerator(criteria,setQuery,{new:true}, function (err, dataAry) {
            //         if (err){
            //             cb(err)
            //         }else {
            //             if (dataAry && dataAry._id){
            //                 cb();
            //             }else {
            //                 cb(constants.STATUS_ERROR.IMP_ERROR)
            //             }
            //         }
            //
            //     });
            //
            // } else {
            //    cb(constants.STATUS_ERROR.IMP_ERROR)
            //  }
        }
    ], function (err, result) {
        if (err){
            callback(err)
        }else {
            callback()
        }

    });
};

const expireTokenInDB = function (userId,userType, callback) {
    const criteria = {
        _id: userId
    };
    const setQuery = {
        accessToken : null
    };
    async.series([
        function (cb) {
            // if (userType == constants.userRole.Admin){
            //     Service.adminService.updateAdmin(criteria,setQuery,{new:true}, function (err, dataAry) {
            //         if (err){
            //             cb(err)
            //         }else {
            //             if (dataAry && dataAry.length > 0){
            //                 cb();
            //             }else {
            //                 cb(constants.STATUS_ERROR.INVALID_TOKEN)
            //             }
            //         }
            //     });
            //
            // }
            // else if (userType == constants.userRole.Moderator){
            //     Service.modertorService.updateModerator(criteria,setQuery,{new:true}, function (err, dataAry) {
            //         if (err){
            //             cb(err)
            //         }else {
            //             if (dataAry && dataAry.length > 0){
            //                 cb();
            //             }else {
            //                 cb(constants.STATUS_ERROR.INVALID_TOKEN)
            //             }
            //         }
            //
            //     });
            //
            // } else {
            //     cb(constants.STATUS_ERROR.IMP_ERROR)
            // }
        }
    ], function (err, result) {
        if (err){
            callback(err)
        }else {
            callback()
        }

    });
};


const verifyToken = function (token, callback) {
    const response = {
        valid: false
    };
    Jwt.verify(token, constants.JWT_SECRET_KEY, function (err, decoded) {
        console.log('jwt err',err,decoded)
        if (err) {
            callback(err)
        } else {
            getTokenFromDB(decoded.id, decoded.type,token, callback);
        }
    });
};

const setToken = function (tokenData, callback) {
    if (!tokenData.id || !tokenData.type) {
        callback(constants.STATUS_ERROR.IMP_ERROR);
    } else {
        const tokenToSend = Jwt.sign(tokenData, constants.JWT_SECRET_KEY);
        setTokenInDB(tokenData.id,tokenData.type, tokenToSend, function (err, data) {
            console.log('token>>>>',err,data);
            callback(err, {accessToken: tokenToSend})
        })
    }
};

const expireToken = function (token, callback) {
    Jwt.verify(token, constants.JWT_SECRET_KEY, function (err, decoded) {
        if (err) {
            callback(constants.STATUS_ERROR.INVALID_TOKEN);
        } else {
            expireTokenInDB(decoded.id,decoded.type, function (err, data) {
                callback(err, data)
            });
        }
    });
};

const decodeToken = function (token, callback) {
    Jwt.verify(token, constants.JWT_SECRET_KEY, function (err, decodedData) {
        if (err) {
            callback(constants.STATUS_ERROR.INVALID_TOKEN);
        } else {
            callback(null, decodedData)
        }
    })
};

module.exports = {
    expireToken: expireToken,
    setToken: setToken,
    verifyToken: verifyToken,
    decodeToken: decodeToken
};
