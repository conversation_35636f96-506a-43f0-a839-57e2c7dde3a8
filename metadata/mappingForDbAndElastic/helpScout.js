module.exports = {
  objects: [
    {
      name: 'conversations',
      label: 'Conversations',
      status: 1,
      boosting_factor: 1,
      base_url: '',
      fields: [
        {
          name: 'id',
          label: 'id',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'number',
          label: 'number',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'threads',
          label: 'threads',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'type',
          label: 'type',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'status',
          label: 'status',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'state',
          label: 'state',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'subject',
          label: 'subject',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'preview',
          label: 'preview',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'mailboxId',
          label: 'mailboxId',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'assigneeId',
          label: 'assignee id',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        }, {
          name: 'assigneeType',
          label: 'assignee type',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        }, {
          name: 'assigneeFirst',
          label: 'assignee first',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        }, {
          name: 'assigneeLast',
          label: 'assignee last',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        }, {
          name: 'assigneeEmail',
          label: 'assignee email',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'createdById',
          label: 'created by id',
          type: 'object',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        }, {
          name: 'createdByType',
          label: 'created by type',
          type: 'object',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        }, {
          name: 'createdByEmail',
          label: 'created by email',
          type: 'object',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'tags',
          label: 'tags',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'createdAt',
          label: 'created at',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'closedBy',
          label: 'closed by',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'closedByUserId',
          label: 'closed by user id',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 0, // analyzer
          isActive: 1
        }, {
          name: 'closedByUserType',
          label: 'closed by user type',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 0, // analyzer
          isActive: 1
        },
        {
          name: 'closedByUserFirstName',
          label: 'closed by user frist name',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 0, // analyzer
          isActive: 1
        }, {
          name: 'closedByUserLastName',
          label: 'closed by user last',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 0, // analyzer
          isActive: 1
        },
        {
          name: 'closedByUserPhotoUrl',
          label: 'closed by user photo url',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 0, // analyzer
          isActive: 1
        },
        {
          name: 'closedByUserEmail',
          label: 'closed by user email',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 0, // analyzer
          isActive: 1
        },
        {
          name: 'userUpdatedAt',
          label: 'user updated at',
          type: 'datetime',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 0, // analyzer
          isActive: 1
        },
        {
          name: 'customerWaitingSinceTime',
          label: 'customer waiting since time',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'customerWaitingSinceFriendly',
          label: 'customer waiting since user friendly format',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'sourceType',
          label: 'source type',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'sourceVia',
          label: 'Source via',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'cc',
          label: 'CC Users Email',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'bcc',
          label: 'Bcc Users Email',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'embeddedThreads',
          label: 'embedded threads',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'selfHref',
          label: 'self href',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'primaryCustomerHref',
          label: 'primary customer href',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'createdByCustomerHref',
          label: 'created by customer href',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'closedByHref',
          label: 'closed by href',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'threadsHref',
          label: 'threads href',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'assigneeHref',
          label: 'links assignee href',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        }
      ]
    },
    {
      name: 'articles',
      label: 'Articles',
      status: 1,
      boosting_factor: 1,
      base_url: '',
      fields: [
        {
          name: 'slug',
          label: 'slug',
          type: 'string',
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'id',
          label: 'id',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'number',
          label: 'number',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'status',
          label: 'status',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {

          name: 'hasDraft',
          label: 'Has Draft',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'name',
          label: 'Title',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'popularity',
          label: 'popularity',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'publicUrl',
          label: 'View Href',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'changeOrigin',
          label: 'Change Origin',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'related',
          label: 'related',
          type: 'array',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'text',
          label: 'Text',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'categories',
          label: 'categories',
          type: 'array',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'collectionId',
          label: 'Collection Id',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'viewCount',
          label: 'View Count',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'createdBy',
          label: 'Created By user Name',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        },
        {
          name: 'updatedBy',
          label: 'Updated by User',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 0, // analyzer
          isActive: 1
        },
        {
          name: 'createdAt',
          label: 'Created at',
          type: 'datetime',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 0, // analyzer
          isActive: 1
        },
        {
          name: 'updatedAt',
          label: 'updated at',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        }, {
          name: 'lastPublishedAt',
          label: 'last published at',
          type: 'string',
          // indexing method analyzed OR not analyzed
          isFilterable: 1, 
          isSortable: 1,
          isSearchable: 1, // analyzer
          isActive: 1
        }]

    }]
};
