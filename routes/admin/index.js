module.exports = {
  contentSources: require('./contentSources').router
  , indexService: require('./indexService').router
  , crawlerConfig: require('./crawlerConfig').router
  , schedulerConfig: require('./schedulerConfig').router
  , oAuthorization: require('./oAuthorization').router
  , searchClient: require('./searchClient').router
  , ecoSystem: require('./ecoSystem').router
  , ABTesting : require('./ab-testing').router
  , ahClient: require('./ahClient')
  , lithium: require('./lithium').router
  , zendesk: require('./zendesk').router
  , drive: require('./drive').router
  , helpscout: require('./helpscout').router
  , box: require('./box').router
  , notifications: require('./notifications').router
  , assetLibrary: require('./assetLibrary').router
  , userManagement: require('./user').router
  , support: require('./support').router
  , version: require('./version').router
  , jiraOnPrem: require('./jiraOnPrem').router
  , intractiveSearch: require('./intractiveSearch')
  , knowledgeGraph: require('./knowledgeGraph').router
  , pollApi: require('./pollApi').router
  , hostedSearchUser: require('./hostedSearchUser').router
  , duplicacyChecker: require('./duplicacyChecker').router
  , getSimilarSearchRecommend: require('./getSimilarSearchRecommend').router
  , crawlerConfigCs: require('./crawlerConfigCs').router
  , higherlogic: require('./higherLogic.routes').router
  , cron: require('./cron').router
  , sendEmail: require('./sendEmail').router
  , publish: require('./publishAllContent').router,
  quotaManager: require('./quota-limit').router,
  stackoverflow: require('./stackoverflow').router,
  customContentSource: require('./customContentSources').router
};
