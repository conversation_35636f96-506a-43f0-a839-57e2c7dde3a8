module.exports = {
    "post": {
        "properties": {
            "Url": {
                "type": "string"
            },
            "authorName": {
                "type": "string",
                "index": "not_analyzed"
            },
            "authorUrl": {
                "type": "string"
            },
            "commentCount": {
                "type": "long"
            },
            "comments": {
                "type": "string",
                "analyzer": "custom_lowercase_stemmed",
                "search_analyzer": "custom_lowercase_synonym"
            },
            "description": {
                "type": "string",
                "analyzer": "custom_lowercase_stemmed",
                "search_analyzer": "custom_lowercase_synonym"
            },
            "id": {
                "type": "string"
            },
            "placeName": {
                "type": "string",
                "index": "not_analyzed"
            },
            "placeUrl": {
                "type": "string"
            },
            "post_time": {
                "type": "datetime",
                "format": "dateOptionalTime"
            },
            "status": {
                "type": "string",
                "index": "not_analyzed"
            },
            "title": {
                "type": "string",
                "analyzer": "custom_lowercase_stemmed",
                "search_analyzer": "custom_lowercase_synonym"
            },
            "type": {
                "type": "string",
                "index": "not_analyzed"
            }
        }
    },
    "page": {
        "properties": {
            "Url": {
                "type": "string"
            },
            "authorName": {
                "type": "string",
                "index": "not_analyzed"
            },
            "authorUrl": {
                "type": "string"
            },
            "commentCount": {
                "type": "long"
            },
            "comments": {
                "type": "string",
                "analyzer": "custom_lowercase_stemmed",
                "search_analyzer": "custom_lowercase_synonym"
            },
            "description": {
                "type": "string",
                "analyzer": "custom_lowercase_stemmed",
                "search_analyzer": "custom_lowercase_synonym"
            },
            "id": {
                "type": "string"
            },
            "placeName": {
                "type": "string",
                "index": "not_analyzed"
            },
            "placeUrl": {
                "type": "string"
            },
            "post_time": {
                "type": "datetime",
                "format": "dateOptionalTime"
            },
            "status": {
                "type": "string",
                "index": "not_analyzed"
            },
            "title": {
                "type": "string",
                "analyzer": "custom_lowercase_stemmed",
                "search_analyzer": "custom_lowercase_synonym"
            },
            "type": {
                "type": "string",
                "index": "not_analyzed"
            }
        }
    }
}

