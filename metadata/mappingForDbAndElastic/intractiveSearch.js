	
module.exports = [{
    "search_query": "Open home",
    "components": "home",
    "component_id": "",
    "component_type": "In Home"
}, {
    "search_query": "open content sources",
    "components": "content-sources",
    "component_id": "",
    "component_type": "In Content-Sources"
}, {
    "search_query": "open search client",
    "components": "generate-search-client",
    "component_id": "",
    "component_type": "In Search-Clint"
}, {
    "search_query": "open search tuning",
    "components": "search-tuning",
    "component_id": "",
    "component_type": "In Search-Tuning"
}, {
    "search_query": "open analytics",
    "components": "analytics-v2",
    "component_id": "",
    "component_type": "In Analytics"
}, {
    "search_query": "open account",
    "components": "account",
    "component_id": "",
    "component_type": "In Account"
}, {
    "search_query": "Open user-role-management",
    "components": "user-role-management",
    "component_id": "",
    "component_type": "In User Role Management"
}, {
    "search_query": "Open synonym",
    "components": "manage-synonyms",
    "component_id": "",
    "component_type": "In NLP Manager",
    "mat_tab": 0
}, {
    "search_query": "Open NLP Manager",
    "components": "manage-synonyms",
    "component_id": "",
    "component_type": "In NLP Manager"
}, {
    "search_query": "Open Apps",
    "components": "addons",
    "component_id": "",
    "component_type": "In Addons"
}, {
    "search_query": "Open Api",
    "components": "apps",
    "component_id": "",
    "component_type": "In App"
}, {
    "search_query": "Open notification",
    "components": "notifications",
    "component_id": "",
    "component_type": "In notifications"
},
{
    "search_query": "Open alerts",
    "components": "notifications",
    "component_id": "",
    "component_type": "In alerts"
}, {
    "search_query": "Search summary report",
    "components": "analytics-v2",
    "component_id": "searchHitCount",
    "component_type": "In Analytics Overview"
}, {
    "search_query": "Searches With No Clicks for successive searches",
    "components": "analytics-v2",
    "component_id": "searchesWithNoClicksForSuccessiveSearches",
    "component_type": "In Analytics content-gap-analysis"
},{
    "search_query": "Searches without result for successive searches",
    "components": "analytics-v2",
    "component_id": "searchesWithoutResultForSuccessiveSearch",
    "component_type": "In Analytics content-gap-analysis"
}, {
    "search_query": "Top rated featured result",
    "components": "analytics-v2",
    "component_id": "topRatedFeaturedResults",
    "component_type": "In Analytics Overview"
}, {
    "search_query": "Open Discussion ready to become help article",
    "components": "analytics-v2",
    "component_id": "helpArticles",
    "component_type": "In Analytics conversions"
}, {
    "search_query": "newly added content source",
    "components": "analytics-v2",
    "component_id": "newlyAddedContentSource",
    "component_type": "In Analytics Overview"
}, {
    "search_query": "Search index by content source",
    "components": "analytics-v2",
    "component_id": "indexBySource",
    "component_type": "In Analytics Overview"
}, {
    "search_query": "Unsuccessful searches graph",
    "components": "analytics-v2",
    "component_id": "unSuccessfulSearchChart",
    "component_type": "In Analytics content-gap-analysis"
}, {
    "search_query": "Unsuccessful search session",
    "components": "analytics-v2",
    "component_id": "unSuccessfulSearchSession",
    "component_type": "In Analytics content-gap-analysis"
}, {
    "search_query": "Search Session tracking Overview",
    "components": "analytics-v2",
    "component_id": "sessionTrackingOverview",
    "component_type": "In Analytics conversions"
}, {
    "search_query": "Open Session Tracking Details",
    "components": "analytics-v2",
    "component_id": "trackingDetail",
    "component_type": "In Analytics conversions"
}, {
    "search_query": "Open Documents by content length",
    "components": "analytics-v2",
    "component_id": "documentsBylength",
    "component_type": "In Analytics content-gap-analysis"
}, {
    "search_query": "filter based conversions",
    "components": "analytics-v2",
    "component_id": "filterBasedConversion",
    "component_type": "In Analytics conversions"
}, {
    "search_query": "attach to case report",
    "components": "analytics-v2",
    "component_id": "attachedToCase",
    "component_type": "In Analytics conversions"
}, {
    "search_query": "Open security",
    "components": "security",
    "component_id": "",
    "component_type": "In Security"
}, {
    "search_query": "Open chat bot",
    "components": "chat-bot",
    "component_id": "",
    "component_type": "In Chat-Bot"
},
{
    "search_query": "open virtual agent",
    "components": "chat-bot",
    "component_id": "",
    "component_type": "In Chat-Bot"
},
{
    "search_query": "open asset library",
    "components": "asset-library",
    "component_id": "",
    "component_type": "In Asset Library"
}, {
    "search_query": "Open knowledge graph",
    "components": "knowledge-graph",
    "component_id": "",
    "component_type": "In Knowledge Graph"
}, {
    "search_query": "Average Time on a particular page",
    "components": "analytics-v2",
    "component_id": "avgTimeDocument",
    "component_type": "In Analytics content-gap-analysis"
}, {
    "search_query": "Knowledge Graph Titles in analytics",
    "components": "analytics-v2",
    "component_id": "knowledgeGraphTitles",
    "component_type": "In Analytics Overview"
}, {
    "search_query": "Page Rating feedback",
    "components": "analytics-v2",
    "component_id": "pageRatingFeedback",
    "component_type": "In Analytics Overview"
}, {
    "search_query": "Searches with conversions for successive search",
    "components": "analytics-v2",
    "component_id": "searchClassification",
    "component_type": "In Analytics conversions"
}, {
    "search_query": "Most Popular Documents",
    "components": "analytics-v2",
    "component_id": "popularDocuments",
    "component_type": "In Analytics conversions"
}, {
    "search_query": "Top clicked searches",
    "components": "analytics-v2",
    "component_id": "topClickedSearches",
    "component_type": "In Analytics conversions"
}, {
    "search_query": "High Conversion Results Not on Page One",
    "components": "analytics-v2",
    "component_id": "highConversionResultNotFirstPage",
    "component_type": "In Analytics content-gap-analysis"
}, {
    "search_query": "kcs article report",
    "components": "analytics-v2",
    "component_id": "kcsReport",
    "component_type": "In Analytics content-gap-analysis"
}, {
    "search_query": "Searches With No Clicks",
    "components": "analytics-v2",
    "component_id": "searchesWithNoClicks",
    "component_type": "In Analytics Overview"
}, {
    "search_query": "All searches",
    "components": "analytics-v2",
    "component_id": "allSearches",
    "component_type": "In Analytics Overview"
}, {
    "search_query": "Successful searches with clicks",
    "components": "analytics-v2",
    "component_id": "successfulSearchesWithClicks",
    "component_type": "In Analytics Overview"
}, {
    "search_query": "Searches with no result",
    "components": "analytics-v2",
    "component_id": "searchesWithNoResult",
    "component_type": "In Analytics Overview"
}, {
    "search_query": "Open stopwords",
    "components": "manage-synonyms",
    "component_id": "",
    "component_type": "In NLP Manager",
    "mat_tab": 3
}, {
    "search_query": "Open Did You Mean",
    "components": "manage-synonyms",
    "component_id": "",
    "component_type": "In NLP Manager",
    "mat_tab": 2
}, {
    "search_query": "Open Suggestion Removal",
    "components": "manage-synonyms",
    "component_id": "",
    "component_type": "In NLP Manager",
    "mat_tab": 1
}]