module.exports = {
    "objects": [
        {
            "name": "content",
            "label": "Content",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields": [
                {
                    "name": "ModuleId",
                    "label": "Module Id",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 0,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "ModuleTitle",
                    "label": "Module Title",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "CourseTitle",
                    "label": "Course Title",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 0,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "CourseId",
                    "label": "Course Id",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 0,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "TopicTitle",
                    "label": "Topic Title",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 0,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "TopicId",
                    "label": "Topic Id",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 0,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "TopicURL",
                    "label": "Topic URL",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "TopicHTML",
                    "label": "Topic HTML",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 0,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "LastModifiedDate",
                    "label": "Last Modified Date",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 0,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "ActivityType",
                    "label": "Activity Type",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 0,
                    "isSearchable": 1,
                    "isActive": 1
                }
            ]
        }   
    ]
}
