var request = require("request");
var commonFunctions = require('./../../utils/commonFunctions');
var appVariables = require('../../constants/appVariables');


const getAccessToken = function (code, loginUrl, authorization, tokenUrl, redirect_Uri, callback) {
    var headers = {
        'Content-Type': 'application/json'
    };
    var dataString = {
        "grant_type": "authorization_code", "code": code,
        "client_id": authorization.client_id, "client_secret": authorization.organization_user_type ? authorization.client_secret: appVariables.dropbox.appSecret,
        "redirect_uri": redirect_Uri
    };
    var options = {
        url: loginUrl + tokenUrl,
        method: 'POST',
        headers: headers,
        qs: dataString
    };
    request(options, function (error, response, body) {
        if (error) {
            callback(error, null);
        }
        else {
            if (response.statusCode == 200) {
                body = JSON.parse(body);
                callback(null, body);
            }
            else {
                commonFunctions.errorlogger.error("####--Err in Oath--####");
                callback(response.statusMessage, null);
            }

        }
    });

}

const requestTofetchObjectFields = function (authorization, objectName,req, callback) {
    var customFieldArray = [];
    var flag = 7;

    commonFunctions.getContentSourceObjectsAndFieldsById(authorization.content_source_id,req, function (err, res) {
        if (err) {
            callback(err, null)
        }
        else {
          var standardObj = commonFunctions.metadata.loadMappingForDbAndElastic("24").objects;
            var fieldsArr = [];

            for (var i = 0; i < standardObj.length; i++) {
                if (objectName == standardObj[i].name) {
                    flag = 0;
                    fieldsArr = commonFunctions.metadata.loadMappingForDbAndElastic("24").fields.concat(standardObj[i].fields);
                    break;
                }
            }
            var listToAdd = [];
            var listInt = -1;
            for (var type = 0; type < res.length; type++) {
                if (objectName == res[type].name) {
                    listInt = type;
                }
            }
            if (listInt != -1) {
                type = listInt;
                listToAdd = res[type].fields;
                for (var mn = 0; mn < fieldsArr.length; mn++) {
                    var found = 0;
                    for (var field = 0; field < res[type].fields.length; field++) {
                        if (fieldsArr[mn].name == res[type].fields[field].name) {
                            found = 1;
                            break;
                        }
                    }
                    if (found == 0) {
                        listToAdd.push(fieldsArr[mn]);
                    }
                }
                for (var field = 0; field < listToAdd.length; field++) {

                    customFieldArray.push({
                        "name": listToAdd[field].name,
                        "label": listToAdd[field].label,
                        "type": listToAdd[field].type
                    })
                }

            } else {
                for (var field = 0; field < fieldsArr.length; field++) {

                    customFieldArray.push({
                        "name": fieldsArr[field].name,
                        "label": fieldsArr[field].label,
                        "type": fieldsArr[field].type
                    })
                }
            }
            callback(null, { data: customFieldArray, flag: flag });
        }
    })

}

const dropboxAuthorisationIntermediate = function (contentSource, authorization, callback) {
             
    const authUrl = `https://www.dropbox.com/oauth2/authorize?response_type=code&token_access_type=offline&force_reapprove=true&client_id=${authorization.client_id}&redirect_uri=https://oauthsfdc.searchunify.com`;
    callback(null, { "oauth": authUrl });
}

module.exports = {
    getAccessToken: getAccessToken,
    requestTofetchObjectFields: requestTofetchObjectFields,
    dropboxAuthorisationIntermediate: dropboxAuthorisationIntermediate
}
