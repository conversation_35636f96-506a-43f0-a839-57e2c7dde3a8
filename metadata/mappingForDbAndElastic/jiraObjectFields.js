

module.exports= {
  "objects" :[ {
    "name":"issue",
    "label":"Issue",
    "status":1,
    "boosting_factor":1,
    "base_url":"",
    "fields":[]

  }],
  "fields":[
    {
      "name":"id",
      "label":"Id",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"projectName",
      "label":"Project Name",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"projectUrl",
      "label":"Project Url",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"type",
      "label":"Type",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"name",
      "label":"Issue Name",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"issueUrl",
      "label":"Issue Url",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"authorName",
      "label":"Author Name",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"voteCount",
      "label":"Vote Count",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"post_time",
      "label":"Created Date",
      "type":"datetime",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":0, //analyzer
      "isActive":1,
      "isMerged": 1
    },
    {
      "name":"status",
      "label":"Status",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"watchCount",
      "label":"Watch Count",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"description",
      "label":"Description",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"comments",
      "label":"Comments",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"commentCount",
      "label":"Comment Count",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"issueNumber",
      "label":"Issue Key",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"fixVersions",
      "label":"Fix Versions",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"fixVersionsDetail",
      "label":"Fix Version Details",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"resolution_status",
      "label":"Resolution Status",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"resolution_description",
      "label":"Resolution Description",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"lastViewed",
      "label":"Last Viewed",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"priority",
      "label":"Priority",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"labels",
      "label":"Labels",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"issuelinks",
      "label":"Issue links",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"assignee",
      "label":"Assignee",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"components",
      "label":"Components",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"issuekey",
      "label":"Issue key Field",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"subtasks",
      "label":"Subtasks",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"reporter",
      "label":"Reporter",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"aggregateprogress",
      "label":"Aggregate Progress",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"progress",
      "label":"Progress",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"worklog",
      "label":"Worklog",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"issuetype",
      "label":"Issue Type",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"timespent",
      "label":"Time Spent",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"aggregatetimespent",
      "label":"Aggregate Timespent",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"resolutiondate",
      "label":"Resolution Date",
      "type":"datetime",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":0, //analyzer
      "isActive":1
    },
    {
      "name":"workratio",
      "label":"Work Ratio",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"watches",
      "label":"Watches",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"thumbnail",
      "label":"Thumbnail",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"updated",
      "label":"Updated",
      "type":"datetime",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":0, //analyzer
      "isActive":1
    },
    {
      "name":"timetracking",
      "label":"Time Tracking",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"security",
      "label":"Security",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"environment",
      "label":"Environment",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"duedate",
      "label":"Due Date",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    },
    {
      "name":"attachment_issues",
      "label":"Attachment Issues",
      "type":"string",
      "isFilterable":1, //indexing method analyzed OR not analyzed
      "isSortable":1,
      "isSearchable":1, //analyzer
      "isActive":1
    }
  ]
}
