/**
 * Created by manpreet on 11/4/17.
 */

var constants = require('../../constants/constants');
var spawn = require('child_process').spawn;
var path = require('path');
var fs = require('fs');
var async = require('async');
var express = require('express');
var router = express.Router();
var searchunifyEmail = require('../../Lib/email');
var commonFunctions = require('./../../utils/commonFunctions');

router.get('/getAllTickets', function (req, res) {
  var sql = "select *, case WHEN priority=1 then 'Medium' when priority=2 then 'High' else 'Low' end as priority_value, case WHEN status=1 then 'Resolved' else 'Un-resolved' end as status_value from support order by created_date"
  connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
    if (!err) {
      res.send(rows);
    } else {
      commonFunctions.errorlogger.error(err)
    }
  })
});

router.post('/editTicket', function (req, res) {
  var ticket = req.body.ticket;

  switch (ticket.priority) {
    case "High": ticket.priority = 2;
      break;
    case "Medium": ticket.priority = 1;
      break;
    default: ticket.priority = 0
  }

  var values = (ticket.id ? ticket.id : null) + ",'" + ticket.subject + "','" + ticket.priority + "'," + (ticket.status ? ticket.status : 0) + ",'" + (ticket.description || '') + "'," + (ticket.resolved_date ? ticket.resolved_date : null)

  var sql = "Insert into support (id,subject,priority,status,description,resolved_date) VALUES (" + values + ") ON DUPLICATE KEY UPDATE id=VALUES(id),subject=VALUES(subject),priority=VALUES(priority),status=VALUES(status),description=VALUES(description),resolved_date=VALUES(resolved_date)";
  connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
    if (!err) {
      var response = {};
      if (rows.insertId) {
        var email = commonFunctions.appVariables.analyticsEmailList
        email.forEach(email => {
          searchunifyEmail.sendEmail(email, 'New ticket raised', supportCase(email, ticket.priority, ticket.subject, ticket.description), (error, response) => {
            commonFunctions.errorlogger.error("err,response ", error);
            commonFunctions.errorlogger.info('response',response)
          });
        })


      }
      response.status = 200;
      response.message = "OK"
      res.send(response);
    } else {
      commonFunctions.errorlogger.error(err)
    }
  })
})

function supportCase(email, priority, subject, description) {
  var msg = "";
  var template = `<table align="center"; width="600" style="margin: 0 auto; border: 1px solid #ccc; width: 600px;border-collapse: collapse; font-family: Gill Sans Extrabold, sans-serif;">
      <tr>
          <td style="text-align: left;padding: 20px;">
              <img src="${config.get('adminURL') + ":" + config.get('PORT') }" alt="logo.png">
          </td>
      </tr>
      <tr>
          <td style="text-align: center;padding: 20px;color:#fff; background-color: teal;font-size: 25px;">
             SearchUnify: New Ticket Raised
          </td>
      </tr>
      <tr>
          <td style="text-align: center;padding-bottom: 20px; color:#fff; background-color: teal;font-size: 20px;">
             ${subject}
          </td>
      </tr>
      <tr>
          <td style="padding: 15px;">
          Hi, ${email} <br /><br/>
            A new ticket has been raised.<br/><br/>
            <b>Subject:</b> ${subject}<br/>
            <b>Priority:</b> ${priority}<br/>
            <b>Description:</b> ${description}<br/>            
          </td>
      </tr>
      
      <tr><td style="padding: 20px 25px;color:#fff; background-color: teal;font-size: 11px; text-align: center;">
      Powered by © ${new Date().getFullYear()}  <a href="https://www.grazitti.com/" target="_blank" style="color: #fff;text-decoration: none;">Grazitti Interactive</a>. All rights reserved.
      </td></tr>
  </table>
    `;
  return template;
}

module.exports = {
  router: router
};
