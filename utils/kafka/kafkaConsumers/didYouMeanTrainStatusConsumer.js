const kafkaLib = require("../kafka-lib");
const commonFunctions = require("../../commonFunctions");
const { didYouMeanTrainingCompleted } = require("../../../routes/synonyms")



const subscribeConsumer = async () => {
    try{
        const dymConsumer = await kafkaLib.getConsumer({
            groupId: `admin ${config.get("kafkaTopic.dymTrainingStatusTopic")}`,
            topic: config.get("kafkaTopic.dymTrainingStatusTopic"),
            fromBeginning: false
        })
        await dymConsumer.run({
            eachMessage: async ({
              message
            }) => {
              try {
                let data = message.value ? JSON.parse(message.value) : {};
                console.log('dym kafka consumed : ', JSON.stringify(data));
                
                if(data.source && data.source == "didyoumean"){
                   await didYouMeanTrainingCompleted(data)
                }else{
                    commonFunctions.errorlogger.info(`Kafka message not processed | Action: ${data.type} | Source: ${data.source}`);
                }
              } catch (e) {
                  console.log(e)
              }
            }
          });
          dymConsumer.on('consumer.crash', async (event) => {
            try {
              let error;
                if (event && event.payload && event.payload.error) {
                  error = event.payload.error;
                }
              logger.error(`${config.get("kafkaTopic.dymTrainingStatusTopic")} crashed unexpectedly: ${JSON.stringify(error)}`);
            } catch (error) {
              logger.error(`Crash error ${config.get("kafkaTopic.dymTrainingStatusTopic")}: ${JSON.stringify(error)}`);
            }
          });
    }catch(error){
        if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
            kafkaLib.createTopic(config.get("kafkaTopic.dymTrainingStatusTopic"));
          }
    }
}

module.exports = {
    subscribeConsumer
}
