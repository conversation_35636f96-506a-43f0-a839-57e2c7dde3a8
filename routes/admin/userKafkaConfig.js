const async = require('async');
const config = require('config');
const kafkaLib = require("./../../utils/kafka/kafka-lib");
const kafkaStatusLib = require("./../../utils/kafka-status-page/kafka-lib");

const publishUsersViaKafka = function (userEmail, req, callback) {
    async.auto({
        getUserConfig: (cb) => {
            let sql = `select * from user join user_roles 
                         on user.id=user_roles.userId
                         join roles on user_roles.roleId=roles.id`;
            if(userEmail) {
               sql += `  where user.user_email='${userEmail}'`
            }
            connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
                if(rows.length){
                    cb(null, rows);
                }
                else cb(err, null);
            })
        },
        publishUsers: ["getUserConfig", (dataFromAbove, cb) => {
            const options = {};
            const settings = { user: dataFromAbove.getUserConfig, event: "insert"};
            if (settings.user.length) {
                try{
                    kafkaLib.publishMessage({
                        topic:config.get("kafkaTopic.userTopic"),
                        messages:[{
                            value:JSON.stringify(settings),
                            key: req.headers['tenant-id']
                        }]
                    });
                    kafkaStatusLib.publishMessage({
                        topic:config.get("statusPageService.kafka.userTopic"),
                        messages:[{
                            value:JSON.stringify(settings),
                            key: req.headers['tenant-id']
                        }]
                    });
                }catch(e){
                    console.log(e);
                }
            }
            cb(null, []);
        }]
    }, (error, result) => {
        callback(error, result);
    })
};

const deleteUsersViaKafka = (user, callback) => {
    const settings = { event: "delete", user, tenantId: config.get('tenantId') };
    if (settings.user) {
        try{
            kafkaLib.publishMessage({
                topic:config.get("kafkaTopic.userTopic"),
                messages:[{
                    value:JSON.stringify(settings),
                }]
            });
            kafkaStatusLib.publishMessage({
                topic:config.get("statusPageService.kafka.userTopic"),
                messages:[{
                    value:JSON.stringify(settings),
                }]
            });
        }catch(e){
            console.log(e);
        }
    
    
    }
    callback(null, []);
}

module.exports = { publishUsersViaKafka, deleteUsersViaKafka };