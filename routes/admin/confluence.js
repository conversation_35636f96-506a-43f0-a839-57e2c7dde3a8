var commonFunctions = require('./../../utils/commonFunctions');
var oauthUtil = require('./../../utils/oAuthConfluenceJiraUtil');


const confluenceAuthorisationIntermediate = function (contentSource, authorization, req,callback) {
    authorization.privateKey = authorization.privateKey.replace('BEGIN RSA PRIVATE KEY', '');
    authorization.privateKey = authorization.privateKey.replace('END RSA PRIVATE KEY', '');
    authorization.privateKey = authorization.privateKey.replace(/ /g, '\n');
    authorization.privateKey = authorization.privateKey.replace(/-/g, '').replace(/\n/g, '');
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    commonFunctions.errorlogger.info('The auth key is: ', authorization.privateKey);
    var url='';
    var objectToPass = {
      host: url,
      oauth: {
        consumer_key: authorization.publicKey,
        private_key: authorization.privateKey,
        signature_method: 'RSA-SHA1'
      }
    };
    if (contentSource.url.indexOf('https://') != -1) {
      url = contentSource.url.replace('https://', '');
    } else {
      url = contentSource.url.replace('http://', '');
      objectToPass["protocol"]='http';
    }
    objectToPass["host"]=url;
    if (url.indexOf(":") != -1) {
      objectToPass["host"] = url.split(":")[0];
      objectToPass["port"] = url.split(":")[1];
    }
    oauthUtil.getAuthorizeURL(objectToPass, function (error, oauthResult) {
        commonFunctions.errorlogger.info("oauth", oauthResult, error);
        if (oauthResult && oauthResult.token) {
        authorization.refreshToken = oauthResult.token_secret;
        authorization.accessToken = oauthResult.token;
            insertUpdateAuthorization(authorization, req,function (errAsync, rows) {
                if (errAsync) {
                    commonFunctions.errorlogger.error("Error inside insert_content_source_authorization",errAsync);
                    callback({ "error": "Failed" });
                }
                else {
                    callback({ "oauth": oauthResult.url });
                }
            })
        }
        else {
            commonFunctions.errorlogger.info("oauth failed for confluence", oauthResult, error);
            callback({ "error": "Failed" });
        }
    });
}

const insertUpdateAuthorization = function (authorization, req, callback) {
    const colums = []
    const parameters = []

    for (var key in authorization) {
        if (authorization.hasOwnProperty(key)) {
            colums.push(key)
            parameters.push(authorization[key])
        }
    }
    const sqlCS = "INSERT INTO `content_source_authorization`(" + colums +
        ") VALUES (" + colums.map(x => {
            return '?'
        }).join(',') + ") " +
        "ON DUPLICATE KEY UPDATE " + colums.map(x => {
            return x + "=values(" + x + ")"
        }).join(',');
    const q = connection[req.headers['tenant-id']].execute.query(sqlCS, parameters, function (errAsync, rows) {
        if (errAsync) {
            callback(errAsync, [])
        }
        else {
            callback(null, rows)
        }
    })
}

/* Fetch custom and default objects */

const requestTofetchObjectFields = function (authorization, objectName,req, callback) {
    var customFieldArray = [];
    var flag = 7;
    commonFunctions.getContentSourceObjectsAndFieldsById(authorization.content_source_id,req, function (err, res) {
        if (err) {
            callback(err, null)
        }
        else {
            var standardObj = commonFunctions.metadata.loadMappingForDbAndElastic("4").objects;
            var fieldsArr = [];

            for (var i = 0; i < standardObj.length; i++) {
                if (objectName == standardObj[i].name) {
                    flag = 0;
                    fieldsArr = commonFunctions.metadata.loadMappingForDbAndElastic("4").fields.concat(standardObj[i].fields);
                    break;
                }
            }
            var listToAdd = [];
            var listInt = -1;
            for (var type = 0; type < res.length; type++) {
                if (objectName == res[type].name) {
                    listInt = type;
                }
            }
            if (listInt != -1) {
                type = listInt;
                listToAdd = res[type].fields;
                for (var mn = 0; mn < fieldsArr.length; mn++) {
                    var found = 0;
                    for (var field = 0; field < res[type].fields.length; field++) {
                        if (fieldsArr[mn].name == res[type].fields[field].name) {
                            found = 1;
                            break;
                        }
                    }
                    if (found == 0) {
                        listToAdd.push(fieldsArr[mn]);
                    }
                }
                for (var field = 0; field < listToAdd.length; field++) {

                    customFieldArray.push({
                        "name": listToAdd[field].name,
                        "label": listToAdd[field].label,
                        "type": listToAdd[field].type
                    })
                }

            } else {
                for (var field = 0; field < fieldsArr.length; field++) {

                    customFieldArray.push({
                        "name": fieldsArr[field].name,
                        "label": fieldsArr[field].label,
                        "type": fieldsArr[field].type
                    })
                }
            }
            callback(null, { data: customFieldArray, flag: flag });
        }
    })

}

module.exports = {
    confluenceAuthorisationIntermediate: confluenceAuthorisationIntermediate,
    requestTofetchObjectFields: requestTofetchObjectFields
}
