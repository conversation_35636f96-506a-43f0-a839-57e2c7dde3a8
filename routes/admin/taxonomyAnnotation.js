var request = require("request");
var commonFunctions = require('./../../utils/commonFunctions');
var express = require('express');
var router = express.Router();
var multipart = require('connect-multiparty');
var multipartMiddleware = multipart();
const csv = require('csv-parser');
const fs = require('fs');
const appVariables = require('../../constants/appVariables');
const {
  tenantGlobals,
} = require("../../utils/kafka/kafkaPublishers/tenantGlobals");

const mlCoreUrl = appVariables.ml.coreURL

let mlService = function (req, res, cb) {
  ml_url = mlCoreUrl
  // console.log(req.path)
  var options = {
    method: req.method,
    rejectUnauthorized: false,
    url: ml_url + req.path,
    json: true,
    body: {}
  };
  if (req.path === "/get-taxonomy" || req.path === "/get-annotations"||req.path==="/get-synonym" ||req.path==="/get-select-synonym" ) {
    options.body.client_id = req.headers['tenant-id'];
  } else if (req.path === "/create-update-taxonomy" || req.path === "/delete-taxonomy" || req.path === "/create-update-annotation" || req.path === "/delete-annotation" || req.path==="/snippet-status" ||req.path==="/synonym-select" || req.path === "/create-update-synonym" || req.path === "/delete-synonym" ) {
    options.body = req.body;
    options.body.client_id = req.headers['tenant-id'];
    console.log('req body is ',options.body)
  } 
  // commonFunctions.errorlogger.info("|===== Option ======|", options);
  request(options, function (error, response, body) {
    if (error) {
      commonFunctions.errorlogger.error('i am error', error);
      res.send([]);
    }
    else {
      if (req.path==="/synonym-select") {
        tenantGlobals(req.headers["tenant-id"], { useSearchSynonym: req.body});
      }
      res.send(body);
    }
  });
}

router.post("/", multipartMiddleware, function (req, res) {
  const csvResults = {
    "entities": [
      {
        "entity_id": '',
        "name": '',
        "values": []
      }
    ],
    "client_id": ''
  }
  let method = req.method  //'POST'
  let url = ml_url + '/create-update-taxonomy'
  let headers = {
    'Content-Type': 'application/json',
  }
  let list = [];
  if(req.body.value !== '') {
    csvResults.entities[0].values.push(...req.body.value.split(','));
  }
  fs.createReadStream(req.files.upload_entity.path)
  .pipe(csv())
  .on('headers', (data) => {
    /**ignore header */
  })
  .on('data', (data) => {
      list = data;
      for(let i = 0; i < Object.keys(list).length; i++){
        csvResults.entities[0].values.push(Object.values(list)[i].trim())
      }
  })
  .on('error', (error) => {
    commonFunctions.errorlogger.error('i am error', error)
  })
  .on('end', () => {
    csvResults.client_id = req.headers['tenant-id'];
    csvResults.entities[0].name = req.body.name;
    if(req.body.entity_id === '') {
      delete csvResults.entities[0].entity_id;
    } else {
      csvResults.entities[0].entity_id = req.body.entity_id;
    }
    commonFunctions.httpRequest(method, url, '', csvResults, headers, function (error, body) {
      res.send(body)
    });
  });

})

module.exports = {
  mlService: mlService,
  router: router
};
