<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="28" height="28" viewBox="0 0 28 28">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#55c7ff"/>
      <stop offset="1" stop-color="#7886f7"/>
    </linearGradient>
    <clipPath id="clip-Icons">
      <rect width="28" height="28"/>
    </clipPath>
  </defs>
  <g id="Icons" clip-path="url(#clip-Icons)">
    <g id="Group_1438" data-name="Group 1438" transform="translate(-3501 9845)">
      <g id="Group_1437" data-name="Group 1437" transform="translate(3138 -10227)">
        <rect id="Rectangle_285" data-name="Rectangle 285" width="28" height="28" rx="2" transform="translate(363 382)" fill="#c8dcff" opacity="0.28"/>
        <g id="outline-insert_chart-24px_1_" data-name="outline-insert_chart-24px (1)" transform="translate(363 382)">
          <path id="Path_1094" data-name="Path 1094" d="M0,0H28V28H0Z" fill="none"/>
          <path id="Path_1095" data-name="Path 1095" d="M22.556,3H5.444A2.452,2.452,0,0,0,3,5.444V22.556A2.452,2.452,0,0,0,5.444,25H22.556A2.452,2.452,0,0,0,25,22.556V5.444A2.452,2.452,0,0,0,22.556,3Zm0,19.556H5.444V5.444H22.556Zm-14.667-11h2.444v8.556H7.889Zm4.889-3.667h2.444V20.111H12.778Zm4.889,7.333h2.444v4.889H17.667Z" fill="url(#linear-gradient)"/>
        </g>
      </g>
    </g>
  </g>
</svg>
