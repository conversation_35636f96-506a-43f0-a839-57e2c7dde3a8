const config = require('config');
var kafkaLib = require('../../utils/kafka/kafka-lib');
const { getTenantInfoFromTenantId } = require('auth-middleware');
const { tenantSqlConnection  } = require('../../auth/sqlConnection');
const {OBJECT_CRAWL} = require('../../constants/constants');

const subscribeCsCrawlEvents = async () =>{
    try{
        const csCrawlUpdateConsumer = await kafkaLib.getConsumer({
            groupId: `admin ${config.get('kafkaTopic.csCrawlUpdates')}`,
            topic: config.get('kafkaTopic.csCrawlUpdates'),
            fromBeginning : false
        })   
    
    await csCrawlUpdateConsumer.run({
        eachMessage: async ({
            topic, parition, message, heartBeat
        }) => {
            try {
                let crawlMessage = JSON.parse(message.value);
                // fetch tenantInfo and check sql connection
                const tenantInfo = await getTenantInfoFromTenantId(crawlMessage.tenantId);
                await tenantSqlConnection(tenantInfo[0].tenant_id, tenantInfo[0].database_name);

                const contentSourceId = crawlMessage.mysqlId;
                if(contentSourceId){
                    if (crawlMessage.setObj.authVerificationData) {
                        if (crawlMessage.setObj.authVerificationData.authObj) {
                            const q = `UPDATE content_source_authorization SET is_authenticated = ? WHERE content_source_id = ?`;
                            connection[tenantInfo[0].tenant_id].execute.query(q, [crawlMessage.setObj.authVerificationData.authObj.is_authenticated, contentSourceId], (err, res) => {
                                if (err) {
                                    console.log('Error while updating cs authorization', err)
                                } else {
                                    console.log('ContentSource authorization updated for id =>',contentSourceId);
                                    console.log(`ContentSource authorization updated for id => ${JSON.stringify(res)}`);
                                }
                            });       
                        }

                        if (crawlMessage.setObj.authVerificationData.setObj) {
                            var q = `UPDATE content_sources SET is_paused = ? WHERE id = ?`;
                            connection[tenantInfo[0].tenant_id].execute.query(q, [crawlMessage.setObj.authVerificationData.setObj.is_paused, contentSourceId], (err, res) => {
                                if (err) {
                                    console.log('Error while updating paused status for content source', err)
                                } else {
                                    console.log('ContentSource updated for id =>',contentSourceId);
                                    console.log(`paused status contentSource for id => ${JSON.stringify(res)}`);
                                }
                            });    
                        }
                    } else if (crawlMessage.setObj.access_token) {
                    var q = `UPDATE content_source_authorization SET ? WHERE content_source_id = ?`;
                    const authObj = {
                        accessToken: crawlMessage.setObj.access_token
                    }
                    if (crawlMessage.setObj.refresh_token) {
                        authObj['refreshToken'] = crawlMessage.setObj.refresh_token;
                    }
                    connection[tenantInfo[0].tenant_id].execute.query(q, [authObj, contentSourceId], (err, res) => {
                        if (err) {
                            console.log('Error while updating Authentication details', err)
                        } else {
                            console.log(' Authorization updated for id =>',contentSourceId);
                            console.log(`Last updated contentSource Authorization for id => ${JSON.stringify(res)}`);
                        }
                    })
                  } else if (crawlMessage.setObj.spacesData) {
                    // update spaces
                    for (const space of crawlMessage.setObj.spacesData) {
                        var q = `UPDATE content_source_spaces
                        SET spaceName = ?, folderType = ?
                        WHERE id = ? and content_source_id = ?;`;  
                        connection[tenantInfo[0].tenant_id].execute.query(q, [space.name, space.folder_type, space.mysql_id, contentSourceId], (err, res) => {
                            if (err) {
                                console.log('Error while updating spaces', err)
                            } else {
                                console.log('ContentSource spaces updated for id =>',contentSourceId);
                                console.log(`spaces updated for id => ${JSON.stringify(res)}`);
                            }
                        })
                    }
                  } else {
                    var q = `UPDATE content_sources SET ? WHERE id = ?`;
                    const setObj = JSON.parse(JSON.stringify(crawlMessage.setObj));
                    if (setObj.objectName) {
                        delete setObj.adminLogFile;
                        delete setObj.logFile;
                    }
                    delete setObj.objectCrawlEndTime;
                    delete setObj.objectName;
                    connection[tenantInfo[0].tenant_id].execute.query(q, [setObj, contentSourceId], (err, res) => {
                        if (err) {
                            console.log('Error while updating Last updated for content source', err)
                        } else {
                            console.log('ContentSource updated for id =>',contentSourceId);
                            console.log(`Last updated contentSource for id => ${JSON.stringify(res)}`);
                        }
                    })
                    if(crawlMessage.csTypeId && OBJECT_CRAWL.includes(crawlMessage.csTypeId)){
                        var q = `UPDATE content_source_objects SET content_source_id=?`;
                        const objectParam = [contentSourceId];
                        let objectCrawlStatus = crawlMessage.setObj.crawl_status;
                        if (crawlMessage.setObj.objectName) {
                            q += `, object_pid=?`
                            objectParam.push(crawlMessage.setObj.pid);
                        }
                        if (crawlMessage.setObj.adminLogFile && crawlMessage.setObj.objectName) {
                            q += ', objectAdminLogFile=?, objectLogFile=?'
                            objectParam.push(crawlMessage.setObj.adminLogFile, crawlMessage.setObj.logFile);
                        }
                        if (crawlMessage.setObj.objectCrawlEndTime) {
                            q += ', current_crawl_end_time=?';
                            objectParam.push(crawlMessage.setObj.objectCrawlEndTime);
                        }
                        if (crawlMessage.setObj.current_crawl_start_time) {
                            q += ', current_crawl_start_time=?';
                            objectParam.push(crawlMessage.setObj.current_crawl_start_time);
                        }
                        if (objectCrawlStatus == 2) {
                            q += ', object_status=null';
                        }
                        q += ' WHERE content_source_id = ?';
                        objectParam.push(contentSourceId);
                        if (crawlMessage.setObj.objectName) {
                            q += ' AND name = ?'
                            objectParam.push(crawlMessage.setObj.objectName);
                        }

                        connection[tenantInfo[0].tenant_id].execute.query(q, objectParam, (err, res) => {
                            if (err) {
                                console.log('Error while updating object details', err)
                            } else {
                                console.log(' object details updated for id =>',contentSourceId);
                                console.log(`Last updated contentSource object details for id => ${JSON.stringify(res)}`);
                            }
                        })
                    }
                  }
               } 
            }catch (err) {
                console.log('Error while recieving Updated crawl data to admin service by Kafka => ', err);
            }
        }
    })
    }catch(error){
        if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
            kafkaLib.createTopic(config.get('kafkaTopic.csCrawlUpdates'));
        }
    }
};



module.exports = {
    subscribeCsCrawlEvents:subscribeCsCrawlEvents
}
