var async = require('async');
var fs = require('fs');
var express = require('express');
var router = express.Router();
var multipart = require('connect-multiparty');
var multipartMiddleware = multipart();
var commonFunctions = require('../../utils/commonFunctions');
var dirPath =__dirname;
var newPath=dirPath.substr(0,dirPath.indexOf('routes'));
router.post('/generateSitemap', multipartMiddleware, function (req, res, next) {
    fileUpload(req, function (err) {
        res.send({ url: config.get('adminURL')+'/resources/madcap/madcapSitemap1.xml', message: 'saved', code: 200 });
    });
});
function fileUpload(req, callback) {
    var Url = [];
    var cookiesValue=""
    var mainUrl = {
        "cookies": cookiesValue,
        "UrlSet": ""
    };
    fs.writeFile(__dirname + req.files.uploadFile.name, fs.readFileSync(req.files.uploadFile.path), 'binary', function (err, data1) {
        fs.readFile(__dirname + req.files.uploadFile.name, function (err, data) {
            var f1 = data.toString();
            var res = f1.split("\n");
            commonFunctions.errorlogger.info(res.length);
            var finalResult = res.slice(0, (res.length - 1))
            async.eachSeries(finalResult, (r, clbk) => {
                Url.push({ 'url': r })
                clbk();
            }, () => {
                mainUrl.UrlSet = Url;
                commonFunctions.errorlogger.info(mainUrl);

                if (!fs.existsSync(newPath+'resources/madcap/')){
                    fs.mkdir(newPath+'resources/madcap/',function(err){
                        if (err) {
                            return console.error(err);
                        }
                        else{
                            fs.writeFile(newPath+"resources/madcap/madcapSitemap1.xml", "", function (err) {
                                if (err){
                                    callback(err) 
                                }else{
                                    fs.writeFile(newPath+"resources/madcap/" + 'madcapChunks.json', JSON.stringify(mainUrl), function (err) {
                                        if (err) throw err;
                                        commonFunctions.errorlogger.warn('Saved!');
                                        callback(err)
                                    });
                                }
                            });
                           
                        }
                       
                     });
                }
                else{
                    fs.writeFile(newPath+"resources/madcap/madcapSitemap1.xml", "", function (err) {
                        if (err){
                            callback(err) 
                        }else{
                            fs.writeFile(newPath+"resources/madcap/" + 'madcapChunks.json', JSON.stringify(mainUrl), function (err) {
                                if (err) throw err;
                                console.log('Saved!');
                                callback(err)
                            });
                        }
                    });
                }
              

            })
        });

    })

};
module.exports =
    {
        router: router
    }