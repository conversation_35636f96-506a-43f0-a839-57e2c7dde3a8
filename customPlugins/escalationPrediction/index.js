const router = require('express').Router();

router.post("/getCSAndObjects", (req, res, next) => {
    let id = null;
    let object = null;
    if (req.body.cs == 'Salesforce') {
        id = '3';
        object = 'case';
    } else if (req.body.cs == 'ServiceNow') {
        id = '26';
        object = 'incident';
    } else {
        id = '7';
        object = 'tickets';
    }
    connection[req.headers['tenant-id']].execute.query("Select cs.id,cs.name,cs.label ,cs.sync_start_date as crawl_frequency , cso.label as cso_label, cso.name as cso_name, cs.last_sync_date as last_sync_date, cs.elasticIndexName as elastic_index_name  from `content_sources` cs inner join `content_source_objects` cso on cs.id = cso.content_source_id where cs.content_source_type_id = ? and cso.name = ? ", [id, object], (error, results) => {
        if (error) {
            console.error(error);
            res.send(error);
        }
        else {
            res.send(
                {
                    "status": "ok",
                    "data": results
                }
            );
        }
    });
});

router.post("/getCSFields", (req, res, next) => {
    connection[req.headers['tenant-id']].execute.query("select csof.label as csof_label, csof.name as csof_name from `content_sources` cs inner join `content_source_objects` cso on cs.id = cso.content_source_id inner join `content_source_object_fields` csof on cso.id = csof.content_source_object_id where cs.name = ? and cso.name = ? ", [req.body.cs.name, req.body.cs.cso_name], (error, results) => {
        if (error) {
            console.error(error);
            res.send(error);
        }
        else {
            res.send(
                {
                    "status": "ok",
                    "data": results
                }
            );
        }
    });
});

module.exports.index = router;