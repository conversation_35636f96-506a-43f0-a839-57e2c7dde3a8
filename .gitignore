# See http://help.github.com/ignore-files/ for more about ignoring files.

# custom plugins logs
customPlugins/scripts/keyWordBoostingScriptLogs/*.log
customPlugins/scripts/updateAnalyticsUserConfigLogs/*.log


# compiled output
/dist
/out-tsc
/tmp/*
!/tmp/README
# dependencies
/node_modules
/node_modules_bkp

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.vscode

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
/config/production.json
/config/development.json
# misc
/.sass-cache
/connect.lock
/coverage/*
/libpeerconnection.log
npm-debug.log
testem.log
/typings
.nyc_output

# e2e
/e2e/*.js
/e2e/*.map

# System Files
.DS_Store
Thumbs.db


localDev.json
m18local.json
/nbproject/private/
project.properties
project.xml
/crawlingLogs
resources/search_clients_custom/*
!resources/search_clients_custom/README.md
resources/Asset-Library/*
!resources/Asset-Library/README.md*
resources/Asset-Library/csvFiles/*
!resources/Asset-Library/csvFies/README.md*
resources/jiraonprem-Metadata/*
resources/on_premise/1_*
resources/reports*
reports/*
init-bootstrap/c21-dependency/tempBckup/*

build/
routes/sessionCapturing/encryption.txt
routes/sessionCapturing/sessionCapturing.out
routes/sessionCapturing/testSession.txt
routes/sessionCapturing/textEncryption.out
test.js
/logs
customPlugins/onPremises/download/onPremisesNodeService/config.js
dump.rdb
*.ignore*
customPlugins/semanticSearch/cron/dymTxtFile.txt

#Chatbot
chatbot/src/models
chatbot/config.json
*.pyc


#historical sync
csvDir
jsonDir

#Clustering
*.pyc
__pycache__
customPlugins/agentHelper/optics/agentHelper
*.pickle
*.ckpt
customPlugins/agentHelper/optics/Stage3/**/Graph/
customPlugins/agentHelper/optics/Stage3/**/Checkpoints/
customPlugins/agentHelper/**/*.csv
customPlugins/agentHelper/**/*.h5
temp

#sfdx
.sfdx

#slackbot
slackBotApi/.data
/Lib/jwt-keys/*
!/Lib/jwt-keys/readme
!/Lib/jwt-keys/development.pub

# synonyms
synonyms/*
!synonyms/README.md

# community helper
customPlugins/communityHelper/logs/*
!customPlugins/communityHelper/logs/README.md

#react search client
# dependencies
resources/search_clients_standard/react/node_modules
/.pnp
.pnp.js
# resources/search_clients_standard/react/package-lock.json
#resources/search_clients_standard/react/download
package-lock.json

# testing
/coverage

# production
# /download

# misc
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

cron-logs/*

routes/marketplace/localMarketplace/*
!routes/marketplace/localMarketplace/kcs
!routes/marketplace/localMarketplace/suva

config/development.json

