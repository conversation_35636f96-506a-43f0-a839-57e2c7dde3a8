module.exports = {

    paths: {
      '/v2_cs/contentSource/all': {
        get: {
          tags: [
            "ContentSource"
          ],
          summary: "Get all Content Source list",
          description: "Get all Content Sources",
          operationId: "contentSourceListAll",
          produces: [
            "application/json"
          ],
          responses: {
            200: {
              description: "successful operation",
              content:{
                'applicaton/json':{
                    schema: {
                        type: "object",
                        properties: {
                          contentSources: {
                            type: "array",
                            items: {
                              // $ref: "#/definitions/ContentSourceList"
                              type: "object",
                              properties: {
                                  id: {
                                      type: "integer",
                                      example: 1
                                  },
                                  label: {
                                      type: "string",
                                      example: "stackoverflow"
                                  },
                                  name: {
                                    type: "string",
                                    example: "micro_stackoverflow"
                                  },
                                  url: {
                                    type: "string",
                                    example: "https://stackoverflow.com"
                                  },
                              }
                            }
                          }
                        }
                      }
                }
              }
            },
            400: {
              description: "Invalid status value"
            }
          },
          security: [
            {
              oAuth2Password: []
            }
          ],
          'x-swagger-router-controller': "ContentSourceList"
        }
      },
      '/v2_cs/contentSource/{id}': {
        get: {
          tags: [
            "ContentSource"
          ],
          summary: "Get Content Source Info",
          description: "Get all Content Sources",
          operationId: "contentSourceInfo",
          produces: [
            "application/json"
          ],
          parameters: [
            {
              name: "id",
              in: "path",
              description: "id of the Content Source",
              required: true,
              type: "string",
              example: "1"
            }
          ],
          responses: {
            200: {
              description: "successful operation",
              content:{
                'applicaton/json':{
                    schema: {
                        type: "object",
                        properties: {
                        id: {
                            type: "string",
                            example: "20"
                        },
                        label: {
                            type: "string",
                            example: "Drive cs"
                        },
                        name: {
                            type: "string",
                            example: "drive_cs"
                        },
                        url: {
                            type: "string",
                            example: "http://drive.img1.com"
                        },
                        size: {
                            type: "integer",
                            example: 254
                        }
                        }
                    }
                }
              }
            },
            400: {
              description: "Invalid status value"
            }
          },
          security: [
            {
              oAuth2Password: []
            }
          ],
          'x-swagger-router-controller': "ContentSourceList"
        }
      },
      '/v2_cs/contentSource/{id}/objectAndFields': {
        get: {
          tags: [
            "ContentSource"
          ],
          summary: "Get Content Source Object Details",
          description: "Get Content Sources Object and field details",
          operationId: "contentSourceObjectDetails",
          produces: [
            "application/json"
          ],
          parameters: [
            {
              name: "id",
              in: "path",
              description: "Id of the Content Source",
              required: true,
              type: "string",
              example: "1"
            }
          ],
          responses: {
            200: {
              description: "successful operation",
              content:{
                'applicaton/json':{
                    schema: {
                        type: "object",
                        properties: {
                            items: {
                                type: "array",
                                items: {
                                // $ref: "#/definitions/ContentSourceObject"
                                    type: "object",
                                    properties: {
                                        id: {
                                            type: "integer",
                                            example: 1
                                        },
                                        name: {
                                            type: "string",
                                            example: "stack_question"
                                        },
                                        label: {
                                            type: "string",
                                            example: "Question"
                                        },
                                        fields: {
                                            type: "array",
                                            example: [
                                                {
                                                  "id": 1,
                                                  "name": "id",
                                                  "label": "id",
                                                  "type": "string",
                                                  "isFilterable": 1,
                                                  "isSearchable": 1
                                                },
                                                {
                                                  "id": 2,
                                                  "name": "tag",
                                                  "label": "Tag",
                                                  "type": "string",
                                                  "isFilterable": 1,
                                                  "isSearchable": 1
                                                },
                                                {
                                                  "id": 3,
                                                  "name": "title",
                                                  "label": "Title",
                                                  "type": "string",
                                                  "isFilterable": 1,
                                                  "isSearchable": 1
                                                },
                                                {
                                                  "id": 4,
                                                  "name": "post_time",
                                                  "label": "Created Date",
                                                  "type": "datetime",
                                                  "isFilterable": 1,
                                                  "isSearchable": 0
                                                },
                                                {
                                                  "id": 5,
                                                  "name": "body",
                                                  "label": "Description",
                                                  "type": "string",
                                                  "isFilterable": 1,
                                                  "isSearchable": 1
                                                },
                                                {
                                                  "id": 6,
                                                  "name": "is_answered",
                                                  "label": "Answered",
                                                  "type": "string",
                                                  "isFilterable": 1,
                                                  "isSearchable": 1
                                                },
                                                {
                                                  "id": 7,
                                                  "name": "comment_count",
                                                  "label": "Comment Count",
                                                  "type": "string",
                                                  "isFilterable": 1,
                                                  "isSearchable": 1
                                                },
                                                {
                                                  "id": 8,
                                                  "name": "view_href",
                                                  "label": "View Href",
                                                  "type": "string",
                                                  "isFilterable": 1,
                                                  "isSearchable": 1
                                                }
                                              ]
                                            
                                        },
                                    }
                                }
                            }
                        }
                    }
                }
              }
            },
            400: {
              description: "Invalid status value"
            }
          },
          security: [
            {
              oAuth2Password: []
            }
          ],
          'x-swagger-router-controller': "ContentSourceList"
        }
      },
      '/v2_cs/contentSource/{id}/getContentSourceFields/{cso_name}': {
        get: {
          tags: [
            "ContentSource"
          ],
          summary: "Get Content Sources fields Details",
          description: "Get Content Sources fields Details",
          operationId: "getContentSourceFields",
          produces: [
            "application/json"
          ],
          parameters: [
            {
              name: "id",
              in: "path",
              description: "Id of the Content Source",
              required: true,
              type: "string",
              example: "1"
            },
            {
              name: "cso_name",
              in: "path",
              description: "name from the Content Source object",
              required: true,
              type: "string",
              example: "stack_question"
            }
          ],
          responses: {
            200: {
              description: "successful operation",
              content:{
                'applicaton/json':{
                    schema: {
                        type: "object",
                        properties: {
                        items: {
                            type: "array",
                            example: [
                                {
                                "name": "id",
                                "label": "id"
                                },
                                {
                                "name": "tag",
                                "label": "Tag"
                                },
                                {
                                "name": "title",
                                "label": "Title"
                                },
                                {
                                "name": "post_time",
                                "label": "Created Date"
                                },
                                {
                                "name": "body",
                                "label": "Description"
                                },
                                {
                                "name": "is_answered",
                                "label": "Answered"
                                },
                                {
                                "name": "comment_count",
                                "label": "Comment Count"
                                },
                                {
                                "name": "view_href",
                                "label": "View Href"
                                }
                            ],
                        }
                        }
                    }
                }
              }
            },
            400: {
              description: "Invalid status value"
            }
          },
          security: [
            {
              oAuth2Password: []
            }
          ],
          'x-swagger-router-controller': "ContentSourceList"
        }
      },
      '/v2_cs/apiData/contentSource/{contentSourceId}/object/{objectId}/get': {
        get: {
          tags: [
            "APIData"
          ],
          summary: "Get Content Source Data",
          description: "Get Content Sources data",
          operationId: "contentSourceData",
          produces: [
            "application/json"
          ],
          parameters: [
            {
              name: "contentSourceId",
              in: "path",
              description: "Id of the Content Source",
              required: true,
              type: "string",
              example: "1"
            },
            {
              name: "objectId",
              in: "path",
              description: "Id of the Object",
              required: true,
              type: "string",
              example: "1"
            },
            {
              name: "from",
              in: "query",
              description: "from where we get content source data",
              required: false,
              type: "integer"
            },
            {
              name: "size",
              in: "query",
              description: "No of documents should be fetched from content source ",
              required: false,
              type: "integer"
            }
          ],
          responses: {
            200: {
              description: "successful operation",
              content:{
                'applicaton/json':{
                    schema: {
                        type: "object",
                        properties: {
                        items: {
                            type: "array",
                            items: {
                            // $ref: "#/definitions/ContentSourceData"
                            type: "object",
                            example: [
                                {
                                    "id": "59553482",
                                    "contentSource": "micro_stackoverflow",
                                    "object": "stack_question",
                                    "source": {
                                      "tag": "javascript",
                                      "id": 59553482,
                                      "title": "Issues getting my input value into an innerHTML - JavaScript",
                                      "body": "I cannot get the input value to go into my invitation on submit. After pressing submit the values are stored as I have checked the values in my array using document.getElementById(\"volunteersName\" + index).value;",
                                      "post_time": "2020-01-01T14:52:11.000Z",
                                      "is_answered": true,
                                      "comment_count": 4,
                                      "view_href": "https://stackoverflow.com/questions/59553482/issues-getting-my-input-value-into-an-innerhtml-javascript"
                                    }
                                  },
                                  {
                                    "id": "59552246",
                                    "contentSource": "micro_stackoverflow",
                                    "object": "stack_question",
                                    "source": {
                                      "tag": "javascript",
                                      "id": 59552246,
                                      "title": "API Key Mailgun not defined in Node.js project",
                                      "body": "I am having trouble running my node.js app. I have a node project running version 8.",
                                      "post_time": "2020-01-01T11:49:46.000Z",
                                      "is_answered": false,
                                      "comment_count": 3,
                                      "view_href": "https://stackoverflow.com/questions/59552246/api-key-mailgun-not-defined-in-node-js-project"
                                    }
                                  },
                            ]

                            }
                        }
                        }
                    }
                }
              }
            },
            400: {
              description: "Invalid status value"
            }
          },
          security: [
            {
              oAuth2Password: []
            }
          ],
          'x-swagger-router-controller': "ContentSourceList"
        }
      },
      '/v2_cs/apiData/contentSource/{contentSourceId}/object/{objectId}/document/{documentId}/get': {
        get: {
          tags: [
            "APIData"
          ],
          summary: "Get Document Data",
          description: "Get Content from Document with Id",
          operationId: "getDocumentData",
          produces: [
            "application/json"
          ],
          parameters: [
            {
              name: "contentSourceId",
              in: "path",
              description: "Id of the Content Source",
              required: true,
              type: "string",
              example: "1"
            },
            {
              name: "objectId",
              in: "path",
              description: "Id of the Object",
              required: true,
              type: "string",
              example: "1"
            },
            {
              name: "documentId",
              in: "path",
              description: " Id of the indexed Document",
              required: true,
              type: "string",
              example: "59553482"
            }
          ],
          responses: {
            200: {
              description: "successful operation",
              content:{
                'applicaton/json':{
                    schema: {
                        type: "object",
                        properties: {
                        id: {
                            type: "string",
                            example: "20"
                        },
                        contentSource: {
                            type: "string",
                            example: "drive"
                        },
                        object: {
                            type: "string",
                            example: "file"
                        },
                        source: {
                            type: "object",
                            example: {
                            createdDate: "20-08-2018",
                            url: "localhost:8025/drive.html"
                            }
                        },
                        found: {
                            type: "boolean",
                            example: true
                        }
                        }
                    }
                }
              }
            },
            400: {
              description: "Invalid status value"
            }
          },
          security: [
            {
              oAuth2Password: []
            }
          ],
          'x-swagger-router-controller': "ContentSourceList"
        }
      },
      '/v2_cs/apiData/contentSource/{contentSourceId}/object/{objectId}/document/{documentId}/update': {
        post: {
          tags: [
            "APIData"
          ],
          summary: "Update Content Source Document",
          description: "Update Content Source Document",
          operationId: "updateContentSourceDocument",
          produces: [
            "application/json"
          ],
          requestBody:{
            description: "if there are fields of array type, inside su__arrary_operations we need to define operation as append or update with regarging to those fields  Ex:- tag field",
            content: {
                'application/json': {
                    schema: {
                        type: "object",
                        example: {
                            title:"s",
                            tag: ["tesr"],
                            Body:"wqeqe",
                            su__arrary_operations: {
                                tag:"append"
                            }
                        },
                      }
                }
            }
          },
          parameters: [
            {
              name: "contentSourceId",
              in: "path",
              description: "Id of the Content Source",
              required: true,
              type: "string",
              example: "2"
            },
            {
              name: "objectId",
              in: "path",
              description: "Id of the Object",
              required: true,
              type: "string",
              example: "2"
            },
            {
              name: "documentId",
              in: "path",
              description: "Id of the Document",
              required: true,
              type: "string",
              example: "F_QevsJ5qcA"
            },
            {
              name: "postTimeField",
              in: "query",
              description: "Define field which can be used as post_time",
              required: false,
              type: "string"
            }
          ],
          responses: {
            200: {
              description: "successful operation",
              content:{
                'applicaton/json':{
                    schema: {
                        type: "object",
                        example: {
                            "statusMsg": "Ok"
                          }
                    }
                }
              }
            },
            400: {
              description: "Invalid status value"
            }
          },
          security: [
            {
              oAuth2Password: []
            }
          ],
          'x-swagger-router-controller': "ContentSourceList"
        }
      },
      '/v2_cs/apiData/contentSource/{contentSourceId}/object/{objectId}/document/{documentId}/delete': {
        get: {
          tags: [
            "APIData"
          ],
          summary: "Delete Content Source Document",
          description: "Delete Document form ant Content source",
          operationId: "deleteContentSourceDocument",
          produces: [
            "application/json"
          ],
          parameters: [
            {
              name: "contentSourceId",
              in: "path",
              description: "id of the Content Source",
              required: true,
              type: "string",
              example: "1"
            },
            {
              name: "objectId",
              in: "path",
              description: "Id of the Object",
              required: true,
              type: "string",
              example: "1"
            },
            {
              name: "documentId",
              in: "path",
              description: "Id of the indexed Document",
              required: true,
              type: "string",
              example: "7878"
            }
          ],
          responses: {
            200: {
              description: "successful Deletion"
            },
            400: {
              description: "Invalid status value"
            }
          },
          security: [
            {
              oAuth2Password: []
            }
          ],
          'x-swagger-router-controller': "ContentSourceList"
        }
      },
      '/v2_cs/apiData/contentSource/{contentSourceId}/object/{objectId}/bulkUpload': {
        post: {
          tags: [
            "APIData"
          ],
          summary: "Document Bulk Upload",
          description: "Bulk upload to the Content Source",
          operationId: "documentBulkUpload",
          produces: [
            "application/json"
          ],
          requestBody:{
            content: {
                'application/json': {
                    schema: {
                        type: "object",
                        properties: {
                          bulkData: {
                            type: "array",
                            items: {
                              // $ref: "#/definitions/ApiDataBulkItem"
                              type: "object",
                              properties: {
                                id: {
                                    type: "string",
                                    example: "12361211"
                                },
                                content: {
                                    type: "object",
                                    properties: {
                                        id : {
                                            type: "string",
                                            example: "12361211"
                                        },
                                        title: {
                                            type: "string",
                                            example: "New Templates for Search Clients - Part 1"
                                        }
        
                                    }
                                }
                              }
                            }
                          }
                        }
                    }
                }
            }
          },

          parameters: [
            {
              name: "contentSourceId",
              in: "path",
              description: "Id of the Content Source",
              required: true,
              type: "string",
              example: "1"
            },
            {
              name: "objectId",
              in: "path",
              description: "Id of the Object",
              required: true,
              type: "string",
              example: "1"
            },
            {
              name: "postTimeField",
              in: "query",
              description: "Define field which can be used as post_time",
              required: false,
              type: "string"
            },
          ],
          responses: {
            200: {
              description: "successful operation",
              content:{
                'applicaton/json':{
                    schema: {
                        type: "object",
                        example: {
                            "statusMsg": "Bulk Upload Done",
                            "createdDocIds": [],
                            "errorDocIds": [],
                            "updatedDocIds": [
                              "12361211"
                            ]
                          }
                    }
                }
              }
            },
            400: {
              description: "Invalid status value"
            }
          },
          security: [
            {
              oAuth2Password: []
            }
          ],
          'x-swagger-router-controller': "ContentSourceList"
        }
      },
      '/v2_cs/apiData/multiIndexBulkUpload': {
        post: {
          tags: [
            "APIData"
          ],
          summary: "Multi Index Bulk Upload",
          description: "Bulk upload to Multiple Content source simultaneously",
          operationId: "multiIndexBulkUpload",
          produces: [
            "application/json"
          ],
          requestBody:{
            content: {
                'application/json': {
                    schema: {
                        type: "object",
                        properties: {
                          bulkData: {
                            type: "array",
                            items: {
                              // $ref: "#/definitions/ApiDataBulkItem"
                              type: "object",
                              properties: {
                                id: {
                                    type: "string",
                                    example: "12361211"
                                },
                                contentSourceId: {
                                    type: "string",
                                    example: "1"
                                },
                                obejctId: {
                                    type: "string",
                                    example: "1"
                                },
                                postTimeField: {
                                    type: "string",
                                    example: "1612761805310"
                                },
                                content: {
                                    type: "object",
                                    properties: {
                                        body : {
                                            type: "string",
                                            example: "ssss"
                                        },
                                        title: {
                                            type: "string",
                                            example: "New Templates for Search Clients - Part 1"
                                        },
                                        video_time:{
                                            type: "string",
                                            example: "1612761805310"
                                        }
                                    }
                                }
                              }
                            }
                          }
                        }
                    }
                }
            }
          },
          responses: {
            200: {
              description: "successful operation",
              content:{
                'applicaton/json':{
                    schema: {
                        type: "object",
                        example: {
                            "statusMsg": "Multi Bulk Upload Done",
                            "createdDocIds": [],
                            "errorDocIds": [],
                            "updatedDocIds": [
                              {
                                "docId": "12361216",
                                "contentSourceId": 1,
                                "obejctId": 1
                              }
                            ]
                          }
                    }
                }
              }
            },
            400: {
              description: "Invalid status value"
            }
          },
          security: [
            {
              oAuth2Password: []
            }
          ],
          'x-swagger-router-controller': "ContentSourceList"
        }
      },
      '/v2_cs/apiData/contentSource/{cs_name}/object/{cso_name}/getBulkData': {
        post: {
          tags: [
            "APIData"
          ],
          summary: "To get Content Source Data",
          description: "To get Content Source Data",
          operationId: "getContentSourceBulkData",
          produces: [
            "application/json"
          ],
          requestBody:{
            description: "Params info required to fetch content source Data.",
            content: {
                'application/json': {
                    schema: {
                        type: "object",
                        required: [
                            "fields",
                            "startDate"
                        ],
                        properties: {
                            fields: {
                                type: "array",
                                items: {
                                  type: "string",
                                  example: "body"
                                }
                            },
                            startDate: {
                                type: "string",
                                example: "2022-03-03"
                            },
                            startDateField: {
                                type: "string",
                                default: "indexedDate"
                            },
                            offset: {
                                type: "integer",
                                default: 0,
                                minimum: 0
                            },
                            size: {
                                type: "integer",
                                default: 10,
                                minimum: 1,
                                maximum: 1000
                            },
                        }
                    }
                }
            }
          },


          parameters: [
            {
              name: "cs_name",
              in: "path",
              description: "Elastic Index Name of Content source",
              required: true,
              type: "string",
              example: "micro_stackoverflow"
            },
            {
              name: "cso_name",
              in: "path",
              description: "Content Source Object Name",
              required: true,
              type: "string",
              example: "stack_question"
            },
          ],
          responses: {
            200: {
              description: "successful operation",
              content:{
                'applicaton/json':{
                    schema: {
                        type: "object",
                        example: {
                            "took": 29,
                            "timed_out": false,
                            "_shards": {
                              "total": 5,
                              "successful": 5,
                              "skipped": 0,
                              "failed": 0
                            },
                            "hits": {
                              "total": 20630,
                              "max_score": 1,
                              "hits": [
                                {
                                  "_index": "micro_stackoverflow",
                                  "_type": "stack_question",
                                  "_id": "59552246",
                                  "_score": 1,
                                  "_source": {
                                    "body": "I am having trouble running my node.js app. I have a node project running version 8. I am having trouble with using mailgun. I have my API keys but they are not working in my project."
                                  }
                                },
                                {
                                  "_index": "micro_stackoverflow",
                                  "_type": "stack_question",
                                  "_id": "59555176",
                                  "_score": 1,
                                  "_source": {
                                    "body": "This is a snippet of my code on my site which displays json data onto the html page however, theres a lot of data on the json file."
                                  }
                                }
                              ]
                            }
                          }
                    }
                }
              }
            },
            400: {
              description: "Invalid status value"
            }
          },
          security: [
            {
              oAuth2Password: []
            }
          ],
          'x-swagger-router-controller': "ContentSourceList"
        }
      }
    }
  }
  
