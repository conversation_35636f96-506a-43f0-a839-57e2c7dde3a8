/*
    Developed by: <PERSON><PERSON>
    Description: 
    1. Read latest generated cookies from file
    2. Find out total number of documents to be crawled.
    3. 

*/
var path = require('path');
console.log('Current Dir => ',__dirname);
environment = require('../../../../routes/environment');
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
var request = require('request');
var async = require('async');
var fs = require('fs');
var elastic = require('elasticsearch');
var commonFunctions = require('../../../../utils/commonFunctions');

var config2 = fs.readFileSync('knowledgecenter/config.json');
config2 = JSON.parse(config2);

var elasticIndex = config2.elasticIndex;
var elasticType = config2.elasticType;
var pagecount = 2;
client = new elastic.Client({
    host: config.get('elasticIndexCS.host')+':'+config.get('elasticIndexCS.port')
});

var kba = [];
var k = 1;
var funcarray = [];

// 1. Read latest cookies and headers
var kbheaders = fs.readFileSync('knowledgecenter/kb_headers.json');
var latestCookiesData = fs.readFileSync('../../../../cookies/kbcookie.txt').toString();
kbheaders = JSON.parse(kbheaders);

// 2. Function for getting total number of documents to be crawled
function getTotalCount(cb){
    console.log('Getting count of docs: ')
    var headers = kbheaders.header;
    headers.Cookie = latestCookiesData.split('\n').join('; ');
    var dataString = '$\r\n--batch_3203-8745-20d3\r\nContent-Type: application/http\r\nContent-Transfer-Encoding: binary\r\n\r\nGET SAPNotes/$count?$filter=startswith(Component,%27LOD-SF%27)%20and%20FilterReleaseStatus%20eq%20%27NotRestricted%27%20and%20FilterSecurityPatchDay%20eq%20%27NotRestricted%27%20and%20FilterFuzzyThreshold%20eq%20%270.9%27 HTTP/1.1\r\nsap-contextid-accept: header\r\nAccept: text/plain, */*;q=0.5\r\nAccept-Language: en\r\nDataServiceVersion: 2.0\r\nMaxDataServiceVersion: 2.0\r\nsap-cancel-on-close: true\r\n\r\n\r\n--batch_3203-8745-20d3--\r\n';
    
    var options = {
        url: 'https://launchpad.support.sap.com/services/odata/svt/snogwsmynotes/$batch?sap-language=en',
        method: 'POST',
        headers: headers,
        body: dataString
    };
    
    function callback(error, response, body) {
        if (!error && response.statusCode == 202) {
            var arr = body.split('\r\n');
            try{
                // calculating total number of requests needed to fetch all documents ids
                var obj = parseInt(arr[arr.length-2].trim());
                pagecount = Math.ceil(obj/50);
                console.log('### count = '+pagecount);
            }catch(e){
                console.log(e);
            }
            cb(null, 1);
        }else{
            console.log(error, response?response.statusCode:'error');
        }
    }
    
    request(options, callback);
}

async.series([callcb => { return getTotalCount(callcb);}, callcb => {
    // 3. creating request functions for each page(every page contains batch of document meta properties).
    for(page = 0;page<pagecount;page++){
        (function(id){
            funcarray.push(function(cb){
                var headers = kbheaders.header;
                headers.Cookie = latestCookiesData.split('\n').join('; ');
                var ar2 = kbheaders.postBody.split('$skip=');
                ar2[1] = ar2[1].substring(ar2[1].indexOf('&')+1);
                var dataString = ar2.join('$skip='+(id*50)+'&');
            
                var options = {
                    url: 'https://launchpad.support.sap.com/services/odata/svt/snogwsmynotes/$batch?sap-language=en',
                    method: 'POST',
                    headers: headers,
                    gzip: true,
                    body: dataString
                };
                //console.log(dataString);
                function callback(error, response, body) {
                    //console.log(JSON.stringify(options));
                    if (!error && response.statusCode == 202) {
                        var arr = body.split('\r\n');
                        try{
                            var obj = JSON.parse(arr[arr.length-2]);
                            //console.log(obj.d.results.length);
                            obj.d.results.map(function(item){
                                kba.push(item);
                            });
                        }catch(e){
            
                        }
                        cb(null, {'success': true});
                    }else{
                        cb(null, {'success':true});
                    }
                }
            
                request(options, callback);
            });
        })(page);
    }

    async.parallel(funcarray,function(error, response){
        var headers = kbheaders.headerForArticleFetch;
        headers.Cookie = latestCookiesData.split('\n').join('; ');
        var funcArray = [];
        for(i=0;i<kba.length;i++){
            (function(id, idx){
                funcArray.push(function(cb){
                    console.log('requesting for '+id);
        
                    var reqbody = kbheaders.postBodyForArticleFetch;
                    var ar2 = reqbody.split('GET Doc(Number=');
                    ar2[1] = ar2[1].substring(ar2[1].indexOf(',')+1);
                    reqbody = ar2.join('GET Doc(Number='+id+',');
                    //console.log(reqbody);
                    var options = {
                        url: 'https://launchpad.support.sap.com/services/odata/svt/snogwsnnf/$batch?sap-language=en',
                        method: 'POST',
                        headers: headers,
                        gzip: true,
                        body: reqbody
                    };
                    
                    function callback(error, response, body) {
                        try{
                            if (error) throw new Error(error);
                            var arr = response.body.split('\n');
                            var obj = JSON.parse(arr[arr.length-2]);
                            if(!obj.error){
                                console.log(k+': response');
                                console.log(JSON.stringify(obj));
                                writeToElastic(obj, idx, cb);
                                //fs.writeFile('sapcrawl/file_'+k+'.json', JSON.stringify(obj));
                                k++;
                            }else{
                                cb(null, body);
                            }
                        }catch(e){
                            cb(null, body);
                        }
                        
                    }
                    
                    request(options, callback);
        
                });
            })(kba[i].Number, i);
               
        }
        
        /* async.parallelLimit(funcArray, 1, function(error, response){
            console.log('Completed!');
        }); */
        
        async.parallelLimit(funcArray, 50, function(error, response){
            console.log('Completed!');
            callcb(null, response);
        });
    });
}], function(error, response){
    console.log(response);
});

function writeToElastic(data, idx, cb){
    var source = {};
    source.title = data.d.Number +' - '+kba[idx].Title;
    source.url = 'https://launchpad.support.sap.com'+kba[idx].URL;
    source.id = 'https://launchpad.support.sap.com'+kba[idx].URL;
    source.view_href = 'https://launchpad.support.sap.com'+kba[idx].URL;
    try{
        source.tstamp = new Date(parseInt(kba[idx].ReleasedOn.replace('/Date(','').replace(')/','')));
        source.post_time = source.tstamp;
    }catch(e){

    }
    if(!source.post_time){
        source.post_time = new Date();
        source.tstamp = new Date();
    }
    source.content = '';

    //console.log('long text: '+data.d.Longtext);
    if(data.d && data.d.Longtext){
        
        data.d.Longtext.results.map(function(lti){
            source.content+= ' '+commonFunctions.stripHtml((lti.Title?lti.Title:''));
            source.content+= ' '+commonFunctions.stripHtml((lti.Text?lti.Text:''));
        });
    }
    source.content = source.content.trim();
    source.boardId = "1";
    source.privacy = 'open';
    //console.log(JSON.stringify(source));
    console.log('Indexname: ',elasticIndex);
    client.index({
        index: elasticIndex,
        type: elasticType,
        id: source.id,
        body: source
    }, function (error, response) {
	console.log('error: ',error,response);
        cb(null, 1);        
    });
}