const maxKeywordLength = config.get('searchTunning.maxKeywordLength');

const requestValidator = (keyword) => {

  // keyword validations
  if (keyword.data && keyword.data.length > 0) {
    const data = keyword.data;

    // to check whether keyword is empty
    if (data.some((item) => item.keywordValues.some((value) => value.searchString.length === 0))) {
      return 0;
    }

    // to check whether keyword length exceeds the maximum allowed limit
    if (data.some((item) => item.keywordValues.some((value) => value.searchString.length > maxKeywordLength))) {
      return 2;
    }
  }

  return 1;
}

module.exports = requestValidator;