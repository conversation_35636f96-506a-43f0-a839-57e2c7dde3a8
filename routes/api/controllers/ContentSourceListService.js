'use strict';

var commonFunctions = require("../../../utils/commonFunctions");
var elastic = require('elasticsearch');
const { getTenantInfoFromTenantId } = require('auth-middleware');
var client;
client = new elastic.Client({
    host: config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port')
});
var request = require('request');
var regex = new RegExp("^[_a-zA-Z0-9\s]+$");
var regex1 = new RegExp("^[_0-9\s]+$");
var async = require('async');

const HTTP_CODES = {
    mysqlError: 500,
    elasticError: 500,
    elasticDown: 500,
    validationError: 400,
    dataLimitError: 413,
    idNotFound: 404,
    indexNotFound: 404,
    docNotFound: 404,
    partialSuccess : 206,
    fieldMappingIssue: 400,
    internalServerError: 500
};

const HTTP_RESPONSE = {
    mysqlError: { error: {message: "Internal Server Error"} },
    elasticError: { error: {message: "Internal Server Error"} },
    elasticDown: { error: {message: "Internal Server Error"} },
    validationError: { error: {message: "Bad Request"} },
    dataLimitError: { error: {message: "Maximum limit for bulk upload is 50 documents"} },
    csIdNotFound: { error: {message: "Content Source Id is not valid"} },
    objectIdNotFound: { error: {message: "Object Id is not valid"} },
    indexNotFound: { error: {message: "Metadata not found"} },
    docNotFound: { error: {message: "document id is not valid"} },
    partialSuccess: { error: { message: "some field updates failed" } },
    fieldMappingIssue: { error: { message: "invalid fields" }},
    objectNotFound: { error: {message: "Object not found"}},
    internalServerError: { error: {message: "Internal Server Error"} },
};

function checkIfMigrationInProgress (args,req, cb) {
    try{
            let headers = {
                "Content-Type": "application/json",
                "su-crawler-secret" : config.get("crawler.sharedSecret"),
                "tenant-id": req.headers['tenant-id']
            }
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content/check-migration-status`, '', {...req, args}, headers, function (error, result) {
                if (error) {
                    console.log('checkIfMigrationInProgress error', error);
                    cb(error, null);
                }
                if(result) {
                    console.log('checkIfMigrationInProgress response', result);
                   cb(null,result);
                }
            });
           
        }catch(e){
            console.log('in checkIfMigrationInProgress catch',e)
        return e;
        }
}

exports.getAllcs = function (args, res,req, next) {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + '/content-api/contentSource/all', '', {}, headers, function (error, result) {
        if (error) {
            res.send({error: true, msg: error});
        }
        if(result) {
           res.send(result);
        }else {
            res.send({error: true, msg: result.data});
        }
    });
};

exports.getcsInfo = function (args, res, req, next) {
    if (regex1.test(args.id.value)) {
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id']
    }
    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/content-api/contentSource/${args.id.value}`, '', {}, headers, function (error, result) {
        if (error) {
            res.send({error: true, msg: error});
        }
        if(result) {
           res.send(result);
        }else {
            res.send({error: true, msg: result.data});
        }
    });
}
    else {
        res.status(HTTP_CODES.validationError).send(HTTP_RESPONSE.validationError);
    }
}

exports.getcsObject = function (args,req, res, next) {
    if (regex1.test(args.id.value)) {
        let headers = {
            "Content-Type": "application/json",
            "su-crawler-secret" : config.get("crawler.sharedSecret"),
            "tenant-id": req.headers['tenant-id']
        }
        commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/content-api/contentSource/${args.id.value}/objectAndFields`, '', {}, headers, function (error, result) {
            if (error) {
                res.send({error: true, msg: error});
            }
            if(result) {
               res.send(result);
            }else {
                res.send({error: true, msg: result.data});
            }
        });
    }
    else {
        res.status(HTTP_CODES.validationError).send(HTTP_CODES.validationError);
    }
}
exports.getContentSourceFields = async (args,req, res) => {

    try {
        if (!args.id.value || !args.cso_name.value) {
            throw "ContentSourceId Or cso_name is missing"
        }
        if (regex1.test(args.id.value)) {
            let headers = {
                "Content-Type": "application/json",
                "su-crawler-secret" : config.get("crawler.sharedSecret"),
                "tenant-id": req.headers['tenant-id']
            }
            commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/content-api/contentSource/${args.id.value}/getContentSourceFields/${args.cso_name.value}`, '', {}, headers, function (error, result) {
                if (error) {
                    res.send({error: true, msg: error});
                }
                if(result) {
                   res.send(result);
                }else {
                    res.send({error: true, msg: result.data});
                }
            });
        }

    } catch (err) {
        res.status(400).send({ "error": err })
    }

}

exports.getContentSourceBulkData = async (args,req, res, next) => {
    try {
        if (!args) {
            throw "inputs are missing"
        }
        else {
            let headers = {
                "Content-Type": "application/json",
                "su-crawler-secret" : config.get("crawler.sharedSecret"),
                "tenant-id": req.headers['tenant-id']
            }
            commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-api/contentSource/${args.cs_name.value}/object/${args.cso_name.value}/getBulkData`, '', {body:req.body, args}, headers, function (error, result) {
                if (error) {
                    res.send({error: true, msg: error});
                }
                if(result) {
                   res.send(result);
                }else {
                    res.send({error: true, msg: result.data});
                }
            });
        }

    } catch (err) {
        res.status(400).send({ "error": err })
    }
}

exports.getScrollData = async (args, req, res) => {
    if (!args.scrollId.value && args.scroll.value) {
        throw "ScrollId is missing."
    }

    let queryObject = {
        scrollId: args.scrollId.value,
        scroll: args.scroll.value
    }

    const tenantInfo = await getTenantInfoFromTenantId(req.headers['tenant-id']);
    let esNode = commonFunctions.getRandomClusterNode(tenantInfo[0].es_cluster_ip);
    var options = {
        method: 'GET',
        url:  esNode + "/_search/_scroll",
        body: JSON.stringify(queryObject)
    };

    request(options,async function (error, response, data) {
        if (error || response.statusCode != 200) {
            if (response.statusCode == 404)
                res.status(HTTP_CODES.docNotFound).send(HTTP_RESPONSE.docNotFound);
            else
                res.status(HTTP_CODES.elasticDown).send(HTTP_RESPONSE.elasticDown);
        }
        else {
            data = JSON.parse(data)
            data.hits.hits.map(d => {
                Object.keys(d._source).map(k => {
                    if (typeof d._source[k] == 'string') {
                        d._source[k] = d._source[k].split(new RegExp('[\r\n\t ]+')).join(' ');
                    }
                })
            })
            res.send(data)
        }
    })
}
function getObject(Id,req, callback) {
    commonFunctions.getContentSourceObjectsAndFieldsById(Id,req, (error, data) => {
        if (error) {
            callback({code: HTTP_CODES.mysqlError, response: HTTP_RESPONSE.mysqlError});
        }
        else {
            var result = [];
            if(!data.length){
                return callback({code: HTTP_CODES.idNotFound, response: HTTP_RESPONSE.objectNotFound})
            }
            if (data.length > 0) {
                for (var i = 0; i < data.length; i++) {
                    var obj = {
                        "object": {
                            "id": data[i].id,
                            "name": data[i].name,
                            "label": data[i].label,
                            "fields": []
                        }
                    }
                    for (var j = 0; j < data[i].fields.length; j++) {
                        var fObj = {
                            "id": data[i].fields[j].id,
                            "name": data[i].fields[j].name,
                            "label": data[i].fields[j].label,
                            "type": data[i].fields[j].type,
                            "isFilterable": data[i].fields[j].isFilterable,
                            "isSearchable": data[i].fields[j].isSearchable
                        };
                        obj.object.fields.push(fObj);
                    }
                    result.push(obj);
                }
            }
            callback(null, { "items": result })
        }
    })
}

exports.getcsData = function (args, req, res, next) {
    if ((regex1.test(args.contentSourceId.value) && regex.test(args.objectId.value)) && (args.size.value ? regex1.test(args.size.value) : true) && (args.from.value ? regex1.test(args.from.value) : true)) {
        let headers = {
            "Content-Type": "application/json",
            "su-crawler-secret" : config.get("crawler.sharedSecret"),
            "tenant-id": req.headers['tenant-id']
        }
        commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/content-api/contentSource/${args.contentSourceId.value}/object/${args.objectId.value}/get/${args.from.value}/${args.size.value}`, '', {}, headers, function (error, result) {
            if (error) {
                res.send({error: true, msg: error});
            }
            if(result) {
               res.send(result);
            }else {
                res.send({error: true, msg: result.data});
            }
        });
    }
    else {
        res.status(HTTP_CODES.validationError).send(HTTP_RESPONSE.validationError);
    }
}

exports.getElasticIndex = function (args, res,req, next) {
    if (regex1.test(args.contentSourceId.value) && regex1.test(args.objectId.value) && args.documentId.value) {
        let headers = {
            "Content-Type": "application/json",
            "su-crawler-secret" : config.get("crawler.sharedSecret"),
            "tenant-id": req.headers['tenant-id']
        }
        commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/content-api/contentSource/${args.contentSourceId.value}/object/${args.objectId.value}/document/${args.documentId.value}/get`, '', {}, headers, function (error, result) {
            if (error) {
                res.send({error: true, msg: error});
            }
            if(result) {
               res.send(result);
            }else {
                res.send({error: true, msg: result.data});
            }
        });
    }
    else {
        res.status(HTTP_CODES.validationError).send(HTTP_RESPONSE.validationError);
    }
}

exports.updateElasticIndex = function (args, res,req, next) {
    checkIfMigrationInProgress(args, req, function (err1, res1) {
        if (res1) {
            console.log('checkIfMigrationInProgress response', res1)
            if (res1.status == "InProgress") {
                return res.status(200).send({ "statusMsg": "Ok" });
            }else{
                if (!regex1.test(args.contentSourceId.value) || !regex1.test(args.objectId.value)) {
                    res.status(HTTP_CODES.validationError).send(HTTP_RESPONSE.validationError);
                }
                else {
                    let headers = {
                        "Content-Type": "application/json",
                        "su-crawler-secret" : config.get("crawler.sharedSecret"),
                        "tenant-id": req.headers['tenant-id']
                    }
                    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/content-api/contentSource/${args.contentSourceId.value}/object/${args.objectId.value}/document/${args.documentId.value}/update`, '', {body:req.body, args}, headers, function (error, result) {
                        if (error) {
                            res.send({error: true, msg: error});
                        }
                        if(result) {
                           res.send(result);
                        }else {
                            res.send({error: true, msg: result.data});
                        }
                    });
                }
            }
        }else{
            return res.status(HTTP_CODES.internalServerError).send(HTTP_RESPONSE.internalServerError);
        }
    })
}

exports.deleteElasticDocument = function (args, res,req, next) {
    checkIfMigrationInProgress(args, req, function (err1, res1) {
        if (res1) {
            console.log('checkIfMigrationInProgress response', res1)
            if (res1.status == "InProgress") {
                return res.status(200).send({ "statusMsg": "Ok" });
            }else{
                if (regex1.test(args.contentSourceId.value) && regex1.test(args.objectId.value) && args.documentId.value) {
                    let headers = {
                        "Content-Type": "application/json",
                        "su-crawler-secret" : config.get("crawler.sharedSecret"),
                        "tenant-id": req.headers['tenant-id']
                    }
                    commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/content-api/contentSource/${args.contentSourceId.value}/object/${args.objectId.value}/document/${args.documentId.value}/delete`, '', {}, headers, function (error, result) {
                        if (error) {
                            res.send({error: true, msg: error});
                        }
                        if(result) {
                        res.send(result);
                        }else {
                            res.send({error: true, msg: result.data});
                        }
                    });
                }
                else {
                    res.status(HTTP_CODES.validationError).send(HTTP_RESPONSE.validationError);
                }
            }
        }else{
            return res.status(HTTP_CODES.internalServerError).send(HTTP_RESPONSE.internalServerError);
        }
    })
}

exports.bulkUploadElasticIndex = function (args, res ,req, next) {
    checkIfMigrationInProgress(args, req, function (err1, res1) {
        if (res1) {
            console.log('checkIfMigrationInProgress response', res1)
            if (res1.status == "InProgress") {
                return res.status(200).send({ "statusMsg": "Ok" });
            }else{
                if (!regex1.test(args.contentSourceId.value) || !regex1.test(args.objectId.value)) {
                    res.status(HTTP_CODES.validationError).send(HTTP_RESPONSE.validationError);
                }
                else if (!args.body.value.bulkData) {
                    res.status(HTTP_CODES.validationError).send(HTTP_RESPONSE.validationError);
                }
                else if (args.body.value.bulkData.length > 50) {
                    res.status(HTTP_CODES.dataLimitError).send(HTTP_RESPONSE.dataLimitError);
                }
                else {
                    let headers = {
                        "Content-Type": "application/json",
                        "su-crawler-secret" : config.get("crawler.sharedSecret"),
                        "tenant-id": req.headers['tenant-id']
                    }
                    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-api/contentSource/${args.contentSourceId.value}/object/${args.objectId.value}/bulkUpload`, '', {body:req.body, args}, headers, function (error, result) {
                        if (error) {
                            res.send({error: true, msg: error});
                        }
                        if(result) {
                        res.send(result);
                        }else {
                            res.send({error: true, msg: result.data});
                        }
                    });
                
                }
            }
        }else{
            return res.status(HTTP_CODES.internalServerError).send(HTTP_RESPONSE.internalServerError);
        }
    })
}

exports.bulkUploadForMultipe = function (args, res,req, next) {
    checkIfMigrationInProgress(args, req, function (err1, res1) {
        if (res1) {
            if (res1.status == "InProgress") {
                return res.status(200).send({ "statusMsg": "Multi Bulk Upload Done" });
            }else {
                var elasticIdsMap;
                if (!(args && args.body && args.body.value && args.body.value.bulkData)) {
                    res.status(HTTP_CODES.validationError).send(HTTP_RESPONSE.validationError);
                }
                else if (args.body.value.bulkData.length > 50) {
                    res.status(HTTP_CODES.dataLimitError).send(HTTP_RESPONSE.dataLimitError);
                }
                else {
                    let headers = {
                        "Content-Type": "application/json",
                        "su-crawler-secret" : config.get("crawler.sharedSecret"),
                        "tenant-id": req.headers['tenant-id']
                    }
                    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/content-api/multiIndexBulkUpload`, '', {body:req.body, args}, headers, function (error, result) {
                        if (error) {
                            res.send({error: true, msg: error});
                        }
                        if(result) {
                        res.send(result);
                        }else {
                            res.send({error: true, msg: result.data});
                        }
                    });
                }
            }
        }else{
            return res.status(HTTP_CODES.internalServerError).send(HTTP_RESPONSE.internalServerError);
        }
    })
};


const getContentObjectJson = (req,cb) => {
    connection[req.headers['tenant-id']].execute.query(
        `select
        cs.id as cs_id,
        cs.elasticIndexName,
        cso.id as cso_id,
        cso.name as cso_name,
        csof.name as field_name,
        csof.type as field_type
    from
        content_sources cs
    join content_source_objects cso on
        cs.id = cso.content_source_id
    join content_source_object_fields csof on
        csof.content_source_object_id = cso.id ;`,
        (err, rows) => {
            if (err) {
                cb({code : HTTP_CODES.mysqlError, response: HTTP_RESPONSE.mysqlError}, null);
            } else if(!rows || !rows.length){
                cb({code: HTTP_CODES.idNotFound, response: HTTP_RESPONSE.csIdNotFound})
            }
            else {
                let jsonMap = {};
                let elasticVsIdMap = {};
                for (let i = 0; i < rows.length; i++) {
                    if (!jsonMap[rows[i].cs_id]) {
                        jsonMap[rows[i].cs_id] = {};
                        jsonMap[rows[i].cs_id]["objects"] = {};
                    }
                    jsonMap[rows[i].cs_id]["elasticIndexName"] = rows[i].elasticIndexName;
                    if (!jsonMap[rows[i].cs_id]["objects"][rows[i].cso_id]) {
                        jsonMap[rows[i].cs_id]["objects"][rows[i].cso_id] = {};
                        jsonMap[rows[i].cs_id]["objects"][rows[i].cso_id]["fields"] = [];
                    }
                    jsonMap[rows[i].cs_id]["objects"][rows[i].cso_id]["name"] = rows[i].cso_name;
                    jsonMap[rows[i].cs_id]["objects"][rows[i].cso_id]["fields"].push({
                        name: rows[i].field_name,
                        type: rows[i].field_type
                    })
                    // Code for elasticVsIdMap
                    if (!elasticVsIdMap[rows[i].elasticIndexName]) {
                        elasticVsIdMap[rows[i].elasticIndexName] = {};
                        elasticVsIdMap[rows[i].elasticIndexName]["__id__"] = rows[i].cs_id;
                        elasticVsIdMap[rows[i].elasticIndexName]["objects"] = {};
                    }
                    elasticVsIdMap[rows[i].elasticIndexName]["objects"][rows[i].cso_name] = rows[i].cso_id;
                }
                cb(null, { jsonMap, elasticVsIdMap });
            }
        });
}

function getContentFieldsByObjectId(ObjectId,req, callback) {
    connection[req.headers['tenant-id']].execute.query("SELECT * FROM content_source_object_fields WHERE content_source_object_id = ? ", [ObjectId], (err, rows) => {
        if (err) {
            commonFunctions.errorlogger.error("error while fetching fields ", err);
            callback(null, []);
        }
        else {
            var res = rows.map(x => { return x.name });
            callback(null, res);
        }
    })
}

async function checkElasticIndex(index,req, callback) {
    var request = require("request");
    const tenantInfo = await getTenantInfoFromTenantId(req.headers['tenant-id']);
    let esNode = commonFunctions.getRandomClusterNode(tenantInfo[0].es_cluster_ip);
    let options = {
        "method": "HEAD",
        "url": esNode + "/" + index,
        "headers": {
            'cache-control': 'no-cache'
        }
    };
    request(options, function (error, response, body) {
        if (error) {
            callback(error, null);
        }
        else {
            callback(null, response.statusCode);
        }
    });
}

function checkFields(bodyValue, dataObjectFields, postTimeField) {
    var objFields = [];
    dataObjectFields.forEach((x) => {
        objFields.push(x.name);
    });
    var jsonFields = Object.keys(bodyValue);
    for (var i = 0; i < jsonFields.length; i++) {
        if (jsonFields[i] === 'su__arrary_operations')
            continue;
        if (typeof bodyValue[jsonFields[i]] == 'object' && Array.isArray(bodyValue[jsonFields[i]]) == false) {
            let objData = bodyValue[jsonFields[i]];
            for (var property in objData) {
                if (objFields.includes(jsonFields[i] + '__' + property)) {
                    bodyValue[jsonFields[i] + "__" + property] = bodyValue[jsonFields[i]][property];
                }
            }
        }
        else if (typeof bodyValue[jsonFields[i]] == 'object' && Array.isArray(bodyValue[jsonFields[i]]) == true) {
            for (var l = 0; l < bodyValue[jsonFields[i]].length; l++) {
                var arrayObj = bodyValue[jsonFields[i]][l];
                for (var property in arrayObj) {
                    if (objFields.includes(jsonFields[i] + '__' + property)) {
                        if (!bodyValue[jsonFields[i] + "__" + property]) {
                            bodyValue[jsonFields[i] + "__" + property] = [];
                        }
                        (bodyValue[jsonFields[i] + '__' + property]).push(bodyValue[jsonFields[i]][l][property]);
                    }
                }
            }
        }
        if (!objFields.includes(jsonFields[i])) {
            /** We can also Throw error in this case */
            delete bodyValue[jsonFields[i]];
        }
        else {
            let temp1 = dataObjectFields.filter((x) => x.name == jsonFields[i]);
            if (temp1[0].type == "datetime") {
                let date = new Date(bodyValue[jsonFields[i]]);
                if (date == "Invalid Date") {
                    /** Deleing because it is notvalid date for datetime Object */
                    // delete bodyValue[jsonFields[i]];
                    return { bodyValue: bodyValue, isValid: jsonFields[i], errorMessage: `Invalid Date while Parsing` };
                } else {
                    if (jsonFields[i] !== 'post_time') {
                        bodyValue[jsonFields[i]] = date;
                    }
                    /** post_time field is only updated according 
                     * to the value of postTimeField 
                     */
                    if (postTimeField === jsonFields[i]) {
                        bodyValue["post_time"] = date;
                    }
                }
            }
            else if (temp1[0].type == "number") {
                if (typeof bodyValue[jsonFields[i]] !== "number")
                    return { bodyValue: bodyValue, isValid: jsonFields[i], errorMessage: `Data type Mismatch with Required type ${temp1[0].type}` };
            }
            else if (
                Array.isArray(bodyValue[jsonFields[i]]) == false &&
                typeof bodyValue[jsonFields[i]] != "object" &&
                bodyValue[jsonFields[i]] != null &&
                typeof bodyValue[jsonFields[i]] != temp1[0].type
            ) {
                return { bodyValue: bodyValue, isValid: jsonFields[i] };
            }
        }
    }
    return { bodyValue: bodyValue, isValid: null };
}

function checkBulkFields(dataObjectFields, bulkArray, postTimeField, indexName, objectName) {
    let dataObjectFields1 = [];
    dataObjectFields.forEach(x => {
        dataObjectFields1.push(x.name);
    });
    for (let i = 0; i < bulkArray.length; i++) {
        if (bulkArray[i].content && bulkArray[i].id) {
            let objKeys = Object.keys(bulkArray[i].content);
            for (let j = 0; j < objKeys.length; j++) {
                if (typeof bulkArray[i].content[objKeys[j]] == 'object' && Array.isArray(bulkArray[i].content[objKeys[j]]) == false) {
                    let objData = bulkArray[i].content[objKeys[j]];
                    for (var property in objData) {
                        if (dataObjectFields1.includes(objKeys[j] + '__' + property)) {
                            bulkArray[i].content[objKeys[j] + '__' + property] = bulkArray[i].content[objKeys[j]][property];
                        }
                    }
                }
                else if (typeof bulkArray[i].content[objKeys[j]] == 'object' && Array.isArray(bulkArray[i].content[objKeys[j]]) == true) {
                    for (let l = 0; l < bulkArray[i].content[objKeys[j]].length; l++) {
                        let arrayObj = bulkArray[i].content[objKeys[j]][l];
                        for (let property in arrayObj) {
                            if (dataObjectFields1.includes(objKeys[j] + '__' + property)) {
                                if (!bulkArray[i].content[objKeys[j] + '__' + property]) {
                                    bulkArray[i].content[objKeys[j] + '__' + property] = [];
                                }
                                (bulkArray[i].content[objKeys[j] + '__' + property]).push(bulkArray[i].content[objKeys[j]][l][property]);
                            }
                        }
                    }
                }
                if (!dataObjectFields1.includes(objKeys[j])) {
                    /** TODO also we can return it from here, as it contain invalid field */
                    delete bulkArray[i].content[objKeys[j]];
                } else {
                    let temp1 = dataObjectFields.filter((x) => x.name == objKeys[j]);
                    if (temp1[0].type == "datetime") {
                        let date = new Date(bulkArray[i].content[objKeys[j]]);
                        if (date == "Invalid Date") {
                            /** Deleing because it is notvalid date for datetime Object */
                            // delete bulkArray[i].content[objKeys[j]];
                            return { bulkArray: bulkArray, isValid: objKeys[j], errorMessage: `Invalid Date while Parsing`, docId: bulkArray[i].id };
                        } else {
                            if (objKeys[j] !== 'post_time') {
                                bulkArray[i].content[objKeys[j]] = date;
                            }
                            /** post_time field is only updated according 
                             * to the value of postTimeField 
                             */
                            if (postTimeField === objKeys[j]) {
                                bulkArray[i].content["post_time"] = date
                            }
                        }
                    }
                    else if (temp1[0].type == "number") {
                        if (temp1[0].type != typeof bulkArray[i].content[objKeys[j]]) {
                            return { bulkArray: bulkArray, isValid: objKeys[j], errorMessage: `Data type Mismatch with Required type ${temp1[0].type}`, docId: bulkArray[i].id };
                        }
                    }
                    else if (
                        Array.isArray(bulkArray[i].content[objKeys[j]]) == false &&
                        typeof bulkArray[i].content[objKeys[j]] != "object" &&
                        bulkArray[i].content[objKeys[j]] != null &&
                        typeof bulkArray[i].content[objKeys[j]] != temp1[0].type
                    ) {
                        return { bulkArray: bulkArray, isValid: objKeys[j], errorMessage: `Data type Mismatch with Required type ${temp1[0].type}`, docId: bulkArray[i].id };
                    }
                }
                /** At last checking if post_time has not updated yet by any field
                 * then add index time as post_time
                 */
                if (!bulkArray[i].content["post_time"] || bulkArray[i].content["post_time"] === '') {
                    bulkArray[i].content["post_time"] = new Date();
                }
                bulkArray[i].content["indexedDate"] = new Date();
                bulkArray[i].content["language"] = bulkArray[i].content["language"] || "en";
                bulkArray[i].content["objectName"] = objectName;
                bulkArray[i].content["indexName"] = indexName;
                bulkArray[i].content["uniqueField"] = indexName + '_' + objectName + '_' + bulkArray[i].id;
            }
        }
        else {
            /** TODO if Content Id missing for some then contine with other */
            return { bulkArray: bulkArray, isValid: "Content or Id", errorMessage: `Missing Mandatory field`, docId: bulkArray[i].id };
        }
    }
    return { "bulkArray": bulkArray, "isValid": null };
}

function isValidJson(json) {
    try {
        JSON.parse(json);
        return true;
    } catch (e) {
        return false;
    }
}
