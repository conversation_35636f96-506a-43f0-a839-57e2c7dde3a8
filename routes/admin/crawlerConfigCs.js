var express = require('express');
var router = express.Router();
var commonFunctions = require('./../../utils/commonFunctions');
const config = require('config');
var {CS_AUTH_COMPONENT_FIELDS} = require('../../constants/constants');

router.get('/getCsAuthComponent', function (req, res, next) {
    const sqlGet = `SELECT * FROM content_source_component`; 
    connection[req.headers['tenant-id']].execute.query(sqlGet, [], (err, results) => {
      if (err) {
        res.status(500).send({ "error": "Error fetching content source components" });
        return;
      }
  
      else {
        res.send({"data": results});
      }
    });
  });

  router.post('/updateCsAuth',  function (req, res, next) {
    if (req.body.appId){
     delete req.body.appId;
    }
    const unexpectedFields = Object.keys(req.body).filter(field => !CS_AUTH_COMPONENT_FIELDS.includes(field));
    if (unexpectedFields.length > 0) {
         return res.status(500).send({ "error": "Error updating cs :unexpected fields" });
    }
    const {content_source_type_id, hasPlaces, hasClientCreds, hasTenant, hasClientUrl,clientUrl, spaceLabel, allowedAuths, defaultAuth,additionalFields, dropDownLabel, dropDownOptions, dropDownKey} = req.body;
    const sqlUpdate = `UPDATE content_source_component SET  content_source_type_id = ?,hasPlaces = ?,hasClientCreds = ?,hasTenant = ?,hasClientUrl = ?,clientUrl = ?,spaceLabel = ?,allowedAuths = ?,defaultAuth = ?,additionalFields = ?,dropDownLabel = ?,dropDownOptions = ?,dropDownKey = ? WHERE content_source_type_id = ?`;

    const sqlParams = [
      content_source_type_id, hasPlaces, hasClientCreds, hasTenant, hasClientUrl,clientUrl, spaceLabel, allowedAuths, defaultAuth,
        JSON.stringify(JSON.parse(additionalFields)), dropDownLabel, dropDownOptions, dropDownKey,content_source_type_id];
      connection[req.headers['tenant-id']].execute.query(sqlUpdate, sqlParams,(err, results) => {
        if (err) {
           res.status(500).send({ "error": "Error updating cs Auth  components" });
           return;
          }
            
        else {
          res.send({"data": results});
        }
     });
  })

  router.get('/getCsType', function (req, res, next) {
    const sqlGet = `SELECT * FROM content_source_types`;
    connection[req.headers['tenant-id']].execute.query(sqlGet, [], (err, results) => {
      if (err) {
         res.status(500).send({ "error": "Error fetching content source types" });
        return;
      }
  
      else {
        res.send({"data": results});
      }
    });
  });

router.post('/updateCsType', function(req,res){
    if (req.body.appId){
        delete req.body.appId;
    }
    const {id,name,description,url } = req.body;
    const filteredBody = {
      id:id,
      name:name,
      description:description,
      url:url,
    };
    const sqlUpdate = `UPDATE content_source_types SET name = ?,description = ?,url = ? WHERE id = ?`;
    const sqlParams = [name,description,url,id];
    connection[req.headers['tenant-id']].execute.query(sqlUpdate, sqlParams,(err, results) => {
        if (err) {
           res.status(500).send({ "error": "Error updating cs Type in admin" });
          return;
        } 
    });
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret": config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'],
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + `/crawler-config/update-cs-type`,'', filteredBody, headers, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.error("Error found: ", error);
            res.send(error);
        }
        else {
            commonFunctions.errorlogger.info("result found: ", result);
            res.send(result);
        }
    })
    
});

  module.exports = {
    router: router,
}
