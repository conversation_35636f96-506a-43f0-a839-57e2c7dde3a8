module.exports = {
    "objects": [{
        "name": "Files",
          "label": "Files",
          "status": 1,
          "boosting_factor": 1,
          "base_url": "",
          "fields": []
      }],

     "fields": [
        {
          name: 'id',
          label: 'Id',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'url',
          label: 'URL',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'description',
          label: 'Description',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
    
        },
        {
          name: 'folder_name',
          label: 'Root Folder Name',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'action',
          label: 'Action',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'title',
          label: 'Title',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'modified_at',
          label: 'Modified At',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1,
          isMerged: 1
        },
        {
          name: 'mix_type',
          label: 'Mix Type',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'format',
          label: 'Format',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'tags',
          label: 'Tags',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'last_replication_action',
          label: 'Last Replication Action',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'created_by',
          label: 'Created By',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'contact_name',
          label: 'Contact Name',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'language',
          label: 'Language',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'uuid',
          label: 'Uuid',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'status',
          label: 'Status',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        },
        {
          name: 'asset_type',
          label: 'Asset Type',
          type: 'string',
          isFilterable: 1,
          isSortable: 1,
          isSearchable: 1,
          isActive: 1
        }
      ]
}