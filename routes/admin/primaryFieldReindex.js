var path = require('path');
environment = require('../environment');
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, './../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
const request = require('request');
var async = require('async');
const { Client } = require('@elastic/elasticsearch')
var emailTemplates = require('../emailTemplates');
const searchunifyEmail = require('../../Lib/email.js');
var connection_sql = require('./../../utils/connection');
var commonFunctions = require('./../../utils/commonFunctions');
const { tenantSqlConnection  } = require('../../auth/sqlConnection');

commonFunctions.errorlogger.info("Arguments passed: ",process.argv);
var processType = process.argv[2]
var index = process.argv[3];
var typeObj = process.argv[4];
var primaryField = process.argv[5];
var data = {
    searchClientName: process.argv[6],
    emailId: process.argv[7]
}
var uid = process.argv[8];

let esClient = new Client({
    nodes: process.argv[9].split(','),
    nodeSelector: 'random'
});

const createMappingObj = (currentIndex, propertiesObj, primaryField, cb) => {

    var updateCopyTo = [];
    var backupPrimaryField = [];
    let gvAlreadyExists = false;

    for (var prop in propertiesObj) {
        let exists = false;
        if (prop == `groupVersion_${uid}`)
            gvAlreadyExists = true;

        if (propertiesObj[prop]["copy_to"] && propertiesObj[prop]["copy_to"].indexOf(`groupVersion_${uid}`) > -1) {
            if (prop != primaryField) {
                propertiesObj[prop]["copy_to"].splice(propertiesObj[prop]["copy_to"].indexOf(`groupVersion_${uid}`),1);
                updateCopyTo.push({
                    field: prop,
                    prop: propertiesObj[prop],
                    copy_to: propertiesObj[prop]["copy_to"]
                })
            }
            else
                exists = true;
        }
        if (processType == "reindex" && prop == primaryField && !exists) {
            backupPrimaryField.push({
                field: prop,
                prop: propertiesObj[prop],
                copy_to: propertiesObj[prop]["copy_to"] && propertiesObj[prop]["copy_to"].length ? propertiesObj[prop]["copy_to"].concat(`groupVersion_${uid}`) : [`groupVersion_${uid}`]
            })
        }
        else if (processType == "removeField" && prop == "uniqueField") {
            let copyTo = [];
            if (propertiesObj[prop]["copy_to"] && propertiesObj[prop]["copy_to"].indexOf(`groupVersion_${uid}`) > -1)
                copyTo = propertiesObj[prop]["copy_to"];
            else {
                copyTo = propertiesObj[prop]["copy_to"] && propertiesObj[prop]["copy_to"].length ?  propertiesObj[prop]["copy_to"].concat(`groupVersion_${uid}`) : [`groupVersion_${uid}`];

                updateCopyTo.push({
                    field: prop,
                    prop: propertiesObj[prop],
                    copy_to: copyTo
                })
            }
        }


    }
    // commonFunctions.errorlogger.info("Remove mapping from: ", updateCopyTo);
    // commonFunctions.errorlogger.info("Update mapping for: ", backupPrimaryField);

    cb(null, {updateCopyTo, backupPrimaryField, gvAlreadyExists});
};

const getMapping = async (index, typeObj, cb) => {
    let response = await esClient.indices.getMapping({
        index,
        type: typeObj,
    });
    if (response && response.statusCode == 200) cb (null, response.body[index].mappings[typeObj].properties);
    else cb ('Cannot get mapping');
}

const updateMapping = async (index, fields, typeObj, cb) => {

    var i = {"properties": {}}
    if (!fields.gvAlreadyExists)
        i["properties"][`groupVersion_${uid}`] = {
            "type" : "text",
            "fields" : {
                "en" : {
                "type" : "text",
                "analyzer" : "custom_lowercase_stemmed",
                "search_analyzer" : "custom_lowercase_synonym"
                },
                "keyword" : {
                    "type" : "keyword",
                    "normalizer": "custom_normalizer",
                    "ignore_above" : 256
                }
            }
        }

    fields.updateCopyTo.forEach((f) => {
        let j = f["field"];
        i["properties"][j] = f["prop"];
        i["properties"][j]["copy_to"] = f["copy_to"]
    })
    if (processType == "reindex")
        fields.backupPrimaryField.forEach((f) => {
            let j = f["field"] ;
            i["properties"][j] = f["prop"];
            i["properties"][j]["copy_to"] = f["copy_to"]
        })

    let options = {
        url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port')+`/${index}/_mapping/${typeObj}`,
        method: "PUT",
        body: JSON.stringify(i),
        headers : {
        "Content-Type": "application/json"
        }
    }
    if (Object.keys(i["properties"]).length) {
        commonFunctions.errorlogger.info(`Updating mapping for ${index} ${typeObj}: `,JSON.stringify(i));
        let response = await esClient.indices.putMapping({
            index,
            type: typeObj,
            body: i
        });
        if (response && response.statusCode == 200) {
            commonFunctions.errorlogger.info('Added field and updated mapping if any for: ',index, typeObj, response.body);
            cb(null, response.body);
        } else {
            commonFunctions.errorlogger.error('error in update Mapping:', response);
            cb('Unable to update mapping');
        }
    }
    else {
        commonFunctions.errorlogger.info(`No mapping update required for ${index} ${typeObj}: `,JSON.stringify(i));
        cb(null, '');
    }
}

const reindex = async (index, typeObj, cb) => {
    commonFunctions.errorlogger.info("Reindexing for ",index, typeObj);
    let response = await esClient.updateByQuery({
        index,
        type: typeObj,
        body: `{
            "query": {
                "match_all": {}
            }
        
        }`,
    });
    if (response && response.statusCode == 200)
        commonFunctions.errorlogger.info("Reindexing complete for ",index, typeObj, ' with response: ',response.body);
    else
        commonFunctions.errorlogger.error('error while reindex:', response);
    cb(null, response.body);
}

const sendPrimaryFieldReindexingEmail = function (data, callback){

    if (data.emailId) {
        var body = emailTemplates.reindexingSuccessTemplate(data.emailId, data.searchClientName);
        searchunifyEmail.sendEmail(data.emailId, `SearchUnify: ${data.searchClientName} Merge Results reindexing completed`, body, (response) => {
            if(!response){
            callback(null, 'Mail could not be sent');
            }
            else{
            callback(null, 'Mail sent');
            }
        });
    } else
        callback(null, 'Email ID not provided');
}

const createUniqueFieldMapping = function (index, type, merge_results, propertiesObj, cb) {

    var updateCopyTo = [];
    var backupPrimaryField = [];
    let exists = false;
    let gvAlreadyExists = false;
    merge_results = JSON.parse(merge_results || "{}");

    if (Object.keys(merge_results).length == 0 || (Object.keys(merge_results).length && !merge_results)) {
        for (var prop in propertiesObj) {
            if (prop != "uniqueField" && propertiesObj[prop]["copy_to"] && propertiesObj[prop]["copy_to"].indexOf(`groupVersion_${uid}`) > -1) {
                propertiesObj[prop]["copy_to"].splice(propertiesObj[prop]["copy_to"].indexOf(`groupVersion_${uid}`),1);
                updateCopyTo.push({
                    field: prop,
                    prop: propertiesObj[prop],
                    copy_to: propertiesObj[prop]["copy_to"]
                })
            }
        }
    }

    for (var prop in propertiesObj) {
        if (prop == `groupVersion_${uid}`)
            gvAlreadyExists = true;
        if (propertiesObj[prop]["copy_to"] && propertiesObj[prop]["copy_to"].indexOf(`groupVersion_${uid}`) > -1 && prop != "uniqueField") {
            exists = true;
        }
    }
    if ( !exists ) {
        let copyTo = [];
        if (propertiesObj["uniqueField"]["copy_to"] && propertiesObj["uniqueField"]["copy_to"].indexOf(`groupVersion_${uid}`) > -1)
            copyTo = propertiesObj["uniqueField"]["copy_to"];
        else {
            copyTo = propertiesObj["uniqueField"]["copy_to"] && propertiesObj["uniqueField"]["copy_to"].length ?  propertiesObj["uniqueField"]["copy_to"].concat(`groupVersion_${uid}`) : [`groupVersion_${uid}`];

            updateCopyTo.push({
                field: "uniqueField",
                prop: propertiesObj["uniqueField"],
                copy_to: copyTo
            })
        }
    }
    // commonFunctions.errorlogger.info(`CopyTo mapping for ${index} ${type}: `, updateCopyTo);

    cb(null, {updateCopyTo, backupPrimaryField, gvAlreadyExists});
}

const groupVersionUpdate = function (indexDetails, callback) {
        async.auto({
            "getMapping" : cb => {
                getMapping(indexDetails.indexName, indexDetails.type, cb);
            },
            "createMapping" : ["getMapping", (data, cb) => {
                if ( processType == "setDefaultField" )
                    createUniqueFieldMapping(indexDetails.indexName, indexDetails.type, indexDetails.mr, data.getMapping, cb);
                else
                    createMappingObj(index, data.getMapping, primaryField, cb);
            }],
            "updateMapping" : ["createMapping", (data, cb) => {
                updateMapping(indexDetails.indexName, data.createMapping, indexDetails.type, cb);
            }],
            "reindex" : ["updateMapping", (data, cb) => {
                if ( data.createMapping.updateCopyTo.length || data.createMapping.backupPrimaryField.length || !data.createMapping.gvAlreadyExists )
                    reindex(indexDetails.indexName, indexDetails.type, cb);
                else
                    cb(null, 'Done');
            }]
        },(error, results) => {
            if (error)
                commonFunctions.errorlogger.error("Error: ",error);
            else {
                if (processType == "reindex")
                    sendPrimaryFieldReindexingEmail(data, (err, response) => {
                        commonFunctions.errorlogger.info(response);
                        callback(null, 'Done');
                    })
                else {
                    callback(null, 'Done');
                }
            }
        })
}

const getIndexes = async function () {
    await tenantSqlConnection(process.argv[10], process.argv[11]);
    let sql = `SELECT cs.elasticIndexName as indexName, cso.name as type, sc.uid, sctco.merge_results mr 
                from search_clients sc, search_clients_to_content_objects sctco, content_source_objects cso, content_sources cs 
                WHERE sc.uid = ? AND sc.id = sctco.search_client_id AND sctco.content_source_object_id  = cso.id AND cso.content_source_id = cs.id`;
    connection[process.argv[10]].execute.query(sql, uid, (error, indexArray) => {
        // console.log("indexArray data: ",indexArray);
        if (!error && indexArray.length){
            // indexArray.filter((f) => f.indexName != index);
            let taskobjects = [];
            commonFunctions.errorlogger.info("Indices: ",indexArray);
            for (var i = 0; i < indexArray.length; i++) {
                taskobjects.push((function (i) {
                    return function (cb) {
                        groupVersionUpdate(indexArray[i], cb);
                    }
                })(i))
            }
            async.parallel(taskobjects, function (error, result) {
                if (error)
                    commonFunctions.errorlogger.error(error);
                commonFunctions.errorlogger.info("Done");
                process.exit(0);
            })
        }
    })
}

try{
    connection_sql.handleDisconnect().then(function (result) {
        if ( processType != "setDefaultField" ) {
            groupVersionUpdate({indexName:process.argv[3],type:process.argv[4],primaryField:process.argv[5],mr:''}, (error, resp) => {
                commonFunctions.errorlogger.info("Process completed");
                process.exit(0);
            });
        }
        else {
            getIndexes();
        }
    });
} catch (e) {
    commonFunctions.errorlogger.error("Exception: ", e);
}
