var moment = require("moment");
var express = require("express");
var router = express.Router();
environment = require("../../routes/environment");
config = require("config");
var commonFunctions = require("../../utils/commonFunctions");
commonFunctions.errorlogger.log(environment.configuration);
const {
    tenantGlobals,
} = require("../../utils/kafka/kafkaPublishers/tenantGlobals");

config = require("config");
const aes256 = require('nodejs-aes256');

router.post("/", (req,res)=>{

    const data = aes256.decrypt(config.get("statusPageService.secretKey"), req.body._s);
    const decryptedData = JSON.parse(data);
   
    const endDateFromApi = moment(decryptedData.subscriptionEndDate).local();
    const subscriptionDays = endDateFromApi.diff(moment().local(), 'days');
    const tenantId = decryptedData.tenantId;
    
    // Retrieve current data from tenant_globals
    const selectSql = "SELECT * FROM tenant_globals";
    connection[tenantId].execute.query(selectSql, (selectErr, results) => {
        if (selectErr) {
            commonFunctions.errorlogger.error(
                selectErr || "Error retrieving data from tenant_globals"
            );
            return res.send({
                success: false,
                message: "Failed updating LLM Configuration",
                data: null,
            });
        }

        const currentData = results[0] || {};
        const parsedData = JSON.parse(currentData.gpt);
        const claudeIntegration = parsedData.integrations.find(
            (integration) => integration.name === "claude"
        );
        claudeIntegration.hasError = false;

        var currentDate;
        var final_start_date;
        var final_end_date;

        if(claudeIntegration.subscription){
            const endDateMoment = moment(claudeIntegration.subscription.trial.subscriptionEndDate).local();
            const timeDifference = endDateMoment.diff(moment(), 'days');
            
            if(timeDifference > 0){
                final_start_date = moment().add(timeDifference,'days');
                final_end_date = final_start_date.clone().add(subscriptionDays,'days');;
            }
            else{
                final_start_date = moment();
                final_end_date = moment().add(subscriptionDays,'days');
            }

            claudeIntegration.subscription.main = { 
                subscriptionStartDate: final_start_date, 
                subscriptionEndDate: final_end_date,
                enabledStatus: 2,
            };
        }else{
            final_start_date = moment();
            final_end_date = moment().add(subscriptionDays,'days');

            if (!claudeIntegration.subscription || typeof claudeIntegration.subscription !== 'object') {
                claudeIntegration.subscription = {};
            }

            claudeIntegration.subscription.main = {
                subscriptionStartDate: final_start_date, 
                subscriptionEndDate: final_end_date,
                enabledStatus: 2,
            }
        }

        //updating the data in tenant_globals 
        const updateSql = "UPDATE tenant_globals set gpt=?";
        connection[tenantId].execute.query(
            updateSql,
            [JSON.stringify(parsedData)],
            (err) => {
                if (err) {
                    commonFunctions.errorlogger.error(
                        err || "no data found in tenant_globals"
                    );
                    return res.send({
                        success: false,
                        message: "Failed updating LLM Configuration",
                        data: null,
                    });
                }

            /* Publishing the kafka */
            tenantGlobals(tenantId, { parsedData });
            res.send({
                success: true,
                message: "LLM Configuration Updated",
                data: parsedData,
            });
            }
        );
    });
});

module.exports = {
   router: router
}