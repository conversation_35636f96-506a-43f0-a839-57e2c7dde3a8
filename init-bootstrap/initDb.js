const mysql = require("mysql");
const async = require("async");
let connection = mysql.createConnection({
    host: config.get('databaseSettings.host'),
    user: config.get('databaseSettings.user'),
    password: config.get('databaseSettings.password'),
    // database   : config.get('databaseSettings.database'),
    port: config.get('databaseSettings.mysqlPORT'),
    socketPath: '/var/run/mysqld/mysqld.sock'
});
connection.connect((err)=>{
    if (err) 
        console.error('error connecting: ' , err);
    else
        console.log('connected as id ' + connection.threadId);
});
exports.main = cb => {
    /**
     * 1. Check DB
     * 2. Check tables
     */
    async.series([
        cb=>{
            connection.query(`CREATE DATABASE IF NOT EXISTS \`${config.get('databaseSettings.database')}\``,cb);
        },
        cb=>{
            connection.query(`use \`${config.get('databaseSettings.database')}\``,cb);
        },
        cb=>{
            connection.query('CREATE TABLE IF NOT EXISTS deflecton_formula (id int NOT NULL AUTO_INCREMENT,search_client_id int NOT NULL,session_url_regex varchar(255),custom_event varchar(63) DEFAULT NULL,PRIMARY KEY (id),FOREIGN KEY (search_client_id) REFERENCES search_clients(id))',cb);
        },
        cb=>{
            connection.query("show tables",(e,r,f)=>{
                console.log(e,r,f);
                cb(e,r);
            });
        }
    ],(e,r)=>{
        cb(e,r);
    });
    // connection.end();
    // cb();
};

/**
 * fetch:cb=>{
      const getMoreUntilDone = (error, response) => {
        if (error) cb(error);
        else {
          cast = cast.concat(response.hits.hits.map(d=>{
            if(d._type==="log"){
              let geo = geoip.lookup(d._source.ip);
              d._source = {
                "cookie"    : d._source.cookie,
                "event"     : d._source.event,
                "referer"   : d._source.referer,
                // "size"      : d._source.size,
                "taid"      : d._source.taid,
                "targetUrl" : d._source.targetUrl,
                "title"     : d._source.title,
                "ts"        : d._source.ts,
                "uid"       : d._source.uid,
                "url"       : d._source.url
              };
              if(geo){
                d._source.geo={
                  lat:geo.ll[0],
                  lon:geo.ll[1]
                };
              }
            }
            else if(d._type==="search_keyword"){
              d._source = {
                "conversion"   : d._source.conversion,
                "cookie"       : d._source.cookie,
                "filters"      : d._source.filters,
                "isClicked"    : d._source.isClicked,
                "page_no"      : d._source.page_no,
                "result_count" : d._source.result_count,
                "search_date"  : d._source.search_date,
                "search_type"  : d._source.search_type,
                "text_entered" : d._source.text_entered,
                "uid"          : d._source.uid
              };
            }
            return d;
          }));
          if (response.hits.hits.length > 0) {
            client.scroll({
                scrollId: response._scroll_id,
                "scroll": "30s"
            }, getMoreUntilDone);
          }
          else{
            cb(null,cast);
          }
        }
      };
      client.search({
        index: config.get("elasticIndex.analytics"),
        // type: "log",
        size: 50,
        scroll: "30s"
      }, getMoreUntilDone);
    },
    "delete":["fetch",(results,cb)=>{
      client.indices.delete({index:config.get("elasticIndex.analytics")},(error, results)=>{
        cb(error, results);
      });
    }],
    "createMapping":["delete",(results,cb)=>{
      client.indices.create({
        index:config.get("elasticIndex.analytics"),
        body:{"mappings":{"users":{"properties":{"cookie":{"type":"string","index":"not_analyzed"},"domain":{"type":"string","index":"not_analyzed"},"emailMD5":{"type":"string","index":"not_analyzed"},"taid":{"type":"string","index":"not_analyzed"},"ts":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"uid":{"type":"string","index":"not_analyzed"}}},"supportLog":{"properties":{"caseUid":{"type":"string","index":"not_analyzed"},"cookie":{"type":"string","index":"not_analyzed"},"event":{"type":"string","index":"not_analyzed"},"ts":{"type":"date","format":"strict_date_optional_time||epoch_millis"}}},"log":{"properties":{"cookie":{"type":"string","index":"not_analyzed"},"event":{"type":"string","index":"not_analyzed"},"referer":{"type":"string","index":"not_analyzed"},"size":{"type":"long"},"taid":{"type":"string","index":"not_analyzed"},"targetUrl":{"type":"string","index":"not_analyzed"},"title":{"type":"string","index":"not_analyzed"},"ts":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"uid":{"type":"string","index":"not_analyzed"},"url":{"type":"string","index":"not_analyzed"},"gio":{"type":"geo_point"}}},"search_keyword":{"properties":{"conversion":{"type":"nested","properties":{"conversion_date":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"es_id":{"type":"string","index":"not_analyzed"},"rank":{"type":"short"},"subject":{"type":"string","index":"not_analyzed"},"url":{"type":"string","index":"not_analyzed"}}},"cookie":{"type":"string","index":"not_analyzed"},"entry":{"type":"string"},"filters":{"type":"nested","properties":{"name":{"type":"string","index":"not_analyzed"},"selectedValues":{"type":"string","index":"not_analyzed"}}},"isClicked":{"type":"boolean"},"page_no":{"type":"short"},"result_count":{"type":"short"},"search_date":{"type":"date","format":"strict_date_optional_time||epoch_millis"},"search_type":{"type":"boolean"},"text_entered":{"type":"string","index":"not_analyzed"},"uid":{"type":"string","index":"not_analyzed"}}}}}
      },(error, results,ex)=>{
        // console.log(error, results,ex);
        cb(error, results);
      });
    }],
    "insert":["fetch","createMapping",(results,cb)=>{
      let bulk = [];
      results.fetch.forEach(d=>{
        bulk.push({
          index: {
            "_index":d._index,
            "_type":d._type,
            "_id":d._id,
          }
        });
        bulk.push(d._source);
      });
      client.bulk({
        body:bulk
      },(error,result,ex)=>{
        cb(error,result);
      });
    }],
 */