# docker compose for admin panel service
version: "3.0"

services:
  admin:
    container_name: admin
    build:
      context: .
      dockerfile: prod.Dockerfile
    volumes:
      - ".:/home/<USER>"
      - "/home/<USER>/node_modules"
      - "/home/<USER>/dist"
    networks:
      - shared_network
    command: node app.js
    ports:
      - "6009:6009"

networks:
  shared_network:
    external:
      name: shared_network
