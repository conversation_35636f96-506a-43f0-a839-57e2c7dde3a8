const axios = require('axios');

const createRequest = (options) =>
  new Promise((resolve, reject) => {
    const body = {
      method: options.method,
      url: options.url,
      headers: options.headers || {},
      data: options.body,
      params: options.qs,
    };
    axios
      .request(body)
      .then((res) => {
        resolve(res);
      })
      .catch((error) => {
        reject(error);
      });
});

module.exports = createRequest;
  