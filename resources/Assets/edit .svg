<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" viewBox="0 0 24 24">
  <defs>
    <linearGradient id="linear-gradient" x1="1.151" y1="0.149" x2="-0.291" y2="1.384" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#55c7ff"/>
      <stop offset="1" stop-color="#7886f7"/>
    </linearGradient>
    <clipPath id="clip-Edit">
      <rect width="24" height="24"/>
    </clipPath>
  </defs>
  <g id="Edit" clip-path="url(#clip-Edit)">
    <g id="outline-border_color-24px_2_" data-name="outline-border_color-24px (2)" transform="translate(0 0.002)">
      <path id="Path_1157" data-name="Path 1157" d="M0,0H24V24H0Z" fill="none"/>
      <path id="Path_1158" data-name="Path 1158" d="M14,3.25l-10,10V17H7.75l10-10ZM6.92,15H6v-.92l8-8,.92.92ZM20.71,4.04a1,1,0,0,0,0-1.41L18.37.29a1,1,0,0,0-1.41,0L15,2.25,18.75,6Z" fill="url(#linear-gradient)"/>
      <path id="Path_1159" data-name="Path 1159" d="M0,20H24v4H0Z" fill="url(#linear-gradient)"/>
    </g>
  </g>
</svg>
