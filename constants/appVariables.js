/**
 * Created by man<PERSON><PERSON> on 9/12/17.
 */
const config = require('config');
var variables = {
  project: "SearchUnify-v2.0",
  plugins: {
    "pluginsPath": "./customPlugins",
    "propertyFile": "/properties.json",
    "universalPath": "../../routes/universal"
  },
  analytics: {
    "encryptionKey": "Grazitti$271##",
    "md5salt": "@##@Lkvk@8a$*5c9#JOKxZwf64d@@"
  },
  salesforce: {
    "redirectUri": "/admin/authorization/oAuthCallback",
    "clientId": "3MVG9WtWSKUDG.x4.egfG0lQq64wyWUxqJ5ugxZ6vjPSGGBF276q4fkkY9M38Y.fkz.vKSTQlqTN_FFk8KPOn",
    "appSecret": "A83D8B369639A7CD8866F6CBD9744AEC7467E3AFE0197187F5B4E0A9A2AD3FE2",
    "urlSuffix": "/services/oauth2/authorize"
  },
  drive: {
    "redirectUri": "/admin/authorization/oAuthCallback",
    "clientId": "942983407695-ktldbm2c5q931605laf11ea92q6fp1ed.apps.googleusercontent.com",
    "appSecret": "J8_vDgyIJKXdHpdT-R9lGDWB"
  },
  youtube: {
    "redirectUri": "/admin/authorization/oAuthCallback",
    "clientId": config.get("youtube.clientId"),
    "appSecret": config.get("youtube.appSecret")
  },
  helpScout: {
    baseUrl: 'https://secure.helpscout.net/authentication/authorizeClientApplication',
    redirectUri: '/admin/authorization/oAuthCallback',
    accessTokenUrl: 'https://api.helpscout.net/v2/oauth2/token',
    urlSuffix: '',
  },
  vimeo: {
    "baseUrl": "https://api.vimeo.com",
    "redirectUri": "/admin/authorization/oAuthCallback",
    "clientId": "794f298031fabd81e17c0c2c8c1fe16e95626930",
    "appSecret": "7o02vPScI5hu9XRsa6TC4P8Ydf41bM9nFIWRJxihlECh5V0spl8i/yxI8aZ3GQRiMF2ZfdQe7CX1bfLOO+2y6K5VIKyh/m5lpg40fmP0Ae8LJ6LL3NIolEKW3+2eRhGB",
    "additionalTags": "&response_type=code"
  },
  box: {
    "redirectUri": "/admin/authorization/oAuthCallback",
    "clientId": "********************************",
    "appSecret": "********************************"
  },
  github: {
    "url": "https://github.com/login",
    "redirectUri": "/admin/authorization/oAuthCallback",
    "clientId": "5932df051a583a90d21b",
    "appSecret": "e341fb58418cbd31a3a1696a909cd728d848715f",
    "additionalTags": "&response_type=code&scope=repo user"
  },
  stackoverflow: {
    "redirectUri": "/admin/authorization/oAuthCallback",
    "accessToken": 'xfFUuH7rELQ7ogALqoieNw))',
    "clientId": config.get("stackoverflow.clientId"),
    "clientSecret": config.get("stackoverflow.clientSecret"),
    "key": config.get("stackoverflow.key")
  },
  aha: {
    "redirectUri" : "/admin/authorization/oAuthCallback",
    "additionalTags": "&response_type=code"
  },
  microsoftTeams:{
    "url": "https://login.microsoftonline.com/5db30893-fb80-480e-be22-911156d0e5e0/",
    "clientId": "341f4ed9-36eb-4198-b1ff-16c23736cccb",
    "appSecret": "*************************************",
    "urlSuffix": "oauth2/v2.0/authorize",
    "additionalTags": "&scope=offline_access user.read mail.read&response_type=code"
  },
  sharepoint: {
    // "appSecret": "0bNk4Gop3cxfleBroNJEIKnohlwGuEbr1F+FABNFcXA=",
    "baseUrl": "https://login.microsoftonline.com/common",
    "redirectUri" : "/admin/authorization/oAuthCallback",
    "urlSuffix": "/oauth2/v2.0/authorize",
    "additionalTags": "&response_type=code&grant_type=authorization_code"
  },
  dropbox: {
    "clientId": "5108rv7fdg5wxgh",
    "appSecret": "q414h3f15jngy27"
  },
  slack: {
    "baseUrl": "https://slack.com",
    "redirectUri": "/admin/authorization/oAuthCallback",
    "clientId":"236847335267.529647717923",
    "appSecret":"********************************",
    "channelHistory": "https://slack.com/api/channels.history",
    "getMessageUrl":  "https://slack.com/api/chat.getPermalink",
    "authorizationUrl":"/rest/api/2/field",
    "additionalTags": "&scope=read&team="
  },
  lithium: {
    "restPath": "/restapi/vc",
    "publicRoles": [
      "CategoryExpert"
    ],
    "redirectUri": "/oAuthCallbackLithium",
  },
  JiveCron: {
    "name": "jiveUpdateCron",
    "api_to_call": "/getAllContent?startDate=2016-09-20&endDate=2016-09-21&typeContent=document"
  },
  zendesk: {
     "urlSuffix": "/oauth/authorizations/new",
     "additionalTags": "&response_type=code&scope=read write",
     "redirectUri": "/admin/authorization/oAuthCallback"
  },
  docebo: {
     "urlSuffix": "/oauth2/authorize",
     "additionalTags": "&response_type=code&grant_type=implicit&scope=api",
     "redirectUri": "/admin/authorization/oAuthCallback"
  },
  jive: {
    "urlSuffix": "/oauth2/authorize",
    "additionalTags": "&response_type=code",
    "redirectUri": "https://oauthsfdc.searchunify.com"
  },
  wistia: {
    "url": "https://app.wistia.com",
    "redirectUri": "/admin/authorization/oAuthCallback",
    "clientId": "a965d1704b6f68db64bda0efaf149e5f8b8a41bcc65b9ae4d16a713faccdb749",
    "clientSecret": "2c9af4937368bff00805e651af0130562b9898c5f44704562a1cd14a15ba3679",
    "additionalTags": "&response_type=code"
  },
  accessTokenAdmin: "a7cc167ab1e57e3ca70c96d1f4413535",
  "crawlCronUrl": "/admin/contentSources/crawlData",
  "surchUnifyLogo": "/assets/img/searchUnifyLogo.png",
  "searchunify_version_mail": "/assets/img/searchunify_version_mail.png",
  "baner2": "/assets/img/baner2.png",
  "nodeMailer": {
    "user": "<EMAIL>",
    "clientId": "************-72oql1an4je6rgeuqd0736povn14v46v.apps.googleusercontent.com",
    "clientSecret": "IWM5Nmg3kvhurJXNvnjrFKAs",
    "refreshToken": "1/5gTr_qvBeiPsNmq7CweUmY9EcOAErFc17qulRxkVrBQ"
  },
  servicenow: {
    "urlSuffix":'/oauth_auth.do',
    "additionalTags": "&response_type=code&grant_type=authorization_code&scope=useraccount",
    "redirectUri": "/admin/authorization/oAuthCallback"
  },
  dynamics: {
    "urlSuffix":'/oauth2/authorize',
    "redirectUri": "/admin/authorization/oAuthCallback",
    "baseUrl":"https://login.microsoftonline.com/",
    "additionalTags": "&response_type=code&prompt=select_account"
  },
  brightspace: {
    "redirectUri": "https://oauthsfdc.searchunify.com",
    "accessTokenUrl": "/core/connect/token",
    "urlSuffix":'/oauth2/auth',
    "additionalTags": "&response_type=code&scope=content:file:read content:toc:read content:topics:readonly enrollment:orgunit:read enrollment:own_enrollment:read managefiles:files:read organizations:organization:read orgunits:course:read users:userdata:read users:own_profile:read users:profile:read"
  },
  khorosAurora: {
    "redirectUri": "https://oauthsfdc.searchunify.com"
  },
  uservoice: {
    "urlSuffix": '/api/v2/oauth/auth',
    "redirectUri": "https://oauthsfdc.searchunify.com",
    "accessTokenUrl": "/api/v2/oauth/token",
    "additionalTags": "&response_type=code&code_challenge_method=S256"
  },
  simpplr: {
    "url": "https://login.salesforce.com/services",
    "urlSuffix": "/oauth2/authorize",
    "redirectUri": "https://oauthsfdc.searchunify.com",
    "accessTokenUrl": "/oauth2/token",
    "additionalTags": "&response_type=code"
  },
  airtable: {
    "url": "https://api.airtable.com",
    "urlSuffix": '/oauth2/v1/authorize',
    "redirectUri": 'https://oauthsfdc.searchunify.com',
    "accessTokenUrl": '/oauth2/v1/token',
    "additionalTags": '&response_type=code&scope=data.records:read data.records:write schema.bases:read'
  },
  oauthLog: {
    "burst": 3500,
    "interval": "1",
    "frequency": "hour"
  },
  "transporterWebMail": {
    "host": "8wrk-pj2f.accessdomain.com",
    "user": "<EMAIL>",
    "pass": "qli#G0oM?7"
  },
  "gmail":
  {
    "userName": config.get('gmail.userName'),
    "password": config.get('gmail.password')
  },
  "analyticsEmailList": ["<EMAIL>"],
  "registerURL": "/signup?",
  "fromEmail": "Search Unify<<EMAIL>>",
  "oAuthRedirectURI": "https://oauthsfdc.searchunify.com",
  "allowedTags": ["<img>", "<video>"],
  userRole: {
    Admin: 1,
    Moderator: 2
  },
  STATUS_ERROR: {
    USER_EXISTS: {
      statusCode: 401,
      type: 'USER_EXISTS',
      customMessage: 'User Exists. Please Enter the Password'
    },
    CUSTOMER_USER_EXISTS: {
      statusCode: 401,
      type: 'CUSTOMER_EXISTS',
      customMessage: 'This Account is registered as Customer. Please Choose a different Phone Number'
    },
    INVALID_USER_PASS: {
      statusCode: 401,
      type: 'INVALID_USER_PASS',
      customMessage: 'Invalid username or password'
    },
    TOKEN_EXPIRED: {
      statusCode: 402,
      customMessage: 'Token Expired',
      type: 'TOKEN_EXPIRED'
    },
    DB_ERROR: {
      statusCode: 400,
      customMessage: 'DB Error : ',
      type: 'DB_ERROR'
    },
    INVALID_ID: {
      statusCode: 400,
      customMessage: 'Invalid Id Provided : ',
      type: 'INVALID_ID'
    },
    APP_ERROR: {
      statusCode: 400,
      customMessage: 'Application Error',
      type: 'APP_ERROR'
    },
    ADDRESS_NOT_FOUND: {
      statusCode: 400,
      customMessage: 'Address not found',
      type: 'ADDRESS_NOT_FOUND'
    },
    IMP_ERROR: {
      statusCode: 500,
      customMessage: 'Implementation Error',
      type: 'IMP_ERROR'
    },
    APP_VERSION_ERROR: {
      statusCode: 400,
      customMessage: 'One of the latest version or updated version value must be present',
      type: 'APP_VERSION_ERROR'
    },
    INVALID_TOKEN: {
      statusCode: 402,
      customMessage: 'Invalid token provided',
      type: 'INVALID_TOKEN'
    },
    DEFAULT: {
      statusCode: 400,
      customMessage: 'Error',
      type: 'DEFAULT'
    },
    EMAIL_EXIST: {
      statusCode: 400,
      customMessage: 'Email Already Exist',
      type: 'EMAIL_EXIST'
    },
    DUPLICATE: {
      statusCode: 400,
      customMessage: 'Duplicate Entry',
      type: 'DUPLICATE'
    },
    INVALID_EMAIL: {
      statusCode: 400,
      customMessage: 'Invalid Email Address',
      type: 'INVALID_EMAIL'
    },
    PASSWORD_REQUIRED: {
      statusCode: 400,
      customMessage: 'Password is required',
      type: 'PASSWORD_REQUIRED'
    },
    NOT_FOUND: {
      statusCode: 404,
      customMessage: 'User Not Found',
      type: 'NOT_FOUND'
    },
    INVALID_RESET_PASSWORD_TOKEN: {
      statusCode: 400,
      customMessage: 'Invalid Reset Password Token',
      type: 'INVALID_RESET_PASSWORD_TOKEN'
    },
    INCORRECT_PASSWORD: {
      statusCode: 401,
      customMessage: 'Incorrect Password',
      type: 'INCORRECT_PASSWORD'
    },
    NOT_APPROVED: {
      statusCode: 401,
      customMessage: 'Account is not approved yet, Please wait for confirmation from client',
      type: 'NOT_APPROVED'
    },
    EMPTY_VALUE: {
      statusCode: 400,
      customMessage: 'Empty String Not Allowed',
      type: 'EMPTY_VALUE'
    },
    SAME_PASSWORD: {
      statusCode: 400,
      customMessage: 'Old password and new password are same',
      type: 'SAME_PASSWORD'
    },
    EMAIL_ALREADY_EXIST: {
      statusCode: 400,
      customMessage: 'Email Address Already Exists',
      type: 'EMAIL_ALREADY_EXIST'
    },
    INCORRECT_OLD_PASS: {
      statusCode: 400,
      customMessage: 'Incorrect Old Password',
      type: 'INCORRECT_OLD_PASS'
    },
    UNAUTHORIZED: {
      statusCode: 401,
      customMessage: 'You are not authorized to perform this action',
      type: 'UNAUTHORIZED'
    },
    NO_POST_FOUND: {
      statusCode: 401,
      customMessage: 'no post found',
      type: 'NO_POST_FOUND'
    },
    ACCOUNT_EXISTS: {
      statusCode: 401,
      type: 'ACCOUNT_EXISTS',
      customMessage: 'Account already Exists'
    },
    USERNAME_EXISTS: {
      statusCode: 401,
      type: 'USERNAM_EXISTS',
      customMessage: 'UserName already Exists'
    },
    ACCOUNT_NOT_REGISTERED: {
      statusCode: 401,
      type: 'ACCOUNT_NOT_REGISTERED',
      customMessage: 'Your Account is not registered. Please sign up first!'
    }
  },
  STATUS_SUCCESS: {
    CREATED: {
      statusCode: 201,
      customMessage: 'Created Successfully',
      type: 'CREATED'
    },
    MAIL_SENT: {
      statusCode: 201,
      customMessage: 'New Password Sent Successfully',
      type: 'SENT'
    },
    DEFAULT: {
      statusCode: 200,
      customMessage: 'Success',
      type: 'DEFAULT'
    },
    UPDATED: {
      statusCode: 200,
      customMessage: 'Updated Successfully',
      type: 'UPDATED'
    },
    LOGOUT: {
      statusCode: 200,
      customMessage: 'Logged Out Successfully',
      type: 'LOGOUT'
    },
    DELETED: {
      statusCode: 200,
      customMessage: 'Deleted Successfully',
      type: 'DELETED'
    }
  },
  JWT_SECRET_KEY: 'Vvvbmhppra',
  ENCRYPTION_KEY: '62887325424d2d2375a387f2cd34a9f3', // key used to encrypt jwt payload
  ml: {
    coreURL: config.get("MLService.url")
  }
}

module.exports = variables
