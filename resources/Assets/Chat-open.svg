<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="57" height="57" viewBox="0 0 57 57">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#55c7ff"/>
      <stop offset="1" stop-color="#7886f7"/>
    </linearGradient>
    <clipPath id="clip-Close">
      <rect width="57" height="57"/>
    </clipPath>
  </defs>
  <g id="Close" clip-path="url(#clip-Close)">
    <g id="Group_2852" data-name="Group 2852" transform="translate(-2170.005 -4528)">
      <g id="Group_32" data-name="Group 32" transform="translate(2169 4527)">
        <path id="Path_287" data-name="Path 287" d="M57.005,0,52.1,14.7A28.01,28.01,0,1,1,41.044,4.2Z" transform="translate(1 1)" fill="url(#linear-gradient)"/>
      </g>
      <path id="Path_1911" data-name="Path 1911" d="M9.657,7.214,13.922,2.95A1.727,1.727,0,0,0,11.479.507L7.214,4.772,2.949.507A1.727,1.727,0,0,0,.506,2.95L4.771,7.214.506,11.479a1.727,1.727,0,0,0,2.443,2.443L7.214,9.658l4.265,4.265a1.727,1.727,0,0,0,2.443-2.443Z" transform="translate(2191 4549.999)" fill="#fff"/>
    </g>
  </g>
</svg>
