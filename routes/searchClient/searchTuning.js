/**
 * Created by harman on 27/10/16.
 */
var request = require('request');
var async = require('async');
var router = require('express').Router();
var commonFunctions = require('./../../utils/commonFunctions');
var searchClient = require('./../admin/searchClient');
var path = require("path");
var fs = require("fs");
var { spawn, exec } = require('child_process');
var searchResultKafkaConfig = require('../admin/searchResultKafkaConfig');
const excelReportGenerator = require('../analytics/excelReportGenerator');
const searchunifyEmail = require('../../Lib/email');
const analyticsMiddleware = require('../analytics/serviceMiddleware');
const emailTemplates = require('../emailTemplates');
const fastCsv = require("fast-csv");
const {decode} = require('html-entities');
const {Parser} = require("json2csv");
const { kafkaStartup } = require('../../utils/kafka/startup');
var multipart = require('connect-multiparty');
var multipartMiddleware = multipart();
var json = require('csvjson');
var sanitizeHtml = require('sanitize-html');
const moment = require('moment');
const { alertGoogleChat } = require('../../Lib/alert-gchat');
const boostingAlertUrl = config.get('gchatUrls.boostingAlert');
const boostingAlertFlag = config.get('enableBoostingAlert');
const he = require('he');
const tuningService = require('./service/tuning-service');

const requestValidator = require('./requestValidator');

function saveTuning(dataObject, searchString, search_client_id,req, callback) {
  commonFunctions.CleanRedisCache();
  let title = dataObject.title || dataObject.highlight.TitleToDisplayString[0];
  let recordId = dataObject._id || dataObject.recordId;
  let indexName = dataObject.sourceName || dataObject.index;
  let objName = dataObject.objName || dataObject.type;
  let objLabel = dataObject.objLabel || dataObject.object_label;
  let sourceLabel = dataObject.sourceLabel || dataObject.source_label;
  connection[req.headers['tenant-id']].execute.query(`SELECT * FROM keyword_boost WHERE search_string=? AND boosting_level = ? AND search_client_id=? AND deindex=0`, [searchString, dataObject.tuning.rank,search_client_id], (err, response) => {
    if (response.length == 0) {
      async.auto({
        insert_update_rows: function (cb) {
          if (dataObject.isBoosted) {
            var sqlInsert = "INSERT INTO `keyword_boost` (`id`,`search_string`, `index_name`, `index_type`, `record_id`,`boosting_level`,`search_client_id`,`isBoostingEnabled`,`title`,`href`,`object_label`,`source_label`) " +
              "VALUES (?,?, ?, ?, ?, ?, ?,?,?,?,?,?) ON DUPLICATE KEY UPDATE id=values(id),`index_name`=values(index_name), `index_type`=values(index_type), `record_id`=values(record_id),`boosting_level`=values(boosting_level),`search_client_id`=values(search_client_id),search_string=values(search_string),href=values(href),title=values(title),object_label=values(object_label),source_label=values(source_label);";
            connection[req.headers['tenant-id']].execute.query(sqlInsert, [dataObject.tuning.id, searchString, indexName, objName, recordId, dataObject.tuning.rank, search_client_id,1,title,dataObject.href,objLabel,sourceLabel], function (errInsert, rowsInsert) {
              if (errInsert) {
                commonFunctions.errorlogger.info("error while updating and inserting ", errInsert);
                cb(errInsert);
              } else {
                cb(null, rowsInsert);
              }
            });
          }
          else
            cb(null, {})
        },
        delete_tuning: ['insert_update_rows', function (dataFromAbove, cb) {
          if (!dataObject.isBoosted && dataObject.tuning.id)
            connection[req.headers['tenant-id']].execute.query("delete from keyword_boost where id=?", [dataObject.tuning.id], cb)
          else cb(null, [])
        }]
      }, function (err, result) {
        callback(null, result.insert_update_rows)
      })
    }
    else{
      if(response.length != 0){
        if(response[0].index_name == dataObject.tuning.index_name && response[0].search_client_id == dataObject.tuning.search_client_id && response[0].boosting_level == dataObject.tuning.boosting_level  ){
          callback(null, response[0]);
        }else{
          callback(null, {});
        }
      }else{
          callback(null, {});
      }

    }

  });
}

router.post("/saveTuningData", function (req, res) {
  var DataToSave = req.body.dataToSave;
  // var searchString = req.body.searchString;
  var search_client_id = req.body.search_client_id;
  var asyncTasks = [];

  for (var i = 0; i < DataToSave.length; i++) {
    var elastic_id = DataToSave[i]._id || DataToSave[i].Id || DataToSave[i].id
    /*if(DataToSave[i]["_index"]=="grazittiblogs") {
      elastic_id = elastic_id.replace("https","http")
    }*/
    var type = DataToSave[i].objName;
    var index = DataToSave[i].sourceName;
    var boosted = (DataToSave[i].isBoosted ? 1 : 0);
    var scorelevel = DataToSave[i].tuning.rank;
    var searchString = DataToSave[i].tuning.searchString;
    asyncTasks.push(saveTuning.bind(null, DataToSave[i], searchString, search_client_id,req));
  }
  async.series(asyncTasks, function (err,result) {
    let configObj = { "platformId": search_client_id };
    searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
      res.send({ "status": 200, "message": 'Successfully saves records' });
    });
  });

});

router.post("/checkDuplicateEntry", function (req, res) {
  connection[req.headers['tenant-id']].execute.query(`SELECT * FROM keyword_boost WHERE search_string=? AND boosting_level=? AND search_client_id=? AND deindex=0`, [req.body.searchString, req.body.boostValue,req.body.search_client_id], (err, response) => {
    if (!err && (response.length > 0)) {
      res.send({ "status": 201, "message": 'Already Exist' });
    }
    else
      res.send({ "status": 200, "message": 'Not Exist' });
  });
});

router.post("/updateKeywordBoostingToggle", function (req, res) {
  commonFunctions.CleanRedisCache();
  var isEnabled = (req.body.isEnabled) ? 1 : 0;
  connection[req.headers['tenant-id']].execute.query("UPDATE keyword_boost SET isBoostingEnabled=? WHERE search_client_id=?",[isEnabled,req.body.search_client_id],(err,response) => {
    if (!err) {
      let configObj = { "platformId": req.body.search_client_id };
      searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
        res.send({ "status": 201, "message": 'Saved' });
      });
    }
    else
      res.send({ "status": 200, "message": 'Not Exist' });
  })
});

router.post("/updateSynonymKeywordBoostToggle", function (req, res) {
  commonFunctions.CleanRedisCache();
  var isEnabled = (req.body.isEnabled) ? 1 : 0;
  connection[req.headers['tenant-id']].execute.query("UPDATE search_clients SET synonymKeywordBoosting=? WHERE id=?",[isEnabled,req.body.search_client_id],(err,response) => {
    if (!err) {
      let configObj = { "platformId": req.body.search_client_id };
      searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
        res.send({ "status": 201, "message": 'Saved' });
      });
    }
    else
      res.send({ "status": 200, "message": 'Not Exist' });
  })
});

router.post("/updateContentBoostingToggle", function (req, res) {
  var isContentEnabled = (req.body.isContentEnabled) ? 1 : 0;
  connection[req.headers['tenant-id']].execute.query("UPDATE search_clients_to_content_objects SET isContentBoostingEnabled=? WHERE search_client_id=?",[isContentEnabled,req.body.search_client_id],(err,response) => {
    if (!err) {
      let configObj = { "platformId": req.body.search_client_id };
      searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
        res.send({ "status": 201, "message": 'Saved' });
      });
    }
    else
      res.send({ "status": 200, "message": 'Not Exist' });
  })
});

router.post("/getTuningData", function (req, res) {
   // type 1 = boosted documents
  // type 0 = boosted query
  let search_client_id = req.body.search_client_id;
  let arrtypes = [];
  const type = req.body.type;
  let pageNo = req.body.pageNo || 0;
  let offset = req.body.offset || 10;
  let deindex = req.body.deindex || 0;
  let searchKeyword = '';
  let fieldToSearch = '';
  if(req.body.searchString){
    searchKeyword = req.body.searchString;
    fieldToSearch = req.body.fieldToSearch;
  }
  const sessionKeyword = req.body.sessionKeyword.data;
  let deletedDocs = req.body.sessionKeyword.deleteBoostedDocs || [];
  let deletedDocsBoostedQueryIds = req.body.sessionKeyword.deleteBoostedQueryIds;
  let newDocs = 0;
  let newOffset = 0;
  let newLimit = 10;
  let actualLimit = 10;
  let actualPageNumber = 1;
  let actualOffset = 0;
  let newDocuments = [];
  let updatedDocuments = [];
  let deletedDocuments = [];

  let actualNewDocumentsBoostQuery = [];
  let newDocumentsBoostQuery = [];
  let documentIdsBoostQuery = [];

  sessionKeyword.map(item => {
    if (item.event === 'new') {
      newDocuments.push(item);
      newDocs++;
    } else {
      updatedDocuments.push(item);
    }
  });
  if(type){
    if(searchKeyword && fieldToSearch == "title"){
      updatedDocuments = updatedDocuments.filter(u => u.title.toLowerCase().includes(searchKeyword.toLowerCase()));
    }
    if(searchKeyword && fieldToSearch == "search_string"){
      updatedDocuments = updatedDocuments.filter(u =>  {
        const temp = u.keywordValues.filter(v => v.searchString.toLowerCase().includes(searchKeyword.toLowerCase()));
        if (temp.length > 0) {
            return u;
        }
        if(temp.length == 0){
          deletedDocuments.push(u.recordId)
        }
    });
    }
    if(searchKeyword && fieldToSearch == "title"){
      newDocuments = newDocuments.filter(u => u.title.toLowerCase().includes(searchKeyword.toLowerCase()));
    }
    if(searchKeyword && fieldToSearch == "search_string"){
      newDocuments = newDocuments.filter(u =>  {
        const temp = u.keywordValues.filter(v => v.searchString.toLowerCase().includes(searchKeyword.toLowerCase()));
        if (temp.length > 0) {
            return u;
        }
    });
    }
  }
  if (deletedDocs && deletedDocs.length > 0) {
    deletedDocs.map(item => {
      deletedDocuments.push(item.recordId);
    });
  }
  let factor = pageNo;
  if (offset === 20) {
    factor = pageNo <= 10 ? pageNo * Math.ceil(offset / pageNo) : (pageNo * Math.ceil(offset / pageNo)) + pageNo;
  } else if (offset === 30) {
    if (pageNo <= 10) {
      factor = pageNo * Math.ceil(offset / pageNo);
    } else if (pageNo > 10 && pageNo <= 20) {
      factor = pageNo * Math.ceil(offset / pageNo) + pageNo;

    } else {
      factor = pageNo * Math.ceil(offset / pageNo) + (pageNo * 2);
    }
  }
  actualOffset = pageNo === 0 ? pageNo : factor;
  actualLimit = offset;
  actualPageNumber = Math.ceil(actualOffset / actualLimit + 1);
  if (type) {
    newLimit = actualLimit;
    if ((actualLimit * actualPageNumber) - newDocs > 0) {
      if (Math.ceil(newDocs / actualLimit) >= actualPageNumber) {
        newLimit = newLimit - Math.ceil(newDocs % actualLimit) > 0 ? newLimit - (newDocs % actualLimit) : 0;
      }
    } else {
      if (Math.ceil(newDocs / actualLimit) >= actualPageNumber) {
        newLimit = newLimit - Math.ceil(newDocs % actualLimit) > 0 ? 0 : newLimit - (newDocs % actualLimit);
      }
    }
    newOffset = actualOffset;
    if (Math.floor(newDocs / actualOffset) <= actualPageNumber) {
      const tempVal = (actualLimit * (actualPageNumber - 1)) - newDocs;
      newOffset = tempVal > 0 ? tempVal : 0;
    }
  } else {
    // convert session boosted documents data into boosted query
    documentIdsBoostQuery = [];
    sessionKeyword.map(nD => {
      const otherIndex = documentIdsBoostQuery.findIndex(o => o.recordId === nD.recordId);
      nD.keywordValues.map(kv => {
        if (true) {
          const index = actualNewDocumentsBoostQuery.findIndex(o => o.searchString.toLowerCase() === kv.searchString.toLowerCase());
          let data = {
            title: nD.title,
            index: nD.index,
            type: nD.type,
            href: nD.href,
            isBoostingEnabled: nD.isBoostingEnabled,
            object_label: nD.object_label,
            source_label: nD.source_label,
            last_state: nD.last_state,
            recordId: nD.recordId,
            keywordValues: nD.keywordValues,
            queryToSend: kv
          }
          if (index === -1) {
            actualNewDocumentsBoostQuery.push({
              searchString: kv.searchString,
              document: [data]
            });
          } else {
            actualNewDocumentsBoostQuery[index].document.push(data);
          }
        }
      });
      if (otherIndex > -1) {
        documentIdsBoostQuery[otherIndex].documents = nD.keywordValues;
      } else {
        documentIdsBoostQuery.push({
          recordId: nD.recordId,
          searchData: nD.keywordValues
        });
      }
    });
    newDocumentsBoostQuery = actualNewDocumentsBoostQuery;
    if(searchKeyword && fieldToSearch == "title"){
      newDocumentsBoostQuery = newDocumentsBoostQuery.filter((u, j) => {
        let docs = [];
        u.document.map((v, i) => {
          if (v.title.toLowerCase().includes(searchKeyword.toLowerCase())) {
            docs.push(u.document[i]);
          }
        });
        u.document = docs;
        return u.document.length > 0;
      });
    }
    if(searchKeyword && fieldToSearch == "search_string"){
      newDocumentsBoostQuery = newDocumentsBoostQuery.filter(u => u.searchString.toLowerCase().includes(searchKeyword.toLowerCase()));
    }
    newLimit = req.body.offset;
  }

  async.auto({
    get_search_Client_id: cb => {
      connection[req.headers['tenant-id']].execute.query(`SELECT * FROM search_clients WHERE id=?`, [search_client_id], cb);
    },
    get_search_client_settings: (cb) => {
      searchClient.getSearchClient(search_client_id,req,cb)
    },
    get_diplay_fields: ['get_search_Client_id', (dataFromAbove, cb) => {

      commonFunctions.getDisplayFields(dataFromAbove.get_search_Client_id[0][0].uid,req, function (displayRows) {
        commonFunctions.errorlogger.warn("here in get_display_fields ")

        var rowsDisplay = displayRows
        for (var iy = 0; iy < rowsDisplay.length; iy++) {
          if (rowsDisplay[iy]["display_field_name"] == "href") {
            href = rowsDisplay[iy]["elastic_field_name"];
          }
        }

      const unique = [...new Set(rowsDisplay.map(item => item.elasticIndexName+"@___@"+item.elastic_object_name))];
        unique.forEach(x => {
          arrtypes.push(
            {
              "index":x.split('@___@')[0],
              "type": x.split('@___@')[1],
              "values": rowsDisplay.filter(z => { if (z.elasticIndexName == x.split('@___@')[0] && z.elastic_object_name == x.split('@___@')[1] && z.display_field_name != "Search") return z }).sort(function (a, b) {
                return a.search_priority - b.search_priority;
              }).map(y => { return ({ "fieldName": y.elastic_field_name, "Displaytype": y.display_field_name, "search_priority": y.search_priority }) })
            }
          )
        })

        cb(null, displayRows)
      })
    }],
    get_boost_data: cb => {
      let querySql;
      if (type) {
        querySql = `SELECT *, GROUP_CONCAT(search_string,'@-~@',boosting_level,'@-~@',id ,'@-~@',bypass_filter SEPARATOR '*-*') as keywords FROM keyword_boost where search_client_id=? AND deindex=?`
        if (deletedDocuments && deletedDocuments.length > 0) {
          var deleteData = "";
          deletedDocuments.map((o, i) => {
            o = JSON.stringify(o);
            if (deletedDocuments.length - 1 == i) {
              deleteData = deleteData.concat(o);
            } else {
              deleteData = deleteData.concat(o, ",");
            }
          });
          deleteData = "(" + deleteData + ")";
          querySql += ` AND record_id NOT IN ${deleteData}`;
        }
        let queryParams;
        if (searchKeyword) {
          querySql += ` AND ${fieldToSearch} LIKE ? GROUP by record_id LIMIT ?,?`;
          queryParams = [search_client_id, deindex, '%' + searchKeyword + '%', newOffset, newLimit]
        } else {
          querySql += ` GROUP by record_id LIMIT ?,?`;
          queryParams = [search_client_id, deindex, newOffset, newLimit]
        }
        connection[req.headers['tenant-id']].execute.query(querySql, queryParams, (err, records) => {
          cb(null, { records });
        });
      } else {
        querySql = `SELECT *, GROUP_CONCAT(
                IFNULL(title,href) ,'@-~@',
                index_name,'@-~@',
                index_type,'@-~@',
                href,'@-~@',
                isBoostingEnabled,'@-~@',
                object_label,'@-~@',
                source_label,'@-~@',
                last_state,'@-~@',
                record_id,'@-~@',
                boosting_level,'@-~@',
                search_string,'@-~@',
                id ,'@-~@',
                bypass_filter SEPARATOR '*-*'
              ) as documents FROM keyword_boost where search_client_id=? AND deindex=?`
        if (deletedDocuments && deletedDocuments.length > 0) {
          var deleteData = "";
          deletedDocuments.map((o, i) => {
            o = JSON.stringify(o);
            if (deletedDocuments.length - 1 == i) {
              deleteData = deleteData.concat(o);
            } else {
              deleteData = deleteData.concat(o, ",");
            }
          });
          deleteData = "(" + deleteData + ")";
          querySql += ` AND record_id NOT IN ${deleteData}`;
        } 
        if (newDocumentsBoostQuery && newDocumentsBoostQuery.length > 0) {
          var sessionSearchString = "";
          newDocumentsBoostQuery.map((o, i) => {
            let searchS = o.searchString
            searchS = JSON.stringify(searchS);
            if (newDocumentsBoostQuery.length - 1 == i) {
              sessionSearchString = sessionSearchString.concat(searchS);
            } else {
              sessionSearchString = sessionSearchString.concat(searchS, ",");
            }
          });
          sessionSearchString = "(" + sessionSearchString + ")";
          querySql += ` AND search_string IN ${sessionSearchString}`;
        } else {
          querySql += ` AND search_string IN ('')`;
        }
        querySql += ` GROUP by search_string`;
        connection[req.headers['tenant-id']].execute.query(querySql, [search_client_id, deindex], (err, records) => {
          if (err) {
            commonFunctions.errorlogger.error("Error in fetching search clinets");
          }
          // update session data with DB
          // get record of all search string
          let storeRecordId = []
          let boostedQueryDb = [];
          if (records) {
            for (let i = 0; i < records.length; i++) {
              let documentsData = records[i].documents.split("*-*");
              documentsData.forEach(k => {
                let keywordSplitValue = k.split("@-~@");
                let data = {
                  db : true,
                  title: keywordSplitValue[0],
                  index: keywordSplitValue[1],
                  type: keywordSplitValue[2],
                  href: keywordSplitValue[3],
                  isBoostingEnabled: keywordSplitValue[4],
                  object_label: keywordSplitValue[5],
                  source_label: keywordSplitValue[6],
                  last_state: keywordSplitValue[7],
                  recordId: keywordSplitValue[8],
                  keywordValues: [{
                    "searchString": keywordSplitValue[10],
                    "rank": keywordSplitValue[9],
                    "id": keywordSplitValue[11],
                    "bypass_filter":keywordSplitValue[12]
                  }],
                  queryToSend: {
                    "searchString": keywordSplitValue[10],
                    "rank": keywordSplitValue[9],
                    "id": keywordSplitValue[11],
                    "bypass_filter":keywordSplitValue[12]
                  }
                }
                storeRecordId.push(data.recordId)
                boostedQueryDb.push(data);
              });
            }
            deletedDocsBoostedQueryIds.map(id => {
              const indx = boostedQueryDb.findIndex(i => i.recordId == id.recId && i.queryToSend.searchString == id.searchString);
              if (indx != -1) {
                boostedQueryDb.splice(indx, 1)
              }
            })

          }
          // get data from record id
          let querySqlStoreIds = `SELECT *, GROUP_CONCAT(search_string,'@-~@',boosting_level,'@-~@',id ,'@-~@',
          bypass_filter SEPARATOR '*-*') as keywords FROM keyword_boost where search_client_id=? AND deindex=?`
          if (storeRecordId && storeRecordId.length > 0) {
            let recordIdsBoostedDocs = "";
            storeRecordId.map((o, i) => {
              o = JSON.stringify(o);
              if (storeRecordId.length - 1 == i) {
                recordIdsBoostedDocs = recordIdsBoostedDocs.concat(o);
              } else {
                recordIdsBoostedDocs = recordIdsBoostedDocs.concat(o, ",");
              }
            });
            recordIdsBoostedDocs = "(" + recordIdsBoostedDocs + ")";
            querySqlStoreIds += ` AND record_id IN ${recordIdsBoostedDocs}`;
          }
          querySqlStoreIds += ` GROUP by record_id`;
          connection[req.headers['tenant-id']].execute.query(querySqlStoreIds, [search_client_id, deindex], (err, recordsForIds) => {
            let keywordArray = [];
            for (let index = 0; index < recordsForIds.length; index++) {
              let keywordsData = recordsForIds[index].keywords.split("*-*");
              let finalData = {
                "recordId": "",
                "keywordstring": []
              }
              keywordsData.forEach(k => {
                let keywordSplitValue = k.split("@-~@");
                let data = {
                  searchString: keywordSplitValue[0],
                  rank: keywordSplitValue[1],
                  id: keywordSplitValue[2],
                  bypass_filter:keywordSplitValue[3]
                }
                finalData.keywordstring.push(data);
              });
              finalData.recordId = recordsForIds[index].record_id;
              keywordArray.push(finalData);
            }
            keywordArray.map(kA => {
              const docI = boostedQueryDb.findIndex(doi => doi.recordId == kA.recordId);
              if (docI != -1) {
                boostedQueryDb[docI].keywordValues = kA.keywordstring;
              }
            })
            // finally update boosted query session data with db
            boostedQueryDb.map(item => {
              const index = newDocumentsBoostQuery.findIndex(bQ => bQ.searchString == item.queryToSend.searchString);
              if (index != -1) {
                const docIndex = newDocumentsBoostQuery[index].document.findIndex(doc => doc.recordId == item.recordId);
                if (docIndex == -1) {
                  newDocumentsBoostQuery[index].document.push(item)
                }
              }
            })

            // --- start
            // logic for offset, pageno, pagination and handle how much data is come from session and db
            let backUpOffset = offset;
            let from = (actualPageNumber - 1) * offset
            let dbFrom = 0;
            let recordIds = [];
            let afterSplice = [];
            let restDocs = 0;
            if (newDocumentsBoostQuery.length > 0) {
              let boostedDocsSession = JSON.parse(JSON.stringify(newDocumentsBoostQuery));
              afterSplice = boostedDocsSession.splice(from, offset);
              if (afterSplice.length == offset) {
                recordIds = afterSplice
                cb(null, { records, recordIds, newDocumentsBoostQuery });
              } else {
                if (afterSplice.length != offset) {
                  restDocs = offset - afterSplice.length;
                }
                let resPage = Math.floor(newDocumentsBoostQuery.length / offset) + 1;
                let docsPresentSession = newDocumentsBoostQuery.length % offset;
                let left = offset - docsPresentSession;
                if (docsPresentSession == 0 && left > offset) {
                  dbFrom = ((actualPageNumber - (resPage)) * offset) + left
                } else if (docsPresentSession == 0 && left <= offset) {
                  dbFrom = ((actualPageNumber - (resPage)) * offset);
                } else if (docsPresentSession != 0 && left > offset) {
                  dbFrom = ((actualPageNumber - (resPage + 1)) * offset + left);
                } else if (docsPresentSession != 0 && left <= offset) {
                  if (restDocs != offset) {
                    dbFrom = ((actualPageNumber - (resPage)) * offset);
                  } else if (restDocs == offset) {
                    dbFrom = ((actualPageNumber - (resPage + 1)) * offset + left);
                  }
                }
                offset = restDocs;
              }
            } else {
              dbFrom = from;
            }
            // --end
            // get data from db 
            if (newDocumentsBoostQuery.length == 0 || afterSplice.length != backUpOffset) {
              let querySqlDb;
              querySqlDb = `SELECT *, GROUP_CONCAT(
                   IFNULL(title,href) ,'@-~@',
                   index_name,'@-~@',
                   index_type,'@-~@',
                   href,'@-~@',
                   isBoostingEnabled,'@-~@',
                   object_label,'@-~@',
                   source_label,'@-~@',
                   last_state,'@-~@',
                   record_id,'@-~@',
                   boosting_level,'@-~@',
                   search_string,'@-~@',
                   id ,'@-~@',
                   bypass_filter SEPARATOR '*-*'
                 ) as documents FROM keyword_boost where search_client_id=? AND deindex=?`;
              if (deletedDocuments && deletedDocuments.length > 0) {
                var deleteData = "";
                deletedDocuments.map((o, i) => {
                  o = JSON.stringify(o);
                  if (deletedDocuments.length - 1 == i) {
                    if (deletedDocsBoostedQueryIds && deletedDocsBoostedQueryIds.length > 0) {
                      deleteData = deleteData.concat(o, ",");
                    } else {
                      deleteData = deleteData.concat(o);
                    }
                  } else {
                    deleteData = deleteData.concat(o, ",");
                  }
                });
                if (deletedDocsBoostedQueryIds && deletedDocsBoostedQueryIds.length > 0) {
                  var deleteDocsBoostQuery = "";
                  deletedDocsBoostedQueryIds.map((o, i) => {
                    let recordId = o.recId
                    recordId = JSON.stringify(recordId);
                    if (deletedDocsBoostedQueryIds.length - 1 == i) {
                      deleteDocsBoostQuery = deleteDocsBoostQuery.concat(recordId);
                    } else {
                      deleteDocsBoostQuery = deleteDocsBoostQuery.concat(recordId, ",");
                    }
                  });
                  deleteData = deleteData.concat(deleteDocsBoostQuery);
                }
                deleteData = "(" + deleteData + ")";
                querySqlDb += ` AND record_id NOT IN ${deleteData}`;
              } else {
                if (deletedDocsBoostedQueryIds && deletedDocsBoostedQueryIds.length > 0) {
                  var deleteDocsBoostQuery = "";
                  deletedDocsBoostedQueryIds.map((o, i) => {
                    let recordId = o.recId
                    recordId = JSON.stringify(recordId);
                    if (deletedDocsBoostedQueryIds.length - 1 == i) {
                      deleteDocsBoostQuery = deleteDocsBoostQuery.concat(recordId);
                    } else {
                      deleteDocsBoostQuery = deleteDocsBoostQuery.concat(recordId, ",");
                    }
                  });
                  deleteDocsBoostQuery = "(" + deleteDocsBoostQuery + ")";
                  querySqlDb += ` AND record_id NOT IN ${deleteDocsBoostQuery}`;
                }
              }
              if (newDocumentsBoostQuery && newDocumentsBoostQuery.length > 0) {
                var sessionSearchString = "";
                newDocumentsBoostQuery.map((o, i) => {
                  let searchS = o.searchString
                  searchS = JSON.stringify(searchS);
                  if (newDocumentsBoostQuery.length - 1 == i) {
                    sessionSearchString = sessionSearchString.concat(searchS);
                  } else {
                    sessionSearchString = sessionSearchString.concat(searchS, ",");
                  }
                });
                sessionSearchString = "(" + sessionSearchString + ")";
                querySqlDb += ` AND search_string NOT IN ${sessionSearchString}`;
              }
              let queryParams;
              if (searchKeyword) {
                querySqlDb += ` AND ${fieldToSearch} LIKE ? GROUP by search_string Limit ? ,?`;
                queryParams = [search_client_id, deindex, '%' + searchKeyword + '%',dbFrom, offset]
              } else {
                querySqlDb += ` GROUP by search_string Limit ? ,?`;
                queryParams = [search_client_id, deindex, dbFrom, offset]
              }
              connection[req.headers['tenant-id']].execute.query(querySqlDb, queryParams, (err, recordIds) => {
                let boostedQueryDbNew = [];
                let boostedQuerykeywordIds = [];
                for (let i = 0; i < recordIds.length; i++) {
                  let documentsData = recordIds[i].documents.split("*-*");
                  let data = {
                    "searchString": "",
                    "document": []
                  }
                  documentsData.forEach(k => {
                    let keywordSplitValue = k.split("@-~@");
                    let docsData = {
                      db: true,
                      title: keywordSplitValue[0],
                      index: keywordSplitValue[1],
                      type: keywordSplitValue[2],
                      href: keywordSplitValue[3],
                      isBoostingEnabled: keywordSplitValue[4],
                      object_label: keywordSplitValue[5],
                      source_label: keywordSplitValue[6],
                      last_state: keywordSplitValue[7],
                      recordId: keywordSplitValue[8],
                      keywordValues: [{
                        "searchString": keywordSplitValue[10],
                        "rank": keywordSplitValue[9],
                        "id": keywordSplitValue[11],
                        "bypass_filter":keywordSplitValue[12]
                      }],
                      queryToSend: {
                        "searchString": keywordSplitValue[10],
                        "rank": keywordSplitValue[9],
                        "id": keywordSplitValue[11],
                        "bypass_filter":keywordSplitValue[12]
                      }
                    }
                    boostedQuerykeywordIds.push(docsData.recordId)
                    data.document.push(docsData);
                  });
                  data.searchString = recordIds[i].search_string
                  boostedQueryDbNew.push(data)
                }

                // update keywordValues 
                let querySqlBoosted = `SELECT *, GROUP_CONCAT(search_string,'@-~@',boosting_level,'@-~@',id ,'@-~@',
                bypass_filter SEPARATOR '*-*') as keywords FROM keyword_boost where search_client_id=? AND deindex=?`
                if (boostedQuerykeywordIds && boostedQuerykeywordIds.length > 0) {
                  var recordIdsBoostedDocs = "";
                  boostedQuerykeywordIds.map((o, i) => {
                    o = JSON.stringify(o);
                    if (boostedQuerykeywordIds.length - 1 == i) {
                      recordIdsBoostedDocs = recordIdsBoostedDocs.concat(o);
                    } else {
                      recordIdsBoostedDocs = recordIdsBoostedDocs.concat(o, ",");
                    }
                  });
                  recordIdsBoostedDocs = "(" + recordIdsBoostedDocs + ")";
                  querySqlBoosted += ` AND record_id IN ${recordIdsBoostedDocs}`;
                }
                querySqlBoosted += ` GROUP by record_id`;
                connection[req.headers['tenant-id']].execute.query(querySqlBoosted, [search_client_id, deindex], (err, resultRow) => {
                  let keywordArray = [];
                  for (let index = 0; index < resultRow.length; index++) {
                    let keywordsData = resultRow[index].keywords.split("*-*");
                    finalData = {
                      "recordId": "",
                      "keywordstring": []
                    }
                    keywordsData.forEach(k => {
                      let keywordSplitValue = k.split("@-~@");
                      let data = {
                        searchString: keywordSplitValue[0],
                        rank: keywordSplitValue[1],
                        id: keywordSplitValue[2],
                        bypass_filter:keywordSplitValue[3]
                      }
                      finalData.keywordstring.push(data);
                    });
                    finalData.recordId = resultRow[index].record_id;
                    keywordArray.push(finalData);
                  }
                  keywordArray.map(kA => {
                    boostedQueryDbNew.map(i => {
                      const docI = i.document.findIndex(doi => doi.recordId == kA.recordId);
                      if (docI != -1) {
                        i.document[docI].keywordValues = kA.keywordstring;
                      }
                    })
                  })

                  recordIds = boostedQueryDbNew
                  if (afterSplice.length > 0) {
                    recordIds = afterSplice.concat(recordIds)
                  }
                  cb(null, { records, recordIds, newDocumentsBoostQuery });
                });
              });
            }else{
              let records = [];
              let recordIds = [];
              let newDocumentsBoostQuery = [];
              cb(null, { records, recordIds, newDocumentsBoostQuery });
            }
          })
        });
     }
    },
    getTotalDocumentCount: ['get_boost_data', function (dataFromAbove, cb) {
      let querySql;    
      if (type) {
        querySql = `SELECT COUNT(1), GROUP_CONCAT(search_string,'@-~@',boosting_level,'@-~@',id,'@-@',bypass_filter SEPARATOR '*-*') as keywords FROM keyword_boost where search_client_id=? AND deindex=?`;
        if (deletedDocuments && deletedDocuments.length > 0) {
          var deleteData = "";
          deletedDocuments.map((o, i) => {
            o = JSON.stringify(o);
            if (deletedDocuments.length - 1 == i) {
              deleteData = deleteData.concat(o);
            } else {
              deleteData = deleteData.concat(o, ",");
            }
          });
          deleteData = "(" + deleteData + ")";
          querySql += ` AND record_id NOT IN ${deleteData}`;
        }
        let queryParams;
        if (searchKeyword) {
          querySql += ` AND ${fieldToSearch} LIKE ? GROUP by record_id`;
          queryParams = [search_client_id, deindex, '%' + searchKeyword + '%']
        } else {
          querySql += ` GROUP by record_id`;
          queryParams = [search_client_id, deindex]
        }
        connection[req.headers['tenant-id']].execute.query(querySql, queryParams, cb);
      } else {
        querySql = `SELECT COUNT(1), GROUP_CONCAT(
                title,'@-~@',
                index_name,'@-~@',
                index_type,'@-~@',
                href,'@-~@',
                isBoostingEnabled,'@-~@',
                object_label,'@-~@',
                source_label,'@-~@',
                last_state,'@-~@',
                record_id,'@-~@',
                boosting_level,'@-~@',
                search_string,'@-~@',
                id ,'@-@',
                bypass_filter SEPARATOR '*-*'
              ) as documents FROM keyword_boost where search_client_id=? AND deindex=?`
        if (deletedDocuments && deletedDocuments.length > 0) {
          var deleteData = "";
          deletedDocuments.map((o, i) => {
            o = JSON.stringify(o);
            if (deletedDocuments.length - 1 == i) {
              if (deletedDocsBoostedQueryIds && deletedDocsBoostedQueryIds.length > 0) {
                deleteData = deleteData.concat(o, ",");
              } else {
                deleteData = deleteData.concat(o);
              }
            } else {
              deleteData = deleteData.concat(o, ",");
            }
          });
          if (deletedDocsBoostedQueryIds && deletedDocsBoostedQueryIds.length > 0) {
            var deleteDocsBoostQuery = "";
            deletedDocsBoostedQueryIds.map((o, i) => {
              let recordId = o.recId
              recordId = JSON.stringify(recordId);
              if (deletedDocsBoostedQueryIds.length - 1 == i) {
                deleteDocsBoostQuery = deleteDocsBoostQuery.concat(recordId);
              } else {
                deleteDocsBoostQuery = deleteDocsBoostQuery.concat(recordId, ",");
              }
            });
            deleteData = deleteData.concat(deleteDocsBoostQuery);
          }
          deleteData = "(" + deleteData + ")";
          querySql += ` AND record_id NOT IN ${deleteData}`;
        } else {
          if (deletedDocsBoostedQueryIds && deletedDocsBoostedQueryIds.length > 0) {
            var deleteDocsBoostQuery = "";
            deletedDocsBoostedQueryIds.map((o, i) => {
              let recordId = o.recId
              recordId = JSON.stringify(recordId);
              if (deletedDocsBoostedQueryIds.length - 1 == i) {
                deleteDocsBoostQuery = deleteDocsBoostQuery.concat(recordId);
              } else {
                deleteDocsBoostQuery = deleteDocsBoostQuery.concat(recordId, ",");
              }
            });
            deleteDocsBoostQuery = "(" + deleteDocsBoostQuery + ")";
            querySql += ` AND record_id NOT IN ${deleteDocsBoostQuery}`;
          }
        }
        if (dataFromAbove.get_boost_data.newDocumentsBoostQuery && dataFromAbove.get_boost_data.newDocumentsBoostQuery.length > 0) {
          var sessionSearchString = "";
          dataFromAbove.get_boost_data.newDocumentsBoostQuery.map((o, i) => {
            let searchS = o.searchString
            searchS = JSON.stringify(searchS);
            if (dataFromAbove.get_boost_data.newDocumentsBoostQuery.length - 1 == i) {
              sessionSearchString = sessionSearchString.concat(searchS);
            } else {
              sessionSearchString = sessionSearchString.concat(searchS, ",");
            }
          });
          sessionSearchString = "(" + sessionSearchString + ")";
          querySql += ` AND search_string NOT IN ${sessionSearchString}`;
        }
        let queryParams;
        if (searchKeyword) {
          querySql += ` AND ${fieldToSearch} LIKE ? GROUP by search_string`;
          queryParams = [search_client_id, deindex, '%' + searchKeyword + '%']
        } else {
          querySql += ` GROUP by search_string`;
          queryParams = [search_client_id, deindex]
        }
        connection[req.headers['tenant-id']].execute.query(querySql, queryParams, cb);
     }
    }],
  }, (error, results) => {
    var totalNumberOfDocs = results.getTotalDocumentCount[0].length;
    let boostDoc = results.get_boost_data.records;
    let boostDocWithRecords = results.get_boost_data.recordIds;
    async.parallelLimit(boostDoc.map(r => {
      return cb => {
        if(!r.title){
          let options = {
            method: 'GET',
            url: `http://${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/${encodeURIComponent(r.index_name)}/${encodeURIComponent(r.index_type)}/${encodeURIComponent(r.record_id)}`,
            headers: { 'content-type': 'application/json' },
            json: true
          };
          request(options, (error, response, body) => {
            commonFunctions.errorlogger.info(`http://${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/${r.index_name}/${r.index_type}/${r.record_id}`, body);
  
            if (body && body.found) {
              var typeData = arrtypes.find(x => x.type == body["_type"])
              r.url = body._source.view_href;
              var titleFieldName = typeData["values"].find(x => { if (x["Displaytype"] == "Title") return x }) ? typeData["values"].find(x => { if (x["Displaytype"] == "Title") return x })["fieldName"] : "_id"
              r.subject = body._source[titleFieldName] ? body._source[titleFieldName] : body._source.title
  
              //logic for href----------------**********************************
              r.url = results.get_search_client_settings.sources.filter(x => x.enabled).map(y => { let o = y.objects.filter(z => z.enabled); return o }).reduce(commonFunctions.reduceObject).find(x => x.name == body["_type"]).base_href
              var testRegex = /{{.*?}}/g;
              var str = r.url;
              var m;
              while ((m = testRegex.exec(str)) !== null) {
                // This is necessary to avoid infinite loops with zero-width matches
                if (m.index === testRegex.lastIndex) {
                  testRegex.lastIndex++;
                }
                m.forEach((match, groupIndex) => {
                  r.url = r.url.replace(match, body["_source"][match.replace("{{", "").replace("}}", "")])
                  commonFunctions.errorlogger.info(match);
                });
              }
              if (!r.url)
                r.url = body["_id"] || body["_source"]['id'];
  
              r.keywordArrays = r.keywords
            }
            else {
              r.url = "";
              r.recordId = r.record_id;
              r.subject = "Document Not Found"
              r.keywordArrays = r.keywords
  
            }
            cb(null, r);
          });
        }else{
          cb(null,r)
        }
      }
    }), 3, (error, results) => {
      let keywordBoostingArray = [];
      if (type) {
        for (let i = 0; i < results.length; i++) {
          let data = {
            title: results[i].title || results[i].subject,
            index: results[i].index_name,
            type: results[i].index_type,
            recordId: results[i].record_id,
            href: results[i].href,
            isBoostingEnabled: results[i].isBoostingEnabled,
            object_label: results[i].object_label,
            source_label: results[i].source_label,
            last_state: results[i].last_state,
            db: true
          }

          let keywordsData = results[i].keywords.split("*-*");
          let keywordArray = [];
          keywordsData.forEach(k => {
            let keywordSplitValue = k.split("@-~@");
            let data = {
              searchString:keywordSplitValue[0],
              rank:keywordSplitValue[1],
              id:keywordSplitValue[2],
              bypass_filter:keywordSplitValue[3]
            }
            keywordArray.push(data);
          });
          data.keywordValues = [...keywordArray];

          // push the data to the array
          keywordBoostingArray.push(data);
        }

        // if(req.body.csv == 1){
        } else {
          keywordBoostingArray = boostDocWithRecords
        }
        if (req.body.csv == 1) {  
        let keywordCsvData = []
        if(keywordBoostingArray.length){
          keywordBoostingArray.forEach((kB)=>{
            kB.keywordValues.forEach((k)=>{
              keywordCsvData.push({
                "Keyword":k.searchString,
                "Rank":k.rank,
                "Title":kB.title,
                "Id":kB.recordId,
                "Url":kB.href,
                "IndexName": kB.index,
                "IndexLabel": kB.source_label,
                "ObjectLabel": kB.object_label,
                "ObjectName": kB.type,
                "SearchClientId":req.body.search_client_id,
                "LastState":kB.last_state,
                "BypassFilter":k.bypass_filter
              })
            })
        })
        }else{
          keywordCsvData.push({});
        }

        const Fields = ["Keyword","Rank","Title","Url","Id","IndexName", "IndexLabel", "ObjectName", "ObjectLabel","SearchClientId","LastState","BypassFilter"]
        const opts = { Fields };
        const parser = new Parser(opts);
        const csv = parser.parse(keywordCsvData);
        res.attachment('keywords.csv');
        res.send(csv);
      } else {
        // Merging new temporary boosting documents with documents from database
        if (type) {
          if (updatedDocuments.length > 0) {
            updatedDocuments.map(item => {
              const index = keywordBoostingArray.findIndex(o => (o.recordId === item.recordId || o.recordId === item.Id || o.recordId === item._id));
              if (index !== -1) {
                keywordBoostingArray[index] = item;
              }
            });
          }
        }
        if (type) {
          if (keywordBoostingArray.length < actualLimit) {
            fromIndex = 0;
            toIndex = actualLimit;
            if (Math.ceil((parseInt(totalNumberOfDocs) + parseInt(newDocs)) / parseInt(actualLimit)) > parseInt(actualPageNumber)) {
              if (newDocs !== 0 && newDocs > actualLimit) {
                fromIndex = actualLimit * (actualPageNumber - 1);
                const slicedArray = newDocuments.splice(fromIndex, toIndex);
                keywordBoostingArray = [...slicedArray, ...keywordBoostingArray];
              } else {
                keywordBoostingArray = [...newDocuments, ...keywordBoostingArray];
              }
            } else {
              if (Math.ceil((parseInt(totalNumberOfDocs) + parseInt(newDocs)) / parseInt(actualLimit)) == parseInt(actualPageNumber)) {
                if (newDocs !== 0 && newDocs > actualLimit) {
                  fromIndex = actualLimit * (actualPageNumber - 1);
                  toIndex = Math.ceil(newDocs / actualLimit);
                  const slicedArray = newDocuments.splice(fromIndex, toIndex);
                  keywordBoostingArray = [...slicedArray, ...keywordBoostingArray];
                } else {
                  if (((Math.floor((parseInt(totalNumberOfDocs) + parseInt(newDocs)) / parseInt(actualLimit)) == parseInt(actualPageNumber))) || parseInt(actualPageNumber) === 1) {
                    keywordBoostingArray = [...newDocuments, ...keywordBoostingArray];
                  }
                }
              }
            }
          }
        }
        res.send(error ||
        {
          totalDocs: type == true ? totalNumberOfDocs + newDocs : totalNumberOfDocs + newDocumentsBoostQuery.length,
          keywordBoostingArray,
          dbRecord: totalNumberOfDocs,
          maxKeywordLength : config.get('searchTunning.maxKeywordLength')
        }
        );
      }
    });
  });
});

router.post("/getContentTuningData", function (req, res){
  let search_client_id = req.body.search_client_id;
  let flag = req.body.flag 
  connection[req.headers['tenant-id']].execute.query('SELECT * FROM search_clients_to_content_objects WHERE search_client_id = ?',[search_client_id],(error,response) =>{
    if (error)
      commonFunctions.errorlogger.error("error", error);
    else{
      if(flag){
        const responseData = {
          data: response,
          boostingAlertFlag: boostingAlertFlag
        };
        res.send(responseData);
      }else{
        res.send(response);
      }
    }
  })
});

router.post("/deleteTuningData", function (req, res) {
  commonFunctions.CleanRedisCache();
  connection[req.headers['tenant-id']].execute.query("DELETE FROM `keyword_boost` WHERE `keyword_boost`.`id` = ?", [req.query.id], (error, resp) => {
    if (error)
      commonFunctions.errorlogger.error("error", error);
    else{
      let configObj = { "platformId": req.query.search_client_id };
      searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
        res.send(resp);
      });
   }
  })
});

router.post("/deleteDeindexedData", async (req, res) => {
  try {
    commonFunctions.CleanRedisCache();
    const savedDocs = await tuningService.boostingDocs.saveDocs(
      req,
      { searchClientId : req.body.search_client_id },
      [],
      { dData : [ { recordId : req.body.id }] }
    );
    const sql = "DELETE FROM `keyword_boost` WHERE `keyword_boost`.`record_id` = ?";
    await new Promise((resolve, reject) => {
      connection[req.headers['tenant-id']].execute.query(sql, [req.body.id], (error, resp) => {
        if (error) reject(error);
        else {
          resolve(resp);
        }
      })
    });
    let configObj = { "platformId": req.body.search_client_id, "savedDocs" : savedDocs };
    const scResponse = await new Promise((resolve, reject) => {
      searchResultKafkaConfig
        .getSearchClientSettingsViaKafka(configObj, req, (err, response) => {
        if (err) {
          reject(err);
        } else {
          resolve(response);
        }
      });
    });
    res.status(200).send(scResponse);
  } catch (error) {
    commonFunctions.errorlogger.error("error", error);
    res.status(500).send({ statusCode: 500, message : "Could not delete the de-indexed Doc" });
  }
});

// router.get("/getUnrankedSearchResults", function (req, res) {
//   let dataString = {
//     "from": 0,
//     "size": 0,
//     "query": { "bool": { "must": [{ "term": { "isClicked": true } }], "must_not": { "term": { "page_no": 1 } } } },
//     "aggs": {
//       "conversion": {
//         "nested": { "path": "conversion" },
//         "aggs": {
//           "top_clicked_articles": {
//             "terms": { "field": "conversion.url", "size": 10 },
//             "aggs": {
//               "clicked_search_text": { "reverse_nested": {}, "aggs": { "search_keywords": { "terms": { "field": "text_entered" } } } },
//               "conversion.subject": { "terms": { "field": "conversion.subject", "size": 10 } }
//             }
//           }
//         }
//       }
//     }
//   };
//   if (req.query.uid) {
//     dataString.query.bool.must.push({
//       "term": {
//         "uid": req.query.uid
//       }
//     });
//   }
//   commonFunctions.errorlogger.info(dataString);
//   async.parallel([
//     cb => {
//       let options = {
//         method: 'POST',
//         url: "http://" + config.get('elasticIndex.host') + ":" + config.get('elasticIndex.port') + "/" + config.get('elasticIndex.analytics') + '/search_keyword/_search',
//         qs: {
//           pretty: ''
//         },
//         headers: {
//           'content-type': 'application/json',
//         },
//         body: dataString,
//         json: true
//       };
//       request(options, function (error, response, body) {
//         if (error) {
//           commonFunctions.errorlogger.error(error);
//           return;
//         }
//         cb(null, body);
//       });
//     }
//   ], (error, results) => {
//     // var unrankedSearch = results[0].aggregations.conversion.top_clicked_articles.buckets;
//     let list = results[0].aggregations.conversion.top_clicked_articles.buckets.map(l => {
//       let temp = {
//         "url": l.key,
//         "subject": "Document",
//         "doc_count": l.doc_count,
//         "Keyword": ""
//       };
//       if (l["conversion.subject"].buckets.length) temp.subject = l["conversion.subject"].buckets[0].key;
//       if (l.clicked_search_text.search_keywords.buckets) temp.Keyword = l.clicked_search_text.search_keywords.buckets[0].key;
//       return temp;
//     });
//     res.send(list);
//   });
// });

router.post("/getAutoTuningConfiguration", function (req, res) {
  connection[req.headers['tenant-id']].execute.query("select auto_boosting,facet_interpreter,auto_spell_corrector,featured_snippet,synonymKeywordBoosting, re_ranking, ner, vector_search_enabled, vector_search_settings from search_clients where uid=?", [req.body.uid], (error, resp) => {
    if (error) {
      commonFunctions.errorlogger.error("error", error);
      res.send({
        auto_tuning: 0,
        facet_interpreter: 0,
        auto_spell_corrector: 0,
        featured_snippet: 0,
        synonymKeywordBoosting: 0,
        re_ranking:0,
        ner:0,
        vector_search_enabled: 0,
        vector_search_settings: null
      });
    } else
      res.send(resp[0]);
  })
});

router.post("/getFacetInterpreterService", function (req, res) {
  connection[req.headers['tenant-id']].execute.query("select facet_interpreter from search_clients where uid=?", [req.body.uid], (error, resp) => {
    if (error) {
      commonFunctions.errorlogger.error("error", error);
      res.send([{ facet_interpreter: 0 }]);
    } else
      res.send(resp[0]);
  })
});

router.post("/getAutoSpellCorrectorSevice", function (req, res) {
  connection[req.headers['tenant-id']].execute.query("select  auto_spell_corrector from search_clients where uid=?", [req.body.uid], (error, resp) => {
    if (error) {
      commonFunctions.errorlogger.error("error", error);
      res.send([{ auto_spell_corrector: 0 }]);
    } else
      res.send(resp[0]);
  })
});

router.post("/updateAutoBoostingStatus", async function (req, res) {
  try {
    const result = await tuningService.autoTuning.updateAutoBoostingStatus(req);
    res.send(result);
  } catch (error) {
    commonFunctions.errorlogger.error('Error while updating auto boosting status at route /updateAutoBoostingStatus : ', error);
    res.send({ result: 0 });
  }
});

router.post("/updateFeaturedSnippetStatus", function (req, res) {
  if (req.body.uid && typeof req.body.status != 'undefined') {
    commonFunctions.errorlogger.warn('Inside featured snippet status update call executing db update call');
    connection[req.headers['tenant-id']].execute.query("update search_clients set featured_snippet=? where uid=?", [req.body.status ? 1 : 0, req.body.uid], (error, resp) => {
      if (error) {
        console.log("error", error);
        res.send({ result: 0 });
      } else {
        let configObj = { "platformId": req.body.search_client_id };
        searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
          res.send({ result: 1 });
        });
      }
    })
  } else {
    res.send({ result: 0 });
  }
});

router.post("/updateAutoSpellCorrectorStatus", async function (req, res) {
  try {
    const result = await tuningService.autoTuning.updateAutoSpellCorrectorStatus(req);
    res.send(result);
  } catch (error) {
    commonFunctions.errorlogger.error('Error while updating auto spell corrector status at route /updateAutoSpellCorrectorStatus : ', error);
    res.send({ result: 0 });
  }
});

router.post("/updateFacetInterpreterStatus", async function (req, res) {
  try {
    const result = await tuningService.autoTuning.updateFacetInterpreterStatus(req);
    res.send(result);
  } catch (error) {
    commonFunctions.errorlogger.error('Error while updating facet interpreter status at route /updateFacetInterpreterStatus : ', error);
    res.send({ result: 0 });
  }
});

router.get('/getContentSourceObjectBySearchClientId', function (req, res, next) {
  let sql = 'SELECT sco.search_client_id AS searchclientId, sco.content_source_object_id, ';
  sql += 'cso.label AS contentSourceObjectLabel, cso.name AS contentSourceObjectName, cso.id AS contentSourceObjectId, ';
  sql += 'cs.name AS contentSourceName, cs.id AS contentSourceId ';
  sql += 'from search_clients_to_content_objects sco ';
  sql += 'LEFT JOIN content_source_objects cso ON sco.content_source_object_id = cso.id ';
  sql += 'LEFT JOIN content_sources cs ON cso.content_source_id = cs.id ';
  sql += 'WHERE sco.search_client_id = ' + req.query.id;

  var q = connection[req.headers['tenant-id']].execute.query(sql, function (err, contentSourceData) {
    if (err) {
      console.log("Error in fecthing supported content source types", err);
    } else {
      res.send({ "data": contentSourceData });
    }
  });
});

router.post('/getSearchClientInfo', function (req, res, next) {
  if (req && req.body && req.body.searchClientId) {
    let sql = "SELECT sc.id as scId, sc.name as scName, sc.uid as scUid, cs.id as csId, cs.label as csLabel, cso.id as csoId, cso.label as csoLabel, csof.id as csofId, csof.label as csofLabel, csof.isFilterable as csofFilterable "
      + "FROM search_clients sc "
      + "left join search_clients_to_content_objects scco on scco.search_client_id=sc.id "
      + "left join content_source_objects cso on cso.id = scco.content_source_object_id "
      + "left join content_source_object_fields csof on csof.content_source_object_id = cso.id "
      + "left join content_sources cs on cso.content_source_id=cs.id "
      + "where sc.id = ? ";
      if(!req.query.mergedFields) {
        sql = sql.concat("and csof.merge_field_id = 0");
      }
      connection[req.headers['tenant-id']].execute.query(sql, [req.body.searchClientId], function (err, data) {
      if (err) {
        res.send({ "status": 401, "error": err });
      }
      else {
        if (req.body.configurationType == 1) {
          data = data.filter(x => {
            return x.csofFilterable == 1
          })
        }
        res.send({ "status": 200, "data": data });
      }
    });
  }
  else {
    res.send("ERROR : No searchClientId in Query Params", null);
  }
});

router.post('/insertAutoTuningSettings', function (req, res, next) {
  // First check if this is a child search client
  const sql = "SELECT ab_test_parent FROM search_clients WHERE id = ?";
  
  connection[req.headers['tenant-id']].execute.query(sql, [req.body.searchClientId], function(err, results) {
    if (err) {
      return res.send({ "status": 401, "error": err });
    }

    // Check if search client exists and is not a child
    if (!results.length || results[0].ab_test_parent !== null) {
      return res.send({ "status": 400, "message": "This is a child search client. Auto tuning settings cannot be modified." });
    }

    // Proceed with insert if not a child
    var temp = [];
    req.body.contentSourceObjectField.map(x => {
      temp.push([req.body.configurationType, req.body.searchClientId, req.body.contentSourceId, req.body.contentSourceObjectId, x.id, x.is_selected])
    });

    var insertSql = "INSERT INTO auto_tuning_settings "
      + "(auto_tuning_configuration_type, search_client_id, content_source_id, content_source_object_id, content_source_object_field_id, is_selected)"
      + "VALUES ?";

    connection[req.headers['tenant-id']].execute.query(insertSql, [temp], function (err, data) {
      if (err) {
        res.send({ "status": 401, "error": err });
      }
      else {
        let configObj = { "platformId": req.body.searchClientId };
        searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
          res.send({ "status": 200, "message": "Inserted" });
        });
      }
    });
  });
});


router.post('/deleteAutoTuningSettings', function (req, res, next) {
  var sql = `DELETE FROM auto_tuning_settings WHERE content_Source_id = ? AND 
          content_source_object_id = ? AND
          auto_tuning_configuration_type = ? AND 
          search_client_id = ? `;
  connection[req.headers['tenant-id']].execute.query(sql, [req.body.contentsourceId, req.body.contentSourceObjectId, req.body.configurationType, req.body.searchClientId], function (err, data) {
    if (err) {
      res.send({ "status": 401, "error": 401 });
    }
    else {
      res.send({ "status": 200, "message": "deleted" });
    }
  });
})

router.post('/getAutoTuningSettings', function (req, res, next) {
  let sql = 'SELECT ats.id, cs.id as csId, cs.label as csLabel, cso.id as csoId, cso.label as csoLabel, csof.id as csofId, csof.label as csofLabel, ats.is_selected as is_selected from auto_tuning_settings ats '
    + 'LEFT JOIN content_sources AS cs on cs.id = ats.content_source_id '
    + 'LEFT JOIN content_source_objects as cso on cso.id = ats.content_source_object_id '
    + 'LEFT JOIN content_source_object_fields as csof on csof.id = ats.content_source_object_field_id '
    + 'WHERE auto_tuning_configuration_type = ? AND search_client_id = ? ';
  connection[req.headers['tenant-id']].execute.query(sql, [req.body.auto_tuning_configuration_type, req.body.search_client_id], function (err, tuningData) {
    if (err) {
      console.log("Error in fecthing supported content source types", err);
      res.send({ "status": 401, "error": err });
    } else {
      res.send({ "status": 200, "data": tuningData });
    }
  });
});

router.post('/updateAutoTuningSettings', function (req, res, next) {
  console.log("req");
  var caseStr = " (CASE";
  var includesStr = "";
  const params = [];
  for (var i = 0; i < req.body.contentSourceObjectField.length; i++) {
    caseStr = caseStr + " WHEN content_source_object_field_id = ? then ?";
    params.push(req.body.contentSourceObjectField[i].csofId);
    params.push(req.body.contentSourceObjectField[i].is_selected);
    includesStr = includesStr + "?,";
    params.push(req.body.contentSourceObjectField[i].csofId)
  }
  includesStr = includesStr.slice(0, includesStr.length - 1);
  caseStr = caseStr + " END ) "
  var sql = " UPDATE auto_tuning_settings SET is_selected = " + caseStr + " WHERE content_source_object_field_id IN (" + includesStr + ")";
  console.log(sql);
  connection[req.headers['tenant-id']].execute.query(sql, params, function (err, data) {
    if (err) {
      console.log("Error" + err);
      res.send({ "status": 401, "error": err });
    }
    else {
      res.send({ "status": 200, "data": "updated" })
    }
  })
})


router.post("/updateRankTuningData", function (req, res) {
  connection[req.headers['tenant-id']].execute.query(`SELECT * FROM keyword_boost WHERE search_string=? AND boosting_level = ? AND search_client_id = ?`, [req.body.search_string, req.body.rank,req.body.search_client_id], (err, response) => {
    if (!err && (response.length == 0)) {
      connection[req.headers['tenant-id']].execute.query("UPDATE `keyword_boost` SET `boosting_level` = ? WHERE `id` = ?", [req.body.rank, req.body.id], (error, resp) => {
        if (error) {
          console.log("error", error);
          res.send({ "status": 401, "error": error });
        }
        else {
          let configObj = { "platformId": req.body.search_client_id };
          searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
            res.send({ "status": 200, "data": resp });
          });
        }
      })
    }
    else{
      res.send({ "status": 401, "error": err });
    }
  })
});

router.post("/getCustomBoosting", function (req, res) {
  let search_client_id = req.body.search_client_id;
  var arrtypes = [];
  /**
   * get display mapping
   * get salesforce base url
   * get values from db
   */
  async.auto({
    get_search_Client_id: cb => {
      connection[req.headers['tenant-id']].execute.query(`SELECT * FROM search_clients WHERE id=?`, [search_client_id], cb);
    },
    get_search_client_settings: (cb) => {
      searchClient.getSearchClient(search_client_id,req, cb)
    },
    get_diplay_fields: ['get_search_Client_id', (dataFromAbove, cb) => {

      commonFunctions.getDisplayFields(dataFromAbove.get_search_Client_id[0][0].uid,req, function (displayRows) {
        console.log("here in get_display_fields ")

        var rowsDisplay = displayRows
        for (var iy = 0; iy < rowsDisplay.length; iy++) {
          if (rowsDisplay[iy]["display_field_name"] == "href") {
            href = rowsDisplay[iy]["elastic_field_name"];
          }
        }

        const unique = [...new Set(rowsDisplay.map(item => item.elastic_object_name))];
        unique.forEach(x => {
          arrtypes.push(
            {
              "type": x,
              "values": rowsDisplay.filter(z => z.elastic_object_name == x).map(y => { return ({ "fieldName": y.elastic_field_name, "Displaytype": y.display_field_name }) })
            }
          )
        })
        cb(null, displayRows)
      })
    }],
    get_boost_data: cb => {
      connection[req.headers['tenant-id']].execute.query(`SELECT * FROM custom_boosting WHERE search_client_id=?`, [search_client_id], cb);
    },
    // get_CustomBoost_data:cb => {
    //   connection[req.headers['tenant-id']].execute.query(`SELECT * FROM custom_boosting WHERE search_client_id=? ORDER BY search_string ASC`, [search_client_id], cb);
    // }
  }, (error, results) => {
    let boostDoc = results.get_boost_data[0];
    async.parallelLimit(boostDoc.map(r => {
      return cb => {
        let options = {
          method: 'GET',
          url: `http://${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/${encodeURIComponent(r.index_name)}/${encodeURIComponent(r.index_type)}/${encodeURIComponent(r.record_id)}`,
          headers: { 'content-type': 'application/json' },
          json: true
        };
        request(options, (error, response, body) => {
          console.log(`http://${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/${r.index_name}/${r.index_type}/${r.record_id}`, body);

          if (body.found) {
          console.log("***************got it****************");


            // r.subject = body._source.title || body._source.subject || body._source.Title || body._source.Subject || body._source.Full_Name__c || body._source.name || body._source.Name;
          }
          else {
            r.url = "";
            r.subject = "Document Not Found";
          }
          cb(null, r);
        });
      }
    }), 3, (error, results) => {
      console.log(error, results);
      res.send(error || results);
    });
  });
});

router.get("/getFacetInterpreterTrainButtonInfo", function (req, res) {
  var sql = "Select * from dym_train_dictionary where id = 1"
  connection[req.headers['tenant-id']].execute.query(sql, function (error, data) {
    if (error) {
      commonFunctions.errorlogger.error(error);
      res.send({ "status": 400, "res": "Some Error in Getting Dictionary Info" });
    }
    else {
      res.send({ "status": 200, "res": data[0] });
    }
  });
});

router.get("/trainFacetInterpreterDictionary", function (req, res) {
  let dir = path.join(__dirname, '../../crawlingLogs');
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
  }
  let outputFile = process.cwd() + '/crawlingLogs/facetInterpreter' + "_" + (new Date()).toISOString() + '.log'
  const out = fs.openSync(outputFile, 'a');
  const err = fs.openSync(outputFile, 'a');
  const subprocess = spawn('node', [path.resolve(process.cwd() + '/customPlugins/semanticSearch/cron/facetInterpreter.js')], {
    detached: true,
    stdio: ['ignore', out, err]
  });
  subprocess.unref();
  commonFunctions.errorlogger.info(subprocess.pid);
  var currDate = new Date();
  var sql = "UPDATE dym_train_dictionary "
    + " SET is_enable = 1, last_train_date = ?, pid = ? WHERE id = 1;"
  connection[req.headers['tenant-id']].execute.query(sql, [currDate, subprocess.pid], function (error, data) {
    if (error) {
      commonFunctions.errorlogger.error(error);
      res.send({ "status": 400, "res": "Not Updated" })
    }
    else {
      var dropEventSql = "DROP Event facet_interpreter"
      connection[req.headers['tenant-id']].execute.query(dropEventSql, function (error2, data2) {
        var eventSql = "CREATE EVENT facet_interpreter ON SCHEDULE AT "
          + "CURRENT_TIMESTAMP + INTERVAL 1 HOUR "
          + "DO "
          + "UPDATE dym_train_dictionary "
          + "SET is_enable = 0, pid = 0  where id = 1; "
          connection[req.headers['tenant-id']].execute.query(eventSql, function (error1, data1) {
          if (error1) {
            commonFunctions.errorlogger.error(error1);
          }
          res.send({ "status": 200, "res": "Updated" });
        });
      });
    }
  });
  subprocess.on('exit', (code) => {
    commonFunctions.errorlogger.info("Exiting main Process with Code " + code);
    updatePidStatus(0, req,function (error, data) {
    });
  })
});

function updatePidStatus(pid,req,cb) {
  var sql = "UPDATE dym_train_dictionary SET pid = ? where id = 1"
  connection[req.headers['tenant-id']].execute.query(sql, pid, function (error, data) {
    if (error) {
      commonFunctions.errorlogger.error(error);
      cb(null, null);
    }
    else {
      commonFunctions.errorlogger.info("PID Updated in DB with pid - " + pid);
      cb(null, null);
    }
  })
}

router.post("/getSearchClientSourceObjects",(req,res)=>{
  let search_client_id = req.body.search_client_id;
  const query = `Select cso2.name, cso2.label,cs.elasticIndexName from search_clients_to_content_objects sctco INNER JOIN content_source_objects cso2  ON sctco.content_source_object_id =  cso2.id INNER JOIN content_sources cs ON cso2.content_source_id = cs.id  WHERE sctco.search_client_id = ?`
  connection[req.headers['tenant-id']].execute.query(query,[search_client_id],(err,docs)=>{
    if(err)res.send(err);
    else{
      res.send(docs);
    }
  })
})


router.post("/importKeywordBoosting",async (req,res)=>{
  const {to,from} = req.body;
  const objects = req.body.objects;
  importKeywordBoosting(from,to,objects,req,(err,result)=>{
    if(result){
      let configObj = { "platformId": to };
      searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
        res.send(result );
      });
    }else{
      res.send(err);
    }
  });
})


const importKeywordBoosting = (fromId,toId,objects,req,callback) => {
  async.auto({
    getDataForImport: (cb) => { 
      const query = `SELECT * FROM keyword_boost WHERE search_client_id = ? AND deindex = 0 AND object_label IN ( ${objects.map(ob => '?').join(",")} )`;
      connection[req.headers['tenant-id']].execute.query(query,[fromId,...objects],(err,docs)=>{
        if(docs){
          cb(null,docs)
        }else cb(err,null);
      })
    },
    getDataForCurrentClient: (cb) => {
      const query = `SELECT * FROM keyword_boost WHERE search_client_id = ? AND deindex = 0`;
      connection[req.headers['tenant-id']].execute.query(query,[toId],(err,docs)=>{
        if(docs){
          cb(null,docs)
        }else cb(err,null);
      })
    },
    preProcessData:['getDataForImport','getDataForCurrentClient',(dataFromAbove,cb)=>{
      let importKeyword = dataFromAbove.getDataForImport;
      let currentKeywords = dataFromAbove.getDataForCurrentClient;

      let keywordToImport = [];

      importKeyword.forEach((ik)=>{
        let conflictRank = false;
        let conflictDocument = false;
        currentKeywords.forEach((ck)=>{
            if(ik.search_string.toLowerCase() === ck.search_string.toLowerCase()){
              if(ik.boosting_level === ck.boosting_level){
                conflictRank = true;
              }
              if(ik.record_id === ck.record_id){
                conflictDocument = true;
              }
            }     
        })

        if(!conflictDocument && !conflictRank){
          keywordToImport.push({
            ...ik,
            title:ik.title,
            href:ik.href,
            boosting_level:ik.boosting_level,
            record_id:ik.record_id
          })
        }
      })
      let asyncTasks = [];
      for(let i = 0 ;i<keywordToImport.length;i++){
          delete keywordToImport[i].id;
          keywordToImport[i].search_client_id = toId;
          asyncTasks.push(saveImportTuning.bind(null,keywordToImport[i],req));
      }
      async.series(asyncTasks,(err,docs)=>{
        cb(null,"done");
      })
    }]
  },(err,result)=>{
    if(err)callback(err,null);
    else callback(null,result);
  })
}


const saveImportTuning = (dataObject,req,cb) => {
  console.log(JSON.stringify(dataObject),"dataObject")
  connection[req.headers['tenant-id']].execute.query(`SELECT * FROM keyword_boost WHERE search_string=? AND boosting_level = ? AND search_client_id=? AND record_id=? AND deindex = 0`, [dataObject.search_string, dataObject.boosting_level,dataObject.search_client_id,dataObject.record_id], (err, response) => {
    if(response.length == 0){
      var sqlInsert = "INSERT INTO `keyword_boost` (`id`,`search_string`, `index_name`, `index_type`, `record_id`,`boosting_level`,`search_client_id`,`isBoostingEnabled`,`title`,`href`,`object_label`,`source_label`,`bypass_filter`) " +
      "VALUES (?,?, ?, ?, ?, ?, ?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE id=values(id),`index_name`=values(index_name), `index_type`=values(index_type), `record_id`=values(record_id),`boosting_level`=values(boosting_level),`search_client_id`=values(search_client_id),search_string=values(search_string),href=values(href),title=values(title),object_label=values(object_label),source_label=values(source_label),bypass_filter=values(bypass_filter);";
      connection[req.headers['tenant-id']].execute.query(sqlInsert,[dataObject.id,dataObject.search_string,dataObject.index_name,dataObject.index_type,dataObject.record_id,dataObject.boosting_level,dataObject.search_client_id,1,dataObject.title,dataObject.href,dataObject.object_label,dataObject.source_label,dataObject.bypass_filter],(err,docs)=> {
        if(docs){
          cb(null,docs)
        }else cb(err,null);
      })
    }else{
      cb(null,{});
    }
  })
}


router.get("/getClientsWithBoosting",(req,res)=>{
  const query = `select * from search_clients sc where exists (select 1 from keyword_boost kb where kb.search_client_id = sc.id AND kb.deindex = 0);`
  connection[req.headers['tenant-id']].execute.query(query,(err,response)=>{
    if(err)res.send(err);
    res.send({response});
  })
})

router.post("/getSearchClientSources",(req,res)=>{
  const searchClientId = req.body.searchClientId;
  const query = `SELECT  index_name,index_type,object_label,source_label,COUNT(DISTINCT record_id) as count from keyword_boost kb  WHERE search_client_id = ? AND kb.deindex = 0 GROUP BY index_type ORDER BY(count) DESC`;
  connection[req.headers['tenant-id']].execute.query(query,[searchClientId],(err,docs)=>{
    if(err)res.send(err);
    res.send({sources:docs});
  })
})


router.post('/exportSearchResultByPost', function (req, res, next) {
  res.setHeader("Access-Control-Allow-Origin", "*");
  req.body.knowledgeGraph = true;
  let emailId= req.body.email;
  delete req.body.email;
  if (!req.body.startDate && !req.body.endDate) {
    delete req.body.startDate;
    delete req.body.endDate;
  }
  req.body.searchTuning = req.body.searchTuning || 2;
  analyticsMiddleware.Apisearch(req, res, function(err, response){
    if (err) {
      res.send(err);
    }
    else if(!response){
      res.send({
        "status": 500,
        "msg": "Some Error occurred", 

      });
    }
    else {
      let data = [];
      const currentSelectedTypeEmail = req.body.currentSelectedTypeEmail || '';
      if (currentSelectedTypeEmail === 'basic_results') {
          data = response.resultTab;
      } else {
          data = response.result.hits;
      }
      
      let columns;
      let name = new Date().getTime();
      const dateBetween = req.body.startDate && req.body.endDate ? 'To' : '';

      var dir = path.resolve(__dirname + '/../../reports/reports');
      if (!fs.existsSync(dir))
        fs.mkdirSync(dir);
      let filePath = path.resolve(__dirname + '/../../reports/reports/' + name + '.csv');

      if (!req.body.fromTestTuning) {

        columns = ['Document Title', 'URL', 'Last Crawled Date'];

        fastCsv.writeToPath(filePath, data, {
          headers: columns,
          transform:((b) => {
            return { 'Document Title': he.decode(b.highlight.TitleToDisplayString[0]) || "", 'URL': b.href, 'Last Crawled Date': b.indexedDate };
            })
        });
      }
      else {

        columns = ['ID', 'Title', 'Link', 'Content Source', 'Content type', 'Score', 'Metadata', "Solved"];

        fastCsv.writeToPath(filePath, data, {
          headers: columns,
          transform:((b) => {
            return { 'ID': b.Id,
              'Title': decode(b.highlight.TitleToDisplayString[0] || ""),
              'Content Source': b.sourceLabel, 'Content type': b.objLabel, 'Score': b._score, 'Link': b.href, 
              'Metadata': JSON.stringify(b.metadata.map( i => {
                return (
                  i.key !== 'Tag' && i.value[0] && i.value[0].length 
                  ? {[i.key]: decode(i.value[0])}
                  : i.key == "Tag"
                      ? {[i.key]:i.value
                        .filter(g => decode(g))
                        .join(',')} : null) 
              }).filter(f=>f)).slice(1,-1),
              'ScoreLevel': b.scoreLevel, 'Solved': b.solved, 'SourceLabel': b.sourceLabel };
            })
        });
      }

      var attachment = {
        filename: req.body.fromTestTuning ? "'Test your Tuning' Search Results.csv" : "Content Source Indexed Documents.csv",
        path : filePath,
        contentType: 'text/csv'
      }
      
      const bodyName = sanitizeHtml(req.body.name, {
        allowedTags: [],
        allowedAttributes: []
      })

      var emailObject = {
        to: emailId,
        subject: req.body.fromTestTuning ? `SearchUnify: ${bodyName} 'Test your Tuning' search results` : `SearchUnify: ${bodyName} Indexed Documents`,
        html: emailTemplates.searchResultsEmailTemplate(
          emailId,
          req.body.startDate,
          req.body.endDate,
          dateBetween,
          bodyName,
          req.body.fromTestTuning ? "'Test your Tuning' Search Results" : "Content Source Indexed Documents",
          req.body.fromTestTuning
        ),
        attachments: attachment
      }

      searchunifyEmail.sendEmailWithAttachments(emailObject.to, emailObject.subject, emailObject.html, emailObject.attachments, (err, resp) => {
        commonFunctions.errorlogger.info('email sent with attachment');
        fs.unlinkSync(filePath);
      });
      res.send({
        "status": 200,
        "msg": "email send"
      });
    }

  });
});


router.post('/exportMergefacetResult', function (req, res, next) {
  res.setHeader("Access-Control-Allow-Origin", "*");
  const { uid, email, sendToEmail } = req.body;

  //EARLY EXIT - I | For invalid request format
  if(!uid || (sendToEmail == 1 && !email ) ) return res.json({status:500, message: 'Invalid request!'});

  //get data from search client table
  connection[req.headers['tenant-id']].execute.query(
    "select name, merged_facets from search_clients where uid = ?",
    [uid],
    (error, results) => {
      //EARLY EXIT - II | For data not found / error
      if (error || !results.length) {
        console.error(error || `no data found for ${uid}`);
        return res.sendStatus(500);
      }

      // continue with rest of the formatting
      const searchClientName = results[0].name;
      let data = [];
      try{
        data = JSON.parse(results[0].merged_facets);
      }
      catch(e){
        console.log('Error :',e);
      }
      let mergeFacetParentName = '';
      mergeFacetParentName += data.filter(u => !u.createdDefaultPref).map(u => u.filterNewName).join(' , ')
      const name = new Date().getTime();
      const dir = path.resolve(__dirname + '/../../reports/reports');
      !fs.existsSync(dir) && fs.mkdirSync(dir);
      const filePath = path.resolve(__dirname + '/../../reports/reports/' + name + '.csv');
      const columns = ['Merge Facet Name', 'Merge Facet Values'];

      const arr = [];
      data.forEach(g => {
        if (!g.createdDefaultPref)
          g.filterList.forEach((f, index) => {
            console.log(" Object.keys(f).length --",Object.keys(f).length);
            if(typeof f === "string"){
              arr.push({
                parent: index == 0 ? g.filterNewName : '', child: decodeURIComponent(f)
              })
            }
          })
      })

      // gather data and trigger email
      fastCsv.writeToPath(filePath, arr, {
        headers: columns,
        transform: ((b) => {
          return {
            'Merge Facet Name': b.parent,
            'Merge Facet Values': decode(b.child || "")
          };
        })
      })
        .on('finish', () => {
          // Trigger Email Action
          if (sendToEmail == 1) {
            const attachment = {
              filename: "Merge Facets - Search Client.csv",
              path: filePath,
              contentType: 'text/csv'
            }

            const emailObject = {
              to: email,
              subject: 'Merge Facets - Search Client ' + searchClientName,
              html: emailTemplates.mergeFacetEmailTemplate(
                mergeFacetParentName,
                email,
                searchClientName
              ),
              attachments: attachment
            }

            searchunifyEmail.sendEmailWithAttachments(emailObject.to, emailObject.subject, emailObject.html, emailObject.attachments, (err, resp) => {
              commonFunctions.errorlogger.info('email sent with attachment');
              fs.unlinkSync(filePath);
            });
            res.send({
              "status": 200,
              "msg": "Email triggered!"
            });
          }
          // Downloads Action
          else {
            res.download(filePath);
            fs.unlink(filePath);
          }
        });
    }
  );
});
// API's for Intent Boosting
const saveIntentBoosting= (documents,searchClientId,req,cb) => {
  const scId = searchClientId
  const {id,intent,indexName,indexLabel,documentId,rank,title,href,objectLabel,objectName} = documents;
  var sqlInsert = "INSERT INTO `intent_boosting` (`id`,`intent`, `index_name`, `index_label`, `document_id`,`rank`,`search_client_id`,`title`,`href`,`object_name`,`object_label`,`isBoostingEnabled`) " +
  "VALUES (?,?, ?, ?, ?, ?, ?,?,?,?,?,?) ON DUPLICATE KEY UPDATE id=values(id),intent=values(intent),`index_name`=values(index_name), `index_label`=values(index_label), `document_id`=values(document_id),`rank`=values(rank),`search_client_id`=values(search_client_id),href=values(href),title=values(title),object_label=values(object_label),object_name=values(object_name),isBoostingEnabled=values(isBoostingEnabled);";
  connection[req.headers['tenant-id']].execute.query(sqlInsert, [id,intent,indexName,indexLabel,documentId,rank,scId,title,href,objectName,objectLabel,1], function (errInsert, rowsInsert) {
  if (errInsert) {
    commonFunctions.errorlogger.info("error while updating and inserting ", errInsert);
  } else {
    cb(null,"Done")
  }
});
}


router.post("/saveIntentBoosting", (req,res)=>{
const {intents} = req.body.data;
const {searchClientId} = req.body
var asyncTasks = []
for(let i =0; i<intents.length; i++){
  asyncTasks.push(saveIntentBoosting.bind(null,intents[i],searchClientId,req));
}

async.series(asyncTasks, function (err,result) {
    let configObj = { "platformId": searchClientId };
    searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
      res.send({ "status": 200, "message": 'Successfully saves records' });  
    });
  });
});

router.post('/getIntentBoosting', (req, res) => {
  const { searchClientId, searchKeyword, pageNo, offset, intentData, deletedDocs } = req.body;
  let fieldToSearch = req.body.fieldToSearch ? req.body.fieldToSearch : 'intent';
  // Fixed pageNo offset and limit fields and added logic for temp documents
  let newDocs = 0;
  let newDocuments = [];
  let updatedDocuments = [];
  let deletedDocuments = [];
  if (intentData && intentData.length > 0) {
    intentData.map(item => {
      if (item.event === 'new') {
        newDocuments.push(item);
        newDocs++;
      } else {
        updatedDocuments.push(item);
      }
    });
  }
  if (deletedDocs && deletedDocs.length > 0) {
    deletedDocs.map(item => {
      deletedDocuments.push(item.intent);
    });
  }

  let factor = pageNo;
  if (offset === 20) {
    factor = pageNo <= 10 ? pageNo * Math.ceil(offset / pageNo) : (pageNo * Math.ceil(offset / pageNo)) + pageNo;
  } else if (offset === 30) {
    if (pageNo <= 10) {
      factor = pageNo * Math.ceil(offset / pageNo);
    } else if (pageNo > 10 && pageNo <= 20) {
      factor = pageNo * Math.ceil(offset / pageNo) + pageNo;
    } else {
      factor = pageNo * Math.ceil(offset / pageNo) + (pageNo * 2);
    }
  }
  let actualOffset = pageNo === 0 ? pageNo : factor;
  let actualLimit = offset;
  let actualPageNumber = Math.ceil(actualOffset / actualLimit + 1);

  let newLimit = actualLimit;
  if ((actualLimit * actualPageNumber) - newDocs > 0) {
    if (Math.ceil(newDocs / actualLimit) >= actualPageNumber) {
      newLimit = newLimit - Math.ceil(newDocs % actualLimit) > 0 ? newLimit - (newDocs % actualLimit) : 0;
    }
  } else {
    if (Math.ceil(newDocs / actualLimit) >= actualPageNumber) {
      newLimit = newLimit - Math.ceil(newDocs % actualLimit) > 0 ? 0 : newLimit - (newDocs % actualLimit);
    }
  }
  let newOffset = actualOffset;
  if (Math.floor(newDocs / actualOffset) <= actualPageNumber) {
    const tempVal = (actualLimit * (actualPageNumber - 1)) - newDocs;
    newOffset = tempVal > 0 ? tempVal : 0;
  }
  // Fix end
  async.auto({
    getIntentBoostingDocument: cb => {
      querySql = `SELECT *, GROUP_CONCAT(document_id,'@-~@',object_label,'@-~@',index_label,'@-~@',rank,'@-~@',title,'@-~@',href,'@-~@', intent ,'@-~@' ,id, '@-~@', last_state, '@-~@',index_name,'@-~@',object_name ,'@-~@' ,bypass_filter SEPARATOR '#-#') as documents FROM intent_boosting where search_client_id=? `
      if (deletedDocuments && deletedDocuments.length > 0) {
        var deleteData = "";
        deletedDocuments.map((o, i) => {
          o = JSON.stringify(o);
          if (deletedDocuments.length - 1 == i) {
            deleteData = deleteData.concat(o);
          } else {
            deleteData = deleteData.concat(o, ",");
          }
        });
        deleteData = "(" + deleteData + ")";
        querySql += ` AND intent NOT IN ${deleteData}`;
      }
      querySql += ` AND ${fieldToSearch} LIKE ?  GROUP by intent LIMIT ?,?`;
      connection[req.headers['tenant-id']].execute.query(querySql, [searchClientId, '%' + searchKeyword + '%', newOffset, newLimit], cb);
    },
    getIntentBoostingTotalCount: cb => {
      querySqlCount = `SELECT COUNT(*), GROUP_CONCAT(document_id,'@-~@',object_label,'@-~@',index_label,'@-~@',rank,'@-~@',title,'@-~@',href,'@-~@',intent,'@-~@',id ,'@-~@' ,bypass_filter SEPARATOR '#-#') as documents FROM intent_boosting where search_client_id=? `
      if (deletedDocuments && deletedDocuments.length > 0) {
        var deleteData = "";
        deletedDocuments.map((o, i) => {
          o = JSON.stringify(o);
          if (deletedDocuments.length - 1 == i) {
            deleteData = deleteData.concat(o);
          } else {
            deleteData = deleteData.concat(o, ",");
          }
        });
        deleteData = "(" + deleteData + ")";
        querySqlCount += ` AND intent NOT IN ${deleteData}`;
      }
      querySqlCount += `AND ${fieldToSearch} LIKE ? GROUP by intent`
      connection[req.headers['tenant-id']].execute.query(querySqlCount, [searchClientId, '%' + searchKeyword + '%'], cb);
    }
  }, (err, response) => {
    let totalDocuments = response.getIntentBoostingTotalCount[0].length;
    let results = response.getIntentBoostingDocument[0];
    results.forEach((result) => {
      documentArray = [];
      const documents = result.documents.split("#-#");
      documents.forEach((document) => {
        let documentSplit = document.split("@-~@");
        documentArray.push({
          documentId: documentSplit[0],
          objectLabel: documentSplit[1],
          indexLabel: documentSplit[2],
          rank: documentSplit[3],
          title: documentSplit[4],
          href: documentSplit[5],
          intent: documentSplit[6],
          id: documentSplit[7],
          last_state: documentSplit[8],
          indexName: documentSplit[9],
          objectName: documentSplit[10],
          bypass_filter: documentSplit[11],
        })
      })
      // attach to result
      result.documents = [...documentArray];
    })
    if (updatedDocuments.length > 0) {
      updatedDocuments.map(item => {
        const index = results.findIndex(o => (o.intent === item.intent));
        if (index !== -1) {
          results[index] = item;
        }
      });
    }
    if (results.length < actualLimit) {
      fromIndex = 0;
      toIndex = actualLimit;
      if (Math.ceil((parseInt(totalDocuments) + parseInt(newDocs)) / parseInt(actualLimit)) > parseInt(actualPageNumber)) {
        if (newDocs !== 0 && newDocs > actualLimit) {
          fromIndex = actualLimit * (actualPageNumber - 1);
          const slicedArray = newDocuments.splice(fromIndex, toIndex);
          results = [...slicedArray, ...results];
        } else {
          results = [...newDocuments, ...results];
        }
      } else {
        if (Math.ceil((parseInt(totalDocuments) + parseInt(newDocs)) / parseInt(actualLimit)) == parseInt(actualPageNumber)) {
          if (newDocs !== 0 && newDocs > actualLimit) {
            fromIndex = actualLimit * (actualPageNumber - 1);
            toIndex = Math.ceil(newDocs % actualLimit);
            const slicedArray = newDocuments.splice(fromIndex, toIndex);
            results = [...slicedArray, ...results];
          } else {
            if (((Math.floor((parseInt(totalDocuments) + parseInt(newDocs)) / parseInt(actualLimit)) == parseInt(actualPageNumber))) || parseInt(actualPageNumber) === 1) {
              results = [...newDocuments, ...results];
            }
          }
        }
      }
    }
    if(req.body.csv == 1){
      let intentCsvData = []
      if(results.length){
        results.forEach((iB)=>{
          iB.documents.forEach((i)=>{
            intentCsvData.push({
              "Intent":iB.intent,
              "IndexName": i.indexName,
              "IndexLabel": i.indexLabel,
              "DocumentId":i.documentId,
              "Rank":i.rank,
              "SearchClientId":req.body.searchClientId,
              "Title":i.title || i.href,
              "Href":i.href,
              "ObjectName": i.objectName,
              "ObjectLabel": i.objectLabel,
              "LastState":i.last_state,
              "BypassFilter":i.bypass_filter,
            })
            
          })
      })
    }else{
        intentCsvData.push({});
      }
      const Fields = ["Intent","IndexName","IndexLabel","DocumentId","Rank","SearchClientId","Title","Href","ObjectName","ObjectLabel","LastState","BypassFilter"]
      const opts = { Fields };
      const parser = new Parser(opts);
      const csv = parser.parse(intentCsvData);
      res.attachment('intent.csv');
      res.send(csv);
    }
    else{
    res.send({
      total: totalDocuments + newDocs,
      body: results,
      dbRecord: totalDocuments
    })
  }
  })
})


router.post('/getIntentBoostingDocument',(req,res)=>{
  const {documentId,searchClientId,intentData,deletedDocs} = req.body;
  querySql = `SELECT *, GROUP_CONCAT(document_id,'@-~@',object_label,'@-~@',index_label,'@-~@',rank,'@-~@',title,'@-~@',href,'@-~@',intent, '@-~@',index_name,'@-~@',object_name,'@-~@', id ,'@-~@' ,bypass_filter SEPARATOR '#-#') as intents FROM intent_boosting where search_client_id=? AND document_id = ? GROUP by document_id`
  connection[req.headers['tenant-id']].execute.query(querySql, [searchClientId, documentId],(err,docs)=>{
    const intentsArr = [];
    let result = {}
    if(docs && docs.length){
      result = docs[0];
      let intents = result.intents.split('#-#')
      intents.forEach((document)=>{
        let documentSplit = document.split("@-~@");
        const bypassFilter = documentSplit[10] == "1";
        intentsArr.push({
          documentId:documentSplit[0],
          objectLabel:documentSplit[1],
          indexLabel:documentSplit[2],
          rank:documentSplit[3],
          title:documentSplit[4],
          href:documentSplit[5],
          intent:documentSplit[6],
          indexName:documentSplit[7],
          objectName:documentSplit[8],
          id:documentSplit[9],
          bypass_filter: bypassFilter
        })
      })
    }
    if(deletedDocs && deletedDocs.length > 0){
      deletedDocs.map(dItem => {
        const index =  intentsArr.findIndex(i=> i.intent == dItem.intent);
         if(index > -1){
           intentsArr.splice(index,1);
         }
       })
    }
    if(intentData && intentData.length > 0){
      intentData.map(iD => {
        const x = intentsArr.findIndex(x=> x.intent == iD.intent);
        if(x > -1){
          intentsArr.splice(x,1);
        }
      })
      intentData.map(iD => {
      const docsIndex = iD.documents.findIndex(docsI => docsI.documentId == documentId);
       if(docsIndex > -1){
        intentsArr.push(iD.documents[docsIndex])
       }
      })
    }
    result.intents = intentsArr;
    res.send(result);
  });
});

router.post("/getRank", (req, res) => {
  const { searchString, searchClientId, keyword } = req.body;
  querySql = `SELECT boosting_level, record_id FROM keyword_boost where search_client_id=? AND search_string=?`;
  connection[req.headers['tenant-id']].execute.query(querySql, [searchClientId, searchString], (err, docs) => {
      let ranks = [];
      if (keyword.data && keyword.data.length > 0) {
          keyword.data.map((u) => {
              u.keywordValues.map((i) => {
                  if (i.searchString == searchString) {
                      const index = docs.findIndex(
                          (k) => k.record_id == u.recordId
                      );
                      if (index > -1) {
                          docs[index].boosting_level = parseInt(i.rank);
                      } else {
                          ranks.push(parseInt(i.rank));
                      }
                      return i;
                  }
              });
              return u;
          });
      }
      // update db value from session
      if (keyword.data && keyword.data.length > 0) {
          docs = docs.filter(doc=>{
          const recordFound = keyword.data.find(u => u.recordId == doc.record_id);
          if(recordFound){
           const result =  recordFound.keywordValues.filter( rec => rec.searchString == searchString);
           if(result && result.length > 0){
            return true;
           }else{
            return false;
           }
          }else{
            return true;
          }
        })
      }
      if (keyword.deleteBoostedDocs && keyword.deleteBoostedDocs.length > 0) {
        docs = docs.filter(doc=>{
        const recordFound = keyword.deleteBoostedDocs.find(u => u.recordId == doc.record_id);
        if(recordFound){
         const result =  recordFound.keywordValues.filter( rec => rec.searchString == searchString);
         if(result && result.length > 0){
          return true;
         }else{
          return false;
         }
        }else{
          return true;
        }
       })
      }
      if (docs && docs.length > 0) {
          docs.map((x) => {
              ranks.push(x.boosting_level);
          });
          if (ranks.length > 0) {
              ranks.sort(function (a, b) {
                  return a - b;
              });
              res.send({ ranks });
          }
      } else {
          res.send({ ranks });
      }
  });
});

router.post("/getIntentRank", (req, res) => {
  let { intentName, searchClientId, intent } = req.body;
  let searchForIntent = '';
  if(intent && intent.data.length > 0){
   let index =  intent.data.findIndex(i=> i.intent == intentName);
   if(index > -1){
     searchForIntent = ''
   }else{
    searchForIntent = intentName ;
   }
  }else{
    searchForIntent = intentName ;
  }
  let querySqlIntent = `SELECT *, GROUP_CONCAT(document_id,'@-~@',object_label,'@-~@',index_label,'@-~@',rank,'@-~@',title,'@-~@',href,'@-~@', intent ,'@-~@' ,id, '@-~@', last_state, '@-~@', index_name , '@-~@', object_name ,'@-~@' ,bypass_filter SEPARATOR '#-#') as documents FROM intent_boosting where search_client_id=? AND intent=? GROUP by intent`;
  connection[req.headers['tenant-id']].execute.query(querySqlIntent, [searchClientId,searchForIntent], (error, results) => {
    if(results.length > 0){
      documentArray = [];
      const documents = results[0].documents.split("#-#");
      documents.forEach((document)=>{
        let documentSplit = document.split("@-~@");
        documentArray.push({
          documentId:documentSplit[0],
          objectLabel:documentSplit[1],
          indexLabel:documentSplit[2],
          rank:documentSplit[3],
          title:documentSplit[4],
          href:documentSplit[5],
          intent:documentSplit[6],
          id:documentSplit[7],
          last_state: documentSplit[8],
          index_name : documentSplit[9],
          object_name : documentSplit[10],
          bypass_filter: documentSplit[11]
        })
      })
      // attach to results
      results[0].documents = [...documentArray];
      res.send({ results:results[0] });
    }else{
      let results = []
      if(intent && intent.data){
        intent.data.map(i => {
          if(i.intent == intentName){
             results.push(i);
          }
        })
      }
      res.send({ results : results[0] });
    }
  });
});


router.get("/deleteIntentBoosting",(req,res)=>{
  commonFunctions.CleanRedisCache();
  const {searchClientId,id} = req.query;
  connection[req.headers['tenant-id']].execute.query("DELETE FROM `intent_boosting` WHERE id = ?", [id], (error, resp) => {
    if (error){
      commonFunctions.errorlogger.error("error", error);
      res.send({"Error":1})
    }
    else{
      let configObj = { "platformId": searchClientId };
      searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
        res.send({ "status": 200, "message": 'Successfully Delete records' });  
      });
   }
  })
})

router.post("/updateIntentBoosting",(req,res)=>{
  const {rank,id,searchClientId,intent} = req.body;
  commonFunctions.CleanRedisCache();
    connection[req.headers['tenant-id']].execute.query(`SELECT * FROM intent_boosting WHERE intent=? AND rank = ? AND search_client_id = ?`, [intent, rank,searchClientId], (err, response) => {
      if (!err && (response.length == 0)) {
      connection[req.headers['tenant-id']].execute.query("UPDATE `intent_boosting` SET rank=? WHERE id = ?", [rank,id], (error, resp) => {
        if (error){
          commonFunctions.errorlogger.error("error", error);
          res.send({"Error":1})
        }
        else{
          let configObj = { "platformId": searchClientId };
          searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
            res.send({ "status": 200, "message": 'Successfully Update records' });  
          });
       }
      })
      }else{
        res.send({ "status": 400, "message": 'Rank not available' });  
      }
    })
});

router.post("/updateIntentBoostingToggle", function (req, res) {
  commonFunctions.CleanRedisCache();
  var isEnabled = (req.body.isEnabled) ? 1 : 0;
  connection[req.headers['tenant-id']].execute.query("UPDATE intent_boosting SET isBoostingEnabled=? WHERE search_client_id=?",[isEnabled,req.body.search_client_id],(err,response) => {
    if (!err) {
      let configObj = { "platformId": req.body.search_client_id };
      searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
        res.send({ "status": 201, "message": 'Saved' });
      });
    }
    else
      res.send({ "status": 200, "message": 'Not Exist' });
  })
});

router.post("/enableReranking", async function (req, res) {
  try {
    const result = await tuningService.autoTuning.reRankingEnable(req);
    res.send(result);
  } catch (error) {
    commonFunctions.errorlogger.error('Error while updating reRanking status at route /enableReranking : ', error);
    res.send({ result: 0 });
  }
});

router.post("/enableNER", async function (req, res) {
  try {
    const result = await tuningService.autoTuning.enableNER(req);
    res.send(result);
  } catch (error) {
    commonFunctions.errorlogger.error('Error while updating NER status at route /enableNER : ', error);
    res.send({ result: 0 });
  }
});

router.post("/updateReRankingValue", function (req, res) {
  if (req.body.uid) {
    connection[req.headers['tenant-id']].execute.query("update search_clients set re_rank_value=? where uid=?", [req.body.rerankValue, req.body.uid], (error, resp) => {
      if (error) {
        console.log("error", error);
        res.send({ result: 0 });
      } else {
        let configObj = { "platformId": req.body.search_client_id };
        searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj, req, function (err, searchConfig) {
          res.send({ result: 1 });
        });
      }
    })
  } else {
    res.send({ result: 0 });
  }
});

router.get("/getReRankValue",(req,res)=>{
  const query = `select re_rank_value from search_clients where uid=?;`
  connection[req.headers['tenant-id']].execute.query(query,[req.query.uid], (err,response) =>{
    if(err)res.send(err);
    res.send(response[0]);
  })
})


async function alertMessage(data, req, scName,email) {
  let formattedMessage = '*Search Tuning Information*\n*---------------------------------------------------------*\n';
  try {
    if (!Array.isArray(data) || data.length === 0) {
      return "Invalid data format or empty array";
    }

    formattedMessage += `*Timestamp* : \t${moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}\n`;
    formattedMessage += `*Tenant ID* : \t${req.headers['tenant-id'] || 'Not available'}\n`;
    formattedMessage += `*Email Id* : \t${email}\n`;

    if (!req || !req.get('Referer')) {
      throw new Error('Invalid request object or missing Referer.');
    }

    formattedMessage += `*SearchClient* :\t${`${scName}`}\n`;
    formattedMessage += `*API* :\t${`${req.get('Referer').replace("dashboard/search-tuning","")}`}\n`;

    formattedMessage += '\n*Boosting* :\t*Status*\n';
    data.forEach(({ key, oldValue, newValue }) => {
      const status = newValue ? 'Enabled' : 'Disabled';
      const keyDisplayName = getKeyDisplayName(key);
      formattedMessage += `${keyDisplayName}:\t${status}\n`;
    });

  } catch (error) {
    formattedMessage = 'Error formatting message. Please check the data and request.';
  }
  return formattedMessage;
}

function getKeyDisplayName(key) {
  const keyMapping = {
    "intentTuning": "Intent Tuning",
    "keywordTuning": "Keyword Tuning",
    "contentTuning": "Content Tuning",
    "customTuning": "Custom Tuning",
  };

  return keyMapping[key] || key;
}

router.post("/saveAllBoosting", async (req, res) => {
  try {

    const {
      params,
      content,
      custom,
      intent,
      keyword,
      auto
    } = req.body;

    const flag = requestValidator(keyword);
    if (flag === 1) {

      commonFunctions.CleanRedisCache();

      if (params.isModified === 'true') {

        const processContentTuning = async function (req, params, content) {
          // Save content based tuning
          commonFunctions.errorlogger.info('Saving content based tuning');
          try {
            await tuningService.contentTuning.changeStatus(req, params, content);
            await tuningService.contentTuning.deleteOld(req, params, content);
            await tuningService.contentTuning.insertNew(req, content);
            await tuningService.contentTuning.insertObjectBoosting(req, content);
          } catch (error) {
            commonFunctions.errorlogger.error('Error at method processContentTuning : ', error);
            throw error;
          }
        }

        const processCustomTuning = async function (req, params, custom) {
          // Save custom based tuning
          commonFunctions.errorlogger.info('Saving custom based tuning');
          try {
            await tuningService.customTuning.changeStatus(req, params, custom);
            await tuningService.customTuning.deleteOld(req, params, custom);
            await tuningService.customTuning.insertNew(req, params, custom);
          } catch (error) {
            commonFunctions.errorlogger.error('Error at method processCustomTuning : ', error);
            throw error;
          }
        }

        const processIntentTuning = async function (req, params, intent) {
          // Save intent based tuning
          commonFunctions.errorlogger.info('Saving intent based tuning');
          try {
            await tuningService.intentTuning.deleteOld(req, params, intent);
            await tuningService.intentTuning.insertNew(req, params, intent)
          } catch (error) {
            commonFunctions.errorlogger.error('Error at method processIntentTuning : ', error);
            throw error;
          }
        }

        const processKeywordTuning = async function (req, params, keyword) {
          // Save keyword based tuning
          commonFunctions.errorlogger.info('Saving keyword based tuning');
          try {
            await tuningService.keywordTuning.changeStatus(req, params, keyword);
            await tuningService.keywordTuning.deleteOld(req, params, keyword);
            await tuningService.keywordTuning.insertNew(req, params, keyword);
          } catch (error) {
            commonFunctions.errorlogger.error('Error at method processKeywordTuning : ', error);
            throw error;
          }
        }

        const processAutoTuning = async function (req, params, auto) {
          // save auto based tuning 
          commonFunctions.errorlogger.info('Saving auto based tuning');
          try {
            req.body = {
              ...req.body,
              search_client_id: params.searchClientId,
              uid: params.uid,
              status: auto.autoTab.autoBoostingEnable,
              getKafka: false
            }
            await tuningService.autoTuning.updateAutoBoostingStatus(req);
            req.body.status = auto.autoTab.autoSpellEnable;
            await tuningService.autoTuning.updateAutoSpellCorrectorStatus(req);
            req.body.status = auto.autoTab.facetInterpreterEnable;
            await tuningService.autoTuning.updateFacetInterpreterStatus(req);
            req.body = {
              ...req.body,
              status: auto.autoTab.reRankingStatus,
              updateImpactedBoostings: false
            }
            await tuningService.autoTuning.reRankingEnable(req);
            req.body.status = auto.autoTab.nerStatus;
            await tuningService.autoTuning.enableNER(req);
          } catch (error) {
            commonFunctions.errorlogger.error('Error at method processAutoTuning : ', error);
            throw error;
          }
        }

        const savedDocs = await tuningService.boostingDocs.saveDocs(req, params, intent, keyword);

        await Promise.all([
          processContentTuning(req, params, content),
          processCustomTuning(req, params, custom),
          processIntentTuning(req, params, intent),
          processKeywordTuning(req, params, keyword),
          processAutoTuning(req, params, auto)
        ]);

       // Determine if keyword boosting is enabled
        const isKeywordBoostingEnabled = keyword && keyword.enabled ? keyword.enabled : false;
        commonFunctions.errorlogger.info('Keyword Boosting Enabled:', isKeywordBoostingEnabled);

        // Determine if intent boosting is enabled
        const isIntentBoostingEnabled = intent && intent.enabled ? intent.enabled : false;
        commonFunctions.errorlogger.info('Intent Boosting Enabled:', isIntentBoostingEnabled);


        commonFunctions.errorlogger.info('Getting SC settings via Kafka');
        let configObj = { 
          "platformId": params.searchClientId,
          "keywordBoostingDocsDetails": docsDetailsForAnalytics,
         "savedDocs": {
            ...savedDocs,
            "keywordBoostingEnabled": isKeywordBoostingEnabled,
            "intentBoostingEnabled": isIntentBoostingEnabled
          }
        }
        await new Promise((resolve, reject) => {
          searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj, req, (err, response) => {
            if (err) {
              reject(err);
            } else {
              resolve(response);
            }
          });
        });

        res.send({ "status": 200, "message": 'Successfully updated boostings' });
      } else {
        res.send({ "status": 400, "message": 'You need to make some changes first' });
      }
    } else if (flag === 0) {
      commonFunctions.errorlogger.info("Keyword is empty");
      res.status(400).send({ "status": 400, "message": 'Keyword should not be empty' });
    } else if (flag === 2) {
      commonFunctions.errorlogger.info(`Keyword length exceeds the maximum allowed length of ${config.get('searchTunning.maxKeywordLength')}`);
      res.status(400).send({ "status": 400, "message": `Keyword length should not exceed the maximum allowed length of ${config.get('searchTunning.maxKeywordLength')}` });
    } else {
      commonFunctions.errorlogger.info('Something went wrong');
      res.status(500).send({ "status": 500, "message": 'Something went wrong' });
    }
  } catch (e) {
    commonFunctions.errorlogger.info('Something went wrong',e);
    res.status(500).send({ "status": 500, "message": 'Something went wrong' });
  }
});

router.post("/sendAlertSearchTuning", async (req, res) => {
  try {
    commonFunctions.CleanRedisCache();
    const {
      params,
      differences,
      currentloggedUser
    } = req.body;

    if (boostingAlertFlag && Array.isArray(differences) && differences.length > 0) {
      if (typeof alertMessage === 'function') {
        const beautifiedMessage = await alertMessage(differences, req, params.searchClientName, currentloggedUser);
        console.log("beautifiedMessage -->", beautifiedMessage);
        if (typeof alertGoogleChat === 'function') {
          alertGoogleChat({
            url: boostingAlertUrl,
            message: beautifiedMessage,
            event: "Alert Tuning",
          });

          res.status(200).send({ "status": 200, "message": differences });
        } else {
          res.status(500).send({ "status": 500, "message": 'Internal Server Error: alertGoogleChat is not a function' });
        }
      } else {
        res.status(500).send({ "status": 500, "message": 'Internal Server Error: alertMessage is not a function' });
      }
    } else {
      res.status(200).send({ "status": 200, "message": 'Nothing change found' });
    }
  } catch (error) {
    console.error('Error:', error);
    res.status(500).send({ "status": 500, "message": 'Internal Server Error' });
  }
});

const readFile = function (req, cb) {
  if (req.files) {
    fs.readFile(req.files.bulk_upload_csv.path, (err, data) => {
      if (!err) {
        data = data.toString('utf-8')
        var options = {
          delimiter: ',', // optional
          quote: '"' // optional
        };
        data = json.toObject(data, options);
        cb(null, data);
      }
      else cb(err, null);
    });
  }
  else
    cb({ status: 400, message: "No data to import" });
}

router.post('/importTuning', multipartMiddleware, function (req, res, cb) {
  let searchClientId = req.query.id;
  var taskArray = [];
  readFile(req, function (err, rows) {
    if (err) res.send(err);
    const validKeysName = ["Keyword","Rank","Title","Url","Id","IndexName", 
    "IndexLabel", "ObjectName", "ObjectLabel","SearchClientId","LastState","BypassFilter"]
    let validCsv = false;
    if(rows.length == 0){
      validCsv = false;
    }else if(rows.length > 0){
        if(Object.keys(rows[0]).length == 12){
          for (var key in rows[0]) {
            if (rows[0].hasOwnProperty(key)) {
                if(validKeysName.includes(key)){
                  validCsv = true;
                }else{
                  validCsv = false;
                  break;
                }
            }
        }
        }else{
          validCsv = false;
        }
    }
    if(JSON.parse(req.query.proceed)){
      rows.forEach((element, index) => {
        taskArray.push(importKeywordThroughFile.bind(null, element, searchClientId, req))
      })
      async.series(taskArray, (err, results) => {
        keywordBoostingDeindex(req);
        if(results){
          let configObj = { "platformId": searchClientId };
          searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
            res.send({message:"Imported file successfully", status: 200})
          });
        }else{
          res.send(err);
        }
      })
    }else{
      if(validCsv){
        res.send({message:"Imported file successfully", status: 200})
      }else
      res.send({message:"InvalidCsv", status: 400})
    }
  })
});


router.post('/importTuningIntent', multipartMiddleware, function (req, res, cb) {
  let searchClientId = req.query.id;
  var taskArray = [];
  readFile(req, function (err, rows) {
    if (err) res.send(err);
    const validKeysName = ["Intent","IndexName","IndexLabel","DocumentId","Rank","SearchClientId","Title","Href","ObjectName","ObjectLabel","LastState","BypassFilter"]
    let validCsv = false;
    if(rows.length == 0){
      validCsv = false;
    }else if(rows.length > 0){
        if(Object.keys(rows[0]).length == 12){
          for (var key in rows[0]) {
            if (rows[0].hasOwnProperty(key)) {
                if(validKeysName.includes(key)){
                  validCsv = true;
                }else{
                  validCsv = false;
                  break;
                }
            }
        }
        }else{
          validCsv = false;
        }
    }
    if(JSON.parse(req.query.proceed)){
      rows.forEach((element, index) => {
        taskArray.push(importIntentThroughFile.bind(null, element, searchClientId, req))
      })

      async.series(taskArray, (err, results) => {
        if(results){
          let configObj = { "platformId": searchClientId };
          searchResultKafkaConfig.getSearchClientSettingsViaKafka(configObj,req, function (err, searchConfig) {
            res.send({message:"Imported file successfully", status: 200})
          });
        }else{
          res.send(err);
        }
      })
    }else{
      if(validCsv){
        res.send({message:"Imported file successfully", status: 200})
      }else
      res.send({message:"InvalidCsv", status: 400})
    }
  })
});

function importKeywordThroughFile(element, searchClientId, req, cb) {
  if (!element.Id || !element.IndexName || !element.ObjectName) {
    return cb();
  }

  // Set isBoostingEnabled to 1 before the SELECT query
  let updateIsBoostingSql = `
    UPDATE keyword_boost 
    SET isBoostingEnabled = 1 
    WHERE search_client_id = ?`;

  connection[req.headers['tenant-id']].execute.query(updateIsBoostingSql, [searchClientId], (updateErr) => {
    if (updateErr) {
      commonFunctions.errorlogger.error("Error updating isBoostingEnabled", updateErr);
      return cb(updateErr, null);
    }

    // Continue with the SELECT query
    connection[req.headers['tenant-id']].execute.query(
      `SELECT * FROM keyword_boost WHERE search_string = ? AND search_client_id = ? AND deindex = 0`,
      [element.Keyword, searchClientId],
      (selectErr, response) => {
        if (selectErr) {
          commonFunctions.errorlogger.error("Error in SELECT query", selectErr);
          return cb(selectErr, null);
        }

        let conflictRank = false;
        let conflictDocument = false;
        if (response && response.length) {
          response.some((ck) => {
            if (element.Keyword.toLowerCase() === ck.search_string.toLowerCase()) {
              conflictRank = element.Rank == ck.boosting_level;
              conflictDocument = element.Id == ck.record_id;
              return true;
            }
          });

          if (conflictDocument || conflictRank) {
            return cb();
          }
        }

        // Construct SQL query to insert or update records
        let sql = `
          INSERT INTO keyword_boost
          (
            search_string, index_name, index_type, record_id, boosting_level,
            search_client_id, isBoostingEnabled, title, href, object_label,
            source_label, deindex, last_state, bypass_filter
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE 
            index_name = VALUES(index_name),
            index_type = VALUES(index_type),
            record_id = VALUES(record_id),
            boosting_level = VALUES(boosting_level),
            search_client_id = VALUES(search_client_id),
            isBoostingEnabled = VALUES(isBoostingEnabled),
            title = VALUES(title),
            href = VALUES(href),
            object_label = VALUES(object_label),
            source_label = VALUES(source_label),
            deindex = VALUES(deindex),
            last_state = VALUES(last_state),
            bypass_filter = VALUES(bypass_filter)`;

        // Execute the query
        connection[req.headers['tenant-id']].execute.query(sql,
          [
            element.Keyword, element.IndexName, element.ObjectName, element.Id,
            element.Rank, searchClientId, '1', element.Title, element.Url,
            element.ObjectLabel, element.IndexLabel, '0', element.LastState,
            element.BypassFilter,
          ],
          (error, resp) => {
            if (error) {
              commonFunctions.errorlogger.error("Error in SQL query", error);
              return cb(error, null);
            }

            // Callback with success
            cb(null, {});
          }
        );
      }
    );
  });
}


function importIntentThroughFile(element, searchClientId, req, cb) {
  if (!element.DocumentId || !element.IndexName || !element.ObjectName) {
    return cb();
  }

  // Set isBoostingEnabled to 1 before the SELECT query
  let updateIsBoostingSql = `
    UPDATE intent_boosting 
    SET isBoostingEnabled = 1 
    WHERE search_client_id = ?`;

  connection[req.headers['tenant-id']].execute.query(updateIsBoostingSql, [ searchClientId], (updateErr) => {
    if (updateErr) {
      commonFunctions.errorlogger.error("Error updating isBoostingEnabled", updateErr);
      return cb(updateErr, null);
    }

    // Continue with the SELECT query
    connection[req.headers['tenant-id']].execute.query(
      `SELECT * FROM intent_boosting WHERE intent = ? AND search_client_id = ?`,
      [element.Intent, searchClientId],
      (selectErr, response) => {
        if (selectErr) {
          commonFunctions.errorlogger.error("Error in SELECT query", selectErr);
          return cb(selectErr, null);
        }

        let conflictRank = false;
        let conflictDocument = false;

        if (response && response.length) {
          response.some((ck) => {
            if (element.Intent.toLowerCase() === ck.intent.toLowerCase()) {
              conflictRank = element.Rank == ck.rank;
              conflictDocument = element.DocumentId == ck.document_id;
              return true;
            }
          });

          if (conflictDocument || conflictRank) {
            console.log(conflictDocument, conflictRank, "Conflicts found");
            return cb();
          }
        }

        // Construct SQL query to insert or update records
        let sql = `
          INSERT INTO intent_boosting
          (
            intent, index_name, index_label, document_id, rank, search_client_id,
            title, href, object_name, object_label, last_state, bypass_filter, isBoostingEnabled
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE 
            intent = VALUES(intent),
            index_name = VALUES(index_name),
            index_label = VALUES(index_label),
            document_id = VALUES(document_id),
            rank = VALUES(rank),
            search_client_id = VALUES(search_client_id),
            title = VALUES(title),
            href = VALUES(href),
            object_name = VALUES(object_name),
            object_label = VALUES(object_label),
            last_state = VALUES(last_state),
            bypass_filter = VALUES(bypass_filter),
            isBoostingEnabled = VALUES(isBoostingEnabled)`;

        // Execute the query
        connection[req.headers['tenant-id']].execute.query(sql,
          [
            element.Intent, element.IndexName, element.IndexLabel, element.DocumentId,
            element.Rank, searchClientId, element.Title, element.Href, element.ObjectName,
            element.ObjectLabel, element.LastState, element.BypassFilter, '1'
          ],
          (error, resp) => {
            if (error) {
              commonFunctions.errorlogger.error("Error in SQL query", error);
              return cb(error, null);
            }

            // Callback with success
            cb(null, {});
          }
        );
      }
    );
  });
}

function keywordBoostingDeindex(req) {
  let dirpathLogs =  path.resolve(process.cwd(),"customPlugins/semanticSearch/cron/keywordLogs"
  )
  if (!fs.existsSync(dirpathLogs)) {
    fs.mkdirSync(dirpathLogs);
    commonFunctions.errorlogger.info("Created logs directory");
    }
  let outputFile = dirpathLogs +'/keywordBoosting_' + (new Date()).toISOString() + '.log'
  const out = fs.openSync(outputFile, 'a');
  const err = fs.openSync(outputFile, 'a');
  const subprocess = spawn(
    "node",
    [
      path.resolve(
        process.cwd(),
        "customPlugins/semanticSearch/cron/keywordBoostingCron.js"
      ),"updateBoosting","keywordBoostingCsvImport",req.headers['tenant-id'],
    ],
    {
      detached: true,
      stdio: ['ignore', out, err]
    }
  );
  subprocess.unref();
}


module.exports = router;
