const router = require('express').Router();
const async = require('async');
const aes256 = require('nodejs-aes256');
const commonFunctions = require('../../../utils/commonFunctions');
const appVariables = require('../../../constants/appVariables');
const searchClient = require('../../../routes/admin/searchClient');
const cron = require('../../../utils/Cron');
const archiver = require('archiver');
const fs = require('fs');
const path = require('path');
const replace = require('stream-replace');
const templates = require('../personalizedTemplateSettings/templateSettings');
const {publishCrons} = require('../../../utils/kafka/kafkaConsumers/publishCrons')

router.get('/getAllBots', function (req, res) {
    getAllBotsAndClients(req, (error, result) => {
        res.send(result);
    })
});

router.get('/getBotSettings', function (req, res) {
    const tenantId = req.headers['tenant-id'];
    getBotSettings(req.query.botId, tenantId, (error, result) => {
        if (result) {
            delete result.accessToken;
            delete result.instanceURL;
            delete result.refreshToken;
            delete result.password;
            delete result.client_secret;
            delete result.htaccessPassword;
            res.send(result);
        } else {
            res.send({});
        }
    })
});

router.post('/updateBotSettings', function (req, res) {
    updateBotSettings(req.body.bot,req, (error, result) => {
        if (error) {
            return res.send(error);
        }
        res.send(result);
    });
});

router.post('/deleteBot', function (req, res) {
    let sql = `DELETE FROM community_helper_bot WHERE id = ?`;
    connection[req.headers['tenant-id']].execute.query(sql, req.body.botId, function (err, result) {
        if (!err) {
            cron.deleteCron('bot_' + req.body.botId, function (result) {
                commonFunctions.errorlogger.info(`Cron deleted for bot ${req.body.botId}`)
            });
            res.send(result);
        }
        else {
            commonFunctions.errorlogger.error(err);
        }
    })
});

router.get('/getBotContentSources', function (req, res) {
    getBotContentSources(req.query.clientId, req.query.botId, req, (error, result) => {
        res.send(result);
    })
});

router.post('/updateBotContentSources', function (req, res) {
    const taskObjects = [];
    for (var o = 0; o < req.body.length; o++) {
        taskObjects.push((function (o) {
            return function (cb) {
                insertUpdateBotSettings(req.body[o], 2,req, cb);
            }
        })(o))
    }
    async.parallel(taskObjects, function (err, result) {
        if (!err) res.send({ "flag": 200, "Success": true });
        else res.send({ "flag": 400, "Success": false });
    })
});

router.get('/deleteAllCrons', function(req, res) {
    async.auto({
        getAllBots: (cb) => {
            getAllBots(req,cb);
        },
        deleteAllCrons: ['getAllBots', (dataFromAbove, cb) => {
            async.parallel( dataFromAbove.getAllBots.map((bot) => {
                return function(cb) {
                    bot.status = 0;
                    bot.is_connected = 0;
                    delete bot.search_client_name;
                    updateBotSettings({botSettings: bot},req, (error, result) => {
                        if (result.flag == 200)
                            cb(null, '');
                        else
                            cb(result.message);
                    });
                }
            }), (error, result) => {
                cb(error, result);
            })
        }]
    }, (error, result) => {
        if (!error) res.send({ "flag": 200, "message": "Deleted" });
        else res.send({ "flag": 400, "message": "Failed" });
    })
});

const getAllBots = (req,cb) => {
    let sql = `select community_helper_bot.*, search_clients.name as search_client_name from community_helper_bot left join search_clients on community_helper_bot.search_client_id = search_clients.id`;
    connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
        if (!err) {
            cb(null, rows);
        }
        else {
            commonFunctions.errorlogger.info("fetch all community helper bots", error);
            cb(null, []);
        }
    })
};

const getBotSettings = (botId, tenantId, cb) => {
    let sql = `select community_helper_auth.*,community_helper_bot.platform_type_id from 
        community_helper_auth left join community_helper_bot on 
        community_helper_bot.id=community_helper_auth.community_helper_bot_id  
        where community_helper_bot_id=?`;
        connection[tenantId].execute.query(sql, botId, function (err, rows) {
        if (!err) {
            rows = rows[0] || {};
            for (var key in rows) {
                if (rows.hasOwnProperty(key)) {
                    if (key === "client_secret" || key === "password" || key === "htaccessPassword" || key === "accessToken" || key === "refreshToken" || key === "sessionId") {
                        rows[key] = rows[key] ? aes256.decrypt(appVariables.analytics.encryptionKey, rows[key]) : ""
                    }
                }
            }
            cb(null, rows);
        }
        else {
            cb(null, []);
        }
    })
};

const updateBotSettings = (bot,req, callback) => {
    // add validation for wrong input on name
    var htmlRegex = new RegExp("<\/?[^>]+(>|$)");
    var specialCharacterNotExist = /^[a-zA-Z0-9 ]*$/;
    if (htmlRegex.test(bot.botSettings.name) || !specialCharacterNotExist.test(bot.botSettings.name)) {
        return callback({ statusCode: 403, message: 'Only strings are allowed' });
    }
    if (bot.botSettings.platform_type_id === 3){
        bot.oauthSettings.client_id = config.get('salesforceClientId');
    }
    async.auto({
        insertBotSetting: (cb) => {
            if (bot.botSettings) {
                insertUpdateBotSettings(bot.botSettings, 0,req, cb);
            } else {
                cb(null, []);
            }
        },
        insertBotAuthSetting: ["insertBotSetting", (dataFromAbove, cb) => {
            if (bot.oauthSettings && bot.botSettings.id) {
                bot.oauthSettings.community_helper_bot_id = bot.botSettings.id;
                if (bot.botSettings.platform_type_id === 3)
                    bot.oauthSettings.client_secret = commonFunctions.appVariables.salesforce.appSecret;
                if (bot.botSettings.platform_type_id == 2)
                    bot.oauthSettings.authorization_type = bot.oauthSettings.authorization_type.join(',');
                insertUpdateBotSettings(bot.oauthSettings, 1,req, cb);
            } else {
                cb(null, []);
            }
        }],
        setCron: ["insertBotAuthSetting", "insertBotSetting", (dataFromAbove, cb) => {
            if (bot.botSettings.is_connected) {
                getCronEssentials(bot.botSettings.id, bot.botSettings.platform_type_id,req, cb);
            } else if (!bot.botSettings.is_connected) {

                const values = {
                    type : 'DELETE',
                    service: 'admin',
                    tenantId: req.headers['tenant-id'],
                    cronId: `communityhelper_${req.headers.session.tpk}`
                }
                publishCrons(values);
                cb(null, [])
            } else {
                cb(null, [])
            }
        }]
    }, function (error, result) {
        let response = { "flag": 400, "message": "" }
        if (error) {
            commonFunctions.errorlogger.info("add community helper", error);
            response.message = "Sorry error occured while updating helper settings";
        } else {
            response.flag = 200;
            response.message = "Successfully updated helper settings";
        }
        callback(null, response)
    })
};

const insertUpdateBotSettings = (settings, tableName,req, cb) => {
    let table;
    if (tableName === 0) table = "community_helper_bot";
    if (tableName === 1) table = "community_helper_auth";
    if (tableName === 2) table = "community_helper_to_search_client_objects";
    const columns = [];
    const parameters = [];

    for (var key in settings) {
        if (settings.hasOwnProperty(key)) {
            columns.push(key);
            if (key === "client_secret" || key === "password" || key === "htaccessPassword" || key === "accessToken" || key === "refreshToken" || key === "sessionId")
                parameters.push(aes256.encrypt(appVariables.analytics.encryptionKey, settings[key]))
            else
                parameters.push(settings[key]);
        }
    }

    const sql = "INSERT INTO " + table + " (" + columns +
        ") VALUES (" + columns.map(x => {
            return '?'
        }).join(',') + ") " +
        "ON DUPLICATE KEY UPDATE " + columns.map(x => {
            return x + "=values(" + x + ")"
        }).join(',');

    const q = connection[req.headers['tenant-id']].execute.query(sql, parameters, function (err, result) {
        if (!err) {
            cb(null, result);
        }
        else {
            cb(err, '');
        }
    })
};

const getBotSearchConfiguration = (botId,req, cb) => {
    let sql = `select * from community_helper_to_search_client_objects where community_helper_bot_id = ?`;
    let q = connection[req.headers['tenant-id']].execute.query(sql, botId, function (err, rows) {
        if (!err) {
            cb(null, rows);
        }
        else {
            commonFunctions.errorlogger.info("fetch search client setting helper bots", err);
            cb(err, []);
        }
    })
};

const getBotContentSources = (clientId, botId, req, callback) => {
    async.auto({
        getSearchClientSettings: (cb) => {
            searchClient.getSearchClient(clientId, req, cb);
        },
        getBostContentSources: (cb) => {
            getBotSearchConfiguration(botId, req, cb);
        }
    }, function (error, result) {
        if (result) {
            clientId = parseInt(clientId);
            if (result.getSearchClientSettings) {
                let sources = result.getSearchClientSettings.sources.filter(x => x.enabled).map(y => { y.objects = y.objects.filter(z => z.enabled); return y });

                sources.map(s => {
                    s.enabled = false;
                    s.objects.map(o => {
                        o.enabled = false;
                        result.getBostContentSources.map(x => {
                            if (x.content_source_object_id === o.id && x.search_client_id === clientId && x.is_enabled) {
                                s.enabled = true;
                                o.enabled = true;
                                o.community_helper_object_id = x.id;
                            }
                        })
                    })
                })

                callback(null, sources);
            }
        } else {
            commonFunctions.errorlogger.info("fetch search client setting helper bots", error);
            callback(null, []);
        }
    })
}

const getAllBotsAndClients = (req,cb) => {
    async.auto({
        getAllBots: (cb) => {
            getAllBots(req,cb);
        },
        getLithiumSearchClients: (cb) => {
            getSearchClientsByPlatformId(2,req, cb);
        },
        getSalesforceSearchClients: (cb) => {
            getSearchClientsByPlatformId(8,req, cb); // salesforce types [3,7,8,9]
        }
    }, function (error, result) {
        if (error) {
            commonFunctions.errorlogger.info("Error while fetching bots and clients", error);
            cb(null, [])
        } else {
            cb(null, result)
        }
    })
}

const getSearchClientsByPlatformId = (id,req, cb) => {
    let sql = `select sc.id, sc.name, typ.img,sc.uid,sc.created_date,sc.search_client_type_id 
                from search_clients AS sc, search_client_types AS typ 
                where sc.search_client_type_id = typ.id and 
                typ.id IN ( `;
    Array.isArray(id) ? id.forEach((i, index) => {
        sql += `${i}`;
        sql += index < id.length - 1 ? ', ' : '';
    }) :
        sql += id
    sql += ` ) and sc.id NOT IN (select search_client_id from community_helper_bot ) ORDER BY sc.created_date DESC`;
    connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
        if (!err) {
            cb(null, rows);
        }
        else {
            cb(null, []);
        }
    })

}

const setCronForBot = (botId, freqType, freqTime, platformType,req,cb) => {

    let parameter = ""
    let interval = freqTime;
    let frequencyType = freqType;
    let platformName = '';
    let filePath = '';

    interval = Math.round(parseInt(freqTime * 1));

    if (frequencyType === 'Minutes')
        parameter = '*/' + interval + ' * * * *';
    else if (frequencyType === 'Hours')
        parameter = '0 */' + interval + ' * * *';
    else if (frequencyType === 'Days')
        parameter = '0 0 */' + interval + ' * *'
    else
        parameter = '';

    if (platformType == 3) {
        filePath = "/feedPost"
        platformName = 'salesforce';
    }
    if (platformType == 2) {
        filePath = '/lithiumPosts'
        platformName = 'lithium';
    }

    if (typeof interval === 'number') {
       if(parameter){ 
           const values = {
            type: 'ADD',
            service: 'admin',
            frequency: parameter,
            tenantId: req.headers['tenant-id'],
            cronId: `communityhelper_${req.headers.session.tpk}`,
            command: `curl '${config.get('adminURL')}/admin/cron${filePath}?botId=${botId}&tenant-id=${req.headers['tenant-id']}'`

        }
            publishCrons(values);
            cb(null, "cron set");

        }else{
            cb(null, "");
        }
    } else
        cb(null, "cron not set")
}

const getCronEssentials = (botId, platformId,req, callback) => {
    async.auto({
        fetchResponseSetting: (cb) => {
            templates.getBotResponses(botId,req, cb)
        },
        setCron: ["fetchResponseSetting", (dataFromAbove, cb) => {
            if (dataFromAbove.fetchResponseSetting && dataFromAbove.fetchResponseSetting.length && platformId && botId) {
                let frequencySettings = dataFromAbove.fetchResponseSetting[0];
                let freq_type = frequencySettings.response_freq_type;
                let freq_time = frequencySettings.response_freq_time;
                setCronForBot(botId, freq_type, freq_time, platformId,req, cb)
            } else {
                cb(null, []);
            }
        }]
    }, function (error, result) {
        let response = { "flag": 400, "message": "" }
        if (error) {
            commonFunctions.errorlogger.info("add community helper", error);
            response.message = "Sorry error occured while updating helper settings";
        } else {
            response.flag = 200;
            response.message = "Successfully updated helper settings";
        }

        callback(null, response)
    })
}

const createFolderZipRec = (folder, zipArchive, base, cb) => {
    fs.readdir(folder, (err, files) => {
        if (err) cb(err);
        else {
            async.parallel(files.map(f => {
                return cb => {
                    fs.stat(path.join(folder, f), (err, stat) => {
                        if (err) cb(err);
                        else {
                            if (stat && stat.isDirectory()) {
                                createFolderZipRec(path.join(folder, f), zipArchive, path.join(base, f), cb);
                            }
                            else {
                                let stream = fs.createReadStream(path.join(folder, f));
                                let ext = f.split(".")[f.split(".").length - 1];
                                // ext = ext[ext.length-1];
                                ext = ext.toLowerCase();
                                if (ext == "png" || ext == "ico" || ext == "jpg" || ext == "jpeg" || ext == "zip") { }
                                else {
                                    stream = stream
                                        .pipe(replace(/{{host}}/g, config.get("adminURL").split('/')[2]))
                                }
                                zipArchive.append(stream, {
                                    name: f,
                                    prefix: base + "/"
                                });
                                cb(null, path.join(base, f));
                            }
                        }
                    });
                };
            }), cb);
        }
    });
};

router.get('/downloadLithiumEndpoint', (req, res) => {
    let zipArchive = archiver('zip', { zlib: { level: 9 } });
    res.setHeader('Content-disposition', 'attachment; filename=communityBot.zip');

    zipArchive.pipe(res);
    createFolderZipRec(DIRNAME + "/customPlugins/communityHelper/download/", zipArchive, "", (error, result) => {
        if (error) commonFunctions.errorlogger.error("Error: ", error);
        else {
            zipArchive.finalize();
        }
    });
})

module.exports = {
    router: router,
    getBotSettings: getBotSettings,
    insertUpdateBotSettings: insertUpdateBotSettings,
    getBotContentSources: getBotContentSources
};