module.exports = {
	"objects": [
		{
			"name": "articles",
			"label": "Articles",
			"status": 1,
			"boosting_factor": 1,
			"base_url": "",
			"fields": []
		},
		{
			"name": "conversations",
			"label": "Conversations",
			"status": 1,
			"boosting_factor": 1,
			"base_url": "",
			"fields": []
		},
		{
			"name": "questions",
			"label": "Questions",
			"status": 1,
			"boosting_factor": 1,
			"base_url": "",
			"fields": []
		},
		{
			"name": "ideas",
			"label": "Ideas",
			"status": 1,
			"boosting_factor": 1,
			"base_url": "",
			"fields": [
				{
					"name": "id",
					"label": "Id",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "title",
					"label": "Title",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "content",
					"label": "Content",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "submittedAt",
					"label": "Submitted At",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 0,
					"isActive": 1
				},
				{
					"name": "lastActivityAt",
					"label": "Last Activity At",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 0,
					"isActive": 1
				},
				{
					"name": "totalReplyCount",
					"label": "Total Reply Count",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "tags",
					"label": "Tags",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "trashed",
					"label": "Trashed",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 0,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "closed",
					"label": "Closed",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 0,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "pendingApproval",
					"label": "Pending Approval",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 0,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "pinnedReplyId",
					"label": "Pinned Reply Id",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 0,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "ideaStatus",
					"label": "Idea Status",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "ideaStatusChangedAt",
					"label": "Idea Status Changed At",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 0,
					"isActive": 1
				},
				{
					"name": "seoCommunityUrl",
					"label": "Url",
					"type": "string",
					"isFilterable": 0,
					"isSortable": 0,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "replies",
					"label": "Replies",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				}
			]

		},
		{
			"name": "events",
			"label": "Events",
			"status": 1,
			"boosting_factor": 1,
			"base_url": "",
			"fields": [
				{
					"name": "id",
					"label": "Id",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "title",
					"label": "Title",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "content",
					"label": "Content",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "startDate",
					"label": "Start Date",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 0,
					"isActive": 1
				},
				{
					"name": "endDate",
					"label": "End Date",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 0,
					"isActive": 1
				},
				{
					"name": "location",
					"label": "Location",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "createdAt",
					"label": "Created At",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 0,
					"isActive": 1
				},
				{
					"name": "trashed",
					"label": "Trashed",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 0,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "url",
					"label": "Url",
					"type": "string",
					"isFilterable": 0,
					"isSortable": 0,
					"isSearchable": 1,
					"isActive": 1
				},
				{
					"name": "attendees",
					"label": "Attendees",
					"type": "string",
					"isFilterable": 1,
					"isSortable": 1,
					"isSearchable": 1,
					"isActive": 1
				}
			]

		}
	],
	"fields": [
		{
			"name": "id",
			"label": "Id",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "title",
			"label": "Title",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "content",
			"label": "Content",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "replyCount",
			"label": "Reply Count",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "likes",
			"label": "Likes",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "votes",
			"label": "Votes",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "views",
			"label": "Views",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "voteSet",
			"label": "Vote Set",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "likeSet",
			"label": "Like Set",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "trashed",
			"label": "Trashed",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "sticky",
			"label": "Sticky",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "bestAnswer",
			"label": "Best Answer",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "author",
			"label": "Author Username",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "lastContributor",
			"label": "Last Contributor Username",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "createdAt",
			"label": "Created At",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 0,
			"isActive": 1
		},
		{
			"name": "lastActivityAt",
			"label": "Last Activity At",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 0,
			"isActive": 1
		},
		{
			"name": "status",
			"label": "Status",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "ideaStatus",
			"label": "Idea Status",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "productAreas",
			"label": "Product Areas",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "publishedAt",
			"label": "Published At",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 0,
			"isActive": 1
		},
		{
			"name": "replies",
			"label": "Replies",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "categoryId",
			"label": "Category Id",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "categoryName",
			"label": "Category Name",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "publicId",
			"label": "Public Id",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		},
		{
			"name": "seoCommunityUrl",
			"label": "Community Url",
			"type": "string",
			"isFilterable": 1,
			"isSortable": 1,
			"isSearchable": 1,
			"isActive": 1
		}
	]
};
