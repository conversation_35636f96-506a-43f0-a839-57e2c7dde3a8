include:
  - project: 'searchunify/gitlab-actions' # Use the path   to your common configuration repository
    file: '.gitlab-ci.yml'
sonarqube-check:
  rules:
  - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "nonexistent-source"'
    when: never
build-check:
  image: node:10
  stage: sonarqube
  script:
    - |
      echo -e "\n[ BASH : INFO ] NPM version: $(npm --version)\n[ BASH : INFO ] Node version: $(node --version)\n"
      echo -e "\n[ NPM : LINT ] Linting (backend)..."
      npm run lint-backend
  rules:
  - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^((2[4-9]|[3-9][0-9])-((jun)|(jul)|(aug)|(sep)|(oct)|(nov)|(dec)|(jan)|(feb)|(feb-opensearch)|(mar)|(apr)|(may)))$/ && $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME !~ /^((2[4-9]|[3-9][0-9])-((jun)|(jul)|(aug)|(sep)|(oct)|(nov)|(dec)|(jan)|(feb)|(feb-opensearch)|(mar)|(apr)|(may)))$/'
    when: on_success
