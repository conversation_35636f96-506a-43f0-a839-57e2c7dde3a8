

module.exports = {
    "objects": [{
        "name": "boxfile",
        "label": "Box Content",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [{
        "name": "id",
        "label": "id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "parent_folder",
        "label": "Parent Folder",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "file_url",
        "label": "File Url",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1,
        "isMasked": 0
    },
    {
        "name": "author_name",
        "label": "Author Name",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1,
        "isMasked": 0
    },
    {
        "name": "author_email",
        "label": "Author Email",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1,
        "isMasked": 0
    },
    {
        "name": "title",
        "label": "Title",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "view_href",
        "label": "View Href",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1,
        "isMasked": 0
    },
    {
        "name": "post_time",
        "label": "Created Date",
        "type": "datetime",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 0,
        "isActive": 1,
        "isMasked": 0
    },
    {
        "name": "tag",
        "label": "Tag",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "permissions",
        "label": "Permissions",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1,
        "isMerged": 1
    },
    {
        "name": "content",
        "label": "Description",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "comment",
        "label": "Comment",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "file_type",
        "label": "File Type",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "html_body",
        "label": "HTML Body",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "parent_id",
        "label": "Parent ID",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "selected_folder_id",
        "label": "Selected Folder Id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "parent_folders_list",
        "label": "Parent Folders List",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 0,
        "isSearchable": 1,
        "isActive": 1
    }
]}
