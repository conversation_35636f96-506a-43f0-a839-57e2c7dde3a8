var express = require('express');
var router = express.Router();
const axios = require("axios");
var request = require("request");
const commonFunctions = require('../utils/commonFunctions');

const getAccessToken = async (uid, req) => {
    const sql = 'Select slack_enabled, slack_creds from agenthelper where uid = ?';
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(sql, uid, (err, rows) => {
            if (err) {
                resolve({"message": 'Invalid Creds'});
            } else {
                console.log(rows);
                if (rows && rows.length) {
                    if (!rows[0].slack_enabled) resolve({enabled: rows[0].slack_enabled});
                    else if (JSON.parse(rows[0].slack_creds)) {
                        let slackCreds = JSON.parse(rows[0].slack_creds);
                        slackCreds.enabled = rows[0].slack_enabled;
                        slackCreds.clientId = slackCreds.clientId ? commonFunctions.encryptDecryptCreds(slackCreds.clientId, 'decrypt') : null;
                        slackCreds.clientSecret = slackCreds.clientSecret ? commonFunctions.encryptDecryptCreds(slackCreds.clientSecret, 'decrypt') : null;
                        if (slackCreds.clientId && slackCreds.clientSecret) resolve(slackCreds);
                        else resolve({});
                    } else resolve ({});
                } else resolve ({});
            }
        })
    });
}

const getDetailsDb = async (ahId, req) => {
    const sql = 'Select slack_enabled, slack_client_id, slack_client_secret from ah_clients where agent_helper_id = ?';
    return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(sql, ahId, (err, rows) => {
            if (err) {
                resolve({"message": 'Invalid Creds'});
            } else {
                console.log(rows);
                if (rows && rows.length) {
                    if (rows[0] && rows[0].slack_client_id && rows[0].slack_client_secret && rows[0].slack_enabled) {
                        let slackCreds = {};
                        slackCreds.enabled = rows[0].slack_enabled;
                        slackCreds.clientId = rows[0].slack_client_id;
                        slackCreds.clientSecret = rows[0].slack_client_secret;
                        if (slackCreds.clientId && slackCreds.clientSecret) resolve(slackCreds);
                        else resolve({});
                    } else resolve ({});
                } else resolve ({});
            }
        })
    });
}

//returns the slack creds saved in the db 
const getSlackCreds = async (uid, req) => {
    return new Promise((resolve, reject) => {
        try {
            const tenantId = req.headers['tenant-id'];
            if (!tenantId || !connection[tenantId]) {
                return resolve({ status: 400, message: 'Invalid tenant ID or connection not found' });
            }
            
            const sql = 'SELECT slack_enabled, slack_creds FROM agenthelper WHERE uid = ?';
            connection[tenantId].execute.query(sql, uid, (err, rows) => {
                if (err) {
                    console.log("err in the making datbase connection", err);
                    return resolve({ status: 500, message: 'Database query error' });
                }
                
                if (!rows || !rows.length) {
                    return resolve({ status: 404, message: 'No data found' });
                }
                
                const slackEnabled = rows[0].slack_enabled;
                if (!slackEnabled) {
                    return resolve({ status: 404, message: "Slack not enabled for the tenant" });
                }
                
                let slackCreds = {};
                try {
                    slackCreds = JSON.parse(rows[0].slack_creds);
                    slackCreds = slackCreds.clientId;
                } catch (parseError) {
                    console.log("error in parsing slackCreds >>>>", parseError);
                    return resolve({ status: 500, message: 'Error parsing Slack credentials' });
                }
                
                slackCreds = slackCreds ? commonFunctions.encryptDecryptCreds(slackCreds, 'decrypt') : null;
                
                if (!slackCreds) {
                    return resolve({ status: 404, message: 'Incomplete Slack credentials' });
                }
                
                return resolve({ status: 200, data: slackCreds });
            });
        } catch (error) {
            return resolve({ status: 500, message: 'Unexpected server error' });
        }
    });
};

const splitLongMessage = (messageText, pretextIfLong, characterLimit = 4999) => {
    // Calculate total length including pretext
    const pretextLength = messageText.pretext ? messageText.pretext.length : 0;
    const pretextForLong = pretextIfLong ? pretextIfLong: "... Continued" ;
    const textLength = messageText.text ? messageText.text.length : 0;
    const totalLength = pretextLength + textLength;
    
    //message within limit
    if (totalLength <= characterLimit) {
        return [messageText]; 
    }
    
    const messages = [];
    let remainingText = messageText.text || '';
    
    const firstMessageTextLimit = characterLimit - pretextLength;
    
    //finding the last space
    let splitIndex = firstMessageTextLimit;
    if (remainingText.length > firstMessageTextLimit) {
        const lastSpaceIndex = remainingText.lastIndexOf(' ', firstMessageTextLimit);
        
        //use space if avaible or last word
        if (lastSpaceIndex > 0) {
            splitIndex = lastSpaceIndex;
        }
    }
    
    messages.push({
        pretext: messageText.pretext || '',
        text: remainingText.substring(0, splitIndex),
    });
    
    remainingText = remainingText.substring(splitIndex + 1);
    
    // Process any remaining text into chunks
    while (remainingText.length > 0) {
        const continuedPretext = pretextForLong;
        const chunkLimit = characterLimit - continuedPretext.length;
        
        let chunkSplitIndex = Math.min(chunkLimit, remainingText.length);
        
        if (remainingText.length > chunkLimit) {
            const lastSpaceIndex = remainingText.lastIndexOf(' ', chunkLimit);
            
            if (lastSpaceIndex > 0) {
                chunkSplitIndex = lastSpaceIndex;
            }
        }
        
        const chunk = remainingText.substring(0, chunkSplitIndex);
        
        messages.push({
            pretext: continuedPretext,
            text: chunk,
        });
        
        remainingText = chunkSplitIndex < remainingText.length ? 
            remainingText.substring(chunkSplitIndex + 1) : '';
    }
    
    return messages;
};


const validateSlackMessage = (req, res, next) => {
    const { uid, email, messageToPost } = req.body;

    if (!uid) return res.status(400).json({ message: 'Insufficient Data - uid missing' });
    if (!email) return res.status(400).json({ message: 'Insufficient Data - email missing' });
    if (!messageToPost) return res.status(400).json({ message: 'Insufficient Data - no message to post' });
    
    //valdating uid format
    const uidRegex = new RegExp('[0-9a-f]{8}[-][0-9a-f]{4}[-][0-9a-f]{4}[-][0-9a-f]{4}[-][0-9a-f]{12}$');

    if (!uidRegex.test(uid)) {
      console.log('Validation failed for uid >>', uid);
      return res.status(400).json({ message: 'UID format not valid' });
    }

    //validating email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!emailRegex.test(email)) {
        console.log('Validation failed for email >>', email);
        return res.status(400).json({ message: 'Email format not valid' });
    }

    
    if (typeof messageToPost !== 'object' || !('pretext' in messageToPost) || !('text' in messageToPost)) {
        return res.status(400).json({ message: 'Invalid message format' });
    }
    
    if (!messageToPost.pretext.trim() && !messageToPost.text.trim()) {
        return res.status(400).json({ message: 'Both pretext and text cannot be empty' });
    }

    next();
};

const getSlackChannels = async (req, token) => {
    const url = 'https://slack.com/api/conversations.list';
    var options = {
        url,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization':  'Bearer ' + token
        },
        qs: {
            types: 'public_channel,private_channel',
        }
    };
    return new Promise((resolve, reject) => {
        request(options, function (err, res, body) {
            if (err) reject(err);
            if (body && JSON.parse(body)) {
                commonFunctions.errorlogger.info(JSON.parse(body))
                resolve(JSON.parse(body));
            } else reject(body);
        });
    });
}

const getSlackToken = async (slackCreds) => {
    const url = 'https://slack.com/api/oauth.v2.access';
    var data = {
        "grant_type": "authorization_code",
        "client_id": slackCreds.clientId,
        "client_secret": slackCreds.clientSecret,
        "code": slackCreds.code,
        "redirect_uri": 'https://oauthsfdc.searchunify.com'
    }
    var options = {
        url,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        formData: data
    };
    return new Promise((resolve, reject) => {
        request(options, function (err, res, body) {
            if (err) reject(err);
            if (body && JSON.parse(body)) {
                commonFunctions.errorlogger.info(JSON.parse(body))
                resolve(JSON.parse(body));
            } else reject(body);
        });
    });
}

const postMessageOnSlack = async (data) => {
    const url = 'https://slack.com/api/chat.postMessage';
    const payload = {
        channel: data.channelId,
        as_user: true,
    };

    if (!data.blocks && !data.message) {
        throw new Error("Either 'blocks' or 'message' must be provided.");
    }

    if (Array.isArray(data.blocks)) {
        payload["blocks"] = data.blocks;
    } else if (data.message) {
        payload["attachments"] = [data.message];
    }

    const options = {
        url,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + data.token
        },
        body: JSON.stringify(payload)
    };
    return new Promise((resolve, reject) => {
        request(options, function (err, res, body) {
            if (err) reject(err);
            if (body && JSON.parse(body)) {
                commonFunctions.errorlogger.info(JSON.parse(body))
                resolve(JSON.parse(body));
            } else reject(body);
        });
    });
};

const fetchSlackUserId = async (email, token) => {
    const response = await axios.get(`https://slack.com/api/users.lookupByEmail`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { email }
    });
    if(!response.data.ok) console.log("error in slack API lookupByEmail>>>", response.data.error);
    return response.data.ok ? response.data.user.id : null;
}

const openSlackDM = async (userId, token) => {
    const response = await axios.post(`https://slack.com/api/conversations.open`, {
        users: userId
    }, {
        headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json"
        }
    });
    if(!response.data.ok) console.log("error in slack API conversations.open>>>", response.data.error);
    return response.data.ok ? response.data.channel.id : null;
}

const sendSlackMessage = async (channelId, messageText, token) =>  {
    const response = await axios.post(`https://slack.com/api/chat.postMessage`, {
        channel: channelId,
        attachments: [messageText]
    }, {
        headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json"
        }
    });
    if(!response.data.ok) console.log("error in slack API chat.postMessage>>>", response.data.error);
    return response.data.ok ? response.data : null;
}

router.get('/getSlackChannels', async (req,res) => {
    try {
        if (req.query.token) req.query.token = commonFunctions.encryptDecryptCreds(req.query.token, 'decrypt');
        let accessToken = req.query.token;
        let data;
        if (req.query.platform === 'zendesk') {
            data = await getDetailsDb(req.query.ahId, req);
        } else {
            data = await getAccessToken(req.query.uid, req);
        }
        if (!data.enabled) // if slack settings disabled
            res.send({error: 'Slack Disabled'}).status(200);
        else if (accessToken && accessToken.length) { // if authorised
            let response = await getSlackChannels(req, accessToken);
            response.channels = response.channels.filter((channel) =>  channel.is_member)
            if(response.channels) {
                response.channels = response.channels.map((channel) => {return {id: channel.id, name: channel.name}});
                res.send(response);
            } else res.send(response);
        }
        else if (data.clientId) // if not authorised send client id for authorisation
            res.send({...data, error: 'Not Authenticated'}).status(200);
        else res.send({error: 'Not Authenticated'}).status(200); // no client id secret saved
    } catch(e) {
        commonFunctions.errorlogger.error(e);
        return res.send({error: 'Something went wrong'});
    }
})

function extractParams(url) {
    const platformStart = url.indexOf("platform=") + "platform=".length;
    const platformEnd = url.indexOf("ahId=", platformStart);
    const platform = platformEnd !== -1 ? url.substring(platformStart, platformEnd) : null;

    const ahIdStart = url.indexOf("ahId=") + "ahId=".length;
    const ahId = ahIdStart !== -1 ? url.substring(ahIdStart) : null;

    return { platform, ahId };
}

router.get('/slackAuth', async (req, res) => {
    const { platform, ahId } = extractParams(req.query.state);

    try {
        let data = {};
        if(platform === "zendesk"){
            data = await getDetailsDb(ahId, req);
        }else{
            data = await getAccessToken(req.query.state.split('url=')[0], req);
        }
        // const data = await getAccessToken(req.query.state.split('url=')[0], req);
        if(data.clientId && data.clientSecret) {
            let response = await getSlackToken({ ...data,
                code: req.query.code
            });
            response = response.authed_user;
            if (!response.access_token) return res.render('fail.ejs');
            response.access_token = commonFunctions.encryptDecryptCreds(response.access_token, 'encrypt');
            if (platform === 'zendesk') {
              res.render('success-ah-client.ejs', { id: req.query.state.split('url=')[1].split('platform')[0]+'token='+response.access_token });
            } else {
              res.render('success.ejs', { id: req.query.state.split('url=')[1]+'token='+response.access_token });
            }
        } else {
            res.render('fail.ejs');
        }
    } catch(e) {
        commonFunctions.errorlogger.error(e);
        res.render('fail.ejs');
    }
});

router.post('/postMessageOnSlack', async(req, res) => {
    try {
        if (!req.body.uid) return res.send('Insufficient Data');
        let data;
        if(req.body.platform === "zendesk"){
            data = await getDetailsDb(req.body.ahId, req);
        }else{
            data = await getAccessToken(req.body.uid, req);
        }
        if (!data.enabled) // if slack settings disabled
            return res.send({error: 'Slack Disabled'}).status(200);
        else if (req.body.token) {
            req.body.token = commonFunctions.encryptDecryptCreds(req.body.token, 'decrypt');
            const response = await postMessageOnSlack({
                    token: req.body.token,
                    ...req.body
                });
            if (response.ok) return res.send({message: 'Message posted'}).status(200);
            else return res.send({error: 'An error occured while posting to slack'}).status(200);
        }
        return res.send({error: 'Not Authenticated'}); // no client id secret saved
    } catch(e) {
        commonFunctions.errorlogger.error(e);
        return res.send({error: 'Something went wrong'});
    }
});

router.post("/slack/share-message", validateSlackMessage, async (req,res)=>{
    try{

        const slackCredsApi = await getSlackCreds(req.body.uid, req);
        
        if (slackCredsApi.status !== 200) { //error in fetching the slack creds 
            return res.status(slackCredsApi.status).json({ message: slackCredsApi.message });
        }


        if (!req.body.token) { //we dont have the token return slack creds for the authorization
            return res.status(401).json({
                message: "Not Authenticated",
                data: { slackCreds: slackCredsApi.data },
            });
        }
        
        const token = commonFunctions.encryptDecryptCreds(req.body.token, "decrypt");

        const userEmail = req.body.email;
        const messageToPost = req.body.messageToPost;
        const pretextIfLong = req.body.preTextIflong;

        // Split message if necessary
        const messages = splitLongMessage(messageToPost, pretextIfLong);

        const userId = await fetchSlackUserId(userEmail, token);
        if (!userId) {
            return res.status(400).json({ message: "Failed to fetch Slack user ID with email" });
        }

        const channelId = await openSlackDM(userId, token);
        if (!channelId) {
            return res.status(400).json({ message: "Failed to open Slack DM channel" });
        }

        // Send all message parts sequentially
        const messageResponses = [];
        for (const message of messages) {
            const messageResponse = await sendSlackMessage(channelId, message, token);
            if (!messageResponse) {
                return res.status(400).json({ 
                    message: "Failed to send message on Slack", 
                    partialSuccess: messageResponses.length > 0
                });
            }
            messageResponses.push(messageResponse);
        }

        return res.status(200).json({
            message: `Message${messages.length > 1 ? 's' : ''} posted on Slack successfully`,
            data: messageResponses
        });
    } catch (e) {
        commonFunctions.errorlogger.error(e);
        return res.status(500).json({ error: "Something went wrong" });
    }
});

module.exports = {
    router
}