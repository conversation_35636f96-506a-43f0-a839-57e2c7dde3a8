var request = require("request");
var commonFunctions = require("../../utils/commonFunctions");
var express = require("express");
var router = express.Router();
var multipart = require("connect-multiparty");
var multipartMiddleware = multipart();
const emailTemplates = require("../emailTemplates");
const searchunifyEmail = require("../../Lib/email");
const {
    tenantGlobals,
} = require("../../utils/kafka/kafkaPublishers/tenantGlobals");

const { getUserInfo } = require("auth-middleware");
config = require("config");
const aes256 = require('nodejs-aes256');
const qs = require("qs");
const urlRegex = /^(?:https?|ftp):\/\/(?:\w+\.?)+(?:[/?#]\S*)?$/i;
const {NodeHtmlMarkdown} = require('node-html-markdown');
const { urlValidityCheck } = require('../../utils/commonFunctions');

//Claude subscription route
router.post("/llm-subscriptions", async (req, res) => {

    let payload = {
            tenantId: req.headers["tenant-id"],
            api: `${req.protocol}://${req.get('host')}${req.originalUrl}`,
            appId: req.body.appId,
            type:req.body.type
    };

    const statusPageUrl = config.get("statusPageService.url");
    const secretKey = config.get("statusPageService.secretKey");
    const secretAuthToken = config.get("statusPageService.secretAuthToken");
    let encryptedData = aes256.encrypt(secretKey, JSON.stringify(payload));

    let method = 'POST';
    let url =  `${statusPageUrl}/admin/llm-subscriptions/enabled`
    let headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        authorization: secretAuthToken,
        tenant: payload.tenantId,
    };
    const data = qs.stringify({ _s: encryptedData });
   
    commonFunctions.httpRequest(
        method,
        url,
        "",
        data,
        headers,
        function (error, body) {
            if (error) {
                res.send({ status: "400", data: error || body });
            } else {
                if(body.status === 200 && body.response === 'Client does not exist'){
                    res.send({ status: "200", data: 'Client does not exist' });
                }
                else{
                    res.send({ status: "200", data: JSON.parse(
                        aes256.decrypt(secretKey , body._s)
                    ) });
                }     
            }   
        }
    );
});

const appVariables = require('../../constants/appVariables');

const mlCoreURL = appVariables.ml.coreURL

router.post("/updateAnnotation", multipartMiddleware, function (req, res) {
    const workbenchBody = {
        synonyms: req.body.synonyms,
        client_id: req.body.client_id,
        annotation_id: req.body.annotation_id,
    };
    let method = req.method; //'POST'
    let url = mlCoreURL + "/api/ml/synonyms/create-update";
    let headers = {
        "Content-Type": "application/json",
    };

    commonFunctions.httpRequest(
        method,
        url,
        "",
        workbenchBody,
        headers,
        function (error, body) {
            console.log("url", url);
            console.log("headers", headers);
            if (error) {
                res.send({ status: "400", data: error });
            } else {
                res.send({ status: "200", data: body });
            }
        }
    );
});

router.post("/getMlWorkbench", multipartMiddleware, async (req, res) => {
    const isValid = await urlValidityCheck(req.body.data);
    if(!isValid){
        return res.send({ status: "400", data: 'Invalid URL' });
    }

    if(req.body.isUrl == '1' && !commonFunctions.isValidAndSecureUrl(req.body.data)){
        return res.send({ status: "400", data: { message: "Invalid URL" } });
    }
    const workbenchBody = {
        data: req.body.data,
        isUrl: req.body.isUrl,
        tenantId: req.headers["tenant-id"],
        // "timeout": 20000,
    };
    let method = req.method; //'POST'
    let url = mlCoreURL + "/api/ml/playground/rich-snippets";
    let headers = {
        "Content-Type": "application/json",
    };

    commonFunctions.httpRequest(
        method,
        url,
        "",
        workbenchBody,
        headers,
        function (error, body) {
            console.log("url", url);
            console.log("headers", headers);
            if (error || ( body && body.status === 500) ) {
                res.send({ status: "400", data: error || body });
            } else {
                res.send({ status: "200", data: body });
            }
        }
    );
});

  
router.post("/getAnnotationHistory", multipartMiddleware, function (req, res) {
    const workbenchBody = {
        offset: req.body.offset,
        tenantId: req.headers["tenant-id"],
        // "timeout": 20000,
    };
    let method = req.method; //'POST'
    let url =
        mlCoreURL + "/ml/api/v1/annotation/control-panel/annotation-history";
    let headers = {
        "Content-Type": "application/json",
    };

    commonFunctions.httpRequest(
        method,
        url,
        "",
        workbenchBody,
        headers,
        function (error, body) { 
            if (error || ( body && body.status === 500) ) {
                res.send({ status: "400", data: error || body });
            } else {
                res.send({ status: "200", data: body });
            }
        }
    );
});

router.post("/configure", multipartMiddleware, function (req, res) {
    const workbenchBody = {
        custom_flag: req.body.custom_flag,
        search_flag: req.body.search_flag,
        values: req.body.values,
        client_id: req.body.client_id,
        annotation_id: req.body.annotation_id,
    };
    let method = req.method; //'POST'
    let url = mlCoreURL + "/api/ml/synonyms/configure";
    let headers = {
        "Content-Type": "application/json",
    };

    commonFunctions.httpRequest(
        method,
        url,
        "",
        workbenchBody,
        headers,
        function (error, body) {
            console.log("url", url);
            console.log("headers", headers);
            if (error) {
                res.send({ status: "400", data: error });
            } else {
                res.send({ status: "200", data: body });
            }
        }
    );
});

router.post("/testContent", multipartMiddleware, function (req, res) {
    const workbenchBody = {
        data: req.body.data,
        values: req.body.values,
        tenantId: req.headers["tenant-id"],
    };
    let method = req.method; //'POST'
    let url = mlCoreURL + "/api/ml/playground/annotation";
    let headers = {
        "Content-Type": "application/json",
    };

    commonFunctions.httpRequest(
        method,
        url,
        "",
        workbenchBody,
        headers,
        function (error, body) {
            res.send(body);
        }
    );
});

router.post("/getResetThreshold", multipartMiddleware, function (req, res) {
    const workbenchBody = {
        uid: req.body.uid,
        tenant_id: req.headers["tenant-id"],
        // "timeout": 20000,
    };
    let method = req.method; //'POST'
    let url =
        mlCoreURL + "/ml/api/v1/rich-snippets/control-panel/reset-threshold";
    let headers = {
        "Content-Type": "application/json",
    };

    commonFunctions.httpRequest(
        method,
        url,
        "",
        workbenchBody,
        headers,
        function (error, body) {
            console.log("url", url);
            console.log("headers", headers);
            if (error || ( body && body.status === 500) ) {
                res.send({ status: "400", data: error || body });
            } else {
                res.send({ status: "200", data: body });
            }
        }
    );
});

router.post("/saveThreshold", multipartMiddleware, function (req, res) {
    const workbenchBody = { 
        threshold: req.body.threshold,
        uid: req.body.uid,
        tenant_id: req.headers["tenant-id"],
        // "timeout": 20000,
    };
    let method = req.method; //'POST'
    let url =
        mlCoreURL + "/ml/api/v1/rich-snippets/control-panel/save-threshold";
    let headers = {
        "Content-Type": "application/json",
    };

    commonFunctions.httpRequest(
        method,
        url,
        "",
        workbenchBody,
        headers,
        function (error, body) {
            console.log("url", url);
            console.log("headers", headers);
            if (error || ( body && body.status === 500) ) {
                res.send({ status: "400", data: error || body });
            } else {
                res.send({ status: "200", data: body });
            }
        }
    );
});

router.post("/getSnippetsList", multipartMiddleware, function (req, res) {
    const workbenchBody = {
        tenant_id: req.headers["tenant-id"],
        uid: req.body.uid,
        threshold: req.body.threshold,
        query: req.body.query,
        metadata_ml: req.body.metadata_ml,
        // "timeout": 20000,
    };
    let method = req.method; //'POST'
    let url = mlCoreURL + "/ml/api/v1/rich-snippets/control-panel/snippets";
    let headers = {
        "Content-Type": "application/json",
    };

    commonFunctions.httpRequest(
        method,
        url,
        "",
        workbenchBody,
        headers,
        function (error, body) {
            console.log("url", url);
            console.log("headers", headers);
            if (error || ( body && body.status === 500) ) {
                res.send({ status: "400", data: error || body });
            } else {
                res.send({ status: "200", data: body });
            }
        }
    );
});


router.post("/getKeywordsForSelectedRecord", multipartMiddleware, function (req, res) {
    const workbenchBody = {
        tenant_id: req.headers["tenant-id"],
        metadata_ml: req.body.metadata_ml
        // "timeout": 20000,
    };
    let method = req.method; //'POST'
    let url = mlCoreURL + "/ml/api/v1/rich-snippets/control-panel/keyword";
    let headers = {
        "Content-Type": "application/json",
    };

    commonFunctions.httpRequest(
        method,
        url,
        "",
        workbenchBody,
        headers,
        function (error, body) { 
            if (error || ( body && body.status === 500) ) {
                res.send({ status: "400", data: error || body });
            } else {
                res.send({ status: "200", data: body });
            }
        }
    );
});

router.put("/deleteRichSnippet", multipartMiddleware, function (req, res) {
    const workbenchBody = {
        tenant_id: req.headers["tenant-id"],
        cs_type_id: req.body.cs_type_id,
        object_name: req.body.object_name,
        index: req.body.index,
        id: req.body.id,
        metadata_ml: req.body.metadata_ml,
        // "timeout": 20000,
    };
    let method = req.method; //'POST'
    let url = mlCoreURL + "/ml/api/v1/rich-snippets/control-panel/delete";
    let headers = {
        "Content-Type": "application/json",
    };

    commonFunctions.httpRequest(
        method,
        url,
        "",
        workbenchBody,
        headers,
        function (error, body) { 
            if (error || ( body && body.status === 500) ) {
                res.send({ status: "400", data: error || body });
            } else {
                res.send({ status: "200", data: body });
            }
        }
    );
});

    router.post("/getAnnotationStrength", multipartMiddleware, function (req, res) {
        const workbenchBody = {
            tenantId: req.headers["tenant-id"],
            annotation_id: req.body.annotation_id,
            threshold: req.body.threshold,
            strong_entity_rule_flag: req.body.strong_entity_rule_flag,
            data: req.body.data,
            // "timeout": 20000,
        };
        let method = req.method; //'POST'
        let url =
            mlCoreURL + "/ml/api/v1/annotation/control-panel/fetch-strength";
        let headers = {
            "Content-Type": "application/json",
        };

        commonFunctions.httpRequest(
            method,
            url,
            "",
            workbenchBody,
            headers,
            function (error, body) { 
                if (error || ( body && body.status === 500) ) {
                    res.send({ status: "400", data: error || body });
                } else {
                    res.send({ status: "200", data: body });
                }
            }
        );
    });

    router.post("/saveAnnotationStrength", multipartMiddleware, function (req, res) {
        const workbenchBody = {
            tenantId: req.headers["tenant-id"],
            annotation_id: req.body.annotation_id,
            threshold: req.body.threshold,
            strong_entity_rule_flag: req.body.strong_entity_rule_flag,
            data: req.body.data,
            // "timeout": 20000,
        };
        let method = req.method; //'POST'
        let url =
            mlCoreURL + "/ml/api/v1/annotation/control-panel/save-strength";
        let headers = {
            "Content-Type": "application/json",
        };

        commonFunctions.httpRequest(
            method,
            url,
            "",
            workbenchBody,
            headers,
            function (error, body) { 
                if (error || ( body && body.status === 500) ) {
                    res.send({ status: "400", data: error || body });
                } else {
                    res.send({ status: "200", data: body });
                }
            }
        );
    });

    router.get("/getSaveAnnotationThreshold", multipartMiddleware, function (req, res) {
        const workbenchBody = {
            tenantId: req.headers["tenant-id"],
            annotation_id: req.query.annotation_id,
        };
        let method = req.method; //'POST'
        let url =
            mlCoreURL + "/ml/api/v1/annotation/control-panel/threshold";
        let headers = {
            "Content-Type": "application/json",
        };

        commonFunctions.httpRequest(
            method,
            url,
            "",
            workbenchBody,
            headers,
            function (error, body) { 
                if (error || ( body && body.status === 500) ) {
                    res.send({ status: "400", data: error || body });
                } else {
                    res.send({ status: "200", data: body });
                }
            }
        );
    });

    /** LLM routes - START */

    /* Get LLM Config */
    router.get("/llm-config", async (req, res) => {
        if (!req.headers["tenant-id"]) {
            return res.send({
                success: false,
                message: "Invalid Request",
                data: null,
            });
        }
        const sql = "SELECT gpt FROM tenant_globals";
        connection[req.headers["tenant-id"]].execute.query(sql, [], (err, rows) => {
            if (err || !rows || !rows.length) {
                console.log(rows);
                commonFunctions.errorlogger.error(
                    err || "no data found in tenant_globals"
                );
                return res.send({
                    success: false,
                    message: "Failed fetching LLM Configuration",
                    data: null,
                });
            }
            res.send({
                success: true,
                message: "Successfuly fetched LLM Configuration",
                data: JSON.parse(rows[0].gpt),
            });
        });
    });
/* Get LLM Config */
router.get("/llm-config", async (req, res) => {
    if (!req.headers["tenant-id"]) {
        return res.send({
            success: false,
            message: "Invalid Request",
            data: null,
        });
    }
    const sql = "SELECT gpt FROM tenant_globals";
    connection[req.headers["tenant-id"]].execute.query(sql, [], (err, rows) => {
        if (err || !rows || !rows.length) {
            console.log(rows);
            commonFunctions.errorlogger.error(
                err || "no data found in tenant_globals"
            );
            return res.send({
                success: false,
                message: "Failed fetching LLM Configuration",
                data: null,
            });
        }
        res.send({
            success: true,
            message: "Successfuly fetched LLM Configuration",
            data: JSON.parse(rows[0].gpt),
        });
    });
});

/*Fetching the search clients having the llm configured with them */
router.get("/get-llm-sc", async (req, res) => {
    if (!req.headers["tenant-id"]) {
        return res.send({
            success: false,
            message: "Invalid Request",
            data: null,
        });
    }
    const sql = "SELECT gpt, name FROM search_clients";
    connection[req.headers["tenant-id"]].execute.query(sql, [], (err, rows) => {
        if (err || !rows || !rows.length) {
            console.log(rows);
            commonFunctions.errorlogger.error(
                err || "no data found in search_clients"
            );
            return res.send({
                success: false,
                message: "Failed fetching Search Clients",
                data: null,
            });
        }
        res.send({
            success: true,
            message: "Successfuly fetched Search Clients with LLMs added",
            data: rows,
        });
    });
});

    /* Save LLM Config */
    router.post("/llm-config", (req, res) => {
        const { integrations, currentIntegration } = req.body;
        if (!integrations || !integrations.length || !req.headers["tenant-id"]) {
            return res.send({
                success: false,
                message: "Invalid Request",
                data: null,
            });
        }

        var today = new Date();
        var dd = String(today.getDate()).padStart(2, '0');
        var mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
        var yyyy = today.getFullYear();
        var hours = String(today.getHours()).padStart(2, '0');
        var minutes = String(today.getMinutes()).padStart(2, '0');
        var lastActivated =  yyyy + '-' + mm + '-' + dd + ' '+  hours + ':' + minutes;

        /* global flag ( if at least one is avtive ) */
        const gpt = { integrations };
        gpt.integrations = gpt.integrations.map((integration)=>{
            if(currentIntegration === integration.name && integration.active){
                return { ...integration, lastActivated : lastActivated }
            }
            return integration;
        })

        gpt.active = !!gpt.integrations.find((v) => v.active);
        /* mysql update */
        const sql = "UPDATE tenant_globals set gpt=?";
        connection[req.headers["tenant-id"]].execute.query(
            sql,
            [JSON.stringify(gpt)],
            (err) => {
                if (err) {
                    commonFunctions.errorlogger.error(
                        err || "no data found in tenant_globals"
                    );
                    return res.send({
                        success: false,
                        message: "Failed updating LLM Configuration",
                        data: null,
                    });
                }
                /* kafkaPublisher */
                tenantGlobals(req.headers["tenant-id"], { gpt });
                res.send({
                    success: true,
                    message: "LLM Configuration Updated",
                    data: null,
                });
            }
        );
    });

    /**
     * informAdmins - informs admin about the fact that global llm flag has been turned off
     * @param {string} tenantId - tenant ID
     * @returns {void}
     */
    const informAdmins = async (tenantId,type) => {
        // fetching allAdmins Email for the tenant
        let body = {
            selectedFields: {
                user: ["user_email"],
            },
            filter: {
                tenants: {
                    tenant_id: tenantId,
                },
                roles: {
                    hierarchy: [1, 4],
                },
            },
        };
        const response = await getUserInfo(body);
        response &&
            response.data.forEach((element) => {
            if(type === 'claude'){
                searchunifyEmail.sendEmail(
                    element.user_email,
                    "Authentication error from Claude for LLM integration",
                    emailTemplates.llmEmailTemplateClaude(element.user_email),
                    (sent) => {
                        if (!sent) {
                            return commonFunctions.errorlogger.error(
                                "LLM FLAG TURNED OFF | Error sending email"
                            );
                        }
                        commonFunctions.errorlogger.info(
                            "LLM FLAG TURNED OFF | Email Sent"
                        );
                    }
                );
            }
            else if(type === 'openai'){
                    searchunifyEmail.sendEmail(
                        element.user_email,
                        "Authentication error from Openai for LLM integration",
                        emailTemplates.llmEmailTemplate(element.user_email),
                        (sent) => {
                            if (!sent) {
                                return commonFunctions.errorlogger.error(
                                    "LLM FLAG TURNED OFF | Error sending email"
                                );
                            }
                            commonFunctions.errorlogger.info(
                                "LLM FLAG TURNED OFF | Email Sent"
                            );
                        }
                    );
            }else if(type === 'gemini'){
                searchunifyEmail.sendEmail(
                    element.user_email,
                    "Authentication error from Gemini for LLM integration",
                    emailTemplates.llmEmailTemplateGemini(element.user_email),
                    (sent) => {
                        if (!sent) {
                            return commonFunctions.errorlogger.error(
                                "LLM FLAG TURNED OFF | Error sending email"
                            );
                        }
                        commonFunctions.errorlogger.info(
                            "LLM FLAG TURNED OFF | Email Sent"
                        );
                    }
                );
        }
            });
    };

    /**
     * gptReqOptions - prepare ML request options
     * @param {Request} req - Request Object
     * @returns
     */
    const gptReqOptions = (req , type) => {
        let  invalidRequest;
        if(type=='summary'){
            const{doc_id , query , search_payload} = req.body;
            invalidRequest = 
            !doc_id  ||  !req.headers["tenant-id"] || !search_payload  || !req.headers["uid"];
            console.log(doc_id , query , req.headers["tenant-id"]  , req.headers["uid"] , 'invalidRequest =======>>>>>>>');
            if(invalidRequest)return null
            
            return {
                timeout: 12000,
                url: req.headers["mlClusterIp"] + "/summary/document-summary",
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "tenant-id": req.headers["tenant-id"],
                    "uid": req.headers["uid"],
                    "search-id": req.headers["search-id"],
                    "taid-device": req.headers["taid-device"],
                    "sid-session": req.headers["sid-session"]
                },
                body: req.body,
                json: true,
            };
            
            
        }else{
            const { query } = req.body;
            invalidRequest =
            !query ||
            !req.headers["tenant-id"] ||
            !req.headers["uid"];
        if (invalidRequest) return null;
        const headers = {
            "Content-Type": "application/json",
            "tenant-id": req.headers["tenant-id"],
            uid: req.headers["uid"],
            
            "search-id": req.headers["search-id"],
            "taid-device": req.headers["taid-device"],
            "sid-session": req.headers["sid-session"]
        }
        if(req.headers["authorization"]){
            headers["Authorization"] = req.headers["authorization"]
        }
        if(req.headers["origin"]){
            headers["origin"] = req.headers["origin"]
        }
        return {
            timeout: 12000,
            url: req.headers["mlClusterIp"] + "/answer-summary",
            method: "POST",
            headers,
            body: req.body,
            json: true,
        };
        
            
        }
    
    };
    const executeSummaryGpt = (req, res) => {
    const options = gptReqOptions(req , 'summary');
    if (!options) {
        return res.send({
            status: 403,
            success: false,
            message: "Invalid Request",
            data: null,
        });
    }
    let firstChunk = true,
    statusCode;
    request(options)
    .on("error", (error) => {
        console.error("Error in ML Response:", error);
        commonFunctions.errorlogger.error(error);
        res.send({
            status: 500,
            success: false,
            message: "Error in ML Response",
            data: null,
        });
    })
    .on("data", (mlResponse) => {
        if (firstChunk) {
            const pattern = /"status":\s*(\d+)/;
            statusCode = pattern.exec(mlResponse.toString()) ? pattern.exec(mlResponse.toString())[1] : 404;
            firstChunk = false;
            if (statusCode != 200) {
                res.write(
                    JSON.stringify({
                        status: statusCode,
                        success: false,
                        message: "Unable to generate.",
                        data: null,
                    })
                );
            }
            }

            if (statusCode == 200) {
                console.log("Writing response to client");
                res.write(mlResponse);
            } else if (statusCode == 401) {
                const selectSearchClientData = "SELECT summarize FROM search_clients where uid=?";
                connection[req.headers["tenant-id"]].execute.query(selectSearchClientData, [req.headers["uid"]] , (err, rows) => {
                    if (err || !rows || !rows.length) {
                        commonFunctions.errorlogger.error(
                            err || "no data found in search_clients for summarize feature"
                        );
                    }
            
                });
        }
    })
    .on("complete", () => {
        console.log("Request to ML service completed");
        res.end();
    });
};
router.post("/summary" , executeSummaryGpt)



    const executeGPT = (req, res) => {
        /* ML Request Payload */
        const options = gptReqOptions(req);
        if (!options) {
            return res.send({
                status: 403,
                success: false,
                message: "Invalid Request",
                data: null,
            });
        }

    /* HTTP Request to ML */
    let firstChunk = true,
     emailSent = false,
        statusCode;
    request(options)
        .on("error", (error) => {
            commonFunctions.errorlogger.error(error);
            res.send({
                status: 500,
                success: false,
                message: "Error in ML Response",
                data: null,
            });
        })
        .on("data", (mlResponse) => {
            if (firstChunk) {
                const pattern = /"status":\s*(\d+)/;
                statusCode = pattern.exec(mlResponse.toString()) ? pattern.exec(mlResponse.toString())[1] : 404;
                firstChunk = false;
                if (statusCode != 200) {
                    res.write(
                        JSON.stringify({
                            status: statusCode,
                            success: false,
                            message: "Unable to generate.",
                            data: null,
                        })
                    );
                }
                }

                if (statusCode == 200) {
                    res.write(mlResponse);
                    
                } else if (statusCode == 401 && !emailSent) {
                    // statusCode == 429
                    /* TURN OFF gpt global flag(s) */      
                    // VERIFICATION PENDING
                    emailSent = true;
                    const selectSearchClientData = "SELECT gpt FROM search_clients where uid=?";
                    connection[req.headers["tenant-id"]].execute.query(selectSearchClientData, [req.headers["uid"]] , (err, rows) => {
                        if (err || !rows || !rows.length) {
                            commonFunctions.errorlogger.error(
                                err || "no data found in search_clients"
                            );
                        }
                
                        rows.map((row) => {
                            const gptData = JSON.parse(row.gpt);
                            
                            if(gptData.active === true){
                                const selectTenantGlobalsData = "SELECT gpt FROM tenant_globals";
                                connection[req.headers["tenant-id"]].execute.query(selectTenantGlobalsData, (selectErr, results) => {
                                    if (selectErr) {
                                        commonFunctions.errorlogger.error(
                                            selectErr || "Error retrieving data from tenant_globals"
                                        );
                                        return res.send({
                                            success: false,
                                            message: "Failed updating LLM Configuration",
                                            data: null,
                                        });
                                    }
                             
                                    const currentData = results[0] || {};
                                    const parsedData = JSON.parse(currentData.gpt);
                                    const { integrations } = parsedData;
                                    const integrationToStop = integrations.find(
                                        (integration) => integration.name === gptData.name
                                    );
                                    integrationToStop.active = false;
                                    integrationToStop.hasError = true;
                                    parsedData.active = !!parsedData.integrations.find((v) => v.active);
                                    
                                    const updateTenantGlobals = "UPDATE tenant_globals set gpt=?";
                                    connection[req.headers["tenant-id"]].execute.query(
                                        updateTenantGlobals,
                                        [JSON.stringify(parsedData)],
                                        (err) => {
                                            if (err) {
                                                commonFunctions.errorlogger.error(
                                                    err || "no data found in tenant_globals"
                                                );
                                                return res.send({
                                                    success: false,
                                                    message: "Failed updating LLM Configuration",
                                                    data: null,
                                                });
                                            }
                        
                                            /* Publishing the kafka */
                                            tenantGlobals(req.headers["tenant-id"], { parsedData });
                                            informAdmins(req.headers["tenant-id"], gptData.name)
                                            res.send({
                                                success: true,
                                                message: "LLM Configuration Updated",
                                                data: parsedData,
                                            });
                                        }
                                    );
             
                                });
                            }
                        });
                    });
            }
        })
        .on("complete", () => {
            res.end();
        });
};
    /**
     * /mlService/su-gpt | POST | serves su gpt text / stream
     */
    router.post("/su-gpt",executeGPT);

// Middleware for basic validation
const validateHeaders = (req, res, next) => {
    const tenantId = req.headers["tenant-id"];
    const uid = req.headers["uid"];
    if (!tenantId || !uid) {
        return res.status(401).send({
            status: 401,
            response: { success: false, message: "Invalid Request", data: null }
        });
    }
    req.body.tenant_id = tenantId;
    req.body.uid = uid;
    next();
};

const agentHlperSummary = (req) => {
    const { description, subject } = req.body;
    // Check if either description or subject is provided
    if (!description && !subject) {
        console.error("Invalid request: Description or subject must be provided.");
        return null;
    }

    // Construct the request options for the ML service
    req.body["service"] = "agenthelper"
    return {
        timeout: 400000, // Set a timeout for the request
        url: config.get('MLService.url') + "/case-summary", // Construct the URL for the ML service
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "tenant-id": req.headers["tenant-id"], // Include tenant-id from the request headers
            uid: req.headers["uid"], // Include uid from the request headers
        },
        body: req.body, // Include the modified body
        json: true, // Set the json option to true to automatically stringify the body
    };
};

router.post('/su-gpt-writing', async (req, res) => {
    const payload = {
        url: config.get('MLService.url') + "/ai-editor",
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "tenant-id": req.headers["tenant-id"],
            uid: req.headers["uid"],
        },
        body: {
            ...req.body,
            tenant_id: req.headers["tenant-id"],
            service: "agenthelper"
        },
        json: true,
    };

    let gptResponse;

    try {
        const mlResponse = await makeRequest(payload);
        gptResponse = mlResponse.data;
    }
    catch (err) {
        gptResponse = [];
    }
    res.send({
        success: true,
        message: 'Success',
        data: gptResponse,
    });
});

const executeSQLQuery = async (tenantId, sql, params = []) => {
    return new Promise((resolve, reject) => {
        try {
            // Ensure the connection for the specified tenantId exists
            if (!connection[tenantId]) {
                throw new Error(`No database connection available for tenant ID: ${tenantId}`);
            }
            // Execute the SQL query with parameters
            connection[tenantId].execute.query(sql, params, (err, data) => {
                if (err) {
                    console.error("Error executing SQL query:", err);
                    reject({
                        success: false,
                        message: "Error executing SQL query",
                        error: err,
                    });
                }else{
                    resolve({
                        success: true,
                        message: "Query executed successfully",
                        data: data,
                    });
                }
            });
        } catch (err) {
            console.error("Error executing SQL query:", err);
            reject({
                success: false,
                message: "Error executing SQL query",
                error: err,
            });
        }
    })
};

const firstResponse = (req, tones) => {
    try {
        const body = {
            "tenant_id": req.headers["tenant-id"],
            "custom_model_name": req.body.custom_model_name,
            "subject": req.body.subject,
            "description": req.body.description,
            "case_owner": req.body.case_owner,
            "customer_name": req.body.customer_name,
            "agent_name": req.body.agent_name,
            "agent_tone": tones,
            "search_result": req.body.search_result,
            "uid":req.body.uid,
            "data": {
                "caseId": req.body.caseId,
                "activities": req.body.activities
            },
            "search_token": req.body.token,
            "search_uid": req.body.search_uid,
            "search_access_token": req.body.search_access_token,
            "language": req.body.language || 'english',
            "charLimit": config.get('agentHelper.charLimitRA') ? config.get('agentHelper.charLimitRA') : 5000
        }
        return {
            timeout: 400000,
            url: config.get('MLService.url') + "/first-response",
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "tenant-id": req.headers["tenant-id"],
                uid: req.headers["uid"],
            },
            body: body,
            json: true,
        };
    } catch (error) {
        console.error("Error in getting ML response", error);
    }
};

const getActiveLLM = async (tenantId, uid) => {
    const sql = "SELECT gpt FROM search_clients where uid = ?";
    const result = await executeSQLQuery(tenantId, sql, [uid]);
    let rows = [];
    if (result.success && result.data && result.data.length) {
        rows = JSON.parse(JSON.stringify(result.data));
        if (rows.length && rows[0].hasOwnProperty("gpt") && typeof rows[0].gpt === "string") {
            data = JSON.parse(rows[0].gpt);
            if (data.name && data.active) {
                return data;
            } else {
                throw new Error("Error in getting search client llm configuration");
            }
        } else {
            throw new Error("Error in getting search client llm configuration");
        }
    } else {
        throw new Error("Error in getting search client llm configuration");
    }
};


/**
 * Checks if LLM (Language Model) is configured/activated for a specific tenant.
 * @param {Object} req - The request object containing headers.
 * @returns {Promise} A promise that resolves if LLM is configured/activated, and rejects with an error response if not.
 */
const checkLlmConfig = async (req, name) => {
    const errorResponse = {
        status: 403,
        success: false,
        message: "LLM is not Configured/activated.",
        data: null,
    };

    try {
        const sql = "SELECT gpt FROM tenant_globals";
        const result = await executeSQLQuery(req.headers["tenant-id"], sql, [])
        let rows = [];
        if (result.success && result.data && result.data.length) {
            rows = JSON.parse(JSON.stringify(result.data));
        } else {
            throw errorResponse;
        }
        const isLlmActive = rows.some((item) =>
            JSON.parse(item.gpt).integrations.some((ai) =>
                ai.name === name && ai.active === true
            )
        );

        if (!isLlmActive) {
            throw errorResponse;
        } else {
            return true;
        }

    } catch (err) {
        if (err.status) {
            throw err; // Re-throw custom error responses
        }
        commonFunctions.errorlogger.info("Error :", err);
        throw {
            status: 500,
            success: false,
            message: "Internal Server Error",
            data: null,
        };
    }
};

const checkLlmConfigMiddleware = async (req, res, next) => {
    const errorResponse = {
        success: false,
        message: "LLM is not Configured/activated.",
        data: null,
    };

    try {
        const sql = "SELECT gpt FROM tenant_globals";
        const result = await executeSQLQuery(req.headers["tenant-id"], sql, []);

        let rows = [];
        if (result.success && result.data && result.data.length) {
            rows = JSON.parse(JSON.stringify(result.data));
        } else {
            return res.status(403).json(errorResponse);
        }

        const isLlmActive = rows.some((item) =>
            JSON.parse(item.gpt).integrations.some((ai) =>
                ai.name === 'openai' && ai.active === true
            )
        );

        if (!isLlmActive) {
            return res.status(403).json(errorResponse);
        } else {
            next();
        }

    } catch (err) {
        commonFunctions.errorlogger.info("Error checkLlmConfigMiddleware :", err);
        return res.status(500).json({
            success: false,
            message: "Internal Server Error",
            data: null,
        });
    }
};


const getCaseSummaryCache = async (tenantId, caseId, uid ) => {
    try {
        const sql = "SELECT params, brief_response, detailed_response, last_updated FROM ah_case_summary_cache WHERE tenant_id = ? AND case_id = ? AND uid = ?";
        const params = [
            tenantId,
            caseId,
            uid
        ];
        const result = await executeSQLQuery(tenantId, sql, params)
        if (result.success) {
            return result.data;
        } else {
            return [];
        }
    } catch (err) {
        console.error("Error retrieving case summary cache:", err);
        return []; // Return an empty array in case of error to handle gracefully
    }
};


const getFirstResponseCache = async (req, caseTone) => {
    try {
        const sql = "SELECT params, ml_response, last_updated, response_type, current_language, detected_languages FROM ah_first_response_cache where tenant_id = ? AND case_id = ? AND uid = ? AND tone_id = ? AND is_custom_tone = ?";
        const params = [
            req.headers["tenant-id"],
            req.body.caseId,
            req.body.uid,
            (caseTone.length > 0 && caseTone[0].hasOwnProperty('selected_tone_id')) ? caseTone[0].selected_tone_id : null,
            (caseTone.length > 0 && caseTone[0].hasOwnProperty('is_custom')) ? caseTone[0].is_custom : null,
        ]
        const result = await executeSQLQuery(req.headers["tenant-id"], sql, params);

        if (result.success) {
            return result.data;
        } else {
            return [];
        }
    } catch (err) {
        console.error(err);
        return [];
    }
};

const saveSummaryCache = async (req, breifData, detailedData, last_updated, queryType) => {
    try {
        const params_data = {
            comments: req.body.comments,
            description: req.body.description,
            subject: req.body.subject
        };

        const sql = "INSERT INTO ah_case_summary_cache (tenant_id, case_id, uid, params, brief_response, detailed_response, last_updated) VALUES (?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE params = VALUES(params), brief_response = VALUES(brief_response), detailed_response = VALUES(detailed_response), last_updated = VALUES(last_updated)";

        const params = [
            req.body.tenant_id,
            req.body.case_id,
            req.body.uid,
            JSON.stringify(params_data),
            JSON.stringify(breifData),
            JSON.stringify(detailedData),
            last_updated
        ];

        const result = await executeSQLQuery(req.headers["tenant-id"], sql, params);
        if (result.success) {
            return result.data;
        } else {
            console.error("Error saving summary cache:", result.error);
            throw new Error(result.error)
        }
    } catch (err) {
        console.error("Error saving summary cache:", err);
        throw err; // Rethrow the error to be handled by the caller
    }
};

function formatLanguages(body, oldLanguages = []) {
    const newLanguages = body.detected_lang ? body.detected_lang.map(lang => lang.toLowerCase()) : [];
    try {
        oldLanguages = JSON.parse(oldLanguages).map(lang => lang.toLowerCase());
        if (!Array.isArray(oldLanguages)) throw new Error();
    } catch {
        console.error("Cannot parse JSON variable");
        oldLanguages = [];
    }

    const combinedLanguages = [...new Set([...oldLanguages, ...newLanguages])];
    if (!combinedLanguages.includes('english')) return combinedLanguages;
    
    return ['english', ...combinedLanguages.filter(lang => lang !== 'english')];
};


const saveFirstResponseCache = async (req, last_updated, data, userTone, detectedLanguages = '[]') => {
    try {
        const tenantId = req.headers["tenant-id"];
        const caseId = req.body.caseId;
        const uid = req.body.uid;
        const userId = req.body.user_id;
        const mlData = JSON.stringify(data.output || {});
        const language = data.current_lang ? data.current_lang : 'english';

        const params_data = {
            agent_name: req.body.agent_name,
            description: req.body.description,
            subject: req.body.subject,
            case_owner: req.body.case_owner,
            activities: req.body.activities
        };

        const sql = `
            INSERT INTO ah_first_response_cache 
            (tenant_id, case_id, uid, user_id, params, ml_response, last_updated, tone_id, is_custom_tone, response_type, current_language, detected_languages) 
            VALUES (?,?,?,?,?,?,?,?,?,?,?,?) 
            ON DUPLICATE KEY UPDATE 
                params = VALUES(params), 
                ml_response = VALUES(ml_response), 
                last_updated = VALUES(last_updated), 
                tone_id = VALUES(tone_id), 
                is_custom_tone = VALUES(is_custom_tone),
                response_type = VALUES(response_type),
                current_language = VALUES(current_language), 
                detected_languages = VALUES(detected_languages)`;

        const params = [
            tenantId,
            caseId,
            uid,
            userId,
            JSON.stringify(params_data),
            mlData,
            last_updated,
            (userTone.length > 0 && userTone[0].hasOwnProperty('selected_tone_id')) ? userTone[0].selected_tone_id : 1,
            (userTone.length > 0 && userTone[0].hasOwnProperty('is_custom')) ? userTone[0].is_custom : null,
            data.response_type,
            language,
            detectedLanguages
        ];

        const result = await executeSQLQuery(tenantId, sql, params);

        if (result.success) {
            return result.data;
        } else {
            console.error("Error saving first response cache: ", result.error);
            throw new Error(result.error);
        }
    } catch (err) {
        console.error("Error saving first response cache:", err);
        throw err;
    }
};

const updateFirstResponseCache = async (req) => {
    const last_updated = new Date().toISOString();
    try {
        const sql = 'UPDATE ah_first_response_cache SET ml_response = ?, last_updated = ?, current_language = ? WHERE tenant_id = ? AND case_id = ? AND uid = ? AND user_id = ?';

        const params = [
            JSON.stringify(req.body.updated_response),
            last_updated,
            req.body.language,
            req.headers["tenant-id"],
            req.body.caseId,
            req.body.uid,
            req.body.user_id
        ];
        const result = await executeSQLQuery(req.headers["tenant-id"], sql, params);
        if (result.success) {
            return {updatedAt: last_updated};
        } else {
            console.error("Error updating first response cache: ", result.error);
            throw new Error(result.error)
        }
    } catch (err) {
        console.error("Error updating first response cache:", err);
        throw err;
    }
}

/**
 * Function to format the summary response from the LLM.
 * @param {Object} summaryCache - The summary cache object containing the LLM response.
 * @param {Boolean} updated - A flag indicating whether the response has been updated.
 * @param {String} requestType - The type of summary response requested.
 * @returns {Object} - An object containing the formatted summary response.
 */
const formatSummaryResponse = (summaryCache, updated) => {
    let result = {
        status: 200,
        updated: updated,
        output: null,
        last_updated: null
    };

    try {
        result.brief_response = JSON.parse(summaryCache.brief_response);
        result.detailed_response = JSON.parse(summaryCache.detailed_response);

        result.updatedAt = summaryCache.last_updated;
    } catch (error) {
        console.error("Error formatting summary response:", error);
        result.status = 500;
        result.error = "Failed to parse response data";
    }

    return result;
};

const formatFirstResponse = (firstResponseCache, updated) => {
    const result = {
        status: 200,
        updated: updated,
    };
    try {
        result["output"] = JSON.parse(firstResponseCache.ml_response);
    } catch (error) {
        result["output"] = "";
    }
    result.response_type = firstResponseCache.response_type;
    result.updatedAt = firstResponseCache.last_updated;
    result.markdownData = htmlToMarkdown(result.output)
    result.language = firstResponseCache.current_language;
    result.detectedLanguages = JSON.parse(firstResponseCache.detected_languages) || [];
    return result;
}


const summaryDataValidator = (req, res, next) => {
    const { description, subject, case_id } = req.body;
    let tempResponse = {
        success: false,
        message: '',
        data: null
    }
    if (!case_id){
        tempResponse.message = "Invalid request: case_id is required."
        return res.status(400).json(tempResponse);
    }else if(!description && !subject) {
        tempResponse.message = "Invalid request: Description or subject must be provided."
        return res.status(400).json(tempResponse);
    }
    next();
};

router.post("/case-summary", validateHeaders,summaryDataValidator, async (req, res, next) => {
    try {
        const activeLLM = await getActiveLLM(req.body.tenant_id, req.body.uid);
        if (activeLLM.name) {
            await checkLlmConfig(req, activeLLM.name);
            req.body.custom_model_name = activeLLM.name;
        }
        req.body.llm = true;
        if(!req.body.isRefreshed){
            let summaryCache = await getCaseSummaryCache(req.body.tenant_id, req.body.case_id , req.body.uid);
            if (summaryCache.length && summaryCache[0].params && summaryCache[0].brief_response && summaryCache[0].brief_response.length && summaryCache[0].detailed_response && summaryCache[0].detailed_response.length) {
                summaryCache = summaryCache[0];
                const temp_params = {
                    comments: req.body.comments,
                    description: req.body.description,
                    subject: req.body.subject
                };

                let result = {}
                let updated = JSON.stringify(temp_params) !== summaryCache.params
                result = formatSummaryResponse(summaryCache, updated);
                return res.status(200).send({ ...result });
            }
        }
        const last_updated = new Date().toISOString();
        let options = agentHlperSummary(req);
        if (!options) {
            return res.status(403).send({
                success: false,
                message: "Invalid Request",
                data: null,
            });
        }
        
        Promise.all([
            makeRequest({...options, body:{...options.body, type: 'brief_summary'}}),
            makeRequest({...options, body:{...options.body, type: 'detailed_summary'}})
        ]).then(responses => {
            const [breifData, detailedData] = responses;  
            saveSummaryCache(req, breifData, detailedData, last_updated, "brief_summary");

            const result = {
                status: 200,
                brief_response: breifData,
                detailed_response: detailedData,
                updated: false,
                updatedAt: last_updated
            };
            return res.status(200).send(result);
        }).catch(error => {
            console.error('Error with one or both requests:', error);
            return res.status(500).send({
                success: false,
                message: "Error in ML Response",
                data: null,
            });
        });
        
    } catch (error) {
        console.error("Error in processing case summary:", error);
        return res.status(500).send({
            success: false,
            message: "Error in ML Response",
            data: null,
        });
    }
});

async function makeRequest(options) {
    console.log("===Request to ML API====", {options});
    return new Promise((resolve, reject) => {
        request(options, (error, response, body) => {
            if (error) {
                console.error("Error: Ml response not success: ", {error, body});
                reject(error);
            }else if(body && !body.success){
                console.error("Error: Ml response not success: ", {error, body});
                reject(new Error(body.error || "Ml response failed, url -->"+ options.url));
            }else {
                console.log("===Response from ML API====", body);
                resolve(body);
            }
        });
    });
}

async function SUSearchResults(req){
    var options = {
        method: 'POST',
        rejectUnauthorized: false,
        url: config.get('searchService.url') + '/search/SUSearchResults',
        headers: {
            'content-type': 'application/json',
            'authorization': req.body.token,
            'origin': req.headers.origin
        },
        body: {
            "searchString": req.body.subject + ' ' + req.body.description,
            "from": 0,
            "sortby": "_score",
            "orderBy": "desc",
            "resultsPerPage": 5,
            "uid": req.headers.uid,
            "app": "agent-helper",
            "contextLimit": 2000,
            "resultsToConsider": 5
        },
        json: true
    };
    return new Promise((resolve, reject) => {
        request(options, (error, response, docs) => {
            if (error || docs.statusCode!== 200 || !docs) {
                reject(error ? error : docs);
            } else {
                let search_result = [];
                if (docs.result && docs.result.hits && docs.result.hits.length) {
                    // search_result = docs.result.hits.filter(obj => typeof obj.href === 'string' && urlRegex.test(obj.href)).map(obj => obj.href);
                    search_result = docs.result.hits.map(obj => obj.href);
                }
                resolve(search_result);
            }
        });
    });
}

async function getUserTone(req) {
    try {
        const sql = "SELECT selected_tone_id, is_custom FROM ah_user_tone where user_id = ? AND uid = ? AND tenant_id = ?";
        const params = [
            req.body.user_id,
            req.headers["uid"],
            req.headers["tenant-id"],
        ];
        const result = await executeSQLQuery(req.headers["tenant-id"], sql, params);

        if (result.success && result.data.length) {
            return result.data;
        } else {
            return [];
        }
    } catch (err) {
        console.error("Error in getting user tone", err);
        return [];
    }
}

async function assignDefaultTone(req) {
    try {
        const sql1 = `INSERT INTO ah_user_tone (user_id, tenant_id, uid, selected_tone_id, is_custom, last_updated) VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE selected_tone_id = VALUES(selected_tone_id);`;
        const params1 = [
            req.body.user_id,
            req.headers["tenant-id"],
            req.body.uid,
            1,
            false,
            new Date()
        ];
        await executeSQLQuery(req.headers["tenant-id"], sql1, params1);
        const sql2 = "SELECT props FROM ah_default_tone where tone_id = ?";
        const tone = await executeSQLQuery(req.headers["tenant-id"], sql2, [1]);
        const propsArray = JSON.parse(tone.data[0].props);
        const toneArray =  propsArray.map(feature => feature.value === 0 ? feature.lowerName : feature.upperName);
        return toneArray;
    } catch (error) {
        console.error("Error in assigning default tone: " + error);
        return [];
    }
}

async function fetchCaseTone(req, userTone) {
    try {
        if(userTone.length){
            userTone = userTone[0];
            const sql = `SELECT props FROM ${userTone.is_custom?'ah_custom_tone':'ah_default_tone'} where tone_id = ?`;
            const params = [
                userTone.selected_tone_id,
            ];
            const tone = await executeSQLQuery(req.headers["tenant-id"], sql, params);
            if(tone.data.length){
                const propsArray = JSON.parse(tone.data[0].props);
                const toneArray = propsArray.map(feature => feature.value === 0 ? feature.lowerName : feature.upperName);
                return toneArray;
            } else {
                console.log("tone not found for given user >> assiging default tone");
                const toneArray = await assignDefaultTone(req);
                return toneArray;
            }
        } else {
            console.log("user not found >> assiging default tone");
            const toneArray = await assignDefaultTone(req);
            return toneArray;
        }
    } catch (error) {
        console.error("Error in fetching case tone", error);
        return [];
    }
}

router.post("/case-sentiments", async (req, res, next) => {
    if (!req.headers["tenant-id"] || !req.headers["uid"]) {
        return res.status(401).send({
            status: 401,
            response: {
                success: false,
                message: "Invalid Request",
                data: null,
            },
        });
    }
    try {
        const activeLLM = await getActiveLLM(req.headers["tenant-id"], req.headers["uid"]);
        if (activeLLM.name) {
            await checkLlmConfig(req, activeLLM.name);
            req.body.custom_model_name = activeLLM.name;
        }
        var options = {
            url: `${config.get("MLService.url")}/get-sentiment`,
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: {
                ...req.body.case, 
                tenant_id: req.headers["tenant-id"],
                custom_model_name: req.body.custom_model_name,
                service: "agenthelper"
            },
            json: true,
        };
        
        request(options, (error, response, body) => {
            if (error  || !body.success){
                console.log("Error in /get-sentiment response from ml: ", {error, body} );
                res.status(500).send({
                    success: false,
                    message: "Unable to generate response!",
                    data: null,
                });
            }else {
                res.status(200).send({
                    ...body,
                    success: true,
                });
            }
        });
    } catch (error) {
        console.log("Error generating response assist Data: " + error.message);
        console.log("Error Stack ====> ", error.stack ? error.stack : error);
        return res.status(500).send({
            status: 500,
            success: false,
            message: "Internal Server Error, Something went Wrong !!",
            data: null,
        });
    }
});

router.post("/first-response", async (req, res, next) => {
    try {
        if (!req.headers["tenant-id"] || !req.headers["uid"] || !req.body.caseId || !req.body.user_id) {
            return res.status(401).send({
                status: 401,
                response: {
                    success: false,
                    message: "Invalid Request",
                    data: null,
                },
            });
        }
        
        req.body.tenant_id = req.headers["tenant-id"];
        req.body.uid= req.headers["uid"];
        req.body.subject = req.body.subject;
        req.body.llm = true;
        const activeLLM = await getActiveLLM(req.body.tenant_id, req.body.uid);
        if (activeLLM.name) {
            await checkLlmConfig(req, activeLLM.name);
            req.body.custom_model_name = activeLLM.name;
        }

        const userTone = await getUserTone(req);
        let firstResponseCache = await getFirstResponseCache(req, userTone);
        if (firstResponseCache.length && firstResponseCache[0].params && firstResponseCache[0].current_language && !req.body.isRefreshed){
            firstResponseCache = firstResponseCache[0]
            const temp_params = {
                agent_name: req.body.agent_name,
                description: req.body.description,
                subject: req.body.subject,
                case_owner: req.body.case_owner,
                activities: req.body.activities
            }
            if (JSON.stringify(temp_params) == firstResponseCache.params) {
                const result = formatFirstResponse(firstResponseCache, false)
                res.status(200).send({...result});
            } else {
                const result = formatFirstResponse(firstResponseCache, true)
                res.status(200).send({...result});
            }
        } else {
            const tones = await fetchCaseTone(req, userTone)
            // const search_result = await SUSearchResults(req);
            // req.body.search_result = search_result;
            req.body.search_result = [];
            const options = firstResponse(req, tones);
            if (!options) {
                return res.send({
                    status: 403,
                    success: false,
                    message: "Invalid Request",
                    data: null,
                });
            }
            const last_updated = new Date().toISOString();
    
            const body = await makeRequest(options)
            if(body && body.output){
                body.output = body.output.split("\n").join("<br>");
            }
            const cacheDetectedLanguages = firstResponseCache.length && firstResponseCache[0].detected_languages ? firstResponseCache[0].detected_languages : []
            const updatedLanguages = formatLanguages(body,cacheDetectedLanguages)
            
            if(req.body.isActiveResponse){
                saveFirstResponseCache(req, last_updated, body, userTone, JSON.stringify(updatedLanguages));
            }
            const result = {
                status: 200,
                output: body.output,
                updated: false,
                updatedAt : last_updated,
                markdownData : htmlToMarkdown(body.output),
                response_type: body.response_type,
                language : body.current_lang,
                detectedLanguages: updatedLanguages
            }
            res.status(200).send(result);
        }
    } catch (error){
        console.log("Error generating response assist Data: " + error.message);
        console.log("Error Stack ====> ", error.stack ? error.stack : error);
        return res.status(500).send({
            status: 500,
            success: false,
            message: "Internal Server Error, Something went Wrong !!",
            data: null,
        });
    }
});

router.post("/update-first-response", async (req, res, next) => {
    if (!req.headers["tenant-id"] || !req.headers["uid"] || !req.body.caseId || !req.body.user_id || !req.body.updated_response) {
        return res.status(401).send({
            status: 401,
            response: {
                success: false,
                message: "Invalid Request",
                data: null,
            },
        });
    }
    req.body.tenant_id = req.headers["tenant-id"];
    req.body.uid= req.headers["uid"];
    updateFirstResponseCache(req).then((response) => {
        const result = {
            status: 200,
            success: true,
            message: "Success",
            updatedAt: response.updatedAt,
            markdownData: htmlToMarkdown(req.body.updated_response)
        }
        res.status(200).send(result);
    }).catch((error) => {
        console.error("Error in Saving Updated response: ", error);
        const errorMessage = {
            status: 500,
            success: false,
            message: "Error in Saving Updated response",
        }
        res.status(500).send(errorMessage);
    });
});



    /** LLM routes - END */

    router.post("/saveKey", multipartMiddleware, function (req, res) {
        const llmBody = {
            type: req.body.type,
            token: req.body.token,
            tenant_id: req.headers["tenant-id"],
            // "timeout": 20000,
        };
        
        if(req.body.creds){
            llmBody.creds = req.body.creds 
        }

        let method = req.method; //'POST'
        let url = mlCoreURL + "/ml/api/v1/save-key";
        let headers = {
            "Content-Type": "application/json",
        };

        commonFunctions.httpRequest(
            method,
            url,
            "",
            llmBody,
            headers,
            function (error, body) {
                console.log("url", url);
                console.log("headers", headers);
                if (error || (body && body.status === 500)) {
                    res.send({ status: "400", data: error || body });
                } else {
                    res.send({ status: "200", data: body });
                }
            }
        );
    });

    router.get("/getKey", multipartMiddleware, function (req, res) {
        const workbenchBody = {
            tenant_id: req.headers["tenant-id"],
        };
        let method = req.method; //'POST'
        let url = mlCoreURL + "/ml/api/v1/get-key";
        let headers = {
            "Content-Type": "application/json",
        };

        commonFunctions.httpRequest(
            method,
            url,
            "",
            workbenchBody,
            headers,
            function (error, body) {
                if (error || (body && body.status === 500)) {
                    res.send({ status: "400", data: error || body });
                } else {
                    res.send({ status: "200", data: body });
                }
            }
        );
    });

    router.post("/removeKey", multipartMiddleware, function (req, res) {
        const llmBody = {
            type: req.body.type,
            tenant_id: req.headers["tenant-id"],
            // "timeout": 20000,
        };
        let method = req.method; //'POST'
        let url = mlCoreURL + "/ml/api/v1/remove-token";
        let headers = {
            "Content-Type": "application/json",
        };

        commonFunctions.httpRequest(
            method,
            url,
            "",
            llmBody,
            headers,
            function (error, body) {
                console.log("url", url);
                console.log("headers", headers);
                if (error || (body && body.status === 500)) {
                    res.send({ status: "400", data: error || body });
                } else {
                    res.send({ status: "200", data: body });
                }
            }
        );
    });

    const getTimelineCache = async (req) => {
        try {
            if (!req.body.data || !req.body.data.caseId) {
                return {
                    status: 401,
                    success: false,
                    message: "Invalid Request",
                    data: null,
                }
            }
            const sql = "SELECT activity_id, ml_response, activity_ts FROM ah_timeline_cache where tenant_id = ? AND case_id = ? AND uid = ? ORDER BY activity_ts ASC";
            const params = [
                req.headers["tenant-id"],
                req.body.data.caseId,
                req.body.uid,
            ]
            const result = await executeSQLQuery(req.headers["tenant-id"], sql, params);
            if (result.success) {
                return result.data;
            } else {
                console.error("Error while fetching case timeline cache: ", result.error);
                return [];
            }
        } catch (err) {
            console.error("Error while fetching case timeline cache: ", err);
            throw new Error("Error while fetching case timeline cache");
        }
    };

    const getCaseMetaData = async (req) => {
        try {
            if (!req.body.data || !req.body.data.caseId) {
                return {
                    status: 401,
                    success: false,
                    message: "Invalid Request",
                    data: null,
                }
            }
            const sql = "SELECT case_id, hashtags, timeline_ml_response, last_updated, payload FROM ah_case_metadata where tenant_id = ? AND case_id = ? AND uid = ?";
            const params = [
                req.headers["tenant-id"],
                req.body.data.caseId,
                req.body.uid,
            ]
            const result = await executeSQLQuery(req.headers["tenant-id"], sql, params);
                if (result.success) {
                    return result.data;
                } else {
                    console.error("Error while fetching case timeline cache: ", result.error);
                    return [];
                }
        } catch (err) {
            console.error("Error while fetching case timeline cache: ", err);
            throw new Error("Error while fetching case timeline cache");
        }
    };

    const timelineMLRequest = (req) => {
        return new Promise((resolve, reject) => {
            try {
                let options = {
                    timeout: 400000,
                    url: config.get('MLService.url') + "/case-timeline",
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "tenant-id": req.headers["tenant-id"],
                        uid: req.headers["uid"],
                    },
                    body: {
                        data: req.body.data,
                        uid: req.headers["uid"],
                        custom_model_name: req.body.custom_model_name,
                        tenant_id:req.headers["tenant-id"],
                        service: "agenthelper"
                    },
                    json: true,
                }
                console.log("request to MLService");
                console.log(JSON.stringify(options));
                request(options, function (error, response, body) {
                    if (error  || body.status !== 200) {
                        reject({
                            status: 500,
                            success: false,
                            message: "Error in ML Response",
                            data: null,
                        });
                    } else {
                        console.log("response from mlservice");
                        console.log(JSON.stringify(body.data));
                        resolve(body);
                    }
                })
            } catch (error) {
                reject({
                    status: 500,
                    success: false,
                    message: "Error in ML Response",
                    data: null,
                });
            }
        });
    };

// Main handler
router.post("/case-timeline", validateHeaders, async (req, res) => {
    try {
        const activeLLM = await getActiveLLM(req.body.tenant_id, req.body.uid);
        if (activeLLM.name) {
            await checkLlmConfig(req, activeLLM.name);
            req.body.custom_model_name = activeLLM.name;
        }
        const caseMetaData = await getCaseMetaData(req);
        const timelineCache = await getTimelineCache(req);
        let responsePayload = {
            hashtags:[],
            activities:[],
            caseId: req.body
            .data.caseId
        };
        let mlActivityPayload = [];
        let caseCreateOrigin = 'ml';
        let createActivityPayload = await createMLPayloadCaseCreate(req, caseMetaData);
        if (createActivityPayload) {
            mlActivityPayload.push(createActivityPayload)
        } else {
            caseCreateOrigin = 'cache';
        }
        let timelinePayload = await createMLPayloadTimelineActivites(req, timelineCache);

        mlActivityPayload = [...mlActivityPayload, ...timelinePayload.mlActivityPayload];

        const mlReponse = await requestMLCaseTimeline(req, mlActivityPayload);
        if(caseMetaData.length && caseCreateOrigin == 'cache'){
            responsePayload.hashtags = JSON.parse(caseMetaData[0].hashtags);
            responsePayload.activities.push(JSON.parse(caseMetaData[0].timeline_ml_response));
        }
        else{
            responsePayload.hashtags = mlReponse.hashTags;
            responsePayload.activities.push(...mlReponse.caseCreateActivity);
        }
        responsePayload.activities = [...responsePayload.activities, ...timelinePayload.cacheTimlineActivites];
        responsePayload.activities = [...responsePayload.activities, ...mlReponse.caseActivities];

        res.status(200).send({ status: 200, data:responsePayload});

    } catch (error) {
        console.error("Error in /case-timeline:", error);
        res.status(500).send({ status: 500, response: { success: false, message: "Internal Server Error", data: error.message } });
    }
});

async function createMLPayloadCaseCreate(req, caseMetaData) {
    try {
        let caseCreateActivity;
        if (req.body.data && req.body.data.activities && req.body.data.activities.length) {
            caseCreateActivity = req.body.data.activities.find(activity => activity.type === "caseCreate");
        } else {
            throw new Error("Invalid request payload. 'activities' field is required.");
        }

        if (caseMetaData.length && caseMetaData[0].payload && caseCreateActivity && caseMetaData[0].payload == JSON.stringify(caseCreateActivity)) {
            return false;
        } else {
            return caseCreateActivity;
        }
    } catch (error) {
        throw new Error("Error in MLPayloadCaseCreate")
    }
}

async function createMLPayloadTimelineActivites(req, timelineCache) {
    try {
        let reqActivityPayload = [...req.body.data.activities];
    
        const cacheTimlineActivites = timelineCache.map(activity => {
            return JSON.parse(activity.ml_response);
        });
        let cacheActivityIds = cacheTimlineActivites.map(activity => activity.id);
        let reqActivitiesNotInCache = reqActivityPayload.filter(activity => (activity.type !== "caseCreate") && (cacheActivityIds.indexOf(activity.id) < 0));
        return {mlActivityPayload: [...reqActivitiesNotInCache], cacheTimlineActivites: cacheTimlineActivites};
    } catch (error) {
        console.error("Error in MLPayloadTimelineActivites: ", error);
        throw new Error("Error in MLPayloadTimelineActivites");
    }
}



async function requestMLCaseTimeline(req, mlActivityPayload) {
    try {
        let caseCreateActivityResponse = [];
        let caseActivitiesResponse = [];
        let hashTags = [];
        if (mlActivityPayload.length) {
            req.body.data.activities = mlActivityPayload;
            const response = await timelineMLRequest(req);
            if (response.status === 200) {
                if (response.data && response.data.activities && response.data.activities.length) {
                    caseCreateActivityResponse = response.data.activities.filter(activity => activity.type === "caseCreate");
                    caseActivitiesResponse = response.data.activities.filter(activity => activity.type !== "caseCreate");
                    
                    hashTags = response.data.hashtags;
    
                    let hasAnyMLKey = false;
                    if (caseCreateActivityResponse.length) {
                        caseCreateActivityResponse[0].llm_response.activityData.map(activity => {if(activity.value.length > 0) hasAnyMLKey = true;});
                    }
                    if(hasAnyMLKey){
                        
                        let caseCreateActivityFromPayload = mlActivityPayload.filter(activity => activity.type === "caseCreate");
                        await updateCaseMetadata(req, caseCreateActivityFromPayload, caseCreateActivityResponse, hashTags);
                    } else {
                        caseCreateActivityResponse = [];
                    }

                    const caseActivitiesResponseData = caseActivitiesResponse.filter(caseActivity => {
                        let hasAnyMLKey = false;
                        if (caseActivity.llm_response && Array.isArray(caseActivity.llm_response.activityData)) {
                          caseActivity.llm_response.activityData.map(activity => {
                            if (activity.value.length > 0) hasAnyMLKey = true;
                          });
                        } else {
                          console.log(`No llm_response key found in the caseActivity ${caseActivity}.`);
                        }
                        if(hasAnyMLKey){
                            return caseActivity;
                        }
                    });
                    await updateTimelineCache(req, caseActivitiesResponseData)
    
                } else {
                    console.error("Invalid response from ML request. No activities found.");
                }
            }
            else{
                console.error("Error in ML request:", response.message);
                throw new Error("Error in ML request");
            }
        }
        else{
            console.log("No ML activities to process");
        }
        return {
            caseCreateActivity: caseCreateActivityResponse,
            caseActivities: caseActivitiesResponse,
            hashTags: hashTags
        }
    } catch (error) {
        console.error("Error in ML request:", error);
        throw new Error("Error in ML request");
    }
}


// Function to update case metadata
async function updateCaseMetadata(req, caseCreateActivityPayload, caseCreateActivityResponse, hashtags) {
    try {
        const params = [
            req.body.data.caseId,
            req.headers["tenant-id"],
            req.headers["uid"],
            caseCreateActivityPayload.length ? JSON.stringify(caseCreateActivityPayload[0]) : null,
            caseCreateActivityResponse.length ? JSON.stringify(caseCreateActivityResponse[0]) : null,
            JSON.stringify(hashtags),
            req.body.data.lastModifiedDate,
        ];
    
        const sql = `INSERT INTO ah_case_metadata (case_id, tenant_id, uid, payload, timeline_ml_response, hashtags, last_updated) 
                        VALUES 
                        (?, ?, ?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                        payload = VALUES(payload),
                        timeline_ml_response = VALUES(timeline_ml_response),
                        hashtags = VALUES(hashtags),
                        last_updated = VALUES(last_updated);
                    `
        await executeSQLQuery(req.headers["tenant-id"], sql, params);
    } catch (error) {
        console.error("Error in updateCaseMetadata: ",error);
        throw new Error("Error in updateCaseMetadata");
    }
}

// Function to insert case metadata
async function insertCaseMetadata(req, reqActivityPayload, response) {
    const data = [
        req.body.data.caseId,
        req.headers["tenant-id"],
        req.headers["uid"],
        JSON.stringify(reqActivityPayload.find(activity => activity.type === "caseCreate")),
        JSON.stringify(response.data.activities.find(activity => activity.type === "caseCreate")),
        JSON.stringify(response.data.hashtags),
        req.body.data.lastModifiedDate,
    ];
    const query = "INSERT INTO ah_case_metadata (case_id, tenant_id, uid, payload, timeline_ml_response, hashtags, last_updated) VALUES (?, ?, ?, ?, ?, ?, ?)";
    await executeSQLQuery(req.headers["tenant-id"], query, data);
}

// Function to update timeline cache
async function updateTimelineCache(req, response) {
    try {
        const mlActivityResponse = response
        if (mlActivityResponse.length) {
            const params = mlActivityResponse.map(activity => [
                activity.id,
                req.body.data.caseId,
                req.headers["tenant-id"],
                req.headers["uid"],
                JSON.stringify(activity),
                activity.ts
            ]);
            const sql = `INSERT INTO ah_timeline_cache (activity_id, case_id, tenant_id, uid, ml_response, activity_ts) VALUES ?
                            ON DUPLICATE KEY UPDATE
                            params = VALUES(params),
                            ml_response = VALUES(ml_response),
                            activity_ts = VALUES(activity_ts),
                            created_ts = VALUES(created_ts);
                        `;
            await executeSQLQuery(req.headers["tenant-id"], sql, [params]);
        }
    } catch (error) {
        console.error("Error in Error in updateCaseMetadata", error);
        throw new Error("Error in updateCaseMetadata");
    }
}

async function getAllTones(req) {
    try {
        const params = [
            req.body.user_id,
            req.headers["tenant-id"],
            req.headers["uid"]
        ];
        const sql = "SELECT * FROM ah_custom_tone WHERE user_id =? AND tenant_id =? AND uid =?";
        const customTones = await executeSQLQuery(req.headers["tenant-id"], sql, params);
        const params1 = [
            req.headers["tenant-id"],
        ]
        const sql1 = "SELECT * FROM ah_default_tone";
        const defaultTones = await executeSQLQuery(req.headers["tenant-id"], sql1, params1);

        let allTones = [];
        if (defaultTones.data && defaultTones.data.length) {
            allTones.push(...defaultTones.data);
        }
        if (customTones.data && customTones.data.length) {
            allTones.push(...customTones.data);
        }

        return allTones;
    } catch (error) {
        console.error("Error getting all tones", error);
        new Error("Error getting all tones");
    }
}

router.post('/get-tones', async (req, res) => {
    try {
        const allTones  = await getAllTones(req)
        const userTone = await getUserTone(req)
        const response = {
            success: true,
            data: allTones,
            selected_tone_id: (userTone.length > 0 && userTone[0].hasOwnProperty('selected_tone_id')) ? userTone[0].selected_tone_id : 1,
            is_custom: (userTone.length > 0 && userTone[0].hasOwnProperty('is_custom')) ? userTone[0].is_custom : 0
        }

        res.status(200).send({ status: 200, data:response});
    } catch (error) {
        console.error("Error in saving user tonality: ", error);
        res.status(500).send({ status: 500, response: { error: "error in saving user tonality" }});
    }
});


async function updateCustomProfile(req){
    try {
        const data = req.body;
        const sql = "INSERT INTO ah_custom_tone (user_id, tenant_id, uid, name, image_path, props, is_custom, last_updated) VALUES (?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE name = VALUES(name), image_path = VALUES(image_path), props = VALUES(props), last_updated = VALUES(last_updated)";
        const params = [
            data.user_id,
            req.headers["tenant-id"],
            req.headers["uid"],
            data.name,
            data.image_path,
            JSON.stringify(data.props),
            data.is_custom,
            new Date(),
        ];
        await executeSQLQuery(req.headers["tenant-id"], sql, params);
    } catch (error) {
        console.error("Error in updating user custom tone: ", error);
        throw new Error("Error in updating user custom tone");
    }
}

async function getCustomTone(user_id, uid, tenant_id) {
    try {
        const sql = "SELECT tone_id from ah_custom_tone where user_id=? AND uid=? AND tenant_id=?"
        const params = [
            user_id,
            uid,
            tenant_id
        ]
        const result = await executeSQLQuery(tenant_id, sql, params);
        return result["data"][0]["tone_id"]
    } catch (error) {
        console.error("Error getting user custom tone: ", error);
        throw new Error("Error getting user custom tone");
    }
}

async function updateUserTone(tone_id, user_id, uid, tenant_id, is_custom=false) {
    try {
        const sql = "UPDATE ah_user_tone SET selected_tone_id = ?, is_custom = ? WHERE user_id = ? AND uid = ? AND tenant_id = ?";
        const params = [
            tone_id,
            is_custom,
            user_id,
            uid,
            tenant_id,
        ];
        await executeSQLQuery(tenant_id, sql, params);
    } catch (error) {
        console.error("Error updating user tone", error);
        throw new Error("Error updating user tone");
    }
}

router.post('/user-tone', async (req, res) => {
    const data = req.body;
    try {
        if (data.is_custom) {
            await updateCustomProfile(req);
            const tone_id = await getCustomTone(data.user_id, req.headers["uid"], req.headers["tenant-id"])
            await updateUserTone(tone_id, data.user_id, req.headers["uid"], req.headers["tenant-id"], true);
        } else {
            await updateUserTone(data.tone_id, data.user_id, req.headers["uid"], req.headers["tenant-id"], false);
        }
        res.status(200).send({ status: 200, response: { message: "User tone updated successfully" } });
    } catch (error) {
        console.error("Error while updating user tone: ", error);
        res.status(500).send({ status: 500, response: { error: "Error while updating user tone" }});
    }
});

router.post("/get-esc-score", async (req, res) => {
    try {
        const tenantId = req.headers["tenant-id"];
        const uid = req.headers["uid"];

        if (!tenantId || !uid) {
            return res.status(401).json({
                status: 401,
                success: false,
                message: "Invalid Request: Missing tenant-id or uid",
                data: null,
            });
        }

        const options = {
            url: `${config.get("epaddon.url")}/get_esc_score`,
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "tenant-id": tenantId,
            },
            body: {
                case_number: req.body.case_number,
                secret_key: config.get("epaddon.secretKey"),
            },
            json: true,
        };

        request(options, (error, response, body) => {
            if (error) {
                console.error("Error fetching Escalation Score:", error.message);
                return res.status(500).json({
                    status: 500,
                    success: false,
                    message: "Error in EP Response",
                    data: null,
                });
            }

            if (body.success && body.case_exists && body.LikelihoodEscalate) {
                return res.status(200).json({
                    status: 200,
                    success: true,
                    message: "Success",
                    LikelihoodEscalate: body.LikelihoodEscalate,
                });
            }

            return res.status(400).json({
                status: 400,
                success: false,
                message: "Escalation Score for this case does not exist",
                data: null,
            });
        });
    } catch (error) {
        console.error("Unexpected error:", error.message);
        return res.status(500).json({
            status: 500,
            success: false,
            message: "Server Error",
            data: null,
        });
    }
});

function htmlToMarkdown(html) {
    return NodeHtmlMarkdown.translate(
        html,
        {
            bulletMarker: '•',
            strongDelimiter: '*',
            strikeDelimiter: '~'
        }
    )
}

const updateTranslatedFirstResponse = async (req, mlResponse) => {
    // Combined input validation
    if (
        !req || 
        !req.headers || 
        !req.headers["tenant-id"] ||
        !req.body || 
        !req.body.caseId || 
        !req.body.uid || 
        !req.body.user_id ||
        !mlResponse || 
        !mlResponse.data || 
        !mlResponse.language
    ) {
        throw new Error("Invalid request");
    }

    const last_updated = new Date().toISOString();
    try {
        const sql = 'UPDATE ah_first_response_cache SET ml_response = ?, current_language = ? WHERE tenant_id = ? AND case_id = ? AND uid = ? AND user_id = ?';

        const params = [
            JSON.stringify(mlResponse.data),
            mlResponse.language,
            req.headers["tenant-id"],
            req.body.caseId,
            req.body.uid,
            req.body.user_id
        ];
        const result = await executeSQLQuery(req.headers["tenant-id"], sql, params);
        if (!result || !result.success) {
            console.error(
                "Database update failed:", 
                result && result.error ? result.error : "Unknown error"
            );
            throw new Error("Update failed");
        }
    } catch (err) {
        console.error("Error updating first response cache:", err);
        throw new Error("Update failed");
    }
};

router.post('/translate-ra', async (req, res) => {
    // Initial request validation
    if (!req.headers["tenant-id"]) {
        return res.status(400).send({
            status: 400,
            success: false,
            message: "Missing tenant-id header"
        });
    }

    if (!req.body || Object.keys(req.body).length === 0) {
        return res.status(400).send({
            status: 400,
            success: false,
            message: "Request body is required"
        });
    }

    const payload = {
        url: config.get('MLService.url') + "/ai-editor",
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "tenant-id": req.headers["tenant-id"],
            uid: req.headers["uid"]
        },
        body: {
            ...req.body,
            tenant_id: req.headers["tenant-id"]
        },
        json: true,
    };

    try {
        const mlResponse = await makeRequest(payload);
        
        // Validate ML response
        if (!mlResponse || !mlResponse.data) {
            throw new Error("Invalid ML service response: missing data");
        }

        if (req.body.isActiveResponse) {
            updateTranslatedFirstResponse(req, mlResponse);
        }

        return res.status(200).send({
            success: true,
            message: 'Success',
            status: 200,
            translatedContent: mlResponse.data,
            markdownData: htmlToMarkdown(mlResponse.data),
            language: mlResponse.language || 'english'
        });
    } catch (err) {
        console.error("Error in translation:", err);
        return res.status(500).send({
            status: 500,
            success: false,
            message: 'Failed to translate',
        });
    }
});

router.post('/regenerate-ra', async (req, res) => {
    const body = req.body;
    const headers = req.headers;
    const tenantId = req.headers['tenant-id'];

    if (!body || Object.keys(body).length === 0) {
        return res.status(400).json({
            status: 400,
            success: false,
            message: "Request body cannot be empty.",
        });
    }

    const payload = {
        url: config.get('MLService.url') + "/ai-editor",
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "tenant-id": tenantId,
            uid: headers["uid"],
        },
        body: Object.assign({}, body, { 
            tenant_id: tenantId,
            service: "agenthelper",
            rephrase: true
        }),
        json: true,
    };


    try {
        const mlResponse = await makeRequest(payload);

        if (!mlResponse || !mlResponse.data) {
            throw new Error("Error in generating the response by ML.");
        }

        return res.status(200).json({
            status: 200,
            success: true,
            message: "Content generated successfully.",
            regeneratedContent: mlResponse.data,
            markdownData: htmlToMarkdown(mlResponse.data),
            language: mlResponse.language || 'english',
        });
    } catch (error) {
        console.error("Internal server error:", error && error.message ? error.message : error);
        return res.status(500).json({
            status: 500,
            success: false,
            message: "Internal server error.",
        });
    }
});

module.exports = {
    router: router,
    informAdmins,
    executeGPT
};
