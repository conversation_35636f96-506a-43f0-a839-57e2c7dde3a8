var request = require("request");
var commonFunctions = require('./../../utils/commonFunctions');


const getAccessToken = function (resultData, code, redirect_Uri, callback) {

    var headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "cache-control": "no-cache"
    };
    var dataString = {
        "code": code,
        "client_id": resultData.authorization.client_id,
        "redirect_uri": redirect_Uri,
        "grant_type": "authorization_code",
        "client_secret": resultData.authorization.client_secret,
        "Prefer": "odata.maxpagesize=10"
    };
    var options = {
        url: "https://login.microsoftonline.com/" + resultData.authorization.tenantId + "/oauth2/token",
        method: 'POST',
        headers: headers,
        form: dataString

    };
    request(options, function (error, response, body) {
        if (error) {
            callback(error, null);
        }
        else {
            if (response.statusCode == 200) {
                body = JSON.parse(body);
                callback(null, body);
            }
            else {
                commonFunctions.errorlogger.error("Auth err in Dynamics");
                callback(response.statusMessage, null);
            }
        }
    });
}

const requestTofetchObjectFields = function (authorization, contentSource, objectName, callback) {

    var objectName;
    if (objectName == "cases") {
        objectName = "incident";
    }
    else {
        objectName = "knowledgearticle";
    }

    var fieldsArray = [];
    var options = {
        method: 'GET',
        url: contentSource.url + "/api/data/v9.0/EntityDefinitions(LogicalName='" + objectName + "')/Attributes",
        headers:
        {
            "Content-Type": "application/json",
            "cache-control": "no-cache"
        }
    };
    if (authorization.authorization_type == "OAuth") {
        options["headers"] = {
            "authorization": "Bearer " + authorization.accessToken
        }

        request(options, function (error, response, body) {
            if (error) {
                callback(error, null);
            }
            else {
                if (response.statusCode == 200) {
                    body = JSON.parse(body);
                    var body = body.value;
                    for (var f = 0; f < body.length; f++) {
                        var type;
                        if (body[f].LogicalName && body[f].AttributeType) {
                            if (body[f].AttributeType == "Datetime") {
                                type = body[f].AttributeType
                            }
                            if (body[f].LogicalName == "createdon") {
                                body[f].LogicalName = "post_time";
                            }
                            if (objectName == "knowledgearticle" && body[f].LogicalName == "statuscodename") {
                                body[f].LogicalName = "statusname";
                            }
                            else {
                                type = "string";
                            }

                        }
                        else {
                            type = "string"
                        }
                        fieldsArray.push({
                            "name": (body[f].LogicalName) ? body[f].LogicalName : body[f].SchemaName,
                            "label": body[f].SchemaName.replace(/[`~!@#$%^&*()_|Σ+\-=?;:'",.<>\{\}\[\]\\\/]/gi, ''),
                            "type": type

                        })
                    }
                    callback(null, { data: fieldsArray, flag: 0 })
                }
                else {
                    commonFunctions.errorlogger.error("APi Error: ", response.statusMessage);
                    callback(response.statusCode, null);
                }
            }

        })
    }
}

module.exports = {
    getAccessToken: getAccessToken,
    requestTofetchObjectFields: requestTofetchObjectFields
}

