module.exports = {
    objects: [
        {
            name: 'mail',
            label: 'Email',
            status: 1,
            boosting_factor: 1,
            base_url: '',
            fields: []
        },
        {
            name: 'chat',
            label: 'Chat Message',
            status: 1,
            boosting_factor: 1,
            base_url: '',
            fields: []
        }
    ],
    fields: [
        // Mail fields
        {
            name: 'id',
            label: 'ID',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'threadId',
            label: 'Thread ID',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'subject',
            label: 'Subject',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'from',
            label: 'From',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'to',
            label: 'To',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'date',
            label: 'Date',
            type: 'datetime',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 0,
            isActive: 1,
            isMerged: 1
        },
        {
            name: 'snippet',
            label: 'Snippet',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'body',
            label: 'Body',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'hasAttachment',
            label: 'Has Attachment',
            type: 'boolean',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'labelIds',
            label: 'Labels',
            type: 'array',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        // Chat fields
        {
            name: 'name',
            label: 'Message Name',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'text',
            label: 'Message Text',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'argumentText',
            label: 'Argument Text',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'createTime',
            label: 'Created Time',
            type: 'datetime',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 0,
            isActive: 1,
            isMerged: 1
        },
        {
            name: 'lastUpdateTime',
            label: 'Last Updated',
            type: 'datetime',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 0,
            isActive: 1
        },
        {
            name: 'thread',
            label: 'Thread',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'attachments',
            label: 'Attachments',
            type: 'array',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'sender',
            label: 'Sender',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'space',
            label: 'Space',
            type: 'string',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        },
        {
            name: 'deleted',
            label: 'Is Deleted',
            type: 'boolean',
            isFilterable: 1,
            isSortable: 1,
            isSearchable: 1,
            isActive: 1
        }
    ]
};
