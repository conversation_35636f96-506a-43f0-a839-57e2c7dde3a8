/**
 * <AUTHOR>
 * @version 1
 */
var express = require('express');
var async = require('async');
var router = express.Router();
var request = require('request');
var md5 = require('md5');
var universal = require('./../universal')
var accessKey = config.get('predictionIO.accessKey');
var recommenderRoutes = {};
var elastic = require('elasticsearch');
var commonFunctions = require('../../utils/commonFunctions');
//const crawler = require('./../crawlers');
var client = new elastic.Client({
    host: 'http://' + config.get('elasticIndexCS.host') + ':' + config.get('elasticIndexCS.port')
});

var searchDataIndex = config.get('elasticIndex.analytics');
var searchDataType = "search_keyword";
var facet_interpreter_index = "facet_interpreter"+"_"+config.get('poc_name');;
var facet_interpreter_type = "facetvalues";

var daterang<PERSON> = {};
daterange["from"] = "now-180d/d";  //Should be same day
daterange["to"] = "now/d";


router.get('/getFacet', function (req, res, err1) {
    debugger;
    getFilteredSearch(0, 100);
});



function getFilteredSearch(from, size, next){
    if(!next){
        client.search({
            index:searchDataIndex,
            type:searchDataType,
            size:size,
            from:from,
            scroll:'1h',
            body:{
                "query": {
                  "nested": {
                    "path": "filters",
                    "query": {
                      "bool": {
                        "must": [
                          {
                            "exists": {
                              "field": "filters"
                            }
                          },
                          {
                            "range": {
                              "search_date": {
                                "gte": daterange.from,
                                "lte": daterange.to
                              }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                "_source": {
                    "includes": ["text_entered", "filters", "uid"]
                }
            }   
        },
        function (err, datain) {
          return getMoreUntilDone(err, from, size, datain);
        });
    }else {
      client.scroll({
        scrollId: next,
        scroll: '1h'
      }, function (err, data) {
        return getMoreUntilDone(err, from, size, data);
      });
    } 
}


function getMoreUntilDone(err, from, size, data){
    if(!err){
        analyticsData = data.hits.hits;
        storeFilteredSearches(analyticsData);
        if (data.hits.total > (from + size)) {
            getAnalyticsData(from+size+1, size, data._scroll_id);
        }
    }
}

function storeFilteredSearches(filteredRes){

}


module.exports = {
    router: router
};
