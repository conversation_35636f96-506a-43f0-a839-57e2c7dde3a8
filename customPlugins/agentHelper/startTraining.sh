#!/bin/bash

set -e
echo prepareDataCSVForPython.js --createNow --uid=$1;
node prepareDataCSVForPython.js --createNow --uid=$1
echo "Starting ML server"
INSID=i-0c6333a5160704066
aws ec2 start-instances --instance-ids $INSID
STATUS=80
while [ $STATUS -ne 16 ]
    do
    echo "Status:$STATUS"
    sleep 5
    STATUS=$(echo "let x=`aws ec2 describe-instances --instance-ids $INSID`;console.log(x.Reservations[0].Instances[0].State.Code)" | node )
done
echo "Server Status Changed $STATUS"
MLIP=$(echo "let x=`aws ec2 describe-instances --instance-ids $INSID`;console.log(x.Reservations[0].Instances[0].PrivateIpAddress)" | node )
echo "Uploading file"
scp docs.csv $MLIP:/home/<USER>/Optics/Stage1/original/dataset.csv
SUB="source agentHelper/bin/activate
cd Optics
ls -la
python starter.py";

ssh $MLIP "$SUB"

scp $MLIP:/home/<USER>/Optics/Stage4/clusterfile/clustered_cases.csv .
scp $MLIP:/home/<USER>/Optics/Stage4/model/model.h5 optics/.
node createbulkupdateJson.js
curl -H "Content-Type: application/x-ndjson" -XPOST localhost:8045/_bulk --data-binary "@bulkUpdate.json.ignore"
cd optics
source agentHelper/bin/activate
forever stop agentHelperClassificaionServer
forever start -a --uid "agentHelperClassificaionServer" -c python app.py

SUB="cd Optics
ls -la"
ssh $MLIP '$SUB'