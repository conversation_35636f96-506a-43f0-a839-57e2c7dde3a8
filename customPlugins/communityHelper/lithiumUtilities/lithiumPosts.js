const path = require('path');
const fs = require('fs');
environment = require('../../../routes/environment');
process.env.NODE_ENV = environment.configuration;
const configPath = path.join(__dirname, '../../../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
const request = require("request");
const connection_sql = require('../../../utils/connection');
const async = require('async');
const router = require('express').Router();
const commonFunctions = require('../../../utils/commonFunctions');
const appVariables = require('../../../constants/appVariables');
const templates = require('../personalizedTemplateSettings/templateSettings');
const getSearchResults = require('../getSearchResults/getSearchResults');
const { spawn } = require('child_process');
const botAnalytics = require('../analytics/botAnalyticsLib');
const communityHelperSettings = require('../communityHelperSettings/communityHelperSettings');


// var forumsTrain = {
//     get runForumTrainer() {
//         if (process.argv[2]) {
//             connection_sql.handleDisconnect().then(function (result) {
//                 commonFunctions.errorlogger.info("process started for ", process.argv[2]);
//                 preProcessForumPost(process.argv[2],(error, result) => {
//                     // This function need's to be discussed to check about SQL Connection.
//                     // SQL ISSUE
//                     if (results) {
//                         botAnalytics.insertBotAnalytics(result, 0, (aError, aResult) => {
//                             process.exit(0);
//                         })
//                     } else {
//                         process.exit(0);
//                     }
//                 })
//             });
//         }
//     },
// };

const getLithiumUsers = (botSettings, cb) => {
    var query = `SELECT * from users where id = '${botSettings.username}'`
    let headers = {
        'authorization': 'Bearer ' + botSettings.accessToken,
        'content-type': 'application/json'
    }
    let url = botSettings.url + '/api/2.0/search'
    commonFunctions.errorlogger.info(url);
    commonFunctions.httpRequest('GET', url, query, "", headers, function (error, body) {
        let response = {
            user: {}
        }
        if (!error) {
            if (body.data && body.data.items && body.data.items.length) {
                response.user = {
                    id: body.data.items[0].id,
                    email: body.data.items[0].email,
                    name: body.data.items[0].first_name + ' ' + body.data.items[0].last_name,
                    role: body.data.items[0].rank.name
                }
                cb(null, response);
            }
            else {
                cb(null, response);
            }
        }
        else {
            cb(null, response);
        }
    })
}

const getTokenAndUser = function (botId, tenantId, callback) {
    async.auto({
        getBotSettings: (cb) => {
            if (botId)
                communityHelperSettings.getBotSettings(botId, tenantId, cb);
            else
                cb([], null);
        },
        getRefreshToken: ["getBotSettings", (dataFromAbove, cb) => {
            getRefreshToken(dataFromAbove.getBotSettings, (error, token) => {
                if (error) cb(error);
                else {
                    dataFromAbove.getBotSettings.accessToken = token;
                    cb(null, []);
                }
            });
        }],
        getLithiumUser: ['getRefreshToken', (dataFromAbove, cb) => {
            getLithiumUsers(dataFromAbove.getBotSettings, (error, result) => {
                if (result) {
                    cb(null, result);
                } else {
                    cb(null, {});
                }
            })
        }]
    }, (error, result) => {
        if (error)
            callback(error);
        else
            callback(null, result.getLithiumUser);
    });
}

const getRefreshToken = function (botSettings, cb) {
    let url = botSettings.url + "/api/2.0/auth/refreshToken";
    let query = "";
    let headers = {
        'Content-Type': 'application/json'
    }
    let body = {
        client_id: botSettings.client_id,
        client_secret: botSettings.client_secret,
        refresh_token: botSettings.refreshToken,
        grant_type: 'refresh_token'
    }
    if (botSettings.authorization_type.indexOf('htaccess') > -1 && botSettings.htaccessUsername && botSettings.htaccessPassword) {
        let start = url.indexOf('//') + 2;
        url = url.substring(0, start) + botSettings.htaccessUsername + ':' + botSettings.htaccessPassword + '@' + url.substring(start);
    }
    commonFunctions.errorlogger.info(url);
    commonFunctions.httpRequest('POST', url, query, body, headers, function (error, body) {
        if (!error && body.http_code == 200) {
            cb(null, body.data.access_token);
        }
        else {
            commonFunctions.errorlogger.error(error);
            cb('error');
        }
    });
}

router.get('/getLithiumUser', function (req, res) {
    const tenantId = req.headers['tenant-id'];
    getTokenAndUser(req.query.botId, tenantId, (error, result) => {
        if (error)
            res.send({ flag: 400, message: "Unauthorised credentials" });
        else
            res.send(result);
    })
});

const getBoardsAndRoles =  (botSettings, cb) => {
    let url = botSettings.url + '/grazitti/plugins/custom/grazitti/grazitti/SearchUnify_helper_bot';
    let headers = {
        'authorization': 'Bearer ' + botSettings.accessToken,
        'content-type': 'application/json'
    }
    commonFunctions.errorlogger.info(url);
    commonFunctions.httpRequest('GET', url, "", "", headers,  (error, result) => {
        if (!error && result.boardsArr && result.rolesArr){ 
            
            cb(null, result); 
        }else{
            cb("Unauthorised user", null);
        }  
    })
}

const getTags = function (botSettings, query, data, limit, offset, callback) {
    let url = botSettings.url + '/api/2.0/search'
    let qs = query + ' limit ' + limit + ' offset ' + offset
    let headers =
    {
        'Authorization': 'Bearer ' + botSettings.accessToken,
        'client-id': botSettings.client_id,
        'content-type': 'application/json'
    }
    commonFunctions.httpRequest('GET', url, qs, "", headers, function (error, body) {
        if (error) {
            commonFunctions.errorlogger.error("eror is", error)
            callback(error, null);
        }
        if (body && body.http_code == 200) {
            data = data.concat(body.data.items.map((f) => f.text));
            commonFunctions.errorlogger.warn("here in data 1")
            if (body.data.items.length < limit)
                callback(null, data)
            else {
                offset += parseInt(limit);
                commonFunctions.errorlogger.warn("getting tags for ", offset);
                getTags(botSettings, query, data, limit, offset, callback);
            }
        }
    });
}

function getTagsForMessages(botSettings, messages, callback) {
    var tags = []
    for (var i = 0; i < messages.length; i++) {
        tags.push((function (i) {
            return function (cb) {
                getTags(botSettings, messages[i].tags.query, [], 10, 0, function (err, result) {
                    if (!err) {
                        messages[i].tags = result;
                        cb(null, messages[i])
                    }
                    else
                        cb(err)
                })
            }
        })(i))
    }
    async.parallelLimit(tags, 5, function (err, messages) {
        if (!err)
            callback(null, messages);
        else
            console.log(err);
    });
}

const getUidForBot = (botId, tenantId, cb) => {
    let sql = `select community_helper_bot.id, community_helper_bot.name, community_helper_bot.platform_type_id, search_client_id, 
                community_helper_bot.community, community_helper_bot.id, search_clients.uid as uid,
                community_helper_bot_responses.response_freq_type, community_helper_bot_responses.response_freq_time
                from community_helper_bot left join search_clients on 
                community_helper_bot.search_client_id = search_clients.id left join 
                community_helper_bot_responses on 
                community_helper_bot.id = community_helper_bot_responses.community_helper_bot_id 
                where community_helper_bot.id=?`;
      connection[tenantId].execute.query(sql, botId, function (err, rows) {
      if (!err && rows.length) {
        let resultToSend = { "id": rows[0].search_client_id, "name": rows[0].name, "uid": rows[0].uid, "frequencyType": rows[0].response_freq_type, "frequencyTime": rows[0].response_freq_time, "platform_id": rows[0].platform_type_id };
        cb(null, resultToSend);
      }
      else {
        commonFunctions.errorlogger.info("fetch all community helper bots", err);
        cb(null, []);
      }
    })
};

const sendForumPostReply = (botSettings, processVariables, templateBody, cb) => {
    let bodyText = templateBody[0].text.replace(/\n/g, '<br>');
    let url = botSettings.url + '/api/2.0/messages';
    let headers = {
        'authorization': 'Bearer ' + botSettings.accessToken,
        'content-type': 'application/json'
    }
    let body = {
        "data": {
            "type": "message",
            "parent": { "id": processVariables.forumId },
            "body": bodyText,
            "board": {
                "id": processVariables.boardId
            }
        }
    }
    commonFunctions.errorlogger.info(url);
    commonFunctions.httpRequest('POST', url, "", body, headers, function (error, result) {
        if (!error)
            cb(null, result);
        else
            cb(error, null);
    })
}

const preProcessForumPost = (botId, req, callback) => {
    const tenantId = req.headers['tenant-id'];
    async.auto({
        getBotSettings: (cb) => {
            if (botId)
                communityHelperSettings.getBotSettings(botId,tenantId, cb);
            else
                cb([], null);
        },
        getRefreshToken: ["getBotSettings", (dataFromAbove, cb) => {
            getRefreshToken(dataFromAbove.getBotSettings, (error, token) => {
                if (error) cb(error);
                else {
                    dataFromAbove.getBotSettings.accessToken = token;
                    cb(null, []);
                }
            });
        }],
        getUidForBot: (cb) => {
            getUidForBot(botId, tenantId, cb);
        },
        getBoardsAndRoles: ["getRefreshToken", (dataFromAbove, cb) => {
            getBoardsAndRoles(dataFromAbove.getBotSettings, cb);
            // let result={}
            // result.boardsArr =[]; 
            // result.rolesArr=[];
            // cb(null,result)
        }],
        getBotContentSources: ["getUidForBot", (dataFromAbove, cb) => {
            if (dataFromAbove.getUidForBot && dataFromAbove.getUidForBot.id) {
                communityHelperSettings.getBotContentSources(dataFromAbove.getUidForBot.id, botId, req, (error, result) => {
                    if (error) {
                        cb(error, null);
                    } else {
                        let resultToSend = [{ "type": "_type", "filter": [] }];
                        result.map(x => {
                            if (x.enabled) {
                                x.objects.map(o => {
                                    if (o.enabled) {
                                        resultToSend[0].filter.push(o.name);
                                    }
                                })
                            }
                        })
                        cb(null, resultToSend);
                    }
                });
            } else
                cb([], null);
        }],
        fetchTemplate: (cb) => {
            templates.fetchAllTemplates(botId, tenantId, (error, result) => {
                if (!error && result && result.length) {
                    let randomTemplate = result[Math.floor(Math.random() * result.length)];
                    let templateBody = randomTemplate.template_body;
                    cb(null, templateBody);
                } else {
                    cb(error, '');
                }
            });
        },
        getTemplateFields: ["fetchTemplate", (dataFromAbove, cb) => {
            let template = dataFromAbove.fetchTemplate;
            let searchFields = templates.getSearchFields(template);
            let platformFields = templates.getPlatformFields(template);
            let allFields = { searchFields, platformFields };
            cb(null, allFields);
        }],
        fetchDailyForums: ["getRefreshToken", "getUidForBot", (dataFromAbove, cb) => {
            if (dataFromAbove.getUidForBot) {
                let frequencyTime = dataFromAbove.getUidForBot.frequencyTime;
                let frequencyType = dataFromAbove.getUidForBot.frequencyType;
                let date = new Date(new Date().setHours(new Date().getHours() - 12)).toISOString();
                if (frequencyType === 'Hours')
                    date = new Date(new Date().setHours(new Date().getHours() - frequencyTime)).toISOString();
                if (frequencyType === 'Minutes')
                    date = new Date(new Date().setMinutes(new Date().getMinutes() - frequencyTime)).toISOString();
                if (frequencyType === 'Days')
                   { date = new Date(new Date().setDate(new Date().getDate() - frequencyTime)).toISOString();}
                date = new Date(date).getTime();
                let startDate= new Date(date-4*24 * 60 * 60 * 1000).getTime() // getting data bfore a date
                let condition = " conversation.last_post_time < " + date + " AND conversation.last_post_time > "+ startDate+ " AND depth = 0 AND replies.count(*)=0 AND conversation.style IN ('forum','qanda') ORDER BY conversation.last_post_time DESC";
                let fields = ["id", "body", "subject", "board.parent_category.short_title", "post_time", "board.short_title", "author.view_href", "author.login", "tags"];

                fetchLithiumForums(dataFromAbove.getBotSettings, fields, condition, 10, 0, [], (error, result) => {
                    if (!error) {
                        cb(null, result);
                    } else {
                        cb(error, []);
                    }
                })
            } else {
                cb(error, []);
            }
        }]
    }, (error, result) => {
        if (error) {
            commonFunctions.errorlogger.info(error);
            callback(null, 'Some error occured!!');
        } else {
            if (result && result.getUidForBot && result.fetchDailyForums && result.getBoardsAndRoles) {
                let asyncTasks = [];
                let platformFields = result.getTemplateFields.platformFields;
                for (var j = 0; j < result.fetchDailyForums.length; j++) {
                    let allPlatformFields = platformFields.find((x) => !result.fetchDailyForums[j][x]);
                    if (!allPlatformFields) {
                        let searchString = '';
                        if (result.fetchDailyForums[j].subject)
                            searchString = result.fetchDailyForums[j].subject;
                        if (result.fetchDailyForums[j].body)
                            searchString = searchString + ' ' + result.fetchDailyForums[j].body.substring(0, 100);
                        searchString = commonFunctions.stripHtml(searchString);
                        //  searchString = templates.getTokenizeText(searchString);
                        let forumFields = [
                            { "name": "subject", "value": result.fetchDailyForums[j].subject },
                            { "name": "body", "value": result.fetchDailyForums[j].body },
                            { "name": "parentCategoryName", "value": result.fetchDailyForums[j].board.parent_category.short_title },
                            { "name": "post_time", "value": result.fetchDailyForums[j].post_time },
                            { "name": "boardName", "value": result.fetchDailyForums[j].board.short_title },
                            { "name": "author", "value": result.fetchDailyForums[j].author.login, "view_href": result.fetchDailyForums[j].author.view_href },
                            { "name": "tags", "value": result.fetchDailyForums[j].tags },
                        ]

                        let processVariables = {
                            botId,
                            searchString,
                            forumFields,
                            forumId: result.fetchDailyForums[j].id,
                            boardId: result.fetchDailyForums[j].id,
                            getUidForBot: result.getUidForBot,
                            boardsArr: result.getBoardsAndRoles.boardsArr || [],
                            rolesArr: result.getBoardsAndRoles.rolesArr || [],
                            getBotContentSources: result.getBotContentSources,
                            fetchTemplate: result.fetchTemplate,
                            getTemplateFields: result.getTemplateFields,

                        }
                        
                        let shouldPost;
                        shouldPost = forumFields.forEach((field) => {
                            if (platformFields.includes(field.name) && !field.value.length)
                                shouldPost = false;
                        })

                        asyncTasks.push(sendForumPost.bind(null, result.getBotSettings, processVariables, req));
                    } else {
                        commonFunctions.errorlogger.info('Unable to send result as feed fields are', allPlatformFields);
                    }
                }
                async.series(asyncTasks, function (err, data) {
                    if (err) {
                        commonFunctions.errorlogger.info(error);
                        callback(null, 'Some error occured!!');
                    } else {
                        callback(null, data);
                    }
                });
            } else {
                commonFunctions.errorlogger.info(result);
                callback(null, 'Some error occured!!');
            }
        }
    })

};

const sendForumPost = (botSettings, processVariables, req, callback) => {
    async.auto({
        getUserInfo: (cb) => {
            botSettings.username = processVariables.forumFields.author;
            getLithiumUsers(botSettings, (error, result) => {
                if (result && result.user)
                    cb(null, result.user);
                else
                    cb([], null);
            })
        },
        getSearchResult: ["getUserInfo", (dataFromAbove, cb) => {
            let searchObj = {
                "uid": processVariables.getUidForBot.uid,
                "UserId": dataFromAbove.getUserInfo.id,
                "boardsArr": processVariables.boardsArr,
                "rolesArr": processVariables.rolesArr,
                "email": dataFromAbove.getUserInfo.email,
                "aggregationsArray": processVariables.getBotContentSources,
                "searchString": processVariables.searchString
            };
            getSearchResults.getSearchResults(searchObj, processVariables.getTemplateFields, req, (error, result) => {
                cb(error, result);
            });
        }],
        evaluateTemplateBody: ["getSearchResult", (dataFromAbove, cb) => {
            let templateValue = '';
            if (dataFromAbove.getSearchResult && dataFromAbove.getSearchResult.length && processVariables.fetchTemplate && processVariables.forumFields) {
                templateValue = templates.getTemplateWithValues(processVariables.fetchTemplate, dataFromAbove.getSearchResult, processVariables.forumFields, botSettings.platform_type_id);
            }
            cb(null, templateValue);
        }],
        sendForumPost: ["evaluateTemplateBody", (dataFromAbove, cb) => {
            let templateBody = dataFromAbove.evaluateTemplateBody;
            if (processVariables.forumId && templateBody) {
                sendForumPostReply(botSettings, processVariables, templateBody, (error, result) => {
                    cb(error, result);
                });
            } else {
                cb([], null);
            }

        }]
    }, function (error, result) {
        let response = { "flag": 400, "data": [] }
        if (error) {
            commonFunctions.errorlogger.info("add comment in community helper", error);
            callback(null, null)
        } else {
            let finalResult = {};
            if (result && result.sendForumPost && processVariables)
                finalResult = { postData: result.sendForumPost, botData: processVariables };
            callback(null, finalResult);
        }
    })
};

const fetchLithiumForums = function (botSettings, fields, condition, limit, offset, messages, cb) {
    var query = "SELECT " + fields.join(',') + " FROM messages WHERE " + condition + ' limit ' + limit + ' offset ' + offset;
    let headers = {
        'authorization': 'Bearer ' + botSettings.accessToken,
        'content-type': 'application/json'
    }
    let body = "";
    let url = botSettings.url + '/api/2.0/search'
    commonFunctions.errorlogger.info(query);
    commonFunctions.httpRequest('GET', url, query, body, headers, function (error, result) {
        if (!error && result.http_code == 200) {
            messages = messages.concat(result.data.items);
            if (result.data.items.length < parseInt(limit)) {
                getTagsForMessages(botSettings, messages, (error, result) => {
                    commonFunctions.errorlogger.info("Posts Fetched: ", result.length);
                    cb(null, result);
                });
            } else {
                commonFunctions.errorlogger.info("offset is", offset)
                offset += parseInt(limit);
                fetchLithiumForums(botSettings, fields, condition, limit, offset, messages, cb);
            }
        }
        else {
            commonFunctions.errorlogger.error(error);
            cb('error');
        }
    });
}

router.get('/lithiumForumPost', function (req, res) {
    var botId = req.query.botId;
    /* let outputFile = 'customPlugins/communityHelper/logs/lithium_' + botId + "_" + (new Date()).toISOString() + '.log'
     const out = fs.openSync(outputFile, 'a');
     const err = fs.openSync(outputFile, 'a');
 
     const subprocess = spawn('node', [path.resolve(process.cwd(), "customPlugins/communityHelper/lithiumUtilities/lithiumPosts.js"), botId], {
         detached: true,
         stdio: ['ignore', out, err]
     });
 SELECT * FROM boards WHERE ancestor_categories.id = 'external'
     subprocess.unref();
     let passData = {
         pid: subprocess.pid,
         filename: outputFile
     };
     res.send("Process started");*/

    preProcessForumPost(botId, 1, req, (error, result) => {
        if (result) {
            botAnalytics.insertBotAnalytics(result, 0, req, (aError, aResult) => {
                res.send(result)
            })
        } else {
            res.send(error);
        }
    })
});

router.get('/getForumKudos', function (req, res) {
    const tenantId = req.headers['tenant-id'];
    async.auto({
        getBotSettings: (cb) => {
            if (req.query.botId)
                communityHelperSettings.getBotSettings(req.query.botId, tenantId, cb);
            else
                cb([], null);
        },
        getRefreshToken: ["getBotSettings", (dataFromAbove, cb) => {
            getRefreshToken(dataFromAbove.getBotSettings, (error, token) => {
                if (error) cb(error);
                else {
                    dataFromAbove.getBotSettings.accessToken = token;
                    cb(null, []);
                }
            });
        }],
        getKudos: ["getRefreshToken", (dataFromAbove, cb) => {
            if (req.query.replyId) {
                getKudos(dataFromAbove.getBotSettings, req.query.replyId, function (err, result) {
                    if (err) cb(err, null);
                    cb(null, result);
                });
            } else {
                cb([], null);
            }
        }]
    }, function (error, result) {
        let response = { "flag": 400, "data": [] }
        if (error) {
            commonFunctions.errorlogger.info("add comment in community helper", error);
        } else {
            response.flag = 200;
            response.data = result.getKudos;
        }
        res.send(response);
    })
});

const getKudos = (botSettings, replyId, cb) => {
    var query = "SELECT kudos.sum(weight) FROM messages WHERE id='" + replyId + "'";
    let headers = {
        'authorization': 'Bearer ' + botSettings.accessToken,
        'content-type': 'application/json'
    }
    let body = "";
    let url = botSettings.url + '/api/2.0/search'
    commonFunctions.errorlogger.info(query);
    commonFunctions.httpRequest('GET', url, query, body, headers, function (error, result) {
        if (!error) {
            if (result.data.items && result.data.items[0])
                cb(null, result.data.items[0].kudos.sum.weight);
            else

                cb([], null);
        }
        else
            cb("Unauthorised user", null);
    });
};

router.get('/getSolvedForums', function (req, res) {
    const tenantId = req.headers['tenant-id'];
    fetchForumPosts(req.query.botId, tenantId, (error, result) => {
        res.send(result);
    })
});

const fetchForumPosts = (botId, tenantId) => {
    async.auto({
        getBotSettings: (cb) => {
            if (botId)
                communityHelperSettings.getBotSettings(botId, tenantId, cb);
            else
                cb([], null);
        },
        getRefreshToken: ["getBotSettings", (dataFromAbove, cb) => {
            getRefreshToken(dataFromAbove.getBotSettings, (error, token) => {
                if (error) cb(error);
                else {
                    dataFromAbove.getBotSettings.accessToken = token;
                    cb(null, []);
                }
            });
        }],
        getSolvedPosts: ["getRefreshToken", (dataFromAbove, cb) => {
            getSolvedPosts(dataFromAbove.getBotSettings, function (err, result) {
                if (err) cb(err, null);
                cb(null, result);
            });
        }]
    }, function (error, result) {
        let response = { "flag": 400, "data": [] }
        if (error) {
            commonFunctions.errorlogger.info("add comment in community helper", error);
        } else {
            response.flag = 200;
            response.data = result.getSolvedPosts;
        }
        res.send(response);
    })
}

// forumsTrain.runForumTrainer

module.exports = { router, preProcessForumPost };