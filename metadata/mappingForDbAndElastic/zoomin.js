const crawlerCache = require('../../utils/crawler-config-cache');


const initZoominConfig =  ( tenantId,  cb ) => {

crawlerCache.fetchCrawlerConfig({ tenantId, RETRY_COUNT: 1,  configKey: 'zoomin' }, function(response){

let metaData = {
    "objects": [{
        "name": "zoomin_page",
        "label": "Zoomin Page",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [{
        "name": "id",
        "label": "Bundle Id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "title",
        "label": "Title",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "rating",
        "label": "Rating",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "breadcrumbs",
        "label": "Breadcrumbs",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "bundle_title",
        "label": "Bundle Title",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "topic_language",
        "label": "Topic Language",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "keywords",
        "label": "Keywords",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "views",
        "label": "Views",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "post_time",
        "label": "Created Date",
        "type": "datetime",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 0,
        "isActive": 1
    },
    {
        "name": "updated_date",
        "label": "Modified Date",
        "type": "datetime",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 0,
        "isActive": 1
    },
    {
        "name": "description",
        "label": "Description",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "view_href",
        "label": "View Href",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "product",
        "label": "Product",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "task",
        "label": "Task",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "version",
        "label": "Version",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "page_id",
        "label": "Page Id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "bundle_id",
        "label": "Bundle Id",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    },
    {
        "name": "information_type",
        "label": "Information Type",
        "type": "string",
        "isFilterable": 1,
        "isSortable": 1,
        "isSearchable": 1,
        "isActive": 1
    }
    ]
}
if(response.config_value.japaneseContent) {
    let jpmeta = [{
            "name": "content_jp",
            "label": "Japanese",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        }]
    metaData.fields.push(...jpmeta)
}

  cb(null, metaData);
}) }

module.exports = {
  initZoominConfig : initZoominConfig
}
