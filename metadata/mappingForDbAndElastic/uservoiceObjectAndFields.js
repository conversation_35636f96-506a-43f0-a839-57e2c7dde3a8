module.exports = {
    "objects": [
        {
            "name": "ideas",
            "label": "Ideas",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields": [
                {
                    "name": "url",
                    "label": "URL",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "id",
                    "label": "Idea Id",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "created_date",
                    "label": "Created At",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1,
                    "isMerged": 1
                },
                {
                    "name": "comments_count",
                    "label": "Comments Count",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "notes",
                    "label": "Notes",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "status",
                    "label": "Status",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "last_updated",
                    "label": "Updated At",
                    "type": "datetime",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 0,
                    "isActive": 1,
                    "isMerged": 1
                },
                {
                    "name": "label",
                    "label": "Label",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "forumId",
                    "label": "Forum Id",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "votes",
                    "label": "Votes",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "type",
                    "label": "Type",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "feedbacks",
                    "label": "Feedbacks",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "name",
                    "label": "Name",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "body",
                    "label": "Body",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "category",
                    "label": "Category",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": 'attachment_multiple',
                    "label": 'Attachments',
                    "type": 'string',
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1,
                    "isMerged": 1
                }
            ]
        }
    ] 
}