module.exports = {
    "objects": [
        {
            "name": "features",
            "label": "Features",
            "status": 1,
            "boosting_factor": 1,
            "base_url": "",
            "fields": []
        }
    ],
    "fields": [
        {
            "name": "id",
            "label": "Id",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "title",
            "label": "Title",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "description",
            "label": "Description",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "resolution",
            "label": "Resolution",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "status",
            "label": "Status",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "post_time",
            "label": "Created Date",
            "type": "datetime",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "products",
            "label": "Products",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "html_url",
            "label": "View Href",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "form_entry",
            "label": "Form Entry",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "uploads",
            "label": "Uploads",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        }
    ]
}
