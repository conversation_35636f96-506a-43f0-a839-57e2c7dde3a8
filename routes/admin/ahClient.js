const express = require('express');
const router = express.Router();
const async = require('async');
const archiver = require('archiver');
const fs = require('fs');
const path = require('path');
const replace = require('stream-replace');
const axios = require('axios');
var commonFunctions = require('./../../utils/commonFunctions');
const kafkaLib = require('../../utils/kafka/kafka-lib');
const config = require('config');
const { uuid } = require('uuidv4');
const adminUrl = config.get("adminURL")

const upsertAhClient = async (req) => {
  return new Promise((resolve, reject) => {
    const input = req.body;
    const {
      id,
      agentHelperId,
      searchClientId,
      searchClientName,
      platformType,
      clientId,
      clientSecret,
      clientUrl,
      indexName,
      searchClientRACitations,
      agentHelperName
    } = input;
  
    connection[req.headers['tenant-id']].execute.query(`
      INSERT INTO ah_clients (
        id,
        agent_helper_id,
        search_client_id,
        search_client_name,
        platform_type,
        client_id,
        client_secret,
        client_url,
        index_name,
        search_client_ra_citations,
        agent_helper_name
      )
        VALUES (?,?,?,?,?,?,?,?,?,?,?)
      ON DUPLICATE KEY UPDATE
        search_client_name = VALUES(search_client_name),
        search_client_id = VALUES(search_client_id),
        platform_type = VALUES(platform_type),
        client_id = VALUES(client_id),
        client_secret = VALUES(client_secret),
        client_url = VALUES(client_url),
        index_name = VALUES(index_name),
        search_client_ra_citations = VALUES(search_client_ra_citations),
        agent_helper_name = VALUES(agent_helper_name)
    `, [id, agentHelperId, searchClientId, searchClientName, platformType, clientId, clientSecret, clientUrl, indexName, searchClientRACitations, agentHelperName], (err) => {
      if (err) {
        commonFunctions.errorlogger.error('Error while upserting AH Client Details: ', err);
        reject(false);
      }
  
      resolve(true);
    })
  });
};

const upsertSlackConfig = async (req) => {
  return new Promise((resolve, reject) => {
    const input = req.body;
    const {
      id,
      agentHelperId,
      searchClientId,
      searchClientName,
      platformType,
      slackClientId,
      slackClientSecret,
      slackEnabled
    } = input;
  
    connection[req.headers['tenant-id']].execute.query(`
      INSERT INTO ah_clients (
        id,
        agent_helper_id,
        search_client_id,
        search_client_name,
        platform_type,
        slack_client_id,
        slack_client_secret,
        slack_enabled
      )
        VALUES (?,?,?,?,?,?,?,?)
      ON DUPLICATE KEY UPDATE
        slack_client_id = VALUES(slack_client_id),
        slack_client_secret = VALUES(slack_client_secret),
        slack_enabled = VALUES(slack_enabled)
    `, [id, agentHelperId, searchClientId, searchClientName, platformType, slackClientId, slackClientSecret, slackEnabled], (err) => {
      if (err) {
        commonFunctions.errorlogger.error('Error while upserting Slack Config Details: ', err);
        reject(false);
      }
  
      resolve(true);
    })
  });
};

const getAhClients = async (req, ahId) => {
  return new Promise((resolve, reject) => {
    let query;

    if (ahId) {
      query = `
        SELECT
          id,
          agent_helper_id as agentHelperId,
          search_client_id as uid,
          search_client_name as search_client_name,
          platform_type as type,
          created_date as createdDate,
          client_id,
          client_secret,
          client_url,
          index_name,
          slack_client_id,
          slack_client_secret,
          search_client_ra_citations,
          slack_enabled as slackEnabled,
          agent_helper_name
        FROM
          ah_clients ac
        WHERE
          id = ?
      `;
    } else {
      query = `
        SELECT
          id,
          agent_helper_id as agentHelperId,
          search_client_id as uid,
          search_client_name as search_client_name,
          platform_type as type,
          created_date as createdDate,
          slack_client_id as slackClientId,
          slack_enabled as slackEnabled,
          agent_helper_name,
          index_name
        FROM
          ah_clients ac
        ORDER BY
          created_date DESC;
      `;
    }

    connection[req.headers['tenant-id']].execute.query(query, [ahId], (err, result) => {
      if (err) {
        commonFunctions.errorlogger.error('Error while fetching AH Client(s): ', err);
        reject(new Error('Error encountered while fetching AH Client(s)'));
      }

      resolve(result);
    });
  });
};

const deleteAhClient = async (req) => {
  return new Promise((resolve, reject) => {
    const input = req.body;
    const {
      agentHelperId
    } = input;
  
    connection[req.headers['tenant-id']].execute.query(`
      DELETE FROM ah_clients WHERE agent_helper_id = ?
    `, [agentHelperId], (err) => {
      if (err) {
        commonFunctions.errorlogger.error('Error while deleting ah client details: ', err);
        reject(false);
      }
  
      resolve(true);
    })
  });
};

const handleAxiosError = (serviceName, err) => {
  if (err.response) {
    // Request made but the server responded with an error
    commonFunctions.errorlogger.error(`${serviceName} service responded with error: ${err.response.status} - ${err.response.statusText}`);
    return {
      statusCode: err.response.status,
      status: false,
      message: err.response.statusText
    };
  } else if (err.request) {
    // Request made but no response is received from the server
    commonFunctions.errorlogger.error(`No response received from ${serviceName} service. Error: ${err.message}`);
    return { status: false, message: 'Something went wrong. Please try again.'};
  } else {
    // Error occured while setting up the request
    commonFunctions.errorlogger.error('Error:', err.message);
    return { status: false, message: 'An unexpected error occurred.' };
  }
};

router.get('/download/:id', async (req, res) => {
  try {
    const ahId = req.params.id;
    const type = req.query.type;
    const ahClient = await getAhClients(req, ahId);

    if (ahClient.length) {
      const SEARCH_CLIENT_SERVICE_URL = `${config.get("searchClient.url")}/sc/ahClient/download`;
      const options = {
        params: {
          ahId: ahClient[0].agentHelperId,
          platform: ahClient[0].type,
          type
        },
        headers: {
          'Content-Type': 'application/json',
          'Cookie': req.headers.cookie || '',
          'CSRF-Token' : req.headers['csrf-token'] || ''
        },
        responseType: 'stream',
        timeout: 30000
      };

      const response = await axios.get(SEARCH_CLIENT_SERVICE_URL, options);
      if (response && response.data) {
        res.setHeader('Content-disposition', 'attachment; filename=agent_helper.zip');
        response.data.pipe(res);
      } else {
        throw new Error('Received an empty response from the search-client service.');
      }
    } else {
      return res.status(400).send({
        status: false,
        message: 'Agent Helper does not exist for the provided ID.'
      });
    }
  } catch (err) {
    let statusCode, errorObj;
    if (err.isAxiosError) {
      ({ statusCode, ...errorObj } = handleAxiosError('search-client', err));
    } else {
      commonFunctions.errorlogger.error('Error:', err.message);
      errorObj = {
        status: false,
        message: 'Internal Server Error'
      }
    }
    res.status(statusCode || 500).send(errorObj);
  }
});

router.post('/', async (req, res) => {
  if (!req.body.agentHelperId) {
    req.body.agentHelperId = uuid(); //here we also need to check if details are being editted then not to make again 
  }
  try {
    const isUpserted = await upsertAhClient(req);

    if (isUpserted) {
      const {
        agentHelperId,
        searchClientId,
        searchClientName,
        platformType,
        clientId,
        clientSecret,
        clientUrl,
        indexName,
        searchClientRACitations,
        agentHelperName
      } = req.body;

      let data = {
        event: 'insert',
        tenantId: req.headers['tenant-id'],
        client: {
          agentHelperId,
          agentHelperName,
          searchClientId,
          searchClientName,
          platformType,
          clientId,
          clientSecret,
          clientUrl,
          indexName,
          searchClientRACitations,
          adminUrl
        }
      };
      kafkaLib.publishMessage({
        topic: config.get('kafkaTopic.agentHelperClient'),
        messages: [
          {
            value: JSON.stringify(data),
            key: req.body.agentHelperId
          }
        ]
      });

      res.send({
        staus: true,
        message: "Done"
      });
    } else {
      throw new Error('Failed to insert/update AH Client details');
    }
  } catch (err) {
    res.status(500).send({
      status: false,
      message: 'Internal Server Error'
    });
  }
});

router.post('/slack', async (req, res) => {
  try {
    const isUpserted = await upsertSlackConfig(req);

    if (isUpserted) {
      const {
        agentHelperId,
        searchClientId,
        searchClientName,
        platformType,
        slackClientId,
        slackClientSecret
      } = req.body;

      let data = {
        event: 'insert',
        tenantId: req.headers['tenant-id'],
        client: {
          agentHelperId,
          searchClientId,
          searchClientName,
          platformType,
          slackConfig: {
            slackClientId,
            slackClientSecret
          }
        }
      };
      kafkaLib.publishMessage({
        topic: config.get('kafkaTopic.slackConfigAh'),
        messages: [
          {
            value: JSON.stringify(data),
            key: req.body.agentHelperId
          }
        ]
      });

      res.send({
        staus: true,
        message: "Done"
      });
    } else {
      throw new Error('Failed to insert/update slack details');
    }
  } catch (err) {
    res.status(500).send({
      status: false,
      message: `Internal Server Error`
    });
  }
});

router.get('/:id?', async (req, res) => {
  try {
    const ahId = req.params.id;

    const ahClients = await getAhClients(req, ahId);

    res.send({
      status: true,
      message: ahId ? 'Agent Helper Client Details' : 'Agent Helper Clients',
      data: ahClients
    });
  } catch (err) {
    res.status(500).send({
      status: false,
      message: err.message
    });
  }
});

router.post('/delete', async (req, res) => {
  try {
    const isUpserted = await deleteAhClient(req);

    if (isUpserted) {
      const {
        agentHelperId
      } = req.body;

      let data = {
        event: 'delete',
        tenantId: req.headers['tenant-id'],
        client: {
          agentHelperId
        }
      };
      kafkaLib.publishMessage({
        topic: config.get('kafkaTopic.agentHelperClient'),
        messages: [
          {
            value: JSON.stringify(data),
            key: req.body.agentHelperId
          }
        ]
      });

      res.send({
        staus: true,
        message: "Done"
      });
    } else {
      throw new Error('Failed to Delete AH Client details');
    }
  } catch (err) {
    res.status(500).send({
      status: false,
      message: 'Internal Server Error'
    });
  }
});

module.exports = router;
