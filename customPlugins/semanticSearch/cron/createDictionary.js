var express = require('express');
var router = express.Router();
var path = require('path');
var elastic = require('elasticsearch');
var fs = require('fs');
var async = require('async');
environment = require(path.join(__dirname, '../../../routes/environment'));
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../../config');
process.env.NODE_CONFIG_DIR = configPath;
console.log(environment.configuration);
config = require('config');
var connection_sql = require('./../../../utils/connection');
const unique = require('unique-words');
var stopwords = require('nltk-stopwords');
var english = stopwords.load('english')

const { getEsClient } = require('../../../utils/elastic');

var uniquieArray;

const createDictionary = async (req, cb) => {
    const tenantId = req.headers['tenant-id']
    start(req,tenantId, cb);
}

function start(req,tenantId, cb) {
    async.auto({
        "sqlConfiguration": cb => {
            getSqlConfiguration(req,function (err, data) {
                cb(err, data);
            });
        },
        "ContentSourceData": ["sqlConfiguration", function (DFA, cb) {
            var asyncClientTask = [];
            var searchClients = Object.keys(DFA.sqlConfiguration);
            for (var z = 0; z < searchClients.length; z++) {
                asyncClientTask.push(function (z) {
                    return function (cbz) {
                        // Reset Uniquie Array to []
                        uniquieArray = [];
                        console.log("Fetching data for " + searchClients[z]);
                        var asyncIndexTasks = [];
                        var csIndices = Object.keys(DFA.sqlConfiguration[searchClients[z]]);
                        for (var i = 0; i < csIndices.length; i++) {
                            asyncIndexTasks.push(function (i) {
                                return function (cbx) {
                                    console.log("Fetching data for " + csIndices[i]);
                                    asyncObjectTasks = [];
                                    var csObjects = Object.keys(DFA.sqlConfiguration[searchClients[z]][csIndices[i]]);
                                    for (var j = 0; j < csObjects.length; j++) {
                                        asyncObjectTasks.push(function (j) {
                                            return function (cby) {
                                                console.log("Fetching data for " + csIndices[i] + "-->" + csObjects[j]);
                                                getCSData(0, 1000,`${csIndices[i]}` , csObjects[j], DFA.sqlConfiguration[searchClients[z]][csIndices[i]][csObjects[j]], null, tenantId,  function (err, dataFinal) {
                                                    if (err) {
                                                        console.log(err);
                                                        cby(null, null);
                                                    }
                                                    else {
                                                        writeDict(searchClients[z], function (err, data) {
                                                            cby(null, "write Done");
                                                        })
                                                    }
                                                })
                                            }
                                        }(j));
                                    }
                                    async.series(asyncObjectTasks, function (err, data) {
                                        cbx(null, "done y");
                                    });
                                }
                            }(i));
                        }
                        async.series(asyncIndexTasks, function (err, data) {
                            cbz(null, "done x");
                        });
                    }
                }(z));
            }
            async.series(asyncClientTask, function (err, data) {
                cb(null, null);
            });
        }]
    }, function (err, data) {
        if (err) {
            console.log(":: ERROR F:strat " + err);
        }
        else {
            console.log("Completed");
        }
        cb() ;
    })
}


async function getCSData(from, size, indexName, typeName, fieldsArray, next,tenantId,  cb) {
    const esClient = await getEsClient(tenantId);
    console.log("FROM :: " + from);
    console.log("SIZE :: " + size);
    const indexExists = await esClient.indices.exists({ index: indexName + "__" + typeName });
    if (indexExists.body) {
        if (!next) {
            esClient.search({
                index: indexName + "__"+typeName,
                size: size,
                from: from,
                scroll: '1h',
                body: {
                    "_source": {
                        "includes": fieldsArray
                    }
                }
            }, function (err, data) {
                if (err) {
                    cb(err, null);
                }
                else {
                    proceesUniquie(data);
                    let scroll_Id = data.body._scroll_id;
                    if (from + size < data.body.hits.total) {
                        data = null;
                        getCSData(from + 1000, size, indexName, typeName, fieldsArray, scroll_Id, tenantId,  function (err, data1) {
                            if (err) {
                                cb(err, null);
                            }
                            else {
                                cb(null, "Done");
                            }
                        });
                    }
                    else {
                        cb(null, "Done All")
                    }
                }
            });
        }
        else {
            esClient.scroll({
                scrollId: next,
                scroll: '1h'
            }, function (err, data) {
                if (err) {
                    cb(err, null);
                }
                else {
                    proceesUniquie(data);
                    let scroll_Id = data.body._scroll_id;
                    if (from + size < data.body.hits.total) {
                        data = null;
                        getCSData(from + 1000, size, indexName, typeName, fieldsArray, scroll_Id, tenantId,  function (err, data1) {
                            if (err) {
                                cb(err, null);
                            }
                            else {
                                cb(null, "Done");
                            }
                        });
                    }
                    else {
                        cb(null, "Done All")
                    }
                }
            })
        }
    }else{
        console.log("index not found");
        cb(null, "Done All")
    }

}

function proceesUniquie(data) {
    let content = data.body.hits.hits;
    var concatArr = [];
    if (content.length > 0) {
        for (var i = 0; i < content.length; i++) {
            let FieldKeys = Object.keys(content[i]._source);
            for (var j = 0; j < FieldKeys.length; j++) {
                if(content[i]._source[FieldKeys[j]] && typeof(content[i]._source[FieldKeys[j]]) == "string"){
                    let lowerCaseData = content[i]._source[FieldKeys[j]].toLowerCase();
                    let newUniquieWords = unique(lowerCaseData);
                    let stopFilteredWords = stopwords.remove(newUniquieWords, english);
                    let numericFilteredWords = stopFilteredWords.filter(x => {
                        return !(x.match("[0-9]"));// return !(x.match("^[0-9]+$"));
                    });
                    var lengthCheck = numericFilteredWords.filter(x => {
                        return x.length > 2;
                    })
                    concatArr = concatArr.concat(lengthCheck);
                }
            }
        }
        var start = new Date();
        uniquieArray = unique(uniquieArray, concatArr);
        console.log(new Date().getTime() - start.getTime());
        console.log("uniquieArray Length -->>", uniquieArray.length);
    }
    data = null;
    content = null;
    concatArr = null;
}

function writeDict(fileName, cb) {
    fs.writeFile(__dirname + '/dictionaryFiles/' + fileName + '_dict.txt', uniquieArray, function (err) {
        if (err) {
            console.log("ERROR :: " + err);
            cb(err, null);
        }
        else {
            console.log("Write Done");
            cb(null, "done");
        }
    });
}

function getSqlConfiguration(req,cb) {
    var jsonSql = {};
    var sql = "select DISTINCT(ats.content_source_object_id), ats.content_source_id, ats.content_source_object_field_id, csof.name as fieldName, cs.elasticIndexName as indexName, cso.name as objectName, sc.uid as uid "
        + "FROM  auto_tuning_settings as ats "
        + "LEFT JOIN content_sources as cs  on cs.id = ats.content_source_id "
        + "LEFT JOIN content_source_objects as cso on cso.id = ats.content_source_object_id "
        + "LEFT JOIN content_source_object_fields csof on csof.id =ats.content_source_object_field_id "
        + "LEFT JOIN search_clients as sc on sc.id = ats.search_client_id "
        + "WHERE auto_tuning_configuration_type = 0 AND is_selected =1 AND sc.auto_spell_corrector = 1";
        connection[req.headers['tenant-id']].execute.query(sql, function (err, data) {
        if (err) {
            console.log("ERROR :: F:getSqlConfiguration" + err);
            cb(err, null);
        }
        else if (data.length <= 0) {
            console.log("ERROR :: F:getSqlConfiguration no content source");
            cb("no content source", null);
        }
        else {
            data.map(x => {
                if (!jsonSql[x.uid]) {
                    jsonSql[x.uid] = {};
                }
                if (!jsonSql[x.uid][x.indexName]) {
                    jsonSql[x.uid][x.indexName] = {};
                }
                if (!jsonSql[x.uid][x.indexName][x.objectName]) {
                    jsonSql[x.uid][x.indexName][x.objectName] = [];
                }
                if(x.fieldName && x.fieldName.includes('.'))
                    x.fieldName = x.fieldName.replace(/\./g,'_');
                    
                jsonSql[x.uid][x.indexName][x.objectName].push(x.fieldName);
            });
            // console.log(jsonSql);
            cb(null, jsonSql);
        }
    });
}

module.exports = {
    createDictionary
}