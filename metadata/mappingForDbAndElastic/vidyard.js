module.exports = {
    "objects": [{
        "name": "video",
        "label": "Vidyard Content",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [
        {
            "name": "id",
            "label": "id",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "name",
            "label": "Title",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "post_time",
            "label": "Created Date",
            "type": "datetime",
            "isFilterable": 1,
            "isMerged" : 1,
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1
        },
        {
            "name": "updated_at",
            "label": "Updated Date",
            "type": "datetime",
            "isFilterable": 1,
            "isMerged" : 1,
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1
        },
        {
            "name": "description",
            "label": "Description",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'upload_url',
            "label": 'Upload URL',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'url',
            "label": 'URL',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'tags_attributes',
            "label": 'Tags',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'user_id',
            "label": 'User Id',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'current_thumbnail_id',
            "label": 'Thumbnail Id',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'notes',
            "label": 'Notes',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'uuid',
            "label": 'UUId',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'status',
            "label": 'Status',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": 'user_team_id',
            "label": 'User Team Id',
            "type": 'string',
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "thumbnail",
            "label": "Thumbnail",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1,
            "isMerged": 1
        },
        {
            "name": "captions",
            "label": "Captions",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1,
            "isMerged": 1
        },
        {
            "name": "hub_name",
            "label": "Hub name",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 0,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "folder_name",
            "label": "Folder name",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 0,
            "isSearchable": 1,
            "isActive": 1
        }, 
        {
            "name": "subtitle__body__s",
            "label": "Subtitle Body",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        },
        {
            "name": "subtitles",
            "label": "Subtitles",
            "type": "string",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 1,
            "isActive": 1
        }
    ]
}
