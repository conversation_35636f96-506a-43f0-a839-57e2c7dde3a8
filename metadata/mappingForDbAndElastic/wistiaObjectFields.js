module.exports = {
    "objects": [{
        "name": "media",
        "label": "Media",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": [
            {
                "name": "projectId",
                "label": "Project ID",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "projectName",
                "label": "Project Name",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "public",
                "label": "Public",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "projectDescription",
                "label": "Project Description",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "mediaType",
                "label": "Media Type",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "mediaName",
                "label": "Media Name",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "mediaId",
                "label": "Media ID",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "mediaDescription",
                "label": "Media Description",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "duration",
                "label": "Duration",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1
            },
            {
                "name": "imageSource",
                "label": "Thumbnail Url",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1,
                "isMerged" : 1
            },
            {
                "name": "createdAt",
                "label": "Created At",
                "type": "datetime",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1,
                "isMasked": 0
            },
            {
                "name": "updatedAt",
                "label": "Updated At",
                "type": "datetime",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1,
                "isMasked": 0
            },
            {
                "name": "viewHref",
                "label": "View Href",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1,
                "isMasked": 0
            },
            {
                "name": "hashedId",
                "label": "Hash ID",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 0,
                "isActive": 1
            },
            {
                "name": "subtitle__body__s",
                "label": "Subtitle Body",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            },
            {
                "name": "subtitles",
                "label": "Subtitles",
                "type": "string",
                "isFilterable": 1,
                "isSortable": 1,
                "isSearchable": 1,
                "isActive": 1
            }
        ]
    }],
    "fields": []
}
