var request = require("request");
const kafka = require("../kafka-lib");
const kafkaStatusLib = require("../../kafka-status-page/kafka-lib");

var config = require("config");
const {
    errorlogger, fetchIndexingConfig
} = require("../../commonFunctions");

/**
 * tenantGlobals - publishes tenantGlobals kafka topic
 * @param {string} tenantId - tanant ID
 * @param {object} incoming - incoming data to publish
 */
const tenantGlobals = async (tenantId, incoming = {}) => {
    errorlogger.info('>>>tenantGlobals');
    try {
        const data = { tenantId, globals : {  ...incoming } };

        /* populate gpt with existing data if does not exist */
        if (!incoming.hasOwnProperty("gpt")) {
            const sql = "SELECT gpt FROM tenant_globals";
            const gpt = await new Promise((s, x) => {
                connection[tenantId].execute.query(sql, [], (err, rows) => {
                    if (err || !rows || !rows.length) {
                        return x(err || "no data found in tenant_globals");
                    }
                    s(rows[0].gpt);
                });
            });
            data.globals.gpt = JSON.parse(gpt);
        }

        /* populate exact match with existing data if does not exist */
        if (!incoming.hasOwnProperty("exactMatch")) {
            const sql = `SELECT * FROM synonym_settings`;
            const exactMatch = await new Promise((s, x) => {
                connection[tenantId].execute.query(sql, function (err, rows) {
                    if (err || !rows || !rows.length) {
                        return x(err || "no data found in synonym_settings");
                    }
                    s(rows[0].exact_match == 1);
                });
            });
            data.globals.exactMatch = exactMatch;
        }

        /* populate synonym search (nlp feature) with existing data if does not exist */
        if (!incoming.hasOwnProperty("useSearchSynonym")) {
            let useSearchSynonym = {};
            
            var getSearchSynonymOptions = {
                method: 'GET',
                rejectUnauthorized: false,
                url: `${config.get("MLService.url")}/get-select-synonym`,
                headers: {
                    'tenant-id': tenantId,
                },
                json: true,
                body: {
                    client_id: tenantId
                }
            };
            useSearchSynonym = await new Promise((resolve, reject) => {
                try{
                    errorlogger.info(`Sending request ${JSON.stringify(getSearchSynonymOptions)}`);
                    request(getSearchSynonymOptions, function (error, response, body) {
                        if (error) {
                            errorlogger.error('Could not fetch details for /get-select-synonym ', error);
                            resolve({});
                        }
                        else if (body && body.getSelectSynonym){
                            errorlogger.info(`Response received: ${body}`);
                            resolve(body.getSelectSynonym)
                        }
                        resolve({});
                    });
                } catch(err) {
                    error("Error while fetching useSearchSynonym from ML",err);
                    resolve({});
                }
            });
            data.globals.useSearchSynonym = useSearchSynonym || {};
        }

        errorlogger.info(`Tenant Data to publish: ${JSON.stringify(data)}`);
        /* publish tenantGlobals kafka topic */
        kafka.publishMessage({
            topic: config.get("kafkaTopic.tenantGlobals"),
            messages: [
                {
                    value: JSON.stringify(data),
                },
            ],
        });

        kafkaStatusLib.publishMessage({
            topic: config.get("kafkaTopic.tenantGlobals"),
            messages: [
                {
                    value: JSON.stringify(data),
                },
            ],
        });
        errorlogger.info('tenantGlobals<<<');
    } catch (e) {
        errorlogger.error(e);
    }
};

module.exports = {
    tenantGlobals,
};
