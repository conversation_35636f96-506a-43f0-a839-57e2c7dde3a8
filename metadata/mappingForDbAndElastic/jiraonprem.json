{"projects": [{"spaceName": "Dimple Project Testing", "spaceUrl": "http://45.79.128.241:8080/browse/DPT", "spaceKey": "DPT", "spaceId": "10200", "isSelected": 0}, {"spaceName": "Grazitti Connector", "spaceUrl": "http://45.79.128.241:8080/browse/GC", "spaceKey": "GC", "spaceId": "10000", "isSelected": 0}, {"spaceName": "JIRA Connector", "spaceUrl": "http://45.79.128.241:8080/browse/JC", "spaceKey": "JC", "spaceId": "10001", "isSelected": 0}, {"spaceName": "sandeep test", "spaceUrl": "http://45.79.128.241:8080/browse/ST", "spaceKey": "ST", "spaceId": "10100", "isSelected": 0}], "objects": {"fields": [{"isFilterbale": 0, "name": "issuetype", "isSelected": 0, "label": "IssueType", "isSearchable": 1, "type": "issuetype"}, {"isFilterbale": 0, "name": "timespent", "isSelected": 0, "label": "TimeSpent", "isSearchable": 1, "type": "number"}, {"isFilterbale": 0, "name": "project", "isSelected": 0, "label": "Project", "isSearchable": 1, "type": "project"}, {"isFilterbale": 0, "name": "fixVersions", "isSelected": 0, "label": "FixVersions", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "aggregatetimespent", "isSelected": 0, "label": "TimeSpent", "isSearchable": 1, "type": "number"}, {"isFilterbale": 0, "name": "resolution", "isSelected": 0, "label": "Resolution", "isSearchable": 1, "type": "resolution"}, {"isFilterbale": 0, "name": "customfield_10500", "isSelected": 0, "label": "CustomTest", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10302", "isSelected": 0, "label": "ExampleCustom", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10104", "isSelected": 0, "label": "Sprint", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "customfield_10501", "isSelected": 0, "label": "CustomerNames", "isSearchable": 1, "type": "option"}, {"isFilterbale": 0, "name": "customfield_10105", "isSelected": 0, "label": "Rank", "isSearchable": 1, "type": "any"}, {"isFilterbale": 0, "name": "customfield_10502", "isSelected": 0, "label": "CustomTest", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10106", "isSelected": 0, "label": "StoryPoints", "isSearchable": 1, "type": "number"}, {"isFilterbale": 0, "name": "customfield_10503", "isSelected": 0, "label": "CustomTest", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10504", "isSelected": 0, "label": "Testing1", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10505", "isSelected": 0, "label": "Testing2", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "resolutiondate", "isSelected": 0, "label": "Resolved", "isSearchable": 0, "type": "datetime"}, {"isFilterbale": 0, "name": "customfield_10506", "isSelected": 0, "label": "SFCustom", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "<PERSON><PERSON><PERSON>", "isSelected": 0, "label": "WorkRatio", "isSearchable": 1, "type": "number"}, {"isFilterbale": 0, "name": "lastViewed", "isSelected": 0, "label": "LastViewed", "isSearchable": 0, "type": "datetime"}, {"isFilterbale": 0, "name": "watches", "isSelected": 0, "label": "Watchers", "isSearchable": 1, "type": "watches"}, {"isFilterbale": 0, "name": "thumbnail", "isSelected": 0, "label": "Images", "isSearchable": 1, "type": ""}, {"isFilterbale": 0, "name": "created", "isSelected": 0, "label": "Created", "isSearchable": 0, "type": "datetime"}, {"isFilterbale": 0, "name": "customfield_10220", "isSelected": 0, "label": "CUSJira", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "priority", "isSelected": 0, "label": "Priority", "isSearchable": 1, "type": "priority"}, {"isFilterbale": 0, "name": "customfield_10100", "isSelected": 0, "label": "EpicLink", "isSearchable": 1, "type": "any"}, {"isFilterbale": 0, "name": "customfield_10221", "isSelected": 0, "label": "JobCheckbox", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10101", "isSelected": 0, "label": "EpicS<PERSON>us", "isSearchable": 1, "type": "option"}, {"isFilterbale": 0, "name": "customfield_10300", "isSelected": 0, "label": "CaseID", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10102", "isSelected": 0, "label": "EpicName", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "labels", "isSelected": 0, "label": "Labels", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "customfield_10301", "isSelected": 0, "label": "AccountNamesSFDC", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "customfield_10103", "isSelected": 0, "label": "EpicColor", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10214", "isSelected": 0, "label": "newtextfield", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10215", "isSelected": 0, "label": "SalesforceAccountNames", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10216", "isSelected": 0, "label": "ReportTest", "isSearchable": 1, "type": "number"}, {"isFilterbale": 0, "name": "customfield_10217", "isSelected": 0, "label": "URL", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "timeestimate", "isSelected": 0, "label": "RemainingEstimate", "isSearchable": 1, "type": "number"}, {"isFilterbale": 0, "name": "aggregatetimeoriginalestimate", "isSelected": 0, "label": "OriginalEstimate", "isSearchable": 1, "type": "number"}, {"isFilterbale": 0, "name": "customfield_10218", "isSelected": 0, "label": "NEWURLFIELD", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "versions", "isSelected": 0, "label": "AffectsVersions", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "customfield_10219", "isSelected": 0, "label": "123", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "issuelinks", "isSelected": 0, "label": "LinkedIssues", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "assignee", "isSelected": 0, "label": "Assignee", "isSearchable": 1, "type": "user"}, {"isFilterbale": 0, "name": "updated", "isSelected": 0, "label": "Updated", "isSearchable": 0, "type": "datetime"}, {"isFilterbale": 0, "name": "status", "isSelected": 0, "label": "Status", "isSearchable": 1, "type": "status"}, {"isFilterbale": 0, "name": "components", "isSelected": 0, "label": "Components", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "issuekey", "isSelected": 0, "label": "Key", "isSearchable": 1, "type": ""}, {"isFilterbale": 0, "name": "timeoriginalestimate", "isSelected": 0, "label": "OriginalEstimate", "isSearchable": 1, "type": "number"}, {"isFilterbale": 0, "name": "description", "isSelected": 0, "label": "Description", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10210", "isSelected": 0, "label": "Singlelinetextfield", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10211", "isSelected": 0, "label": "URLfield", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10212", "isSelected": 0, "label": "UserPickersingleuser", "isSearchable": 1, "type": "user"}, {"isFilterbale": 0, "name": "timetracking", "isSelected": 0, "label": "TimeTracking", "isSearchable": 1, "type": "timetracking"}, {"isFilterbale": 0, "name": "customfield_10203", "isSelected": 0, "label": "DateTimePicker", "isSearchable": 0, "type": "datetime"}, {"isFilterbale": 0, "name": "customfield_10600", "isSelected": 0, "label": "CustomTest", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "customfield_10204", "isSelected": 0, "label": "NumberField", "isSearchable": 1, "type": "number"}, {"isFilterbale": 0, "name": "security", "isSelected": 0, "label": "SecurityLevel", "isSearchable": 1, "type": "securitylevel"}, {"isFilterbale": 0, "name": "customfield_10205", "isSelected": 0, "label": "RadioField", "isSearchable": 1, "type": "option"}, {"isFilterbale": 0, "name": "customfield_10206", "isSelected": 0, "label": "SelectListCascading", "isSearchable": 1, "type": "option-with-child"}, {"isFilterbale": 0, "name": "attachment", "isSelected": 0, "label": "Attachment", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "aggregatetimeestimate", "isSelected": 0, "label": "RemainingEstimate", "isSearchable": 1, "type": "number"}, {"isFilterbale": 0, "name": "customfield_10207", "isSelected": 0, "label": "SelectListMultiplechoice", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "customfield_10208", "isSelected": 0, "label": "Category", "isSearchable": 1, "type": "option"}, {"isFilterbale": 0, "name": "customfield_10209", "isSelected": 0, "label": "Problem", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "summary", "isSelected": 0, "label": "Summary", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "creator", "isSelected": 0, "label": "Creator", "isSearchable": 1, "type": "user"}, {"isFilterbale": 0, "name": "subtasks", "isSelected": 0, "label": "SubTasks", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "reporter", "isSelected": 0, "label": "Reporter", "isSearchable": 1, "type": "user"}, {"isFilterbale": 0, "name": "aggregateprogress", "isSelected": 0, "label": "Progress", "isSearchable": 1, "type": "progress"}, {"isFilterbale": 0, "name": "customfield_10000", "isSelected": 0, "label": "Development", "isSearchable": 1, "type": "any"}, {"isFilterbale": 0, "name": "customfield_10200", "isSelected": 0, "label": "SalesforceCaseNumbers", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "customfield_10201", "isSelected": 0, "label": "IsHelpful", "isSearchable": 1, "type": "array"}, {"isFilterbale": 0, "name": "customfield_10202", "isSelected": 0, "label": "DatePicker", "isSearchable": 0, "type": "datetime"}, {"isFilterbale": 0, "name": "customfield_10400", "isSelected": 0, "label": "<PERSON><PERSON><PERSON><PERSON>", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "environment", "isSelected": 0, "label": "Environment", "isSearchable": 1, "type": "string"}, {"isFilterbale": 0, "name": "duedate", "isSelected": 0, "label": "DueDate", "isSearchable": 0, "type": "datetime"}, {"isFilterbale": 0, "name": "progress", "isSelected": 0, "label": "Progress", "isSearchable": 1, "type": "progress"}, {"isFilterbale": 0, "name": "comment", "isSelected": 0, "label": "Comment", "isSearchable": 1, "type": "comments-page"}, {"isFilterbale": 0, "name": "votes", "isSelected": 0, "label": "Votes", "isSearchable": 1, "type": "votes"}, {"isFilterbale": 0, "name": "worklog", "isSelected": 0, "label": "LogWork", "isSearchable": 1, "type": "array"}], "object": "issue"}, "authentication": {"password": "jira@1234", "indexName": "jira-on-prem", "label": "<PERSON><PERSON><PERSON><PERSON>-Prem", "url": "http://45.79.128.241:8080", "username": "graz<PERSON><PERSON><PERSON>"}}