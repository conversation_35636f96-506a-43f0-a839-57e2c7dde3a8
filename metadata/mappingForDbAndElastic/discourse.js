module.exports = {
    "objects": [{
        "name": "topics",
          "label": "Topics",
          "status": 1,
          "boosting_factor": 1,
          "base_url": "",
          "fields": []
      }],

     "fields": [
                {
                    "name": "url",
                    "label": "URL",
                    "type": "string",
                    "isFilterable": 1,
                    "isSortable": 1,
                    "isSearchable": 1,
                    "isActive": 1
                },
                {
                    "name": "draft_key",
                    "label": "Draft Key",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "id",
                    "label": "Topic Id",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "title",
                    "label": "Topic Title",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "slug",
                    "label": "Topic Slug",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "posts_count",
                    "label": "Posts Count",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "reply_count",
                    "label": "Reply Count",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "post_time",
                    "label": "Created At",
                    "type": "datetime",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 0, //analyzer
                    "isActive": 1,
                    "isMerged":1
                },
                {
                    "name": "last_posted_at",
                    "label": "Last Posted At",
                    "type": "datetime",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 0, //analyzer
                    "isActive": 1
                },
                {
                    "name": "cooked",
                    "label": "Description",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "last_poster_username",
                    "label": "Username",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "category_id",
                    "label": "Category Id",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "views",
                    "label": "Views Count",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "like_count",
                    "label": "Like Count",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "tags",
                    "label": "Tags",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
                {
                    "name": "categories_name",
                    "label": "Category Name ",
                    "type": "string",
                    "isFilterable": 1, //indexing method analyzed OR not analyzed
                    "isSortable": 1,
                    "isSearchable": 1, //analyzer
                    "isActive": 1
                },
            ]
}