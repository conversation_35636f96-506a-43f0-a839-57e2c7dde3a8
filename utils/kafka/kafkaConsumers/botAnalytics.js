var {preProcessFeedPosts} = require("../../../customPlugins/communityHelper/analytics/botAnalyticsLib")
const kafkaLib = require("../kafka-lib");
const commonFunctions = require("../../commonFunctions");

const subscribeConsumer = async () => {
  try{
      const analyticsConsumer = await kafkaLib.getConsumer({
        groupId: `admin ${config.get("kafkaTopic.communityBotAnalytics")}`,
        topic: config.get("kafkaTopic.communityBotAnalytics"),
        fromBeginning: false
    })
  await analyticsConsumer.run({
      eachMessage: async ({
        topic, parition, message, heartBeat
      }) => {
        try {
          console.log('data is', message)
          let allFeeds = message.value ? JSON.parse(message.value) : {};
          commonFunctions.errorlogger.info("all feeds are--" + message.value);
          if(allFeeds && allFeeds.data){
            if (allFeeds && allFeeds.data[0] && allFeeds.data[0].value.feedCommentIDs && allFeeds.data[0].value.feedCommentIDs.length) {
              preProcessFeedPosts(allFeeds.data[0].value.feedCommentIDs);
          }else{
              if(allFeeds.data[0] && allFeeds.data[0].value.feedCommentIDs && !allFeeds.data[0].value.feedCommentIDs.length){}
          }
          }
         
        } catch (e) {
            console.log(e)
        }
      }
    });
  }catch(error){
    if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
      kafkaLib.createTopic(config.get("kafkaTopic.communityBotAnalytics"));
    }
  }
}

module.exports = {
    subscribeConsumer
}