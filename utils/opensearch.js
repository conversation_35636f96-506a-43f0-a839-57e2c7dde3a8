const { Client } = require('@opensearch-project/opensearch');
const { getTenantInfoFromTenantId } = require('auth-middleware');
const esClientMap = new Map();


const getOsClient = async (tenantId) => {
    const client = esClientMap.get(tenantId);
    if (!client) {
        const tenantInfo = await getTenantInfoFromTenantId(tenantId);
        const esClient = new Client({
            nodes: tenantInfo[0].es_cluster_ip.split(','),
            nodeSelector: 'random'
          });
          esClientMap.set(tenantId, esClient);
        return esClient;
    }else{
        return esClientMap.get(tenantId);
    }
};

module.exports = {
  getOsClient
};
