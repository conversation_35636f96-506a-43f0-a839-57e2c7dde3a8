{"PORT": 6009, "base": "/frontend", "adminURL": "https://d2bc-112-196-28-106.ngrok-free.app/backend", "databaseSettings": {"host": "mysql", "user": "root", "password": "graz<PERSON><PERSON>@271", "database": "m20", "mysqlPORT": "3306"}, "elasticIndexCS": {"port": 8045, "transportLayerPort": 9301, "host": "elastic-5.6.16", "encryptionKey": "Grazitti$271##", "md5salt": "@##@Lkvk@8a$*5c9#JOKxZwf64d@@", "oauthLogsIndex": "analytics_oauth_logs"}, "crawler": {"crawlerUrl": "http://crawler:6500"}, "redis": {"redisHost": "redis", "redisPort": "6379"}, "analyticsService": {"url": "http://analytics:3000"}, "searchService": {"url": "http://search:7000"}, "searchClient": {"url": "http://search-client:6008"}, "synonyms": {"HOST": "synonym", "PORT": "3500"}, "jive": {"communityUrl": "https://marketo-ext.jiveon.com"}, "lithium": {"accessTokenUrl": "https://api.lithium.com"}, "nutch": {"path": "/opt/nutch-1.15/"}, "siteMapReplaceUrlContent": ["/help-center/home/<USER>/public_html/help-center", "https://help-center.vlocity.com"], "searchAnalytics": {"supportUrl": ".*/[nN]ew[cC]ase", "detailUrl": ".*/s/publiccasedetail.*", "SUfieldonCase": "SU_Ltng__SearchUnifySessionId__c"}, "tikaPath": "http://localhost:9998/tika", "predictionIO": {"eventServerHost": "localhost:7070", "engineServerHost": "localhost:8000", "accessKey": "MFsv5XjuIYMWVClpeb9g6jkaD5FdTSRbc0VFE0gUqVVKoUPa9_FmMEqfD6v9xT9_"}, "discussionUrl": "*/feed/*", "contentSourcesAndSearchClients": [], "github": {"communityUrl": "https://api.github.com"}, "authentication": [], "statusPage": [], "searchEndpoints": [], "trackingApi": [], "tuning": [], "emailNotifications": [], "profiles": [], "ticketsApi": [], "authContentSource": [], "analyticsOld": [], "analyticsNew": [], "redisCachingTime": 900, "checkForLithiumAttachment": 1, "redis_enable": true, "index_enable": true, "sso_enable": false, "aggregationLogic": false, "checkDraft": false, "solr": {"connection": "local"}, "poc_name": "micro", "onPremises": false, "onPremisesUrl": "http://localhost:3001", "networkId": "0DB0U00000000EmWAI", "communityId": "0000bdettebhsu", "chatBotUrl": "http://chatbot:3100/api", "obfuscation": false, "JWT_SECRET_KEY": "hello", "whiteListOnPremDomain": [], "mergeArticle": false, "linkExpiration": 1, "setSearchRedis": 1, "restrictUserAccount": ["grazitti.com", "searchunify.com", "apiUser.com"], "searchApiEnds": [], "debuglevel": 1, "addUserLimit": 10, "allowMFA": true, "isolateSynonymStopwords": true, "aws": {"s3": {"accessId": "********************", "secret": "tDETFNkh/zlZcY46RIM7ZsBp6adutuq4p8SmanfQ", "isS3Supported": true, "bucketName": "feature1su", "s3BucketURL": "https://feature1su.s3.us-west-2.amazonaws.com", "cloudfrontURL": "https://dpkaqo9nfjwty.cloudfront.net", "searchClientPrefix": "search-clients", "assetLibPrefix": "asset-lib", "region": "ap-south-1", "distributionId": "E2M5BHRJBBYNVU"}}}