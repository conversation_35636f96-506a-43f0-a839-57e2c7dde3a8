<html>

<head>
    <style>
        @media (min-width: 900px) and (max-width: 2100px) {
            .filter-container {
                width: 27%;
                margin: 0 auto;
                border: 1px solid #e8e5e5;
                border-radius: 4px;
                padding: 20px;
                position: relative;
                top: 15%;
            }
        }

        @media (min-width: 200px) and (max-width: 550px) {
            .filter-container {
                width: 85%;
                margin: 0 auto;
                border: 1px solid #e8e5e5;
                border-radius: 4px;
                padding: 20px;
                position: relative;
                top: 15%;
            }
        }

        @media (min-width: 550px) and (max-width: 900px) {
            .filter-container {
                width: 55%;
                margin: 0 auto;
                border: 1px solid #e8e5e5;
                border-radius: 4px;
                padding: 20px;
                position: relative;
                top: 15%;
            }
        }

        .button-allow {
            color: #fff;
            background-color: #337ab7;
            border-color: #2e6da4;
            display: inline-block;
            padding: .4rem 3rem;
            font-size: 15px;
            text-align: center;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .searchunilogo {
            top: 54px;
            position: relative;
            text-align: center;
        }

        .container{
            width: 100%;
        }

        .searchUnifyNewLogo {
            width: 10rem;
        }

        li {
            text-align: left;
            font-size: 15px;
        }

        .access {
            margin: 10px 0;
        }

    </style>
    <script>

        function allow() {
            window.location.href = <%-JSON.stringify(base_url) %> +'/authorise_success?' + window.location.href.split('?')[1];
        }

        function close_window(){
            window.open('','_parent',''); 
            window.close();  
        }
        
    </script>
</head>

<body>
    <header class="searchunilogo">
        <img class="searchUnifyNewLogo" src="resources/Assets/su-logo-black.png">
    </header>
    <div class="filter-container">
        <h4 class="container"> <%=model.name%> wants to: </h4>
        <div class="access">
            <ul>
                <li>
                    Access your SearchUnify data
                </li>
                <li>
                    Access your basic information
                </li>
                <li>
                    Access and manage your data
                </li>
            </ul>
        </div>
        <div class="access container"> Do you want to allow access for <%=username%> </div>
        <div class="container">
            <button class="button-allow" onclick="close_window();">Deny</button>
            <button class="button-allow" onclick="allow();" style="margin-left: 10px;">Allow</button>            
        </div>
    </div>
</body>

</html>