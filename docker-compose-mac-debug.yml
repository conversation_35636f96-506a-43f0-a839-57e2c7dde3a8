# docker compose for admin panel service
version: "3.0"

services:
  backend:
    container_name: backend
    build:
      context: .
      dockerfile: mac.Dockerfile
      args:
        - BASE_IMAGE=${MUL_ARCH_BASE_IMAGE}
    volumes:
      - ".:/home/<USER>"
      - "/home/<USER>/node_modules"
      - "admin_generic:/home/<USER>/resources/search_clients_custom"
      - "synonyms:/home/<USER>/synonyms"
      - "admin_crontab:/var/spool/cron/crontabs/"
      - "asset_library:/home/<USER>/resources/Asset-Library"
      - "analytics_reports:/home/<USER>/reports"
    networks:
      - shared_network
    deploy:
      resources:
        limits:
          memory: ${MEMORY}
      
    command: >
      sh -c "crond -b -l 8
      && npm run debug"
    ports:
      - ${APP_PORT}:${APP_PORT}
      - ${DEBUG_PORT}:${DEBUG_PORT}
      
volumes:
  admin_generic:
  synonyms:
  admin_crontab:
  asset_library:
  analytics_reports:

networks:
  shared_network:
    external:
      name: shared_network
