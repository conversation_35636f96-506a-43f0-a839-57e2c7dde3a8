module.exports = {
    objects: [
        {
            name: "fd_ticket",
            label: "Ticket",
            status: 1,
            boosting_factor: 1,
            base_url: "",
            fields: [
                {
                    name: "id",
                    label: "Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "type",
                    label: "Type",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "subject",
                    label: "Subject",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "description",
                    label: "Description",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "cc_emails",
                    label: "Cc Emails",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "spam",
                    label: "Spam",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "group_id",
                    label: "Group Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "priority",
                    label: "Priority",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "requester_id",
                    label: "Requester Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "responder_id",
                    label: "Responder Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "source",
                    label: "Source",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "company_id",
                    label: "Company Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "status",
                    label: "Status",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "support_email",
                    label: "Support Email",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "product_id",
                    label: "Product Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },

                {
                    name: "due_by",
                    label: "Due By",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "is_escalated",
                    label: "Is Escalated",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "created_at",
                    label: "Created At",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "updated_at",
                    label: "Updated At",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "tags",
                    label: "Tags",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "attachment_multiple",
                    label: "Attachments",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "conversations",
                    label: "Conversations",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "sentiment_score",
                    label: "Sentiment Score",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: 'view_href',
                    label: 'View Href',
                    type: 'string',
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1
                },
                {
                    name: 'permissions',
                    label: 'Permissions',
                    type: 'string',
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                    isMerged: 1
                }
            ],
        },
        {
            name: "fd_article",
            label: "Article",
            status: 1,
            boosting_factor: 1,
            base_url: "",
            fields: [
                {
                    name: "id",
                    label: "Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "type",
                    label: "Type",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "description",
                    label: "Description",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "status",
                    label: "Status",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "agent_id",
                    label: "Agent Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "category_nested",
                    label: "Category",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "folder_id",
                    label: "Folder Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "title",
                    label: "Title",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "created_at",
                    label: "Created At",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "updated_at",
                    label: "Updated At",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "tags",
                    label: "Tags",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "attachment_multiple",
                    label: "Attachments",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "thumbs_up",
                    label: "Thumbs Up",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "thumbs_down",
                    label: "Thumbs Down",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "hits",
                    label: "Hits",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "suggested",
                    label: "Suggested",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "feedback_count",
                    label: "Feedback Count",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: 'view_href',
                    label: 'View Href',
                    type: 'string',
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1
                },
                {
                    name: 'visibility',
                    label: 'Visibility',
                    type: 'string',
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                    isMerged: 1
                },
                {
                    name: 'company_id',
                    label: 'Company Id',
                    type: 'string',
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                    isMerged: 1
                }
            ],
        },    
        {
            name: "fd_topic",
            label: "Topic",
            status: 1,
            boosting_factor: 1,
            base_url: "",
            fields: [
                {
                    name: "id",
                    label: "Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "title",
                    label: "Title",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "forum_id",
                    label: "Forum Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "user_id",
                    label: "User Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "locked",
                    label: "Locked",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "published",
                    label: "Published",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "stamp_type",
                    label: "Stamp Type",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "replied_by",
                    label: "Replied By",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "user_votes",
                    label: "User Votes",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "merged_topic_id",
                    label: "Merged Topic Id",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "comments_count",
                    label: "Comments Count",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "comments",
                    label: "Comments",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "sticky",
                    label: "Sticky",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "created_at",
                    label: "Created At",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "updated_at",
                    label: "Updated At",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "replied_at",
                    label: "Replied At",
                    type: "datetime",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: "hits",
                    label: "Hits",
                    type: "string",
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                },
                {
                    name: 'view_href',
                    label: 'View Href',
                    type: 'string',
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1
                },
                {
                    name: 'visibility',
                    label: 'Visibility',
                    type: 'string',
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                    isMerged: 1
                },
                {
                    name: 'company_id',
                    label: 'Company Id',
                    type: 'string',
                    isFilterable: 1,
                    isSortable: 1,
                    isSearchable: 1,
                    isActive: 1,
                    isMerged: 1
                }
            ],
        }
    ],
};
