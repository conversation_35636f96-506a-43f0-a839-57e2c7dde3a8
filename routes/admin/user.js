/**
 * Created by man<PERSON><PERSON> on 5/4/17.
 */

var constants = require('../../constants/constants');
var md5 = require('md5');
var express = require('express');
var router = express.Router();
var async = require('async');
var redis = require("redis");
var client = redis.createClient(config.get("redis.redisPort"), config.get("redis.redisHost"));
var samlConfiguration = require('../saml/samlController')
var commonFunctions = require('../../utils/commonFunctions');
var CryptoJS = require("crypto-js");
const {Parser} = require("json2csv");
const request = require('request')
const userKafkaConfigurations = require('./userKafkaConfig');
var statusPageAuth = require('./statusPageAuth');
var jwt = require('jsonwebtoken');
const crypto = require('crypto');
const timeZoneList = require("../../utils/timeZoneList");
const { fetchClientInfo, fetchApiLimitInfo } = require('../../utils/commonFunctions');
const { getAccessTokenFromTenantId } = require('auth-middleware');
const forge = require('node-forge');
const passport = require('passport');
var SamlStrategy = require('passport-saml').Strategy;
const { IDP_TYPES } = require('./../../constants/constants');

const urlAuth  = config.get('authUrl');

const fs = require('fs');
const multipart = require('connect-multiparty');
const multipartMiddleware = multipart();
const { osMigration, osMigrationDays } = config.get('openSearch');
config = require('config');

function encode(password, cryptoStr) {
    var textBuffer = new Buffer(JSON.stringify(cryptoStr), 'utf-8');
    var iv = crypto.randomBytes(16);
    var cipher = crypto.createCipheriv('aes-256-cbc', password, iv);
    var encryptedBuffer = cipher.update(textBuffer);
    let encrypted = Buffer.concat([iv, encryptedBuffer, cipher.final()]).toString('base64');
    return encrypted;
}

// Moved to auth
// router.get('/getAllUsers', function (req, res) {
//   // var sql = "select u.id id,r.id roleId,u.user_email email,u.name name,r.role role,u.is_active isActive,u.is_federated is_federated,u.scope scope, u.activation_date activation_date, u.invite_count invite_count from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id where FIND_IN_SET(right(u.user_email,11),'apiUser.com')=0 and FIND_IN_SET(right(u.user_email,12),'grazitti.com')=0 and FIND_IN_SET(right(u.user_email,15),'searchunify.com')=0"
//   let restrict = config.get('restrictUserAccount');
//   var sql = "select u.id id,r.id roleId,u.user_email email,u.name name,r.role role,u.is_active isActive,u.is_federated is_federated,u.scope scope, u.activation_date activation_date, u.invite_count invite_count, u.selectedTabs selectedTabs ";
//   sql += req.headers.session.email.split('@')[1] == 'grazitti.com' || req.headers.session.email.split('@')[1] == 'searchunify.com' ? ", u.is_blocked is_blocked" : "";
//   sql += " from user u join user_roles ur on u.id=ur.userId join roles r on ur.roleId=r.id ";
//   if (restrict.length) {
//     sql += ' where';
//     for (var i = 0; i < restrict.length; i++) {
//       sql += ` FIND_IN_SET(right(u.user_email, ${restrict[i].length}),'${restrict[i]}')=0`;
//       sql += i < restrict.length - 1 ? ' and' : '';
//     }
//   }
//   connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
//     if (!err) {
//       res.send(rows);
//     }
//   })
// });

router.get('/checkUser', function (req, res) {
  if (!req.query.email || !req.query.passwordHash) {
    return res.status(400).send({ flag: 0, message: "Missing Parameters" });
  }
  var sql = "select * from user where user_email='" + req.query.email + "' and access_token='" + req.query.passwordHash + "'";
  connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
    if (!err) {
      if (rows.length == 0) {
        res.status(400).send({ flag: 0, message: "Invalid Credentials" });
      } else {
        res.send({ flag: 1, message: "Valid Credentials" });
      }
    } else {
      commonFunctions.errorlogger.error("Error while fetching user ", err);
      res.status(400).send({ flag: 0, message: "Db Read Failed" });
    }
  });

});

// Moved to auth service
// router.post('/editUser', async (req, res) => {
//   let user = req.body.user;
//   let tabsChanged = req.body.tabsChanged;
//   let allowFurther = await accessCheck(req.headers.session.email, req.headers.session.roleId, 'manageUser', user.email,req)
//   if (!allowFurther) { res.send({ flag: 401, message: 'Unauthorized' }) }
//   else {
//     async.auto({
//       user: cb => {
//         var sql = "update user set is_active=?, is_federated=?, scope=?, selectedTabs=? where id=?";
//         connection[req.headers['tenant-id']].execute.query(sql, [user.isActive, user.is_federated || null, user.scope || null, JSON.stringify(user.selectedTabs) || null, user.id], function (err, rows) {
//           cb(null, rows)
//         })
//       },
//       userRoles: cb => {
//         var sql = "update user_roles set roleId=" + user.roleId + " where userId=" + user.id;
//         connection[req.headers['tenant-id']].execute.query(sql, function (err, rows) {
//           cb(null, rows)
//         })
//       },
//       deleteSession: cb => {
//         if (tabsChanged) {
//           var sql = "delete from sessions where user_id = ?";
//           connection[req.headers['tenant-id']].execute.query(sql, [user.email], function (err, rows) {
//             cb(null, rows)
//           })
//         }
//         else
//           cb(null, [])
//       },
//       userPublish: ['user', 'userRoles', (dataFromAbove, cb) => {
//         if (config.get("kafkaTopic.enable") && user) {
//           userKafkaConfigurations.publishUsersViaKafka(user.email,req, function (err, searchConfig) {
//             cb(null, [])
//           })
//         }
//       }]
//     }, (error, result) => {
//       if (req.headers.session.email == user.email && tabsChanged) {
//         req.logout();
//         req.headers.session.destroy(err => {
//           commonFunctions.errorlogger.error("error", err);
//           res.end();
//         });
//       }
//       else {
//         res.send(result);
//       }
//     })
//   }
// })

router.post('/manageAccount', function (req, res) {
  var user = req.body.user;
  var sql = "update user set name=? where user_email=?";
  var q = connection[req.headers['tenant-id']].execute.query(sql, [user.name, user.email], function (err, rows) {
    if (!err) {
      if (config.get("kafkaTopic.enable") && user) {
        userKafkaConfigurations.publishUsersViaKafka(user.email,req,function (err, searchConfig) {
          var response = {};
          response.status = 200;
          response.message = "OK"
          res.send(response);
        })
      }
    }
  })
})

router.post('/changePassword', function (req, res) {
  var email = req.body.credentials.email;
  var oldPassword = req.body.credentials.oldPassword;
  var newPassword = req.body.credentials.newPassword
  var tokenOld = md5(email + oldPassword)
  var tokenNew = md5(email + newPassword)
  var response = {};
  var re = new RegExp("^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})");
  if (!re.test(newPassword)) {
    response.status = 401;
    response.message = "Password must have a lowercase, uppercase letter, number, special character and must have 8 characters!!"
    res.send(response);
  } else {
    var sql = "update user set access_token=? where access_token=?";
    connection[req.headers['tenant-id']].execute.query(sql, [tokenNew, tokenOld], function (err, rows) {
      if (!err) {
        if (rows.affectedRows == 0) {
          response.status = 401;
          response.message = "Old password is incorrect."
          res.send(response);
        } else {
          if (config.get("kafkaTopic.enable")) {
            userKafkaConfigurations.publishUsersViaKafka(email,req, function (err, searchConfig) {
              response.status = 200;
              response.message = "Password Changed"
              res.send(response);
            })
          }
        }
      } else {
        commonFunctions.errorlogger.error(err)
      }
    })
  }
})

router.post('/updateQuestion', function (req, res) {
  var email = req.body.credentials.email;
  var password = req.body.credentials.password;
  var ques = req.body.credentials.ques;
  var ans = req.body.credentials.ans;
  var token = password ? md5(email + password) : password;
  var ques_ans = md5(ques + ans);
  var sql = "update user set ques_ans=? where access_token=?";
  connection[req.headers['tenant-id']].execute.query(sql, [ques_ans, token], function (err, rows) {
    if (!err) {
      var response = {};
      if (rows.affectedRows == 0) {
        response.status = 401;
        response.message = "Password is incorrect."
        res.send(response);
      } else {
        response.status = 200;
        response.message = "Security Question Updated."
        res.send(response);
      }
    } else {
      commonFunctions.errorlogger.error(err);
      response.status = 400;
      response.message = "Cannot update security question."
      res.send(response);
    }
  })
})

router.post('/uploadCertificate', multipartMiddleware,  function (req, res) {
  try{
    fs.readFile(req.files.uploadFile.path, "ascii", function (err, data) {
      if(data) {
        let cert = '';
        try {
          cert = forge.pki.certificateFromPem(data);
        } catch(error) {
          res.send({ "certificate": '', "status": 104 });
        }
        if(cert && cert.verify(cert)) res.send({ "certificate": data, "status": 200 })
      } else res.send({ "certificate": '', "status": 400 });
    });
  } catch(error){
    res.send({ "certificate": '', "status": 104 });
  }
 })

 router.post('/verifyCertificate', function (req, res) {
  try {
    if(req.body.certificate) {
      const cert = forge.pki.certificateFromPem(req.body.certificate);
      if(cert && cert.verify(cert)) res.send({ "status": 200 });
    } else res.send({ "status": 400 });
  } catch(error) {
    res.send({ "status": 104 });
  }
})

router.get('/getIdpSettings', async (req, res, next) => {
  samlConfiguration.getSamlConfiguration(req, async (error, result) => {
    if(result && result.hostedConfiguration){
    const data = await getAccessTokenFromTenantId(req.headers['tenant-id']);
        if (data.accessToken) {
          result["hostedConfiguration"].accessToken = data.accessToken;
        }
        res.send(result);
    }
  });
})

router.post('/updateIdpSettings', function (req, res) {
  samlConfiguration.updateSamlConfiguration(req.body.sso,req, (error, result) => {
    res.send(result);
  });
})

router.post('/updateSSOCertificate', function (req, res) {
  samlConfiguration.updateSSOCertificate(req.body.sso,req, (error, result) => {
    res.send(result);
  });
})


router.get('/checkSsoEnable', (req, res) => {
  let sql = `select isActivated from saml_auth where tenant_id = '${req.headers['tenant-id']}' and idp_type=0`;
  connection[req.headers['tenant-id']].execute.query(sql, (err, rows) => {
    if(rows && rows.length && rows[0].isActivated)
      res.send({ login: 'getSSOLogin' });
    else
      res.send({ login: 'getUserLogin' });  
  });   
})

router.get('/getLastLogin', (req, res, next) => {
  let sql = `select last_login from user where 	user_email = '${req.headers.session.email}'`;
  connection[req.headers['tenant-id']].execute.query(sql, (err, rows) => {
    res.send(rows);
  });
})

router.get('/getSearchUnifySession', (req, res, next) => {
  var options = {
    "method": "GET",
    "url": `${urlAuth}/verification/getSearchUnifySession`,
    "headers": { 'content-type': 'application/json', 'Cookie': req.headers.cookie, 'CSRF-Token' : req.headers['csrf-token'] }
  };
  request(options, function (err, response, body) {
      if (err) {
          console.log("ERROR :: " + err);
      } else {
          try{
            const data = JSON.parse(body);
            res.send(data);
          }catch(error){
            cb({statusCode : 400, message: 'Bad Request'}, null);
          }
      }
  })

})

router.get('/getChatbotConfig', (req, res, next) => {
  res.send({ url: config.get('chatBotConfig.url'), uid: config.get('chatBotConfig.uid') });
})

// Moved to auth
// router.get('/getSignupSettings', (req, res) => {
//   getSignupSettings(req, (result) => {
//       res.send(result);
//   });
// })

const getSignupSettings = (req, cb) => {
  if (config.hasOwnProperty('linkExpiration') == true) {
    hourParam = config.get('linkExpiration');
  } else {
    hourParam = 1;
  }
  let type = 1; //1 - register, 2 - forgot
  let hour = 1000 * 60 * 60 * hourParam;
  let hourAgo = Date.now() - hour;
  async.auto({
    getDecodedData: cb => {
      commonFunctions.registerEmailJwt({ token: req.query.token }, (err, result) => {
        if (result.email)
          cb(null, result);
        else cb('No email found');
      });
    },
    checkForRegister: ["getDecodedData", (results, cb) => {
      var sql = connection[req.headers['tenant-id']].execute.query("Select access_token, ques_ans from user where user_email=?", results.getDecodedData.email, (error, result) => {
        cb(null, result[0])
      })
    }],
    process: ["checkForRegister", (results, cb) => {
      if (!results.checkForRegister) {
        cb(null, false);
      }
      else if (results.checkForRegister.access_token) {
        type = 2;
        client.get(`node-forget-${results.getDecodedData.email}`, (error, response) => {
          if (response) {
            response = JSON.parse(response);
            console.log('start Time', new Date(response.startTime));
            console.log('Hourago', new Date(hourAgo));
            if (response && (response.email.indexOf(results.getDecodedData.email) > -1) && (response.startTime < hourAgo))
              cb(null, false);
            else
              cb(null, true)
          }
          else
            cb(null, true);
        })
      } else {
        client.get(`node-register-${results.getDecodedData.email}`, (error, response) => {
          if (response) {
            response = JSON.parse(response);
            console.log('start Time', new Date(response.startTime));
            console.log('Hourago', new Date(hourAgo));

            if (response && (response.email.indexOf(results.getDecodedData.email) > -1) && (response.startTime < hourAgo))
              cb(null, false);
            else
              cb(null, true);
          } else
            cb(null, true);
        })
      }
    }]
  }, (error, result) => {
    if (result && result.process) {
      cb({ linkActive: true, email: result.getDecodedData.email, type, show: result.checkForRegister && result.checkForRegister.ques_ans ? true : false }); // link not expired
    } else {
      cb({ linkActive: false, email: result.getDecodedData.email, type, show: result.checkForRegister && result.checkForRegister.ques_ans ? true : false }); //link expired
    }
  })
}

router.get('/updateUserReports', (req, res, next) => {
  if (req.headers.session.email == '<EMAIL>' || req.headers.session.email == "<EMAIL>") {
    let sql = `UPDATE sessions SET user_id = ? WHERE session_id = ? `;
    connection[req.headers['tenant-id']].execute.query(sql, [req.headers.session.email, req.headers.session.id], function (err, rows) {
      res.send({ flag: 200 });
    });
  } else {
    async.auto({
      deleteRows: cb => {
        let sql = 'DELETE FROM sessions WHERE user_id = ? AND session_id != ?';
        let q = connection[req.headers['tenant-id']].execute.query(sql, [req.headers.session.email, req.headers.session.id],
          (error, rows) => {
            cb(error, []);
          })
      },
      updateRow: ["deleteRows", (result, cb) => {
        let sql = `UPDATE sessions SET user_id = ? WHERE session_id = ? `;
        connection[req.headers['tenant-id']].execute.query(sql, [req.headers.session.email, req.headers.session.id], function (err, rows) {
          cb(err, []);
        });
      }],
      deleteExtra: ["updateRow", (result, cb) => {
        let sql = 'DELETE FROM sessions WHERE user_id IS NULL';
        let q = connection[req.headers['tenant-id']].execute.query(sql, (error, rows) => {
          cb(error, []);
        })
      }],
    }, (error, result) => {
      res.send({ flag: 200 });
    });
  }
})

// Move to auth service
// router.post('/deleteRegisteredUser', async (req, res) => {
//   let allowFurther = await accessCheck(req.headers.session.email, req.headers.session.roleId, 'manageUser', req.body.email,req)
//   if (!allowFurther) { res.send({ flag: 401, message: 'Unauthorized' }) }
//   else {
//     let sql = "DELETE u, ur FROM user u JOIN user_roles ur ON ur.userId = u.id WHERE u.user_email = ?";
//     connection[req.headers['tenant-id']].execute.query(sql, [req.body.email], function (err, rows) {
//       console.log(err, rows);
//       if (err)
//         res.send({ flag: 400, message: "Cannot delete user" });
//       else {
//         if (config.get("kafkaTopic.enable")) {
//           userKafkaConfigurations.deleteUsersViaKafka(req.body.email, function (err, searchConfig) {
//             res.send({ flag: 200, message: "User deleted Successfully!" })
//           })
//         }
//       }
//     });
//   }
// });

router.get('/getUserLimit', function (req, res) {
  fetchClientInfo(req.headers['tenant-id'], (err,data)=>{
    let adminUserLimit = config.get("addUserLimit");
    if(err){
      res.send({ "userLimit": adminUserLimit});
    }else{
      if(data && data.usages && data.usages.adminLicenseLimit >= 0){
        adminUserLimit = data.usages.adminLicenseLimit
      }
      res.send({ "userLimit": adminUserLimit });
    }
});
});

let contentFields = [0,1,2];
const parser = new Parser({
  header:false,
  fields : [
      ...contentFields.map(f =>{
          return{
              value:row => row[f]
          }
      })
  ]
});


router.post("/getApiUsageDetails", (req, res) => {
  let month = req.body.month;
  let year = req.body.year;
  let jsonData = [];

  let query = "SELECT * FROM accountDetails WHERE month=? AND year=?";
  connection[req.headers['tenant-id']].execute.query(query, [month, year], (err, data) => {
      if (data.length) {
          let accountData = data[0];
          let jsonTocsv = [{
              0: "Resources",
              1: "Consumed",
              2: "Limit"
          }, {
              0: "Admin Panel Licenses",
              1: accountData.licenseConsumed >= 0 ? accountData.licenseConsumed :  '-',
              2: accountData.licenseLimit > 0 ? accountData.licenseLimit : '-'
          }, {
              0: "No of Content Sources",
              1: accountData.contentSourceConsumed >= 0 ? accountData.contentSourceConsumed :  '-',
              2: accountData.contentSourceLimit > 0 ? accountData.contentSourceLimit : '-'
          }, {
              0: "Index Size",
              1: accountData.indexSizeConsumed >= 0 ? accountData.indexSizeConsumed + " GB" : '-',
              2: accountData.indexSizeLimit > 0 ? accountData.indexSizeLimit + " GB" : '-'
          }, {
              0: "Number of documents",
              1: accountData.documentsConsumed >= 0 ? accountData.documentsConsumed : '-',
              2: accountData.documentsLimit > 0 ? accountData.documentsLimit : '-'
          }, {
              0: "No of Search Clients",
              1: accountData.searchClientsConsumed >= 0 ? accountData.searchClientsConsumed :  '-',
              2: accountData.searchClientsLimit > 0 ? accountData.searchClientsLimit : '-'
          }, {
              0: "Searches Performed in this month",
              1: accountData.searchesConsumed >= 0  ? accountData.searchesConsumed : '-',
              2: accountData.searchesLimit > 0 ? accountData.searchesLimit :  '-'
          }, {
              0: "",
              1: "",
              2: ""
          }, {
              0: "API",
              1: "Consumed",
              2: "Limit"
          }, {
              0: "Search",
              1: accountData.searchApiConsumed >= 0 ? accountData.searchApiConsumed : '-',
              2: accountData.searchApiLimit > 0 ? accountData.searchApiLimit :  '-'
          }, {
              0: "Analytics",
              1: accountData.analyticsApiConsumed  >= 0 ? accountData.analyticsApiConsumed : '-',
              2: accountData.analyticsApiLimit > 0 ? accountData.analyticsApiLimit : '-'
          }, {
              0: "Content",
              1: accountData.contentApiConsumed >= 0 ?  accountData.contentApiConsumed : '-',
              2: accountData.contentApiLimit > 0 ? accountData.contentApiLimit : '-'
          }]
          jsonData.push(...jsonTocsv);
      }else{
        jsonData.push({
          0:"No Data for the month : " + month,
        })
      }
      const result = parser.parse(jsonData);
      res.attachment('accountDetails.csv');
      res.status(200).send(result);
  })
})

// "https://macmillan.searchunify.com"
router.get("/getAccountDetail",(req,res)=>{
  fetchClientInfo(req.headers['tenant-id'],(err,data)=>{
    if(err){
      res.status(400).send({ "message": 'Unable to retrieve information'});
    } else{
      let objToSend = {};
      if(data) {
        objToSend = {
          productionUrl: data.instances.production,
          serviceStatusPageUrl: data.instances.statusPage,
          startDate: data.subscriptionStartDate,
          subscriptionEndDate: data.subscriptionEndDate,
          releaseVersion: data.releaseVersion,
          licenseType: data.licenseType,
          accountName: data.name,
          accountStatus: data.status,
          annualSupportPackage: data.annualSupportPackage,
          dataCenterLocation: data.dataCenter,
          indexSizeLimit: data.usages.indexSizeLimit,
          contentSourceLimit: data.usages.contentSourceLimit,
          documentCountLimit: data.usages.documentsLimit,
          searchClientsLimit: data.usages.searchClientsLimit,
          adminPanelLicenseLimit: data.usages.adminLicenseLimit,
          searchesLimit: data.usages.searchesLimit,
          searchApiLimit: {
            daily: parseInt(data.usages.searchApiLimit.hourly)* 24,
            monthly: data.usages.searchApiLimit.monthly
          },
          contentApiLimit: {
            daily: parseInt(data.usages.contentApiLimit.hourly)* 24,
            monthly: data.usages.contentApiLimit.monthly
          },
          analyticsApiLimit: {
            daily: parseInt(data.usages.analyticsApiLimit.hourly)* 24,
            monthly: data.usages.analyticsApiLimit.monthly
          },
          gptApiLimit:{
            daily: parseInt(data.usages.gptApiLimit.hourly)* 24,
            monthly: data.usages.gptApiLimit.monthly
          },
          g2Review:data.g2Review || false,
          llmUsages: data.llmUsages
        }
      }
      if(req.headers && req.headers.session && (req.headers.session.email.includes('@searchunify.com') || req.headers.session.email.includes('@grazitti.com'))){
        objToSend.tenantId = req.headers && req.headers['tenant-id'] || 'No tenant found';
        objToSend.databaseName = req.headers && req.headers['databaseName'] || 'No db found';
      }
      res.send(objToSend);
    }
  });

})

router.get("/getApiLimitDetail",(req,res)=>{
  fetchApiLimitInfo(req.headers['tenant-id'],(err,data)=>{
    if(err){
      res.status(400).send({ "message": 'Unable to retrieve information'});
    }else{
      let objToSend = {};
      if (data && data.usages) {
          if (data.usages.analyticsApiLimit) {
              objToSend['analyticsApiLimit'] = {
                  minute: data.usages.analyticsApiLimit.minute,
                  hourly: data.usages.analyticsApiLimit.hourly,
                  daily: parseInt(data.usages.analyticsApiLimit.hourly) * 24,
                  monthly: data.usages.analyticsApiLimit.monthly,
              };
            }

              if (data.usages.searchApiLimit) {
                  objToSend['searchApiLimit'] = {
                      minute: data.usages.searchApiLimit.minute,
                      hourly: data.usages.searchApiLimit.hourly,
                      daily: parseInt(data.usages.searchApiLimit.hourly) * 24,
                      monthly: data.usages.searchApiLimit.monthly,
                };
              }

              if (data.usages.contentApiLimit) {
                  objToSend['contentApiLimit'] = {
                      minute: data.usages.contentApiLimit.minute,
                      hourly: data.usages.contentApiLimit.hourly,
                      daily: parseInt(data.usages.contentApiLimit.hourly) * 24,
                      monthly: data.usages.contentApiLimit.monthly,
                };
              }
              if (data.usages.gptApiLimit) {
                objToSend['gptApiLimit'] = {
                    minute: data.usages.gptApiLimit.minute,
                    hourly: data.usages.gptApiLimit.hourly,
                    daily: parseInt(data.usages.gptApiLimit.hourly) * 24,
                    monthly: data.usages.gptApiLimit.monthly,
              };
            }
          res.send(objToSend);
      }
    }
  });

})
/** Update Acces control setting   */
router.post('/saveAccessControlSettingData', async (req, res) => {
  let allowFurther = await accessCheck(req.headers.session.email, req.headers.session.roleId, 'accessControlTab',req)
  if (!allowFurther) { res.send({ flag: 401, message: 'Unauthorized' }) }
  else {
    var accessControl = req.body.data;
    var sql = `UPDATE access_control_settings SET contentSource = ${accessControl.contentSource}, searchClient = ${accessControl.searchClient} Where id ='1';`;
    var q = connection[req.headers['tenant-id']].execute.query(sql, [accessControl], (err, rows) => {
      if (err) {
        console.log(`Error ${err}`)
      } else {
        res.send({ flag: 200, message: "settings saved" })
      }
    })
  }
});

/** Get Access control settings */
router.get('/getAccessControlSettingData', async (req, res) => {
  let allowFurther = await accessCheck(req.headers.session.email, req.headers.session.roleId, 'accessControlTab',req)
  if (!allowFurther) { res.send({ flag: 401, message: 'Unauthorized' }) }
  else {
    var sql = `SELECT contentSource, searchClient from access_control_settings where id = '1';`;
    connection[req.headers['tenant-id']].execute.query(sql, (err, rows) => {
      if (err) {
        console.log(`Error while getting data from access control table ${err}`)
      } else {
        res.send({ flag: 200, data: rows[0] })
      }
    })
  }
});

// Moved to auth
// router.post('/usersConfigurations', async (req, res) => {
//   let data = req.body.paramters
//   if (data.type === 'set') {
//     if(data.ai){
//     var sql = `INSERT INTO usersConfigurations(userId, email,ActionableInsightsPreSelectedSC) VALUES 
//     (${req.headers.session.userId},'${req.headers.session.email}','${data.AIPreselected}') ON DUPLICATE KEY UPDATE ActionableInsightsPreSelectedSC = '${data.AIPreselected}'`;
//     } else if(data.cd){
//       var sql = `INSERT INTO usersConfigurations(userId, email,CaseDeflectionPreSelectedSC) VALUES 
//       (${req.headers.session.userId},'${req.headers.session.email}','${data.CDPreselected}') ON DUPLICATE KEY UPDATE CaseDeflectionPreSelectedSC = '${data.CDPreselected}'`;
//     }else if(data.lightMode){
//       var sql = `INSERT INTO usersConfigurations(userId, email,lightMode) VALUES 
//       (${req.headers.session.userId},'${req.headers.session.email}','${data.currentTheme}') ON DUPLICATE KEY UPDATE lightMode = '${data.currentTheme}'`;
//     }else if(data.sideBar){
//       var sql = `INSERT INTO usersConfigurations(userId, email,sideBarActive) VALUES 
//       (${req.headers.session.userId},'${req.headers.session.email}',${data.sideBarActive}) ON DUPLICATE KEY UPDATE sideBarActive = ${data.sideBarActive}`;
//     }else if (data.timezone) {
//       var sql = `UPDATE user SET timezone = '${data.timezone}' WHERE id = '${req.headers.session.userId}'`;
//     }else if(data.analytics){
//         var sql = `INSERT INTO usersConfigurations(userId, email,preselectedPinSCAnalytics) VALUES 
//       (${req.headers.session.userId},'${req.headers.session.email}','${data.preselectedPinSCAnalytics}') ON DUPLICATE KEY UPDATE preselectedPinSCAnalytics = '${data.preselectedPinSCAnalytics}'`;
//     }else if(data.tuning){
//         var sql = `INSERT INTO usersConfigurations(userId, email,preselectedPinSCTuning) VALUES 
//       (${req.headers.session.userId},'${req.headers.session.email}','${data.preselectedPinSCTuning}') ON DUPLICATE KEY UPDATE preselectedPinSCTuning = '${data.preselectedPinSCTuning}'`;
//     }else if(data.hideSuggestion){
//         var sql = `INSERT INTO usersConfigurations(userId, email,preselectedPinSCHideSuggestion) VALUES 
//       (${req.headers.session.userId},'${req.headers.session.email}','${data.preselectedPinSCHideSuggestion}') ON DUPLICATE KEY UPDATE preselectedPinSCHideSuggestion = '${data.preselectedPinSCHideSuggestion}'`;
//     }
    
//     await connection[req.headers['tenant-id']].execute.query(sql, (err, rows) => {})
//   } 
//   var sql = `SELECT usersConfigurations.*, user.timezone from usersConfigurations right join user on user.user_email = email where user.user_email = '${req.headers.session.email}'`;
//   await connection[req.headers['tenant-id']].execute.query(sql, (err, rows) => {
//     if (err) console.log(`Error ${err}`)
//     else {
//       res.send({ flag: 200,data:rows.length?rows[0]:{}, message: "User configurations" })
//     }
//   })
// });
router.get("/statusPage", async function (req, res){
  try{
    let redirectUrl = '/status-page';
    if(config.get("restrictUserAccount").includes(req.headers.session.email.split('@')[1]) && config.get("allowStatusPageAdminLogin")){
      redirectUrl = '/app/dashboard'
    }
    const clientUrl = `${config.get('statusPageService.url')}${redirectUrl}`;
    const response = { tenantId: req.headers['tenant-id'], sourceUrl: `${config.get('statusPageService.url')}/admin/user/sso`, clientUrl};
    const u = await getAccessTokenFromTenantId(req.headers['tenant-id']);
    let accessToken = (typeof req.headers.session.accessToken != 'undefined') ? req.headers.session.accessToken : '';
    let uid = (typeof req.headers.session.userId != 'undefined') ? req.headers.session.userId : '';
    let email = (typeof req.headers.session.email != 'undefined') ? req.headers.session.email : '';
    let name = (typeof req.headers.session.name != 'undefined') ? req.headers.session.name : '';

    var d = new Date();
    var d2 = d.getTime() + 180 * 60000;
    let payloadData = {
        "access_token": accessToken,
        "UserId": uid,
        "name": name,
        "email": email,
        "instanceUrl": config.get('adminURL'),
        "tenantId": req.headers['tenant-id'],
        "clientUrl": clientUrl,
        "iat": d.getTime() / 1000,
        "exp": d2 / 1000
    };
    var token = jwt.sign(payloadData, Buffer.from(u.accessToken + payloadData.UserId, 'base64'));

    var payloadToken = token.split('.')[1];
    var jwtToken = encode(u.accessToken, payloadData);
    var newToken = token.replace(payloadToken, jwtToken);
    response.code = newToken;
    res.send(response)
  }catch(error){
    res.send(error)
  }
})

router.get("/getTimeZoneList", function (req, res) {
var List = timeZoneList;
res.send(List);
});

router.get('/getSuperAdminUsersList', (req, res, next) => {
  connection[req.headers['tenant-id']].execute.query(`SELECT name, ur.roleID FROM user u , user_roles ur where u.id=ur.id and ur.roleId =4`, (err, rows) => {
    let listOfSuperAdmin = [];
    rows.map((e)=>{listOfSuperAdmin.push(e.name)})
    res.send(listOfSuperAdmin);
  });
})

let accessCheck = async (email, roleId, type, manageUserEmail,req) => {
  let allowedSUuser = [/grazitti.com/, /searchunify.com/];
  /** All user with grazitti.com and searchunify.com are considered as Searchunify user */
  let searchUnifyUser = allowedSUuser[0].test(email) || allowedSUuser[1].test(email);
  if (searchUnifyUser) { return true }
  else if (type == 'accessControlTab') {
    return searchUnifyUser || roleId == 4;
  } else if (type == 'manageUser') {
    if (![1, 4, '1', '4'].includes(roleId)) { return false }
    let editedUserRoleID;
    var queryString = `select roleId from user u JOIN user_roles ur where u.id = ur.userId  and u.user_email='${manageUserEmail}' and roleId!=4 `
    await (new Promise((resolve, reject) => {
      connection[req.headers['tenant-id']].execute.query(queryString, (err, rows) => {
        editedUserRoleID = rows[0].roleId
        resolve("done!")
      })
    }));
    return !((editedUserRoleID == 4 && roleId == 1) || (editedUserRoleID === roleId))
  }
}

router.get('/getAdminChatbotSettings', (req, res, next) => {
  connection[req.headers['tenant-id']].execute.query(`UPDATE user set adminChatbotDetails=?`,[req.query.isEnabled], (err, rows) => {
    res.send({updated:true})
  });
})

router.get('/getAdminChatbotToggle', (req, res, next) => {
  connection[req.headers['tenant-id']].execute.query(`select adminChatbotDetails from user LIMIT 1`, (err, rows) => {
    res.send(rows);
  });
})

router.post('/ssoActivation', async (req, res) => {
  let sql = `Update saml_auth set isActivated = ? where tenant_id = '${req.headers['tenant-id']}' and idp_type=?`;
  connection[req.headers['tenant-id']].execute.query(sql,  [req.body.activate, req.body.idp_type ], (err, rows) => {
    if(err) {
      res.send({ message: "Error Occured!!", activate: 0 });
    } else {
      // initialise the passport strategy
      if (req.body.idp_type == IDP_TYPES.ADMIN_SSO) {
        const query = `SELECT * FROM saml_auth where idp_type=?`;
        connection[req.headers['tenant-id']].execute.query(query,  [req.body.idp_type ], (err, rows) => {
          if(err) {
            res.send({ message: "Error Occured!!", activate: req.body.activate });
          } else {
            if (rows && rows.length) {
              const ssoInfo = rows[0];
              passport.use(new SamlStrategy({ 
                entryPoint: ssoInfo.saml_sso_url,
                callbackUrl: `https://${req.headers.session.config.subdomain}` + `/saml/auth/${md5(req.headers['tenant-id'])}`,
                signatureAlgorithm:'sha256',
                digestAlgorithm:'sha256',
                issuer: `https://${req.headers.session.config.subdomain}`,
                identifierFormat: null,
                decryptionPvk: fs.readFileSync(__dirname + '/../../cert/key.pem', 'utf8'),
                privateCert: fs.readFileSync(__dirname + '/../../cert/key.pem', 'utf8'),
                cert: ssoInfo.certificate,
                validateInResponseTo: false,
                acceptedClockSkewMs: -1,
                disableRequestedAuthnContext: true,
                logoutUrl: ssoInfo.saml_logout_url,
                logoutCallbackUrl: `https://${req.headers.session.config.subdomain}/saml/logout`
              }, function (profile, done) {
                done(null, profile);
              }));
            }
          }
        });
      }
      res.send({ message: "Success", activate: req.body.activate });
    }
  });
});

router.get('/getOSStatus', (req, res, next) => {
  res.send({status: osMigration, days: osMigrationDays});
})

router.post('/sandboxToProductionMapping', (req,res,next) => {
  const authUrl = config.get('authUrl');
  const method = 'POST';
  const url = `${authUrl}/sandboxToProductionMapping`;
  const headers = {
    'Cookie': req.headers.cookie, 
    'CSRF-Token' : req.headers['csrf-token'],
    "tenant-id": req.headers['tenant-id'],
  };

  let body = {};
      body = { 
        mapped_instance_url:req.body.paramters.mappingUrl,
        mapped_tenant_id:req.body.paramters.mappingTenantId,
        current_tenant_id: req.headers["tenant-id"],
        current_instance_url: config.get('adminURL'),
  }

  commonFunctions.httpRequest(
    method,
    url,
    "",
    body,
    headers,
    function (error, body) {
      if(!error){
        res.send({ code:200, message:"Updated the table" });
      }else{
        res.send({ code:500, message:error });
      }    
    }
  );
})

router.get('/getsandboxToProductionMapping', (req,res,next) => {
  const authUrl = config.get('authUrl');
  const method = 'GET';
  const url = `${authUrl}/getSandboxToProductionMapping`;
  const headers = {
    'Cookie': req.headers.cookie, 
    'CSRF-Token' : req.headers['csrf-token'],
    "tenant-id": req.headers['tenant-id'],
  };

  commonFunctions.httpRequest(
    method,
    url,
    "",
    {},
    headers,
    function (error, body) {
      if(!error){
        res.send({ data:body });
      }else{
        res.send({error: error})
      }
    }
  );
})

// Getting notification list from status page
router.post("/getNotificationFromStatusPage", function (req, res) {
  const statusPageBody = {
    pageNumber: req.body.pageNumber,
    limit: req.body.limit,
    appId: req.body.appId,
    ids: req.body.ids,
    unread: req.body.unread
  };
  let method = req.method; //'POST'
  let url = config.get('statusPageService.url') + '/admin/notifications/fetch';
  let headers = {
      "Content-Type": "application/json",
      "lastlogin": JSON.stringify(req.body.firstLogin),
      "authorization": config.get("statusPageService.secretAuthToken"),
      "referer": config.get('adminURL'),
      "tenant": req.headers['tenant-id']
  };
  commonFunctions.httpRequest(
      method,
      url,
      "",
      statusPageBody,
      headers,
      function (error, body) {
          if (error || (body && body.status === 500)) {
              res.send({ status: "400", data: error || body });
          } else {
              res.send({ status: "200", data: body });
          }
      }
  );
});

// Adding clicked notifications details in "notification_read_state" table

router.post('/updateNotificationReadState', async (req, res, next) => {
  const sqlCS = 'INSERT INTO notification_read_state(notification_id, email) VALUES (?,?) ON DUPLICATE KEY UPDATE notification_id = VALUES(notification_id), email = VALUES(email)';
  const notificationIds = Array.isArray(req.body.parameters.notification_id) ? req.body.parameters.notification_id : [req.body.parameters.notification_id];
  const email = req.body.parameters.email;

  try {
    // Using a promise to handle each query asynchronously
    const promises = notificationIds.map(notificationId => {
      return new Promise((resolve, reject) => {
        connection[req.headers['tenant-id']].execute.query(sqlCS, [notificationId, email], (errAsync, rows) => {
          if (errAsync) {
            console.error("Error inside notification_read_state");
            console.error(errAsync);
            reject(errAsync);
          } else {
            resolve(rows);
          }
        });
      });
    });

    // Waiting for all promises to resolve
    await Promise.all(promises);
    res.send({ flag: 200, message: 'Success' });
  } catch (err) {
    res.send({ message: 'error' });
  }
});

// Getting seen notifications details by user saved in "notification_read_state" table
router.get("/getSeenNotificationsList", async (req, res) => {
  if (!req.headers["tenant-id"]) {
      return res.send({
          success: false,
          message: "Invalid Request",
          data: null,
      });
  }
  const sql = "SELECT * FROM notification_read_state";
  connection[req.headers["tenant-id"]].execute.query(sql, [], (err, rows) => {
      if (err || !rows || !rows.length) {
          console.log(rows);
          commonFunctions.errorlogger.error(
              err || "no data found in notification_read_state"
          );
          return res.send({
              success: false,
              message: "Failed fetching notification_read_state",
              data: null,
          });
      }
      res.send({
          success: true,
          message: "Successfuly fetched notification_read_state",
          data: rows
      });
  });
});

// Status Page API hit for getting unseen notifications ids for mark all as read 
router.post("/fetchNotificationIdsList", function (req, res) {
  const statusPageBody = {
    appId: req.body.appId,
    ids: req.body.notification_id
  };
  if (config.get('adminURL') !== "https://feature6.searchunify.com") {
    statusPageBody.status = 1;
  }
  let method = req.method; //'POST'
  let url = config.get('statusPageService.url') + '/admin/notifications/fetchIds';
  let headers = {
      "Content-Type": "application/json",
      "lastlogin": JSON.stringify(req.body.firstAndLastLogin),
      "authorization": config.get("statusPageService.secretAuthToken"),
      "tenant": req.headers['tenant-id']
  };
  commonFunctions.httpRequest(
      method,
      url,
      "",
      statusPageBody,
      headers,
      function (error, body) {
          if (error || (body && body.status === 500)) {
              res.send({ status: "400", data: error || body });
          } else {
              res.send({ status: "200", data: body });
          }
      }
  );
});
 
module.exports = {
  router: router,
  getSignupSettings
};
