const async = require("async");
const fs = require("fs");
const path = require("path");
var oauthLogs = require('./../routes/admin/rateLimit');
var { insertLanguages } = require('./languages.js');
const connection_sql = require('../utils/connection');
var commonFunctions = require('../utils/commonFunctions');
var { changeCollation } = require('./changeCollation');
const checkAutomatedScripts = require("./checkAutomatedScripts");

const init = function () {
  var zoomin = "INSERT INTO `content_source_types` " +
    "(`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) " +
    "VALUES" +
    "(31, 'zoomin', 'API', 'Zoomin content source', 'assets/img/zoomin.png', 1, NULL, 'assets/img/zoomin.png')" +
    " ON DUPLICATE KEY UPDATE id=31;";
  var receptive = "INSERT INTO `content_source_types` " +
    "(`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) " +
    "VALUES" +
    "(30, 'receptive', 'API', 'Receptive content source', 'assets/img/receptive.svg', 1, NULL, 'assets/img/receptive.svg')" +
    " ON DUPLICATE KEY UPDATE id=30;";
  var dynamics = "INSERT INTO `content_source_types` " +
    "(`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) " +
    "VALUES" +
    "(29, 'Dynamics', 'API', 'Dynamics content source', 'assets/img/dynamics.png', 1, NULL, 'assets/img/dynamics.png')" +
    " ON DUPLICATE KEY UPDATE id=29;";
  var helpscout = "INSERT INTO `content_source_types` " +
    "(`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) " +
    "VALUES" +
    "(14, 'HelpScout', 'API', 'Help Scout content source', 'assets/img/helpscout.png', 1, NULL, 'assets/img/helpscout-large.png')" +
    " ON DUPLICATE KEY UPDATE id=14;";
  var amazonS3 = "INSERT INTO `content_source_types` " +
    "(`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) " +
    "VALUES" +
    "(19, 'Amazon S3', 'API', 'Amazon S3 content source', 'assets/img/amazon-s3.png', 1, NULL, 'assets/img/amazon-s3-large.png')" +
    " ON DUPLICATE KEY UPDATE id=19;";
  var github = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES ('15', 'Github', 'API', 'github content source', 'assets/img/github.jpg', '1', NULL, 'assets/img/github-large.png')" +
    " ON DUPLICATE KEY UPDATE id=15;";

  var litmos = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(20, 'Litmos', 'API', 'Litmos content source', 'assets/img/litmos.png', 1, NULL, 'assets/img/litmos.png')" +
    " ON DUPLICATE KEY UPDATE id=20;";

  var moodle = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(23, 'Moodle', 'API', 'Moodle content source', 'assets/img/moodle.png', 1, NULL, 'assets/img/moodle.png')" +
    " ON DUPLICATE KEY UPDATE id=23;";

  var sabaCloud = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(25, 'SabaCloud', 'API', 'Saba Cloud content source', 'assets/img/saba.png', 1, NULL, 'assets/img/saba.png')" +
    " ON DUPLICATE KEY UPDATE id=25,name='SabaCloud';";

  var jiraOnprem = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(27, 'Jira Onprem', 'API', 'jira Onprem content source', 'assets/img/jira.svg', 1, NULL, 'assets/img/jira.svg')" +
    " ON DUPLICATE KEY UPDATE id=27;";

  var servicenow = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(26, 'servicenow', 'API', 'servicenow content source', 'assets/img/ServiceNow-Logo.png', 1, NULL, 'assets/img/ServiceNow-Logo.png')" +
    " ON DUPLICATE KEY UPDATE id=26;";
  var dropbox = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(24, 'Dropbox', 'API', 'Dropbox content source', 'assets/img/dropbox.png', 1, 'https://docs.searchunify.com/KB/GettingStartedwithSearchUnify/ContentSources/Using_Dropbox_As_A_Content_Repository.htm', 'assets/img/dropbox.png')" +
    " ON DUPLICATE KEY UPDATE id=24;";

  var marketo = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(28, 'Marketo', 'API', 'Marketo content source', 'assets/img/marketo.png', 1, NULL, 'assets/img/marketo.png')" +
    " ON DUPLICATE KEY UPDATE id=28;";

  var azureDevops = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(34, 'Azure Devops', 'API', 'Azure Devops Content Source', 'assets/img/azure-devops.png', 1, 'NULL', 'assets/img/azure-devops.png')" +
    " ON DUPLICATE KEY UPDATE id=34;";

  var cornerstone = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(38, 'Cornerstone', 'API', 'Cornerstone content source', 'assets/img/cornerstone-crawler.png', 1, 'NULL', 'assets/img/cornerstone-crawler-small.png')" +
    " ON DUPLICATE KEY UPDATE id=38;";

  var zendeskSearchClient = "INSERT INTO `search_client_types` (`id`, `name`, `description`, `img`, `standard_client_folder`, `large_image`) VALUES " +
    "('11', 'Zendesk Guide', 'zendesk Guide', 'assets/img/zendesk_help_center.svg', 'resources/search_clients_standard/zendesk_help_center', 'assets/img/zendesk_help_center_large.svg')" +
    "  ON DUPLICATE KEY UPDATE id=11,name='Zendesk Guide',description='zendesk Guide',img='assets/img/zendesk_help_center.svg',large_image='assets/img/zendesk_help_center_large.svg',standard_client_folder='resources/search_clients_standard/zendesk_help_center';";

  var aemSearchClient = "INSERT INTO `search_client_types` (`id`, `name`, `description`, `img`, `standard_client_folder`, `large_image`) VALUES " +
    "('24', 'AEM', 'AEM', 'assets/img/aem.svg', 'resources/search_clients_standard/aem', 'assets/img/aem_large.svg')" +
    "  ON DUPLICATE KEY UPDATE id=24,img='assets/img/aem.svg',large_image='assets/img/aem_large.svg',standard_client_folder='resources/search_clients_standard/aem';";

  var zendeskSupportSearchClient = "INSERT INTO `search_client_types` (`id`, `name`, `description`, `img`, `standard_client_folder`, `large_image`) VALUES" +
    " ('12', 'Zendesk Support', 'zendesk Support', 'assets/img/zendesk_support.svg', 'resources/search_clients_standard/zendesk_support', 'assets/img/zendesk_support_large.svg')" +
    "  ON DUPLICATE KEY UPDATE id=12,img='assets/img/zendesk_support.svg',large_image='assets/img/zendesk_support_large.svg',standard_client_folder='resources/search_clients_standard/zendesk_support';";

  var solr = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES ('21', 'SolrCrawler', 'API', 'solr content source', 'assets/img/solr.png', '1', NULL, 'assets/img/solr.png')" +
    " ON DUPLICATE KEY UPDATE id=21;";

  var higherLogic = "INSERT INTO `search_client_types` (`id`, `name`, `description`, `img`, `standard_client_folder`, `large_image`) VALUES " +
    "(23, 'Higher Logic', 'Higher Logic','assets/img/higher_logic.svg','resources/search_clients_standard/higher_logic','assets/img/higher_logic_large.svg')" +
    "  ON DUPLICATE KEY UPDATE id= 23,img='assets/img/higher_logic.svg',large_image='assets/img/higher_logic_large.svg'";
  
  var joomlaSearchClient = "INSERT INTO `search_client_types` (`id`, `name`, `description`, `img`, `standard_client_folder`, `large_image`,`category`) VALUES " +
    "(26, 'Joomla', 'Joomla','assets/img/joomla.svg','resources/search_clients_standard/joomla','assets/img/joomla_large.svg',1)" +
    "  ON DUPLICATE KEY UPDATE id= 26,img='assets/img/joomla.svg',large_image='assets/img/joomla_large.svg',category=1";


  var customContentSource = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES ('22', 'CustomContentSource', 'API', 'custom content source', 'assets/img/API.svg', '1', NULL, 'assets/img/API.svg')" +
    " ON DUPLICATE KEY UPDATE id=22;";

  var delete_admin_reports_settings = "DROP TABLE IF EXISTS admin_report_settings;"
  var delete_admin_reports = "DROP TABLE IF EXISTS admin_reports;"
  var wordpressSearchClient = "INSERT INTO  search_client_types (`id` ,`name` ,`description` ,`img` ,`standard_client_folder` ,`large_image`) VALUES (14 ,  'WordPress',  'WordPress Search Client',  'assets/img/Wordpress.svg',  'resources/search_clients_standard/wordpress',  'assets/img/Wordpress_large.svg')" +
    " ON DUPLICATE KEY UPDATE id=14,name='WordPress',description='WordPress Search Client',img='assets/img/Wordpress.svg',large_image='assets/img/Wordpress_large.svg',standard_client_folder='resources/search_clients_standard/wordpress';";

  var drupalSearchClient7 = "INSERT INTO  search_client_types (`id` ,`name` ,`description` ,`img` ,`standard_client_folder` ,`large_image`) VALUES (15 ,  'Drupal 7',  'Drupal 7 Search Client',  'assets/img/Drupal_7.svg',  'resources/search_clients_standard/drupal7',  'assets/img/Drupal_7_large.svg')" +
    " ON DUPLICATE KEY UPDATE id=15,name='Drupal 7',description='Drupal 7 Search Client',img='assets/img/Drupal_7.svg',large_image='assets/img/Drupal_7_large.svg',standard_client_folder='resources/search_clients_standard/drupal7';";

  var zendeskSupportConsoleSearchClient = "INSERT INTO  search_client_types (`id` ,`name` ,`description` ,`img` ,`standard_client_folder` ,`large_image`) VALUES (16 ,  'Zendesk Support Console',  'Zendesk Support Console Search Client',  'assets/img/zendesk_support_console.svg',  'resources/search_clients_standard/zendesk_support_console',  'assets/img/zendesk_support_console_large.svg')" +
    " ON DUPLICATE KEY UPDATE id=16,name='Zendesk Support Console',description='Zendesk Support Console Search Client',img='assets/img/zendesk_support_console.svg',large_image='assets/img/zendesk_support_console_large.svg',standard_client_folder='resources/search_clients_standard/zendesk_support_console';";

  var drupalSearchClient8 = "INSERT INTO  search_client_types (`id` ,`name` ,`description` ,`img` ,`standard_client_folder` ,`large_image`) VALUES (17 ,  'Drupal 8',  'Drupal 8 Search Client',  'assets/img/Drupal_8.svg',  'resources/search_clients_standard/drupal8',  'assets/img/Drupal_8_large.svg')" +
    " ON DUPLICATE KEY UPDATE id=17,name='Drupal 8',description='Drupal 8 Search Client',img='assets/img/Drupal_8.svg',large_image='assets/img/Drupal_8_large.svg',standard_client_folder='resources/search_clients_standard/drupal8';";

  var microsoftDynamics = "INSERT INTO  search_client_types (`id` ,`name` ,`description` ,`img` ,`standard_client_folder` ,`large_image`) VALUES (18 ,  'Microsoft Dynamics',  'Microsoft Dynamics Search Client',  'assets/img/Microsoft_Dynamics.svg',  'resources/search_clients_standard/microsoft_dynamics',  'assets/img/Microsoft_Dynamics_large.svg')" +
    " ON DUPLICATE KEY UPDATE id=18,name='Microsoft Dynamics',description='Microsoft Dynamics Search Client',img='assets/img/Microsoft_Dynamics.svg',large_image='assets/img/Microsoft_Dynamics_large.svg',standard_client_folder='resources/search_clients_standard/microsoft_dynamics';";

  var mindtouchSupportSearchClient = "INSERT INTO `search_client_types` (`id`, `name`, `description`, `img`, `standard_client_folder`, `large_image`) VALUES" +
    " ('19', 'Mindtouch', 'Mindtouch Support Client', 'assets/img/mindtouch.svg', 'resources/search_clients_standard/mindtouch', 'assets/img/mindtouch_large.svg')" +
    "  ON DUPLICATE KEY UPDATE id=19,img='assets/img/mindtouch.svg',large_image='assets/img/mindtouch_large.svg',standard_client_folder='resources/search_clients_standard/mindtouch';";
  var hostedSearchClient = "INSERT INTO `search_client_types` (`id`, `name`, `description`, `img`, `standard_client_folder`, `large_image`) VALUES" +
    " ('20', 'Hosted Search Client', 'Hosted Search Client', 'assets/img/hostedSearch.svg', 'resources/search_clients_standard/hosted', 'assets/img/hostedSearch_large.svg')" +
    "  ON DUPLICATE KEY UPDATE id=20,img='assets/img/hostedSearch.svg',large_image='assets/img/hostedSearch_large.svg',standard_client_folder='resources/search_clients_standard/hosted';";

  var servicenowSearchClient = "INSERT INTO `search_client_types` (`id`, `name`, `description`, `img`, `standard_client_folder`, `large_image`) VALUES" +
    " ('21', 'ServiceNow', 'ServiceNow', 'assets/img/ServiceNow_Logo.svg', 'resources/search_clients_standard/serviceNow', 'assets/img/ServiceNow_Logo_large.svg')" +
    "  ON DUPLICATE KEY UPDATE id=21,img='assets/img/ServiceNow_Logo.svg',large_image='assets/img/ServiceNow_Logo_large.svg',standard_client_folder='resources/search_clients_standard/serviceNow';";

  // var reactSearchClient = "INSERT INTO `search_client_types` (`id`, `name`, `description`, `img`, `standard_client_folder`, `large_image`) VALUES" +
  //   " ('26', 'react', 'react', 'assets/img/react-Logo.svg', 'resources/search_clients_standard/react', 'assets/img/react-Logo.svg')" +
  //   "  ON DUPLICATE KEY UPDATE id=26,img='assets/img/react-Logo.svg',large_image='assets/img/react-Logo.svg',standard_client_folder='resources/search_clients_standard/react';";

  var docebo = "INSERT INTO `content_source_types` " +
    "(`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`)" +
    " VALUES " +
    "(32, 'Docebo', 'API', 'Docebo Connector', 'assets/img/Docebo_Logo.jpg', 1, NULL, 'assets/img/Docebo_Logo.jpg')" +
    " ON DUPLICATE KEY UPDATE id=32;";
  var vimeo = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(33, 'Vimeo', 'API', 'Vimeo content source', 'assets/img/vimeo.png', 1, 'NULL', 'assets/img/vimeo.png')" +
    " ON DUPLICATE KEY UPDATE id=33;";
  var skilljar = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(39, 'skilljar', 'API', 'skilljar content source', 'assets/img/skilljar.png', 1, 'NULL', 'assets/img/skilljar.png')" +
    " ON DUPLICATE KEY UPDATE id=39;";

  let higherLogicCS = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`) VALUES " +
    "(41, 'HigherLogic', 'API', 'HigherLogic content source', 'assets/img/higherLogic.png', 1, 'NULL', 'assets/img/higherLogic.png')" +
    " ON DUPLICATE KEY UPDATE id=41;";
  let ahaCS = "INSERT INTO `content_source_types` (`id`, `name`, `cralwingType`, `description`, `img`, `isAvailable`, `instructions`, `large_image`, `category`, url) VALUES " +
  "(42, 'Aha!', 'API', 'Product Management and Roadmapping Tool', 'assets/img/aha.svg', 1, 'NULL', 'assets/img/aha.png', 7, 'https://docs.searchunify.com/Content/Content-Sources/aha.htm')" +
  " ON DUPLICATE KEY UPDATE id=42;";

  var SlackBotSearchClient = "INSERT INTO `search_client_types` (`id`, `name`, `description`, `img`, `standard_client_folder`, `large_image`) VALUES" +
    " ('22', 'SlackBot', 'SlackBot', 'assets/img/slackbot.svg', 'resources/search_clients_standard/slackBot', 'assets/img/slackbot_large.svg')" +
    "  ON DUPLICATE KEY UPDATE id=22,img='assets/img/slackbot.svg',large_image='assets/img/slackbot_large.svg',standard_client_folder='resources/search_clients_standard/slackBot';";

  var SharePointSearchClient = "INSERT INTO `search_client_types` (`id`, `name`, `description`, `img`, `standard_client_folder`, `large_image`) VALUES " +
    "('25', 'SharePoint', 'SharePoint', 'assets/img/sharepointSearchclient.svg', 'resources/search_clients_standard/sharepoint', 'assets/img/sharepointSearchclient_large.svg')" +
    " ON DUPLICATE KEY UPDATE id=25,img='assets/img/sharepointSearchclient.svg',large_image='assets/img/sharepointSearchclient_large.svg',standard_client_folder='resources/search_clients_standard/sharepoint';";
  var deleteIdpMapping = `DROP TABLE IF EXISTS idp_attribute_mapping`
  var deleteIdp = `DROP TABLE IF EXISTS idp`;



  var updateContentSourceImages =  `update content_source_types set img = 'assets/img/jive-small.svg' where id = 1;
  update content_source_types set img = 'assets/img/lithium-small.svg' where id = 2;
  update content_source_types set img = 'assets/img/salesforce-small.svg' where id = 3;
  update content_source_types set img = 'assets/img/confluence-small.svg' where id = 4;
  update content_source_types set img = 'assets/img/sharepoint-small.svg' where id = 5;
  update content_source_types set img = 'assets/img/jira-small.svg' where id = 6;
  update content_source_types set img = 'assets/img/zendesk-small.svg' where id = 7;
  update content_source_types set img = 'assets/img/slack-small.svg' where id = 8;
  update content_source_types set img = 'assets/img/website-small.svg' where id = 9;
  update content_source_types set img = 'assets/img/madcap-small.svg' where id = 10;
  update content_source_types set img = 'assets/img/mindtouch-small.svg' where id = 11;
  update content_source_types set img = 'assets/img/drive-small.svg' where id = 12;
  update content_source_types set img = 'assets/img/box-small.svg' where id = 13;
  update content_source_types set img = 'assets/img/helpscout-small.svg' where id = 14;
  update content_source_types set img = 'assets/img/github-small.svg' where id = 15;
  update content_source_types set img = 'assets/img/youtube-small.svg' where id = 17;
  update content_source_types set img = 'assets/img/stackoverflow-small.svg' where id = 18;
  update content_source_types set img = 'assets/img/amazon-s3-small.svg' where id = 19;
  update content_source_types set img = 'assets/img/litmos-small.svg' where id =20;
  update content_source_types set img = 'assets/img/solr-small.svg' where id =21;
  update content_source_types set img = 'assets/img/api-small.svg' where id =22;
  update content_source_types set img = 'assets/img/moodle-small.svg' where id =23;
  update content_source_types set img = 'assets/img/dropbox-small.svg' where id =24;
  update content_source_types set img = 'assets/img/saba-small.svg' where id =25;
  update content_source_types set img = 'assets/img/servicenow-small.svg' where id =26;
  update content_source_types set img = 'assets/img/jiraonprem-small.svg' where id =27;
  update content_source_types set img = 'assets/img/marketo-small.svg' where id =28;
  update content_source_types set img = 'assets/img/dynamics-small.svg' where id =29;
  update content_source_types set img = 'assets/img/receptive-small.svg' where id =30;
  update content_source_types set img = 'assets/img/zoomin-small.svg' where id =31;
  update content_source_types set img = 'assets/img/docebo-small.svg' where id =32;
  update content_source_types set img = 'assets/img/vimeo-small.svg' where id =33;
  update content_source_types set img = 'assets/img/azuredevops-small.svg' where id =34;
  update content_source_types set img = 'assets/img/skilljar-small.svg' where id =39;
  update content_source_types set img = 'assets/img/higherlogic-small.svg' where id =41;`

  sqlWrite(updateContentSourceImages, "Content Source Updated Small Logo Images")
  sqlWrite(SharePointSearchClient, "SharePointSearchClient Insereted on Bootstrap");
  sqlWrite(hostedSearchClient, "hostedSearchClient Insereted on Bootstrap");
  sqlWrite(zoomin, "zoomin Inserted on Bootstrap");
  sqlWrite(receptive, "receptive Insereted on Bootstrap");
  sqlWrite(dynamics, "Dynamics Insereted on Bootstrap");
  sqlWrite(helpscout, "Helscout Insereted on Bootstrap");
  sqlWrite(github, "github Insereted on Bootstrap");
  sqlWrite(mindtouchSupportSearchClient, "mindtouch Search Client Insereted on Bootstrap");
  sqlWrite(zendeskSearchClient, "zendesk Search Client Insereted on Bootstrap");
  sqlWrite(aemSearchClient, "AEM Search Client Insereted on Bootstrap");
  sqlWrite(zendeskSupportSearchClient, "zendesk Support Search Client Insereted on Bootstrap");
  sqlWrite(litmos, "litmos Insereted on Bootstrap");
  sqlWrite(moodle, "moodle Insereted on Bootstrap");
  sqlWrite(sabaCloud, "Saba Cloud Insereted on Bootstrap");
  sqlWrite(servicenow, "servicenow Insereted on Bootstrap");
  sqlWrite(servicenowSearchClient, "servicenowSearchClient Insereted on Bootstrap");
  //sqlWrite(reactSearchClient, "reactSearchClient Insereted on Bootstrap");
  sqlWrite(dropbox, "dropbox Insereted on Bootstrap");
  sqlWrite(marketo, "marketo Insereted on Bootstrap");
  sqlWrite(azureDevops, "azureDevops Inserted on Bootstrap");
  sqlWrite(cornerstone, "cornerstone Inserted on Bootstrap");
  sqlWrite(jiraOnprem, "jiraonprem Insereted on Bootstrap");
  sqlWrite(solr, "solr inserted in Bootstrap");
  sqlWrite(higherLogic, "Higher Logic inserted in Bootstrap");
  sqlWrite(joomlaSearchClient, "Joomla inserted in Bootstrap");
  sqlWrite(customContentSource, "Custom Content Source inserted in Bootstrap");
  sqlWrite('CREATE TABLE IF NOT EXISTS marketplace_addons (id int(11) NOT NULL AUTO_INCREMENT,is_installed tinyint(1) DEFAULT 0,is_search_client tinyint(1) DEFAULT 0,installed_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,name varchar(125) NOT NULL,uid varchar(125) NOT NULL,addon_id varchar(200),url text,auth_webhook text, platform varchar(30),api_name varchar(30), logo longtext,search_client_url longtext,PRIMARY KEY (id))',"Marketplace addons table created");
  sqlWrite('CREATE TABLE IF NOT EXISTS deflection_formula (id int NOT NULL AUTO_INCREMENT,search_client_id int NOT NULL,session_url_regex varchar(255),custom_event varchar(63) DEFAULT NULL,PRIMARY KEY (id),FOREIGN KEY (search_client_id) REFERENCES search_clients(id) ON DELETE CASCADE ON UPDATE CASCADE)', "Case Deflection table Created");
  sqlWrite('CREATE TABLE IF NOT EXISTS result_actions(result_action_id int NOT NULL AUTO_INCREMENT,search_client_type_id int NOT NULL,name varchar(255),information varchar(63) DEFAULT NULL,PRIMARY KEY (result_action_id),FOREIGN KEY (search_client_type_id) REFERENCES search_client_types(id) ON DELETE CASCADE ON UPDATE CASCADE)', "");
  sqlWrite('CREATE TABLE IF NOT EXISTS search_client_actions(id int NOT NULL AUTO_INCREMENT,search_client_id int NOT NULL,result_action_id int NOT NULL,status BOOLEAN DEFAULT 0,merge BOOLEAN DEFAULT 0,selected_object varchar(255),content_source_label varchar(255),PRIMARY KEY (id),FOREIGN KEY (search_client_id) REFERENCES search_clients(id) ON DELETE CASCADE ON UPDATE CASCADE,FOREIGN KEY (result_action_id) REFERENCES result_actions(result_action_id) ON DELETE CASCADE ON UPDATE CASCADE)', "");
  sqlWrite('CREATE TABLE IF NOT EXISTS `crawl_logs` (`id` bigint(20) NOT NULL AUTO_INCREMENT, `content_source_id` int(11) NOT NULL, `content_source_type_id` int(11) NOT NULL, `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `end_time` timestamp NOT NULL DEFAULT "0000-00-00 00:00:00", `duration` bigint(20) NOT NULL, `crawl_mode` varchar(45) NOT NULL, `run_status` tinyint(4) NOT NULL, `did_not_crawl` tinyint(4) NOT NULL, `input` json DEFAULT NULL, `output` json DEFAULT NULL, `failure_reason` mediumtext, `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, PRIMARY KEY (`id`), KEY `content_source_id` (`content_source_id`), KEY `content_source_type_id` (`content_source_type_id`) )', "");
  sqlWrite("CREATE TABLE IF NOT EXISTS website_setting (id int NOT NULL AUTO_INCREMENT,content_source_id int NOT NULL,include_url text,exclude_url text,PRIMARY KEY (id),FOREIGN KEY (content_source_id) REFERENCES content_sources(id) ON DELETE CASCADE ON UPDATE CASCADE);", "");
  sqlWrite(delete_admin_reports_settings, "delete admin reports settings");
  sqlWrite(delete_admin_reports, "delete admin reports");
  sqlWrite(wordpressSearchClient, "New search client for Wordpress");
  sqlWrite(drupalSearchClient7, "New search client for Drupal7");
  sqlWrite(drupalSearchClient8, "New search client for Drupal8");
  sqlWrite(zendeskSupportConsoleSearchClient, "New search client for Zendesk Support Console");
  sqlWrite(microsoftDynamics, "New search client for Drupal8");
  sqlWrite(deleteIdpMapping, "mapping delete");
  sqlWrite(deleteIdp, "delete idp");
  sqlWrite(docebo, "docebo Inserted on Bootstrap");
  sqlWrite(SlackBotSearchClient, "SlackBotSearchClient Inserted on Bootstrap")
  sqlWrite(vimeo, "vimeo Inserted on Bootstrap" );
  sqlWrite(skilljar, "skilljar Inserted on Bootstrap" );
  sqlWrite(higherLogicCS, "higherLogic Inserted on Bootstrap" );
  sqlWrite(ahaCS, "aha Inserted on Bootstrap" );

  //deflectionformula labels table 
  sqlWrite("CREATE TABLE IF NOT EXISTS analytics_cd_labels (id INT AUTO_INCREMENT primary key NOT NULL, uid VARCHAR(255), report_title_name TEXT NOT NULL, custom_title_name TEXT NOT NULL,tooltip TEXT NOT NULL);", "analytics label table on Bootstrap" );

  sqlWrite("CREATE TABLE IF NOT EXISTS synonym_settings (id INT primary key NOT NULL, exact_match BOOLEAN default 0 );", "synoymn settings bootstrap");
  sqlWrite("INSERT INTO synonym_settings (id, exact_match ) values (1, 0 ) ON DUPLICATE KEY UPDATE id=1;", "synoymn settings row inserted bootstrap");
  // TO be removed as schema moved to postgres
  // oauthLogs.oauthLogsIndex(function (err, result) {
  //   if (err) {
  //     console.error("Err to create oauth logs index", err);
  //   }
  // })


  async.auto({
    tableAnalyticsReport: cb => {
      let sql = `CREATE TABLE IF NOT EXISTS analytics_reports (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(500) NOT NULL,
        description varchar(1000) DEFAULT 'No description available',
        sample_img varchar(500) DEFAULT NULL,
        PRIMARY KEY (id)
      )`;
      connection.query(sql, cb);
    },
    addApiNameTOMarketPlaceAddon: cb =>{
      let sql = "ALTER TABLE `marketplace_addons` ADD `api_name` varchar(30)"
      sqlWriteIgnoreError(sql, "new api name added", cb);
    },
    dashboardReportsToAnalyticsReport: cb => {
      let sql = "ALTER TABLE `analytics_reports` ADD `is_dashboard` TINYINT NOT NULL DEFAULT '0' AFTER `sample_img`";
      sqlWriteIgnoreError(sql, "is_dasboard column", cb);
    },
    versionToAnalyticsReport: cb => {
      let sql = "ALTER TABLE `analytics_reports` ADD `version` INT(11) NOT NULL DEFAULT 1 AFTER `is_dashboard`";
      sqlWriteIgnoreError(sql, "version column", cb);
    },
    alterContentSourcesTable: cb => {
      let sql = `ALTER TABLE content_sources 
      ADD COLUMN current_crawl_mode varchar(255), 
      ADD COLUMN current_crawl_start_time timestamp,
      ADD COLUMN current_crawl_object_name varchar(255), 
      ADD COLUMN created timestamp default current_timestamp, 
      ADD COLUMN updated timestamp default current_timestamp on update current_timestamp,
      MODIFY COLUMN last_sync_date timestamp NOT NULL DEFAULT '0000-00-00 00:00:00'`
      sqlWriteIgnoreError(sql, "content sources columns added", cb);
    },
    addConnectionEditFlag: cb => {
      let sql = "ALTER TABLE `content_sources` ADD COLUMN `editing` BOOLEAN";
      sqlWriteIgnoreError(sql, "editing column", cb);
    },
    addCrawlStatus: cb => {
      let sql = "ALTER TABLE `content_sources` ADD COLUMN `crawl_status` TINYINT NOT NULL DEFAULT 2 AFTER `pid`";
      sqlWriteIgnoreError(sql, "crawl_status column", cb);
    },
    modifyCrawlStatus:['addCrawlStatus', (dataFromAbove, cb) => {
      let sql = "ALTER TABLE `content_sources` MODIFY COLUMN `crawl_status` TINYINT NOT NULL DEFAULT 0 AFTER `pid`";
      sqlWriteIgnoreError(sql, "crawl_status column modified", cb);
    }],
    insertValuesInAnalyticsReport: ["tableAnalyticsReport", "dashboardReportsToAnalyticsReport", (result, cb) => {
      let sql = `INSERT INTO analytics_reports (id, name, description, sample_img, is_dashboard, version) VALUES
        (1, 'Search Hit Count', 'Monthly total searches represented by line chart', NULL,0, 1),
        (2, 'Searches Count Without Result', 'Monthly search count which does not produce any result', NULL,0,1),
        (3, 'Top search queries', 'Most searched terms with count', NULL,0,1),
        (4, 'Top search queries without result', 'Most searched terms which does not produce any result', NULL,0,1),
        (5, 'Search filter based clicks', 'Histogram showing searches and respective conversions using filters', NULL,0,1),
        (6, 'Top clicks', 'Most viewed search result', NULL,0,1),
        (7, 'Discussion ready to become help article', 'No Description available', NULL,0,1),
        (8, 'Search index by content source', 'Content sources report (size/number of documents)', NULL,0,1),
        (9, 'Newly added content sources', 'No description available', NULL,0,1),
        (10, 'Results with least number of clicks', 'Search results with very less number of clicks', NULL,0,1),
        (11, 'Top converted results which missed top 10  ranks', 'Top converted results which missed top 10  ranks', NULL,0,1),
        (12, 'Top searches with no clicks', 'Top searched terms with no conversions', NULL,0,1),
        (13, 'Documents by content length', 'Report showing documents with too much content', NULL,0,1),
        (14, 'Session tracking - Overview', 'Session history details per user', NULL,0,1),
        (15, 'Session Tracking - Details', 'Session history details per user', NULL,0,1),
        (16, 'Attached to Case','Attached to Case',NULL,0,1),
        (17, 'Top Rated Featured Results','Top Rated Featured Results',NULL,0,1),
        (18, 'Total Conversion','Total Conversion',NULL,1,1),
        (19, 'Live Search Stream','Live Search Stream',NULL,1,1),
        (20, 'Recent Search','Recent Search',NULL,1,1),
        (21, 'Most searched keywords','Most searched keywords',NULL,1,1),
        (22, 'Avg Time on Documents','Avg Time on Documents',NULL,0,1),
        (23, 'Top Knowledge Graph Titles','Top Knowledge Graph Titles',NULL,0,1),
        (24, 'Search Summary', 'Search Summary', NULL, 0, 2),
        (25, 'Search Classifications', 'Search Classifications', NULL, 0, 2),
        (26, 'Search index by content source', 'Search index by content source', NULL, 0, 2),
        (27, 'Newly added content sources', 'Newly added content sources', NULL, 0, 2),
        (28, 'Session Analytics Overview', 'Session Analytics Overview', NULL, 0, 2),
        (29, 'Search filter based clicks', 'Search filter based clicks', NULL, 0, 2),
        (30, 'Search Classifications (Conversions)', 'Search Classifications for conversions', NULL, 0, 2),
        (31, 'Top Clicked Search Results', 'Top Clicked Search Results', NULL, 0, 2),
        (33, 'Unsuccessful Searches', 'Unsuccessful Searches', NULL, 0, 2),
        (34, 'Sessions with unsuccessful searches', 'Sessions with unsuccessful searches', NULL, 0, 2),
        (35, 'Session Tracking - Details', 'Session Tracking - Details', NULL, 0, 2),
        (36, 'Searches With No Clicks', 'Searches With No Clicks', NULL, 0, 2),
        (37, 'Searches With No Result', 'Searches With No Result', NULL, 0, 2),
        (38, 'Content Experience Feedback', 'Content Experience Feedback', NULL, 0, 1),
        (40, 'Top Clicked Searches', 'Most viewed search result', NULL,0,2),
        (41, 'Top Rated Featured Results','Top Rated Featured Results',NULL,0,2),
        (42, 'Top Knowledge Graph Titles','Top Knowledge Graph Titles',NULL,0,2),
        (43, 'Content Experience Feedback', 'Content Experience Feedback', NULL, 0, 2),
        (44, 'Discussion ready to become help article', 'No Description available', NULL,0,2),
        (45, 'Attached to Case','Attached to Case',NULL,0,2),
        (46, 'High Conversion Results Not on Page One', 'High Conversion Results Not on Page One', NULL, 0, 2),
        (47, 'Avg Time on Documents','Avg Time on Documents',NULL,0,2),
        (49, 'Total Conversion','Total Conversion',NULL,1,2),
        (50, 'Live Search Stream','Live Search Stream',NULL,1,2),
        (51, 'Recent Search','Recent Search',NULL,1,2),
        (52, 'Most searched keywords','Most searched keywords',NULL,1,2),
        (53, 'Articles failed to Deflect cases','Articles failed to Deflect cases',NULL,0,2),
        (54, 'Articles that Deflected cases','Articles that Deflected cases',NULL,0,2),
        (55, 'Documents by content length', 'Report showing documents with too much content', NULL,0,2),
        (57, 'Cases Created', 'Report showing cases with sessions', NULL,0,2),
        (58, 'Click Position Report', 'Report showing search click result for tracking performance', NULL,0,2),
        (59, 'Average Click Position', 'Report showing Average Click Position trend chart', NULL,0,2),
        (60, 'Case Deflection Trend Chart', 'Report showing Average Case deflection trends', NULL,0,2),
        (61, 'Articles Usage By Agents', 'Report showing articles used by agents', NULL,0,2),
        (62, 'Search Experience Feedback', 'Search Experience Feedback', NULL,0,2),
        (63, 'Share Results Analytics', 'Share Results Analytics', NULL,0,2)
        ON DUPLICATE KEY UPDATE
        name        = VALUES(name),
        description = VALUES(description),
        sample_img  = VALUES(sample_img),
        is_dashboard  = VALUES(is_dashboard)`;
      connection.query(sql, cb);
    }],
    createTableSearchClientToAnalyticsReport: ["insertValuesInAnalyticsReport", (result, cb) => {
      let sql = `CREATE TABLE IF NOT EXISTS search_client_to_analytics_report (
        id int(11) NOT NULL AUTO_INCREMENT,
        search_client_id int(11) NOT NULL,
        analytics_report_id int(11) NOT NULL,
        is_enabled tinyint(4) NOT NULL DEFAULT '1',
        label varchar(255) DEFAULT NULL,
        PRIMARY KEY (id),
        UNIQUE (search_client_id, analytics_report_id),
        FOREIGN KEY (search_client_id) REFERENCES search_clients(id)    ON DELETE CASCADE ON UPDATE CASCADE,
        FOREIGN KEY (analytics_report_id) REFERENCES analytics_reports(id) ON DELETE CASCADE ON UPDATE CASCADE
      ) `;
      connection.query(sql, cb);
    }],
    /*
      content source category
    */
    addCategoryToContentSources: cb => {
      let sql = "ALTER TABLE `content_source_types` ADD `category` INT NOT NULL , ADD `url` TEXT NOT NULL , ADD `new_content_source_expiry_date` DATE NULL";
      sqlWriteIgnoreError(sql, "category column", cb);
    },
    /*
      search client category
    */
    addCategoryToSearchClient: cb => {
      let sql = "ALTER TABLE `search_client_types` ADD `category` INT NOT NULL";
      sqlWriteIgnoreError(sql, "category column", cb);
    },
    addJavascriptSettingsForWebsite: cb => {
      let sql = `ALTER table website_setting add column javascript_enabled int(2) default 0`;
      sqlWriteIgnoreError(sql, "Javascript enable column", cb);
    },
    addis_case_deflected_shownForSearchClient: cb => {
      let sql = `ALTER table search_clients add column is_case_deflected_shown BOOLEAN default 0`;
      sqlWriteIgnoreError(sql, "is_case_deflected_shown column", cb);
    },
    addIndexingFiltersSettingsForWebsite: cb => {
      let sql = `ALTER table website_setting add column index_filters varchar(1000) default ''`;
      sqlWriteIgnoreError(sql, 'Added index filter column', cb);
    },
    addIndexUrlforOnpremise: cb => {
      let sql = `ALTER TABLE content_sources ADD searchunifyIndexUrl VARCHAR(100) NULL DEFAULT NULL AFTER clickScorePath;`;
      sqlWriteIgnoreError(sql, 'Added searchunifyIndexUrl column for onpremise crawler', cb);
    },
    addAutoTuningFlag: cb => {
      let sql = `ALTER table keyword_boost add column auto_boosted int(2) default 0`;
      sqlWriteIgnoreError(sql, 'Added auto boost column', cb);
    },
    personalisedBoosting: cb => {
      let sql = `ALTER table search_clients add column auto_boosting int(2) default 0`;
      sqlWriteIgnoreError(sql, 'Added auto boosting column', cb);
    },
    addCondtiionsInObjects: cb => {
      let sql = `ALTER TABLE content_source_objects ADD field_conditions TEXT NULL AFTER external_access`;
      sqlWriteIgnoreError(sql, 'Added conditions in objects', cb);
    },
    changeAccessToken: cb => {
      let sql = `ALTER TABLE content_source_authorization CHANGE accessToken accessToken mediumtext DEFAULT NULL;`;
      sqlWriteIgnoreError(sql, 'Added conditions in objects',  cb);
    },
    changeRefreshToken: cb => {
      let sql = `ALTER TABLE content_source_authorization CHANGE refreshToken refreshToken VARCHAR(3000) DEFAULT NULL;`;
      sqlWriteIgnoreError(sql, 'Added conditions in objects', cb);
    },
    jwt_href: cb => {
      let sql = `ALTER TABLE search_clients ADD jwt_href varchar(255) Default NULL;`;
      sqlWriteIgnoreError(sql, 'Added jwt_href in search_clients', cb);
    },
    jwt_expiry: cb => {
      let sql = `ALTER TABLE search_clients ADD jwt_expiry varchar(255) DEFAULT NULL;`;
      sqlWriteIgnoreError(sql, 'Added jwt_expiry in search_clients', cb);
    },
    // primaryKeyCSObjects: cb => {
    //   let sql = 'ALTER TABLE `content_source_objects` ADD UNIQUE( `id`);ALTER TABLE `content_source_objects` DROP PRIMARY KEY, ADD PRIMARY KEY( `content_source_id`,`name`);';
    //   sqlWriteIgnoreError(sql, 'changed primary key for content_source_objects');
    //   cb(null, 'changed primary key for content_source_objects');
    // },
    // primaryKeyCSObjectsAndFields: cb => {
    //   let sql = 'ALTER TABLE `content_source_object_fields` ADD UNIQUE( `id`);ALTER TABLE `content_source_object_fields` DROP PRIMARY KEY, ADD PRIMARY KEY(`content_source_object_id`,`name`);';
    //   sqlWriteIgnoreError(sql, 'changed primary key for content_source_objects_fields');
    //   cb(null, 'changed primary key for content_source_objects_fields');
    // },
    createApiCrawlerFields: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `api_crawler_fields` ("
        + "`id` int(11) NOT NULL AUTO_INCREMENT, "
        + "`url` text, "
        + "`method_type` varchar(250) DEFAULT NULL, "
        + "`headers` text, "
        + "`body` text, "
        + "`query` text, "
        + "`response` text, "
        + "`pagination_type` varchar(250) DEFAULT NULL, "
        + "`default_page_tag` varchar(254) DEFAULT NULL, "
        + "`total_count_tag` varchar(250) DEFAULT NULL, "
        + "`page_no_tag` varchar(250) DEFAULT NULL, "
        + "`offset_start_tag` varchar(250) DEFAULT NULL, "
        + "`page_size_tag` varchar(250) DEFAULT NULL, "
        + "`next_link_tag` varchar(1023) DEFAULT NULL, "
        + "`content_source_authorization_id` int(100) DEFAULT NULL, "
        + "`content_source_id` int(100) DEFAULT NULL, "
        + "`itr_tag` varchar(255) DEFAULT NULL, "
        + "`index_id` varchar(255) DEFAULT NULL, "
        + "`total_result_type` varchar(255) DEFAULT NULL, "
        + "`has_more_tag` varchar(250) DEFAULT NULL, "
        + "`next_link_response_tag` varchar(255) DEFAULT NULL, "
        + " PRIMARY KEY (id), "
        + " KEY(`content_source_id`), "
        + " FOREIGN KEY (`content_source_id`) REFERENCES `content_sources` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) ";
        connection.query(sql, cb);
    },
    createApiJsonFields: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `api_json_fields` (" +
        "`id` int(11) NOT NULL AUTO_INCREMENT, " +
        "`content_source_id` int(11) NOT NULL, " +
        "`label` varchar(255) DEFAULT NULL, " +
        "`tag` varchar(255) DEFAULT NULL ," +
        " PRIMARY KEY (`id`), " +
        " KEY (`content_source_id`), " +
        " FOREIGN KEY (`content_source_id`) REFERENCES `content_sources` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)";
        connection.query(sql, cb);
    },
    createAdminReportsPreferences: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS admin_reports_preferences (
        id int(11) NOT NULL AUTO_INCREMENT,
        report_order int(11) NOT NULL,
        analytics_report_id int(11) NOT NULL,
        user_id int(11) NULL,
        PRIMARY KEY (id),
        UNIQUE (report_order, analytics_report_id),
        FOREIGN KEY (analytics_report_id) REFERENCES analytics_reports(id) ON DELETE CASCADE ON UPDATE CASCADE,
        FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE ON UPDATE CASCADE
      ) `;
      connection.query(sql, cb);
    },
    insertAdminReportsPreferences: ["createAdminReportsPreferences", (result, cb) => {
      let sql = `INSERT INTO admin_reports_preferences (id, analytics_report_id, report_order, user_id) VALUES
      (1, 18, 1, 1),
      (2, 20, 2, 1),
      (3, 8, 3, 1),
      (4, 19, 4, 1),
      (5, 3, 5, 1),
      (6, 21, 6, 1)
      ON DUPLICATE KEY UPDATE
      analytics_report_id = VALUES(analytics_report_id),
      report_order = VALUES(report_order),
      user_id  = VALUES(user_id)`;
      connection.query(sql, cb);
    }],
    addKnowledgeGraph: cb => {
      let sql = `ALTER TABLE search_clients ADD knowledge_graph TINYINT(4) NOT NULL default 0`;
      sqlWriteIgnoreError(sql, 'Added knowledgeGraph in search Client', cb);
    },
    knowledge_graph_relation: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `knowledge_graph_relation` ("
        + " `id` int(11) NOT NULL AUTO_INCREMENT,"
        + " `name` varchar(256) DEFAULT NULL,"
        + " `search_client_id` int(11) DEFAULT NULL,"
        + " `cs_source_id` int(11) DEFAULT NULL,"
        + " `cs_destination_id` int(11) DEFAULT NULL,"
        + " PRIMARY KEY (`id`),"
        + " KEY (`search_client_id`),"
        + " KEY (`cs_source_id`),"
        + " KEY (`cs_destination_id`),"
        + " FOREIGN KEY (`search_client_id`) REFERENCES `search_clients` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,"
        + " FOREIGN KEY (`cs_source_id`) REFERENCES `content_sources` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,"
        + " FOREIGN KEY (`cs_destination_id`) REFERENCES `content_sources` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)";
        connection.query(sql, cb);
    },
    k_graph_fields_relation: ["knowledge_graph_relation", (result, cb) => {
      var sql = "CREATE TABLE IF NOT EXISTS `k_graph_fields_relation` ("
        + "`id` int(11) NOT NULL AUTO_INCREMENT,"
        + "`relation_id` int(11) NOT NULL,"
        + "`mapper_obj_id` int(11) DEFAULT NULL,"
        + "`mapping_obj_id` int(11) DEFAULT NULL,"
        + "`mapper_field_id` int(11) DEFAULT NULL,"
        + "`mapping_field_id` int(11) DEFAULT NULL,"
        + "`heading_1_id` int(11) DEFAULT NULL,"
        + "`heading_2_id` int(11) DEFAULT NULL,"
        + "`link_id` int(11) DEFAULT NULL,"
        + "`img_id` int(11) DEFAULT NULL,"
        + "`tag_line` varchar(254) DEFAULT NULL,"
        + " PRIMARY KEY (`id`),"
        + " KEY (`relation_id`),"
        + " KEY (`mapper_obj_id`),"
        + " KEY (`mapping_obj_id`),"
        + " FOREIGN KEY (`relation_id`) REFERENCES `knowledge_graph_relation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,"
        + " FOREIGN KEY (`mapper_obj_id`) REFERENCES `content_source_objects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,"
        + " FOREIGN KEY (`mapping_obj_id`) REFERENCES `content_source_objects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)";
      connection.query(sql, cb);
    }],
    meta_data_relation: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `meta_data_relation` ("
        + "`id` int(11) NOT NULL AUTO_INCREMENT,"
        + "`name` varchar(254) DEFAULT NULL,"
        + "`search_client_id` int(11) DEFAULT NULL,"
        + "`content_source_id` int(11) DEFAULT NULL,"
        + "`object_id` int(11) DEFAULT NULL,"
        + "`field_id` int(11) DEFAULT NULL,"
        + "`title_id` int(11) DEFAULT NULL,"
        + "`subtitle_id` int(11) DEFAULT NULL,"
        + "`link_id` int(11) DEFAULT NULL,"
        + "`description_id` int(11) DEFAULT NULL,"
        + "`img_id` int(11) DEFAULT NULL,"
        + "`layout` varchar(254) DEFAULT NULL,"
        + " PRIMARY KEY (`id`),"
        + " KEY (`search_client_id`),"
        + " KEY (`content_source_id`),"
        + " KEY (`object_id`),"
        + " FOREIGN KEY (`search_client_id`) REFERENCES `search_clients` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,"
        + " FOREIGN KEY (`content_source_id`) REFERENCES `content_sources` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,"
        + " FOREIGN KEY (`object_id`) REFERENCES `content_source_objects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)";
      connection.query(sql, cb);
    },
    meta_data_relation_fields: ["meta_data_relation", (result, cb) => {
      var sql = "CREATE TABLE IF NOT EXISTS `meta_data_relation_fields` ("
        + "`id` int(11) NOT NULL AUTO_INCREMENT,"
        + "`label` varchar(254) DEFAULT NULL,"
        + "`meta_data_relation_id` int(11) DEFAULT NULL,"
        + "`content_source_object_field_id` int(11) DEFAULT NULL,"
        + " PRIMARY KEY (`id`),"
        + " KEY (`meta_data_relation_id`),"
        + " FOREIGN KEY (`meta_data_relation_id`) REFERENCES `meta_data_relation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)";
      connection.query(sql, cb);
    }],
    /*
      content source category
    */
    createContentSourceCategory: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS content_source_categories (
      type varchar(255) NOT NULL,
      id INT NOT NULL,
      PRIMARY KEY (id)
    ) `;
      connection.query(sql, cb);
    },
    insertContentSourceCategories: ["createContentSourceCategory", (result, cb) => {
      let sql = `INSERT INTO content_source_categories (type, id) VALUES
      ('Learning Management System', 1),
      ('Content Management System', 2),
      ('Collaboration', 3),
      ('Cloud Storage', 4),
      ('CRM and Support', 5),
      ('Others', 7),
      ('Search Engine', 6)
      ON DUPLICATE KEY UPDATE
      type = VALUES(type),
      id = VALUES(id)`;
      connection.query(sql, cb);
    }],
    /*
      content source category
    */
    createAddons: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS addons (
        id int(20) NOT NULL AUTO_INCREMENT,
        name varchar(250) DEFAULT NULL,
        label varchar(250) NOT NULL,
        is_download tinyint(1) NOT NULL DEFAULT '0',
        image_path text,
        description text,
        PRIMARY KEY (id)
      ) `;
      connection.query(sql, cb);
    },
    insertAddons: ["createAddons", (result, cb) => {
      let sql = `INSERT INTO addons (id, name, label, is_download, image_path, description) VALUES
      (1, 'onPremises', 'On Premises', 1, 'assets/img/onPremiseAddon-new.svg', 'Keep your data and SearchUnify search client on your own servers.'),
      (2, 'How to', 'How to', 0, NULL, NULL),
      (3, 'semanticSearch', 'Semantic Search', 0, NULL, NULL),
      (4, 'Spell Checker', 'Spell Checker', 0, NULL, NULL),
      (5, 'Index Cleaner', 'Index Cleaner', 0, NULL, NULL),
      (6, 'Tableau Connector', 'Tableau Connector', 0, NULL, NULL),
      (7, 'Case Deflection', 'Case Deflection', 0, 'assets/img/case-deflection-new.svg', 'Quantify your self-service efforts by measuring case deflection.'),
      (8, 'Email Tracking', 'Email Tracking', 0, 'assets/img/email-tracking-new.svg', 'Map session IDs with user emails. After installation, go to Search Clients.'),
      (9, 'Virtual Agent', 'Virtual Agent', 0, 'assets/img/chat-bot-new.svg', 'AI-powered chatbot to handle most frequent real-time customer queries.'),
      (10, 'Agent Helper','Agent Helper',0,'assets/img/agent-helper-new.svg','Help agents solve cases faster by automating research. It rummages through.'),
      (11, 'Duplicacy Checker','Duplicacy Checker',0,'assets/img/duplicacy-checker-new.svg','Help agents solve cases faster by automating research. It rummages through.'),
      (12, 'KCS<sup>&reg;</sup> Enabler','KCS<sup>&reg;</sup> Enabler',0,'assets/img/KCS-new.svg','Automate knowledge base article writing for support agents working in.'),
      (13, 'Community Helper', 'Community Helper', 0, 'assets/img/community-helper-addon.svg', 'Maintain a high engagement rate for new posts. Answer all new threads on.')
      ON DUPLICATE KEY UPDATE
      id = VALUES(id),
      name = VALUES(name),
      label = VALUES(label),
      is_download = VALUES(is_download),
      image_path = VALUES(image_path),
      description = VALUES(description)`;
      connection.query(sql, cb);
    }],
    createAddonsStatus: ["createAddons", (result, cb) => {
      var sql = `CREATE TABLE IF NOT EXISTS addons_status (
        id int(20) NOT NULL AUTO_INCREMENT,
        is_installed tinyint(4) NOT NULL DEFAULT '0',
        installed_on timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        installed_by varchar(125) NOT NULL,
        is_enabled tinyint(4) NOT NULL DEFAULT '0',
        addon_id int(20) NOT NULL,
        PRIMARY KEY (id),
        FOREIGN KEY (addon_id) REFERENCES addons(id) ON DELETE CASCADE ON UPDATE CASCADE
      ) `;
      connection.query(sql, cb);
    }],
    createplaces: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS places (
        id int(20) NOT NULL AUTO_INCREMENT,
        type varchar(250) NOT NULL,
        path varchar(250) NOT NULL,
        place_name varchar(250) NOT NULL,
        PRIMARY KEY (id)
      )`;
      connection.query(sql, cb);
    },
    insertPlaces: ["createplaces", (result, cb) => {
      var sql = `INSERT INTO places (id, type, path, place_name) VALUES
      (1, 'Analytics', 'dashboard/analytics', 'Analytics Chart'),
      (2, 'Plugins', '/dashboard/addons', 'Edit View'),
      (3, 'Tab', '/dashboard/addons', 'Sidebar Tab'),
      (4,'Search Client Helper' , '--' ,'Search Client Helper')
      ON DUPLICATE KEY UPDATE
      type = VALUES(type),
      path = VALUES(path),
      place_name = VALUES(place_name)`;
      connection.query(sql, cb);
    }],
    createAddonsPlaces: ["createAddons", "createplaces", (result, cb) => {
      var sql = `CREATE TABLE IF NOT EXISTS addon_places (
        id int(20) NOT NULL AUTO_INCREMENT,
        addon_id int(11) NOT NULL,
        place_id int(250) NOT NULL,
        PRIMARY KEY (id),
        FOREIGN KEY (addon_id) REFERENCES addons(id) ON DELETE CASCADE ON UPDATE CASCADE,
        FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE ON UPDATE CASCADE,
        UNIQUE (addon_id)
      )`;
      connection.query(sql, cb);
    }],
    insertAddonsPlaces: ["createAddonsPlaces", (result, cb) => {
      var sql = `INSERT INTO addon_places (id, addon_id, place_id) VALUES
      (1, 2, 1),
      (2, 1, 2),
      (3, 7, 2),
      (4, 8, 2),
      (5, 9, 3),
      (6,10,4)
      ON DUPLICATE KEY UPDATE
      addon_id = VALUES(addon_id),
      place_id = VALUES(place_id)`;
      connection.query(sql, cb);
    }],
    addColumnFolderType: cb => {
      var sql1 = "ALTER TABLE `content_source_spaces` ADD `folderType` VARCHAR(1000) NULL;";
      connection.query(sql1, function (err, data) {
        if (err && err.code !== "ER_DUP_FIELDNAME") {
          console.error(err);
          cb(new Error(err));
        }
        else
          cb();
      });
    },
    addColumnautosuggest: cb => {
      var sql1 = "ALTER TABLE `search_client_metadata_fields` ADD `autosuggestField` INT(11) NOT NULL AFTER `priority`";
      connection.query(sql1, function (err, data) {
        if (err && err.code !== "ER_DUP_FIELDNAME") {
          console.error(err);
          cb(new Error(err));
        }
        else
          cb();
      });
    },
    addColumnmetaField: cb => {
      var sql1 = "ALTER TABLE `search_client_metadata_fields` ADD `metaData` INT(11) NOT NULL AFTER `autosuggestField`";
      connection.query(sql1, function (err, data) {
        if (err && err.code !== "ER_DUP_FIELDNAME") {
          console.error(err);
          cb(new Error(err));
        }
        else
          cb();
      });
    },
    advertisement_templates: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `advertisement_templates` ("
        + "`id` int(11) NOT NULL,"
        + "`search_client_id` int(11) NOT NULL,"
        + "`phrase` varchar(255) NOT NULL,"
        + "`advertisementHtml` longtext NOT NULL"
        + ") ENGINE=InnoDB DEFAULT CHARSET=latin1;";
      connection.query(sql, cb);
    },
    advertisement_templates_pk: ['advertisement_templates', (results, cb) => {
      var sql = "ALTER TABLE `advertisement_templates`"
        + "ADD PRIMARY KEY (`search_client_id`,`phrase`),"
        + "ADD UNIQUE KEY `id` (`id`),"
        + "ADD KEY `search_template` (`search_client_id`);";
      connection.query(sql, (err, result) => {
        cb();
      });
    }],
    advertisement_templates_auto_increment: ['advertisement_templates_pk', (results, cb) => {
      var sql = "ALTER TABLE `advertisement_templates`"
        + "MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;";
      connection.query(sql, cb);
    }],
    advertisement_templates_foreign_key: ['advertisement_templates_auto_increment', (results, cb) => {
      var sql = "ALTER TABLE `advertisement_templates`"
        + "ADD CONSTRAINT `search_template` FOREIGN KEY (`search_client_id`) REFERENCES `search_clients` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;";
      connection.query(sql, (err, result) => {
        cb();
      });
    }],
    security_permissions: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `security_permissions` ("
        + " `id` int(11) NOT NULL AUTO_INCREMENT,"
        + " `permission_name` varchar(254) DEFAULT NULL,"
        + " PRIMARY KEY (`id`))";
      connection.query(sql, cb);
    },
    insert_security_permission: ['security_permissions', (results, cb) => {
      let sql = `INSERT INTO security_permissions (id, permission_name) VALUES
      (4, 'Content Sources And Search Clients'),
      (5, 'Status Page'),
      (6, 'Authentication'),
      (7, 'Search Endpoints'),
      (8, 'Tracking API'),
      (9, 'Search Tuning'),
      (10, 'Email Notification'),
      (11, 'Profiles'),
      (12, 'Tickets'),
      (13, 'Auth Clients'),
      (14, 'Analytics Admin Panel'),
      (15, 'Analytics API'),
      (16, 'Search API'),
      (17, 'Analytics V-2'),
      (18, 'Security Admin Panel')
      ON DUPLICATE KEY UPDATE
      permission_name = VALUES(permission_name)`;
      connection.query(sql, cb);
    }],
    security_ip_groups: ['insert_security_permission', (results, cb) => {
      var sql = "CREATE TABLE IF NOT EXISTS `security_ip_groups` ("
        + " `id` int(11) NOT NULL AUTO_INCREMENT,"
        + " `permission_id` INT(11) DEFAULT NULL,"
        + " `ip` VARCHAR(254) DEFAULT NULL,"
        + " PRIMARY KEY (`id`),"
        + " KEY (`permission_id`),"
        + " FOREIGN KEY (`permission_id`) REFERENCES `security_permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)";
      connection.query(sql, cb);
    }],
    security_api_groups: ['security_ip_groups', (results, cb) => {
      var sql = "CREATE TABLE IF NOT EXISTS `security_api_groups` ("
        + " `id` int(11) NOT NULL AUTO_INCREMENT,"
        + " `permission_id` INT(11) DEFAULT NULL,"
        + " `url` VARCHAR(254) DEFAULT NULL,"
        + " PRIMARY KEY (`id`),"
        + " KEY (`permission_id`),"
        + " FOREIGN KEY (`permission_id`) REFERENCES `security_permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)";
      connection.query(sql, cb);
    }],
    insert_security_api_groups: ['security_api_groups', (results, cb) => {
      var sql = `INSERT INTO security_api_groups (id, permission_id, url) VALUES
      (15, 4, '/admin/contentSources/getAllSupportedContentSourceTypes'),
      (16, 4, '/admin/contentSources/getAddedContentSources'),
      (17, 4, '/admin/contentSources/getContentSourcesAuthData'),
      (18, 4, '/admin/contentSources/getFoldersId'),
      (19, 4, '/admin/contentSources/addContentSource'),
      (20, 4, '/admin/contentSources/deleteContentSource'),
      (21, 4, '/admin/contentSources/deleteObject'),
      (22, 4, '/admin/contentSources/deleteField'),
      (23, 4, '/admin/contentSources/getPlaces'),
      (24, 4, '/admin/contentSources/crawlData'),
      (25, 4, '/admin/contentSources/stopCrawler'),
      (26, 4, '/admin/contentSources/fetchObjects'),
      (27, 4, '/admin/contentSources/getGitInfo'),
      (170, 4, '/admin/contentSources/getChildFolders'),
      (28, 4, '/admin/authorization/oAuthCallback'),
      (29, 4, '/admin/authorization/oAuthCallbackJira'),
      (30, 4, '/admin/authorization/oAuthCallbackConfluence'),
      (36, 4, '/admin/searchClient/deletePlatform'),
      (37, 4, '/admin/searchClient/getSearchClient'),
      (38, 4, '/admin/searchClient/updateSearchClient'),
      (39, 4, '/admin/searchClient/addPlatform'),
      (40, 4, '/admin/searchClient/getPlatforms'),
      (41, 4, '/admin/searchClient/getContentTypes'),
      (42, 4, '/admin/searchClient/getFiltersPriority'),
      (43, 4, '/admin/searchClient/saveFiltersPriority'),
      (44, 4, '/admin/searchClient/downloadPlatform'),
      (45, 4, '/admin/searchClient/saveCss'),
      (46, 4, '/admin/searchClient/editClient'),
      (47, 4, '/admin/searchClient/cloneSearchClient'),
      (53, 4, '/admin/box/getChildFolders'),
      (54, 4, '/admin/dropbox/getChildFolders'),
      (55, 5, '/statusPage'),
      (56, 6, '/oauth/token'),
      (57, 6, '/authorise'),
      (58, 6, '/authorise_success'),
      (59, 6, '/logout'),
      (60, 6, '/signup'),
      (61, 6, '/login'),
      (62, 6, '/registerEmail'),
      (63, 6, '/getAutoProvisionToken'),
      (64, 7, '/search/searchResultByGet'),
      (65, 7, '/search/searchResultByPost'),
      (66, 8, '/analytics/track.png'),
      (67, 9, '/tuning/contentTuning/getAllContent'),
      (68, 9, '/tuning/contentTuning/changeTypeBoosting'),
      (69, 9, '/tuning/contentTuning/changeFieldBoosting'),
      (70, 9, '/tuning/contentTuning/custom-boosting'),
      (71, 9, '/tuning/searchtuning/saveTuningData'),
      (72, 9, '/tuning/searchtuning/getTuningData'),
      (73, 9, '/tuning/searchtuning/deleteTuningData'),
      (74, 9, '/tuning/searchtuning/getUnrankedSearchResults'),
      (75, 10, '/admin/notifications/getAllNotificationPreferences'),
      (76, 10, '/admin/notifications/saveNotificationPreferences'),
      (77, 10, '/admin/notifications/deleteNotificationPreferences'),
      (78, 10, '/admin/notifications/sendNotifications'),
      (79, 10, '/admin/notifications/sendVersionMail'),
      (80, 10, '/admin/notifications/sendReport'),
      (81, 11, '/statusPage'),
      (82, 11, '/admin/userManagement/getAllUsers'),
      (83, 11, '/admin/userManagement/editUser'),
      (84, 11, '/admin/userManagement/manageAccount'),
      (85, 11, '/admin/userManagement/changePassword'),
      (86, 12, '/admin/support/getAllTickets'),
      (87, 12, '/admin/support/editTicket'),
      (88, 12, '/admin/version/getVersions'),
      (89, 13, '/oauthClients/saveOauthClients'),
      (90, 13, '/oauthClients/deleteOauthClients'),
      (91, 13, '/oauthClients/getOauthClients'),
      (92, 13, '/oauthClients/getOauthScopes'),
      (93, 14, '/analytics/an/mytrail'),
      (94, 14, '/analytics/missedQueryHistogram'),
      (95, 14, '/analytics/missedQueryQueries'),
      (96, 14, '/analytics/SearchHistogram'),
      (97, 14, '/analytics/queriesCount'),
      (98, 14, '/analytics/getConversions'),
      (99, 14, '/searchClientAnalytics/getTypesStatistics'),
      (100, 14, '/analytics/getGeoReport'),
      (101, 14, '/analytics/getTileData'),
      (102, 14, '/analytics/getRecentSearch'),
      (103, 14, '/analytics/getLonelyDoc'),
      (104, 14, '/searchClientAnalytics/getAddedContent'),
      (105, 14, '/analytics/readyToBecomeHelpArticle'),
      (106, 14, '/analytics/getFunnel'),
      (107, 14, '/analytics/sa/getSessionReports'),
      (108, 14, '/analytics/sa/trackSession'),
      (109, 14, '/analytics/sa/getTopClickedResults'),
      (110, 14, '/analytics/sa/getSessionsActivityDetails'),
      (111, 14, '/analytics/sa/exploreSession'),
      (112, 14, '/analytics/sa/exploreSearchText'),
      (113, 14, '/analytics/sa/exploreSiteVisitSession'),
      (114, 14, '/analytics/sa/getFilterBasedSearchChart'),
      (115, 14, '/searchClientAnalytics/getAllSearchPlatforms'),
      (116, 14, '/analytics/sa/getPlatformSearchChart'),
      (117, 14, '/analytics/sa/getDomainSpecificVisitors'),
      (118, 14, '/analytics/sa/getDomainSpecificSearchedText'),
      (119, 14, '/analytics/sa/getConversionDetails'),
      (120, 14, '/analytics/sa/getTopBackwardDocuments'),
      (121, 14, '/analytics/sa/getTopSearchsWithNoClicks'),
      (122, 14, '/analytics/sa/getDocumentsWithLargeContent'),
      (123, 14, '/analytics/sa/getDocumentsWithLargeContent'),
      (124, 14, '/analytics/sa/getContentSourceList'),
      (125, 14, '/analytics/sa/getSessionChartDetails'),
      (126, 14, '/analytics/sa/getSessionChartSearches'),
      (127, 14, '/analytics/sa/getSessionChartPageViews'),
      (128, 14, '/analytics/sa/getFiltersForQueries'),
      (129, 14, '/searchClientAnalytics/getActiveReportsInfo'),
      (130, 15, '/api/v2/searchQuery/all'),
      (131, 15, '/api/v2/searchQuery/withResults'),
      (132, 15, '/api/v2/searchQuery/withoutResults'),
      (133, 15, '/api/v2/searchQuery/withNoClicks'),
      (134, 15, '/api/v2/searchQuery/bySessionId'),
      (135, 15, '/api/v2/searchConversion/all'),
      (136, 15, '/api/v2/searchConversion/discussionsReadyToBecomeArticles'),
      (137, 15, '/api/v2/searchConversion/notOnFirstPage'),
      (138, 15, '/api/v2/searchConversion/bySessionId'),
      (139, 15, '/api/v2/searchSession/all'),
      (140, 15, '/api/v2/searchSession/all/searchQuery'),
      (141, 15, '/api/v2/searchSession/all/searchConversion'),
      (142, 15, '/api/v2/searchSession/bySearchSessionId'),
      (143, 15, '/api/v2/searchSession/bySearchSessionId'),
      (144, 15, '/api/v2/searchSession/bySearchSessionId'),
      (145, 15, '/api/v2/searchSession/byCaseUid'),
      (146, 15, '/api/v2/searchSession/byCaseUid/searches'),
      (147, 15, '/api/v2/searchSession/byCaseUid/views'),
      (148, 16, '/api/v2_search/searchResults'),
      (150, 17, '/analytics/av-2/getTileData'),
      (151, 17, '/analytics/av-2/getSearchSummaryChart'),
      (152, 17, '/analytics/av-2/getUnsuccessfulSearchSummaryChart'),
      (153, 17, '/analytics/av-2/getUnsuccessfulSearchSessionSummaryChart'),
      (154, 17, '/analytics/av-2/getTopClickedSearches'),
      (155, 17, '/analytics/av-2/getTopSuccessfulSearches'),
      (156, 17, '/analytics/av-2/getTopSearches'),
      (157, 17, '/analytics/av-2/getTopSearchesWithNoClick'),
      (158, 17, '/analytics/av-2/getTopSearchesWithNoResult'),
      (159, 17, '/analytics/av-2/getTopSearchSessions'),
      (160, 17, '/analytics/av-2/getTopClickedResults'),
      (161, 17, '/analytics/av-2/getSearchesForTopClicked'),
      (162, 17, '/analytics/av-2/getNextSuccessfulSearches'),
      (163, 18, '/security/get_security_permissions'),
      (164, 18, '/security/get_security_groups'),
      (166, 17, '/analytics/av-2/getSearchCaseFormPageReport'),
      (167, 15, '/api/v2/searchConversion/caseFormPageReport')
      ON DUPLICATE KEY UPDATE
      permission_id = VALUES(permission_id),
      url = VALUES(url)`;
      connection.query(sql, cb);
    }],
    hostedSearchUsers: cb => {
      let sql = `CREATE TABLE IF NOT EXISTS hosted_search_users (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(256) NOT NULL,
        email varchar(256) NOT NULL,
        status tinyint(4) NOT NULL DEFAULT '1',
        PRIMARY KEY (id),
        UNIQUE (email)
      )`;
      connection.query(sql, cb);
    },
    samlAuth: cb => {
      let sql = `CREATE TABLE IF NOT EXISTS saml_auth (
        id int(11) NOT NULL AUTO_INCREMENT,
        idp_display_name varchar(256) NOT NULL,
        idp_identifier varchar(256) NOT NULL,
        idp_entity_id varchar(256) NOT NULL,
        certificate text NOT NULL,
        saml_sso_url varchar(256) NOT NULL,
        saml_logout_url varchar(256) NOT NULL,
        idp_type tinyint(4) NOT NULL,
        uid varchar(256) DEFAULT NULL,
        PRIMARY KEY (id)
      )`;
      connection.query(sql, cb);
    },
    page_rating: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `page_rating` ("
        + "`id` int(11) NOT NULL AUTO_INCREMENT,"
        + "`name` varchar(254) DEFAULT NULL,"
        + "`regex` varchar(254) DEFAULT NULL,"
        + "`search_client_id` int(11) NOT NULL,"
        + "`search_client_uid` varchar(127) NOT NULL,"
        + " PRIMARY KEY (`id`),"
        + " KEY (`search_client_id`),"
        + " FOREIGN KEY (`search_client_id`) REFERENCES `search_clients` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)";
      connection.query(sql, cb);
    },
    page_rating_instance: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `page_rating_instance` ("
        + "`id` int(11) NOT NULL AUTO_INCREMENT,"
        + "`instance_name` varchar(254) DEFAULT NULL,"
        + "`instance_regex` varchar(254) DEFAULT NULL,"
        + "`search_client_id` int(11) NOT NULL,"
        + "`search_client_uid` varchar(127) NOT NULL,"
        + " PRIMARY KEY (`id`),"
        + " KEY (`search_client_id`),"
        + " FOREIGN KEY (`search_client_id`) REFERENCES `search_clients` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)";
      connection.query(sql, cb);
    },
    suggestions_removed: cb => {
      let sql = `CREATE TABLE IF NOT EXISTS suggestions_removed (
        id int(11) NOT NULL AUTO_INCREMENT,
        search_client_id int(11) NOT NULL,
        text varchar(256) NOT NULL,
        PRIMARY KEY (id)
      )`;
      connection.query(sql, cb);
    },
    custom_boosting: cb => {
      let sql_custom = "CREATE TABLE IF NOT EXISTS `custom_boosting` ("
        + "`id` int(11) NOT NULL AUTO_INCREMENT,"
        + "`title_boosting_factor` varchar(255) NOT NULL DEFAULT '0',"
        + "`solved_boosting_factor` varchar(255) NOT NULL DEFAULT '0',"
        + "`click_boosting_base` varchar(255) NOT NULL DEFAULT '0',"
        + "`offset` varchar(255) NOT NULL DEFAULT '0',"
        + "`scale` varchar(255) NOT NULL DEFAULT '0',"
        + "`decay_rate` varchar(255) NOT NULL DEFAULT '0',"
        + "`search_client_id` int(11) NOT NULL,"
        + " PRIMARY KEY (`id`),"
        + " KEY (`search_client_id`),"
        + " FOREIGN KEY (`search_client_id`) REFERENCES `search_clients` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)";
      connection.query(sql_custom, cb);
    },
    auto_tuning_settings: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `auto_tuning_settings` ("
        + "id bigint(11) NOT NULL AUTO_INCREMENT,"
        + "auto_tuning_configuration_type tinyint(11) NOT NULL DEFAULT '0' COMMENT '0 - For Auto Spell Corrector 1 - For Facet Interpreter',"
        + "content_source_id int(11) NOT NULL,"
        + "content_source_object_id int(11) NOT NULL,"
        + "content_source_object_field_id int(11) NOT NULL,"
        + "search_client_id int(11) NOT NULL,"
        + "is_selected int(11) NOT NULL DEFAULT 0,"
        + "PRIMARY KEY (id),"
        + " KEY (`search_client_id`),"
        + " KEY (`content_source_id`),"
        + " KEY (`content_source_object_id`),"
        + " KEY (`content_source_object_field_id`),"
        + " FOREIGN KEY (`search_client_id`) REFERENCES `search_clients` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, "
        + " FOREIGN KEY (`content_source_id`) REFERENCES `content_sources` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, "
        + " FOREIGN KEY (`content_source_object_id`) REFERENCES `content_source_objects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, "
        + " FOREIGN KEY (`content_source_object_field_id`) REFERENCES `content_source_object_fields` (`id`) ON DELETE CASCADE ON UPDATE CASCADE)";
      connection.query(sql, (e, r) => {
        var sql = "ALTER TABLE `auto_tuning_settings` ADD COLUMN is_selected int(11) NOT NULL DEFAULT 0";
        connection.query(sql, cb);
      });
      //connection.query(sql, cb);
    },
    email_notification_contentsources: cb => {
      let sql = `CREATE TABLE IF NOT EXISTS email_notification_contentsources (
        id int(11) NOT NULL AUTO_INCREMENT,
        subscriberEmails varchar(255) NOT NULL,
        contentSources varchar(255) NOT NULL,
        PRIMARY KEY (id)
      )`;
      connection.query(sql, cb);
    },
    csm: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `csm` ("
        + "id int(11) NOT NULL AUTO_INCREMENT,"
        + "name varchar(254) DEFAULT NULL,"
        + "email varchar(254) DEFAULT NULL,"
        + "subject text DEFAULT NULL,"
        + "message text DEFAULT NULL,"
        + "messagesReadByUsers varchar(254) DEFAULT NULL,"
        + "created_date timestamp DEFAULT CURRENT_TIMESTAMP,"
        + "PRIMARY KEY (id))";
      connection.query(sql, cb);
    },
    recommendations: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `recommendations` ("
        + "id int(11) NOT NULL AUTO_INCREMENT,"
        + "name varchar(254) DEFAULT NULL,"
        + "email varchar(254) DEFAULT NULL,"
        + "title text DEFAULT NULL,"
        + "description text DEFAULT NULL,"
        + "created_date timestamp DEFAULT CURRENT_TIMESTAMP,"
        + "PRIMARY KEY (id))";
      connection.query(sql, cb);
    },
    create_train_dictionay: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `dym_train_dictionary` ("
        + "id int(11) NOT NULL,"
        + "is_enable tinyint(4) NOT NULL DEFAULT 0,"
        + "last_train_date datetime, "
        + "pid int(11) NOT NULL DEFAULT 0, "
        + "PRIMARY KEY (id));"
      // connection.query(sql, (e,r)=>{
      //   var sql = "ALTER TABLE `dym_train_dictionary` ADD COLUMN pid int(11) NOT NULL DEFAULT 0";
      connection.query(sql, cb);
      //});
    },
    insert_train_dictionay: ['create_train_dictionay', (data, cb) => {
      var sql = "INSERT IGNORE INTO dym_train_dictionary (id, is_enable, last_train_date, pid) "
        + "VALUES (0, 0, NULL, 0) "
      connection.query(sql, function (err, data) {
        if (err && err.code !== "ER_DUP_FIELDNAME") {
          commonFunctions.errorlogger.error(err);
          cb(null, null);
        }
      });
    }],
    insert_facet_interpreter: ['create_train_dictionay', (data, cb) => {
      var sql = "INSERT IGNORE INTO dym_train_dictionary (id, is_enable, last_train_date, pid) "
        + "VALUES (1, 0, NULL, 0) "
      connection.query(sql, function (err, data) {
        if (err && err.code !== "ER_DUP_FIELDNAME") {
          commonFunctions.errorlogger.error(err);
          cb(null, null);
        }
      });
    }],
    create_community_helper_bot: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS community_helper_bot (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(127) NOT NULL,
        search_client_id int(11) DEFAULT NULL,
        platform_type_id int(11) DEFAULT NULL,
        status tinyint(4) NOT NULL DEFAULT '0',
        is_connected tinyint(4) NOT NULL DEFAULT '0',
        community varchar(127) DEFAULT NULL,
        PRIMARY KEY (id),
        KEY community_helper_bot_k1 (search_client_id),
        KEY community_helper_bot_k2 (platform_type_id),
        CONSTRAINT community_helper_bot_k1 FOREIGN KEY (search_client_id) REFERENCES search_clients (id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT community_helper_bot_k2 FOREIGN KEY (platform_type_id) REFERENCES search_client_types (id) ON DELETE CASCADE ON UPDATE CASCADE
      )`;
      connection.query(sql, cb);
    },
    create_community_helper_auth: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS community_helper_auth (
        id int(11) NOT NULL AUTO_INCREMENT,
        community_helper_bot_id int(11) NOT NULL,
        authorization_type varchar(256) NOT NULL,
        client_secret varchar(127) DEFAULT NULL,
        client_id varchar(127) DEFAULT NULL,
        username varchar(127) DEFAULT NULL,
        password varchar(127) DEFAULT NULL,
        htaccessUsername varchar(127) DEFAULT NULL,
        htaccessPassword varchar(127) DEFAULT NULL,
        accessToken varchar(3000) DEFAULT NULL,
        refreshToken varchar(3000) DEFAULT NULL,
        tenantId varchar(127) DEFAULT NULL,
        instanceURL varchar(127) DEFAULT NULL,
        url varchar(255) DEFAULT NULL,
        PRIMARY KEY (id),
        KEY community_helper_auth_k1 (community_helper_bot_id),
        CONSTRAINT community_helper_auth_k1 FOREIGN KEY (community_helper_bot_id) REFERENCES community_helper_bot (id) ON DELETE CASCADE ON UPDATE CASCADE
      )`;
      connection.query(sql, cb);
    },
    create_community_helper_template: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS community_helper_template (
        id int(11) NOT NULL AUTO_INCREMENT,
        template_body longtext NOT NULL,
        response_type varchar(127) NOT NULL DEFAULT 'su_results' COMMENT 'su_results, custom',
        community_helper_bot_id int(11) NOT NULL,
        template_front longtext NOT NULL,
        template_name varchar(255) NOT NULL,
        created_date date NOT NULL,
        PRIMARY KEY (id)
      )`;
      connection.query(sql, cb);
    },
    create_community_helper_bot_responses: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS community_helper_bot_responses (
        id int(11) NOT NULL AUTO_INCREMENT,
        community_helper_bot_id int(11) NOT NULL,
        community_helper_template_id int(11) NOT NULL,
        response_freq_type varchar(127) NOT NULL,
        response_freq_time int(11) NOT NULL,
        PRIMARY KEY (id),
        KEY community_helper_bot_responses_k1 (community_helper_bot_id),
        KEY community_helper_bot_responses_k2 (community_helper_template_id),
        CONSTRAINT community_helper_bot_responses_k1 FOREIGN KEY (community_helper_bot_id) REFERENCES community_helper_bot (id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT community_helper_bot_responses_k2 FOREIGN KEY (community_helper_template_id) REFERENCES community_helper_template (id) ON DELETE CASCADE ON UPDATE CASCADE
      )`
      connection.query(sql, cb);
    },
    create_community_helper_objects: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS community_helper_to_search_client_objects (
        id int(11) NOT NULL AUTO_INCREMENT,
        search_client_id int(11) DEFAULT NULL,
        community_helper_bot_id int(11) NOT NULL,
        content_source_object_id int(11) NOT NULL,
        is_enabled tinyint(4) NOT NULL DEFAULT '0',
        PRIMARY KEY (id),
        KEY community_helper_to_search_client_objects_k1 (search_client_id),
        KEY community_helper_to_search_client_objects_k2 (content_source_object_id),
        KEY community_helper_to_search_client_objects_k3 (community_helper_bot_id),
        CONSTRAINT community_helper_to_search_client_objects_k1 FOREIGN KEY (search_client_id) REFERENCES search_clients (id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT community_helper_to_search_client_objects_k2 FOREIGN KEY (content_source_object_id) REFERENCES content_source_objects (id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT community_helper_to_search_client_objects_k3 FOREIGN KEY (community_helper_bot_id) REFERENCES community_helper_bot (id) ON DELETE CASCADE ON UPDATE CASCADE
      )`;
      connection.query(sql, cb);
    },
    kcs_configuration: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS kcs_configuration (
        id int(11) NOT NULL AUTO_INCREMENT,
        service_desk_name varchar(500) DEFAULT NULL,
        service_desk_uid varchar(500) DEFAULT NULL,
        search_client_id int(11) DEFAULT NULL,
        knowledge_platform_name varchar(500) DEFAULT NULL,
        knowledge_platform_type varchar(500) DEFAULT NULL,
        knowledge_platform_type_id int(11) DEFAULT NULL,
        mapping_status boolean Default	false,
        kcs_active boolean Default	false,
        author_name varchar(500) DEFAULT NULL,
        last_modified_date_time timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
      ) `;
      connection.query(sql, cb);
    },
    kcs_tools: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS kcs_tools (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(500) DEFAULT NULL,
        description varchar(500) DEFAULT NULL,
        image varchar(500) DEFAULT NULL,
        active boolean Default	false,
        PRIMARY KEY (id)
      ) `;
      connection.query(sql, cb);
    },
    inserKcsTools: cb => {
      var sql = `INSERT INTO kcs_tools
      VALUES (1, 'Salesforce', 'Salesforce', 'assets/img/KCS-Salesforce.svg', 1), (2, 'Zendesk', 'Zendesk', 'assets/img/KCS-Zendesk.svg', 1), (3, 'Service Now', 'Service Now', 'assets/img/KCS-Service-now.svg', 0); `;
      connection.query(sql, cb);
    },
    kcs_knowledge_management_systems: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS kcs_knowledge_management_systems (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(500) DEFAULT NULL,
        description varchar(500) DEFAULT NULL,
        image varchar(500) DEFAULT NULL,
        active boolean Default	false,
        cs_id int(11) NOT NULL,
        PRIMARY KEY (id)
      ) `;
      connection.query(sql, cb);
    },
    inserKcsKnowledgeManagementSystems: cb => {
      var sql = `INSERT INTO kcs_knowledge_management_systems
      VALUES (1, 'Salesforce', 'Salesforce', 'assets/img/KCS-Salesforce.svg', 1, 3), (2, 'Zendesk', 'Zendesk', 'assets/img/KCS-Zendesk.svg', 1, 7), (3, 'Service Now', 'Service Now', 'assets/img/KCS-Service-now.svg', 0, 21), (4, 'Mindtouch', 'Mindtouch', 'assets/img/KCS-Mindtouch.svg', 0, 11); `;
      connection.query(sql, cb);
    },
    deleteKcsReportInAnalyticsReports: cb => {
      var sql = `DELETE FROM analytics_reports 
      WHERE name = 'KCS report' OR name = 'KCS Articles Usage Analytics';`;
      connection.query(sql, cb);
    },
    tableKcsReport: cb => {
      let sql = `CREATE TABLE IF NOT EXISTS kcs_reports (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(500) NOT NULL,
        description varchar(1000) DEFAULT 'No description available',
        PRIMARY KEY (id)
      )`;
      connection.query(sql, cb);
    },
    inserKcsReports: cb => {
      var sql = `INSERT INTO kcs_reports
      VALUES (1, 'Overview', 'Overview Total KCS Articles'),(2, 'Support Effectiveness', 'Support Effectiveness'),(3, 'KCS Contributor Analytics', 'KCS Contributor Analytics'),(4, 'KCS Articles Usage Analytics', 'KCS Articles Usage Analytics'),(5, 'Least Used Articles', 'Least Used Articles')`;
      connection.query(sql, cb);
    },
    create_field_boosting_table: cb => {
      var sql = `CREATE TABLE IF NOT EXISTS field_boosting (id int(11) NOT NULL AUTO_INCREMENT,
      name varchar(127),
      content_source_object_field_id int(11) NOT NULL,
      search_client_id int(11) NOT NULL,
      boosting_factor varchar(255) NOT NULL default 0,
      PRIMARY KEY (id),
      FOREIGN KEY (search_client_id) REFERENCES search_clients (id) ON
      DELETE CASCADE ON UPDATE CASCADE 
      )`;
      connection.query(sql, cb);
    },
    usersConfigurations: cb => {
      let sql = `CREATE TABLE IF NOT EXISTS usersConfigurations (
        userId int(11), email varchar(256), CaseDeflectionPreSelectedSC varchar(256) Default NULL,
         ActionableInsightsPreSelectedSC varchar(256) Default NULL, 
         lightMode varchar(256) Default 'white',
         sideBarActive boolean Default	false,
        PRIMARY KEY (userId))`;
      connection.query(sql, cb);
    },
    create_crons_table: cb => {
      var sql = "CREATE TABLE IF NOT EXISTS `crons` ("
        + "id int(11) NOT NULL AUTO_INCREMENT,"
        + "name varchar(500) DEFAULT NULL,"
        + "execution_array varchar(500) DEFAULT NULL,"
        + "last_train_date datetime, "
        + "pid int(11) NOT NULL DEFAULT 0, "
        + "is_enable tinyint(4) NOT NULL DEFAULT 0,"
        + "logFile varchar(500) DEFAULT '',"
        + "PRIMARY KEY (id));"
      connection.query(sql, cb);
    },
    insert_crons_data: ['create_crons_table', (data, cb) => {
      var sql = `INSERT IGNORE INTO crons (id, name, execution_array, last_train_date, pid, is_enable, logFile) 
        VALUES (1, 'Spell Corrector', '[{"execute_by": "customPlugins/semanticSearch/cron/createDictionary.js", "execution_environment" : "admin"}, {"execute_by": "src/custom-plugins/spell-corrector.js", "execution_environment" : "analytics"}]', NULL, 0, 0, ''),
        (2, 'Facet Interpreter', '[{"execute_by": "customPlugins/semanticSearch/cron/facetInterpreter.js", "execution_environment" : "admin"}]', NULL, 0, 0, ''), 
        (3, 'Auto Boosting', '[{"execute_by": "src/custom-plugins/auto-boosting.js", "execution_environment" : "analytics"}]', NULL, 0, 0, ''), 
        (4, 'Similar Searches', '[{"execute_by": "src/custom-plugins/similar-search-recommendation.js", "execution_environment" : "analytics"}]', NULL, 0, 0, ''), 
        (5, 'Click Boosting', '[{"execute_by": "src/custom-plugins/click-boosting.js", "execution_environment" : "analytics"}]', NULL, 0, 0, ''), 
        (6, 'Attach To case Boosting', '[{"execute_by": "src/custom-plugins/attach-to-case-comment.js", "execution_environment" : "analytics"}]', NULL, 0, 0, '') 
        ON DUPLICATE KEY UPDATE 
        id = VALUES(id), 
        name = VALUES(name), 
        execution_array = VALUES(execution_array)`
      connection.query(sql, function (err, data) {
        if (err && err.code !== "ER_DUP_FIELDNAME") {
          commonFunctions.errorlogger.error(err);
          cb(null, null);
        }
      });
    }],
    addSynonymKeywordBoosting: cb => {
      let sql = "ALTER TABLE `search_clients` ADD COLUMN `synonymKeywordBoosting` BOOLEAN default FALSE";
      sqlWriteIgnoreError(sql, "editing column", cb);
    },
    addTimezoneColumn: (cb) => {
      let sql = "ALTER TABLE `user` ADD COLUMN `timezone` VARCHAR(250) default 'UTC'";
      sqlWriteIgnoreError(sql, "editing column", cb);
    },
    deleteEPfromAddon: (cb) => {
      let sql = "delete from addons where id = 15;";
      sqlWriteIgnoreError(sql, "editing column", cb);
    },
    updateCronsStatusOnInit: (cb) => {
      let sql = "update crons set pid=0;";
      sqlWriteIgnoreError(sql, "resetting crons pid", cb);
    },
    updatePageRatingName: (cb) => {
      let sql = `update
        search_client_to_analytics_report
      set
        label = 'Content Experience Feedback'
      where
        label = 'Page Rating Feedback';`
      sqlWriteIgnoreError(sql, "page rating report name change", cb);
    }
  }, (error, results) => {
    if (error)
      commonFunctions.errorlogger.error("Not working");
    else
      commonFunctions.errorlogger.info("async auto sql queries done");
  });

  var alter_train_dictionay = "ALTER TABLE `dym_train_dictionary` ADD COLUMN pid int(11) NOT NULL DEFAULT 0";
  connection.query(alter_train_dictionay, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var sql1 = "ALTER TABLE `content_source_spaces` ADD `spaceType` VARCHAR(127) DEFAULT NULL";
  connection.query(sql1, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `directly_viewed_results` BOOLEAN NOT NULL DEFAULT FALSE;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })

  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `viewed_results` TEXT NOT NULL AFTER `directly_viewed_results`;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })
  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `cumulative` BOOLEAN NOT NULL AFTER `viewed_results`;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })
  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `stage1` BOOLEAN NOT NULL AFTER `cumulative`;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })
  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `user_entitlements` BOOLEAN NOT NULL AFTER `stage1`;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })

  var deflectionFormulas = "ALTER TABLE  `deflection_formula` ADD  `session_idle_timeout` INT NOT NULL DEFAULT  '60';"
  connection.query(deflectionFormulas, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })

  deflectionFormulas = "ALTER TABLE  `deflection_formula` ADD  `support_url` VARCHAR(1000) DEFAULT '';"
  connection.query(deflectionFormulas, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `email_tracking_enabled` BOOLEAN NULL DEFAULT FALSE AFTER `session_idle_timeout`;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })

  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `external_user_enabled` BOOLEAN NOT NULL DEFAULT FALSE AFTER `email_tracking_enabled`;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })

  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `account_name` TEXT NOT NULL AFTER `external_user_enabled`;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })

  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `is_by_su_user` BOOLEAN NOT NULL DEFAULT FALSE AFTER `account_name`;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  })

  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `tracking_only_logged_users_enabled` BOOLEAN NOT NULL DEFAULT FALSE AFTER `external_user_enabled`;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })

  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `kcs_analytics_enabled` BOOLEAN NOT NULL DEFAULT FALSE AFTER `user_entitlements`;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })
  
  var deflectionFormula = "ALTER TABLE `deflection_formula` ADD `kcs_service_desk_uid` TEXT NOT NULL AFTER `kcs_analytics_enabled`;"
  connection.query(deflectionFormula, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  })

  var sql1 = "ALTER TABLE `user` ADD `is_federated` TINYINT NULL DEFAULT '0' AFTER `forget_password_token`";
  connection.query(sql1, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var boost = "ALTER TABLE `search_clients_to_content_objects` ADD `boosting_factor` FLOAT NULL AFTER `icon`;";
  connection.query(boost, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var customBoostingEnabled = `ALTER TABLE custom_boosting ADD custom_boosting_enabled BOOLEAN NOT NULL DEFAULT "0" AFTER id`
  connection.query(customBoostingEnabled, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  // var keyboost = "ALTER TABLE `content_source_objects` DROP `boosting_factor`; ";
  // connection.query(keyboost, function (err, data) {
  //   if (err && err.code !=="ER_DUP_FIELDNAME") {
  //     console.error(err);
  //   }
  // });
  var sql4 = "ALTER TABLE `keyword_boost` ADD `search_client_id` INT NOT NULL AFTER `auto_boosted`; ";
  connection.query(sql4, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var sql5 = "ALTER TABLE `keyword_boost` ADD `isBoostingEnabled` BOOLEAN default 1;";
  connection.query(sql5, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var sql_content = "ALTER TABLE `search_clients_to_content_objects` ADD `isContentBoostingEnabled` BOOLEAN default 0;";
  connection.query(sql_content, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  // var sql5 = "ALTER TABLE `keyword_boost` ADD INDEX(`search_client_id`); ";
  // connection.query(sql5, function (err, data) {
  //   if (err && err.code !=="ER_DUP_FIELDNAME") {
  //     console.error(err);
  //   }
  // });
  var sql6 = "ALTER TABLE `keyword_boost` ADD CONSTRAINT `search_client_id_fkeyc` FOREIGN KEY (`search_client_id`) REFERENCES `search_clients`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;";
  connection.query(sql6, function (err, data) {
    if (err) {
      commonFunctions.errorlogger.error("federated is already added");
    }
  });
  var sqlz = "ALTER TABLE `admin_reports_preferences` ADD `search_client_id` INT NULL AFTER `user_id`;";
  connection.query(sqlz, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var sqlx = "ALTER TABLE `admin_reports_preferences` ADD INDEX(`search_client_id`);";
  connection.query(sqlx, function (err, data) {
    if (err) {
      commonFunctions.errorlogger.error("federated is already added");
    }
  });
  var sql_deflection_unique = "ALTER TABLE `deflection_formula` ADD CONSTRAINT search_client_id_unique UNIQUE(`search_client_id`);";
  connection.query(sql_deflection_unique, function (err, data) {
    if (err) {
      commonFunctions.errorlogger.error("Unique constraint is already added");
    }
  });
  var sqly = "ALTER TABLE `admin_reports_preferences` ADD CONSTRAINT admin_reports_preferences_ibfk_3480 FOREIGN KEY (`search_client_id`) REFERENCES `search_clients`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;"
  connection.query(sqly, function (err, data) {
    if (err) {
      commonFunctions.errorlogger.error("federated is already added");
    }
  });
  var sql_1 = "ALTER TABLE `admin_reports_preferences` DROP FOREIGN KEY `admin_reports_preferences_ibfk_3`; ALTER TABLE `admin_reports_preferences` ADD CONSTRAINT `admin_reports_preferences_ibfk_3` FOREIGN KEY (`search_client_id`) REFERENCES `search_clients`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;"
  var sql_2 = "ALTER TABLE `admin_reports_preferences` ADD INDEX(`search_client_id`);"
  var sql_3 = "ALTER TABLE `admin_reports_preferences` CHANGE `search_client_id` `search_client_id` INT(11) NULL DEFAULT NULL;"
  connection.query(sql_1, function (err, data) {
    if (err) {
      commonFunctions.errorlogger.error("federated is already added");
    }
  });
  connection.query(sql_2, function (err, data) {
    if (err) {
      commonFunctions.errorlogger.error("federated is already added");
    }
  });
  connection.query(sql_3, function (err, data) {
    if (err) {
      commonFunctions.errorlogger.error("federated is already added");
    }
  });
  var sqlr = "ALTER TABLE addons ADD request_to_install TINYINT NOT NULL DEFAULT '0' AFTER description;"
  connection.query(sqlr, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var sqlScope = "INSERT INTO oauth_scopes (id, scope, is_default) VALUES (2, 'Search', '0') ON DUPLICATE KEY UPDATE id=2;"
  connection.query(sqlScope, function (err, data) {
    if (err) {
      commonFunctions.errorlogger.error("federated is already added");
    }
  });

  var sqlScopenew = "INSERT INTO `oauth_scopes`(`id`, `scope`, `is_default`) VALUES (3,'Content',0),(4,'Master',0)"
  connection.query(sqlScopenew, function (err, data) {
    if (err) {
      console.log("already in database ");
    }
  });

  var clickBoosting = "INSERT INTO `boosting_custom` (`id`, `description`, `value`) VALUES ('7', 'Click', '0') ON DUPLICATE KEY UPDATE id=7;"
  connection.query(clickBoosting, function (err, data) {
    if (err && err.code !== "ER_DUP_ENTRY") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var resultActions = "INSERT INTO `result_actions` (`result_action_id`,`search_client_type_id`,`name`,`information`) VALUES ('1', '7', 'Email',''),('2', '7', 'Case comment',''),('3', '7', 'Attach article to case','') ON DUPLICATE KEY UPDATE information=VALUES(information);"
  connection.query(resultActions, function (err, data) {
    if (err && err.code !== "ER_DUP_ENTRY") {
      console.error(err);
    }
  });

  var clickScorePath = "ALTER TABLE `content_sources` ADD `clickScorePath` VARCHAR(2000) NOT NULL AFTER `sync_frequency_name`;"
  connection.query(clickScorePath, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var clickBoostingScore = "INSERT INTO `boosting_custom` (`id`, `description`, `value`) VALUES ('8', 'Click Boosting base', '0') ON DUPLICATE KEY UPDATE id=8;"
  connection.query(clickBoostingScore, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var solvedBoosting = "INSERT INTO `boosting_custom` (`id`, `description`, `value`) VALUES ('9', 'Solved Boosting Enabled', '0') ON DUPLICATE KEY UPDATE id=9;"
  connection.query(solvedBoosting, function (err, data) {
    if (err && err.code !== "ER_DUP_ENTRY") {
      console.error(err);
    }
  });

  var titleBoosting = "INSERT INTO `boosting_custom` (`id`, `description`, `value`) VALUES ('10', 'Title Boosting Enabled', '0') ON DUPLICATE KEY UPDATE id=10;"
  connection.query(titleBoosting, function (err, data) {
    if (err && err.code !== "ER_DUP_ENTRY") {
      console.error(err);
    }
  });

  var dateBoostingEnabled = "INSERT INTO `boosting_custom` (`id`, `description`, `value`) VALUES ('11', 'Date Boosting Enabled', '0') ON DUPLICATE KEY UPDATE id=11;"
  connection.query(dateBoostingEnabled, function (err, data) {
    if (err && err.code !== "ER_DUP_ENTRY") {
      console.error(err);
    }
  });

  var keywordBoosting = "INSERT INTO `boosting_custom` (`id`, `description`, `value`) VALUES ('12', 'Keyword Boosting Enabled', '0') ON DUPLICATE KEY UPDATE id=12;"
  connection.query(keywordBoosting, function (err, data) {
    if (err && err.code !== "ER_DUP_ENTRY") {
      console.error(err);
    }
  });

  var contentBoosting = "INSERT INTO `boosting_custom` (`id`, `description`, `value`) VALUES ('13', 'Content Boosting Enabled', '0') ON DUPLICATE KEY UPDATE id=13;"
  connection.query(contentBoosting, function (err, data) {
    if (err && err.code !== "ER_DUP_ENTRY") {
      console.error(err);
    }
  });

  var customBoosting = "INSERT INTO `boosting_custom` (`id`, `description`, `value`) VALUES ('14', 'Custom Boosting Enabled', '0') ON DUPLICATE KEY UPDATE id=14;"
  connection.query(customBoosting, function (err, data) {
    if (err && err.code !== "ER_DUP_ENTRY") {
      console.error(err);
    }
  });

  var searchPriority = "ALTER TABLE `search_clients_filters` ADD `search_priority` INT NULL AFTER `exclude`;"
  connection.query(searchPriority, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var syncFrequencyColChange = "alter table content_sources change column sync_frequency sync_frequency float default null ;"
  connection.query(syncFrequencyColChange, function (err, data) {
    if (err) {
      commonFunctions.errorlogger.error("syncFrequencyColChange error ", err);
    }
  });



  var samlUpdate = "ALTER TABLE saml_auth ADD hosted_type TINYINT NULL DEFAULT '0' AFTER uid;"
  connection.query(samlUpdate, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var searchPriority = "ALTER TABLE `search_clients_filters` ADD `search_priority` INT NULL AFTER `exclude`;"
  connection.query(searchPriority, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var enumUpdate = "ALTER TABLE search_clients_filters CHANGE use_as use_as ENUM('Filter','Summary','Title','Tag','Search','SearchSummary','SearchFilter','SummaryFilter', 'Extra') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT 'Summary'"
  connection.query(enumUpdate, function (err, data) {
    if (err) {
      commonFunctions.errorlogger.error("Click boosting score is already added");
    }
  });
  var valueUpdate = `ALTER TABLE search_clients_filters ADD use_value TINYINT(2) NOT NULL DEFAULT '10';`
  connection.query(valueUpdate, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var addinSessions = `ALTER TABLE sessions ADD user_id VARCHAR(256) NULL AFTER data, ADD UNIQUE (user_id);`
  connection.query(addinSessions, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {

      commonFunctions.errorlogger.error(err);
    }
  });
  var facetInterpreter = `ALTER TABLE search_clients ADD facet_interpreter INT(2) NULL DEFAULT '0';`
  connection.query(facetInterpreter, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {

      commonFunctions.errorlogger.error(err);
    }
  });
  var recentResultToggle = `ALTER TABLE search_clients ADD recent_searches INT(2) NULL DEFAULT '0';`
  connection.query(recentResultToggle, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {

      console.error(err);
    }
  });
  var recentResultCount = `ALTER TABLE search_clients ADD recent_searches_count VARCHAR(2) NULL DEFAULT '0';`
  connection.query(recentResultCount, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {

      console.error(err);
    }
  });
  spellCheck = `ALTER TABLE search_clients ADD auto_spell_corrector INT(2) NULL DEFAULT '0';`
  connection.query(spellCheck, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {

      commonFunctions.errorlogger.error(err);
    }
  });

  var featuredSniipet = `ALTER TABLE search_clients ADD featured_snippet INT(2) NULL DEFAULT '0';`
  connection.query(featuredSniipet, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var kcsEnabled = `ALTER TABLE search_clients ADD kcsEnabled BOOLEAN NULL DEFAULT FALSE AFTER featured_snippet`
  connection.query(kcsEnabled, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var regex = `ALTER TABLE content_source_object_fields ADD regex TEXT NULL AFTER isMerged;`
  connection.query(regex, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var regex_value = `ALTER TABLE content_source_object_fields ADD regex_value TEXT NULL AFTER isMerged;`
  connection.query(regex_value, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var change_organization_type = "ALTER TABLE content_source_authorization CHANGE organization_user_type organization_user_type TINYINT(1) NULL DEFAULT '0';"
  connection.query(change_organization_type, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  insertLanguages();
  changeCollation();

  var objectPid = "ALTER TABLE content_source_objects ADD object_pid INT(11) DEFAULT NULL AFTER field_conditions;"
  connection.query(objectPid, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var objectStatus = "ALTER TABLE content_source_objects ADD object_status TEXT NULL AFTER object_pid; "
  connection.query(objectStatus, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  // for website to check is file uploaded
  var fileUploaded = `ALTER TABLE content_sources ADD isFileUploaded BOOLEAN DEFAULT FALSE AFTER url`
  connection.query(fileUploaded, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  // for showing crawling log file
  var filename = `ALTER TABLE content_sources ADD logFile VARCHAR(255) DEFAULT NULL AFTER searchunifyIndexUrl`;
  connection.query(filename, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var permissionByPass = `ALTER TABLE search_clients_to_content_objects ADD permission_by_pass TINYINT NOT NULL DEFAULT '0' AFTER boosting_factor`;
  connection.query(permissionByPass, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var updateIMG = `UPDATE content_source_types SET img = 'assets/img/API.svg' WHERE id = 22;`
  connection.query(updateIMG, function (err, data) {
    if (err && err.code != "ER_CANT_DROP_FIELD_OR_KEY") {
      console.error(err);
    }
  });

  var sessionTrackingOverview = `update analytics_cd_labels set tooltip = "Successful Searches Who didn't Visit Support" where tooltip = "Successful Searches Who did't Visit Support";
  update analytics_cd_labels set tooltip = "Number of sessions in which <br/>users didn't visit the search page." where tooltip =  "Number of sessions in which <br/>users did't visit the search page.";`
  connection.query(sessionTrackingOverview, function (err, data) {
    if (err && err.code != "ER_CANT_UPDATE_TOOLTIP") {
      console.error(err);
    }
  });

  var suKhoros = `UPDATE search_client_types SET name = 'Khoros' WHERE id = 2;`
  connection.query(suKhoros, function (err, data) {
    if (err && err.code != "ER_CANT_DROP_FIELD_OR_KEY") {
      console.error(err);
    }
  });

  var removeUniqueKey = `ALTER TABLE content_source_spaces DROP INDEX spaceId`
  connection.query(removeUniqueKey, function (err, data) {
    if (err && err.code != "ER_CANT_DROP_FIELD_OR_KEY") {
      console.error(err);
    }
  });

  var didYouMeanField = `ALTER TABLE content_source_object_fields ADD did_you_mean TINYINT(1) NOT NULL DefAULT '0' AFTER regex_value`
  connection.query(didYouMeanField, function (err, data) {
    if (err && err.code != "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var template = "INSERT INTO template_types (id, name, label, description, folderPath) VALUES ('3', 'Ethereal', 'Ethereal', 'Ethereal', 'templates/Ethereal') ON DUPLICATE KEY UPDATE id=3;"
  connection.query(template, function (err, data) {
    if (err) {
      console.log("Template is already added");
    }
  });
  var renameSynergy = "UPDATE `template_types` SET `name` = 'Impavid', `label` = 'Impavid', `description` = 'Impavid', `folderPath` = 'templates/Impavid.html' WHERE `template_types`.`id` = 1;"
  connection.query(renameSynergy, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var jiveCS = "UPDATE `content_source_types` SET `description`='World''s most adopted intranet with multiple tools for collaboration.', `img`='assets/img/jive.svg',`category`=3, `url`='https://docs.searchunify.com/Content/Content-Sources/Jive.htm' WHERE `id`=1;"
  connection.query(jiveCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var lithiumCS = "UPDATE `content_source_types` SET `name`='Khoros',`description`='(Formerly Lithium) Brand communities where customers, fans and companies can help one another.', `img`='assets/img/khoros.svg',`category`=5, `url`='https://docs.searchunify.com/Content/Content-Sources/Lithium.htm' WHERE `id`=2;"
  connection.query(lithiumCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var salesforceCS = "UPDATE `content_source_types` SET `description`='Cloud-based platform with dedicated services for CRM, support, knowledge base and others.', `img`='assets/img/salesforce-small.svg',`category`=5, `url`='https://docs.searchunify.com/Content/Content-Sources/Salesforce.htm' WHERE `id`=3;"
  connection.query(salesforceCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var confluenceCS = "UPDATE `content_source_types` SET `description`='Powerful content collaboration tool from Atlassian.', `img`='assets/img/confluence.svg',`category`=2, `url`='https://docs.searchunify.com/Content/Content-Sources/Confluence.htm' WHERE `id`=4;"
  connection.query(confluenceCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var sharePointCS = "UPDATE `content_source_types` SET `description`='Microsoft''s collaborative server environment for company intranets.', `img`='assets/img/sharePoint.svg', `category`=3, `url`='https://docs.searchunify.com/Content/Content-Sources/Sharepoint.htm' WHERE `id`=5;"
  connection.query(sharePointCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var jiraCS = "UPDATE `content_source_types` SET `description`='Project management tool for agile teams.', `img`='assets/img/jira.svg', `category`=3, `url`='https://docs.searchunify.com/Content/Content-Sources/Jira.htm' WHERE `id`=6;"
  connection.query(jiraCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var zendeskCS = "UPDATE `content_source_types` SET `description`='Full service support and sales suite.', `img`='assets/img/zendesk.svg', `category`=5, `url`='https://docs.searchunify.com/Content/Content-Sources/Zendesk.htm' WHERE `id`=7;"
  connection.query(zendeskCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var slackCS = "UPDATE `content_source_types` SET `description`='Workplace communication tool with powerful add-ins.', `img`='assets/img/slack.svg',`category`=3, `url`='https://docs.searchunify.com/Content/Content-Sources/Slack.htm' WHERE `id`=8;"
  connection.query(slackCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var webCS = "UPDATE `content_source_types` SET `description`='Any open platform on the website.', `img`='assets/img/website.svg',`category`=7, `url`='https://docs.searchunify.com/Content/Content-Sources/Website.htm' WHERE `id`=9;"
  connection.query(webCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var madcapCS = "UPDATE `content_source_types` SET `name`='MadCap Flare', `description`='Help authoring and single-sourcing tool for technical writers.', `img`='assets/img/madcap.svg',`category`=2, `url`='https://docs.searchunify.com/Content/Content-Sources/MadCap-Flare.htm' WHERE `id`=10;"
  connection.query(madcapCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var mindtouchCS = "UPDATE `content_source_types` SET `description`='Knowledge-management platform with a focus on customization and self-service experience.', `img`='assets/img/mindtouch.svg',`category`=2, `url`='https://docs.searchunify.com/Content/Content-Sources/Mindtouch.htm' WHERE `id`=11;"
  connection.query(mindtouchCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var driveCS = "UPDATE `content_source_types` SET `name`='Google Drive', `description`='Cloud storage and sharing of documents, images, videos and other files.', `img`='assets/img/drive.svg',`category`=4, `url`='https://docs.searchunify.com/Content/Content-Sources/Google-Drive.htm' WHERE `id`=12;"
  connection.query(driveCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var boxCS = "UPDATE `content_source_types` SET `description`='Cloud content management and workflow tool with thousands of integrations.', `img`='assets/img/box.svg', `category`=4, `url`='https://docs.searchunify.com/Content/Content-Sources/Box.htm' WHERE `id`=13;"
  connection.query(boxCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var helpscoutCS = "UPDATE `content_source_types` SET `name`='Help Scout',`description`='Support platform for streamlined communication between business and customers.', `img`='assets/img/helpscout.svg',`category`=5, `url`='https://docs.searchunify.com/Content/Content-Sources/Helpscout.htm' WHERE `id`=14;"
  connection.query(helpscoutCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var githubCS = "UPDATE `content_source_types` SET `description`='Code hosting, sharing and management platform with version control for developers.', `img`='assets/img/github.svg',`category`=4, `url`='https://docs.searchunify.com/Content/Content-Sources/Github.htm' WHERE `id`=15;"
  connection.query(githubCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var youtubeCS = "UPDATE `content_source_types` SET `name`='YouTube',`description`='World''s most popular social platform for video hosting and sharing.', `img`='assets/img/youtube.svg',`category`=4, `url`='https://docs.searchunify.com/Content/Content-Sources/YouTube.htm' WHERE `id`=17;"
  connection.query(youtubeCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var stackoverflowCS = "UPDATE `content_source_types` SET `description`='Large community for developers, coders, designers and other professionals in IT.', `img`='assets/img/stackoverflow.svg',`category`=7, `url`='https://docs.searchunify.com/Content/Content-Sources/StackOverflow.htm' WHERE `id`=18;"
  connection.query(stackoverflowCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var amazonS3CS = "UPDATE `content_source_types` SET `name`='Amazon Web Services', `description`='On-demand cloud computing platform for enterprises.', `img`='assets/img/amazon-s3.svg',`category`=7  WHERE `id`=19;"
  connection.query(amazonS3CS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var litmosCS = "UPDATE `content_source_types` SET `description`='Learning management system with an emphasis on user experience and analytics.', `img`='assets/img/litmos.svg',`category`=1, `url`='https://docs.searchunify.com/Content/Content-Sources/Litmos.htm' WHERE `id`=20;"
  connection.query(litmosCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var solrCS = "UPDATE `content_source_types` SET `name`='SOLR', `description`='Marketing automation software from Adobe.', `img`='assets/img/solr.svg',`category`=6, `url`='https://docs.searchunify.com/Content/Content-Sources/SOLR.htm' WHERE `id`=21;"
  connection.query(solrCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var apiCS = "UPDATE `content_source_types` SET `name`='API', `description`='SearchUnify Api to transform any data repository into a compatible content source.', `img`='assets/img/API.svg', `category`=7, `url`='https://docs.searchunify.com/Content/Content-Sources/API.htm' WHERE `id`=22;"
  connection.query(apiCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var moodleCS = "UPDATE `content_source_types` SET `description`='World''s most popular open source learning platform, popular in academia and industry.', `img`='assets/img/moodle.svg',`category`=1, `url`='https://docs.searchunify.com/Content/Content-Sources/Moodle.htm' WHERE `id`=23;"
  connection.query(moodleCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var dropboxCS = "UPDATE `content_source_types` SET `description`='File sharing and storage service for freelancers and businesses.', `img`='assets/img/dropbox.svg',`category`=4, `url`='https://docs.searchunify.com/Content/Content-Sources/Dropbox.htm' WHERE `id`=24;"
  connection.query(dropboxCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var sabaCS = "UPDATE `content_source_types` SET `name`='Saba', `description`='HR-friendly learning and skill development tool.', `img`='assets/img/saba.svg',`category`=1, `url`='https://docs.searchunify.com/Content/Content-Sources/Saba.htm' WHERE `id`=25;"
  connection.query(sabaCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var servicenowCS = "UPDATE `content_source_types` SET `name`='ServiceNow',`description`='Workflow management system for IT, emplyees and customers.', `img`='assets/img/servicenow.svg',`category`=3, `url`='https://docs.searchunify.com/Content/Content-Sources/ServiceNow.htm' WHERE `id`=26;"
  connection.query(servicenowCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var jiraOnPremCS = "UPDATE `content_source_types` SET `name`='Jira On-Premises', `description`='Project management tool for agile teams. On-premises version.', `img`='assets/img/jiraOnPrem.svg',`category`=3, `url`='https://docs.searchunify.com/Content/Content-Sources/Jira-On-Premises.htm' WHERE `id`=27;"
  connection.query(jiraOnPremCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var marketoCS = "UPDATE `content_source_types` SET `description`='Marketing automation software from Adobe.', `img`='assets/img/marketo-small.svg',`category`=5, `url`='https://docs.searchunify.com/Content/Content-Sources/Marketo.htm' WHERE `id`=28;"
  connection.query(marketoCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var dynamicsCS = "UPDATE `content_source_types` SET `name`='Microsoft Dynamics',`description`='CRM and ERP for large organizations.', `img`='assets/img/dynamics.svg',`category`=5, `url`='https://docs.searchunify.com/Content/Content-Sources/Microsoft-Dynamics.htm' WHERE `id`=29;"
  connection.query(dynamicsCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var receptiveCS = "UPDATE `content_source_types` SET `description`='Product demand intelligence platform to assist managers priortize feature development.', `img`='assets/img/receptive.svg',`category`=3, `url`='' WHERE `id`=30;"
  connection.query(receptiveCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var zoominCS = "UPDATE `content_source_types` SET `description`='Technical documentation management for personalized and dynamic user experience.', `img`='assets/img/zoomin.svg',`category`=2, `url`='https://docs.searchunify.com/Content/Content-Sources/Zoomin.htm' WHERE `id`=31;"
  connection.query(zoominCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var doceboCS = "UPDATE `content_source_types` SET `description`='Mobile-ready, social learning platform for organizations.', `img`='assets/img/docebo.svg',`category`=1, `url`='https://docs.searchunify.com/Content/Content-Sources/Docebo.htm' WHERE `id`=32;"
  connection.query(doceboCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var vimeoCS = "UPDATE `content_source_types` SET `description`='Video hosting, sharing for artists, film makers, animators and other creative types.', `img`='assets/img/vimeo.svg', `category`=4, `url`='https://docs.searchunify.com/Content/Content-Sources/Vimeo.htm' WHERE `id`=33;"
  connection.query(vimeoCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var azureCS = "UPDATE `content_source_types` SET `name`='Azure',`description`='Powerful project tracking and planning tool from Microsoft.', `img`='assets/img/azure-devops.svg', `category`=3, `url`='https://docs.searchunify.com/Content/Content-Sources/Azure.htm' WHERE `id`=34;"
  connection.query(azureCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var skilljarCS = "UPDATE `content_source_types` SET `name`='SkillJar',`description`='Customer training platform with the singular goal to increase adoption.', `img`='assets/img/skilljar.svg',`category`=1, `url`='https://docs.searchunify.com/Content/Content-Sources/SkillJar.htm' WHERE `id`=39;"
  connection.query(skilljarCS, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var higherLogicCSCategory = "UPDATE `content_source_types` SET `name`='Higher Logic', `description`='Popular cloud-based brand community management platform.', `img`='assets/img/higherLogic.svg',`category`=5, `url`='https://docs.searchunify.com/Content/Content-Sources/Higher-Logic.htm' WHERE `id`=41;"
  connection.query(higherLogicCSCategory, function (err, data) {
    if (err) {
      console.log(err);
    }
  });
  var renameAlphaville = "UPDATE `template_types` SET `name` = 'Fervent', `label` = 'Fervent', `description` = 'Fervent', `folderPath` = 'templates/Fervent.html' WHERE `template_types`.`id` = 2;"
  connection.query(renameAlphaville, function (err, data) {
    if (err) {
      console.log(err);
    }
  });

  var similarSearchRecommendation = `ALTER TABLE search_clients ADD similar_search TINYINT(4) NULL DEFAULT '0';`
  connection.query(similarSearchRecommendation, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  //for merged facets
  var merged_facets = `ALTER TABLE search_clients ADD merged_facets LONGTEXT NULL DEFAULT NULL`
  connection.query(merged_facets, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var didYouMeanFieldinSearchClient = `ALTER TABLE search_clients ADD did_you_mean TINYINT(4) NOT NULL DEFAULT '0';`
  connection.query(didYouMeanFieldinSearchClient, function (err, data) {
    if (err && err.code != "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var didYouMeanFieldinDictType = `ALTER TABLE search_clients ADD did_you_mean_dict_type TINYINT(4) NOT NULL DEFAULT '0'`
  connection.query(didYouMeanFieldinDictType, function (err, data) {
    if (err && err.code != "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var changeScopeName = "UPDATE `oauth_scopes` SET `scope` = 'All' WHERE `id` = 4";
  connection.query(changeScopeName, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addScopeNone = "INSERT INTO `oauth_scopes` (`id`, `scope`, `is_default`) VALUES ('5', 'None', 0);"
  connection.query(addScopeNone, function (err, data) {
    if (err && err.code !== "ER_DUP_ENTRY") {
      console.error(err);
    }
  });

  var alterUser = "ALTER TABLE `user` CHANGE `is_active` `is_active` TINYINT(4) NOT NULL DEFAULT '0', ADD `activation_date` DATE NULL DEFAULT NULL AFTER `is_federated`, ADD `invite_count` INT NOT NULL DEFAULT '0' AFTER `activation_date`, ADD `ques_ans` VARCHAR(250) NULL DEFAULT NULL AFTER `invite_count`;"
  connection.query(alterUser, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  //for Slack Bot
  var Channel_ID = "ALTER TABLE `search_clients` ADD `channel_id` VARCHAR(256) NULL AFTER `is_case_deflected_shown`;"
  connection.query(Channel_ID, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.log(err);
    }
  });

  var addSelectedTabs = "ALTER TABLE `user` ADD `selectedTabs` varchar(256) DEFAULT NULL AFTER `ques_ans`;"
  connection.query(addSelectedTabs, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addChatbotToggle = "ALTER TABLE `user` ADD `adminChatbotDetails` BOOLEAN default 0 AFTER `selectedTabs`;"
  connection.query(addChatbotToggle, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var alterAuthorization = "ALTER TABLE `content_source_authorization` ADD `usernameselector` VARCHAR(500) NOT NULL AFTER `connection_status`, ADD `buttonselector` VARCHAR(500) NOT NULL AFTER `usernameselector`, ADD `loginlink` VARCHAR(500) NOT NULL AFTER `buttonselector`, ADD `passwordselector` VARCHAR(500) NOT NULL AFTER `loginlink`";
  connection.query(alterAuthorization, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });


  var cron_stuck_count = "ALTER TABLE `content_sources` ADD `cron_stuck_count` INT NOT NULL DEFAULT '0' AFTER `logFile`";
  connection.query(cron_stuck_count, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addUserBlocked = "ALTER TABLE `user` ADD `is_blocked` INT NOT NULL DEFAULT '0' AFTER `selectedTabs`";
  connection.query(addUserBlocked, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addThread = "ALTER TABLE `content_sources` ADD `thread` INT(11) NOT NULL DEFAULT '1' AFTER `logFile`;";
  connection.query(addThread, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var cron_stuck_count = "ALTER TABLE `content_sources` ADD `cron_stuck_count` INT NOT NULL DEFAULT '0' AFTER `logFile`";
  connection.query(cron_stuck_count, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addSitemapEnable = "ALTER TABLE `website_setting` ADD `sitemap_enabled` TINYINT(11) NULL DEFAULT '0' AFTER `javascript_enabled`;";
  connection.query(addSitemapEnable, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var sessionForNutch = " ALTER TABLE `website_setting` ADD `session` TINYINT(11) NOT NULL DEFAULT '1' AFTER `sitemap_enabled`;";
  connection.query(sessionForNutch, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var batchSizeWebsite = "ALTER TABLE `content_sources` ADD `batch` INT(11) NOT NULL DEFAULT '50' AFTER `thread`;";
  connection.query(batchSizeWebsite, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var outlinkRegex = "ALTER TABLE `website_setting` ADD `outlink_filters` TEXT CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL AFTER `index_filters`;";
  connection.query(outlinkRegex, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addAttachArticleBoosting = "ALTER TABLE custom_boosting ADD date_boosting_enabled TINYINT(4) DEFAULT 0 NOT NULL, ADD click_boosting_enabled TINYINT(4) DEFAULT 0 NOT NULL, ADD attached_articles_enabled TINYINT(4) DEFAULT 0 NOT NULL, ADD attached_articles_base varchar(100) DEFAULT '10' NOT NULL;";
  connection.query(addAttachArticleBoosting, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var updateAddons = "UPDATE `addons` SET `name` = 'KCS<sup>&reg;</sup> Enabler', `label` = 'KCS<sup>&reg;</sup> Enabler' WHERE `id` = 12"
  connection.query(updateAddons, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var moreColumns = `ALTER TABLE search_clients ADD language VARCHAR(50) DEFAULT 'angular'`;
  connection.query(moreColumns, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var redirectionUrl = `ALTER TABLE search_clients ADD redirection_url VARCHAR(300) DEFAULT '',
  ADD autocomplete VARCHAR(50) DEFAULT '5'; `;
  connection.query(redirectionUrl, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var searchClientSettings = `ALTER TABLE search_clients ADD pagination VARCHAR(50) DEFAULT 'page_no_button' ,
    ADD advertisements integer DEFAULT 0 , 
    ADD autocomplete_instant integer DEFAULT 0 ;`;
  connection.query(searchClientSettings, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var mergeSources = `ALTER TABLE search_clients ADD mergeSources text NULL`;
  connection.query(mergeSources, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var clickBoostingSCEnabled = `ALTER TABLE custom_boosting ADD common_click_score_enabled BOOLEAN NOT NULL DEFAULT "1" AFTER attached_articles_base`
  connection.query(clickBoostingSCEnabled, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var updateLightningFolder = `UPDATE search_client_types 
    SET standard_client_folder = (CASE 
        WHEN id = 7 THEN 'resources/search_clients_standard/su_vf_console_v2'  
        WHEN id = 8 THEN 'resources/search_clients_standard/su_lightning_community_v2'
        WHEN id = 9 THEN 'resources/search_clients_standard/su_vf_internal_v2' 
      END)
    WHERE id IN (7,8,9)`
  connection.query(updateLightningFolder, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var searchClientSettingsLanguage = `ALTER TABLE search_clients ADD languageManager integer DEFAULT 0`;
  connection.query(searchClientSettingsLanguage, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var searchClientSettingsViewedResults = `ALTER TABLE search_clients ADD ViewedResults integer DEFAULT 0`;
  connection.query(searchClientSettingsViewedResults, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  /** Adding Access control table */
  var insertTableAccessControl = `CREATE TABLE IF NOT EXISTS access_control_settings (
    id INT AUTO_INCREMENT PRIMARY KEY, contentSource INT NOT NULL, searchClient INT NOT NULL);`;
  connection.query(insertTableAccessControl, (err, data) => {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  /** Adding inital settings to private aceess in access_control_settings table */
  var insertTableAccessControlData = `INSERT  IGNORE INTO access_control_settings (id , contentSource, searchClient) VALUES (1,1,1);`;
  connection.query(insertTableAccessControlData, (err, data) => {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  /**  Adding Super Admin in roles table */
  var addSuperUserRole = `INSERT IGNORE INTO roles (id , role) VALUES (4,'Super Admin');`;
  connection.query(addSuperUserRole, (err, data) => {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  /**Adding email column in Search client table to save owner of every new SC 
   <EMAIL> will be default owner for all exisiting Search clients */
  var emailInSearchClientTable = `ALTER TABLE search_clients ADD email VARCHAR(200) DEFAULT '<EMAIL>' ;`;
  connection.query(emailInSearchClientTable, (err, data) => {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  /** Adding email column in Content Source table to save owner of every new CS 
 <EMAIL> will be default owner for all exisiting content Source */
  var emailInContentSourcesTable = `ALTER TABLE content_sources ADD email VARCHAR(200) DEFAULT '<EMAIL>' ;`;
  connection.query(emailInContentSourcesTable, (err, data) => {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var shareOptionInSearchClientTable = `ALTER TABLE search_clients ADD sharedAccess VARCHAR(10000) DEFAULT '[]' ;`;
  connection.query(shareOptionInSearchClientTable, (err, data) => {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var shareOptionInContentSourceTable = `ALTER TABLE content_sources ADD sharedAccess VARCHAR(10000) DEFAULT '[]' ;`;
  connection.query(shareOptionInContentSourceTable, (err, data) => {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var searchClientSettingsConsoleConfigurations = `ALTER TABLE search_clients ADD SCsalesforceConsoleConfigurations VARCHAR(1000) DEFAULT '{"caseSelection":1,"caseNumberView":1,"searchResultsOpensNewBrowserTab":1}'`;
  connection.query(searchClientSettingsConsoleConfigurations, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var knowledgeGraphAddon = `INSERT INTO addons (id, name, label, is_download, image_path, description) VALUES 
    (14, 'Knowledge Graph', 'Knowledge Graph', 0, 'assets/img/knowledge-graph.svg', 'SearchUnify Knowledge Graph provides answers even before a user has Clicked.') ON DUPLICATE KEY UPDATE id=14;`;
    connection.query(knowledgeGraphAddon, function (err, data) {
      if (err && err.code !== "ER_DUP_FIELDNAME") {
        console.error(err);
      }
    });

  var mergeResults = `ALTER TABLE search_clients_to_content_objects ADD merge_results LONGTEXT NULL DEFAULT NULL`;
  connection.query(mergeResults, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var updateWebsiteSC = "UPDATE `search_client_types` SET `img` = 'assets/img/website.svg', `large_image` = 'assets/img/website_large.svg', `name` = 'Web App', `description` = 'Web App', `category` = 4 WHERE `id` = 6"
  connection.query(updateWebsiteSC, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var updateJiveSC = "UPDATE `search_client_types` SET `img` = 'assets/img/jive.svg', `large_image` = 'assets/img/jive_large.svg', `category` = 4 WHERE `id` = 1"
  connection.query(updateJiveSC, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var updateLithiumSC = "UPDATE `search_client_types` SET `img` = 'assets/img/Lithium.svg', `large_image` = 'assets/img/Lithium_large.svg', `category` = 2 WHERE `id` = 2"
  connection.query(updateLithiumSC, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var updateSu_vf_communitySC = "UPDATE `search_client_types` SET `img` = 'assets/img/su_vf_community.svg', `large_image` = 'assets/img/su_vf_community_large.svg', `category` = 2 WHERE `id` = 3"
  connection.query(updateSu_vf_communitySC, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var updateSu_vf_consoleSC = "UPDATE `search_client_types` SET `img` = 'assets/img/su_vf_console.svg', `large_image` = 'assets/img/su_vf_console_large.svg', `category` = 3 WHERE `id` = 7"
  connection.query(updateSu_vf_consoleSC, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var updateSu_lightning_communitySC = "UPDATE `search_client_types` SET `img` = 'assets/img/su_lightning_community.svg', `large_image` = 'assets/img/su_lightning_community_large.svg', `category` = 2 WHERE `id` = 8"
  connection.query(updateSu_lightning_communitySC, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var updateSu_vf_internalSC = "UPDATE `search_client_types` SET `img` = 'assets/img/su_vf_internal.svg', `large_image` = 'assets/img/su_vf_internal_large.svg', `category` = 4 WHERE `id` = 9"
  connection.query(updateSu_vf_internalSC, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var updateJive_tileSC = "UPDATE `search_client_types` SET `img` = 'assets/img/jive_tile.svg', `large_image` = 'assets/img/jive_tile_large.svg', `category` = 4 WHERE `id` = 10"
  connection.query(updateJive_tileSC, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var zendeskSearchClient = "UPDATE `search_client_types` SET `category` = 1 WHERE `id` = 11";
  connection.query(zendeskSearchClient, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var aemSearchClient = "UPDATE `search_client_types` SET `category` = 2 WHERE `id` = 24";
  connection.query(aemSearchClient, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var zendeskSupportSearchClient = "UPDATE `search_client_types` SET `category` = 3 WHERE `id` = 12";
  connection.query(zendeskSupportSearchClient, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var higherLogic = "UPDATE `search_client_types` SET `category` = 2 WHERE `id` = 23";
  connection.query(higherLogic, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var wordpressSearchClient = "UPDATE `search_client_types` SET `category` = 1 WHERE `id` = 14";
  connection.query(wordpressSearchClient, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var drupalSearchClient7 = "UPDATE `search_client_types` SET `category` = 1 WHERE `id` = 15";
  connection.query(drupalSearchClient7, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var zendeskSupportConsoleSearchClient = "UPDATE `search_client_types` SET `category` = 3 WHERE `id` = 16";
  connection.query(zendeskSupportConsoleSearchClient, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var drupalSearchClient8 = "UPDATE `search_client_types` SET `category` = 1 WHERE `id` = 17";
  connection.query(drupalSearchClient8, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var microsoftDynamics = "UPDATE `search_client_types` SET `category` = 3 WHERE `id` = 18";
  connection.query(microsoftDynamics, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var mindtouchSupportSearchClient = "UPDATE `search_client_types` SET `category` = 1 WHERE `id` = 19";
  connection.query(mindtouchSupportSearchClient, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var hostedSearchClient = "UPDATE `search_client_types` SET `category` = 4 WHERE `id` = 20";
  connection.query(hostedSearchClient, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var servicenowSearchClient = "UPDATE `search_client_types` SET `category` = 3 WHERE `id` = 21";
  connection.query(servicenowSearchClient, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var SlackBotSearchClient = "UPDATE `search_client_types` SET `category` = 4 WHERE `id` = 22";
  connection.query(SlackBotSearchClient, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var SharePointSearchClient = "UPDATE `search_client_types` SET `category` = 1 WHERE `id` = 25";
  connection.query(SharePointSearchClient, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  
  var addLastLogin = `ALTER TABLE user ADD last_login VARCHAR(1000) DEFAULT '{"last_login": "","current_login":""}'`;
  connection.query(addLastLogin, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var addLastUpdatedSC = `ALTER TABLE search_clients ADD last_updated_by VARCHAR(1000);`;
  connection.query(addLastUpdatedSC, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var addLastUpdatedCS = `ALTER TABLE content_sources ADD last_updated_by VARCHAR(1000);`;
  connection.query(addLastUpdatedCS, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addTimeStampCSAuth = `ALTER TABLE content_source_authorization ADD created DATETIME DEFAULT CURRENT_TIMESTAMP,ADD updated DATETIME ON UPDATE CURRENT_TIMESTAMP;`;
  connection.query(addTimeStampCSAuth, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addTimeStampCSSpaces = `ALTER TABLE content_source_spaces ADD created DATETIME DEFAULT CURRENT_TIMESTAMP,ADD updated DATETIME ON UPDATE CURRENT_TIMESTAMP;`;
  connection.query(addTimeStampCSSpaces, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addTimeStampCSObjects = `ALTER TABLE content_source_objects ADD created DATETIME DEFAULT CURRENT_TIMESTAMP,ADD updated DATETIME ON UPDATE CURRENT_TIMESTAMP;`;
  connection.query(addTimeStampCSObjects, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addTimeStampCSObjectFields = `ALTER TABLE content_source_object_fields ADD created DATETIME DEFAULT CURRENT_TIMESTAMP,ADD updated DATETIME ON UPDATE CURRENT_TIMESTAMP;`;
  connection.query(addTimeStampCSObjectFields, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addTimeStampAPICrawlerFields = `ALTER TABLE api_crawler_fields ADD created DATETIME DEFAULT CURRENT_TIMESTAMP,ADD updated DATETIME ON UPDATE CURRENT_TIMESTAMP;`;
  connection.query(addTimeStampAPICrawlerFields, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var jsCrawlingFilter = "ALTER TABLE `website_setting` ADD `jsCrawling_Filter` TEXT CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL AFTER `outlink_filters`;";
  connection.query(jsCrawlingFilter, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var updateConstraint = `ALTER TABLE search_clients_to_content_objects
    DROP FOREIGN KEY search_clients_to_content_objects_ibfk_3,
    ADD CONSTRAINT search_clients_to_content_objects_ibfk_4 FOREIGN KEY (title_field_id)
    REFERENCES content_source_object_fields (id)
    ON DELETE SET NULL;`
  connection.query(updateConstraint, (err, data) => {
    if (err && err.code !== "ER_DUP_KEY") {
      console.error(err);
    }
  });

  var composeTitle = "ALTER TABLE `search_clients_to_content_objects` ADD `compose_title` TEXT NULL AFTER `base_href`;";
  connection.query(composeTitle, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });
  
  var specialSearch = "ALTER TABLE `search_clients` ADD `special_search` TINYINT NOT NULL DEFAULT 0 AFTER `similar_search`;";
  connection.query(specialSearch, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var smartFacet = "ALTER TABLE `search_clients` ADD `smart_facet` TINYINT NOT NULL DEFAULT 0 ;";
  connection.query(smartFacet, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var minFacetSelection = "ALTER TABLE `search_clients` ADD `min_facet_selection` TINYINT NOT NULL DEFAULT 10 ;";
  connection.query(minFacetSelection, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var usersConfigurations = "ALTER TABLE `usersConfigurations` ADD `adminChatbot` BOOLEAN NOT NULL DEFAULT FALSE;"
  connection.query(usersConfigurations, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var addExtraField = `ALTER TABLE search_clients_filters ADD extra_field TINYINT NOT NULL DEFAULT 0`;
  connection.query(addExtraField, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var addTrackAnalytics = `ALTER TABLE search_clients_filters ADD track_analytics TINYINT NOT NULL DEFAULT 0`;
  connection.query(addTrackAnalytics, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  
  var autoLearning = `ALTER TABLE search_clients_filters ADD auto_learning TINYINT NOT NULL DEFAULT 0`;
connection.query(autoLearning, function (err, data) {
  if (err && err.code !== "ER_DUP_FIELDNAME") {
    console.error(err);
  }
});

  var relevancy = `ALTER TABLE search_clients ADD relevancy VARCHAR(1000) DEFAULT '{"searchOperator": "OR","advanceQuery":[]}'`;
connection.query(relevancy, function (err, data) {
  if (err && err.code !== "ER_DUP_FIELDNAME") {
    console.error(err);
  }
});

var user_feedback = `CREATE TABLE IF NOT EXISTS user_feedback (
                      id int(11) NOT NULL AUTO_INCREMENT,
                      search_client_uid varchar(127) NOT NULL,
                      contentSearchExp json DEFAULT NULL,
                      searchExp json DEFAULT NULL,
                      conversionExp json DEFAULT NULL,
                      pageRatingInstance json DEFAULT NULL,
                      pageRatingCustomization json DEFAULT NULL,
                      searchFeedback json DEFAULT NULL,
                      dropdowns json DEFAULT NULL,
                      PRIMARY KEY (id)
                    )`;
connection.query(user_feedback, function (err, data) {
  if (err && err.code !== "ER_TABLE_EXISTS_ERROR") {
    console.error(err);
  }
});


  let user_feedback_update = `ALTER TABLE user_feedback ADD CONSTRAINT user_feedback_UN UNIQUE KEY (search_client_uid)`;
  connection.query(user_feedback_update, function (err, data) {
    if (err && err.code !== "ER_DUP_KEYNAME") {
      console.error(err);
    }
  });

  var field_Boosting = `ALTER TABLE field_boosting ADD sortValue VARCHAR(10000) DEFAULT NULL, ADD isSort boolean DEFAULT false;`;
  connection.query(field_Boosting, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var defaultBoost = `ALTER TABLE search_clients_to_content_objects MODIFY COLUMN boosting_factor float DEFAULT 1 NULL;`;
  connection.query(defaultBoost, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  // Automated Scripts
  connection.query(
    `CREATE TABLE IF NOT EXISTS automated_scripts_check (
      id int(11) NOT NULL AUTO_INCREMENT, 
      script varchar(255), 
      method varchar(255), 
      release_name varchar(255), 
      status tinyint(2) DEFAULT 0,
      PRIMARY KEY (id),
      UNIQUE (script)
    )`, (errS) => {
      if(errS) {
        console.log(errS);
      } else {
        console.log('Created automated scripts table');
          // Add automated scripts entries
        connection.query(
          `INSERT IGNORE INTO automated_scripts_check(script, method, release_name) VALUES
          ('keyword_boosting_table_update', 'runScript', 'c21');
          INSERT IGNORE INTO automated_scripts_check(script, method, release_name) VALUES
          ('pageRatingMigration', 'startScript', 'm22');
          INSERT IGNORE INTO automated_scripts_check(script, method, release_name) VALUES
          ('searchSetup', 'usersUpdate', 'm22');`,
          (errI) => {
            if(errI) {
              console.log(errI);
            } else {
              console.log('Added automated scripts values');

              // Automation Started
              checkAutomatedScripts.startChecks();
            }
        });
      }
  });

  const makeAlertMigration = (cntntSrcIds,resData) => {
    const query = `Truncate table email_notification_contentsources;
      ALTER TABLE email_notification_contentsources change contentSources contentSources JSON;`;
    connection.query(query,(opErr,opRes) => {
      if(opErr) console.log(opErr);
      if(!opErr && resData.length){
        let insQuery = "insert into email_notification_contentsources (id,subscriberEmails,contentSources) VALUES";
        let csVals;
        // Getting  --> 54,44,16
        // Creating --> [{"csId": 54, "value": 1}
        for(let i=0; i<resData.length; i++){
          if(resData[i].contentSources == 'all'){
            csVals = cntntSrcIds.map(function(o){
              return {"csId": parseInt(o.id), "value": 0};
            });
          }else{
            csVals = resData[i].contentSources.split(",").map(function(o){
              return {"csId": parseInt(o), "value": 0};
            });
          }
          
          insQuery += " ("+resData[i].id+",'"+resData[i].subscriberEmails+"','"+JSON.stringify(csVals)+"'),";
        }
        
        insQuery = insQuery.substring(0,insQuery.length-1);
        connection.query(insQuery, (errIns,resIns) => {
          if(errIns)
            console.log('Error while inserting migrated data : ',JSON.stringify(errIns));
          else
            console.log('Migration successfull in email notif contentsource');
        })
      }
    });
  }

  connection.query("SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE DATA_TYPE = 'varchar' and table_name = 'email_notification_contentsources' AND COLUMN_NAME = 'contentSources';", (csErr,csData) => {
    if(!csErr){
      if(csData.length){
        connection.query(`CREATE TABLE IF NOT EXISTS email_notification_contentsources_temp (
          id int(11), subscriberEmails varchar(255), contentSources varchar(255))`, () => {
          //comment out truncate statement for temp table
          // connection.query(`TRUNCATE TABLE email_notification_contentsources_temp;`);
          
          connection.query("SELECT * from email_notification_contentsources;", async (errData,resData) => {
            if(!errData){
                await connection.query(`insert into email_notification_contentsources_temp select * from email_notification_contentsources;`);

                connection.query(`select 1 from email_notification_contentsources where contentSources = 'all'`, async (errAll, resAll) =>{
                  if(errAll) console.log(errAll);
                  else{
                    if(resAll.length)
                      connection.query(`select id from content_sources`, (errCntntIds, resCntntIds) => {
                        if(errCntntIds) console.log(errCntntIds);
                        else makeAlertMigration(resCntntIds, resData);
                      });
                    else makeAlertMigration([],resData);
                  }
                });
              }else console.log(errData);
          });
        });
      }else console.log(csErr);
    }
  });

  var nlpIntent = `CREATE TABLE IF NOT EXISTS nlp_intents (
    id int(11) NOT NULL AUTO_INCREMENT,
    intent json DEFAULT NULL,
    last_train date DEFAULT NULL,
    PRIMARY KEY (id)
  )`
  connection.query(nlpIntent, function (err, data) {
    if (err && err.code !== "ER_TABLE_EXISTS_ERROR") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var addFieldsToSC = `ALTER TABLE search_clients ADD horizontalTabEnabled int(11) DEFAULT 1 NULL, ADD horizontalTabFacet varchar(100) DEFAULT '_index' NULL,ADD horizontalTabOrder MEDIUMTEXT NULL,ADD sourceSortBy varchar(100) DEFAULT 'count' NULL, ADD sourceOrderBy varchar(100) DEFAULT 'desc' NULL,ADD sourceOrder MEDIUMTEXT NULL,ADD sourcePriority int(11) DEFAULT 1 NULL;`
  connection.query(addFieldsToSC, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var nlpSuggestions = `CREATE TABLE IF NOT EXISTS nlp_suggestions (
    id int(11) NOT NULL AUTO_INCREMENT,
    intent json DEFAULT NULL,
    PRIMARY KEY (id)
  )`
  connection.query(nlpSuggestions, function (err, data) {
    if (err && err.code !== "ER_TABLE_EXISTS_ERROR") {
      commonFunctions.errorlogger.error(err);
    }
  });
  var addSFCFields = `ALTER TABLE search_clients_filters ADD sort_by varchar(100) DEFAULT 'count', ADD order_by varchar(100) DEFAULT 'desc', ADD summary_length int(11) DEFAULT 100 ;`
  connection.query(addSFCFields, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var addSFClevelFields = `ALTER TABLE search_clients_filters
    ADD level_limit int(11) DEFAULT 1 ;`
  connection.query(addSFClevelFields, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var addSummaryFields = `ALTER TABLE search_clients ADD collapsibleSummary INT(11) DEFAULT 0 NULL,ADD maxSummaryLength INT(11) DEFAULT 250 NULL,ADD minSummaryLength INT(11) DEFAULT 100 NULL;`
  connection.query(addSummaryFields, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error(err);
    }
  });

  var addContentTag = `ALTER TABLE search_clients ADD contentTag varchar(100) DEFAULT '{"enabled": true, "type": "index"}' NULL`;
  connection.query(addContentTag, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });



  var modifyContentTag = `ALTER TABLE search_clients change contentTag contentTag varchar(100) DEFAULT '{"enabled": true, "type": "index"}'`;
  connection.query(modifyContentTag, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var isAllContentSourcesHidden = `ALTER TABLE search_clients ADD hideAllContentSources BOOLEAN NOT NULL DEFAULT FALSE`;
  connection.query(isAllContentSourcesHidden, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var isAddedOnS3 = `ALTER TABLE search_clients ADD isAddedOnS3 BOOLEAN NOT NULL DEFAULT FALSE`;
  connection.query(isAddedOnS3, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var intent_train_status = `alter table nlp_intents  add column trainStatus int(11)`;
  connection.query(intent_train_status, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var intent_train_date =   `ALTER TABLE nlp_intents MODIFY COLUMN last_train DATETIME NULL`;
 connection.query(intent_train_date, function (err, data) {
   if (err && err.code !== "ER_DUP_FIELDNAME") {
     console.error(err);
   }
 });
 var community_helper_template_created_date =   `ALTER TABLE community_helper_template MODIFY COLUMN created_date DATETIME NULL`;
 connection.query(community_helper_template_created_date, function (err, data) {
   if (err && err.code !== "ER_DUP_FIELDNAME") {
     console.error(err);
   }
 });
 var addBypass = `ALTER TABLE keyword_boost ADD bypass_filter BOOL DEFAULT false NOT NULL;`;
  connection.query(addBypass, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

 var hidden_facet_column = `ALTER TABLE search_clients ADD hidden_facet LONGTEXT NULL;`;
 connection.query(hidden_facet_column, function (err, data) {
   if (err && err.code !== "ER_DUP_FIELDNAME") {
     console.error(err);
   }
 });
  var addBypassIntent = `ALTER TABLE intent_boosting ADD bypass_filter BOOL DEFAULT false NOT NULL;`;
  connection.query(addBypassIntent, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  var updateBoostingFactorValue = `UPDATE search_clients_to_content_objects SET boosting_factor = 1 WHERE boosting_factor IS NULL;`;
  connection.query(updateBoostingFactorValue, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  connection.query("select user_email from user u where user_email ='<EMAIL>';", (err,data) => {
    if(!err){
      if(!data.length){
        connection.query(`INSERT INTO user(user_email, name, access_token, created_date, is_active, provisionKey,
           scope, forget_password_token, is_federated, activation_date, invite_count, ques_ans, selectedTabs, 
           is_blocked, last_login) VALUES('<EMAIL>', 'Admin', '35d7910a11a6d248ef9b53b2ed82b3c2', 
           '2021-05-22 06:44:08.0', 1, '', 'None', '166da0b4c2cb047bbd70ccb0cf8dd959', NULL, '2021-05-22', 0, 
           'f9f8e6937a7e9831137bc954497b3bc2', '[]', 0, '{"last_login": "","current_login":""}');`, () => {
          console.log('Added Ghost user');
        });
      } else console.log('Error while adding ghost user',err);
    }
  });

  var addAnalyticsField = `ALTER TABLE search_clients_filters ADD track_analytics TINYINT NOT NULL DEFAULT 0`;
  connection.query(addAnalyticsField, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  var defaultPinOption = `ALTER TABLE usersConfigurations ADD preselectedPinSCAnalytics varchar(256) DEFAULT NULL,
    ADD preselectedPinSCTuning varchar(256) DEFAULT NULL,
    ADD preselectedPinSCHideSuggestion varchar(256) DEFAULT NULL;`;
  connection.query(defaultPinOption, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });
  //Script to disable Duplicacy Checker automatically - SU-15960
  connection.query(`Delete from addons_status where addon_id  = 11`, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  connection.query('ALTER TABLE search_clients ADD agentHelperSlackCreds json NULL;', function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  connection.query(`Delete from search_client_types where id  = 13`, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  });

  connection.query('ALTER TABLE m20.search_clients_to_content_objects MODIFY COLUMN icon varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL', function(err, data){
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      console.error(err);
    }
  })
};


function sqlWriteIgnoreError(sqlQuery, msg,callback) {
  var q = connection.query(sqlQuery, function (err, data) {
    if (err && err.code !== "ER_DUP_FIELDNAME") {
      commonFunctions.errorlogger.error("sql query failed", sqlQuery, err);
    }
    callback(null, data);
  });
}

function sqlWrite(sqlQuery, msg) {
  var q = connection.query(sqlQuery, function (err, data) {
    if (err) {
      commonFunctions.errorlogger.error("Failed at:::::::", msg);
      throw err;
    }
    else {
      // console.log(msg);
    }
  });
}


connection_sql.handleDisconnect().then((result)=>{
  // This function need's to be discussed to check about SQL Connection.
                    // SQL ISSUE
  init();
})
