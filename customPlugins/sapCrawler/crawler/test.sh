#!/bin/bash
echo "Checking crawling status ---- "
pushd $2
crawlProcessId=`ps -p $1 -o pid=`
echo $crawlProcessId process
if [ "$crawlProcessId" = "$1" ] # check if processid is not empty(i.e process exists)
then
  echo `date` "Crawling is still in progress."
else
  echo `date` "Crawling finished. Removing cron"
  if [ "$3" = "helpdocs" ]
  then
    echo "switching directory for helpdoc crawler"
    cd ./crawler/helpdocs
    pwd
  else
    echo "switching directory for knowledgecenter crawler"
    cd ./crawler/knowledgecenter
    pwd
  fi
  
  echo "executing remove script----"
  node removeCronJob.js
  echo "cron remove script execution finished. Exiting"
  cd ../../
fi
popd