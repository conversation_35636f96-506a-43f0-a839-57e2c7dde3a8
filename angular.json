{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"admin-search-cli": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"aot": false, "outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets", "src/favicon.ico", "src/manifest.webmanifest", {"glob": "**/*", "input": "node_modules/ngx-monaco-editor/assets/monaco", "output": "assets/monaco/"}], "styles": ["src/teal-app-theme.scss", "src/styles.css", "node_modules/ng2-toasty/bundles/style-default.css", "src/customFonts.scss"]}, "configurations": {"production": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "admin-search-cli:build"}, "configurations": {"production": {"browserTarget": "admin-search-cli:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "admin-search-cli:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "styles": ["src/teal-app-theme.scss", "src/styles.css", "node_modules/ng2-toasty/bundles/style-default.css", "src/customFonts.scss"], "assets": ["src/assets", "src/favicon.ico", "src/manifest.webmanifest"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": []}}}}, "admin-search-cli-e2e": {"root": "e2e", "sourceRoot": "e2e", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "./protractor.conf.js", "devServerTarget": "admin-search-cli:serve"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["e2e/tsconfig.e2e.json"], "exclude": []}}}}}, "defaultProject": "admin-search-cli", "schematics": {"@schematics/angular:component": {"prefix": "app", "style": "css"}, "@schematics/angular:directive": {"prefix": "app"}}, "cli": {"analytics": false}}