/**
 * Created by <PERSON><PERSON><PERSON> on 14-05-2016.
 */
'use strict';

module.exports = function(sequelize, DataTypes) {
  var User = sequelize.define('User',  {
    id: {
      type: DataTypes.INTEGER(11),
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
      unique: true,
    },
    user_email: DataTypes.STRING(32),
    // password: DataTypes.STRING(32),
    scope: DataTypes.STRING,
    name: DataTypes.STRING(250),
    access_token : DataTypes.STRING,
    created_date : DataTypes.DATE,
    is_active : DataTypes.TINYINT,
    provisionKey : DataTypes.STRING,
    timezone : DataTypes.STRING
  }, {
    tableName: 'user', // oauth_users
    timestamps: false,
    underscored: false
  });
  User.associate= function associate(models) {
    //User.hasMany(models.OAuthClient);
  };
  return User;
}

