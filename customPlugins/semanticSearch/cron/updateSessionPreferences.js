/**
 * <AUTHOR>
 * @description Session Profiling for automated boosting of search results
 * @version 21-06-2018 (1.0)
 */

 var path = require('path');
 var elastic = require('elasticsearch');
 var async = require('async');
 const pako = require('pako');
 environment = require(path.join(__dirname, '../../../routes/environment'));
 process.env.NODE_ENV = environment.configuration;
 var configPath = path.join(__dirname, '../../../config');
 process.env.NODE_CONFIG_DIR = configPath;
 console.log(environment.configuration);
 config = require('config');
 var profileIndex = config.get('elasticIndex.sessionProfile');
 var profileType = "profile";
 var profileMapping = {"mappings":{"profile":{"properties":{"id":{"type":"string", "index": "not_analyzed"},"uid":{"type":"string", "index": "not_analyzed"},"selectedFilters":{"type":"nested","properties":{"name":{"type":"string","index":"not_analyzed"},"selectedValues":{"type":"string","index":"not_analyzed"},"count":{"type":"integer"}}},"derivedFilters":{"type":"nested","properties":{"name":{"type":"string","index":"not_analyzed"},"selectedValues":{"type":"string","index":"not_analyzed"},"count":{"type":"integer"}}}}}}};
 var connection_sql = require('./../../../utils/connection');
 var filterObjects = [];
 const { getTenantInfoFromTenantId } = require('auth-middleware');
 const { getEsClient } = require('../../../utils/elastic');


 const kafkaLib = require("../../../utils/kafka/kafka-lib");

 // code for node-kafka
 console.log(`Kafka Autoboosting topic - ${config.get("kafkaTopic.boostingTopic")}`);

 const subscribeConsumer = async () => {
   try{
     const updateSessionConsumer = await kafkaLib.getConsumer({
       groupId: `admin ${config.get('kafkaTopic.boostingTopic')}`,
       topic: config.get('kafkaTopic.boostingTopic'),
       fromBeginning: false
   })

   await updateSessionConsumer.run({
       eachMessage: async ({
         topic, parition, message, heartBeat
       }) => {
         try {
           let sessionMessage = JSON.parse(message.value);
           let tenantInfo = await getTenantInfoFromTenantId(sessionMessage.tenantID);
           tenantInfo = tenantInfo[0];
           const esClient = await getEsClient(tenantInfo.tenantId);
           if (sessionMessage.compressed) {
             console.log('Recieved GZIP format');
             sessionMessage.data = JSON.parse(pako.ungzip(sessionMessage.data, { to: 'string' }));
           }
           esClient.indices.delete({
             index: `${tenantInfo.tpk}_${profileIndex}`
           }, (err, res) => {
             console.log('after deleting profiles ',err, res);
             esClient.indices.create({
               index: `${tenantInfo.tpk}_${profileIndex}`,
               body: profileMapping
             }, (e,r) => {
               console.log('after recreating profile index ', e,r);
               generateProfiles(sessionMessage, tenantInfo, esClient);
             });
           });
         } catch (e) {
             console.log(e)
         }
       }
     });
   }catch(error){
     if(error.message === 'UNKNOWN_TOPIC_OR_PARTITION'){
       kafkaLib.createTopic(config.get('kafkaTopic.boostingTopic'));
     }
   }
 }

 function exists(item){
   return filterObjects.findIndex((itemin) => {
     var flag = true;
     if(item.name == itemin.name){
       if(item.selectedValues){
         item.selectedValues.map(selVal => {
           if(itemin.selectedValues.indexOf(selVal)==-1)
             flag = false;
         });
       }
     }else
       flag = false;
     return flag
   });
 }

 function getUniqueCounts(array){
   var unique = {};
   for(var i=0;i<array.length;i++){
     var id = exists(array[i])
     if(id != -1){
       if(unique[''+id]){
         unique[''+id]++;
       }else
         unique[''+id] = 1;
     }else{
       filterObjects.push(array[i]);
       unique[''+(filterObjects.length-1)] = 1;
     }
   }

   return Object.keys(unique).map(key => {
     var obj = {
       name: filterObjects[parseInt(key)].name || filterObjects[parseInt(key)].type,
       selectedValues: filterObjects[parseInt(key)].selectedValues || filterObjects[parseInt(key)].filter,
       count: unique[key]
     }
     return obj;
   });
 }

 function deriveFilters(sessions, tenantInfo, esClient){
   async.auto({
     "getSettings": cb => {
       var sql = "select filter_id, priority, csoid, field_name, field_label, cs.name, cs.label, t3.name object_name, t3.label object_label from (select * from (select t1.content_source_object_field_id filter_id, t1.priority, csof.content_source_object_id csoid, csof.name field_name, csof.label field_label from (select * from search_clients_filters where use_as='Filter' group by content_source_object_field_id) as t1 join content_source_object_fields csof on t1.content_source_object_field_id = csof.id order by priority asc) t2 join content_source_objects cso on t2.csoid=cso.id) t3 join content_sources cs on t3.content_source_id = cs.id";
       connection[tenantInfo.tenantId].execute.query(sql, (err,res)=>{
         if(!err){
           var obj = {};
           res.map(row=>{
             if(!obj[row.name])
               obj[row.name] = {};

             if(!obj[row.name][row.object_name])
               obj[row.name][row.object_name] = [];

             obj[row.name][row.object_name].push(row.field_name);
           });
           cb(null, obj);
         }else
           cb(null, {});
       });
     },
     "getAvailableFilters": ["getSettings", (dataFromAbove, cb) => {
       var fieldBoosts = {};
       async.parallelLimit(sessions.map(session => {
         return cbin2=>{
           async.parallelLimit(session.conversions.map(conversion => {
             return cbin => {
               var index = conversion.indexName
               var type = conversion.index_type;
               var id = conversion.doc_id;
               esClient.get({
                 index: index,
                 type: type,
                 id: id
               },(err, res)=>{
                 if(!err && res.found){
                   fieldBoosts[session.cookie] = {};
                   if(dataFromAbove.getSettings[index] && dataFromAbove.getSettings[index][type]){
                     dataFromAbove.getSettings[index][type].map(field => {
                       if(res._source[field]){
                         if(fieldBoosts[session.cookie][field]){
                           if(fieldBoosts[session.cookie][field][res._source[field]]){
                             fieldBoosts[session.cookie][field][res._source[field]]+=1;
                           }else{
                             fieldBoosts[session.cookie][field][res._source[field]]=1;
                           }
                         }else{
                           fieldBoosts[session.cookie][field] = {};
                           fieldBoosts[session.cookie][field][res._source[field]]=1;
                         }
                       }
                     })
                   }
                 }
                 cbin(null, 1);
               });
             };
           }),1,(err, res) => {
             cbin2(null, fieldBoosts);
           });
         }
       }),1,(err, res)=>{
         cb(null, fieldBoosts);
       });
     }]
   }, (err, res)=>{
     var profiles = sessions.map(session => {
       var profile = {};
       profile.id = session.cookie;
       profile.uid = session.uid;
       profile.selectedFilters = getUniqueCounts(session.filters);
       profile.derivedFilters = [];
       profile.indices_boost = session.indices_boost
       profile.type_boost = session.type_boost
       profile.field_boosts = session.fieldBoosts
       //console.log(JSON.stringify(profile.selectedFilters));
       session.conversions.map(conversion => {
         profile.derivedFilters.push({name: '_index', selectedValues: [conversion.indexName]});
         profile.derivedFilters.push({name: '_type', selectedValues: [conversion.index_type]});
       });

       profile.derivedFilters = getUniqueCounts(profile.derivedFilters);
       if(res.getAvailableFilters[session.cookie] && Object.keys(res.getAvailableFilters[session.cookie]).length>0){
         Object.keys(res.getAvailableFilters[session.cookie]).map(key=>{
           Object.keys(res.getAvailableFilters[session.cookie][key]).map(val=>{
             profile.derivedFilters.push({name: key, selectedValues: [val], count: res.getAvailableFilters[session.cookie][key][val]});
           });
         });
       }

       return profile;
     });
     // console.log(JSON.stringify(profiles));
     // after this step index profiles to elastic index sessionProfiles

     // profiles = createAutoBoosting(profiles);
     addProfilesToIndex(profiles, tenantInfo, esClient);
   });

 }

 function createAutoBoosting(profiles){
   profiles = profiles.map(profile => {
     var indices_boost_1 = {}; // selected
     var indices_boost_2 = {}; // derived
     var type_boost_1 = {};
     var type_boost_2 = {};
     var field_boost_1 = {};
     var field_boost_2 = {};

     profile.selectedFilters.map(filter => {
       if (filter.name == '_index') {
         filter.selectedValues.map(val => {
           if (indices_boost_1[val]) {
             indices_boost_1[val] += filter.count;
           } else {
             indices_boost_1[val] = filter.count;
           }
         });
       }else if(filter.name == '_type'){
         filter.selectedValues.map(val => {
           if (type_boost_1[val]) {
             type_boost_1[val] += filter.count/2;
           } else {
             type_boost_1[val] = filter.count/2;
           }
         });
       }else{
         if(filter.name != undefined && filter.selectedValues != undefined){
           filter.selectedValues.map(val => {
             if (field_boost_1[filter.name]) {
               if(field_boost_1[filter.name][val])
                 field_boost_1[filter.name][val] += filter.count/4;
               else
                 field_boost_1[filter.name][val] = filter.count/4;
             } else {
               field_boost_1[filter.name] = {};
               field_boost_1[filter.name][val] = filter.count/4;
             }
           });
         }
       }
     });

     Object.keys(indices_boost_1).map(key => {
       indices_boost_1[key] = indices_boost_1[key] * 20
     });
     Object.keys(type_boost_1).map(key => {
       type_boost_1[key] = type_boost_1[key] * 2
     });
     Object.keys(field_boost_1).map(key => {
       Object.keys(field_boost_1[key]).map(val => {
         field_boost_1[key][val] = field_boost_1[key][val] * 1.5
       });
     });

     profile.derivedFilters.map(filter => {
       if (filter.name == '_index') {
         filter.selectedValues.map(val => {
           if (indices_boost_2[val]) {
             indices_boost_2[val] += filter.count;
           } else {
             indices_boost_2[val] = filter.count;
           }
         });
       }else if(filter.name == '_type'){
         filter.selectedValues.map(val => {
           if (type_boost_2[val]) {
             type_boost_2[val] += filter.count/2;
           } else {
             type_boost_2[val] = filter.count/2;
           }
         });
       }else{
         filter.selectedValues.map(val => {
           if (field_boost_2[filter.name]) {
             if(field_boost_2[filter.name][val])
               field_boost_2[filter.name][val] += filter.count/4;
             else
               field_boost_2[filter.name][val] = filter.count/4;
           } else {
             field_boost_2[filter.name] = {};
             field_boost_2[filter.name][val] = filter.count/4;
           }
         });
       }
     });

     Object.keys(indices_boost_2).map(key => {
       indices_boost_2[key] = indices_boost_2[key] * 5
       if (indices_boost_1[key]) {
         indices_boost_1[key] += indices_boost_2[key];
       } else {
         indices_boost_1[key] = indices_boost_2[key];
       }
     });

     let indices_boost_1_float = {};
     let indices_keys = Object.keys(indices_boost_1);
     for (let i = 0; i < indices_keys.length; i++) {
       indices_boost_1_float[indices_keys[i]] = parseFloat(indices_boost_1[indices_keys[i]]).toFixed(2);
     }

     profile.indices_boost = indices_boost_1_float;

     Object.keys(type_boost_2).map(key => {
       type_boost_2[key] = type_boost_2[key] * 1.5
       if (type_boost_1[key]) {
         type_boost_1[key] += type_boost_2[key];
       } else {
         type_boost_1[key] = type_boost_2[key];
       }
     });

     let type_boost_1_float = {};
     let type_keys = Object.keys(type_boost_1);
     for (let i = 0; i < type_keys.length; i++) {
       type_boost_1_float[type_keys[i]] = parseFloat(type_boost_1[type_keys[i]]).toFixed(2);
     }

     profile.type_boost = type_boost_1_float;

     Object.keys(field_boost_2).map(key => {
       Object.keys(field_boost_2[key]).map(val => {
         field_boost_2[key][val] = field_boost_2[key][val] * 1.5
         if (field_boost_1[key]) {
           if(field_boost_1[key][val])
             field_boost_1[key][val] += field_boost_2[key][val];
           else
             field_boost_1[key][val] = field_boost_2[key][val];
         } else {
           field_boost_1[key] = {};
           field_boost_1[key][val] = field_boost_2[key][val];
         }
       });
     });

     var fieldBoosts = [];
     Object.keys(field_boost_1).map(key => {
       Object.keys(field_boost_1[key]).map(val => {
         var filterObj = {
           "filter":{
             "term":{
             }
           }
         };
         if(key=="post_time" || key == "CreatedDate")
           filterObj=checkVal(val,filterObj)
         else
         filterObj.filter.term[key] = val;
         filterObj["weight"] = field_boost_1[key][val];
         if(filterObj["weight"]<=1){
           filterObj["weight"]+=1;
         }
         if(key == "post_time" || val == "Past Month"|| val == "Past Year" || val =="Past Week" || val == "Past Day"){
           //skipping as date field identified
         }else{
           fieldBoosts.push(filterObj);
         }
       });
     });
     profile.field_boosts = fieldBoosts;
     return profile;
   });

   return profiles;
 }

 function generateProfiles(res, tenantInfo, esClient){

   console.log('In Generate session profile function');

   try{
     var sess = res.data;
     var os = [];
     sessions = Object.keys(sess);
     console.log(`Total no of Sessions found are - ${sessions.length}`);
     if(sessions.length>0){
       sessions.map(session => {
         var o = {};
         var k = Object.keys(sess[session]);
         var conversions = [];
         var filters = [];
         sess[session].conversion.map(conv => {
           conversions.push(conv);
         });
         sess[session].filters.map( filter => {
           filters.push(filter);
         });
         cookie = sess[session].taid;
         o.filters = filters;
         o.conversions = conversions;
         o.uid = sess[session].uid;
         o.cookie = cookie;
         o.indices_boost = sess[session].indices_boost
         o.fieldBoosts = sess[session].field_boosts
         o.type_boost = sess[session].type_boost
         os.push(o);
       });
       deriveFilters(os, tenantInfo, esClient);
       console.log(JSON.stringify(os))
     }else{
       console.log('no session in last two days');
     }
   }catch(e){
     console.log(e);
   }
 }

 function addProfilesToIndex(profiles, tenantInfo, esClient){
   var bulkRequests = [];
   profiles.map(profile => {
    if (profile.id !== '') {
      bulkRequests.push({index: { "_index": `${tenantInfo.tpk}_${profileIndex}`, "_type": profileType, "_id": profile.id}});
    } else {
      bulkRequests.push({index: { "_index": `${tenantInfo.tpk}_${profileIndex}`, "_type": profileType, "_id": profile.uid}});
    }
    bulkRequests.push(profile);
   });
   console.log(JSON.stringify(bulkRequests));
   if(bulkRequests.length > 0){
     esClient.bulk({
       body: bulkRequests
     }, (err, res) => {
       console.log('added to index', err, res);
       // process.exit(0);
     });
   }
 }

 // connection_sql.handleDisconnect().then(function (result) {
 //   client.indices.delete({
 //     index: profileIndex
 //   }, (err, res) => {
 //     console.log('after deleting profiles ',err, res);
 //     client.indices.create({
 //       index: profileIndex,
 //       body: profileMapping
 //     }, (e,r) => {
 //       console.log('after recreating profile index ', e,r);
 //       generateProfiles();
 //     });
 //   });
 // });

 function checkVal(val,object)
 {
   object={
     "range": {
     "post_time": {
       "format": "yyyy-MM-dd",
       "gte": "now-12M",
       "lt": "now"
     }
   }
   }
   switch(val){
     case 'Past Month':{
       object.range.post_time.gte='now-1M'
         object.range.post_time.lt='now'
       break;
     }
     case 'Past Year':{
       object.range.post_time.gte='now-12M'
       object.range.post_time.lt='now'
       break;

     }
     case 'Past Week':{
       object.range.post_time.gte='now-7d'
       object.range.post_time.lt='now'
       break;
     }
     case 'Past Day':{
       object.range.post_time.gte='now-1d'
       object.range.post_time.lt='now'
       break;
     }
     default:{
       object
     }
   }
   return object
 }




 module.exports = {
   subscribeConsumer
 }
