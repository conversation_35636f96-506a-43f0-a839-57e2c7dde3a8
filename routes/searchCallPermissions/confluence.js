/* Created By <PERSON><PERSON>
    On 06/02/18
*/


var express = require('express');
var request = require('request');
var async = require('async');
var commonFunctions = require('./../../utils/commonFunctions');
var fs = require('fs');
var router = express.Router();

//************************ Start of Confluence Permissions ***************************'
router.get('/getContent', function (req, res, next) {
    var obj = {
        "contentSources": ["147"],
        "email": "<EMAIL>"
    }
    getPermissionObjects(obj,req, function (err, data) {
        res.send(data);
    })
});

const getPermissionObjects = function (contentSources,req, callback) {
    var asyncTasks = [];
    var contentSourceIds = contentSources['contentSources']
    var email = contentSources['email'];
    contentSourceIds.map(x => {
        asyncTasks.push(confleuncePermissions.bind(null, x, email,req));
    })
    async.series(asyncTasks, function (err, data) {
        if (err) {
            callback(err, null);
        }
        else{
            var arrInitial=[];
            for(var we=0;we<data.length;we++){
                arrInitial=arrInitial.concat(data[we].perms);
            }
            callback(null, [{id:contentSourceIds[0],"perms":arrInitial}]);
        }
    })
}

const confleuncePermissions = function (content_source_id, email,req, callback) {
    commonFunctions.getContentSourceDataById(content_source_id,req, function (err, resultData) {
        var username = email.substring(0, email.lastIndexOf("@"));
        var options = {
            method: 'GET',
            url: resultData.contentSource.url + '/rest/api/user?username=' + username,
            /*  oauth: {
                          consumer_key: resultData.authorization.publicKey,
                          private_key: resultData.authorization.privateKey,
                          token: resultData.authorization.accessToken,
                          token_secret: resultData.authorization.refreshToken,
                          signature_method: 'RSA-SHA1'
                      }, */
            headers:
            {
                'content-type': 'application/json'
                //  authorization: 'Basic ' + new Buffer(resultData.authorization.username + ":" + resultData.authorization.password).toString("base64")
            }
        };
        if (resultData.authorization.authorization_type=="OAuth") {
            options["oauth"] = {
                consumer_key: resultData.authorization.publicKey,
                private_key: resultData.authorization.privateKey,
                token: resultData.authorization.accessToken,
                token_secret: resultData.authorization.refreshToken,
                signature_method: 'RSA-SHA1'
            }
        } else {
            options["headers"]["authorization"] = 'Basic ' + new Buffer(resultData.authorization.username + ":" + resultData.authorization.password).toString("base64");
        }
        request(options, function (error, response, body) {
            if (error || JSON.parse(body).statusCode == 400 || JSON.parse(body).statusCode == 404) {
                commonFunctions.errorlogger.error("Error", error);
                commonFunctions.errorlogger.info("body is ", body);
                callback(null, {
                    "id": content_source_id,
                    "perms": []
                });
            }
            else {
                if (commonFunctions.checkJSON(body) && commonFunctions.checkJSON(body)._links) {
                    var body = JSON.parse(body);
                    var ObjToReturn = {
                        "id": content_source_id,
                        "perms": []
                    };

                    getConfluenceGroups(username, resultData, 0, 200, [], function (errGroups, dataGroups) {

                        ObjToReturn["perms"] = dataGroups;
                        ObjToReturn["perms"].push(body._links.self);
                        callback(errGroups, ObjToReturn);
                    });

                } else {
                    callback('Invalid Creds', null);
                }
            }
        });
    });
}

const getConfluenceGroups = function (username, resultData, start, limit, initialArr, callback) {
    var options = {
        method: 'GET',
        url: resultData.contentSource.url + '/rest/api/user/memberof?username=' + username + '&size=' + limit + '&start=' + start,
        /*  oauth: {
                      consumer_key: resultData.authorization.publicKey,
                      private_key: resultData.authorization.privateKey,
                      token: resultData.authorization.accessToken,
                      token_secret: resultData.authorization.refreshToken,
                      signature_method: 'RSA-SHA1'
                  }, */
        headers:
        {
            'content-type': 'application/json'
            //  authorization: 'Basic ' + new Buffer(resultData.authorization.username + ":" + resultData.authorization.password).toString("base64")
        }
    };

    if (resultData.authorization.authorization_type=="OAuth") {
        options["oauth"] = {
            consumer_key: resultData.authorization.publicKey,
            private_key: resultData.authorization.privateKey,
            token: resultData.authorization.accessToken,
            token_secret: resultData.authorization.refreshToken,
            signature_method: 'RSA-SHA1'
        }
    } else {
        options["headers"]["authorization"] = 'Basic ' + new Buffer(resultData.authorization.username + ":" + resultData.authorization.password).toString("base64");
    }
    request(options, function (error, response, body) {
        if (error || JSON.parse(body).statusCode == 400 || JSON.parse(body).statusCode == 404) {
            commonFunctions.errorlogger.error("Error", error);
            commonFunctions.errorlogger.info("body is", body);
            callback(null, {
                "id": resultData.authorization.content_source_id,
                "perms": []
            });
        }
        else {
            if (commonFunctions.checkJSON(body) && commonFunctions.checkJSON(body).results) {
                var body = JSON.parse(body);
                for (var i = 0; i < body.results.length; i++) {
                    initialArr.push(body.results[i]._links.self);
                }
                if (body._links && body._links.next) {
                    start = start + limit;
                    getConfluenceGroups(username, resultData, start, limit, initialArr, function (errGRP, grps) {
                        if (errGRP) {
                            callback(errGRP, null);
                        } else {
                            callback(null, grps);
                        }
                    });
                } else {
                    callback(null, initialArr);
                }

            } else {
                callback('Invalid Creds', null);
            }
        }
    });

}

//************************ End of Confluence Permissions ***************************

module.exports = {
    getPermissionObjects: getPermissionObjects,
    router: router
}
