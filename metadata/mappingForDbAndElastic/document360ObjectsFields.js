module.exports = {
  "objects": [
    {
      "name": "articles",
      "label": "Articles",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "id",
          "label": "Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "title",
          "label": "Title",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "content",
          "label": "Content",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "html_content",
          "label": "HTML Content",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "status",
          "label": "Status",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "slug",
          "label": "Slug",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "created_at",
          "label": "Created At",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 0, //analyzer
          "isActive": 1
        },
        {
          "name": "modified_at",
          "label": "Modified At",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 0, //analyzer
          "isActive": 1
        },
        {
          "name": "hidden",
          "label": "Hidden",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "category_id",
          "label": "Category Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "project_version_id",
          "label": "Project Version Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "root_category",
          "label": "Root category",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "category_name",
          "label": "Category Name",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "api_reference",
          "label": "API Reference",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "version_slug",
          "label": "Version Slug",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name":"data_category_nested",
          "label":"Data Category Nested",
          "type":"string",
          "isFilterable":1,
          "isSortable":1,
          "isSearchable":1,
          "isMerged":1,
          "isActive":1
        },
        {
          "name":"data_category_flat",
          "label":"Category Flat",
          "type":"string",
          "isFilterable":1,
          "isSortable":1,
          "isSearchable":1,
          "isActive":1
        }
      ]

    },
    {
      "name": "pages",
      "label": "Pages",
      "status": 1,
      "boosting_factor": 1,
      "base_url": "",
      "fields": [
        {
          "name": "id",
          "label": "Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "title",
          "label": "Title",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "content",
          "label": "Content",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "html_content",
          "label": "HTML Content",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "status",
          "label": "Status",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "slug",
          "label": "Slug",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "created_at",
          "label": "Created At",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 0, //analyzer
          "isActive": 1
        },
        {
          "name": "modified_at",
          "label": "Modified At",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 0, //analyzer
          "isActive": 1
        },
        {
          "name": "hidden",
          "label": "Hidden",
          "type": "string",
          "isFilterable": 1,
          "isSortable": 1,
          "isSearchable": 1,
          "isActive": 1
        },
        {
          "name": "project_document_version_id",
          "label": "Project Version Id",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "root_category",
          "label": "Root category",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "category_name",
          "label": "Category Name",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "api_reference",
          "label": "API Reference",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name": "version_slug",
          "label": "Version Slug",
          "type": "string",
          "isFilterable": 1, //indexing method analyzed OR not analyzed
          "isSortable": 1,
          "isSearchable": 1, //analyzer
          "isActive": 1
        },
        {
          "name":"data_category_nested",
          "label":"Data Category Nested",
          "type":"string",
          "isFilterable":1,
          "isSortable":1,
          "isSearchable":1,
          "isMerged":1,
          "isActive":1
        },
        {
          "name":"data_category_flat",
          "label":"Category Flat",
          "type":"string",
          "isFilterable":1,
          "isSortable":1,
          "isSearchable":1,
          "isActive":1
        }
      ]
    }
  ]
};