/*
	Author: <PERSON><PERSON>
	Date: 06-09-2018 (DD-MM-YYYY)
    Description:    SearchGraph generated with clustered similar terms with same node and also relational 
                    connectivity between each node based on context
  
  Instruction to execute for particular date range: 
  node updateSearchGraph.js "from=now-1d/d" "to=now-1d/d"

  Can execute directly as well
  node updateSearchGraph.js

  For full refresh
  node updateSearchGraph.js "refreshGraph=true"
  
*/
var path = require('path');
var elastic = require('elasticsearch');
var fs = require('fs');
var async = require('async');
environment = require(path.join(__dirname, '../../../routes/environment'));
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../../config');
process.env.NODE_CONFIG_DIR = configPath;
console.log(environment.configuration);
var config = require('config');
var graphIndex = config.get('elasticIndex.searchgraph');
var graphType = "search_pattern";
var synonymIndex = config.get("elasticIndex.synonymIndex");
var synonymType = "synonym";
var daterange = {};
var skipDeletingSearchGraph = false;

let isolateSynonymStopwords = config?config.get('isolateSynonymStopwords'):false;
let pocName = config?config.get('poc_name'):'';
let elSynonymPath = (isolateSynonymStopwords && pocName)?('nlp/'+pocName+"_synonyms.txt"):"synonyms.txt";
let elStopwordsPath = (isolateSynonymStopwords && pocName)?('nlp/'+pocName+"_stopwords.txt"):"stopwords.txt";

daterange.from = "now-1d/d";
daterange.to = "now-1d/d";

if(process.argv.length>2){
  process.argv.slice(2).map(pair=>{
    var splited = pair.split(',');
    if(splited.length == 2){
      var key = splited[0].trim();
      var value = splited[1].trim();
      if(key == 'from'){
        daterange.from = value;
      }else if(key == 'to'){
        daterange.to = value;
      }else if(key == 'refreshGraph'){

      }else{
        console.log('Please use valid parameters');
        process.exit(0);
      }
    }
  })
}

var graphMapping = {
  "mappings": {
    "search_pattern": {
      "properties": {
        "clusterId": {
          "type": "long"
        },
        "convCount": {
          "type": "long"
        },
        "conversion": {
          "type": "nested",
          "properties": {
            "count": {
              "type": "long"
            },
            "search_term": {
              "type": "string"
            },
            "url": {
              "type": "string"
            }
          }
        },
        "intents": {
          "type": "nested",
          "properties": {
            "intent": {
              "type": "string",
              "analyzer": "custom_lowercase_stemmed",
              "search_analyzer": "custom_lowercase_synonym"
            },
            "count": {
              "type": "long"
            }
          }
        },
        "text": {
          "type": "string",
          "analyzer": "custom_lowercase_stemmed",
          "search_analyzer": "custom_lowercase_synonym"
        }
      }
    }
  },
  "settings": {
    "index": {
      "analysis": {
        "filter": {
          "my_custom_stop": {
            "type": "stop",
            "stopwords_path": elStopwordsPath
          },
          "synonym": {
            "type": "synonym",
            "synonyms_path": elSynonymPath
          },
          "my_stop": {
            "type": "stop",
            "stopwords": "_english_"
          }
        },
        "analyzer": {
          "custom_lowercase_stemmed": {
            "filter": ["lowercase", "my_stop", "my_custom_stop"],
            "tokenizer": "standard"
          },
          "custom_lowercase_synonym": {
            "filter": ["lowercase", "my_stop", "my_custom_stop", "synonym"],
            "tokenizer": "standard"
          }
        }
      }
    }
  }
};
var searchDataIndex = config.get('elasticIndex.analytics');
var searchDataType = "search_keyword";
var stopWords = ["a", "about", "above", "across", "after", "again", "against", "all", "almost", "alone", "along", "already", "also", "although", "always", "among", "an", "and", "another", "any", "anybody", "anyone", "anything", "anywhere", "are", "area", "areas", "around", "as", "ask", "asked", "asking", "asks", "at", "away", "b", "back", "backed", "backing", "backs", "be", "became", "because", "become", "becomes", "been", "before", "began", "behind", "being", "beings", "best", "better", "between", "big", "both", "but", "by", "c", "came", "can", "cannot", "case", "cases", "certain", "certainly", "clear", "clearly", "come", "could", "d", "did", "differ", "different", "differently", "do", "does", "done", "down", "down", "downed", "downing", "downs", "during", "e", "each", "early", "either", "end", "ended", "ending", "ends", "enough", "even", "evenly", "ever", "every", "everybody", "everyone", "everything", "everywhere", "f", "face", "faces", "fact", "facts", "far", "felt", "few", "find", "finds", "first", "for", "four", "from", "full", "fully", "further", "furthered", "furthering", "furthers", "g", "gave", "general", "generally", "get", "gets", "give", "given", "gives", "go", "going", "good", "goods", "got", "great", "greater", "greatest", "group", "grouped", "grouping", "groups", "h", "had", "has", "have", "having", "he", "her", "here", "herself", "high", "high", "high", "higher", "highest", "him", "himself", "his", "how", "however", "i", "if", "important", "in", "interest", "interested", "interesting", "interests", "into", "is", "it", "its", "itself", "j", "just", "k", "keep", "keeps", "kind", "knew", "know", "known", "knows", "l", "large", "largely", "last", "later", "latest", "least", "less", "let", "lets", "like", "likely", "long", "longer", "longest", "m", "made", "make", "making", "man", "many", "may", "me", "member", "members", "men", "might", "more", "most", "mostly", "mr", "mrs", "much", "must", "my", "myself", "n", "necessary", "need", "needed", "needing", "needs", "never", "new", "new", "newer", "newest", "next", "no", "nobody", "non", "noone", "not", "nothing", "now", "nowhere", "number", "numbers", "o", "of", "off", "often", "old", "older", "oldest", "on", "once", "one", "only", "open", "opened", "opening", "opens", "or", "order", "ordered", "ordering", "orders", "other", "others", "our", "out", "over", "p", "part", "parted", "parting", "parts", "per", "perhaps", "place", "places", "point", "pointed", "pointing", "points", "possible", "present", "presented", "presenting", "presents", "problem", "problems", "put", "puts", "q", "quite", "r", "rather", "really", "right", "right", "room", "rooms", "s", "said", "same", "saw", "say", "says", "second", "seconds", "see", "seem", "seemed", "seeming", "seems", "sees", "several", "shall", "she", "should", "show", "showed", "showing", "shows", "side", "sides", "since", "small", "smaller", "smallest", "so", "some", "somebody", "someone", "something", "somewhere", "state", "states", "still", "still", "such", "sure", "t", "take", "taken", "than", "that", "the", "their", "them", "then", "there", "therefore", "these", "they", "thing", "things", "think", "thinks", "this", "those", "though", "thought", "thoughts", "three", "through", "thus", "to", "today", "together", "too", "took", "toward", "turn", "turned", "turning", "turns", "two", "u", "under", "until", "up", "upon", "us", "use", "used", "uses", "v", "very", "w", "want", "wanted", "wanting", "wants", "was", "way", "ways", "we", "well", "wells", "went", "were", "what", "when", "where", "whether", "which", "while", "who", "whole", "whose", "why", "will", "with", "within", "without", "would", "x", "y", "year", "years", "yet", "you", "young", "younger", "youngest", "your", "yours", "z", "a's", "able", "about", "above", "according", "accordingly", "across", "actually", "after", "afterwards", "again", "against", "ain't", "all", "allow", "allows", "almost", "alone", "along", "already", "also", "although", "always", "am", "among", "amongst", "an", "and", "another", "any", "anybody", "anyhow", "anyone", "anything", "anyway", "anyways", "anywhere", "apart", "appear", "appreciate", "appropriate", "are", "aren't", "around", "as", "aside", "ask", "asking", "associated", "at", "available", "away", "awfully", "be", "became", "because", "become", "becomes", "becoming", "been", "before", "beforehand", "behind", "being", "believe", "below", "beside", "besides", "best", "better", "between", "beyond", "both", "brief", "but", "by", "c'mon", "c's", "came", "can", "can't", "cannot", "cant", "cause", "causes", "certain", "certainly", "changes", "clearly", "co", "com", "come", "comes", "concerning", "consequently", "consider", "considering", "contain", "containing", "contains", "corresponding", "could", "couldn't", "course", "currently", "definitely", "described", "despite", "did", "didn't", "different", "do", "does", "doesn't", "doing", "don't", "don’t", "done", "down", "downwards", "during", "each", "edu", "eg", "eight", "either", "else", "elsewhere", "enough", "entirely", "especially", "et", "etc", "even", "ever", "every", "everybody", "everyone", "everything", "everywhere", "ex", "exactly", "example", "except", "far", "few", "fifth", "first", "five", "followed", "following", "follows", "for", "former", "formerly", "forth", "four", "from", "further", "furthermore", "get", "gets", "getting", "given", "gives", "go", "goes", "going", "gone", "got", "gotten", "greetings", "had", "hadn't", "happens", "hardly", "has", "hasn't", "have", "haven't", "having", "he", "he's", "hello", "help", "hence", "her", "here", "here's", "hereafter", "hereby", "herein", "hereupon", "hers", "herself", "hi", "him", "himself", "his", "hither", "hopefully", "how", "howbeit", "however", "i'd", "i'll", "i'm", "i've", "ie", "if", "ignored", "immediate", "in", "inasmuch", "inc", "indeed", "indicate", "indicated", "indicates", "inner", "insofar", "instead", "into", "inward", "is", "isn't", "it", "it'd", "it'll", "it's", "its", "itself", "just", "keep", "keeps", "kept", "know", "knows", "known", "last", "lately", "later", "latter", "latterly", "least", "less", "lest", "let", "let's", "like", "liked", "likely", "little", "look", "looking", "looks", "ltd", "mainly", "many", "may", "maybe", "me", "mean", "meanwhile", "merely", "might", "more", "moreover", "most", "mostly", "much", "must", "my", "myself", "name", "namely", "nd", "near", "nearly", "necessary", "need", "needs", "neither", "never", "nevertheless", "new", "next", "nine", "no", "nobody", "non", "none", "noone", "nor", "normally", "not", "nothing", "novel", "now", "nowhere", "obviously", "of", "off", "often", "oh", "ok", "okay", "old", "on", "once", "one", "ones", "only", "onto", "or", "other", "others", "otherwise", "ought", "our", "ours", "ourselves", "out", "outside", "over", "overall", "own", "particular", "particularly", "per", "perhaps", "placed", "please", "plus", "possible", "presumably", "probably", "provides", "que", "quite", "qv", "rather", "rd", "re", "really", "reasonably", "regarding", "regardless", "regards", "relatively", "respectively", "right", "said", "same", "saw", "say", "saying", "says", "second", "secondly", "see", "seeing", "seem", "seemed", "seeming", "seems", "seen", "self", "selves", "sensible", "sent", "serious", "seriously", "seven", "several", "shall", "she", "should", "shouldn't", "since", "six", "so", "some", "somebody", "somehow", "someone", "something", "sometime", "sometimes", "somewhat", "somewhere", "soon", "sorry", "specified", "specify", "specifying", "still", "sub", "such", "sup", "sure", "t's", "take", "taken", "tell", "tends", "th", "than", "thank", "thanks", "thanx", "that", "that's", "thats", "the", "their", "theirs", "them", "themselves", "then", "thence", "there", "there's", "thereafter", "thereby", "therefore", "therein", "theres", "thereupon", "these", "they", "they'd", "they'll", "they're", "they've", "think", "third", "this", "thorough", "thoroughly", "those", "though", "three", "through", "throughout", "thru", "thus", "to", "together", "too", "took", "toward", "towards", "tried", "tries", "truly", "try", "trying", "twice", "two", "un", "under", "unfortunately", "unless", "unlikely", "until", "unto", "up", "upon", "us", "use", "used", "useful", "uses", "using", "usually", "value", "various", "very", "via", "viz", "vs", "want", "wants", "was", "wasn't", "way", "we", "we'd", "we'll", "we're", "we've", "welcome", "well", "went", "were", "weren't", "what", "what's", "whatever", "when", "whence", "whenever", "where", "where's", "whereafter", "whereas", "whereby", "wherein", "whereupon", "wherever", "whether", "which", "while", "whither", "who", "who's", "whoever", "whole", "whom", "whose", "why", "will", "willing", "wish", "with", "within", "without", "won't", "wonder", "would", "would", "wouldn't", "yes", "yet", "you", "you'd", "you'll", "you're", "you've", "your", "yours", "yourself", "yourselves", "zero"];
var noSynonymFor = []
var queriesProcessed = 0;
var maxNumToken = 15;
var clid = 1;

client = new elastic.Client({
  host: 'http://' + config.get('elasticIndex.host') + ':' + config.get('elasticIndex.port')
});

function min(x, y, z) {
  if (x < y && x < z) return x;
  if (y < x && y < z) return y;
  else return z;
}

function editDist(str1, str2, m, n) {
  // Create a table to store results of subproblems
  var dp = [];

  // Fill d[][] in bottom up manner
  for (i = 0; i <= m; i++) {
    dp.push([]);
    for (j = 0; j <= n; j++) {
      // If first string is empty, only option is to
      // isnert all characters of second string
      if (i == 0) {
        dp[i][j] = j; // Min. operations = j
      }

      // If second string is empty, only option is to
      // remove all characters of second string
      else if (j == 0) {

        dp[i][j] = i; // Min. operations = i
      }

      // If last characters are same, ignore last char
      // and recur for remaining string
      else if (str1.charAt(i - 1) == str2.charAt(j - 1))
        dp[i][j] = dp[i - 1][j - 1];

      // If last character are different, consider all
      // possibilities and find minimum
      else
        dp[i][j] = 1 + min(dp[i][j - 1], // Insert
          dp[i - 1][j], // Remove
          dp[i - 1][j - 1]); // Replace
    }
  }

  return dp[m][n];
}

function preprocessSearch(search_text) {
  if (search_text) {
    search_text = search_text.trim();
    search_text = search_text.toLowerCase();
    var arr = search_text.split(/[0-9\s.:\t;,|#()\[\]\{\}!"/\\<>*=+\^?_`~\\&-]+/);
    arr = arr.filter(function (item) {
      item = item.trim();
      if (item.length > 0) {
        if (stopWords.indexOf(item) >= 0)
          return false;
        else {
          return true;
        }
      } else {
        return false;
      }
    });
    search_text = arr.join(' ').trim();
    search_text = search_text.trim();
  } else {
    search_text = '';
  }
  return search_text;
}

function isSimilar(prev_search, search_text) {
  prev_search = prev_search.toLowerCase();
  search_text = search_text.toLowerCase();
  var edist = editDist(prev_search, search_text, prev_search.length, search_text.length);
  if (edist <= 2) {
    return true;
  } else if (prev_search.length <= search_text.length) {
    if ((prev_search.length > 12 && edist <= Math.ceil(prev_search.length * .2)) || (search_text.indexOf(prev_search) >= 0)) {
      return true;
    }
  } else {
    if ((search_text.length > 12 && edist <= Math.ceil(search_text.length * .2)) || (prev_search.indexOf(search_text) >= 0)) {
      return true;
    }
  }
  return false;
}

function LCSubArray(X, Y) {
  X = X.filter(x => {
    return x.trim() != ''
  });
  Y = Y.filter(x => {
    return x.trim() != ''
  });
  // Find length of both the strings.
  var m = X.length;
  var n = Y.length;

  // Variable to store length of longest
  // common substring.
  var result = 0;

  // Variable to store ending point of
  // longest common substring in X.
  var end;

  // Matrix to store result of two
  // consecutive rows at a time.
  var len = [];

  // Variable to represent which row of
  // matrix is current row.
  var currRow = 0;

  // For a particular value of i and j,
  // len[currRow][j] stores length of longest
  // common substring in string X[0..i] and Y[0..j].
  for (var i = 0; i <= m; i++) {
    for (var j = 0; j <= n; j++) {
      if (typeof len[currRow] == 'undefined')
        len[currRow] = [];
      if (i == 0 || j == 0) {
        len[currRow][j] = 0;
      } else if (X[i - 1] == Y[j - 1]) {
        len[currRow][j] = len[1 - currRow][j - 1] + 1;
        if (len[currRow][j] > result) {
          result = len[currRow][j];
          end = i - 1;
        }
      } else {
        len[currRow][j] = 0;
      }
    }

    // Make current row as previous row and
    // previous row as new current row.
    currRow = 1 - currRow;
  }

  // If there is no common substring, print -1.
  if (result == 0) {
    return [];
  }

  // Longest common substring is from index
  // end - result + 1 to index end in X.
  return X.slice(end - result + 1, end + 1);
}

function findProbableIntents(cluster) {
  var strArray = cluster.text;
  if (strArray.length == 1)
    return [];
  var substrings = [];
  for (i = 0; i < strArray.length - 1; i++) {
    for (j = i + 1; j < strArray.length; j++) {
      var sub = LCSubArray(strArray[i].split(/[0-9\s.:\t;,|#()\[\]\{\}!"/\\<>*=+\^?_`~\\&-]+/), strArray[j].split(/[\s.:\t;,|#()\[\]\{\}!"/\\<>*=+\^?_`~\\&-]+/));
      sub = sub.join(' ');
      if (sub.trim() != '') {
        var ele = substrings.find((str) => {
          return str.intent == sub
        });
        if (ele) {
          ele.count++;
        } else {
          substrings.push({
            intent: sub,
            count: 1
          });
        }
      }
    }
  }
  return substrings.sort((a, b) => {
    return a.count > b.count
  });
}

function findBestMatch(searchQuery, topMatchingClusters) {
  var modifiedQuery = preprocessSearch(searchQuery);
  var maxScoreId = -1;
  var maxScore = 0;
  topMatchingClusters.map((cluster, idx) => {
    var score = cluster._source.text.filter(text => {
      return isSimilar(text, modifiedQuery);
    }).length;
    if (score > 0) {
      maxScoreId = score > maxScore ? idx : maxScoreId;
      maxScore = score > maxScore ? score : maxScore;
    }
  });
  if (maxScoreId != -1) {
    return maxScoreId;
  } else
    return 0;
}

function getMoreUntilDone(err, from, size, datain) {
  //console.log(JSON.stringify(err?err:data));
  //console.log(JSON.stringify(result));

  console.log('recieved data for batch ' + datain._scroll_id);
  if (err) {
    console.log(err);
    return;
  }
  if (datain.hits.hits.length > 0) {
    try {
      /**
       * for each search 
       *      pre process search to remove punctuations, stopwords, and extra spaces
       *      search for nearest matching cluster in search graph
       *      add search to that cluster node
       *      or create new if no matching cluster found
       */
      var asyncArray = [];

      asyncArray = datain.hits.hits.filter(hit => {
        var text = preprocessSearch(hit._source.text_entered);
        var length = text.trim().split(/[\s.:\t;,|#()\[\]\{\}!"/\\<>*=+\^?_`~\\&-]+/).length;
        return hit._source.text_entered.trim() != '' && length > 0 && length <= maxNumToken;
      }).map((hit, hitid, hitsarray) => {
        return function (searchcb) {
          var node = {};
          var originalQuery = hit._source.text_entered;
          console.log('===== ' + hit._source.text_entered);
          hit.processedQuery = preprocessSearch(originalQuery).trim();
          if (hit.processedQuery != '') {
            var conversions = hit._source.conversion;
            if (conversions) {
              conversions = conversions.map(function (c) {
                return c.es_id;
              });
            } else {
              conversions = [];
            }

            conversions.sort();
            var ik = -1;
            for (i = 1, ik = -1; i < conversions.length; i++) {
              if (conversions[i] != conversions[i - 1])
                ik++;
              conversions[ik] = conversions[i];
            }
            if (ik == -1)
              conversions = conversions.slice(0, conversions.length);
            else
              conversions = conversions.slice(0, ik + 1);
            hit.conversions = conversions;

            async.auto({
                "fetchTopMatchingCluster": function (cb) {
                  client.search({
                    index: graphIndex,
                    type: graphType,
                    from: 0,
                    size: 5,
                    body: {
                      "query": {
                        "bool": {
                          "should": [{
                            "match": {
                              "intents": {
                                "query": originalQuery,
                                "boost": 4
                              }
                            }
                          }, {
                            "match": {
                              "text": {
                                "query": originalQuery,
                                "fuzziness": "AUTO",
                                "prefix_length": 4
                              }
                            }
                          }],
                          "minimum_should_match": 1
                        }
                      }
                    }
                  }, (elerr, elres) => {
                    if (!elerr && elres.hits.hits.length > 0) {
                      cb(null, elres.hits.hits);
                    } else {
                      cb(null, []);
                    }
                  });
                },
                "processAndFindBestClusterId": ["fetchTopMatchingCluster", function (results, cb) {
                  console.log(results);
                  var source = {};
                  if (results.fetchTopMatchingCluster.length > 0) {
                    // find best matching cluster
                    var sourceId = findBestMatch(originalQuery, results.fetchTopMatchingCluster);
                    source = results.fetchTopMatchingCluster[sourceId]._source;

                    var modifiedSearch = hit.processedQuery;
                    if (source.text.indexOf(modifiedSearch) < 0) {
                      var syn = findProbableSynonym(modifiedSearch, source.text);

                      var bulkqueries = [];
                      syn.map(s => {
                        var id = s.text.split(',').sort().join(',');
                        s.active = true;
                        bulkqueries.push({
                          update: {
                            _index: synonymIndex,
                            _type: synonymType,
                            _id: id
                          }
                        });
                        bulkqueries.push({
                          "script": {
                            "inline": "ctx._source.count += count",
                            "params": {
                              "count": s.count
                            }
                          },
                          "upsert": s
                        });
                      });
                      if(bulkqueries.length>0)
                        client.bulk({
                          body: bulkqueries
                        }, (elerr, elres) => {

                        });

                      source.text.push(modifiedSearch);
                    }
                    conversions.map(function (currentConversions) {
                      var urlfound = false;
                      source.conversion = source.conversion.map(function (c) {
                        if (c.url == currentConversions) {
                          c.count++;
                          urlfound = true;
                        }
                        return c;
                      });
                      if (!urlfound) {
                        var cnode = {};
                        cnode.search_term = originalQuery;
                        cnode.url = currentConversions;
                        cnode.count = 1;
                        source.conversion.push(cnode);
                      }
                      return currentConversions;
                    });
                  } else {
                    source.clusterId = clid;
                    source.conversion = [];
                    source.text = [hit.processedQuery];
                    source.conversion = conversions.map(function (c) {
                      var cnode = {};
                      cnode.search_term = originalQuery;
                      cnode.url = c;
                      cnode.count = 1;
                      return cnode;
                    });
                    source.convCount = source.conversion.length;
                    clid++;
                  }
                  source.intents = findProbableIntents(source);
                  cb(null, source);
                }],
                "addClusterOrUpdate": ["processAndFindBestClusterId", function (results, cb) {
                  // update or add cluster to elastic
                  client.index({
                    index: graphIndex,
                    type: graphType,
                    id: results.processAndFindBestClusterId.clusterId,
                    body: results.processAndFindBestClusterId
                  }, (elerr, elres) => {
                    cb(null, 'updated');
                  });
                }]
              },
              function (autoerr, autores) {
                setTimeout(() => {
                  console.log('Processed: ' + queriesProcessed + '/' + asyncArray.length);
                  queriesProcessed++;
                  searchcb(null, autores);
                }, 1000);
              });
          } else {
            searchcb(null, 'updated');
          }
        };
      });
      async.series(asyncArray, (aerr, ares) => {
        console.log('All series task executed');
        if (datain.hits.total > (from + size)) {
          getBatch(from + size + 1, size, datain._scroll_id);
        }
      });
    } catch (e) {

    }
  } else {
    
  }
}

function findPattern() {
  async.waterfall([
    cb => {
      console.log('Checking if search graph exists');
      client.indices.exists({
        index: graphIndex
      }, function (err, res) {
        cb(null, err, res);
      });
    },
    (errprev, resultprev, cb) => {
      if (!errprev && resultprev) {
        console.log('deleting old search graph records');
        /**
         * TODO process of deleting search graph is not ideal 
         * and should be removed
         */
        if(!skipDeletingSearchGraph){
          client.indices.delete({
            index: graphIndex
          }, function (err, res) {
            console.log(err, res);
            cb(null, err, res);
          });
        }else{
          cb(null, errprev, resultprev);
        }
      } else if (!errprev && !resultprev) {
        console.log('Search grap not present will create new');
        cb(null, null, {
          notpresent: true,
          acknowledged: true
        });
      } else {
        console.log('error while checking search graph existence');
        cb(null, null, {
          notpresent: true,
          acknowledged: false
        });
      }
    },
    (errprev, resultprev, cb) => {
      if (resultprev.notpresent || (!skipDeletingSearchGraph && resultprev.acknowledged)) {
        console.log('Creating search graph index');
        client.indices.create({
            index: graphIndex,
            body: graphMapping
          },
          function (errmapping, resultmapping) {
            console.log('Created search graph index with mapping ', errmapping, resultmapping);
            cb(errmapping, resultmapping);
          }
        );
      }else{
        cb(null, {acknowledged: true});
      }
    }
  ], function (err, response) {
    if (!err && response.acknowledged) {
      console.log('looping through analytics data');
      var from = 0;
      var size = 50;
      getBatch(from, size);
    }
  });

}

function getBatch(from, size, next) {
  // Fetching all searches (batch of 'size') from analytics from last 90 days 
  if (!next) {
    client.search({
        index: searchDataIndex,
        type: searchDataType,
        size: size,
        from: from,
        scroll: '1h',
        body: {
          "query": {
            "bool": {
              "must": {
                "range": {
                  "search_date": {
                    "gte": daterange.from,
                    "lte": daterange.to
                  }
                }
              }
            }
          },
          "sort": {
            "cookie": {
              "order": "desc"
            },
            "search_date": {
              "order": "asc"
            }
          },
          "_source": {
            "includes": ["text_entered", "conversion", "search_date", "cookie"]
          }
        }
      },
      function (err, datain) {
        return getMoreUntilDone(err, from, size, datain);
      }
    );
  } else {
    client.scroll({
      scrollId: next,
      scroll: '1h'
    }, function (err, data) {
      return getMoreUntilDone(err, from, size, data);
    });
  }
}
// Execution Begins here
findPattern();

function isStopWord(word) {
  return false;
}

function excludeWordForSynonym(word) {
  if (!isNaN(word.trim()))
    return true;
  if (isStopWord(word))
    return true;
  return false;
}

function tokenizer(str) {
  var str0 = str.split(/[\s.:\t;,|#()\[\]\{\}!"/\\<>*=+\^?_`~\\&-]+/).join(" ");
  var s1 = str0.toLowerCase().replace(/(\b(\w{1,3})\b(\s|$))/g,'').split(" ").filter(a=>{return a.trim()!=''});
  return s1;
}

function findSimilarWords(strs) {
  if (strs.length >= 2) {
    for (var i = 0; i < strs.length; i++) {
      for (var j = i + 1; j < strs.length; j++) {
        var s1 = tokenizer(strs[i]);
        console.log("s1" + s1);

        var s2 = tokenizer(strs[j]);
        console.log("s2" + s2);

        var atLeastOneMatches = 0;
        var k = 0,
          l = 0;
        var flag = 0;
        while (k < s1.length) {
          var match = 0;
          while (l < s2.length) {
            if (k >= s1.length)
              break;
            s1[k] = excludeWordForSynonym(s1[k]) ? '' : s1[k];
            s2[l] = excludeWordForSynonym(s2[l]) ? '' : s2[l];
            if (s1[k] != '' && s2[l] != '') {
              if (s1[k] != s2[l]) {
                if (s1[k].indexOf(s2[l]) >= 0 || s2[l].indexOf(s1[k]) >= 0) {
                  s1 = s1.map(function (s) {
                    return s.indexOf(s1[k]) >= 0 ? s1[k] : s;
                  });
                  var idx = s1.lastIndexOf(s1[k]);
                  s1.splice(k, (idx - k) + 1);
                  s2 = s2.map(function (s) {
                    return s.indexOf(s2[l]) >= 0 ? s2[l] : s;
                  });
                  idx = s2.lastIndexOf(s2[l]);
                  s2.splice(l, (idx - l) + 1);
                  match++;
                  flag = 1;
                } else {
                  l++;
                }
              } else {
                var idx = s1.lastIndexOf(s1[k]);
                s1.splice(k, (idx - k) + 1);
                idx = s2.lastIndexOf(s2[l]);
                s2.splice(l, (idx - l) + 1);
                match++;
                flag = 1;
              }
            } else {
              if (s1[k] == '') {
                s1.splice(k, 1);
                flag = 1;
              }

              if (s2[l] == '') {
                s2.splice(l, 1);
              } else {
                l++;
              }
            }
          }
          if (!flag)
            k++;
          flag = 0;
          l = 0;
          if (match)
            atLeastOneMatches = 1;
        }
        if (atLeastOneMatches && s1.length == 1 && s2.length == 1) {
          //console.log(s1[0]+' ~ '+s2[0]);
          if (editDist(s1[0], s2[0], s1[0].length, s2[0].length) > 2) {
            // check if both the terms are not present together in any search query
            if (!wordsPresentInSameQuery(s1[0], s2[0], strs) && allowedSynonym(s1[0], s2[0]))
              similar.push([s1[0], s2[0], strs[i], strs[j], editDist(s1[0], s2[0], s1[0].length, s2[0].length)]);
          }
        }
      }
    }
  }
}

function findSimilarWords1(srcStr, strs) {
  var similar = [];
  if (strs.length >= 1) {
    var s2 = tokenizer(srcStr);
    if(s2.length<2)
      return similar;
    console.log("s2" + s2);
    for (var i = 0; i < strs.length; i++) {
      s2 = tokenizer(srcStr);
      var s1 = tokenizer(strs[i]);
      if(s1.length<2)
        continue;
      console.log("s1" + s1);
      var atLeastOneMatches = 0;
      var k = 0,
        l = 0;
      var flag = 0;
      while (k < s1.length) {
        var match = 0;
        while (l < s2.length) {
          if (k >= s1.length)
            break;
          s1[k] = excludeWordForSynonym(s1[k]) ? '' : s1[k];
          s2[l] = excludeWordForSynonym(s2[l]) ? '' : s2[l];
          if (s1[k] != '' && s2[l] != '') {
            if (s1[k] != s2[l]) {
              if (s1[k].indexOf(s2[l]) >= 0 || s2[l].indexOf(s1[k]) >= 0) {
                s1 = s1.map(function (s) {
                  return s.indexOf(s1[k]) >= 0 ? s1[k] : s;
                });
                var idx = s1.lastIndexOf(s1[k]);
                s1.splice(k, (idx - k) + 1);
                s2 = s2.map(function (s) {
                  return s.indexOf(s2[l]) >= 0 ? s2[l] : s;
                });
                idx = s2.lastIndexOf(s2[l]);
                s2.splice(l, (idx - l) + 1);
                match++;
                flag = 1;
              } else {
                l++;
              }
            } else {
              var idx = s1.lastIndexOf(s1[k]);
              s1.splice(k, (idx - k) + 1);
              idx = s2.lastIndexOf(s2[l]);
              s2.splice(l, (idx - l) + 1);
              match++;
              flag = 1;
            }
          } else {
            if (s1[k] == '') {
              s1.splice(k, 1);
              flag = 1;
            }

            if (s2[l] == '') {
              s2.splice(l, 1);
            } else {
              l++;
            }
          }
        }
        if (!flag)
          k++;
        flag = 0;
        l = 0;
        if (match)
          atLeastOneMatches = 1;
      }
      if (atLeastOneMatches && s1.length == 1 && s2.length == 1) {
        //console.log(s1[0]+' ~ '+s2[0]);
        if (editDist(s1[0], s2[0], s1[0].length, s2[0].length) > 2) {
          // check if both the terms are not present together in any search query
          if (!wordsPresentInSameQuery(s1[0], s2[0], strs) && allowedSynonym(s1[0], s2[0]))
            similar.push([s1[0], s2[0], strs[i], srcStr, editDist(s1[0], s2[0], s1[0].length, s2[0].length)]);
        }
      }
    }
  }
  return similar;
}

function wordsPresentInSameQuery(w1, w2, strs) {
  for (var i = 0; i < strs.length; i++) {
    if (strs[i].indexOf(w1) >= 0 && strs[i].indexOf(w2) >= 0)
      return true;
  }
}

function allowedSynonym(w1, w2) {
  if (noSynonymFor.indexOf(w1) >= 0 || noSynonymFor.indexOf(w2) >= 0)
    return false;
  return true;
}

function getMoreSearchClustersUntilDone(error, response, index, counter, callback1) {
  if (!error && typeof response.hits != 'undefined' &&
    response.hits.hits != 'undefined' && response.hits.hits.length > 0) {
    // preprocess and store to prediction IO event server
    var clusters = response.hits.hits;
    var clustersFunc = clusters.map(function (cluster) {
      return function (cb) {
        findSimilarWords(cluster._source.text);
        cb(null, 'success');
      }

    });
    async.parallelLimit(clustersFunc, 20, function (er, re) {
      console.log('finished with ' + counter + ' batch');
      counter++;
      console.log('requesting for next batch of data');
      client.scroll({
        scrollId: response._scroll_id,
        scroll: '2m'
      }, function (err, data) {
        return getMoreSearchClustersUntilDone(err, data, index, counter, callback1);
      });
    })

    /////////////////////////////////////////////////////

  } else {
    console.log('response end for request no. ' + counter);
    callback1();
    console.log('number of similar items found: ' + similar.length);
  }
}

function findSimilarTerms(callback) {
  client.search({
    index: config.get('elasticIndex.searchgraph'),
    size: 1000,
    from: 0,
    scroll: '30s'
  }, function (err, data) {
    return getMoreSearchClustersUntilDone(err, data, config.get('elasticIndex.searchgraph'), 1, callback);
  });
}

function findProbableSynonym(originalQuery, cluster) {
  // res.send(similar);
  var similar = findSimilarWords1(originalQuery, cluster);
  var map1 = {};
  var res1 = [];
  console.log("+++==>>", similar.length);
  for (var i = 0; i < similar.length; i++) {
    var str = similar[i][0] + "," + similar[i][1];
    var str1 = similar[i][1] + "," + similar[i][0];
    if (map1[str])
      map1[str]++;
    else if (map1[str1])
      map1[str1]++;
    else
      map1[str] = 1;

  }
  console.log(map1);
  var sortedMap = [];
  for (var key in map1) {
    sortedMap.push([key, map1[key]]);
  }
  console.log(sortedMap[0]);
  for (var i = 0; i < sortedMap.length; i++) {
    for (var j = i; j < sortedMap.length; j++) {
      if (sortedMap[i][1] < sortedMap[j][1]) {
        var c = [];
        c.push([sortedMap[i][0], sortedMap[i][1]])
        console.log(c[0]);
        sortedMap[i] = sortedMap[j];
        sortedMap[j] = c[0];
      }
    }
  }
  for (key in sortedMap) {
    var obj = {
      "text": sortedMap[key][0],
      "count": sortedMap[key][1]
    };
    res1.push(obj);
  }
  return res1;
}
