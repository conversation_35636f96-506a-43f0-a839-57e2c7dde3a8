const redisCacheModule = require('./redis/redis-cache');
const redisClientProvider = require('./redis-client-provider');
const getRequestOriginIp = require('./request-origin');
const requestCounterhashName = 'NUMBER_OF_REQUESTS_PER_USER';
const dosConfig = require('config').get('dosConfig');
const ipPrefix = 'BLOCKED_';
const blockOriginIpTime = dosConfig.get('blockOriginIpTimeInSeconds');
const requestThreshold =  dosConfig.get('requestThreshold');
const windowSizeInSeconds =  dosConfig.get('windowSizeInSeconds');
const forbiddenStatusCode = 403;
const endpoints = new Set(dosConfig.get('endpoints'));
var commonFunctions = require("./commonFunctions");
commonFunctions.errorlogger.log(process.env.NODE_ENV);

class RateLimiterHelper{

    async isIpBlocked(originIp){
        return await redisCacheModule.exists(redisClientProvider.getRedisClient(), ipPrefix+originIp);
    }

    isEndpointSupported(req){
        return endpoints.has(req.originalUrl);
    }

    extractUserIp(req){
        return getRequestOriginIp(req).userIP;
    }

    setForbiddenResponse(res)  {
        res.set('Content-Type','application/json').status(forbiddenStatusCode).send('{ "message":"request threshold limit breached."}');
    }

    isThresholdBreached(numberOfRequests) {
        return numberOfRequests && numberOfRequests >= requestThreshold ;
    }

    async incrAndSetTTL(originIp)  {
        await redisCacheModule.hIncrByOne(redisClientProvider.getRedisClient(), requestCounterhashName, originIp);
        const ttl = await redisCacheModule.ttlForKey(redisClientProvider.getRedisClient(), requestCounterhashName);
        if(ttl<0){
            await redisCacheModule.expireKey(redisClientProvider.getRedisClient(), requestCounterhashName, windowSizeInSeconds);
        }
    }

    async getNumberOfRequests(originIp){
        return await redisCacheModule.getKeyHash(redisClientProvider.getRedisClient(),requestCounterhashName,originIp);
    }

    async blockIp(originIp){
        await redisCacheModule.setKey(redisClientProvider.getRedisClient(), ipPrefix+originIp, blockOriginIpTime);
    }

    async processRequest(req, res) {
        commonFunctions.errorlogger.info('>>>>>>>>>>>processRequest', req.originalUrl);
        const originIp = this.extractUserIp(req);
        if(!originIp || originIp == ''){
            commonFunctions.errorlogger.info('unable to detect user ip.Blocking request');
            this.setForbiddenResponse(res);
            return false;
        }
        if(await this.isIpBlocked(originIp)){
            commonFunctions.errorlogger.info('origin ip is blocked, will not proccess request');
            this.setForbiddenResponse(res);
            return false;
        }else{
            const numberOfRequests = await this.getNumberOfRequests(originIp);
            if(this.isThresholdBreached(numberOfRequests)){
                commonFunctions.errorlogger.info('Threshold limit breached blocking origin ip');
                await this.blockIp(originIp);
                this.setForbiddenResponse(res);
                return false;
            }else{
                await this.incrAndSetTTL(originIp);
            }
        }
        return true;
    }
}

module.exports = new RateLimiterHelper();
