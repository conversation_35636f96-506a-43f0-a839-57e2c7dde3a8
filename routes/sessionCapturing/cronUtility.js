var crontab = require("crontab");
/*
  Method to create cron task 
  possible values:-
    0 - cron creation failed
    1 - cron service created successfully
*/
function createCron(fileToExecute, parameter, comment, callback) {
  crontab.load(function (err, crontab) {
    try{

      let allJobs = crontab.jobs({});
    crontab.remove(allJobs);
    let jobSet = Array.from(new Set(allJobs.toString().split(","))); //to get unique cron jobs

    for(let i = 0; i < jobSet.length; i++){
      let parsed = crontab.parse(jobSet[i]);
      let comm = parsed.command();
      let ren = parsed.render();
      let freq = ren.split(comm)[0];
      crontab.create(comm, freq, parsed.comment());
    }

      var job = crontab.create(fileToExecute, parameter, comment); //command,repetition,comment
      crontab.save(function (err, crontab) {
        if (!err)
          callback(1)
        else
          callback(0)
      });
    }catch(e){
      callback(0);
    }

  })
}

/*
  Method to delete cron task 
  possible values:-
    0 - cron deletion failed
    1 - cron service deleted successfully
*/
const deleteCron = function (parameter, callback) {

  crontab.load(function (err, crontab) {
    let job = crontab.jobs({ comment: parameter })
    crontab.remove(job);


    let allJobs = crontab.jobs({});
    crontab.remove(allJobs);
    let jobSet = Array.from(new Set(allJobs.toString().split(","))); //to get unique cron jobs

    for(let i = 0; i < jobSet.length; i++){
      let parsed = crontab.parse(jobSet[i]);
      let comm = parsed.command();
      let ren = parsed.render();
      let freq = ren.split(comm)[0];
      crontab.create(comm, freq, parsed.comment());
    }
    crontab.save(function (err, crontab) {
      if (!err)
        callback(1);
      else{
        console.error(err);
        callback(err);
      }
    });

  })
}

const editCron = function (url, parameter, comment, command, callback) {
  crontab.load(function (err, crontab) {
    let job = crontab.jobs({ comment: comment })
    crontab.remove(job);

    let allJobs = crontab.jobs({});
    crontab.remove(allJobs);
    if (!command)
      url = '/usr/bin/wget -O - ' + url;
    crontab.create(url, parameter, comment); //command,repetiotion,comment
    let jobSet = Array.from(new Set(allJobs.toString().split(","))); //to get unique cron jobs

    for(let i = 0; i < jobSet.length; i++){
      let parsed = crontab.parse(jobSet[i]);
      let comm = parsed.command();
      let ren = parsed.render();
      let freq = ren.split(comm)[0];
      crontab.create(comm, freq, parsed.comment());
    }
    crontab.save(function (err, crontab) {
      if (!err)
        callback(1);
      else{
        console.error(err);
        callback(err);
      }
    });

  })
}

exports.createCron = createCron;
exports.deleteCron = deleteCron;
exports.editCron   = editCron;