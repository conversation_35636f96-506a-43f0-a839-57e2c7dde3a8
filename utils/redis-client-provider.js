let {  createRedisClient, redisClient } = require('./redis/redis-client');


class RedisClientProvider {
    constructor() {
      if (!RedisClientProvider.instance) {
        RedisClientProvider.instance = this;
        this.client = redisClient;
      }
      return RedisClientProvider.instance;
    }

    getRedisClient(){
        if(this.client.closing){
            this.client  = createRedisClient();  
        }
        return  this.client;
    }

  
}

module.exports = new RedisClientProvider();
