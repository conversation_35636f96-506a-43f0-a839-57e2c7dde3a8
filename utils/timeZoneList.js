let timeZoneList = [];
timeZoneList = [{
        name: "(UTC-12:00) International Date Line West",
        detail: "Dateline Standard Time",
        timezone: 'Etc/GMT+12'
    },
    {
        name: "(UTC-11:00) Coordinated Universal Time-11 ",
        detail: "UTC-11",
        timezone: 'Pacific/Midway'
    },
    {
        name: "(UTC-10:00) Aleutian Islands",
        detail: "Aleutian Standard Time",
        timezone: 'US/Aleutian'
    },
    {
        name: "(UTC-10:00) Hawaii",
        detail: "Hawaiian Standard Time",
        timezone: 'US/Hawaii'
    },
    {
        name: "(UTC-09:30) Marquesas Islands",
        detail: "Marquesas Standard Time",
        timezone: 'Pacific/Marquesas'
    },
    {
        name: "(UTC-09:00) Alaska",
        detail: "Alaskan Standard Time",
        timezone: 'US/Alaska'
    },
    {
        name: "(UTC-09:00) Coordinated Universal Time-09",
        detail: "UTC-09",
        timezone: 'Etc/GMT+9'
    },
    {
        name: "(UTC-08:00) Baja California",
        detail: "Pacific Standard Time (Mexico)",
        timezone: 'America/Tijuana'
    },
    {
        name: "(UTC-08:00) Coordinated Universal Time-08",
        detail: "UTC-08",
        timezone: 'Etc/GMT+8'
    },
    {
        name: "(UTC-08:00) Pacific Time (US  Canada)",
        detail: "Pacific Standard Time",
        timezone: 'US/Pacific'
    },
    {
        name: "(UTC-07:00) Arizona",
        detail: "US Mountain Standard Time",
        timezone: 'US/Arizona'
    },
    {
        name: "(UTC-07:00) Chihuahua, La Paz, Mazatlan",
        detail: "Mountain Standard Time (Mexico)",
        timezone: 'America/Chihuahua'
    },
    {
        name: "(UTC-07:00) Mountain Time (US  Canada)",
        detail: "Mountain Standard Time",
        timezone: 'US/Mountain'
    },
    {
        name: "(UTC-06:00) Central America",
        detail: "Central America Standard Time",
        timezone: 'America/Chicago'
    },
    {
        name: "(UTC-06:00) Central Time (US  Canada)",
        detail: "Central Standard Time",
        timezone: 'US/Central'
    },
    {
        name: "(UTC-06:00) Easter Island",
        detail: "Easter Island Standard Time",
        timezone: 'Chile/EasterIsland'
    },
    {
        name: "(UTC-06:00) Guadalajara, Mexico City, Monterrey",
        detail: "Central Standard Time (Mexico)",
        timezone: 'America/Mexico_City'
    },
    {
        name: "(UTC-06:00) Saskatchewan",
        detail: "Canada Central Standard Time",
        timezone: 'Saskatchewan'
    },
    {
        name: "(UTC-05:00) Bogota, Lima, Quito, Rio Branco",
        detail: "SA Pacific Standard Time",
        timezone: 'America/Bogota'
    },
    {
        name: "(UTC-05:00) Chetumal",
        detail: "Eastern Standard Time (Mexico)",
        timezone: 'America/Cancun'
    },
    {
        name: "(UTC-05:00) Eastern Time (US  Canada)",
        detail: "Eastern Standard Time",
        timezone: 'US/Eastern'
    },
    {
        name: "(UTC-05:00) Haiti",
        detail: "Haiti Standard Time",
        timezone: 'America/Port-au-Prince'
    },
    {
        name: "(UTC-05:00) Havana",
        detail: "Cuba Standard Time",
        timezone: 'America/Havana'
    },
    {
        name: "(UTC-05:00) Indiana (East)",
        detail: "US Eastern Standard Time",
        timezone: 'US/East-Indiana'
    },
    {
        name: "(UTC-05:00) Turks and Caicos",
        detail: "Turks And Caicos Standard Time",
        timezone: 'America/Grand_Turk'
    },
    {
        name: "(UTC-04:00) Asuncion",
        detail: "Paraguay Standard Time",
        timezone: 'America/Asuncion'
    },
    {
        name: "(UTC-04:00) Atlantic Time (Canada)",
        detail: "Atlantic Standard Time",
        timezone: 'Canada/Atlantic'
    },
    {
        name: "(UTC-04:00) Caracas",
        detail: "Venezuela Standard Time",
        timezone: 'America/Caracas'
    },
    {
        name: "(UTC-04:00) Cuiaba",
        detail: "Central Brazilian Standard Time",
        timezone: 'America/Cuiaba'
    },
    {
        name: "(UTC-04:00) Georgetown, La Paz, Manaus, San Juan",
        detail: "SA Western Standard Time",
        timezone: 'America/Guyana'
    },
    {
        name: "(UTC-04:00) Santiago",
        detail: "Pacific SA Standard Time",
        timezone: 'America/Santiago'
    },
    {
        name: "(UTC-03:30) Newfoundland",
        detail: "Newfoundland Standard Time",
        timezone: 'Canada/Newfoundland'
    },
    {
        name: "(UTC-03:00) Araguaina",
        detail: "Tocantins Standard Time",
        timezone: 'America/Araguaina'
    },
    {
        name: "(UTC-03:00) Brasilia",
        detail: "E. South America Standard Time",
        timezone: 'America/Sao_Paulo'
    },
    {
        name: "(UTC-03:00) Cayenne, Fortaleza",
        detail: "SA Eastern Standard Time",
        timezone: 'America/Cayenne'
    },
    {
        name: "(UTC-03:00) City of Buenos Aires",
        detail: "Argentina Standard Time",
        timezone: 'America/Buenos_Aires'
    },
    {
        name: "(UTC-03:00) Greenland",
        detail: "Greenland Standard Time",
        timezone: 'America/Godthab'
    },
    {
        name: "(UTC-03:00) Montevideo",
        detail: "Montevideo Standard Time",
        timezone: 'America/Montevideo'
    },
    {
        name: "(UTC-03:00) Punta Arenas",
        detail: "Magallanes Standard Time",
        timezone: 'America/Punta_Arenas'
    },
    {
        name: "(UTC-03:00) Saint Pierre and Miquelon",
        detail: "Saint Pierre Standard Time",
        timezone: 'Indian/Reunion'
    },
    {
        name: "(UTC-03:00) Salvador",
        detail: "Bahia Standard Time",
        timezone: 'America/Bahia'
    },
    {
        name: "(UTC-02:00) Coordinated Universal Time-02",
        detail: "UTC-02",
        timezone: 'Etc/GMT-2'
    },
    {
        name: "(UTC-01:00) Azores",
        detail: "Azores Standard Time",
        timezone: 'Atlantic/Azores'
    },
    {
        name: "(UTC-01:00) Cabo Verde Is.",
        detail: "Cape Verde Standard Time",
        timezone: 'Atlantic/Cape_Verde'
    },
    {
        name: "UTC",
        detail: "UTC",
        timezone: 'UTC'
    },
    {
        name: "(UTC+00:00) Dublin, Edinburgh, Lisbon, London",
        detail: "GMT Standard Time",
        timezone: 'Europe/Dublin'
    },
    {
        name: "(UTC+00:00) Monrovia, Reykjavik",
        detail: "Greenwich Standard Time",
        timezone: 'Etc/UTC'
    },
    {
        name: "(UTC+00:00) Sao Tome",
        detail: "Sao Tome Standard Time",
        timezone: 'Africa/Sao_Tome'
    },
    {
        name: "(UTC+01:00) Casablanca",
        detail: "Morocco Standard Time",
        timezone: 'Africa/Casablanca'
    },
    {
        name: "(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna",
        detail: "Central European Time",
        timezone: 'Europe/Amsterdam'
    },
    {
        name: "(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague",
        detail: "Central Europe Standard Time",
        timezone: 'Europe/Belgrade'
    },
    {
        name: "(UTC+01:00) Brussels, Copenhagen, Madrid, Paris",
        detail: "Romance Standard Time",
        timezone: 'Europe/Paris'
    },
    {
        name: "(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb",
        detail: "Central European Standard Time",
        timezone: 'Europe/Sarajevo'
    },
    {
        name: "(UTC+01:00) West Central Africa",
        detail: "W. Central Africa Standard Time",
        timezone: 'Africa/Bangui'
    },
    {
        name: "(UTC+02:00) Amman",
        detail: "Jordan Standard Time",
        timezone: 'Asia/Amman'
    },
    {
        name: "(UTC+02:00) Athens, Bucharest",
        detail: "GTB Standard Time",
        timezone: 'Europe/Athens'
    },
    {
        name: "(UTC+02:00) Beirut",
        detail: "Middle East Standard Time",
        timezone: 'Asia/Beirut'
    },
    {
        name: "(UTC+02:00) Cairo",
        detail: "Egypt Standard Time",
        timezone: 'Africa/Cairo'
    },
    {
        name: "(UTC+02:00) Chisinau",
        detail: "E. Europe Standard Time",
        timezone: 'Europe/Chisinau'
    },
    {
        name: "(UTC+02:00) Damascus",
        detail: "Syria Standard Time",
        timezone: 'Asia/Damascus'
    },
    {
        name: "(UTC+02:00) Gaza, Hebron",
        detail: "West Bank Standard Time",
        timezone: 'Asia/Gaza'
    },
    {
        name: "(UTC+02:00) Harare, Pretoria",
        detail: "South Africa Standard Time",
        timezone: 'Africa/Harare'
    },
    {
        name: "(UTC+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius",
        detail: "FLE Standard Time",
        timezone: 'Europe/Helsinki'
    },
    {
        name: "(UTC+02:00) Jerusalem",
        detail: "Israel Standard Time",
        timezone: 'Asia/Jerusalem'
    },
    {
        name: "(UTC+02:00) Kaliningrad",
        detail: "Kaliningrad Standard Time",
        timezone: 'Europe/Kaliningrad'
    },
    {
        name: "(UTC+02:00) Khartoum",
        detail: "Sudan Standard Time",
        timezone: 'Africa/Khartoum'
    },
    {
        name: "(UTC+02:00) Tripoli",
        detail: "Libya Standard Time",
        timezone: 'Africa/Tripoli'
    },
    {
        name: "(UTC+02:00) Windhoek",
        detail: "Namibia Standard Time",
        timezone: 'Africa/Windhoek'
    },
    {
        name: "(UTC+03:00) Baghdad",
        detail: "Arabic Standard Time",
        timezone: 'Asia/Baghdad'
    },
    {
        name: "(UTC+03:00) Istanbul",
        detail: "Turkey Standard Time",
        timezone: 'Europe/Istanbul'
    },
    {
        name: "(UTC+03:00) Kuwait, Riyadh",
        detail: "Arab Standard Time",
        timezone: 'Asia/Kuwait'
    },
    {
        name: "(UTC+03:00) Minsk",
        detail: "Belarus Standard Time",
        timezone: 'Europe/Minsk'
    },
    {
        name: "(UTC+03:00) Moscow, St. Petersburg",
        detail: "Russian Standard Time",
        timezone: 'Europe/Moscow'
    },
    {
        name: "(UTC+03:00) Nairobi",
        detail: "E. Africa Standard Time",
        timezone: 'Africa/Nairobi'
    },
    {
        name: "(UTC+03:30) Tehran",
        detail: "Iran Standard Time",
        timezone: 'Asia/Tehran'
    },
    {
        name: "(UTC+04:00) Abu Dhabi, Muscat",
        detail: "Arabian Standard Time",
        timezone: 'Asia/Dubai'
    },
    {
        name: "(UTC+04:00) Astrakhan, Ulyanovsk",
        detail: "Astrakhan Standard Time",
        timezone: 'Europe/Astrakhan'
    },
    {
        name: "(UTC+04:00) Baku",
        detail: "Azerbaijan Standard Time",
        timezone: 'Asia/Baku'
    },
    {
        name: "(UTC+04:00) Izhevsk, Samara",
        detail: "Russia Time Zone 3",
        timezone: 'Europe/Samara'
    },
    {
        name: "(UTC+04:00) Port Louis",
        detail: "Mauritius Standard Time",
        timezone: 'Indian/Mauritius'
    },
    {
        name: "(UTC+04:00) Saratov",
        detail: "Saratov Standard Time",
        timezone: 'Europe/Saratov'
    },
    {
        name: "(UTC+04:00) Tbilisi",
        detail: "Georgian Standard Time",
        timezone: 'Asia/Tbilisi'
    },
    {
        name: "(UTC+04:00) Volgograd",
        detail: "Volgograd Standard Time",
        timezone: 'Europe/Volgograd'
    },
    {
        name: "(UTC+04:00) Yerevan",
        detail: "Caucasus Standard Time",
        timezone: 'Yerevan'
    },
    {
        name: "(UTC+04:30) Kabul",
        detail: "Afghanistan Standard Time",
        timezone: 'Asia/Kabul'
    },
    {
        name: "(UTC+05:00) Ashgabat, Tashkent",
        detail: "West Asia Standard Time",
        timezone: 'Asia/Ashgabat'
    },
    {
        name: "(UTC+05:00) Ekaterinburg",
        detail: "Ekaterinburg Standard Time",
        timezone: 'Asia/Yekaterinburg'
    },
    {
        name: "(UTC+05:00) Islamabad, Karachi",
        detail: "Pakistan Standard Time",
        timezone: 'Asia/Karachi'
    },
    {
        name: "(UTC+05:00) Qyzylorda",
        detail: "Qyzylorda Standard Time",
        timezone: 'Asia/Qyzylorda'
    },
    {
        name: "(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi",
        detail: "Indian Standard Time",
        timezone: 'Asia/Kolkata'
    },
    {
        name: "(UTC+05:30) Sri Jayawardenepura",
        detail: "Sri Lanka Standard Time",
        timezone: 'Asia/Colombo'
    },
    {
        name: "(UTC+05:45) Kathmandu",
        detail: "Nepal Standard Time",
        timezone: 'Asia/Kathmandu'
    },
    {
        name: "(UTC+06:00) Astana",
        detail: "Central Asia Standard Time",
        timezone: 'Asia/Jakarta'
    },
    {
        name: "(UTC+06:00) Dhaka",
        detail: "Bangladesh Standard Time",
        timezone: 'Asia/Dhaka'
    },
    {
        name: "(UTC+06:00) Omsk",
        detail: "Omsk Standard Time",
        timezone: 'Asia/Omsk'
    },
    {
        name: "(UTC+06:30) Yangon (Rangoon)",
        detail: "Myanmar Standard Time",
        timezone: 'Asia/Yangon'
    },
    {
        name: "(UTC+07:00) Bangkok, Hanoi, Jakarta",
        detail: "SE Asia Standard Time",
        timezone: 'Asia/Bangkok'
    },
    {
        name: "(UTC+07:00) Barnaul, Gorno-Altaysk",
        detail: "Altai Standard Time",
        timezone: 'Asia/Barnaul'
    },
    {
        name: "(UTC+07:00) Hovd",
        detail: "W. Mongolia Standard Time",
        timezone: 'Asia/Hovd'
    },
    {
        name: "(UTC+07:00) Krasnoyarsk",
        detail: "North Asia Standard Time",
        timezone: 'Asia/Krasnoyarsk'
    },
    {
        name: "(UTC+07:00) Novosibirsk",
        detail: "N. Central Asia Standard Time",
        timezone: 'Asia/Novosibirsk'
    },
    {
        name: "(UTC+07:00) Tomsk",
        detail: "Tomsk Standard Time",
        timezone: 'Asia/Tomsk'
    },
    {
        name: "(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi",
        detail: "China Standard Time",
        timezone: 'Asia/Shanghai'
    },
    {
        name: "(UTC+08:00) Irkutsk",
        detail: "North Asia East Standard Time",
        timezone: 'Asia/Irkutsk'
    },
    {
        name: "(UTC+08:00) Kuala Lumpur, Singapore",
        detail: "Singapore Standard Time",
        timezone: 'Asia/Kuala_Lumpur'
    },
    {
        name: "(UTC+08:00) Perth",
        detail: "W. Australia Standard Time",
        timezone: 'Australia/Perth'
    },
    {
        name: "(UTC+08:00) Taipei",
        detail: "Taipei Standard Time",
        timezone: 'Asia/Taipei'
    },
    {
        name: "(UTC+08:00) Ulaanbaatar",
        detail: "Ulaanbaatar Standard Time",
        timezone: 'Asia/Ulaanbaatar'
    },
    {
        name: "(UTC+08:45) Eucla",
        detail: "Aus Central W. Standard Time",
        timezone: 'Australia/Eucla'
    },
    {
        name: "(UTC+09:00) Chita",
        detail: "Transbaikal Standard Time",
        timezone: 'Asia/Chita'
    },
    {
        name: "(UTC+09:00) Osaka, Sapporo, Tokyo",
        detail: "Tokyo Standard Time",
        timezone: 'Asia/Tokyo'
    },
    {
        name: "(UTC+09:00) Pyongyang",
        detail: "North Korea Standard Time",
        timezone: 'Asia/Pyongyang'
    },
    {
        name: "(UTC+09:00) Seoul",
        detail: "Korea Standard Time",
        timezone: 'Asia/Seoul'
    },
    {
        name: "(UTC+09:00) Yakutsk",
        detail: "Yakutsk Standard Time",
        timezone: 'Asia/Yakutsk'
    },
    {
        name: "(UTC+09:30) Adelaide",
        detail: "Cen. Australia Standard Time",
        timezone: 'Australia/Adelaide'
    },
    {
        name: "(UTC+09:30) Darwin",
        detail: "AUS Central Standard Time",
        timezone: 'Australia/Darwin'
    },
    {
        name: "(UTC+10:00) Brisbane",
        detail: "E. Australia Standard Time",
        timezone: 'Australia/Brisbane'
    },
    {
        name: "(UTC+10:00) Canberra, Melbourne, Sydney",
        detail: "AUS Eastern Standard Time",
        timezone: 'Australia/Sydney'
    },
    {
        name: "(UTC+10:00) Guam, Port Moresby",
        detail: "West Pacific Standard Time",
        timezone: 'Pacific/Guam'
    },
    {
        name: "(UTC+10:00) Hobart",
        detail: "Tasmania Standard Time",
        timezone: 'Australia/Hobart'
    },
    {
        name: "(UTC+10:00) Vladivostok",
        detail: "Vladivostok Standard Time",
        timezone: 'Asia/Vladivostok'
    },
    {
        name: "(UTC+10:30) Lord Howe Island",
        detail: "Lord Howe Standard Time",
        timezone: '"Australia/Lord_Howe'
    },
    {
        name: "(UTC+11:00) Bougainville Island",
        detail: "Bougainville Standard Time",
        timezone: 'Pacific/Bougainville'
    },
    {
        name: "(UTC+11:00) Chokurdakh",
        detail: "Russia Time Zone 10",
        timezone: 'Asia/Srednekolymsk'
    },
    {
        name: "(UTC+11:00) Magadan",
        detail: "Magadan Standard Time",
        timezone: 'Asia/Magadan'
    },
    {
        name: "(UTC+11:00) Norfolk Island",
        detail: "Norfolk Standard Time",
        timezone: 'Pacific/Norfolk'
    },
    {
        name: "(UTC+11:00) Sakhalin",
        detail: "Sakhalin Standard Time",
        timezone: 'Asia/Sakhalin'
    },
    {
        name: "(UTC+11:00) Solomon Is., New Caledonia",
        detail: "Central Pacific Standard Time",
        timezone: 'Pacific/Noumea'
    },
    {
        name: "(UTC+12:00) Anadyr, Petropavlovsk-Kamchatsky",
        detail: "Russia Time Zone 11",
        timezone: 'Asia/Anadyr'
    },
    {
        name: "(UTC+12:00) Auckland, Wellington",
        detail: "New Zealand Standard Time",
        timezone: 'Pacific/Auckland'
    },
    {
        name: "(UTC+12:00) Coordinated Universal Time+12",
        detail: "UTC+12",
        timezone: 'Etc/GMT-12'
    },
    {
        name: "(UTC+12:00) Fiji",
        detail: "Fiji Standard Time",
        timezone: 'Pacific/Fiji'
    },
    {
        name: "(UTC+12:45) Chatham Islands",
        detail: "Chatham Islands Standard Time",
        timezone: 'Pacific/Chatham'
    },
    {
        name: "(UTC+13:00) Nuku’alofa",
        detail: "Tonga Standard Time",
        timezone: 'Pacific/Tongatapu'
    },
    {
        name: "(UTC+13:00) Coordinated Universal Time+13",
        detail: "UTC+13",
        timezone: 'Etc/GMT-13'
    },
    {
        name: "(UTC+13:00) Samoa",
        detail: "Samoa Standard Time",
        timezone: 'Pacific/Apia'
    },
    {
        name: "(UTC+14:00) Kiritimati Island",
        detail: "Line Islands Standard Time",
        timezone: 'Pacific/Kiritimati'
    }
]

module.exports = timeZoneList;