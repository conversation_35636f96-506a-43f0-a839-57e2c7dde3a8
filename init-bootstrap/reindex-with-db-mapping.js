var path = require('path');
environment = require('../routes/environment');
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../config');
process.env.NODE_CONFIG_DIR = configPath;
config = require('config');
var async = require('async');
var request = require("request");
var fs = require('fs');
var commonFunctions = require('../utils/commonFunctions');
var connection_sql = require('../utils/connection');
commonFunctions.errorlogger.info(environment.configuration);
var contentSourceIds = [];
var mappingFile = fs.createWriteStream("reindex_mapping.bat");
mappingFile.write(`set -e\n`);

//SQL issue
connection_sql.handleDisconnect().then(function (result) {

  async.parallelLimit(
    contentSourceIds.map(contentSourceId => {
      return (cbin) => {
        commonFunctions.getMappingString(contentSourceId, 0, function (err, result) {
          if (!err)
            cbin(null, result)
          else
            cbin(null, result) //error handled
        })
      }
    }), 5,
    (error, result) => {
      result.map(function (content) {
        if(content!=null){
          var index = content.indexName;
          var newMapping = content.mapping;
          mappingFile.write(`curl -XPUT ${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/${index}_update -d'${JSON.stringify(newMapping)}'\n`)
          mappingFile.write(`curl -XPOST ${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/_reindex -d'{"source":{"index":"${index}"},"dest":{"index":"${index}_update"}}'\n`)
          mappingFile.write(`curl -XPOST ${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/${index}_update/_refresh\n`)
          mappingFile.write(`curl -XDELETE  ${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/${index}\n`);
          mappingFile.write(`curl -XPUT ${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/${index} -d'${JSON.stringify(newMapping)}'\n`)
          mappingFile.write(`curl -XPOST ${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/_reindex -d'{"source":{"index":"${index}_update"},"dest":{"index":"${index}"}}'\n`)
          mappingFile.write(`curl -XPOST ${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/${index}/_refresh\n`)
          //mappingFile.write(`curl -XDELETE  ${config.get('elasticIndexCS.host')}:${config.get('elasticIndexCS.port')}/${index}_update\n`);
        }
      })
    }
  )

});
