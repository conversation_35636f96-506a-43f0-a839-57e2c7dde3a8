var express = require('express');
var router = express.Router();
var async = require('async');
var request = require("request");
var fs = require('fs');
var commonFunctions = require('./../../utils/commonFunctions');
var constants = require('../../constants/constants');
var appVariables = require('../../constants/appVariables');
const config = require('config');




// Called from customContentSource.ts to parse the query and response parameters and save theminto the db table `api_json_fields`.
router.post('/parseResponse', function(req, res, next){
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    let body = {
        contentSourceId: req.body.content_source_id,
        tag: req.body.tag,
        tenantId: req.headers['tenant-id'],
        data: JSON.parse(req.body.jsonData)
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/api-cs/process-json-fields', '', body, headers, ( err, result) =>{
        res.send({"items": result.data.fields});
    });
});

router.get('/getApiJsonFields', function(req, res, next){
    let headers = {
        "Content-Type": "application/json",
        "su-crawler-secret" : config.get("crawler.sharedSecret"),
        "tenant-id": req.headers['tenant-id'] 
    }
    let body = {
        contentSourceId: req.query.contentSourceId,
        tag: req.query.tag
    }
    commonFunctions.httpRequest('POST', config.get("crawler.crawlerUrl") + '/content-source/api-cs/get-json-fields', '', body, headers, ( err, result) =>{
        var resultArray = [];
        result.data.fields.forEach(x=>resultArray.push(x.label));
        res.send({"items":resultArray});
    });
})


// Parse the json Data by calling ParseJson function
// Delete parsed fields if already saved in `api_json_fields` table.
// After parsing the result (tag and values of json parsed result ) has been inserted into `api_json_fields`.
const getFieldsFromResponse = function(jsonData, contentSourceId, tag, callback){
    if(typeof jsonData === 'string'){
        jsonData = JSON.parse(jsonData);
    }
    ParseJson(jsonData, "", [],req, function(err, data){
        var val = "";
        for(var i=0; i<data.length; i++){
            val = val +"("+contentSourceId+",'"+data[i]+"','"+tag+"'),"
        }
        val = val.substr(0, val.length-1);
        var array = [contentSourceId, data[0]];
        const sql = "DELETE FROM `api_json_fields` where content_source_id = "+contentSourceId+" AND tag = '"+tag+"'";
       connection.query(sql, function(err, data){
            if(!err){
                const sql = "INSERT INTO `api_json_fields` (`content_source_id`, `label`, `tag`) VALUES "+val;
               connection.query(sql, function(err,data){
                    if(err){
                        commonFunctions.errorlogger.error("Error in inserting into api_json_fields");
                        callback(null, err);
                    }
                    else{
                        callback(null, data);
                    }
                });
            }
            else{
                callback(null, err);
            }
        })
    })
}


// Parse the json Data
const ParseJson = function(jsonData, start , array,req, callback) {
    if(jsonData!=null){
        if (typeof jsonData === 'object') {
            var x;
            if(Array.isArray(jsonData)==true){
                x = Object.keys(jsonData[0]);            
            }
            else{
                x = Object.keys(jsonData);            
            }
            var y =[];
            x.forEach(temp=>{
                if(start==""){
                    array.push(temp);
                    y = x;
                }
                else{
                    array.push(start+'.'+temp);
                    y.push(start+'.'+temp);
                }
            })        
            moreTask = [];
            for(var i = 0; i < x.length; i++){3
                moreTask.push(function(i){
                    return function(cb){
                        var more_jsonData = {};
                        if(typeof jsonData[x[i]]=='object'){
                            if(Array.isArray(jsonData[x[i]])==true){
                                more_jsonData = jsonData[x[i]][0];
                            }
                            else{
                                more_jsonData = jsonData[x[i]];
                            }
                        }
                        else{
                            more_jsonData = jsonData[x[i]];
                        }
                        ParseJson(more_jsonData, y[i], array, function(err, data){
                            cb(null, array);
                        })
                    }
                }(i))
            }
            async.series(moreTask , function(erre, data){
                callback(null, array);
            })
        }
        else {
            callback(null, array);
        }
    }
    else{
        callback(null, array);
    }
}


//Used to validate json
const isValidJson = function(json) {
    try {
        JSON.parse(json);
        return true;
    } catch (e) {
        return false;
    }
}



module.exports = {
    router: router,
    isValidJson:isValidJson,
    ParseJson:ParseJson,
    getFieldsFromResponse:getFieldsFromResponse
}