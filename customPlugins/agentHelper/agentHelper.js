/* 
    Created By V<PERSON><PERSON><PERSON>
*/
const fs = require('fs');
const moment = require('moment');
const docSimilarity = require('doc-similarity');
var path = require('path');
var request = require("request");
var async = require('async');
const kafkaLib = require("../../utils/kafka/kafka-lib");
const jsep = require("jsep").addBinaryOp("AND", 10).addBinaryOp("OR", 10);
environment = require('../../routes/environment');
process.env.NODE_ENV = environment.configuration;
var configPath = path.join(__dirname, '../../config');
process.env.NODE_CONFIG_DIR = configPath;
console.log(environment.configuration);
config = require('config');
const elastic = require('elasticsearch');
const commonFunctions = require('../../utils/commonFunctions');
const sendEmail = require('../../Lib/email').sendEmail;
const { emitCompleteStatus } = require('../../utils/socket');
// const { isCloudFrontSupported } =  require('config').get('CloudStorageConfig.storage');

const { isCloudFrontSupported } =  require('config').get('cloudStorageConfig');

var  esclient = new elastic.Client({ host: `http://${config.get("elasticIndexCS.host")}:${config.get("elasticIndexCS.port")}` });
// Gets the data from agentHelperConfig file in json format
getAgentHelperConfigurations();
function getAgentHelperConfigurations() {
    fs.readFile(__dirname + '/agentHelperConfig.json.ignore', 'utf8', (error, txt)=>{
        if(error)
            getDataFromElastic({});
        else{
            try{
                getDataFromElastic(JSON.parse(txt));
            } catch(ex){
                console.error(ex);
                getDataFromElastic({});
            }
        }
    });
}

// get data from elastic
function getDataFromElastic(rawdata) {
    let keys = Object.keys(rawdata);
    let asyncTask = [];
    for (let i = 0; i < keys.length; i++) {
        console.log("keys", keys);
        console.log("rawdata[keys[0]]", rawdata[keys[0]]);
        let IndexName = rawdata[keys[i]].Source_object.parent;
        asyncTask.push(getElasticData.bind(null, IndexName, i,rawdata));
    }
    async.series(asyncTask,  function (err, result) {
        if (err) {
            console.error(err);
            // callback(err, null);
        } else {
            console.log("done");
            let finalObject = {};
            result.forEach(client=>{mergeObject(finalObject, client);});
            createSets(finalObject, rawdata);
            let GroupsData = JSON.stringify(finalObject);
            fs.writeFile(__dirname + '/agentHelperGroups.json.ignore', GroupsData, 'utf8', function (err) {
                if (err) {
                    console.error(err);
                    // res.send(err); 
                }
                else{
                    console.log("The file was saved!");
                    // res.send("Saved");
                }
            });
        }
    });
}

function getElasticData(IndexName, key,rawdata,callback) {
    let dsl = {"query":{"bool":{"must":[
        {"exists":{"field":"caseComments"}}
    ]}}};
    esclient.search({
        index:IndexName,
        size: 2000,
        body:{"query":{"bool":{"must":{"exists":{"field":"caseComments"}}}}}
    }, (error,resp)=>{
        if(error || resp.error)
            console.error(error || resp.error);
        else
            createGroups(resp.hits.hits, rawdata, key, function (err1, data1) {
                callback(err1, data1);
            });
    });
    let options = {
        method: "POST",
        url: "http://" + config.get('elasticIndexCS.host') + ":" + config.get('elasticIndexCS.port') + "/" + IndexName + '/_search',
        headers: {'content-type': 'application/json'}
    };
    /*request(options, function (error, result) {
        if (error) {
            console.error("error", error);
        } else {
            let response = JSON.parse(result.body);
            
            createGroups(response.hits.hits, rawdata, key, function (err1, data1) {
                callback(err1, data1);
            });
        }
    });*/
}

const createGroups = function (response, rawdata, key, callback) {
    // getDifference(response.hits.hits, rawdata, keys);
    let keys = Object.keys(rawdata);
    async.auto({
        getDifference: function (cb) {
            getDifference(response, rawdata, keys, key, function (err, result) {
                if (err) {
                    console.error(err);
                    cb(err, null);
                } else {
                    cb(null, result);
                }
            });
        },
        getGroups: ['getDifference', function (data, cb) {
            getGroups(data.getDifference, response, rawdata, key, function (err, result) {
                if (err) {
                    console.error(err);
                    cb(err, null);
                } else {
                    cb(null, result);
                }
            });
        }],
        getUniqueGroups: ['getGroups', function (data, cb) {
            getUniqueGroups(data.getGroups,rawdata, function (err, result) {
                if(err){
                    console.error(err);
                    cb(err, null);
                }else{
                    cb(null, result);
                }
            });
        }]
    }, function (err, result) {
        if (err) {
            console.error(err);
            callback(err, null);
        } else {
            console.log("data",result);
            let keys = Object.keys(rawdata);
            let data = {};
            // for (let i = 0; i < keys.length; keys++) {
                data[keys[key]] = result.getUniqueGroups;
            // }
            callback(null, data);
        }
    });
}

// find the difference between the 2 case subjects  
function getDifference(data, rawdata, keys, key, cb) {
    let minMatch = 0.76;
    let diff = [];
    let groups = [];
    for (let inputField = 0; inputField < rawdata[keys[key]].Input_Fields.length; inputField++) {
        diff[inputField] = [];
        for (let i = 0; i < data.length; i++) {
            diff[inputField][i] = [];
            groups[i] = [data[i]];
            for (let j = 0; j < i; j++) {
                let InputField = rawdata[keys[key]].Input_Fields[inputField].name;
                if (typeof data[i]["_source"][InputField] === "string" && typeof data[j]["_source"][InputField] === "string") {
                    diff[inputField][i][j] = docSimilarity.wordFrequencySim(data[i]["_source"][InputField], data[j]["_source"][InputField], docSimilarity.cosineSim);
                }
            }
        }
    }
    cb(null, diff);
    // getGroups(diff, data,rawdata);
}

function getGroups(diff, data, rawdata,key,cb) {
    let keys = Object.keys(rawdata);
    let Input_Fields = rawdata[keys[key]].Input_Fields;
    let minMatch = 0.76;
    let groups = [];
    let cases = data;
    for (let inputField = 0; inputField < Input_Fields.length; inputField++) {
        for (let i = 0; i < cases.length; i++) {
            groups[i] = [cases[i]];
            for (let j = 0; j < i; j++) {
                if (diff[inputField][i][j] >= minMatch) {
                    groups[j].forEach(c => {
                        if (groups[i].indexOf(c) === -1) {
                            groups[i].push(c);
                        }
                    });
                    groups[j] = groups[i];
                }
            }
        }
    }
    cb(null, groups);
    // getUniqueGroups(groups,rawdata);
}

function getUniqueGroups(groups, rawdata,cb) {
    let uniqueGroups = [];
    // for(let i =0;i<groups.length; i++){
    groups.forEach(g => {
        if (uniqueGroups.indexOf(g) === -1) {
            uniqueGroups.push(g);
        }
    });
    // }
    cb(null, uniqueGroups);
    // checkAgentHelperGroups(data);
    console.log("*************groups*************", JSON.stringify(uniqueGroups));
}

function checkAgentHelperGroups(data) {
    let rawdata = fs.readFileSync(__dirname + '/agentHelperGroups.json.ignore', 'utf8');
    let groups = JSON.parse(rawdata);
    let flag = false;
    for (let i = 0; i < Object.keys(rawdata).length; i++) {
        if (rawdata[Object.keys(rawdata)[i]] == data[Object.keys(data)[0]]) {

        }
    }
}

function createSets(results, config){
    for(let client in results){
        let groups = results[client];
        let Input_Fields = config[client].Input_Fields.map(field=>field.name);
        results[client] = groups.map(group=>{
            let words = new Set();
            let outputs = [];
            group.forEach(doc=>{
                Input_Fields.forEach(field=>{
                    if(!doc._source[field]){
                        doc._source[field] = "";
                    }
                    doc._source[field].split(/\W+/g)
                    .map(w=>w.toLowerCase())
                    .forEach(w=>words.add(w));
                });
                if(config[client].Output_Field==='caseComments' && typeof doc._source[config[client].Output_Field] != "string"){
                    doc._source[config[client].Output_Field] = doc._source[config[client].Output_Field].sort((a,b)=>moment(a.createdDate)-moment(b.createdDate))[0].body;
                }
                outputs.push({
                    text : doc._source[config[client].Output_Field],
                    caseId : doc._source.Id,
                    CaseNumber : doc._source[`${config[client]["Source_object"]["parent"]}___${config[client]["Source_object"]["child"]}___CaseNumber`] || null
                });
            });
            words = Array.from(words).join(" ");
            let resultDiffs = [], temp = [];
            for (let i = 0; i < outputs.length; i++) {
                resultDiffs[i]=[];
                temp[i] = {"doc":outputs[i],sum:0};
                for (let j = 0; j < outputs.length; j++) {
                    if(i===j)
                        resultDiffs[i][j] = 1;
                    else if(j<i)
                        resultDiffs[i][j] = resultDiffs[j][i];
                    else
                        resultDiffs[i][j] = docSimilarity.wordFrequencySim(outputs[i].text, outputs[j].text, docSimilarity.cosineSim);
                    temp[i].sum += resultDiffs[i][j];
                }
            }
            outputs = temp.sort((a,b)=>{a.sum-b.sum;}).map(d=>d.doc);
            return {words,outputs};
        });
    }
    return results;
}

function mergeObject(final, newObj){
    for(let key in newObj){
        final[key] = newObj[key];
    }
}



// new APIS for agent helper
const checkExpression = (expression) => {
    return expression === 'AND' || expression === 'OR' || /\d/.test(expression);
}

const validateSyntax = (config) => {
    try{
        if(config && config.Formula && config.Formula.Expression){
             jsep(" " + config.Formula.Expression + " ");
            const expressionCounts= config.Formula.Expression.match(/\d+/g);
            if (!expressionCounts.every(eC => config.Formula.Condition.length >= parseInt(eC) ) || !expressionCounts.every(eC => eC != 0 ) ){
                throw new Error('count_mismatch');
            }
            if(config.Formula.Expression.length <= 1 || !config.Formula.Expression.split(' ').every(checkExpression)){
                throw new Error('invalid_syntax')
            }
        }
    }catch(error){
        if(error.message === 'count_mismatch'){
            throw new Error('count_mismatch')
        }else{
            throw new Error('invalid_syntax')
        }
    }
}

function kafkaData(topic, data){
    try{
        let key = data && data.data && data.data.uid;
        if (!key) {
            console.log(
                "Warning: UID is not available in the data object. The message will be sent without a specific key Agent Helper data sent to search-client service by Kafka"
            );
        }
      kafkaLib.publishMessage({
          topic,
          messages:[{
              value:JSON.stringify(data),
              key: key
          }]
      })
      console.log('Agent Helper data sent to search-client service by Kafka ')
  }catch(err){
    console.log('Error while sending Agent Helper data to search-client service by Kafka => ', err)
  }
}

const changeTrainStatus = (uid, tenantId,) => {
    const query = `Update agenthelper Set training_status = 1 where uid = ? and tenant_id = ?`
    connection[tenantId].execute.query(query, [uid, tenantId], (error, doc) => {
        if(!error){
            return ({message: `Train status changed for ${uid}`});
        }
    })
};

const changeTrainStatusToInProgress = (uid, tenantId,) => {
    const query = `Update agenthelper Set training_status = 2 where uid = ? and tenant_id = ?`
    connection[tenantId].execute.query(query, [uid, tenantId], (error, doc) => {
        if(!error){
            return ({message: `Train status changed for ${uid}`});
        }
    })
};

const saveAgentHelperConfigurations = (body) => {
    return new Promise(async (resolve, reject)=>{
        try{
            const { uid, tenantId, type, scName, newConfigurations,search_uid_AH,search_uid_RA } = body;
        await validateSyntax(body.configurations);
        const configuration = JSON.stringify(body.configurations);
        const insertConfigQuery  = `INSERT INTO agenthelper(uid, tenant_id, configuration, type, search_client_name, search_uid_AH, search_uid_RA) VALUES(?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE uid=VALUES(uid), tenant_id=VALUES(tenant_id), configuration=VALUES(configuration), type=VALUES(type), search_uid_AH=VALUES(search_uid_AH), search_uid_RA=VALUES(search_uid_RA);`
        connection[tenantId].execute.query(insertConfigQuery, [uid, tenantId, configuration, type, scName, search_uid_AH, search_uid_RA], (error, doc) => {
            if(!error){
                if(!newConfigurations){
                    changeTrainStatus(uid, tenantId,);
                }
                let data = { task: 'updateAgentHelperFile', isCloudFrontSupported, data: { tenantId, uid } }
                const topic = config.get("kafkaTopic.searchClientTopic");   
                kafkaData(topic, data);
                resolve({message: `Configuration saved for uid: ${uid}`});
            }else{
                console.error("Error while saving agent helper configuration: ", error);
                reject({message:"Error while saving agent helper configuration"});
            }
        });
        }
        catch(error){
            reject({message: error.message})
        }
    })
   
};

const getAgentHelperDetails = (body) => {
    return new Promise((resolve, reject)=>{
        const { uid, tenantId } = body;
        const query = "SELECT * FROM agenthelper WHERE uid = ? AND tenant_id = ?";
        connection[tenantId].execute.query(query, [uid, tenantId], (error, doc) => {
            if(!error){
                resolve({data: doc[0], message: "Agent helper configurations"});
            }else{
                reject({data: {}, message:"Error while getting agent helper configuration"});
            }
        })
    });
}

const setValueAccordingToType = (type, value) => {
    switch(type){
        case 'datetime':
            return new Date(value);
        default:
            return value
    }
};

const createDataConditionsObject = (conf) => {
    let formula = conf.Formula;
    let createDSLExpression = (jstree)=>{
        if(jstree.type === "BinaryExpression"){
            let dsl = { $and: [] };
            let arr = [
                createDSLExpression(jstree.left),
                createDSLExpression(jstree.right)
            ];
            if(jstree.operator==="AND")
                dsl.$and.push({ $and: arr });
            else if(jstree.operator==="OR")
                dsl.$and.push({ $or: arr });
            return dsl;
        }
        else if(jstree.type === "Literal"){
            let condition = formula.Condition[jstree.value-1];
            let dsl_condition = {};
            let fieldType = { 'type': condition.fieldType };
            if(condition.operator === ">" || condition.operator === "greater than"){
                dsl_condition = { [condition.Field]: { '$gt': setValueAccordingToType(condition.fieldType, condition.value), ...fieldType} }
            }
            else if(condition.operator === "<" || condition.operator === "less than"){
                dsl_condition = { [condition.Field]: { $lt: setValueAccordingToType(condition.fieldType, condition.value), ...fieldType } }
            }
            else if(condition.operator === "=" || condition.operator === "equals"){
                dsl_condition = { [condition.Field]: setValueAccordingToType(condition.fieldType, condition.value), ...fieldType }
            }
            else if(condition.operator === "Contains"){
                dsl_condition = { [condition.Field]: { $regex: setValueAccordingToType(condition.fieldType, condition.value), $options: "i" } }
            }
            else if(condition.operator === "!=" || condition.operator === "not equal"){
                dsl_condition = { [condition.Field]: { $ne: setValueAccordingToType(condition.fieldType, condition.value), ...fieldType } }
            }
            return dsl_condition;
        }
    };
    let query;
    if(formula.Expression){
        query = createDSLExpression(jsep(formula.Expression));
    }
    // else{
    //     query = createDSLExpression(jsep(formula.Condition.map((e,i)=>i+1).join(" AND ")));
    // }
    return query;
}
    


const trainAgentHelper = async (body) => {
    try{
        const { uid, tenantId, tpk } = body;
        await changeTrainStatusToInProgress(uid, tenantId);
        const agentHelperData = await getAgentHelperDetails(body);
        if(agentHelperData && agentHelperData.data && agentHelperData.data.configuration){
            agentHelperData.data.configuration = JSON.parse(agentHelperData.data.configuration);
        };
        const customObject = createDataConditionsObject(agentHelperData.data.configuration);
        if(customObject && agentHelperData.data && agentHelperData.data.configuration){
            agentHelperData.data.configuration.customObject = customObject;
        };
        const trainingObject = {
            type:"ADD",
            uid,
            csObject: agentHelperData.data.configuration,
            tenantId,
            tpk
        }
        const trainingTopic = config.get('kafkaTopic.agentHelperTrainingTopic');
        kafkaData(trainingTopic, trainingObject);
        return {message: "Training started"};
    }catch(error){
        throw new Error(error);
    }
};


const deleteAgentHelperConfigurations = (body) => {
    return new Promise((resolve, reject)=>{
        const { uid, tenantId, tpk } = body;
        const query = "DELETE FROM agenthelper WHERE uid = ? AND tenant_id = ?";
        connection[tenantId].execute.query(query, [uid, tenantId], (error, doc) => {
            if(!error){
                const trainingObject = {
                    type:"DELETE",
                    uid,
                    tenantId,
                    tpk
                }
                let data = { task: 'updateAgentHelperFile', isCloudFrontSupported, data: { tenantId, uid } };
                const topic = config.get("kafkaTopic.searchClientTopic");   
                const trainingTopic = config.get('kafkaTopic.agentHelperTrainingTopic');
                kafkaData(trainingTopic, trainingObject);
                kafkaData(topic, data);
                resolve({ message: "Configuration deleted" });
            }
            console.error("Error while deleting configurations: ", error);
            reject({message: "Error while deleting configurations"})
        });
    });
};


const updateTrainingStatus = async (body) => {
    return new Promise((resolve, reject)=>{
        const { uid, message, status, tenantId } = body;
        let lastTrain = '';
        if(status){
            let msg = `Hi Agent Helper training done for ${config.get('adminURL')}`;
            sendEmail("<EMAIL>", "Agent Helper Training", msg, () => { });
        }
        if(status == 2){
            lastTrain = `,last_trained = '${moment().format('YYYY-MM-DD HH:mm:ss')}'`;
        }
        const query = `UPDATE agenthelper SET training_message = ?, training_status = ? ${lastTrain} WHERE uid = ? AND tenant_id = ?`;
        connection[tenantId].execute.query(query,[message, status, uid, tenantId], (error, updatedDoc) => {
            if(!error){
                emitCompleteStatus(tenantId, 'agentHelperStatusChange');
                resolve({ data: updatedDoc, message: "Status updated successfully"})
            }
            reject({message: "Error while updated agent helper status"});
        })
    })
}

const getAgentHelperList = (body) => {
    return new Promise((resolve, reject)=>{
        const { tenantId } = body;
        const query = "SELECT * FROM agenthelper WHERE tenant_id = ?";
        connection[tenantId].execute.query(query, [tenantId], (error, doc) => {
            if(!error){
                doc.forEach((entry)=> delete entry.slack_creds);
                resolve({data: doc, message: "Agent helper List"});
            }else{
                reject({data: {}, message:"Error while getting agent helper list"});
            }
        })
    });
};

const updateAgentHelper = (sqlValues, uid, tenantId) => {
    return new Promise((resolve, reject) => {
        const query = `UPDATE agenthelper SET ${Object.keys(sqlValues).map((key) => key + "=?").join(',')} WHERE uid = ?;`;
        connection[tenantId].execute.query(query, [...Object.values(sqlValues), uid], (error, doc) => {
            if(!error){
                let data = { task: 'updateAgentHelperFile', isCloudFrontSupported, data: { tenantId, uid } };
                const topic = config.get("kafkaTopic.searchClientTopic");   
                kafkaData(topic, data);
                resolve({data: doc, message: "Agent helper List"});
            }else{
                reject({data: {}, message:"Error while getting agent helper list"});
            }
        })
    });
};

const saveSlackCreds = (body) => {
    const { tenantId, creds, uid} = body;
    return new Promise((resolve, reject) => {
        creds.clientId = creds.clientId ? commonFunctions.encryptDecryptCreds(creds.clientId, 'encrypt') : null;
        creds.clientSecret = creds.clientSecret ? commonFunctions.encryptDecryptCreds(creds.clientSecret, 'encrypt') : null;
        const query = `UPDATE agenthelper SET slack_creds = ? WHERE uid = ? AND tenant_id = ?`;
        connection[tenantId].execute.query(query, [JSON.stringify(creds), uid, tenantId], (error, doc) => {
            if(!error){
                resolve({data: doc, message: "Slack creds updated"});
            }else{
                reject({data: {}, message:"Error while getting saving slack creds"});
            }
        })
    });
}


const getSlackCreds = (body) => {
    const { tenantId, uid, platform} = body;
    if(platform == "zendesk"){
            return new Promise((resolve, reject) => {
                const query = `SELECT slack_client_id AS clientId, slack_client_secret AS clientSecret, slack_enabled AS enabled FROM ah_clients WHERE agent_helper_id = ? `;
                    connection[tenantId].execute.query(query, [uid, tenantId], (error, doc) => {
                        if (!error) {
                            const creds = {};
                            if (doc.length) {
                                creds.clientId = doc[0].clientId;
                                creds.clientSecret = '*'.repeat(doc[0].clientSecret.length);
                            }
            
                            const data = {
                                creds,
                                enabled: doc.length ? doc[0].enabled : false
                            };
            
                            resolve({ data, message: "Slack creds fetched from ah_clients" });
                        } else {
                            reject({ data: {}, message: "Error while getting Slack creds from ah_clients" });
                        }
                    });
                });
    }
    return new Promise((resolve, reject) => {
        const query = `SELECT slack_creds creds, slack_enabled enabled from agenthelper WHERE uid = ? AND tenant_id = ?`;
        connection[tenantId].execute.query(query, [uid, tenantId], (error, doc) => {
            if(!error){
                const creds = doc.length && doc[0].creds ? JSON.parse(doc[0].creds) : {};
                creds.clientId = creds.clientId ? commonFunctions.encryptDecryptCreds(creds.clientId, 'decrypt') : null;
                creds.clientSecret = creds.clientSecret ? commonFunctions.encryptDecryptCreds(creds.clientSecret, 'decrypt') : null;
                creds.clientSecret = '*'.repeat(creds.clientSecret.length);
                const data = {
                    creds,
                    enabled: doc.length ? doc[0].enabled : false
                }
                resolve({data, message: "Agent helper slack creds"});
            }else{
                reject({data: {}, message:"Error while getting getting slack creds"});
            }
        })
    });
}

const saveJiraCreds = (body) => {
    const { tenantId, creds, uid} = body;
    return new Promise((resolve, reject) => {
        const query = `UPDATE agenthelper SET jira_creds = ? WHERE uid = ? AND tenant_id = ?`;
        connection[tenantId].execute.query(query, [JSON.stringify(creds), uid, tenantId], (error, doc) => {
            if(!error){
                resolve({data: doc, message: "Agent helper jira creds saved"});
            }else{
                reject({data: {}, message:"Error while getting agent helper jira creds"});
            }
        })
    });
}


const getJiraCreds = (body) => {
    const { tenantId, uid} = body;
    return new Promise((resolve, reject) => {
        const query = `SELECT jira_creds creds, jira_enabled enabled from agenthelper WHERE uid = ? AND tenant_id = ?`;
        connection[tenantId].execute.query(query, [uid, tenantId], (error, doc) => {
            if(!error){
                resolve({data: doc[0], message: "Agent helper jira creds"});
            }else{
                reject({data: {}, message:"Error while getting agent helper jira creds"});
            }
        })
    });
}


const jiraToggle = (body) => {
    const { tenantId, uid} = body;
    const enable = JSON.parse(body.enable) ? 1 : 0
    return new Promise((resolve, reject) => {
        const query = `UPDATE agenthelper SET jira_enabled = ?  WHERE uid = ? AND tenant_id = ?`;
        connection[tenantId].execute.query(query, [enable, uid, tenantId], (error, doc) => {
            let data = { task: 'updateAgentHelperFile', isCloudFrontSupported, data: { tenantId, uid } };
            const topic = config.get("kafkaTopic.searchClientTopic");   
            kafkaData(topic, data);
            if(!error){
                resolve({data: doc, message: "Agent helper jira toggle"});
            }else{
                reject({data: error, message:"Error while getting agent helper jira creds"});
            }
        });
    });
}

const slackToggle = (body) => {
    const { tenantId, uid} = body;
    const enable = JSON.parse(body.enable) ? 1 : 0
    return new Promise((resolve, reject) => {
        const query = `UPDATE agenthelper SET slack_enabled = ?  WHERE uid = ? AND tenant_id = ?`;
        connection[tenantId].execute.query(query, [enable, uid, tenantId], (error, doc) => {
            if(!error){
                let data = { task: 'updateAgentHelperFile', isCloudFrontSupported, data: { tenantId, uid } };
                const topic = config.get("kafkaTopic.searchClientTopic");   
                kafkaData(topic, data);
                resolve({data: doc, message: "Agent helper slack creds"});
            }else{
                reject({data: error, message:"Error while getting agent helper slack creds"});
            }
        })
    });
}

const deactivateAgentHelper = (body) => {
    const { tenantId, uid, enable} = body;
    return new Promise((resolve, reject) => {
        const query = `UPDATE agenthelper SET is_enabled = ?  WHERE uid = ? AND tenant_id = ?`;
        connection[tenantId].execute.query(query, [enable, uid, tenantId], (error, doc) => {
            if(!error){
                let data = { task: 'updateAgentHelperFile', isCloudFrontSupported, data: { tenantId, uid } };
                const topic = config.get("kafkaTopic.searchClientTopic");   
                kafkaData(topic, data);
                resolve({data: doc, message: "Agent helper deactiavted"});
            }else{
                console.error("Error while deactiavting agent helper: ", error);
                reject({message:"Error while deactiavting agent helper"});
            }
        })
    });
}

const agentHelperPublish = (uid, tenantId) => {
    let data = { task: 'updateAgentHelperFile', isCloudFrontSupported, data: { tenantId, uid } };
    const topic = config.get("kafkaTopic.searchClientTopic");   
    kafkaData(topic, data);
}

const publishAllAgentHelperData = async (tenantId) => {
    const query = "SELECT uid FROM agenthelper WHERE tenant_id = ?";
    connection[tenantId].execute.query(query, [tenantId], async (error, docs) => {
        if(!error){
            const uids = docs.map((uids) => uids.uid);
            await Promise.all(uids.map((uid) => agentHelperPublish(uid, tenantId)))
        }else{
            console.log("Error while publising agent helpet data to sc");
        }
    });
}

const deactivateAllAgentHelper = (body) => {
    const {tenantId}= body ;
    const enable = 0;
    return new Promise((resolve, reject) => {
    const query = "UPDATE agenthelper SET is_enabled = ?  WHERE tenant_id = ?";
    connection[tenantId].execute.query(query, [enable, tenantId], async (error, doc) => {
        if(!error){
            await publishAllAgentHelperData(tenantId);
            resolve({data: doc, message: " All Agent helper deactiavted"});
        }else{
            console.error("Error while deactiavting agent helper: ", error);
            reject({message:"Error while deactiavting agent helper"});
        }
    })
})
}

module.exports = {
    deleteAgentHelperConfigurations,
    saveAgentHelperConfigurations,
    getAgentHelperDetails,
    updateTrainingStatus,
    getAgentHelperList,
    updateAgentHelper,
    trainAgentHelper,
    saveSlackCreds,
    saveJiraCreds,
    getSlackCreds,
    getJiraCreds,
    slackToggle,
    jiraToggle,
    deactivateAgentHelper,
    deactivateAllAgentHelper
}