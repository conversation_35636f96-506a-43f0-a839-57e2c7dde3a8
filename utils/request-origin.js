module.exports  = (req) => {
    let userIP = '';  
    {
      let ip;
      if (!ip || ip === 1) {
        try {
          ip = req.headers['x-client-ip'];
        } catch (e) {}
      }
      if (!ip || ip === 1) {
        try {
          ip = req.headers['x-forwarded-for'];
        } catch (e) {}
      }
      if (!ip || ip === 1) {
        try {
          ip = req.headers['cf-connecting-ip'];
        } catch (e) {}
      }
      if (!ip || ip === 1) {
        try {
          ip = req.headers['true-client-ip'];
        } catch (e) {}
      }
      if (!ip || ip === 1) {
        try {
          ip = req.headers['x-real-ip'];
        } catch (e) {}
      }
      if (!ip || ip === 1) {
        try {
          ip = req.headers['x-forwarded'];
        } catch (e) {}
      }
      if (!ip || ip === 1) {
        try {
          ip = req.headers['forwarded-for'];
        } catch (e) {}
      }
      if (!ip || ip === 1) {
        try {
          ip = req.headers.forwarded;
        } catch (e) {}
      }
      if (!ip || ip === 1) {
        try {
          ip = req.connection.remoteAddress;
        } catch (e) {}
      }
      if (!ip || ip === 1) {
        try {
          ip = req.connection.socket.remoteAddress;
        } catch (e) {}
      }
      if (!ip || ip === 1) {
        try {
          ip = req.socket.remoteAddress;
        } catch (e) {}
      }
      if (!ip || ip === 1) {
        try {
          ip = req.info.remoteAddress;
        } catch (e) {}
      }
  
      if (ip && ip.indexOf(':') > -1) {
        ip = ip.split(':');
        ip = ip[ip.length - 1];
      }
      userIP = ip;
    }
  
return { userIP };
  }
  