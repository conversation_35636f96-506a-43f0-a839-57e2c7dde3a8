
module.exports = {
    "objects": [{
        "name": "page_mindtouch",
        "label": "Mindtouch Pages",
        "status": 1,
        "boosting_factor": 1,
        "base_url": "",
        "fields": []

    }],
    "fields": [
        {
            "name": "id",
            "label": "id",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "draft_state",
            "label": "Draft State",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "deleted",
            "label": "Deleted",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "language",
            "label": "Language",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1,
            "isMerged":1

        },
        {
            "name": "namespace",
            "label": "Namespace",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "text",
            "label": "Description",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "path_seo",
            "label": "Seo Path",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },

        {
            "name": "title",
            "label": "Title",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "views",
            "label": "Views Count",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "article",
            "label": "Article",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "view_href",
            "label": "View Href",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "post_time",
            "label": "Created Date",
            "type": "datetime",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1,
            "isMerged":1,
            "isMasked": 0
        },
        {
            "name": "edited_time",
            "label": "Edited Date",
            "type": "datetime",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "modified_time",
            "label": "Modified Date",
            "type": "datetime",
            "isFilterable": 1,
            "isSortable": 1,
            "isSearchable": 0,
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "tags",
            "label": "Tags",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "alltags",
            "label": "All Tags",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "category",
            "label": "Category",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },{
            "name": "count_rating",
            "label": "Count Rating",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },{
            "name": "score_rating",
            "label": "Score Rating",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },{
            "name": "reviewerType",
            "label": "Reviewer Type",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },{
            "name": "articleType",
            "label": "Article Type",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },{
            "name": "categoryname_category_nested",
            "label": "Category Nested",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1,
            "isMerged":1,
            "isMasked": 0
        },{
            "name": "attachments_content",
            "label": "Attachment Content",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },{
            "name": "attachments_url",
            "label": "Attachment Url",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1,
            "isMasked": 0
        },
        {
            "name": "searchableTags",
            "label": "searchable Tags",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },{
            "name": "authorsType",
            "label": "Authors Type",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        },
        {
            "name": "attachment_multiple",
            "label": "Attachment Multiple",
            "type": "string",
            "isFilterable": 1, //indexing method analyzed OR not analyzed
            "isSortable": 1,
            "isSearchable": 1, //analyzer
            "isActive": 1
        }
    ]

}
