var express = require('express');
var fs = require('fs');
var url = require('url');
var path = require('path');
var async = require('async');
var router = express.Router();
var emailLib = require("../../Lib/email");
var multipart = require('connect-multiparty');
var multipartMiddleware = multipart();
const { exec } = require('child_process');
var permissions = ['Admin', 'Search', 'Recommendation', 'RestAPI'];
var dns = require("dns");
var commonFunctions = require('../../utils/commonFunctions');
const { authMiddlewareRoute } = require('../../auth/authMiddleware');
const options = {
    family: 4,
    hints: dns.ADDRCONFIG | dns.V4MAPPED,
};
var crt_result;
var key_result;
var permissionObjToUse  = {};


router.post("/get_security_permissions", function (req, res, next) {
    get_security_permissions(req).then((data) => {
        // insertAllGroups(function(err, data){
        res.send({ "status": "OK", "result": data });
        // });
    }).catch((error) => {
        res.send({ "status": "OK", "result": [] });
    });
});

async function get_security_permissions(req) {
    return new Promise((resolve, reject) => {
        authMiddlewareRoute(req,'/admin/getSecurityPermissions', (error, data) => {
            if (error) reject(error);
            else {
                data.forEach((d)=>{
                    permissionObjToUse[d.id] = d.permission_name
                })
                resolve(data);
            }
        })
    })
}

router.post("/get_security_groups", function (req, res, next) {
    get_security_groups(req, function (err, data) {
        if (err)
            res.send({ "status": "Ok", "result": [] });
        else {
            var group_json = {};
            var final_array = [];
            for (var i = 0; i < data.length; i++) {
                var val = data[i].ip;
                if (!group_json[val])
                    group_json[val] = [];
                group_json[val].push(data[i]);
            }
            for (var j = 0; j < Object.keys(group_json).length; j++) {
                var temp = {};
                temp['ip'] = Object.keys(group_json)[j];
                temp['permission'] = [];
                for (var k = 0; k < group_json[temp['ip']].length; k++) {
                    temp['permission'].push({ "id": group_json[temp['ip']][k].permission_id, "name": permissionObjToUse[group_json[temp['ip']][k].permission_id] })
                }
                final_array.push(temp);
            }
            res.send({ "status": "ok", "result": final_array });
        }
    })
});

async function get_security_groups(req,callback) {
    try {
        connection[req.headers['tenant-id']].execute.query("select sig.id, sig.ip, sig.permission_id from security_ip_groups sig", async (err, result) => {
            if (!Object.keys(permissionObjToUse).length)
                await get_security_permissions(req);
            callback(null, result);
                
        });
    }
    catch (e) {
        callback(e, null);
    }
}

router.post("/insert_security_group", function (req, res, next) {
    if (/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(req.body.rule.ip)) { 
        deleteGroup(req.body.rule.ip, req, function (err, data) {
            insert_security_group(req.body.rule,req, function (err, data) {
                res.send({ "status": "ok" });
            });
        })
    }else{
        res.send({ "error": "Validation Failed" });
    }

});

function insert_security_group(group,req,callback) {
    var temp = [];
    for (var i = 0; i < group.permission.length; i++) {
        if (group.permission[i].id) {
            temp.push([group.ip, group.permission[i].id]);
        }
        else
            temp.push([group.ip, group.permission[i]]);
    }
    try {
        connection[req.headers['tenant-id']].execute.query('insert into security_ip_groups(ip, permission_id) values ?', [temp], (err, result) => {
            if (err) {
                callback(err, null);
            }
            else {
                callback(null, 'ok');
            }
        });
    }
    catch (e) {
        callback(e, null);
    }
}

router.post("/delete_security_group", function (req, res, next) {
    deleteGroup(req.body.ip,req, function (err, data) {
        res.send({ "status": "ok" });
    });
});

function deleteGroup(ip,req, callback) {
    try {
        connection[req.headers['tenant-id']].execute.query("delete from security_ip_groups where ip = ? ", [ip], (err, result) => {
            if (err) {
                callback(err, null);
            }
            else {
                callback(null, 'ok');
            }
        })
    }
    catch (e) {
        callback(e, null);
    }
}

router.get("/get_dns_details", function (req, res, next) {
    async.auto({
        readDataFromJson: cb => {
            fs.readFile(path.join(__dirname, "customDnsMapping.json.ignore"), function (err, data) {
                if (err) {
                    commonFunctions.errorlogger.error(err);
                    cb(null, {});
                }
                else {
                    try {
                        let x = JSON.parse(data);
                        cb(null, x);
                    }
                    catch (ex) {
                        console.error(ex);
                        cb(null, {});
                    }
                }
            });
        }
    }, function (err, results) {
        if (err) {
            callback(err, null);
        } else {
            let responseToSend = results.readDataFromJson;
            let newUrl = url.parse(config.get("adminURL"));
            // var address;
            dns.lookup(newUrl.hostname, options, (err, result) => {
                commonFunctions.errorlogger.info('address,family:', result);
                responseToSend.currentIP = '*************';
                res.send(responseToSend);

            });
        }


    })
});

router.post("/add_data_in_json", function (req, res) {
    let x = true;
    const path_1 = path.join(__dirname, "../../resources/Dns_Certificates/server.crt.ignore");
    let command1 = 'openssl x509 -noout -text -in '+ path_1 +' -modulus | grep Modulus=';
    exec(command1, (err, crt_result) => {
        if (err) {
            res.send({"error" : "Invalid Certificate-Key Combination"})
        } else {
            commonFunctions.errorlogger.info(crt_result);
            if (x == true) {
                const path_2 = path.join(__dirname, "../../resources/Dns_Certificates/server.key.ignore");
                let command = 'openssl rsa -noout -text -in '+ path_2 +' -modulus | grep Modulus';
                exec(command, (err, key_result) => {
                    if (err) {
                        res.send({"error" : "Invalid Certificate-Key Combination"})
                    } else {
                        commonFunctions.errorlogger.info(key_result);
                    }
                });
            }
        }
    });
    const file = (path.join(__dirname, "customDnsMapping.json.ignore"));
    let data = JSON.stringify(req.body.dnsMapping);
    fs.writeFile(file, data, function (err, dataraw) {
        if (err) {
            commonFunctions.errorlogger.warn("success: false");
        } else {
            if (crt_result !== key_result) {
                res.send({ status: "Cannot add DNS details", success: false })
            }
            else {
                var email = "<EMAIL>";
                var content = `Domain change is requested from ${url.parse(config.get("adminURL")).hostname} to ${req.body.dnsMapping.domainName}`;
                var subject = "Request for Domain change";
                emailLib.sendEmail(email, subject, content);
                res.send({ status: "Dns details added", success: true });
            }

        }
    });
});

function checkDirectory(directory, callback) {
    fs.stat(directory, function (err, stats) {
        if (err) {
            fs.mkdir(directory, callback);
            callback(null);
        } else {
            callback(null);
        }
    });
}
router.post('/validate_certificates', function (req, res, next) {
    checkDirectory("resources/Dns_Certificates", function (error) {
        if (error) {
            commonFunctions.errorlogger.error(error);
        } else {
            fileSave(req, res, function (err, result) {
                if (err) {
                    commonFunctions.errorlogger.error(error);
                } else {
                    res.send(result);
                }
            });
        }

    });

})
function fileSave(req, res, callback) {
    const path_1 = path.join(__dirname, "../../resources/Dns_Certificates/server.crt.ignore");
    let data = req.body.dnsMapping.certificate_1;
    data = data.replace(/\n/g, "\r\n")
    // console.log(data);
    fs.writeFile(path_1, data, 'UTF-8', function (err, data) {
        if (err) commonFunctions.errorlogger.error(err);
        commonFunctions.errorlogger.warn("Successfully Written to server.crt file.");
    })
    const path_2 = path.normalize("resources/Dns_Certificates/server.key.ignore");
    let data_2 = req.body.dnsMapping.certificate_2;
    data_2 = data_2.replace(/\n/g, "\r\n")

    fs.writeFile(path_2, data_2, "utf8", function (err, data_2) {
        if (err) {
            commonFunctions.errorlogger.error(err);
            callback(null, { "result": [] });
        }
        else {
            commonFunctions.errorlogger.warn("Successfully Written to server.key file");
            callback(null, { "result": [data, data_2] });
        }

    })

}

router.post('/run_grep_command', function (req, res, next) {
    let x = true;
    const path_1 = path.join(__dirname, "../../resources/Dns_Certificates/server.crt.ignore");
    let command1 = 'openssl x509 -noout -text -in '+ path_1 +' -modulus | grep Modulus=';
    exec(command1, (err, crt_result) => {
        if (err) {
            res.send({"error" : "Invalid Certificate-Key Combination"})
        } else {
            commonFunctions.errorlogger.info(crt_result);
                const path_2 = path.join(__dirname, "../../resources/Dns_Certificates/server.key.ignore");
                let command = 'openssl rsa -noout -text -in '+ path_2 +' -modulus | grep Modulus';
                exec(command, (err, key_result) => {
                    if (err) {
                        res.send({"error" : "Invalid Certificate-Key Combination"})
                    } else {
                        commonFunctions.errorlogger.info(key_result);
                        if (crt_result === key_result) {
                            commonFunctions.errorlogger.info("status: true");
                            res.send({"result" : true})
                        }
                        else {
                            commonFunctions.errorlogger.warn("invalid certificates");
                            res.send({"result" : false})
                        }
                    }

                })

        }


        // x = true;
    })
    // crt_result = "C83AADA8AD1A308D02CA9FDBBBF73B76AB58B90B7B6E2326347FEEAE22F9DCAB9BF3663F916A719FCD14C327EEFF16D9EE56E4898B98E50C7D2FC6E9C511241AC32BC5E3DBB8ED8967E170152B201B5800263133826788079D12D4B5702420554FA0C0A78E966F09F8E6E8A788960A8822589F98B2AE302DB7DAB0888DF59E62E37EA9440393BF83E025639973694F24559DDD62C0BCBA637B25EA19DB3A7E2E1C70F7495FD7688405F853043717383F23832F1B9160E54EDA898920C65C21FF624A27B844BC0DB617D7098757EED94CBC8192D90506C0FC9EAE0612BF8B652E35D79B3B6DB5767FDFAF513922AAAE858E81F69618C087D33EB73B4398396C39";

})


function insertAllGroups(callback) {
    var parent = [
        {
            parentName: 'contentSourcesAndSearchClients',
            children: ['/admin/contentSources/getAllSupportedContentSourceTypes',
                '/admin/contentSources/getAllSupportedContentSourceCategories',
                '/admin/contentSources/getAddedContentSources',
                '/admin/contentSources/getContentSourcesAuthData',
                '/admin/contentSources/getSearchResultForML',
                '/admin/contentSources/getFoldersId',
                '/admin/contentSources/addContentSource',
                '/admin/contentSources/deleteContentSource',
                '/admin/contentSources/deleteObject',
                '/admin/contentSources/deleteField',
                '/admin/contentSources/getPlaces',
                '/admin/contentSources/crawlData',
                '/admin/contentSources/stopCrawler',
                '/admin/contentSources/fetchObjects',
                '/admin/contentSources/getChildFolders',
                '/admin/contentSources/getGitInfo',
                '/admin/authorization/oAuthCallback',
                '/admin/authorization/oAuthCallbackJira',
                '/admin/authorization/oAuthCallbackConfluence',
                '/crawler/jira/getContent',
                '/crawler/box/getContent',
                '/crawler/jive/getContent',
                '/admin/searchClient/deletePlatform',
                '/admin/searchClient/getSearchClient',
                '/admin/searchClient/updateSearchClient',
                '/admin/searchClient/addPlatform',
                '/admin/searchClient/getPlatforms',
                '/admin/searchClient/getContentTypes',
                '/admin/searchClient/getFiltersPriority',
                '/admin/searchClient/saveFiltersPriority',
                '/admin/searchClient/downloadPlatform',
                '/admin/searchClient/saveCss',
                '/admin/searchClient/editClient',
                '/admin/searchClient/cloneSearchClient',
                '/crawler/confluence/getContent',
                '/crawler/helpscout/getContent',
                '/admin/box/getChildFolders',
                '/admin/dropbox/getChildFolders',
                '/crawler/docebo/getContent',
            ]
        },
        {
            parentName: 'statusPage',
            children: ['/statusPage']
        },
        {
            parentName: 'authentication',
            children: ['/oauth/token',
                '/authorise',
                '/authorise_success',
                '/logout',
                '/signup',
                '/login',
                '/registerEmail',
                '/getAutoProvisionToken',
                '/registerSU',
                '/forgot-password'
            ]
        },
        {
            parentName: 'searchEndpoints',
            children: ['/search/searchResultByGet',
                '/search/searchResultByPost',
                '/search/getRecommendedResult'
            ]
        },
        {
            parentName: 'trackingApi',
            children: ['/analytics/track.png']
        },
        {
            parentName: 'tuning',
            children: ['/tuning/contentTuning/getAllContent',
                '/tuning/contentTuning/changeTypeBoosting',
                '/tuning/contentTuning/changeFieldBoosting',
                '/tuning/contentTuning/custom-boosting',
                '/tuning/searchtuning/saveTuningData',
                '/tuning/searchtuning/getTuningData',
                '/tuning/searchtuning/deleteTuningData',
                '/tuning/searchtuning/getUnrankedSearchResults',
                '/tuning/searchtuning/saveAllBoosting',
            ]
        },
        {
            parentName: 'emailNotifications',
            children: ['/admin/notifications/getAllNotificationPreferences',
                '/admin/notifications/saveNotificationPreferences',
                '/admin/notifications/deleteNotificationPreferences',
                '/admin/notifications/sendNotifications',
                '/admin/notifications/sendVersionMail',
                '/admin/notifications/sendReport',
            ]
        },
        {
            parentName: 'profiles',
            children: ['/statusPage',
                '/admin/userManagement/getAllUsers',
                '/admin/userManagement/editUser',
                '/admin/userManagement/manageAccount',
                '/admin/userManagement/changePassword',
                '/admin/userManagement/deleteRegisteredUser',
            ]
        },
        {
            parentName: 'ticketsApi',
            children: ['/admin/support/getAllTickets',
                '/admin/support/editTicket',
                '/admin/version/getVersions',
            ]
        },
        {
            parentName: 'authContentSource',
            children: ['/oauthClients/saveOauthClients',
                '/oauthClients/deleteOauthClients',
                '/oauthClients/getOauthClients',
                '/oauthClients/getOauthScopes',
            ]
        },
        {
            parentName: 'analyticsOld',
            children: ['/analytics/an/mytrail',
                '/analytics/missedQueryHistogram',
                '/analytics/missedQueryQueries',
                '/analytics/SearchHistogram',
                '/analytics/queriesCount',
                '/analytics/getConversions',
                '/searchClientAnalytics/getTypesStatistics',
                '/analytics/getGeoReport',
                '/analytics/getTileData',
                '/analytics/getRecentSearch',
                '/analytics/getLonelyDoc',
                '/searchClientAnalytics/getAddedContent',
                '/analytics/readyToBecomeHelpArticle',
                '/analytics/getFunnel',
                '/analytics/sa/getSessionReports',
                '/analytics/sa/trackSession',
                '/analytics/sa/getTopClickedResults',
                '/analytics/sa/getSessionsActivityDetails',
                '/analytics/sa/exploreSession',
                '/analytics/sa/exploreSearchText',
                '/analytics/sa/exploreSiteVisitSession',
                '/analytics/sa/getFilterBasedSearchChart',
                '/searchClientAnalytics/getAllSearchPlatforms',
                '/analytics/sa/getPlatformSearchChart',
                '/analytics/sa/getDomainSpecificVisitors',
                '/analytics/sa/getDomainSpecificSearchedText',
                '/analytics/sa/getConversionDetails',
                '/analytics/sa/getTopBackwardDocuments',
                '/analytics/sa/getTopSearchsWithNoClicks',
                '/analytics/sa/getDocumentsWithLargeContent',
                '/analytics/sa/getDocumentsWithLargeContent',
                '/analytics/sa/getContentSourceList',
                '/analytics/sa/getSessionChartDetails',
                '/analytics/sa/getSessionChartSearches',
                '/analytics/sa/getSessionChartPageViews',
                '/analytics/sa/getFiltersForQueries',
                '/searchClientAnalytics/getActiveReportsInfo',
                '/searchClientAnalytics/getContentAuthData'
            ]
        },
        {
            parentName: 'analyticsNew',
            children: ['/api/v2/searchQuery/all',
                '/api/v2/searchQuery/withResults',
                '/api/v2/searchQuery/withoutResults',
                '/api/v2/searchQuery/withNoClicks',
                '/api/v2/searchQuery/bySessionId',
                '/api/v2/searchConversion/all',
                '/api/v2/searchConversion/discussionsReadyToBecomeArticles',
                '/api/v2/searchConversion/notOnFirstPage',
                '/api/v2/searchConversion/bySessionId',
                '/api/v2/searchSession/all',
                '/api/v2/searchSession/all/searchQuery',
                '/api/v2/searchSession/all/searchConversion',
                '/api/v2/searchSession/bySearchSessionId',
                '/api/v2/searchSession/bySearchSessionId',
                '/api/v2/searchSession/bySearchSessionId',
                '/api/v2/searchSession/byCaseUid',
                '/api/v2/searchSession/byCaseUid/searches',
                '/api/v2/searchSession/byCaseUid/views'
            ]
        }, {
            parentName: 'searchApiEnds',
            children: [
                '/api/v2/searchResults',
                '/api/v2/provisionToken']
        }
    ];
    var permissionObj = {};
    try {
        connection.query("select * from security_permissions", (err, result) => {
            if (err)
                callback(err, null);
            else {
                for (var i = 0; i < result.length; i++) {
                    permissionObj[result[i].permission_name] = result[i].id;
                }
                var arrayToPush = [];
                for (var i = 0; i < parent.length; i++) {
                    for (var j = 0; j < parent[i].children.length; j++) {
                        temp = [permissionObj[parent[i].parentName], parent[i].children[j]];
                        arrayToPush.push(temp);
                    }
                }
                try {
                    connection.query('insert into security_api_groups(permission_id, url) values ?', [arrayToPush], (err, result) => {
                        if (err) {
                            callback(err, null);
                        }
                        else {
                            callback(null, 1);
                        }
                    });
                }
                catch (e) {
                    commonFunctions.errorlogger.error(e);
                    callback(e, null);
                }
            }
        });
    } catch (e) {
        commonFunctions.errorlogger.error(e);
        callback(e, null);
    }
}

module.exports = {
    router: router
}

