const path = require('path');
environment = require('../routes/environment');
process.env.NODE_ENV = environment.configuration;
const configPath = path.join(__dirname, '../config');
process.env.NODE_CONFIG_DIR = configPath;

config = require('config');

const connection_sql = require('../utils/connection');
const userKafkaConfigurations = require('../routes/admin/userKafkaConfig');

const usersUpdate = (cb) => {
    userKafkaConfigurations.publishUsersViaKafka('', function (err, searchConfig) {
        cb(err, searchConfig);
    })
}

module.exports = {usersUpdate}