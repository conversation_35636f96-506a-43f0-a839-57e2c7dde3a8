var express = require('express');
var router = express.Router();
var commonFunctions = require('./../../utils/commonFunctions');
var appVariables = require('../../constants/appVariables');

const stackOverflowAuthorisationIntermediate = function (contentSource, authorization, callback) {
  var clientId = appVariables.stackoverflow.clientId;
  var redirectUrl = "https://oauthsfdc.searchunify.com";
  var authUrl = 'https://stackoverflow.com/oauth?client_id=' + clientId + '&scope=read_inbox&redirect_uri='+redirectUrl;
  callback(null, {
    "oauth": authUrl
  });

}
router.get('/getSavedTags', function (req, res, next) {
  getSavedTags(req.query.contentSourceId,req, function (err, data) {
    res.send(data);
  });
}); 

const getSavedTags = function (contentSourceId,req, callback) {
  commonFunctions.getContentSourceSpaceBoardsById(contentSourceId,req,function (err, data) {
    if (err) {
      callback(null, []);
    }
    else {
      var tagArray = [];
      if (data.length > 0) {
        for (var i = 0; i < data.length; i++) {
          tagArray.push(data[i].spaceName);
        }
      }
      callback(null, tagArray);
    }
  })
}

router.get('/getInnameTags', function (req, res, next) {
  let headers = {
    "Content-Type": "application/json",
    "su-crawler-secret" : config.get("crawler.sharedSecret"),
    "tenant-id": req.headers['tenant-id']
}
  commonFunctions.httpRequest('GET', config.get("crawler.crawlerUrl") + `/content-source/place-suggestions?tagName=${req.query.tagName}&csId=${req.query.csId}`, '', {}, headers, function (error, result) {
    if (error) {
          commonFunctions.errorlogger.error("Error found: ", error);
          res.send({error: true, msg: error});
      }
      else if(!result.status)
          res.send({error: true, msg: result.data});
      else res.send(result.data);
  });
});

router.post('/insertTagsForStackoverflow', function (req, res, next) {
  insertTagsForStackoverflow(req.body.contentSourceId, req.body.tagsArray, req,function (err, data) {
    res.send(data);
  });
});

const insertTagsForStackoverflow = function (contentSourceId, tagsArray,req, callback) {
  var contentSourceObjectArr = [];
  var projects;
  for (var i = 0; i < tagsArray.length; i++) {
    var contentObjectToInsert = {};
    contentObjectToInsert['content_source_id'] = contentSourceId;
    contentObjectToInsert['spaceName'] = tagsArray[i];
    contentObjectToInsert['spaceUrl'] = tagsArray[i];
    contentObjectToInsert['spaceKey'] = tagsArray[i];
    contentObjectToInsert['spaceId'] = tagsArray[i];
    contentObjectToInsert['isSelected'] = 1;
    contentSourceObjectArr.push(contentObjectToInsert);
  }
  commonFunctions.deleteDriveSpace(contentSourceId,req,function (err, result) {
    if (!err) {
      commonFunctions.insertSpacesBoards(contentSourceId, contentSourceObjectArr,req, function (errAsync, rows) {
        callback(null, rows);
      });
    }
    else {
      callback(err, null);
    }
  });
}

module.exports = {
  router: router,
  stackOverflowAuthorisationIntermediate: stackOverflowAuthorisationIntermediate,
  getSavedTags: getSavedTags,
  insertTagsForStackoverflow: insertTagsForStackoverflow
}
