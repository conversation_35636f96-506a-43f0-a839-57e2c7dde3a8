const commonFunctions = require('../../../utils/commonFunctions');
var request = require('request');
const { getAccessTokenFromTenantId } = require('auth-middleware');

const getSearchResults = async (searchObj, templateFields, req,callback) => {
    let searchFields = templateFields.searchFields;
    let moreResultsCount;
    let moreResults = searchFields.filter(x => x.includes('results:'))[0];
    if (moreResults)
        moreResultsCount = moreResults.split('results:')[1];
        const data = await getAccessTokenFromTenantId(req.headers['tenant-id']);
        if (data.accessToken) {
            var options = {
                method: 'POST',
                rejectUnauthorized: false,
                url: config.get('searchService.url') + '/search/searchResultByPost',
                headers: {
                    'content-type': 'application/json'
                },
                body: {
                    "searchString": searchObj.searchString,
                    "from": 0,
                    "sortby": "_score",
                    "orderBy": "desc",
                    "aggregations": searchObj.aggregationsArray,
                    "resultsPerPage": 10,
                    "accessToken": data.accessToken,
                    "uid": searchObj.uid,
                    "AccountID": searchObj.AccountId,
                    "ContactId": searchObj.ContactId,
                    "ProfileID": searchObj.ProfileId,
                    "UserId": searchObj.UserId,
                    "UserType": searchObj.UserType,
                    "email": searchObj.email
                },
                json: true
            };
            commonFunctions.errorlogger.info("search hit is ", JSON.stringify(options.body));
            request(options, function (err, response, docs) {
                let resultToSend = [];
                if (docs && docs.result && docs.result.hits.length) {
                    let firstResult = docs.result.hits[0];
                    if ((searchFields.includes('title') && !firstResult.highlight.TitleToDisplayString.length)
                        || (searchFields.includes('summary') && !firstResult.highlight.SummaryToDisplay.length)
                        || (searchFields.includes('url') && !firstResult.href)) {
                        resultToSend = [];
                        commonFunctions.errorlogger.info('sending no results as first result is', JSON.stringify(docs.result.hits));
                    } else {
                        resultToSend = [{
                            "value": firstResult.highlight.TitleToDisplayString[0],
                            "name": "title"
                        },
                        {
                            "value": firstResult.href,
                            "name": "url"
                        },
                        {
                            "value": firstResult.highlight.SummaryToDisplay[0] ? firstResult.highlight.SummaryToDisplay[0].replace(/<\/span>/g, '').replace(/<span class='highlight'>/g, '') : '',
                            "name": "summary"
                        }];
                        if (moreResults && moreResultsCount) {
                            let finalResults = docs.result.hits.splice(1, moreResultsCount);
                            if (finalResults.length) {
                                let MoreResultsString = '';
                                finalResults.map(r=>{
                                    MoreResultsString += r.highlight.TitleToDisplayString[0] + '\n' + r.href + '\n' + '\n';
                                })
                                resultToSend.push({
                                    "name": moreResults,
                                    "value": MoreResultsString
                                });
                            } else {
                                commonFunctions.errorlogger.info('sending no results as more results is', JSON.stringify(finalResults));
                                resultToSend = [];
                            }
                        }
                    }
                    callback(null, resultToSend);
                } else {
                    commonFunctions.errorlogger.info('search result in helper error', docs);
                    commonFunctions.errorlogger.info('search result in helper error', err)
                    callback(null, resultToSend);
                }
            })
        }
    
}


module.exports = { getSearchResults }