var express = require('express');
var router = express.Router();
var samlController = require('./samlController');
var passport = require('passport'); 
var saml = require('passport-saml');
const login = require('../login');
const { getTenanDataFromSubdomainOrTenantHash, fetchTenantInfoFromUid } = require('auth-middleware');
var commonFunctions = require("./../../utils/commonFunctions");
const md5 = require('md5');
var jwtInit = require('../jwt');

/** Send Request to Haka for entity ID */
router.get('/',
    function (req, res, next) {
        var instruction = "<h3>Routes:</h3>" +
            // "<ul><li> All routes start with /saml.</li>" +
            "<li>/saml/sp-metadata: This will generate your sp metadata file which can be provided to IDP</li>" +
            // "<li>/saml/auth :  This route can be used to perform SAML SSO directly. Use url &ltbase&gt/saml/auth to perform SSO. To see the attributes returned by IDP which can be used for attribute mapping first name, last name and email use url &ltbase&gt/saml/auth?relayState=testconfig.</li>" +
            // "<li>/saml/logout : This route is used to send a single logout request to IDP if the samlLogoutUrl for that IDP is configured.</li>";
            res.send(instruction)
    }
);

/** Form SAML Strategy */
router.get('/auth', getTenanDataFromSubdomainOrTenantHash,
    samlController.send_saml_request);

/** Read SAML Response and Authenticate*/
router.post('/auth/:ssoKey?', getTenanDataFromSubdomainOrTenantHash,
    (req, res, next) => {
        const tenantHash = req.headers.session.tenantHash;
        let tenantHashPlusScUid = '';
        
        if(req.body && req.body.RelayState && req.body.RelayState.includes('&')) {
            let uid = req.body.RelayState && req.body.RelayState.split('&')[0];
            tenantHashPlusScUid = uid ? md5(tenantHash + uid) : tenantHash;
        } else {
            tenantHashPlusScUid = tenantHash;
        }

        passport.authenticate(tenantHashPlusScUid, { session: true, failureRedirect: '/saml/fail' })(req, res, next);
    },
    samlController.receive_saml_reponse
);

router.get('/auth-login/:ssoKey',
    (req, res, next) => {
        const tenantHash = req.params.ssoKey;
        passport.authenticate(tenantHash, { session: true, failureRedirect: '/saml/fail' })(req, res, next);
    },
    samlController.receive_saml_reponse
);

router.get('/fail',
    function (req, res) {
        res.status(401).send('Login failed');
    }
);

router.get('/logout',
    function (req, res) {
        login.adminLogout(req, () => {
            res.redirect(`https://${req.hostname}` + '/saml/auth');
        })
    }
);

router.post('/auth-logout', samlController.send_logout_request);

router.post('/logout',
    async function (req, res) {
        if(req.query.uid !== '') {
            try {
                let { uid } = req.query;
                let { user } = req.body;
                if(user) {
                    user = JSON.parse(user);
                    const data = await fetchTenantInfoFromUid(uid);
                    if((data && data.tenantId) == user["tenant-id"]) {
                        var samlStrategy = passport._strategy(md5(md5(user["tenant-id"]) + uid));
                        if(!samlStrategy) {
                            return res.redirect(`${config.get('adminURL')}/saml/auth`);
                        }
                        if (user && user.nameID) {
                            req.user = { nameID: "", sessionIndex: "", nameIDFormat: "" }
                            req.user.nameID = user.nameID;
                            req.user.sessionIndex = user.sessionIndex;
                            req.user.nameIDFormat = user.nameIDFormat; 
                            req.headers["tenant-id"] = data.tenantId;
                            samlController.getSamlConfiguration(req, (err, result) => {
                                samlStrategy && samlStrategy.logout(req, function (err, requestUrl) {
                                    req.logOut();
                                    res.send({ "location": result && result.hostedConfiguration.saml_sso_url });
                                })
                            });
                        }
                    }
                }
                else {
                    res.redirect(`https://${req.hostname}/saml/auth?uid=${req.query.uid}`);
                }
            } catch (error) {
                commonFunctions.errorlogger.error("error", error);
            }
        } else {
            login.adminLogout(req, () => {
                res.redirect(`https://${req.hostname}` + '/saml/auth');
            })
        }
    }
);

router.get('/sp-metadata', samlController.show_sp_metadata);

/** refresh jwt token */
router.post('/refreshJwtToken',async (req, res, next) => {
    await jwtInit.refreshJwtToken(req,res,next);
});

module.exports = router;
