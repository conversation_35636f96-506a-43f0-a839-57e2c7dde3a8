// contains producer as well as consumer
const { Kafka, logLevel, CompressionTypes } = require('kafkajs');
const config = require("config");
let kafka;
let producer;

exports.KAFKA_EVENT_TYPES = {
  add    : "ADD",
  delete : "DELETE",
  update : "UPDATE",
  reload : "RELOAD",
  reset  : "RESET",
}

const initializekafka = ({ clientId, brokers, retryConfig }) => {
  kafka = new Kafka({
    clientId,
    brokers,
    logLevel: logLevel.ERROR,
    retry: {
      maxRetryTime: retryConfig.maxRetryTime, // default 30000
      initialRetryTime: retryConfig.initialRetryTime, // default 300
      retries: retryConfig.retries, // default is 5
      restartOnFailure: async () => true // this is default
    }
  });

  producer = kafka.producer();
};

const publishMessage = async ({ topic, messages }) => {
  console.log("Publishing Status Page message on topic",topic)
  const response = { success: false };
  if(!producer){
    initializekafka({
      clientId: 'admin',
      brokers: [config.get('statusPageService.kafka.host')],
      retryConfig: {
        maxRetryTime: config.get('statusPageService.kafka.maxRetryTime'), // default 30000
        initialRetryTime: config.get('statusPageService.kafka.initialRetryTime'), // default 300
        retries: config.get('statusPageService.kafka.retries') // default is 5
      }
    });
  }
  try {
    await producer.connect();
    const publishResult = await producer.send({
      topic,
      messages,
      compression: CompressionTypes.GZIP
    });
    response.success = true;
    response.data = { result: publishResult };
  } catch (e) {
    console.log(e);
    response.error = e;
  }

  return response;
};

exports.initializekafka = initializekafka;
exports.publishMessage = publishMessage;
